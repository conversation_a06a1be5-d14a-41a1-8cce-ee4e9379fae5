package com.afollestad.materialdialogs.util;

import android.content.Context;
import android.content.DialogInterface;
import android.content.res.ColorStateList;
import android.content.res.TypedArray;
import android.graphics.Color;
import android.graphics.drawable.Drawable;
import android.os.Build;
import android.os.IBinder;
import androidx.annotation.ArrayRes;
import androidx.annotation.AttrRes;
import androidx.annotation.ColorInt;
import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import android.util.TypedValue;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import com.afollestad.materialdialogs.GravityEnum;
import com.afollestad.materialdialogs.MaterialDialog;

/**
 * <AUTHOR> (afolles<PERSON>)
 */
public class DialogUtils {

//    @SuppressWarnings("ConstantConditions")
//    public static float resolveFloat(Context context, int attr) {
//        TypedArray a = context.obtainStyledAttributes(null, new int[]{attr});
//        try {
//            return a.getFloat(0, 0);
//        } finally {
//            a.recycle();
//        }
//    }

  @ColorInt
  public static int getDisabledColor(Context context) {
    final int primaryColor = resolveColor(context, android.R.attr.textColorPrimary);
    final int disabledColor = isColorDark(primaryColor) ? Color.BLACK : Color.WHITE;
    return adjustAlpha(disabledColor, 0.3f);
  }

  @ColorInt
  public static int adjustAlpha(@ColorInt int color,
      @SuppressWarnings("SameParameterValue") float factor) {
    int alpha = Math.round(Color.alpha(color) * factor);
    int red = Color.red(color);
    int green = Color.green(color);
    int blue = Color.blue(color);
    return Color.argb(alpha, red, green, blue);
  }

  @ColorInt
  public static int resolveColor(Context context, @AttrRes int attr) {
    return resolveColor(context, attr, 0);
  }

  @ColorInt
  public static int resolveColor(Context context, @AttrRes int attr, int fallback) {
    TypedArray a = context.getTheme().obtainStyledAttributes(new int[]{attr});
    try {
      return a.getColor(0, fallback);
    } finally {
      a.recycle();
    }
  }

  // Try to resolve the colorAttr attribute.
  public static ColorStateList resolveActionTextColorStateList(
      Context context, @AttrRes int colorAttr, ColorStateList fallback) {
    TypedArray a = context.getTheme().obtainStyledAttributes(new int[]{colorAttr});
    try {
      final TypedValue value = a.peekValue(0);
      if (value == null) {
        return fallback;
      }
      if (value.type >= TypedValue.TYPE_FIRST_COLOR_INT
          && value.type <= TypedValue.TYPE_LAST_COLOR_INT) {
        return getActionTextStateList(context, value.data);
      } else {
        final ColorStateList stateList = a.getColorStateList(0);
        if (stateList != null) {
          return stateList;
        } else {
          return fallback;
        }
      }
    } finally {
      a.recycle();
    }
  }

  // Get the specified color resource, creating a ColorStateList if the resource
  // points to a color value.
  public static ColorStateList getActionTextColorStateList(Context context, @ColorRes int colorId) {
    final TypedValue value = new TypedValue();
    context.getResources().getValue(colorId, value, true);
    if (value.type >= TypedValue.TYPE_FIRST_COLOR_INT
        && value.type <= TypedValue.TYPE_LAST_COLOR_INT) {
      return getActionTextStateList(context, value.data);
    } else {

      if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.LOLLIPOP_MR1) {
        //noinspection deprecation
        return context.getResources().getColorStateList(colorId);
      } else {
        return context.getColorStateList(colorId);
      }
    }
  }

  /**
   * Returns a color associated with a particular resource ID
   * <p/>
   * Starting in {@link android.os.Build.VERSION_CODES#M}, the returned
   * color will be styled for the specified Context's theme.
   *
   * @param colorId The desired resource identifier, as generated by the aapt tool. This integer
   * encodes the package, type, and resource entry. The value 0 is an invalid identifier.
   * @return A single color value in the form 0xAARRGGBB.
   */
  @ColorInt
  public static int getColor(Context context, @ColorRes int colorId) {
    return ContextCompat.getColor(context, colorId);
  }

  public static String resolveString(Context context, @AttrRes int attr) {
    TypedValue v = new TypedValue();
    context.getTheme().resolveAttribute(attr, v, true);
    return (String) v.string;
  }

  private static int gravityEnumToAttrInt(GravityEnum value) {
    switch (value) {
      case CENTER:
        return 1;
      case END:
        return 2;
      default:
        return 0;
    }
  }

  public static GravityEnum resolveGravityEnum(Context context,
      @AttrRes int attr,
      GravityEnum defaultGravity) {
    TypedArray a = context.getTheme().obtainStyledAttributes(new int[]{attr});
    try {
      switch (a.getInt(0, gravityEnumToAttrInt(defaultGravity))) {
        case 1:
          return GravityEnum.CENTER;
        case 2:
          return GravityEnum.END;
        default:
          return GravityEnum.START;
      }
    } finally {
      a.recycle();
    }
  }

  public static Drawable resolveDrawable(Context context, @AttrRes int attr) {
    return resolveDrawable(context, attr, null);
  }

  private static Drawable resolveDrawable(Context context,
      @AttrRes int attr,
      @SuppressWarnings(
          "SameParameterValue") Drawable fallback) {
    TypedArray a = context.getTheme().obtainStyledAttributes(new int[]{attr});
    try {
      Drawable d = a.getDrawable(0);
      if (d == null && fallback != null) {
        d = fallback;
      }
      return d;
    } finally {
      a.recycle();
    }
  }

  public static int resolveDimension(Context context, @AttrRes int attr) {
    return resolveDimension(context, attr, -1);
  }

  private static int resolveDimension(Context context, @AttrRes int attr, int fallback) {
    TypedArray a = context.getTheme().obtainStyledAttributes(new int[]{attr});
    try {
      return a.getDimensionPixelSize(0, fallback);
    } finally {
      a.recycle();
    }
  }

  public static boolean resolveBoolean(Context context, @AttrRes int attr, boolean fallback) {
    TypedArray a = context.getTheme().obtainStyledAttributes(new int[]{attr});
    try {
      return a.getBoolean(0, fallback);
    } finally {
      a.recycle();
    }
  }

  public static boolean resolveBoolean(Context context, @AttrRes int attr) {
    return resolveBoolean(context, attr, false);
  }

  public static boolean isColorDark(@ColorInt int color) {
    double darkness = 1
        - (0.299 * Color.red(color) + 0.587 * Color.green(color) + 0.114 * Color.blue(color)) / 255;
    return darkness >= 0.5;
  }

  public static void setBackgroundCompat(View view, Drawable d) {
    if (Build.VERSION.SDK_INT < Build.VERSION_CODES.JELLY_BEAN) {
      //noinspection deprecation
      view.setBackgroundDrawable(d);
    } else {
      view.setBackground(d);
    }
  }

  public static void showKeyboard(@NonNull final DialogInterface di,
      @NonNull final MaterialDialog.Builder builder) {
    final MaterialDialog dialog = (MaterialDialog) di;
    if (dialog.getInputEditText() == null) {
      return;
    }
    dialog.getInputEditText().post(new Runnable() {
      @Override
      public void run() {
        dialog.getInputEditText().requestFocus();
        InputMethodManager imm = (InputMethodManager) builder.getContext()
            .getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null) {
          imm.showSoftInput(dialog.getInputEditText(), InputMethodManager.SHOW_IMPLICIT);
        }
      }
    });
  }

  public static void hideKeyboard(@NonNull final DialogInterface di,
      @NonNull final MaterialDialog.Builder builder) {
    final MaterialDialog dialog = (MaterialDialog) di;
    if (dialog.getInputEditText() == null) {
      return;
    }
    InputMethodManager imm = (InputMethodManager) builder.getContext()
        .getSystemService(Context.INPUT_METHOD_SERVICE);
    if (imm != null) {
      final View currentFocus = dialog.getCurrentFocus();
      final IBinder windowToken = currentFocus != null ?
          currentFocus.getWindowToken() : dialog.getView().getWindowToken();
      if (windowToken != null) {
        imm.hideSoftInputFromWindow(windowToken, 0);
      }
    }
  }

  public static ColorStateList getActionTextStateList(Context context, int newPrimaryColor) {
    final int fallBackButtonColor = DialogUtils
        .resolveColor(context, android.R.attr.textColorPrimary);
    if (newPrimaryColor == 0) {
      newPrimaryColor = fallBackButtonColor;
    }
    int[][] states = new int[][]{
        new int[]{-android.R.attr.state_enabled}, // disabled
        new int[]{} // enabled
    };
    int[] colors = new int[]{
        DialogUtils.adjustAlpha(newPrimaryColor, 0.4f),
        newPrimaryColor
    };
    return new ColorStateList(states, colors);
  }

  public static int[] getColorArray(@NonNull Context context, @ArrayRes int array) {
    if (array == 0) {
      return null;
    }
    TypedArray ta = context.getResources().obtainTypedArray(array);
    int[] colors = new int[ta.length()];
    for (int i = 0; i < ta.length(); i++) {
      colors[i] = ta.getColor(i, 0);
    }
    ta.recycle();
    return colors;
  }

  public static <T> boolean isIn(@NonNull T find, @Nullable T[] ary) {
    if (ary == null || ary.length == 0) {
      return false;
    }
    for (T item : ary) {
      if (item.equals(find)) {
        return true;
      }
    }
    return false;
  }
}