<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:paddingTop="@dimen/md_content_padding_top"
  android:paddingBottom="@dimen/md_content_padding_top"
  android:paddingLeft="@dimen/md_dialog_frame_margin"
  android:paddingRight="@dimen/md_dialog_frame_margin"
  android:gravity="start|center_vertical"
  android:orientation="horizontal">

  <ProgressBar
    android:id="@android:id/progress"
    style="@style/Widget.MaterialProgressBar.ProgressBar"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:indeterminate="true" />

  <TextView
    android:id="@+id/md_content"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingStart="16dp"
    android:paddingLeft="16dp"
    android:fontFamily="sans-serif"
    android:gravity="start"
    android:textAlignment="viewStart"
    android:textSize="@dimen/md_content_textsize"
    tools:ignore="NewApi,RtlSymmetry,UnusedAttribute"
    tools:text="Message" />

</LinearLayout>