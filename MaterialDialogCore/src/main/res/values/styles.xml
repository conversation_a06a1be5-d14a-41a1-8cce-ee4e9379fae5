<resources xmlns:tools="http://schemas.android.com/tools">

  <style name="MD_Light" parent="Theme.AppCompat.Light.Dialog.Alert">
    <item name="md_divider">@color/md_divider_black</item>
    <item name="md_list_selector">@drawable/md_selector</item>
    <item name="md_btn_stacked_selector">@drawable/md_selector</item>
    <item name="md_btn_positive_selector">@drawable/md_btn_selector</item>
    <item name="md_btn_neutral_selector">@drawable/md_btn_selector</item>
    <item name="md_btn_negative_selector">@drawable/md_btn_selector</item>

    <item name="android:windowAnimationStyle">@style/MD_WindowAnimation</item>
    <item name="android:backgroundDimEnabled">true</item>
  </style>

  <style name="MD_Dark" parent="Theme.AppCompat.Dialog.Alert">
    <item name="md_divider">@color/md_divider_white</item>
    <item name="md_list_selector">@drawable/md_selector_dark</item>
    <item name="md_btn_stacked_selector">@drawable/md_selector_dark</item>
    <item name="md_btn_positive_selector">@drawable/md_btn_selector_dark</item>
    <item name="md_btn_neutral_selector">@drawable/md_btn_selector_dark</item>
    <item name="md_btn_negative_selector">@drawable/md_btn_selector_dark</item>

    <item name="android:windowAnimationStyle">@style/MD_WindowAnimation</item>
    <item name="android:backgroundDimEnabled">true</item>
  </style>

  <style name="MD_ActionButtonStacked" parent="@style/MD_ActionButton" tools:ignore="NewApi">
    <item name="android:layout_width">match_parent</item>
    <item name="android:paddingLeft">@dimen/md_button_padding_horizontal_internalexternal</item>
    <item name="android:paddingStart">@dimen/md_button_padding_horizontal_internalexternal
    </item>
    <item name="android:paddingRight">@dimen/md_button_padding_horizontal_internalexternal
    </item>
    <item name="android:paddingEnd">@dimen/md_button_padding_horizontal_internalexternal</item>
  </style>

  <style name="MD_ActionButton">
    <item name="android:layout_width">wrap_content</item>
    <item name="android:layout_height">@dimen/md_button_height</item>
    <item name="android:layout_gravity">center_vertical</item>
  </style>

  <style name="MD_ActionButton.Text" tools:ignore="NewApi">
    <item name="android:textSize">@dimen/md_button_textsize</item>
    <item name="android:singleLine">true</item>
    <item name="android:layout_gravity">center_vertical</item>
    <item name="android:gravity">center</item>
    <item name="android:stateListAnimator">@null</item>
    <item name="android:background">@null</item>
    <item name="android:minWidth">@dimen/md_button_min_width</item>
    <item name="android:paddingLeft">@dimen/md_button_textpadding_horizontal</item>
    <item name="android:paddingRight">@dimen/md_button_textpadding_horizontal</item>
  </style>

  <style name="MD_WindowAnimation">
    <item name="android:windowEnterAnimation">@anim/popup_enter</item>
    <item name="android:windowExitAnimation">@anim/popup_exit</item>
  </style>

</resources>