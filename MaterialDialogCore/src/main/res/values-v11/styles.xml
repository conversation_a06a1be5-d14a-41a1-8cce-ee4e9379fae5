<?xml version="1.0" encoding="utf-8"?>
<resources>

  <style name="MD_Light" parent="Theme.AppCompat.Light.Dialog.Alert">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>

    <item name="md_divider">@color/md_divider_black</item>
    <item name="md_list_selector">@drawable/md_selector</item>
    <item name="md_btn_stacked_selector">@drawable/md_selector</item>
    <item name="md_btn_positive_selector">@drawable/md_btn_selector</item>
    <item name="md_btn_neutral_selector">@drawable/md_btn_selector</item>
    <item name="md_btn_negative_selector">@drawable/md_btn_selector</item>

    <item name="android:actionModeBackground">@color/primary_material_dark</item>
    <item name="android:actionModeCloseDrawable">@drawable/md_nav_back</item>

    <item name="android:windowAnimationStyle">@style/MD_WindowAnimation</item>
    <item name="android:backgroundDimEnabled">true</item>
  </style>

  <style name="MD_Dark" parent="Theme.AppCompat.Dialog.Alert">
    <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
    <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>

    <item name="md_divider">@color/md_divider_white</item>
    <item name="md_list_selector">@drawable/md_selector_dark</item>
    <item name="md_btn_stacked_selector">@drawable/md_selector_dark</item>
    <item name="md_btn_positive_selector">@drawable/md_btn_selector_dark</item>
    <item name="md_btn_neutral_selector">@drawable/md_btn_selector_dark</item>
    <item name="md_btn_negative_selector">@drawable/md_btn_selector_dark</item>

    <item name="android:actionModeBackground">@color/primary_material_dark</item>
    <item name="android:actionModeCloseDrawable">@drawable/md_nav_back</item>

    <item name="android:windowAnimationStyle">@style/MD_WindowAnimation</item>
    <item name="android:backgroundDimEnabled">true</item>
  </style>

</resources>