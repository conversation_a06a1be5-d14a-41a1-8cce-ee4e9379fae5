<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:id="@+id/md_titleFrame"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:gravity="center_vertical"
  android:orientation="horizontal"
  android:paddingBottom="@dimen/md_title_frame_margin_bottom"
  android:paddingLeft="@dimen/md_dialog_frame_margin"
  android:paddingRight="@dimen/md_dialog_frame_margin"
  android:paddingTop="@dimen/md_dialog_frame_margin">

  <ImageView
    android:id="@+id/md_icon"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/md_icon_margin"
    android:layout_marginRight="@dimen/md_icon_margin"
    android:scaleType="fitXY"
    tools:ignore="ContentDescription" />

  <TextView
    android:id="@+id/md_title"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:textSize="@dimen/md_title_textsize"
    tools:text="Title" />

</LinearLayout>