<com.afollestad.materialdialogs.internal.MDRootLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:app="http://schemas.android.com/apk/res-auto"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:orientation="vertical"
  app:md_reduce_padding_no_title_no_buttons="false">

  <include layout="@layout/md_stub_titleframe" />

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_weight="1"
    android:orientation="vertical"
    android:paddingBottom="@dimen/md_content_padding_bottom"
    android:paddingLeft="@dimen/md_dialog_frame_margin"
    android:paddingRight="@dimen/md_dialog_frame_margin"
    android:paddingTop="@dimen/md_content_padding_top">

    <include layout="@layout/md_stub_progress" />

  </LinearLayout>

  <include layout="@layout/md_stub_actionbuttons" />

</com.afollestad.materialdialogs.internal.MDRootLayout>