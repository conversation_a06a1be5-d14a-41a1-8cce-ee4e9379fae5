<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:id="@+id/md_colorChooserCustomFrame"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:orientation="vertical"
  android:paddingBottom="@dimen/md_title_frame_margin_bottom"
  android:paddingTop="@dimen/md_title_frame_margin_bottom">

  <View
    android:id="@+id/md_colorIndicator"
    android:layout_width="match_parent"
    android:layout_height="120dp"
    tools:background="@color/md_material_blue_600" />

  <LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity="center_horizontal"
    android:layout_marginTop="@dimen/md_title_frame_margin_bottom"
    android:gravity="center">

    <TextView
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_marginEnd="2dp"
      android:layout_marginRight="2dp"
      android:digits="0123456789abcdefABCDEF"
      android:text="#"
      android:textColor="?android:textColorPrimary"
      android:textColorHint="?android:textColorSecondary"
      android:textSize="@dimen/md_title_textsize"
      tools:ignore="HardcodedText,TextViewEdits" />

    <EditText
      android:id="@+id/md_hexInput"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:digits="0123456789abcdefABCDEF"
      android:focusable="true"
      android:hint="FF0099CC"
      android:textSize="@dimen/md_title_textsize"
      tools:ignore="HardcodedText" />

  </LinearLayout>

  <RelativeLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/md_title_frame_margin_bottom"
    android:paddingLeft="@dimen/md_dialog_frame_margin"
    android:paddingRight="@dimen/md_dialog_frame_margin">

    <!-- Alpha -->

    <TextView
      android:id="@+id/md_colorALabel"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignBottom="@+id/md_colorA"
      android:layout_alignTop="@+id/md_colorA"
      android:layout_marginEnd="4dp"
      android:layout_marginRight="4dp"
      android:text="A"
      android:textColor="?android:textColorPrimary"
      android:textSize="@dimen/md_content_textsize"
      tools:ignore="HardcodedText" />

    <SeekBar
      android:id="@+id/md_colorA"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_marginTop="@dimen/md_title_frame_margin_bottom"
      android:layout_toEndOf="@+id/md_colorALabel"
      android:layout_toLeftOf="@+id/md_colorAValue"
      android:layout_toRightOf="@+id/md_colorALabel"
      android:layout_toStartOf="@+id/md_colorAValue"
      android:focusable="true"
      android:max="255" />

    <TextView
      android:id="@+id/md_colorAValue"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignBottom="@+id/md_colorA"
      android:layout_alignParentEnd="true"
      android:layout_alignParentRight="true"
      android:layout_alignTop="@+id/md_colorA"
      android:layout_marginLeft="4dp"
      android:layout_marginStart="4dp"
      android:gravity="center"
      android:minWidth="24dp"
      android:text="0"
      android:textColor="?android:textColorPrimary"
      android:textSize="@dimen/md_content_textsize"
      tools:ignore="HardcodedText" />

    <!-- Red -->

    <TextView
      android:id="@+id/md_colorRLabel"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignBottom="@+id/md_colorR"
      android:layout_alignTop="@+id/md_colorR"
      android:layout_marginEnd="4dp"
      android:layout_marginRight="4dp"
      android:text="R"
      android:textColor="?android:textColorPrimary"
      android:textSize="@dimen/md_content_textsize"
      tools:ignore="HardcodedText" />

    <SeekBar
      android:id="@+id/md_colorR"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_below="@+id/md_colorA"
      android:layout_marginTop="@dimen/md_title_frame_margin_bottom"
      android:layout_toEndOf="@+id/md_colorRLabel"
      android:layout_toLeftOf="@+id/md_colorRValue"
      android:layout_toRightOf="@+id/md_colorRLabel"
      android:layout_toStartOf="@+id/md_colorRValue"
      android:focusable="true"
      android:max="255" />

    <TextView
      android:id="@+id/md_colorRValue"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignBottom="@+id/md_colorR"
      android:layout_alignParentEnd="true"
      android:layout_alignParentRight="true"
      android:layout_alignTop="@+id/md_colorR"
      android:layout_marginLeft="4dp"
      android:layout_marginStart="4dp"
      android:gravity="center"
      android:minWidth="24dp"
      android:text="0"
      android:textColor="?android:textColorPrimary"
      android:textSize="@dimen/md_content_textsize"
      tools:ignore="HardcodedText" />

    <!-- Green -->

    <TextView
      android:id="@+id/md_colorGLabel"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignBottom="@+id/md_colorG"
      android:layout_alignTop="@+id/md_colorG"
      android:layout_marginEnd="4dp"
      android:layout_marginRight="4dp"
      android:text="G"
      android:textColor="?android:textColorPrimary"
      android:textSize="@dimen/md_content_textsize"
      tools:ignore="HardcodedText" />

    <SeekBar
      android:id="@+id/md_colorG"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_below="@+id/md_colorR"
      android:layout_marginTop="@dimen/md_title_frame_margin_bottom"
      android:layout_toEndOf="@+id/md_colorGLabel"
      android:layout_toLeftOf="@+id/md_colorGValue"
      android:layout_toRightOf="@+id/md_colorGLabel"
      android:layout_toStartOf="@+id/md_colorGValue"
      android:focusable="true"
      android:max="255" />

    <TextView
      android:id="@+id/md_colorGValue"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignBottom="@+id/md_colorG"
      android:layout_alignParentEnd="true"
      android:layout_alignParentRight="true"
      android:layout_alignTop="@+id/md_colorG"
      android:layout_marginLeft="4dp"
      android:layout_marginStart="4dp"
      android:gravity="center"
      android:minWidth="24dp"
      android:text="0"
      android:textColor="?android:textColorPrimary"
      android:textSize="@dimen/md_content_textsize"
      tools:ignore="HardcodedText" />

    <!-- Blue -->

    <TextView
      android:id="@+id/md_colorBLabel"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignBottom="@+id/md_colorB"
      android:layout_alignTop="@+id/md_colorB"
      android:layout_marginEnd="4dp"
      android:layout_marginRight="4dp"
      android:text="B"
      android:textColor="?android:textColorPrimary"
      android:textSize="@dimen/md_content_textsize"
      tools:ignore="HardcodedText" />

    <SeekBar
      android:id="@+id/md_colorB"
      android:layout_width="match_parent"
      android:layout_height="wrap_content"
      android:layout_below="@+id/md_colorG"
      android:layout_marginTop="@dimen/md_title_frame_margin_bottom"
      android:layout_toEndOf="@+id/md_colorBLabel"
      android:layout_toLeftOf="@+id/md_colorBValue"
      android:layout_toRightOf="@+id/md_colorBLabel"
      android:layout_toStartOf="@+id/md_colorBValue"
      android:max="255" />

    <TextView
      android:id="@+id/md_colorBValue"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignBottom="@+id/md_colorB"
      android:layout_alignParentEnd="true"
      android:layout_alignParentRight="true"
      android:layout_alignTop="@+id/md_colorB"
      android:layout_marginLeft="4dp"
      android:layout_marginStart="4dp"
      android:gravity="center"
      android:minWidth="24dp"
      android:text="0"
      android:textColor="?android:textColorPrimary"
      android:textSize="@dimen/md_content_textsize"
      tools:ignore="HardcodedText" />

  </RelativeLayout>

</LinearLayout>
