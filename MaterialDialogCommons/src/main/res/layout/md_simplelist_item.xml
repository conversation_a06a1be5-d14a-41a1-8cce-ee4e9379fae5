<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:background="?selectableItemBackground"
  android:focusable="true"
  android:gravity="start|center_vertical"
  android:minHeight="@dimen/md_simpleitem_height"
  android:orientation="horizontal"
  android:paddingEnd="@dimen/md_dialog_frame_margin"
  android:paddingLeft="@dimen/md_dialog_frame_margin"
  android:paddingRight="@dimen/md_dialog_frame_margin"
  android:paddingStart="@dimen/md_dialog_frame_margin">

  <ImageView
    android:id="@android:id/icon"
    android:layout_width="@dimen/md_simplelist_icon"
    android:layout_height="@dimen/md_simplelist_icon"
    android:layout_gravity="start|center_vertical"
    android:layout_marginEnd="@dimen/md_simplelist_icon_margin"
    android:layout_marginRight="@dimen/md_simplelist_icon_margin"
    android:background="@drawable/gray_circle"
    android:scaleType="fitXY"
    tools:background="#f5f5f5"
    tools:ignore="ContentDescription" />

  <TextView
    android:id="@android:id/title"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center_vertical"
    android:textSize="@dimen/md_simplelist_textsize"
    tools:text="Title" />

</LinearLayout>