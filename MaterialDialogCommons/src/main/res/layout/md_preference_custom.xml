<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
  xmlns:tools="http://schemas.android.com/tools"
  android:layout_width="match_parent"
  android:layout_height="wrap_content"
  android:baselineAligned="false"
  android:gravity="center_vertical"
  android:minHeight="?android:attr/listPreferredItemHeight"
  android:orientation="horizontal"
  android:paddingEnd="?android:attr/scrollbarSize"
  android:paddingRight="?android:attr/scrollbarSize"
  tools:ignore="RtlSymmetry,UnusedAttribute">

  <ImageView
    android:id="@android:id/icon"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="center"
    android:layout_marginLeft="@dimen/md_preference_content_inset"
    android:layout_marginStart="@dimen/md_preference_content_inset"
    tools:ignore="ContentDescription" />

  <RelativeLayout
    android:layout_width="0dp"
    android:layout_height="wrap_content"
    android:layout_gravity="left|start|center_vertical"
    android:layout_marginBottom="12dip"
    android:layout_marginEnd="6dip"
    android:layout_marginLeft="@dimen/md_preference_content_inset"
    android:layout_marginRight="6dip"
    android:layout_marginStart="@dimen/md_preference_content_inset"
    android:layout_marginTop="12dip"
    android:layout_weight="1">

    <TextView
      android:id="@android:id/title"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignParentLeft="true"
      android:layout_alignParentStart="true"
      android:ellipsize="marquee"
      android:fadingEdge="horizontal"
      android:fontFamily="sans-serif"
      android:lines="1"
      android:textColor="?android:textColorPrimary"
      android:textSize="16sp"
      tools:text="Title" />

    <TextView
      android:id="@android:id/summary"
      android:layout_width="wrap_content"
      android:layout_height="wrap_content"
      android:layout_alignLeft="@android:id/title"
      android:layout_alignStart="@android:id/title"
      android:layout_below="@android:id/title"
      android:layout_marginTop="2dp"
      android:fontFamily="sans-serif"
      android:maxLines="4"
      android:textColor="?android:textColorSecondary"
      android:textSize="14sp"
      tools:text="Summary" />

  </RelativeLayout>

  <!-- Preference should place its actual preference widget here. -->
  <LinearLayout
    android:id="@android:id/widget_frame"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:layout_gravity="end|right|center_vertical"
    android:gravity="center_vertical"
    android:orientation="vertical" />

</LinearLayout>