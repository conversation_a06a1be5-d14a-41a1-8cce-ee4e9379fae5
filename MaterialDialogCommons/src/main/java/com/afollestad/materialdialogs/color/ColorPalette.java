package com.afollestad.materialdialogs.color;

import android.graphics.Color;

/**
 * <AUTHOR> (<PERSON><PERSON><PERSON><PERSON>)
 */
class ColorPalette {

  final static int[] PRIMARY_COLORS = new int[]{
      Color.parseColor("#F44336"),
      Color.parseColor("#E91E63"),
      Color.parseColor("#9C27B0"),
      Color.parseColor("#673AB7"),
      Color.parseColor("#3F51B5"),
      Color.parseColor("#2196F3"),
      Color.parseColor("#03A9F4"),
      Color.parseColor("#00BCD4"),
      Color.parseColor("#009688"),
      Color.parseColor("#4CAF50"),
      Color.parseColor("#8BC34A"),
      Color.parseColor("#CDDC39"),
      Color.parseColor("#FFEB3B"),
      Color.parseColor("#FFC107"),
      Color.parseColor("#FF9800"),
      Color.parseColor("#FF5722"),
      Color.parseColor("#795548"),
      Color.parseColor("#9E9E9E"),
      Color.parseColor("#607D8B")
  };

  final static int[][] PRIMARY_COLORS_SUB = new int[][]{
      new int[]{
          Color.parseColor("#FFEBEE"),
          Color.parseColor("#FFCDD2"),
          Color.parseColor("#EF9A9A"),
          Color.parseColor("#E57373"),
          Color.parseColor("#EF5350"),
          Color.parseColor("#F44336"),
          Color.parseColor("#E53935"),
          Color.parseColor("#D32F2F"),
          Color.parseColor("#C62828"),
          Color.parseColor("#B71C1C")
      },
      new int[]{
          Color.parseColor("#FCE4EC"),
          Color.parseColor("#F8BBD0"),
          Color.parseColor("#F48FB1"),
          Color.parseColor("#F06292"),
          Color.parseColor("#EC407A"),
          Color.parseColor("#E91E63"),
          Color.parseColor("#D81B60"),
          Color.parseColor("#C2185B"),
          Color.parseColor("#AD1457"),
          Color.parseColor("#880E4F")
      },
      new int[]{
          Color.parseColor("#F3E5F5"),
          Color.parseColor("#E1BEE7"),
          Color.parseColor("#CE93D8"),
          Color.parseColor("#BA68C8"),
          Color.parseColor("#AB47BC"),
          Color.parseColor("#9C27B0"),
          Color.parseColor("#8E24AA"),
          Color.parseColor("#7B1FA2"),
          Color.parseColor("#6A1B9A"),
          Color.parseColor("#4A148C")
      },
      new int[]{
          Color.parseColor("#EDE7F6"),
          Color.parseColor("#D1C4E9"),
          Color.parseColor("#B39DDB"),
          Color.parseColor("#9575CD"),
          Color.parseColor("#7E57C2"),
          Color.parseColor("#673AB7"),
          Color.parseColor("#5E35B1"),
          Color.parseColor("#512DA8"),
          Color.parseColor("#4527A0"),
          Color.parseColor("#311B92")
      },
      new int[]{
          Color.parseColor("#E8EAF6"),
          Color.parseColor("#C5CAE9"),
          Color.parseColor("#9FA8DA"),
          Color.parseColor("#7986CB"),
          Color.parseColor("#5C6BC0"),
          Color.parseColor("#3F51B5"),
          Color.parseColor("#3949AB"),
          Color.parseColor("#303F9F"),
          Color.parseColor("#283593"),
          Color.parseColor("#1A237E")
      },
      new int[]{
          Color.parseColor("#E3F2FD"),
          Color.parseColor("#BBDEFB"),
          Color.parseColor("#90CAF9"),
          Color.parseColor("#64B5F6"),
          Color.parseColor("#42A5F5"),
          Color.parseColor("#2196F3"),
          Color.parseColor("#1E88E5"),
          Color.parseColor("#1976D2"),
          Color.parseColor("#1565C0"),
          Color.parseColor("#0D47A1")
      },
      new int[]{
          Color.parseColor("#E1F5FE"),
          Color.parseColor("#B3E5FC"),
          Color.parseColor("#81D4FA"),
          Color.parseColor("#4FC3F7"),
          Color.parseColor("#29B6F6"),
          Color.parseColor("#03A9F4"),
          Color.parseColor("#039BE5"),
          Color.parseColor("#0288D1"),
          Color.parseColor("#0277BD"),
          Color.parseColor("#01579B")
      },
      new int[]{
          Color.parseColor("#E0F7FA"),
          Color.parseColor("#B2EBF2"),
          Color.parseColor("#80DEEA"),
          Color.parseColor("#4DD0E1"),
          Color.parseColor("#26C6DA"),
          Color.parseColor("#00BCD4"),
          Color.parseColor("#00ACC1"),
          Color.parseColor("#0097A7"),
          Color.parseColor("#00838F"),
          Color.parseColor("#006064")
      },
      new int[]{
          Color.parseColor("#E0F2F1"),
          Color.parseColor("#B2DFDB"),
          Color.parseColor("#80CBC4"),
          Color.parseColor("#4DB6AC"),
          Color.parseColor("#26A69A"),
          Color.parseColor("#009688"),
          Color.parseColor("#00897B"),
          Color.parseColor("#00796B"),
          Color.parseColor("#00695C"),
          Color.parseColor("#004D40")
      },
      new int[]{
          Color.parseColor("#E8F5E9"),
          Color.parseColor("#C8E6C9"),
          Color.parseColor("#A5D6A7"),
          Color.parseColor("#81C784"),
          Color.parseColor("#66BB6A"),
          Color.parseColor("#4CAF50"),
          Color.parseColor("#43A047"),
          Color.parseColor("#388E3C"),
          Color.parseColor("#2E7D32"),
          Color.parseColor("#1B5E20")
      },
      new int[]{
          Color.parseColor("#F1F8E9"),
          Color.parseColor("#DCEDC8"),
          Color.parseColor("#C5E1A5"),
          Color.parseColor("#AED581"),
          Color.parseColor("#9CCC65"),
          Color.parseColor("#8BC34A"),
          Color.parseColor("#7CB342"),
          Color.parseColor("#689F38"),
          Color.parseColor("#558B2F"),
          Color.parseColor("#33691E")
      },
      new int[]{
          Color.parseColor("#F9FBE7"),
          Color.parseColor("#F0F4C3"),
          Color.parseColor("#E6EE9C"),
          Color.parseColor("#DCE775"),
          Color.parseColor("#D4E157"),
          Color.parseColor("#CDDC39"),
          Color.parseColor("#C0CA33"),
          Color.parseColor("#AFB42B"),
          Color.parseColor("#9E9D24"),
          Color.parseColor("#827717")
      },
      new int[]{
          Color.parseColor("#FFFDE7"),
          Color.parseColor("#FFF9C4"),
          Color.parseColor("#FFF59D"),
          Color.parseColor("#FFF176"),
          Color.parseColor("#FFEE58"),
          Color.parseColor("#FFEB3B"),
          Color.parseColor("#FDD835"),
          Color.parseColor("#FBC02D"),
          Color.parseColor("#F9A825"),
          Color.parseColor("#F57F17")
      },
      new int[]{
          Color.parseColor("#FFF8E1"),
          Color.parseColor("#FFECB3"),
          Color.parseColor("#FFE082"),
          Color.parseColor("#FFD54F"),
          Color.parseColor("#FFCA28"),
          Color.parseColor("#FFC107"),
          Color.parseColor("#FFB300"),
          Color.parseColor("#FFA000"),
          Color.parseColor("#FF8F00"),
          Color.parseColor("#FF6F00")
      },
      new int[]{
          Color.parseColor("#FFF3E0"),
          Color.parseColor("#FFE0B2"),
          Color.parseColor("#FFCC80"),
          Color.parseColor("#FFB74D"),
          Color.parseColor("#FFA726"),
          Color.parseColor("#FF9800"),
          Color.parseColor("#FB8C00"),
          Color.parseColor("#F57C00"),
          Color.parseColor("#EF6C00"),
          Color.parseColor("#E65100")
      },
      new int[]{
          Color.parseColor("#FBE9E7"),
          Color.parseColor("#FFCCBC"),
          Color.parseColor("#FFAB91"),
          Color.parseColor("#FF8A65"),
          Color.parseColor("#FF7043"),
          Color.parseColor("#FF5722"),
          Color.parseColor("#F4511E"),
          Color.parseColor("#E64A19"),
          Color.parseColor("#D84315"),
          Color.parseColor("#BF360C")
      },
      new int[]{
          Color.parseColor("#EFEBE9"),
          Color.parseColor("#D7CCC8"),
          Color.parseColor("#BCAAA4"),
          Color.parseColor("#A1887F"),
          Color.parseColor("#8D6E63"),
          Color.parseColor("#795548"),
          Color.parseColor("#6D4C41"),
          Color.parseColor("#5D4037"),
          Color.parseColor("#4E342E"),
          Color.parseColor("#3E2723")
      },
      new int[]{
          Color.parseColor("#FAFAFA"),
          Color.parseColor("#F5F5F5"),
          Color.parseColor("#EEEEEE"),
          Color.parseColor("#E0E0E0"),
          Color.parseColor("#BDBDBD"),
          Color.parseColor("#9E9E9E"),
          Color.parseColor("#757575"),
          Color.parseColor("#616161"),
          Color.parseColor("#424242"),
          Color.parseColor("#212121")
      },
      new int[]{
          Color.parseColor("#ECEFF1"),
          Color.parseColor("#CFD8DC"),
          Color.parseColor("#B0BEC5"),
          Color.parseColor("#90A4AE"),
          Color.parseColor("#78909C"),
          Color.parseColor("#607D8B"),
          Color.parseColor("#546E7A"),
          Color.parseColor("#455A64"),
          Color.parseColor("#37474F"),
          Color.parseColor("#263238")
      }
  };

  final static int[] ACCENT_COLORS = new int[]{
      Color.parseColor("#FF1744"),
      Color.parseColor("#F50057"),
      Color.parseColor("#D500F9"),
      Color.parseColor("#651FFF"),
      Color.parseColor("#3D5AFE"),
      Color.parseColor("#2979FF"),
      Color.parseColor("#00B0FF"),
      Color.parseColor("#00E5FF"),
      Color.parseColor("#1DE9B6"),
      Color.parseColor("#00E676"),
      Color.parseColor("#76FF03"),
      Color.parseColor("#C6FF00"),
      Color.parseColor("#FFEA00"),
      Color.parseColor("#FFC400"),
      Color.parseColor("#FF9100"),
      Color.parseColor("#FF3D00")
  };

  final static int[][] ACCENT_COLORS_SUB = new int[][]{
      new int[]{
          Color.parseColor("#FF8A80"),
          Color.parseColor("#FF5252"),
          Color.parseColor("#FF1744"),
          Color.parseColor("#D50000")
      },
      new int[]{
          Color.parseColor("#FF80AB"),
          Color.parseColor("#FF4081"),
          Color.parseColor("#F50057"),
          Color.parseColor("#C51162")
      },
      new int[]{
          Color.parseColor("#EA80FC"),
          Color.parseColor("#E040FB"),
          Color.parseColor("#D500F9"),
          Color.parseColor("#AA00FF")
      },
      new int[]{
          Color.parseColor("#B388FF"),
          Color.parseColor("#7C4DFF"),
          Color.parseColor("#651FFF"),
          Color.parseColor("#6200EA")
      },
      new int[]{
          Color.parseColor("#8C9EFF"),
          Color.parseColor("#536DFE"),
          Color.parseColor("#3D5AFE"),
          Color.parseColor("#304FFE")
      },
      new int[]{
          Color.parseColor("#82B1FF"),
          Color.parseColor("#448AFF"),
          Color.parseColor("#2979FF"),
          Color.parseColor("#2962FF")
      },
      new int[]{
          Color.parseColor("#80D8FF"),
          Color.parseColor("#40C4FF"),
          Color.parseColor("#00B0FF"),
          Color.parseColor("#0091EA")
      },
      new int[]{
          Color.parseColor("#84FFFF"),
          Color.parseColor("#18FFFF"),
          Color.parseColor("#00E5FF"),
          Color.parseColor("#00B8D4")
      },
      new int[]{
          Color.parseColor("#A7FFEB"),
          Color.parseColor("#64FFDA"),
          Color.parseColor("#1DE9B6"),
          Color.parseColor("#00BFA5")
      },
      new int[]{
          Color.parseColor("#B9F6CA"),
          Color.parseColor("#69F0AE"),
          Color.parseColor("#00E676"),
          Color.parseColor("#00C853")
      },
      new int[]{
          Color.parseColor("#CCFF90"),
          Color.parseColor("#B2FF59"),
          Color.parseColor("#76FF03"),
          Color.parseColor("#64DD17")
      },
      new int[]{
          Color.parseColor("#F4FF81"),
          Color.parseColor("#EEFF41"),
          Color.parseColor("#C6FF00"),
          Color.parseColor("#AEEA00")
      },
      new int[]{
          Color.parseColor("#FFFF8D"),
          Color.parseColor("#FFFF00"),
          Color.parseColor("#FFEA00"),
          Color.parseColor("#FFD600")
      },
      new int[]{
          Color.parseColor("#FFE57F"),
          Color.parseColor("#FFD740"),
          Color.parseColor("#FFC400"),
          Color.parseColor("#FFAB00")
      },
      new int[]{
          Color.parseColor("#FFD180"),
          Color.parseColor("#FFAB40"),
          Color.parseColor("#FF9100"),
          Color.parseColor("#FF6D00")
      },
      new int[]{
          Color.parseColor("#FF9E80"),
          Color.parseColor("#FF6E40"),
          Color.parseColor("#FF3D00"),
          Color.parseColor("#DD2C00")
      }
  };
}
