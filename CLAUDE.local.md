# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Core Build Commands
- `./gradlew build` - Assembles and tests the project
- `./gradlew clean` - Deletes build directory
- `./gradlew assembleDebug` - Builds debug APK
- `./gradlew assembleRelease` - Builds release APK (requires signing config)
- `./gradlew installDebug` - Installs debug build to connected device/emulator
- `./gradlew uninstallDebug` - Uninstalls debug build

### Testing Commands
- `./gradlew test` - Runs unit tests
- `./gradlew connectedAndroidTest` - Runs instrumented tests on device/emulator
- `./gradlew check` - Runs all checks including tests and lint

### Code Quality
- `./gradlew lint` - Runs Android lint checks
- `./gradlew lintDebug` - Runs lint on debug variant only

## Architecture Overview

### Multi-Module Structure
- **InspectionFolio**: Main application module containing all app logic and UI
- **MaterialDialogCore/Commons**: Custom dialog components
- **FFmpegAndroid**: Video processing functionality (appears to be empty in current state)
- **z-validations-library**: Input validation utilities

### Key Technologies
- **Language**: Java + Kotlin hybrid (migrating to Kotlin with Compose)
- **UI Framework**: Mix of traditional Android Views + Jetpack Compose for new features
- **Database**: Sugar ORM for SQLite operations (`com.orm.SugarContext`)
- **Architecture**: Activity-based with fragments, moving toward Compose
- **Dependency Injection**: None (uses manual dependency management)
- **Image Loading**: Picasso
- **Networking**: Custom REST client (`IF_RestClient`) + AWS SDK
- **Camera**: Custom CameraX implementation for photo/video capture

### Core Application Structure
- **Main App Class**: `com.snapinspect.snapinspect3.app.App` (MultiDexApplication)
- **Database**: Sugar ORM with version 11, database file: `if_data.db`
- **Package Structure**: `com.snapinspect.snapinspect3.*`
- **Entry Point**: `if_login` activity (portrait-only login screen)

### Key Business Logic Areas
- **Asset Management**: Property/asset inspection and documentation
- **Inspection Workflow**: Multi-step inspection process with photos, videos, comments
- **Synchronization**: Offline-first with background sync to server
- **Floor Plans**: Interactive floor plan viewing and annotation
- **Video Processing**: Recording and merging video clips for inspections
- **Task Management**: Assignment and tracking of inspection tasks
- **Authentication**: Microsoft SSO integration + traditional login

### Database Schema (Sugar ORM)
Key entities include:
- `ai_Assets`, `ai_Inspection`, `ai_InsItem` - Core inspection data
- `ai_Project`, `ai_Schedule`, `ai_Task` - Project management
- `ai_Photo`, `ai_Video`, `ai_File` - Media management
- `ai_User`, `ai_Contact` - User management

### Configuration Notes
- **Min SDK**: 24 (Android 7.0)
- **Target SDK**: 35 (Android 14+)
- **Signing**: Debug and release keystores in `Keystores/` directory
- **Permissions**: Camera, location, storage, network access
- **Firebase**: Messaging, Crashlytics, Analytics configured
- **Deep Linking**: Supports custom URL schemes for external integrations

### Testing Strategy
- Unit tests using JUnit 5 + Robolectric for Android components
- Tests located in `src/test/java/`
- No UI automation tests currently configured

### Development Notes
- App uses legacy external storage (`requestLegacyExternalStorage="true"`)
- Multi-dex enabled for large dependency set
- Portrait orientation enforced on most screens
- Heavy use of custom UI components and adapters
- Background services for sync and upload operations