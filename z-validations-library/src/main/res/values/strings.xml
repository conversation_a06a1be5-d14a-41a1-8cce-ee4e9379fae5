<?xml version="1.0" encoding="utf-8"?>
<!--
  ~ The MIT License (MIT)
  ~
  ~ Copyright (c) 2013 Vita<PERSON>y <PERSON>
  ~
  ~ Permission is hereby granted, free of charge, to any person obtaining a copy of
  ~ this software and associated documentation files (the "Software"), to deal in
  ~ the Software without restriction, including without limitation the rights to
  ~ use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of
  ~ the Software, and to permit persons to whom the Software is furnished to do so,
  ~ subject to the following conditions:
  ~
  ~ The above copyright notice and this permission notice shall be included in all
  ~ copies or substantial portions of the Software.
  ~
  ~ THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
  ~ IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
  ~ FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
  ~ COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
  ~ IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
  ~ CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  -->

<resources>

    <string name="lib_name">Z-Validations</string>

    <string name="zvalidations_not_positive_integer">Field should be positive integer</string>
    <string name="zvalidations_empty">Field could not be empty</string>
    <string name="zvalidations_not_in_range">Field should be between %1$d and %2$d</string>
    <string name="zvalidations_not_email">Value is not valid email address</string>
    <string name="zvalidations_too_short">Minimum field length is %1$d characters</string>
    <string name="zvalidations_not_phone">Please enter your phone number, only numbers are allowed</string>
    <string name="zvalidations_not_match">Password and Repeat Password not match. Please try again.</string>
</resources>
