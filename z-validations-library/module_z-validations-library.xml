<?xml version="1.0" encoding="UTF-8"?>
<project name="module_z-validations-library" default="compile.module.z-validations-library">
  <dirname property="module.z-validations-library.basedir" file="${ant.file.module_z-validations-library}"/>
  
  <property name="module.jdk.home.z-validations-library" value="${jdk.home.android_api_19_platform}"/>
  <property name="module.jdk.bin.z-validations-library" value="${jdk.bin.android_api_19_platform}"/>
  <property name="module.jdk.classpath.z-validations-library" value="jdk.classpath.android_api_19_platform"/>
  
  <property name="compiler.args.z-validations-library" value="-encoding UTF-8 -source 1.6 -target 1.6 ${compiler.args}"/>
  
  <property name="z-validations-library.output.dir" value="${module.z-validations-library.basedir}/build/intermediates/classes/debug"/>
  <property name="z-validations-library.testoutput.dir" value="${module.z-validations-library.basedir}/build/intermediates/classes/test/debug"/>
  
  <path id="z-validations-library.module.bootclasspath">
    <!-- Paths to be included in compilation bootclasspath -->
  </path>
  
  <path id="z-validations-library.module.production.classpath">
    <path refid="${module.jdk.classpath.z-validations-library}"/>
  </path>
  
  <path id="z-validations-library.runtime.production.module.classpath">
    <pathelement location="${z-validations-library.output.dir}"/>
  </path>
  
  <path id="z-validations-library.module.classpath">
    <path refid="${module.jdk.classpath.z-validations-library}"/>
    <pathelement location="${z-validations-library.output.dir}"/>
  </path>
  
  <path id="z-validations-library.runtime.module.classpath">
    <pathelement location="${z-validations-library.testoutput.dir}"/>
    <pathelement location="${z-validations-library.output.dir}"/>
  </path>
  
  
  <patternset id="excluded.from.module.z-validations-library">
    <patternset refid="ignored.files"/>
  </patternset>
  
  <patternset id="excluded.from.compilation.z-validations-library">
    <patternset refid="excluded.from.module.z-validations-library"/>
  </patternset>
  
  <path id="z-validations-library.module.sourcepath">
    <dirset dir="${module.z-validations-library.basedir}">
      <include name="build/generated/source/r/debug"/>
      <include name="build/generated/source/aidl/debug"/>
      <include name="build/generated/source/buildConfig/debug"/>
      <include name="build/generated/source/rs/debug"/>
      <include name="build/generated/res/rs/debug"/>
      <include name="src/main/res"/>
      <include name="src/main/java"/>
    </dirset>
  </path>
  
  <path id="z-validations-library.module.test.sourcepath">
    <dirset dir="${module.z-validations-library.basedir}">
      <include name="build/generated/source/r/androidTest/debug"/>
      <include name="build/generated/source/aidl/androidTest/debug"/>
      <include name="build/generated/source/buildConfig/androidTest/debug"/>
      <include name="build/generated/source/rs/androidTest/debug"/>
      <include name="build/generated/res/rs/androidTest/debug"/>
    </dirset>
  </path>
  
  
  <target name="compile.module.z-validations-library" depends="compile.module.z-validations-library.production,compile.module.z-validations-library.tests" description="Compile module z-validations-library"/>
  
  <target name="compile.module.z-validations-library.production" depends="register.custom.compilers" description="Compile module z-validations-library; production classes">
    <mkdir dir="${z-validations-library.output.dir}"/>
    <javac2 destdir="${z-validations-library.output.dir}" debug="${compiler.debug}" nowarn="${compiler.generate.no.warnings}" memorymaximumsize="${compiler.max.memory}" fork="true" executable="${module.jdk.bin.z-validations-library}/javac">
      <compilerarg line="${compiler.args.z-validations-library}"/>
      <bootclasspath refid="z-validations-library.module.bootclasspath"/>
      <classpath refid="z-validations-library.module.production.classpath"/>
      <src refid="z-validations-library.module.sourcepath"/>
      <patternset refid="excluded.from.compilation.z-validations-library"/>
    </javac2>
    
    <copy todir="${z-validations-library.output.dir}">
      <fileset dir="${module.z-validations-library.basedir}/build/generated/source/r/debug">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.z-validations-library.basedir}/build/generated/source/aidl/debug">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.z-validations-library.basedir}/build/generated/source/buildConfig/debug">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.z-validations-library.basedir}/build/generated/source/rs/debug">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.z-validations-library.basedir}/build/generated/res/rs/debug">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.z-validations-library.basedir}/src/main/res">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.z-validations-library.basedir}/src/main/java">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
    </copy>
  </target>
  
  <target name="compile.module.z-validations-library.tests" depends="register.custom.compilers,compile.module.z-validations-library.production" description="compile module z-validations-library; test classes" unless="skip.tests">
    <mkdir dir="${z-validations-library.testoutput.dir}"/>
    <javac2 destdir="${z-validations-library.testoutput.dir}" debug="${compiler.debug}" nowarn="${compiler.generate.no.warnings}" memorymaximumsize="${compiler.max.memory}" fork="true" executable="${module.jdk.bin.z-validations-library}/javac">
      <compilerarg line="${compiler.args.z-validations-library}"/>
      <bootclasspath refid="z-validations-library.module.bootclasspath"/>
      <classpath refid="z-validations-library.module.classpath"/>
      <src refid="z-validations-library.module.test.sourcepath"/>
      <patternset refid="excluded.from.compilation.z-validations-library"/>
    </javac2>
    
    <copy todir="${z-validations-library.testoutput.dir}">
      <fileset dir="${module.z-validations-library.basedir}/build/generated/source/r/androidTest/debug">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.z-validations-library.basedir}/build/generated/source/aidl/androidTest/debug">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.z-validations-library.basedir}/build/generated/source/buildConfig/androidTest/debug">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.z-validations-library.basedir}/build/generated/source/rs/androidTest/debug">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
      <fileset dir="${module.z-validations-library.basedir}/build/generated/res/rs/androidTest/debug">
        <patternset refid="compiler.resources"/>
        <type type="file"/>
      </fileset>
    </copy>
  </target>
  
  <target name="clean.module.z-validations-library" description="cleanup module">
    <delete dir="${z-validations-library.output.dir}"/>
    <delete dir="${z-validations-library.testoutput.dir}"/>
  </target>
</project>