{"formatVersion": 1, "database": {"version": 1, "identityHash": "e13ab365fabac4c943d523ab465a848a", "entities": [{"tableName": "AIASSETS", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_ASSET_ID` INTEGER, `I_SP_ASSET_ID` INTEGER, `S_ADDRESS_ONE` TEXT, `S_ADDRESS_TWO` TEXT, `S_FILTER` TEXT, `I_CUSTOMER_ID` INTEGER, `I_GROUP_ID` INTEGER, `I_PL_VER_ID` INTEGER, `DT_INS_DUE` TEXT, `S_NOTES` TEXT, `S_KEY` TEXT, `S_ALARM` TEXT, `S_FIELD_ONE` TEXT, `S_FIELD_TWO` TEXT, `S_FIELD_THREE` TEXT, `B_DELETED` INTEGER, `B_PUSH` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSAssetID", "columnName": "I_S_ASSET_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSPAssetID", "columnName": "I_SP_ASSET_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sAddressOne", "columnName": "S_ADDRESS_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sAddressTwo", "columnName": "S_ADDRESS_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFilter", "columnName": "S_FILTER", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iCustomerID", "columnName": "I_CUSTOMER_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iGroupID", "columnName": "I_GROUP_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iPLVerID", "columnName": "I_PL_VER_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtInsDue", "columnName": "DT_INS_DUE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sNotes", "columnName": "S_NOTES", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s<PERSON>ey", "columnName": "S_KEY", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sAlarm", "columnName": "S_ALARM", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldOne", "columnName": "S_FIELD_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldTwo", "columnName": "S_FIELD_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s<PERSON><PERSON><PERSON><PERSON>ee", "columnName": "S_FIELD_THREE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bPush", "columnName": "B_PUSH", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AIASSETS_I_S_ASSET_ID", "unique": true, "columnNames": ["I_S_ASSET_ID"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_AIASSETS_I_S_ASSET_ID` ON `${TABLE_NAME}` (`I_S_ASSET_ID`)"}, {"name": "index_AIASSETS_I_CUSTOMER_ID", "unique": false, "columnNames": ["I_CUSTOMER_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIASSETS_I_CUSTOMER_ID` ON `${TABLE_NAME}` (`I_CUSTOMER_ID`)"}, {"name": "index_AIASSETS_I_SP_ASSET_ID", "unique": false, "columnNames": ["I_SP_ASSET_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIASSETS_I_SP_ASSET_ID` ON `${TABLE_NAME}` (`I_SP_ASSET_ID`)"}, {"name": "index_AIASSETS_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIASSETS_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}], "foreignKeys": []}, {"tableName": "AIINSPECTION", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_INS_ID` INTEGER, `I_S_ASSET_ID` INTEGER, `I_S_INS_TYPE_ID` INTEGER, `I_S_SCHEDULE_ID` INTEGER, `S_TITLE` TEXT, `S_INS_TITLE` TEXT, `S_COMMENTS` TEXT, `S_PTC` TEXT, `S_TYPE` TEXT, `DT_START_DATE` TEXT, `DT_END_DATE` TEXT, `S_LAT` TEXT, `S_LONG` TEXT, `S_ADDRESS_ONE` TEXT, `S_ADDRESS_TWO` TEXT, `S_CUSTOM_ONE` TEXT, `S_CUSTOM_TWO` TEXT, `B_COMPLETE` INTEGER, `B_DELETED` INTEGER, `B_LOCK` INTEGER, `B_SYNCED` INTEGER, FOREI<PERSON>N KEY(`I_S_ASSET_ID`) REFERENCES `AIASSETS`(`I_S_ASSET_ID`) ON UPDATE NO ACTION ON DELETE NO ACTION )", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSInsID", "columnName": "I_S_INS_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSAssetID", "columnName": "I_S_ASSET_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSInsTypeID", "columnName": "I_S_INS_TYPE_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSScheduleID", "columnName": "I_S_SCHEDULE_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sTitle", "columnName": "S_TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sInsTitle", "columnName": "S_INS_TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sComments", "columnName": "S_COMMENTS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sPTC", "columnName": "S_PTC", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sType", "columnName": "S_TYPE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dtStartDate", "columnName": "DT_START_DATE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dtEndDate", "columnName": "DT_END_DATE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sLat", "columnName": "S_LAT", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sLong", "columnName": "S_LONG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sAddressOne", "columnName": "S_ADDRESS_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sAddressTwo", "columnName": "S_ADDRESS_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomOne", "columnName": "S_CUSTOM_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomTwo", "columnName": "S_CUSTOM_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bComplete", "columnName": "B_COMPLETE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bLock", "columnName": "B_LOCK", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bSynced", "columnName": "B_SYNCED", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AIINSPECTION_I_S_ASSET_ID", "unique": false, "columnNames": ["I_S_ASSET_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINSPECTION_I_S_ASSET_ID` ON `${TABLE_NAME}` (`I_S_ASSET_ID`)"}, {"name": "index_AIINSPECTION_I_S_INS_ID", "unique": false, "columnNames": ["I_S_INS_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINSPECTION_I_S_INS_ID` ON `${TABLE_NAME}` (`I_S_INS_ID`)"}, {"name": "index_AIINSPECTION_I_S_INS_TYPE_ID", "unique": false, "columnNames": ["I_S_INS_TYPE_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINSPECTION_I_S_INS_TYPE_ID` ON `${TABLE_NAME}` (`I_S_INS_TYPE_ID`)"}, {"name": "index_AIINSPECTION_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINSPECTION_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}, {"name": "index_AIINSPECTION_B_COMPLETE", "unique": false, "columnNames": ["B_COMPLETE"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINSPECTION_B_COMPLETE` ON `${TABLE_NAME}` (`B_COMPLETE`)"}, {"name": "index_AIINSPECTION_B_SYNCED", "unique": false, "columnNames": ["B_SYNCED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINSPECTION_B_SYNCED` ON `${TABLE_NAME}` (`B_SYNCED`)"}], "foreignKeys": [{"table": "AIASSETS", "onDelete": "NO ACTION", "onUpdate": "NO ACTION", "columns": ["I_S_ASSET_ID"], "referencedColumns": ["I_S_ASSET_ID"]}]}, {"tableName": "AIINS_ITEM", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_INS_ID` INTEGER, `I_P_INS_ITEM_ID` INTEGER, `I_S_LAYOUT_ID` INTEGER, `I_S_ASSET_LAYOUT_ID` INTEGER, `I_SORT` INTEGER, `S_NAME` TEXT, `S_NAME_CHANGED` TEXT, `S_Q_TYPE` TEXT, `S_VALUE_ONE` TEXT, `S_VALUE_TWO` TEXT, `S_VALUE_THREE` TEXT, `S_VALUE_FOUR` TEXT, `S_VALUE_FIVE` TEXT, `S_VALUE_SIX` TEXT, `S_CONFIG_ONE` TEXT, `S_CONFIG_TWO` TEXT, `S_CONFIG_THREE` TEXT, `S_CONFIG_FOUR` TEXT, `S_CONFIG_FIVE` TEXT, `S_CONFIG_SIX` TEXT, `S_CONFIG` TEXT, `S_CUSTOM_ONE` TEXT, `S_CUSTOM_TWO` TEXT, `B_COMPLETED` INTEGER, `B_DELETED` INTEGER, FOREIGN KEY(`I_INS_ID`) REFERENCES `AIINSPECTION`(`ID`) ON UPDATE NO ACTION ON DELETE NO ACTION )", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInsID", "columnName": "I_INS_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iPInsItemID", "columnName": "I_P_INS_ITEM_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSLayoutID", "columnName": "I_S_LAYOUT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSAssetLayoutID", "columnName": "I_S_ASSET_LAYOUT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSort", "columnName": "I_SORT", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sName", "columnName": "S_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sNameChanged", "columnName": "S_NAME_CHANGED", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sQType", "columnName": "S_Q_TYPE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sValueOne", "columnName": "S_VALUE_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sValueTwo", "columnName": "S_VALUE_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s<PERSON><PERSON>ueThr<PERSON>", "columnName": "S_VALUE_THREE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sValueFour", "columnName": "S_VALUE_FOUR", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sValueFive", "columnName": "S_VALUE_FIVE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sValueSix", "columnName": "S_VALUE_SIX", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sConfigOne", "columnName": "S_CONFIG_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sConfigTwo", "columnName": "S_CONFIG_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sConfigThree", "columnName": "S_CONFIG_THREE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sConfigFour", "columnName": "S_CONFIG_FOUR", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sConfigFive", "columnName": "S_CONFIG_FIVE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sConfigSix", "columnName": "S_CONFIG_SIX", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sConfig", "columnName": "S_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomOne", "columnName": "S_CUSTOM_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomTwo", "columnName": "S_CUSTOM_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bCompleted", "columnName": "B_COMPLETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AIINS_ITEM_I_INS_ID", "unique": false, "columnNames": ["I_INS_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINS_ITEM_I_INS_ID` ON `${TABLE_NAME}` (`I_INS_ID`)"}, {"name": "index_AIINS_ITEM_I_P_INS_ITEM_ID", "unique": false, "columnNames": ["I_P_INS_ITEM_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINS_ITEM_I_P_INS_ITEM_ID` ON `${TABLE_NAME}` (`I_P_INS_ITEM_ID`)"}, {"name": "index_AIINS_ITEM_I_S_LAYOUT_ID", "unique": false, "columnNames": ["I_S_LAYOUT_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINS_ITEM_I_S_LAYOUT_ID` ON `${TABLE_NAME}` (`I_S_LAYOUT_ID`)"}, {"name": "index_AIINS_ITEM_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINS_ITEM_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}, {"name": "index_AIINS_ITEM_B_COMPLETED", "unique": false, "columnNames": ["B_COMPLETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIINS_ITEM_B_COMPLETED` ON `${TABLE_NAME}` (`B_COMPLETED`)"}], "foreignKeys": [{"table": "AIINSPECTION", "onDelete": "NO ACTION", "onUpdate": "NO ACTION", "columns": ["I_INS_ID"], "referencedColumns": ["ID"]}]}, {"tableName": "AIPHOTO", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_INS_ID` INTEGER, `I_INS_ITEM_ID` INTEGER, `I_S_PHOTO_ID` INTEGER, `I_SIZE` INTEGER, `S_FILE` TEXT, `S_THUMB` TEXT, `S_COMMENTS` TEXT, `S_LAT` TEXT, `S_LONG` TEXT, `S_FIELD_ONE` TEXT, `DT_DATE_TIME` TEXT, `B_DELETED` INTEGER, `B_UPLOADED` INTEGER, FOREIGN KEY(`I_INS_ID`) REFERENCES `AIINSPECTION`(`ID`) ON UPDATE NO ACTION ON DELETE NO ACTION , FOREIGN KEY(`I_INS_ITEM_ID`) REFERENCES `AIINS_ITEM`(`ID`) ON UPDATE NO ACTION ON DELETE NO ACTION )", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInsID", "columnName": "I_INS_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInsItemID", "columnName": "I_INS_ITEM_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSPhotoID", "columnName": "I_S_PHOTO_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSize", "columnName": "I_SIZE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sFile", "columnName": "S_FILE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sThumb", "columnName": "S_THUMB", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sComments", "columnName": "S_COMMENTS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sLat", "columnName": "S_LAT", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sLong", "columnName": "S_LONG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldOne", "columnName": "S_FIELD_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bUploaded", "columnName": "B_UPLOADED", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AIPHOTO_I_INS_ID", "unique": false, "columnNames": ["I_INS_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPHOTO_I_INS_ID` ON `${TABLE_NAME}` (`I_INS_ID`)"}, {"name": "index_AIPHOTO_I_INS_ITEM_ID", "unique": false, "columnNames": ["I_INS_ITEM_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPHOTO_I_INS_ITEM_ID` ON `${TABLE_NAME}` (`I_INS_ITEM_ID`)"}, {"name": "index_AIPHOTO_I_S_PHOTO_ID", "unique": false, "columnNames": ["I_S_PHOTO_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPHOTO_I_S_PHOTO_ID` ON `${TABLE_NAME}` (`I_S_PHOTO_ID`)"}, {"name": "index_AIPHOTO_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPHOTO_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}, {"name": "index_AIPHOTO_B_UPLOADED", "unique": false, "columnNames": ["B_UPLOADED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPHOTO_B_UPLOADED` ON `${TABLE_NAME}` (`B_UPLOADED`)"}], "foreignKeys": [{"table": "AIINSPECTION", "onDelete": "NO ACTION", "onUpdate": "NO ACTION", "columns": ["I_INS_ID"], "referencedColumns": ["ID"]}, {"table": "AIINS_ITEM", "onDelete": "NO ACTION", "onUpdate": "NO ACTION", "columns": ["I_INS_ITEM_ID"], "referencedColumns": ["ID"]}]}, {"tableName": "AIUSER", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_CUSTOMER_ID` INTEGER, `S_NAME` TEXT, `S_EMAIL` TEXT, `B_DELETED` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iCustomerID", "columnName": "I_CUSTOMER_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sName", "columnName": "S_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sEmail", "columnName": "S_EMAIL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AIUSER_I_CUSTOMER_ID", "unique": false, "columnNames": ["I_CUSTOMER_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIUSER_I_CUSTOMER_ID` ON `${TABLE_NAME}` (`I_CUSTOMER_ID`)"}, {"name": "index_AIUSER_S_EMAIL", "unique": false, "columnNames": ["S_EMAIL"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIUSER_S_EMAIL` ON `${TABLE_NAME}` (`S_EMAIL`)"}, {"name": "index_AIUSER_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIUSER_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}], "foreignKeys": []}, {"tableName": "AIVIDEO", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_VIDEO_ID` INTEGER, `I_INS_ID` INTEGER, `I_INS_ITEM_ID` INTEGER, `S_THUMB` TEXT, `S_FILE` TEXT, `S_S_THUMB` TEXT, `S_S_FILE` TEXT, `B_GET_URL` INTEGER, `B_UPLOADED` INTEGER, `B_PROCESSED` INTEGER, `S_FIELD_ONE` TEXT, `S_FIELD_TWO` TEXT, `S_FIELD_THREE` TEXT, `B_DELETED` INTEGER, `DT_DATE_TIME` TEXT, `I_SIZE` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSVideoID", "columnName": "I_S_VIDEO_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInsID", "columnName": "I_INS_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInsItemID", "columnName": "I_INS_ITEM_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sThumb", "columnName": "S_THUMB", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFile", "columnName": "S_FILE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sSThumb", "columnName": "S_S_THUMB", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sSFile", "columnName": "S_S_FILE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bGetURL", "columnName": "B_GET_URL", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bUploaded", "columnName": "B_UPLOADED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bProcessed", "columnName": "B_PROCESSED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sFieldOne", "columnName": "S_FIELD_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldTwo", "columnName": "S_FIELD_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s<PERSON><PERSON><PERSON><PERSON>ee", "columnName": "S_FIELD_THREE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iSize", "columnName": "I_SIZE", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AIVIDEO_I_S_VIDEO_ID", "unique": false, "columnNames": ["I_S_VIDEO_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIVIDEO_I_S_VIDEO_ID` ON `${TABLE_NAME}` (`I_S_VIDEO_ID`)"}, {"name": "index_AIVIDEO_I_INS_ID", "unique": false, "columnNames": ["I_INS_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIVIDEO_I_INS_ID` ON `${TABLE_NAME}` (`I_INS_ID`)"}, {"name": "index_AIVIDEO_I_INS_ITEM_ID", "unique": false, "columnNames": ["I_INS_ITEM_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIVIDEO_I_INS_ITEM_ID` ON `${TABLE_NAME}` (`I_INS_ITEM_ID`)"}, {"name": "index_AIVIDEO_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIVIDEO_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}], "foreignKeys": []}, {"tableName": "AITASK", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_NOTIFICATION_ID` INTEGER, `I_SUBMIT_CUSTOMER_ID` INTEGER, `I_FOLLOW_UP_CUSTOMER_ID` INTEGER, `I_PROPERTY_ID` INTEGER, `I_COMPANY_ID` INTEGER, `I_INS_ITEM_ID` INTEGER, `I_INSPECTION_ID` INTEGER, `S_CATEGORY` TEXT, `I_PRIORITY` INTEGER, `S_CODE` TEXT, `S_TITLE` TEXT, `S_DESCRIPTION` TEXT, `S_PHOTO_URL` TEXT, `S_VIDEO_URL` TEXT, `DT_DATE_DUE` INTEGER, `S_CUSTOM1` TEXT, `S_CUSTOM2` TEXT, `B_CLOSED` INTEGER, `B_DELETED` INTEGER, `DT_DATE_TIME` INTEGER, `DT_COMPLETE` INTEGER, `DT_UPDATE` INTEGER, `I_P_TASK_ID` INTEGER, `ARR_MEMBER` TEXT, `S_STATUS` TEXT, `S_STATUS_CODE` TEXT, `I_CATEGORY_ID` INTEGER, `DT_UPDATEMSG` INTEGER, `D_COST` REAL)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSNotificationID", "columnName": "I_S_NOTIFICATION_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSubmitCustomerID", "columnName": "I_SUBMIT_CUSTOMER_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iFollowUpCustomerID", "columnName": "I_FOLLOW_UP_CUSTOMER_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iPropertyID", "columnName": "I_PROPERTY_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iCompanyID", "columnName": "I_COMPANY_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInsItemID", "columnName": "I_INS_ITEM_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInspectionID", "columnName": "I_INSPECTION_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sCategory", "columnName": "S_CATEGORY", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iPriority", "columnName": "I_PRIORITY", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sCode", "columnName": "S_CODE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sTitle", "columnName": "S_TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDescription", "columnName": "S_DESCRIPTION", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sPhotoURL", "columnName": "S_PHOTO_URL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sVideoURL", "columnName": "S_VIDEO_URL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dtDateDue", "columnName": "DT_DATE_DUE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sCustom1", "columnName": "S_CUSTOM1", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom2", "columnName": "S_CUSTOM2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bClosed", "columnName": "B_CLOSED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtComplete", "columnName": "DT_COMPLETE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtUpdate", "columnName": "DT_UPDATE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iPTaskID", "columnName": "I_P_TASK_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "arrMember", "columnName": "ARR_MEMBER", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sStatus", "columnName": "S_STATUS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sStatusCode", "columnName": "S_STATUS_CODE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iCategoryID", "columnName": "I_CATEGORY_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtUpdateMsg", "columnName": "DT_UPDATEMSG", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dCost", "columnName": "D_COST", "affinity": "REAL", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AITASK_I_S_NOTIFICATION_ID", "unique": false, "columnNames": ["I_S_NOTIFICATION_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AITASK_I_S_NOTIFICATION_ID` ON `${TABLE_NAME}` (`I_S_NOTIFICATION_ID`)"}, {"name": "index_AITASK_I_PROPERTY_ID", "unique": false, "columnNames": ["I_PROPERTY_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AITASK_I_PROPERTY_ID` ON `${TABLE_NAME}` (`I_PROPERTY_ID`)"}, {"name": "index_AITASK_I_INSPECTION_ID", "unique": false, "columnNames": ["I_INSPECTION_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AITASK_I_INSPECTION_ID` ON `${TABLE_NAME}` (`I_INSPECTION_ID`)"}, {"name": "index_AITASK_I_INS_ITEM_ID", "unique": false, "columnNames": ["I_INS_ITEM_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AITASK_I_INS_ITEM_ID` ON `${TABLE_NAME}` (`I_INS_ITEM_ID`)"}, {"name": "index_AITASK_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AITASK_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}, {"name": "index_AITASK_B_CLOSED", "unique": false, "columnNames": ["B_CLOSED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AITASK_B_CLOSED` ON `${TABLE_NAME}` (`B_CLOSED`)"}], "foreignKeys": []}, {"tableName": "AISCHEDULE", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_SCHEDULE_ID` INTEGER, `I_S_ASSET_ID` INTEGER, `I_S_INS_TYPE_ID` INTEGER, `S_TYPE` TEXT, `S_PTC` TEXT, `S_INS_TITLE` TEXT, `S_ADDRESS_ONE` TEXT, `S_ADDRESS_TWO` TEXT, `DT_DATE_TIME` TEXT, `I_UNIX_TIME` INTEGER, `B_COMPLETED` INTEGER, `B_DELETED` INTEGER, `S_CUSTOM_ONE` TEXT, `S_CUSTOM_TWO` TEXT, `S_R_RULE` TEXT, `S_EX_RULE` TEXT)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSScheduleID", "columnName": "I_S_SCHEDULE_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSAssetID", "columnName": "I_S_ASSET_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSInsTypeID", "columnName": "I_S_INS_TYPE_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sType", "columnName": "S_TYPE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sPTC", "columnName": "S_PTC", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sInsTitle", "columnName": "S_INS_TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sAddressOne", "columnName": "S_ADDRESS_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sAddressTwo", "columnName": "S_ADDRESS_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iUnixTime", "columnName": "I_UNIX_TIME", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bCompleted", "columnName": "B_COMPLETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sCustomOne", "columnName": "S_CUSTOM_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomTwo", "columnName": "S_CUSTOM_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sRRule", "columnName": "S_R_RULE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sEXRule", "columnName": "S_EX_RULE", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AISCHEDULE_I_S_SCHEDULE_ID", "unique": false, "columnNames": ["I_S_SCHEDULE_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AISCHEDULE_I_S_SCHEDULE_ID` ON `${TABLE_NAME}` (`I_S_SCHEDULE_ID`)"}, {"name": "index_AISCHEDULE_I_S_ASSET_ID", "unique": false, "columnNames": ["I_S_ASSET_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AISCHEDULE_I_S_ASSET_ID` ON `${TABLE_NAME}` (`I_S_ASSET_ID`)"}, {"name": "index_AISCHEDULE_I_S_INS_TYPE_ID", "unique": false, "columnNames": ["I_S_INS_TYPE_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AISCHEDULE_I_S_INS_TYPE_ID` ON `${TABLE_NAME}` (`I_S_INS_TYPE_ID`)"}, {"name": "index_AISCHEDULE_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AISCHEDULE_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}, {"name": "index_AISCHEDULE_B_COMPLETED", "unique": false, "columnNames": ["B_COMPLETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AISCHEDULE_B_COMPLETED` ON `${TABLE_NAME}` (`B_COMPLETED`)"}], "foreignKeys": []}, {"tableName": "AIPROJECT", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_PROJECT_ID` INTEGER, `I_CREATOR_ID` INTEGER, `I_COMPANY_ID` INTEGER, `S_REFERENCE` TEXT, `S_NAME` TEXT, `S_DESCRIPTION` TEXT, `S_CUSTOM_FIELD` TEXT, `I_GROUP_ID` INTEGER, `I_TOTAL_INS` INTEGER, `I_COMPLETED_INS` INTEGER, `S_STATUS` TEXT, `S_STATUS_CODE` TEXT, `S_CUSTOM_ONE` TEXT, `S_CUSTOM_TWO` TEXT, `B_ACTIVE` INTEGER, `B_DELETED` INTEGER, `DT_UPDATE` INTEGER, `DT_DATE_TIME` INTEGER, `ARR_INSPECTOR` TEXT, `I_MANAGER_ID` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSProjectID", "columnName": "I_S_PROJECT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iCreatorID", "columnName": "I_CREATOR_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iCompanyID", "columnName": "I_COMPANY_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sReference", "columnName": "S_REFERENCE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sName", "columnName": "S_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDescription", "columnName": "S_DESCRIPTION", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomField", "columnName": "S_CUSTOM_FIELD", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iGroupID", "columnName": "I_GROUP_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iTotalIns", "columnName": "I_TOTAL_INS", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iCompletedIns", "columnName": "I_COMPLETED_INS", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sStatus", "columnName": "S_STATUS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sStatusCode", "columnName": "S_STATUS_CODE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom1", "columnName": "S_CUSTOM_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom2", "columnName": "S_CUSTOM_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bActive", "columnName": "B_ACTIVE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtUpdate", "columnName": "DT_UPDATE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "arrInspector", "columnName": "ARR_INSPECTOR", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iManagerID", "columnName": "I_MANAGER_ID", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AIPROJECT_I_S_PROJECT_ID", "unique": false, "columnNames": ["I_S_PROJECT_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPROJECT_I_S_PROJECT_ID` ON `${TABLE_NAME}` (`I_S_PROJECT_ID`)"}, {"name": "index_AIPROJECT_I_CREATOR_ID", "unique": false, "columnNames": ["I_CREATOR_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPROJECT_I_CREATOR_ID` ON `${TABLE_NAME}` (`I_CREATOR_ID`)"}, {"name": "index_AIPROJECT_I_COMPANY_ID", "unique": false, "columnNames": ["I_COMPANY_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPROJECT_I_COMPANY_ID` ON `${TABLE_NAME}` (`I_COMPANY_ID`)"}, {"name": "index_AIPROJECT_I_GROUP_ID", "unique": false, "columnNames": ["I_GROUP_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPROJECT_I_GROUP_ID` ON `${TABLE_NAME}` (`I_GROUP_ID`)"}, {"name": "index_AIPROJECT_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPROJECT_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}, {"name": "index_AIPROJECT_B_ACTIVE", "unique": false, "columnNames": ["B_ACTIVE"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIPROJECT_B_ACTIVE` ON `${TABLE_NAME}` (`B_ACTIVE`)"}], "foreignKeys": []}, {"tableName": "AICONTACT", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_CONTACT_ID` INTEGER, `I_ASSET_ID` INTEGER, `I_S_ASSET_ID` INTEGER, `S_FIRST_NAME` TEXT, `S_LAST_NAME` TEXT, `S_PHONE` TEXT, `S_MOBILE` TEXT, `S_EMAIL` TEXT, `S_TAG` TEXT, `S_FIELD_ONE` TEXT, `S_FIELD_TWO` TEXT, `S_FIELD_THREE` TEXT, `B_DELETED` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSContactID", "columnName": "I_S_CONTACT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iAssetID", "columnName": "I_ASSET_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSAssetID", "columnName": "I_S_ASSET_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sFirstName", "columnName": "S_FIRST_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sLastName", "columnName": "S_LAST_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sPhone", "columnName": "S_PHONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sMobile", "columnName": "S_MOBILE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sEmail", "columnName": "S_EMAIL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sTag", "columnName": "S_TAG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldOne", "columnName": "S_FIELD_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldTwo", "columnName": "S_FIELD_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s<PERSON><PERSON><PERSON><PERSON>ee", "columnName": "S_FIELD_THREE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AICONTACT_I_S_CONTACT_ID", "unique": false, "columnNames": ["I_S_CONTACT_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AICONTACT_I_S_CONTACT_ID` ON `${TABLE_NAME}` (`I_S_CONTACT_ID`)"}, {"name": "index_AICONTACT_I_ASSET_ID", "unique": false, "columnNames": ["I_ASSET_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AICONTACT_I_ASSET_ID` ON `${TABLE_NAME}` (`I_ASSET_ID`)"}, {"name": "index_AICONTACT_I_S_ASSET_ID", "unique": false, "columnNames": ["I_S_ASSET_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AICONTACT_I_S_ASSET_ID` ON `${TABLE_NAME}` (`I_S_ASSET_ID`)"}, {"name": "index_AICONTACT_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AICONTACT_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}], "foreignKeys": []}, {"tableName": "AILAYOUT", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_LAYOUT_ID` INTEGER, `I_SP_LAYOUT_ID` INTEGER, `S_PTC` TEXT, `S_Q_TYPE` TEXT, `S_NAME` TEXT, `S_FV_ONE_CONFIG` TEXT, `S_FV_TWO_CONFIG` TEXT, `S_FV_THREE_CONFIG` TEXT, `S_FV_FOUR_CONFIG` TEXT, `S_FV_FIVE_CONFIG` TEXT, `S_FV_SIX_CONFIG` TEXT, `S_SV_ONE_CONFIG` TEXT, `S_SV_TWO_CONFIG` TEXT, `S_SV_THREE_CONFIG` TEXT, `S_SV_FOUR_CONFIG` TEXT, `S_SV_FIVE_CONFIG` TEXT, `S_SV_SIX_CONFIG` TEXT, `S_F_CONFIG` TEXT, `S_S_CONFIG` TEXT, `S_FIELD_ONE` TEXT, `S_FIELD_TWO` TEXT, `S_FIELD_THREE` TEXT, `B_DELETED` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSLayoutID", "columnName": "I_S_LAYOUT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSPLayoutID", "columnName": "I_SP_LAYOUT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sPTC", "columnName": "S_PTC", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sQType", "columnName": "S_Q_TYPE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sName", "columnName": "S_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFVOneConfig", "columnName": "S_FV_ONE_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFVTwoConfig", "columnName": "S_FV_TWO_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFVThreeConfig", "columnName": "S_FV_THREE_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFVFourConfig", "columnName": "S_FV_FOUR_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFVFiveConfig", "columnName": "S_FV_FIVE_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFVSixConfig", "columnName": "S_FV_SIX_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sSVOneConfig", "columnName": "S_SV_ONE_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sSVTwoConfig", "columnName": "S_SV_TWO_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sSVThreeConfig", "columnName": "S_SV_THREE_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sSVFourConfig", "columnName": "S_SV_FOUR_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sSVFiveConfig", "columnName": "S_SV_FIVE_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sSVSixConfig", "columnName": "S_SV_SIX_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFConfig", "columnName": "S_F_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sSConfig", "columnName": "S_S_CONFIG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldOne", "columnName": "S_FIELD_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldTwo", "columnName": "S_FIELD_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s<PERSON><PERSON><PERSON><PERSON>ee", "columnName": "S_FIELD_THREE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AILAYOUT_I_S_LAYOUT_ID", "unique": false, "columnNames": ["I_S_LAYOUT_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AILAYOUT_I_S_LAYOUT_ID` ON `${TABLE_NAME}` (`I_S_LAYOUT_ID`)"}, {"name": "index_AILAYOUT_I_SP_LAYOUT_ID", "unique": false, "columnNames": ["I_SP_LAYOUT_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AILAYOUT_I_SP_LAYOUT_ID` ON `${TABLE_NAME}` (`I_SP_LAYOUT_ID`)"}, {"name": "index_AILAYOUT_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AILAYOUT_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}], "foreignKeys": []}, {"tableName": "AIFILE", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_FILE_ID` INTEGER, `I_S_OBJECT_ID` INTEGER, `S_FILE` TEXT, `S_COMMENTS` TEXT, `S_LAT` TEXT, `S_LONG` TEXT, `B_UPLOADED` INTEGER, `B_DELETED` INTEGER, `DT_DATE_TIME` TEXT, `I_SIZE` INTEGER, `S_CUSTOM_ONE` TEXT, `S_CUSTOM_TWO` TEXT)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iFileID", "columnName": "I_FILE_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSObjectID", "columnName": "I_S_OBJECT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sFile", "columnName": "S_FILE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sComments", "columnName": "S_COMMENTS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sLat", "columnName": "S_LAT", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sLong", "columnName": "S_LONG", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bUploaded", "columnName": "B_UPLOADED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iSize", "columnName": "I_SIZE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sCustomOne", "columnName": "S_CUSTOM_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomTwo", "columnName": "S_CUSTOM_TWO", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AIFILE_I_FILE_ID", "unique": false, "columnNames": ["I_FILE_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIFILE_I_FILE_ID` ON `${TABLE_NAME}` (`I_FILE_ID`)"}, {"name": "index_AIFILE_I_S_OBJECT_ID", "unique": false, "columnNames": ["I_S_OBJECT_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIFILE_I_S_OBJECT_ID` ON `${TABLE_NAME}` (`I_S_OBJECT_ID`)"}, {"name": "index_AIFILE_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIFILE_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}, {"name": "index_AIFILE_B_UPLOADED", "unique": false, "columnNames": ["B_UPLOADED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIFILE_B_UPLOADED` ON `${TABLE_NAME}` (`B_UPLOADED`)"}], "foreignKeys": []}, {"tableName": "AINOTIFICATION", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_NOTIFICATION_ID` INTEGER, `I_INS_ITEM_ID` INTEGER, `I_INS_ID` INTEGER, `S_TITLE` TEXT, `S_DESCRIPTION` TEXT, `S_PHOTO_URL` TEXT, `S_VIDEO_ID` TEXT, `S_DUE_DATE` TEXT, `I_PRIORITY` INTEGER, `S_CATEGORY` TEXT, `S_CUSTOM_ONE` TEXT, `S_CUSTOM_TWO` TEXT, `B_DELETED` INTEGER, `DT_DATE_TIME` TEXT)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSNotificationID", "columnName": "I_S_NOTIFICATION_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInsItemID", "columnName": "I_INS_ITEM_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInsID", "columnName": "I_INS_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sTitle", "columnName": "S_TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDescription", "columnName": "S_DESCRIPTION", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sPhotoURL", "columnName": "S_PHOTO_URL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sVideoID", "columnName": "S_VIDEO_ID", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDueDate", "columnName": "S_DUE_DATE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iPriority", "columnName": "I_PRIORITY", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sCategory", "columnName": "S_CATEGORY", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomOne", "columnName": "S_CUSTOM_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomTwo", "columnName": "S_CUSTOM_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AINOTIFICATION_I_S_NOTIFICATION_ID", "unique": false, "columnNames": ["I_S_NOTIFICATION_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AINOTIFICATION_I_S_NOTIFICATION_ID` ON `${TABLE_NAME}` (`I_S_NOTIFICATION_ID`)"}, {"name": "index_AINOTIFICATION_I_INS_ITEM_ID", "unique": false, "columnNames": ["I_INS_ITEM_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AINOTIFICATION_I_INS_ITEM_ID` ON `${TABLE_NAME}` (`I_INS_ITEM_ID`)"}, {"name": "index_AINOTIFICATION_I_INS_ID", "unique": false, "columnNames": ["I_INS_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AINOTIFICATION_I_INS_ID` ON `${TABLE_NAME}` (`I_INS_ID`)"}, {"name": "index_AINOTIFICATION_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AINOTIFICATION_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}], "foreignKeys": []}, {"tableName": "AICOMMENT", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_COMMENT_ID` INTEGER, `I_COMPANY_ID` INTEGER, `I_CUSTOMER_ID` INTEGER, `I_INSPECTION_ID` INTEGER, `S_DATE` TEXT, `S_TIME` TEXT, `DATE` INTEGER, `S_NAME` TEXT, `S_DESCRIPTION` TEXT, `S_TYPE` TEXT, `S_CUSTOM1` TEXT, `I_TASK_ID` TEXT, `S_PHOTO_URL` TEXT, `COLOR_RES_ID` INTEGER, `B_DELETED` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iCommentID", "columnName": "I_COMMENT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iCompanyID", "columnName": "I_COMPANY_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iCustomerID", "columnName": "I_CUSTOMER_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInspectionID", "columnName": "I_INSPECTION_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sDate", "columnName": "S_DATE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sTime", "columnName": "S_TIME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "date", "columnName": "DATE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sName", "columnName": "S_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDescription", "columnName": "S_DESCRIPTION", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sType", "columnName": "S_TYPE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom1", "columnName": "S_CUSTOM1", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iTaskID", "columnName": "I_TASK_ID", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sPhotoUrl", "columnName": "S_PHOTO_URL", "affinity": "TEXT", "notNull": false}, {"fieldPath": "colorResId", "columnName": "COLOR_RES_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AICOMMENT_I_COMMENT_ID", "unique": false, "columnNames": ["I_COMMENT_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AICOMMENT_I_COMMENT_ID` ON `${TABLE_NAME}` (`I_COMMENT_ID`)"}, {"name": "index_AICOMMENT_I_COMPANY_ID", "unique": false, "columnNames": ["I_COMPANY_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AICOMMENT_I_COMPANY_ID` ON `${TABLE_NAME}` (`I_COMPANY_ID`)"}, {"name": "index_AICOMMENT_I_CUSTOMER_ID", "unique": false, "columnNames": ["I_CUSTOMER_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AICOMMENT_I_CUSTOMER_ID` ON `${TABLE_NAME}` (`I_CUSTOMER_ID`)"}, {"name": "index_AICOMMENT_I_INSPECTION_ID", "unique": false, "columnNames": ["I_INSPECTION_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AICOMMENT_I_INSPECTION_ID` ON `${TABLE_NAME}` (`I_INSPECTION_ID`)"}, {"name": "index_AICOMMENT_B_DELETED", "unique": false, "columnNames": ["B_DELETED"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AICOMMENT_B_DELETED` ON `${TABLE_NAME}` (`B_DELETED`)"}], "foreignKeys": []}, {"tableName": "AIASSET_LAYOUT", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`ID` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_LAYOUT_ID` INTEGER, `I_S_ASSET_ID` INTEGER, `S_NAME` TEXT, `S_CHILD_ID` TEXT, `S_MORE_ITEMS` TEXT, `S_FIELD_ONE` TEXT, `S_FIELD_TWO` TEXT, `S_FIELD_THREE` TEXT, `I_SORT` INTEGER, `I_S_ASSET_LAYOUT_ID` INTEGER, `I_ASSET_ID` INTEGER, `I_LAYOUT_ID` INTEGER, `B_DELETED` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSLayoutID", "columnName": "I_S_LAYOUT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSAssetID", "columnName": "I_S_ASSET_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sName", "columnName": "S_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sChildID", "columnName": "S_CHILD_ID", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sMoreItems", "columnName": "S_MORE_ITEMS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldOne", "columnName": "S_FIELD_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldTwo", "columnName": "S_FIELD_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s<PERSON><PERSON><PERSON><PERSON>ee", "columnName": "S_FIELD_THREE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iSort", "columnName": "I_SORT", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSAssetLayoutID", "columnName": "I_S_ASSET_LAYOUT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iAssetID", "columnName": "I_ASSET_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iLayoutID", "columnName": "I_LAYOUT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["ID"]}, "indices": [{"name": "index_AIASSET_LAYOUT_I_S_LAYOUT_ID", "unique": false, "columnNames": ["I_S_LAYOUT_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIASSET_LAYOUT_I_S_LAYOUT_ID` ON `${TABLE_NAME}` (`I_S_LAYOUT_ID`)"}, {"name": "index_AIASSET_LAYOUT_I_S_ASSET_ID", "unique": false, "columnNames": ["I_S_ASSET_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIASSET_LAYOUT_I_S_ASSET_ID` ON `${TABLE_NAME}` (`I_S_ASSET_ID`)"}, {"name": "index_AIASSET_LAYOUT_I_S_ASSET_LAYOUT_ID", "unique": false, "columnNames": ["I_S_ASSET_LAYOUT_ID"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIASSET_LAYOUT_I_S_ASSET_LAYOUT_ID` ON `${TABLE_NAME}` (`I_S_ASSET_LAYOUT_ID`)"}, {"name": "index_AIASSET_LAYOUT_I_SORT", "unique": false, "columnNames": ["I_SORT"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_AIASSET_LAYOUT_I_SORT` ON `${TABLE_NAME}` (`I_SORT`)"}], "foreignKeys": []}, {"tableName": "AICHECK_LIST", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_CHECK_LIST_ID` INTEGER, `S_TITLE` TEXT, `S_PTC` TEXT, `I_LAYOUT_VER_ID` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSCheckListID", "columnName": "I_S_CHECK_LIST_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sTitle", "columnName": "S_TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sPTC", "columnName": "S_PTC", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iLayoutVerID", "columnName": "I_LAYOUT_VER_ID", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIINS_ALERT", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_ASSET_ID` INTEGER, `I_S_LAYOUT_ID` INTEGER, `S_PTC` TEXT, `S_VALUE_ONE` TEXT, `S_VALUE_TWO` TEXT, `S_VALUE_THREE` TEXT, `S_VALUE_FOUR` TEXT, `S_VALUE_FIVE` TEXT, `S_VALUE_SIX` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSAssetID", "columnName": "I_S_ASSET_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSLayoutID", "columnName": "I_S_LAYOUT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sPTC", "columnName": "S_PTC", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sValueOne", "columnName": "S_VALUE_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sValueTwo", "columnName": "S_VALUE_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s<PERSON><PERSON>ueThr<PERSON>", "columnName": "S_VALUE_THREE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sValueFour", "columnName": "S_VALUE_FOUR", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sValueFive", "columnName": "S_VALUE_FIVE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sValueSix", "columnName": "S_VALUE_SIX", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIINS_TYPE", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_INS_TYPE_ID` INTEGER, `S_INS_TITLE` TEXT, `S_TYPE` TEXT, `B_DELETED` INTEGER, `B_REM_LAYOUT` INTEGER, `S_PTC` TEXT, `S_FIELD_ONE` TEXT, `S_FIELD_TWO` TEXT, `S_FIELD_THREE` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSInsTypeID", "columnName": "I_S_INS_TYPE_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sInsTitle", "columnName": "S_INS_TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sType", "columnName": "S_TYPE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bRemLayout", "columnName": "B_REM_LAYOUT", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sPTC", "columnName": "S_PTC", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldOne", "columnName": "S_FIELD_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldTwo", "columnName": "S_FIELD_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s<PERSON><PERSON><PERSON><PERSON>ee", "columnName": "S_FIELD_THREE", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIQUICK_PHRASE", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_QUICK_PHRASE_ID` INTEGER, `I_S_LAYOUT_ID` INTEGER, `S_COMMENTS` TEXT, `S_FIELD_ONE` TEXT, `S_FIELD_TWO` TEXT, `S_FIELD_THREE` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iQuickPhraseID", "columnName": "I_QUICK_PHRASE_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSLayoutID", "columnName": "I_S_LAYOUT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sComments", "columnName": "S_COMMENTS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldOne", "columnName": "S_FIELD_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sFieldTwo", "columnName": "S_FIELD_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "s<PERSON><PERSON><PERSON><PERSON>ee", "columnName": "S_FIELD_THREE", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIASSET_VIEW", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_ASSET_VIEW_ID` INTEGER NOT NULL, `I_CUSTOMER_ID` INTEGER NOT NULL, `I_COMPANY_ID` INTEGER NOT NULL, `I_G<PERSON>UP_ID` INTEGER NOT NULL, `S_NAME` TEXT, `S_DESCRIPTION` TEXT, `S_CUSTOM1` TEXT, `S_CUSTOM2` TEXT, `B_ARCHIVED` INTEGER NOT NULL, `B_DELETED` INTEGER NOT NULL, `DT_UPDATE` INTEGER, `ARR_MEM` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSAssetViewID", "columnName": "I_S_ASSET_VIEW_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iCustomerID", "columnName": "I_CUSTOMER_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iCompanyID", "columnName": "I_COMPANY_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iGroupID", "columnName": "I_GROUP_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sName", "columnName": "S_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDescription", "columnName": "S_DESCRIPTION", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom1", "columnName": "S_CUSTOM1", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom2", "columnName": "S_CUSTOM2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bArchived", "columnName": "B_ARCHIVED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dtUpdate", "columnName": "DT_UPDATE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "arrMem", "columnName": "ARR_MEM", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIPROJECT_INSPECTION", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_PROJECT_ASSET_INS_TYPE_ID` INTEGER NOT NULL, `I_PROJECT_ID` INTEGER NOT NULL, `I_COMPANY_ID` INTEGER NOT NULL, `I_ASSET_ID` INTEGER NOT NULL, `I_INS_TYPE_ID` INTEGER NOT NULL, `I_INSPECTION_ID` INTEGER NOT NULL, `I_S_INSPECTION_ID` INTEGER NOT NULL, `I_INSPECTOR_ID` INTEGER NOT NULL, `DT_START` INTEGER, `DT_END` INTEGER, `S_CUSTOM` TEXT, `B_DELETED` INTEGER NOT NULL, `DT_UPDATE` INTEGER, `DT_DATE_TIME` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSProjectAssetInsTypeID", "columnName": "I_S_PROJECT_ASSET_INS_TYPE_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iProjectID", "columnName": "I_PROJECT_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iCompanyID", "columnName": "I_COMPANY_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iAssetID", "columnName": "I_ASSET_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iInsTypeID", "columnName": "I_INS_TYPE_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iInspectionID", "columnName": "I_INSPECTION_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iSInspectionID", "columnName": "I_S_INSPECTION_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iInspectorID", "columnName": "I_INSPECTOR_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dtStart", "columnName": "DT_START", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtEnd", "columnName": "DT_END", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sCustom", "columnName": "S_CUSTOM", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dtUpdate", "columnName": "DT_UPDATE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIFLOORPLAN", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_FLOOR_PLAN_ID` INTEGER NOT NULL, `I_S_FLOOR_PLAN_ID` INTEGER NOT NULL, `I_S_ASSET_ID` INTEGER NOT NULL, `I_CREATE_CUSTOMER_ID` INTEGER NOT NULL, `S_MEMBER` TEXT, `S_TITLE` TEXT, `S_DESP` TEXT, `S_PLAN_PATH` TEXT, `S_IMAGE_PATH` TEXT, `S_MARKS` TEXT, `S_CUSTOM1` TEXT, `S_CUSTOM2` TEXT, `S_CUSTOM3` TEXT, `B_ARCHIVE` INTEGER NOT NULL, `B_DELETED` INTEGER NOT NULL, `DT_UPDATE` INTEGER, `DT_DATE_TIME` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iFloorPlanID", "columnName": "I_FLOOR_PLAN_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iSFloorPlanID", "columnName": "I_S_FLOOR_PLAN_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iSAssetID", "columnName": "I_S_ASSET_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iCreateCustomerID", "columnName": "I_CREATE_CUSTOMER_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sMember", "columnName": "S_MEMBER", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sTitle", "columnName": "S_TITLE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDesp", "columnName": "S_DESP", "affinity": "TEXT", "notNull": false}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "columnName": "S_PLAN_PATH", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sImagePath", "columnName": "S_IMAGE_PATH", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sMarks", "columnName": "S_MARKS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom1", "columnName": "S_CUSTOM1", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom2", "columnName": "S_CUSTOM2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom3", "columnName": "S_CUSTOM3", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bArchive", "columnName": "B_ARCHIVE", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dtUpdate", "columnName": "DT_UPDATE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIPRODUCT", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `i_s_product_id` INTEGER NOT NULL, `s_check_list_id` TEXT, `i_company_id` INTEGER NOT NULL, `s_sku` TEXT, `s_name` TEXT, `s_model` TEXT, `s_desp` TEXT, `s_associate_area` TEXT, `s_associate_item` TEXT, `d_unit_cost` REAL NOT NULL, `b_allow_edit` INTEGER NOT NULL, `b_one_off_cost` INTEGER NOT NULL, `s_product_category` TEXT, `s_unit_name` TEXT, `s_url` TEXT, `s_image` TEXT, `s_custom1` TEXT, `s_custom2` TEXT, `b_archive` INTEGER NOT NULL, `b_deleted` INTEGER NOT NULL, `i_created_by` INTEGER NOT NULL, `i_updated_by` INTEGER NOT NULL, `dt_update` TEXT, `dt_date_time` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSProductID", "columnName": "i_s_product_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sCheckListID", "columnName": "s_check_list_id", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iCompanyID", "columnName": "i_company_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sSKU", "columnName": "s_sku", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sName", "columnName": "s_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sModel", "columnName": "s_model", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDesp", "columnName": "s_desp", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sAssociateArea", "columnName": "s_associate_area", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sAssociateItem", "columnName": "s_associate_item", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dUnitCost", "columnName": "d_unit_cost", "affinity": "REAL", "notNull": true}, {"fieldPath": "bAllowEdit", "columnName": "b_allow_edit", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "bOneOffCost", "columnName": "b_one_off_cost", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sProductCategory", "columnName": "s_product_category", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sUnitName", "columnName": "s_unit_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sURL", "columnName": "s_url", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sImage", "columnName": "s_image", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom1", "columnName": "s_custom1", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom2", "columnName": "s_custom2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bArchive", "columnName": "b_archive", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "b_deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iCreatedBy", "columnName": "i_created_by", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iUpdatedBy", "columnName": "i_updated_by", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dtUpdate", "columnName": "dt_update", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "dt_date_time", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIPRODUCT_COST", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_COSTING_ID` INTEGER NOT NULL, `I_ASSET_ID` INTEGER NOT NULL, `I_INSPECTION_ID` INTEGER NOT NULL, `I_INS_ITEM_ID` INTEGER NOT NULL, `I_P_INS_ITEM_ID` INTEGER NOT NULL, `I_TASK_ID` INTEGER NOT NULL, `I_PRODUCT_ID` INTEGER NOT NULL, `D_UNIT` REAL NOT NULL, `D_UNIT_COST` REAL NOT NULL, `D_TOTAL_COST` REAL NOT NULL, `S_NOTES` TEXT, `S_CUSTOM1` TEXT, `S_CUSTOM2` TEXT, `S_CUSTOM3` TEXT, `B_DELETED` INTEGER NOT NULL, `I_CREATED_BY` INTEGER NOT NULL, `I_UPDATED_BY` INTEGER NOT NULL, `DT_UPDATE` TEXT, `DT_DATE_TIME` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSCostingID", "columnName": "I_S_COSTING_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iAssetID", "columnName": "I_ASSET_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iInspectionID", "columnName": "I_INSPECTION_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iInsItemID", "columnName": "I_INS_ITEM_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iPInsItemID", "columnName": "I_P_INS_ITEM_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iTaskID", "columnName": "I_TASK_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iProductID", "columnName": "I_PRODUCT_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dUnit", "columnName": "D_UNIT", "affinity": "REAL", "notNull": true}, {"fieldPath": "dUnitCost", "columnName": "D_UNIT_COST", "affinity": "REAL", "notNull": true}, {"fieldPath": "dTotalCost", "columnName": "D_TOTAL_COST", "affinity": "REAL", "notNull": true}, {"fieldPath": "sNotes", "columnName": "S_NOTES", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom1", "columnName": "S_CUSTOM1", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom2", "columnName": "S_CUSTOM2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom3", "columnName": "S_CUSTOM3", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iCreatedBy", "columnName": "I_CREATED_BY", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iUpdatedBy", "columnName": "I_UPDATED_BY", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dtUpdate", "columnName": "DT_UPDATE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AINOTICE_CATEGORY", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `i_s_notice_category_id` INTEGER NOT NULL, `s_name` TEXT, `s_description` TEXT, `b_deleted` INTEGER NOT NULL, `dt_update` TEXT, `dt_date_time` TEXT, `i_p_note_category_id` INTEGER NOT NULL, `s_custom1` TEXT, `s_custom2` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSNoticeCategoryID", "columnName": "i_s_notice_category_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sName", "columnName": "s_name", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDescription", "columnName": "s_description", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "b_deleted", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dtUpdate", "columnName": "dt_update", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "dt_date_time", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iPNoteCategoryID", "columnName": "i_p_note_category_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sCustom1", "columnName": "s_custom1", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom2", "columnName": "s_custom2", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIPROPERTY_LAYOUT", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_PROPERTY_LAYOUT_ID` INTEGER NOT NULL, `I_COMPANY_ID` INTEGER NOT NULL, `I_PROPERTY_ID` INTEGER NOT NULL, `S_PTC` TEXT, `ARR_LAYOUT` TEXT, `S_CUSTOM1` TEXT, `S_CUSTOM2` TEXT, `DT_UPDATE` INTEGER, `B_DELETED` INTEGER NOT NULL, `DT_DATE_TIME` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSPropertyLayoutID", "columnName": "I_S_PROPERTY_LAYOUT_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iCompanyID", "columnName": "I_COMPANY_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iPropertyID", "columnName": "I_PROPERTY_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sPTC", "columnName": "S_PTC", "affinity": "TEXT", "notNull": false}, {"fieldPath": "arrLayout", "columnName": "ARR_LAYOUT", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom1", "columnName": "S_CUSTOM1", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom2", "columnName": "S_CUSTOM2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dtUpdate", "columnName": "DT_UPDATE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIUPDATE_ASSET_TASK", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_UPDATE_ASSET_TASK_ID` INTEGER, `I_ASSET_ID` INTEGER NOT NULL, `I_USER_ID` INTEGER, `I_STATUS` INTEGER, `B_PROCESSED` INTEGER, `DT_LAST_SYNC` TEXT, `S_CUSTOM1` TEXT, `S_CUSTOM2` TEXT, `B_DELETED` INTEGER NOT NULL, `DT_UPDATE` INTEGER, `DT_DATE_TIME` INTEGER)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSUpdateAssetTaskID", "columnName": "I_S_UPDATE_ASSET_TASK_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iAssetID", "columnName": "I_ASSET_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iUserID", "columnName": "I_USER_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iStatus", "columnName": "I_STATUS", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bProcessed", "columnName": "B_PROCESSED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtLastSync", "columnName": "DT_LAST_SYNC", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom1", "columnName": "S_CUSTOM1", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustom2", "columnName": "S_CUSTOM2", "affinity": "TEXT", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dtUpdate", "columnName": "DT_UPDATE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "INTEGER", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AIINBOX", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_INBOX_ID` INTEGER NOT NULL, `I_S_INBOX_ID` INTEGER NOT NULL, `I_USER_ID` INTEGER, `I_TYPE` INTEGER, `B_READ` INTEGER, `B_DELETED` INTEGER, `I_FROM_CUSTOMER_ID` INTEGER NOT NULL, `I_TO_CUSTOMER_ID` INTEGER NOT NULL, `I_COMPANY_ID` INTEGER NOT NULL, `I_ACTION_ID` INTEGER NOT NULL, `DATE_TIME` INTEGER, `COLOR_STRING` TEXT, `ACTION_TYPE` TEXT, `S_COMMENTS` TEXT, `S_NAME` TEXT, `S_DATE_TIME` TEXT, `S_DATE` TEXT, `S_TIME` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iInboxID", "columnName": "I_INBOX_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iSInboxID", "columnName": "I_S_INBOX_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iUserID", "columnName": "I_USER_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iType", "columnName": "I_TYPE", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "bRead", "columnName": "B_READ", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iFromCustomerID", "columnName": "I_FROM_CUSTOMER_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iToCustomerID", "columnName": "I_TO_CUSTOMER_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iCompanyID", "columnName": "I_COMPANY_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "iActionID", "columnName": "I_ACTION_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dateTime", "columnName": "DATE_TIME", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "colorString", "columnName": "COLOR_STRING", "affinity": "TEXT", "notNull": false}, {"fieldPath": "actionType", "columnName": "ACTION_TYPE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sComments", "columnName": "S_COMMENTS", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sName", "columnName": "S_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDateTime", "columnName": "S_DATE_TIME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sDate", "columnName": "S_DATE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sTime", "columnName": "S_TIME", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AITASK_LIST", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `I_S_TASK_LIST_ID` INTEGER, `I_PROJECT_ID` INTEGER, `I_CREATED_USER_ID` INTEGER, `S_NAME` TEXT, `I_TASK_ID` INTEGER NOT NULL, `S_TYPE` TEXT, `S_MESSAGE` TEXT, `S_CUSTOM_ONE` TEXT, `S_CUSTOM_TWO` TEXT, `B_UPLOADED` INTEGER NOT NULL, `B_DELETED` INTEGER NOT NULL, `DT_DATE_TIME` TEXT)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iSTaskListID", "columnName": "I_S_TASK_LIST_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iProjectID", "columnName": "I_PROJECT_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "iCreatedUserID", "columnName": "I_CREATED_USER_ID", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sName", "columnName": "S_NAME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "iTaskID", "columnName": "I_TASK_ID", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "sType", "columnName": "S_TYPE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sMessage", "columnName": "S_MESSAGE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomOne", "columnName": "S_CUSTOM_ONE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sCustomTwo", "columnName": "S_CUSTOM_TWO", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bUploaded", "columnName": "B_UPLOADED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "TEXT", "notNull": false}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}, {"tableName": "AILOG", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `S_TYPE` TEXT, `S_MESSAGE` TEXT, `DT_DATE_TIME` TEXT, `B_UPLOADED` INTEGER NOT NULL, `B_DELETED` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "sType", "columnName": "S_TYPE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "sMessage", "columnName": "S_MESSAGE", "affinity": "TEXT", "notNull": false}, {"fieldPath": "dtDateTime", "columnName": "DT_DATE_TIME", "affinity": "TEXT", "notNull": false}, {"fieldPath": "bUploaded", "columnName": "B_UPLOADED", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "b<PERSON><PERSON><PERSON>", "columnName": "B_DELETED", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [{"viewName": "VASSET", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT c.ID as iID, c.I_S_ASSET_ID as iAssetID, d.I_S_ASSET_ID as iPAssetID, e.I_S_ASSET_ID as iPPAssetID, c.S_FIELD_THREE as sRef, ((CASE WHEN d.I_SP_ASSET_ID > 0 THEN ' ' || e.S_ADDRESS_ONE || ' ' || e.S_ADDRESS_TWO || ' | ' ELSE '' END) || (CASE WHEN c.I_SP_ASSET_ID > 0 THEN ' ' || d.S_ADDRESS_ONE || ' ' || d.S_ADDRESS_TWO || ' | ' ELSE '' END ) || ' ' || c.S_ADDRESS_ONE || ' ' || c.S_ADDRESS_TWO) as sSearchTerm, (CASE WHEN d.I_SP_Asset_ID > 0 THEN e.S_ADDRESS_ONE || ', ' || e.S_ADDRESS_TWO WHEN C.I_SP_ASSET_ID > 0 THEN d.S_ADDRESS_ONE || ', ' || d.S_ADDRESS_TWO ELSE c.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO END) as sBuildingAddress, (CASE WHEN d.I_SP_ASSET_ID > 0 then d.S_ADDRESS_ONE || ', ' || d.S_ADDRESS_TWO WHEN C.I_SP_ASSET_ID > 0 THEN c.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO ELSE '' END) as sUnitAddress, (CASE WHEN C.I_SP_ASSET_ID > 0 and d.I_SP_ASSET_ID > 0 THEN C.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO ELSE '' END) as sRoomAddress, c.I_CUSTOMER_ID as iCustomerID, c.I_GROUP_ID as iGroupID, c.B_PUSH as bApartment, c.S_FIELD_ONE as sCustom1, c.S_FIELD_TWO as sCustom2 FROM AIASSETS c LEFT JOIN AIASSETS d on c.I_SP_ASSET_ID = d.I_S_ASSET_ID LEFT JOIN AIASSETS e on d.I_SP_ASSET_ID = e.I_S_ASSET_ID WHERE c.B_DELETED = 0"}, {"viewName": "VSCHEDULE", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT c.ID as iScheduleID, c.I_S_SCHEDULE_ID as iSScheduleID, c.I_S_INS_TYPE_ID as iSInsTypeID, c.S_PTC as sPTC, c.S_INS_TITLE as sInsTitle, c.DT_Date_Time as sDateTime, c.I_UNIX_TIME as iUnixTime, c.B_COMPLETED as bCompleted, c.S_CUSTOM_ONE as sCustom1, c.S_CUSTOM_TWO as sCustom2, c.S_TYPE as sType, c.S_R_RULE as sRRule, c.S_EX_RULE as sEXRule, d.iPPAssetID, d.iPAssetID, d.iCustomerID, d.sRef, d.s<PERSON><PERSON><PERSON>dd<PERSON>, d.sUnitAddress, d.sRoomAddress, d.iGroupID, d.bApartment, d.iAssetID, d.iID as iID, (IFNULL(c.DT_DATE_TIME, '') || ' ' || IFNULL(d.sSearchTerm, (c.S_ADDRESS_ONE || ' ' || c.S_ADDRESS_TWO))) as sSearchTerm, c.S_ADDRESS_ONE as sAddress1, c.S_ADDRESS_TWO as sAddress2 FROM AISCHEDULE c LEFT JOIN VASSET d on c.I_S_ASSET_ID = d.iAssetID WHERE c.B_DELETED = 0"}, {"viewName": "VINSPECTION", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT c.ID as iInspectionID, c.I_S_INS_ID as iSInsID, c.I_S_ASSET_ID as iSAssetID, c.S_TITLE as sTitle, c.S_INS_TITLE as sInsTitle, c.DT_START_DATE as dtStartDate, c.DT_END_DATE as dtEndDate, c.B_COMPLETE as bComplete, c.B_SYNCED as bSynced, c.S_CUSTOM_ONE as sCustom1, c.S_CUSTOM_TWO as sCustom2, c.I_S_SCHEDULE_ID as iSScheduleID, d.sRef, d.s<PERSON><PERSON><PERSON><PERSON><PERSON>, d.sU<PERSON>t<PERSON><PERSON>, d.sRoom<PERSON>ddress, d.bApartment, (IFNULL(c.S_TITLE, '') || ' ' || IFNULL(c.S_INS_TITLE, '') || ' ' || IFNULL(d.sSearchTerm, '')) as sSearchTerm FROM AIINSPECTION c LEFT JOIN VASSET d on c.I_S_ASSET_ID = d.iAssetID WHERE c.B_DELETED = 0"}, {"viewName": "VREQUEST_INSPECTION", "createSql": "CREATE VIEW `${VIEW_NAME}` AS SELECT c.iInspectionID, c.sInsTitle as sInspectionTitle, c.sTitle, c.bComplete, c.bSynced, c.iSInsID, (c.sInsTitle || ' ' || c.sTitle) as sInsSearchTerm, d.* FROM VINSPECTION c LEFT JOIN VSCHEDULE d ON c.iSScheduleID = d.iSScheduleID WHERE c.iSScheduleID > 0 UNION ALL SELECT c.iInspectionID, c.sInsTitle as sInspectionTitle, c.sTitle, c.bComplete, c.bSynced, c.iSInsID, (d.sAddress1 || ' ' || d.sAddress2) as sInsSearchTerm, d.* FROM VSCHEDULE d LEFT JOIN VINSPECTION c ON c.iSScheduleID = d.iSScheduleID WHERE c.iSScheduleID IS NULL ORDER BY iUnixTime"}], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'e13ab365fabac4c943d523ab465a848a')"]}}