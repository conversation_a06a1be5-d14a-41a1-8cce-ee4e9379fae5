//package com.snapinspect.snapinspect3.db
//
//import com.snapinspect.snapinspect3.SI_DB.db_Product
//import junit.framework.TestCase.*
//import org.junit.jupiter.api.Test
//import org.junit.runner.RunWith
//import org.robolectric.RobolectricTestRunner
//
//
//@RunWith(RobolectricTestRunner::class)
//class db_ProductTest {
//
//    @Test
//    fun testEmptyTitle() {
//        val result = db_Product.getProductsByAssociateItem("")
//        assertTrue(result.isEmpty())
//    }
//
//    @Test
//    fun testSingleKeyword() {
//        // Assuming there's a product with "chair" in its S_ASSOCIATE_ITEM
//        val result = db_Product.getProductsByAssociateItem("chair")
//        assertFalse(result.isEmpty())
//        assertTrue(result.all { it.sAssociateItem.toLowerCase().contains("chair") })
//    }
//
//    @Test
//    fun testMultipleKeywords() {
//        // Assuming there's a product with both "office" and "chair" in its S_ASSOCIATE_ITEM
//        val result = db_Product.getProductsByAssociateItem("office chair")
//        assertFalse(result.isEmpty())
//        assertTrue(result.all {
//            it.sAssociateItem.toLowerCase().contains("office") &&
//            it.sAssociateItem.toLowerCase().contains("chair")
//        })
//    }
//
//    @Test
//    fun testCaseInsensitivity() {
//        val result1 = db_Product.getProductsByAssociateItem("CHAIR")
//        val result2 = db_Product.getProductsByAssociateItem("chair")
//        assertEquals(result1.size, result2.size)
//    }
//
//    @Test
//    fun testNoMatchingProducts() {
//        val result = db_Product.getProductsByAssociateItem("nonexistentproduct")
//        assertTrue(result.isEmpty())
//    }
//
//    @Test
//    fun testSpecialCharacters() {
//        // Assuming there's a product with "desk&chair" in its S_ASSOCIATE_ITEM
//        val result = db_Product.getProductsByAssociateItem("desk&chair")
//        assertFalse(result.isEmpty())
//        assertTrue(result.all { it.sAssociateItem.toLowerCase().contains("desk&chair") })
//    }
//}