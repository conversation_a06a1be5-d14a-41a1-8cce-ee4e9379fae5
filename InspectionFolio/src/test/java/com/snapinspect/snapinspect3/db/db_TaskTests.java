package com.snapinspect.snapinspect3.db;

import com.snapinspect.snapinspect3.IF_Object.ai_Task;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.assertFalse;

/**
 * Test class db_TaskTests.
 */
class db_TaskTests {
    private ai_Task task;

    @BeforeEach
    void setUp() {
        task = new ai_Task();
    }

    @AfterEach
    void tearDown() {
        task = null;
    }

    @Test
    void matchSearch_ValidatesAllSearchScenarios() {
        // Setup task with test data
        task.sTitle = "Test Title";
        task.sDescription = "Important Description";
        task.sCategory = "Test Category";
        task.sStatus = "Open";

        // Empty/null searches
        assertTrue(task.matchSearch(""));
        assertTrue(task.matchSearch(null));

        // Single term matches in different fields
        assertTrue(task.matchSearch("test"));     // matches title & category
        assertTrue(task.matchSearch("title"));    // matches title
        assertTrue(task.matchSearch("important")); // matches description
        assertTrue(task.matchSearch("category")); // matches category
        assertTrue(task.matchSearch("open"));     // matches status

        // Multiple terms - all must match
        assertTrue(task.matchSearch("test title"));
        assertTrue(task.matchSearch("test important"));
        assertFalse(task.matchSearch("test wrong"));

        // Case insensitivity
        assertTrue(task.matchSearch("TEST"));
        assertTrue(task.matchSearch("TiTlE"));
        assertTrue(task.matchSearch("IMPORTANT description"));

        // Negative cases
        assertFalse(task.matchSearch("wrong"));
        assertFalse(task.matchSearch("closed"));
        assertFalse(task.matchSearch("test wrong term"));
    }
}
