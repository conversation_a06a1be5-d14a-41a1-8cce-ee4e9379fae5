package com.snapinspect.snapinspect3.db;

import com.google.gson.Gson;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;

/**
 * Test class db_InsItemTest.
 */
class db_InsItemTest {
    private Gson gson;
    private ai_InsItem aiInsItem;
    private Map<String, Object> singleDropPinData;
    private final static String MARKS = "[{\"x\":0.5,\"y\":0.5}]";

    @BeforeEach
    void setUp() {
        gson = new Gson();
        aiInsItem = new ai_InsItem();

        singleDropPinData = new HashMap<>();
        singleDropPinData.put(Constants.Keys.iFloorPlanID, 133);
        singleDropPinData.put(Constants.Keys.objects, MARKS);

        List<Map<String, Object>> dropPinData = new ArrayList<>();
        dropPinData.add(singleDropPinData);

        aiInsItem.sCustomTwo = gson.toJson(dropPinData);
    }

    @AfterEach
    void tearDown() {
        gson = null;
        aiInsItem = null;
        singleDropPinData = null;
    }

    /**
     * Test method for {@link db_InsItem#getDropPinData(ai_InsItem)}.
     */
    @Test
    void testGetDropPinData() {
        List<Map<String, Object>> resultingDropPinData = db_InsItem.getDropPinData(aiInsItem);

        assertEquals(1, resultingDropPinData.size());

        Map<String, Object> dropPin = resultingDropPinData.get(0);

        Double iSFloorPlanIDDouble = (Double) dropPin.get(Constants.Keys.iFloorPlanID);
        Integer iSFloorPlanID = iSFloorPlanIDDouble != null ? iSFloorPlanIDDouble.intValue() : null;
        assertEquals(133, iSFloorPlanID);

        assertEquals(MARKS, dropPin.get(Constants.Keys.objects));
    }
}