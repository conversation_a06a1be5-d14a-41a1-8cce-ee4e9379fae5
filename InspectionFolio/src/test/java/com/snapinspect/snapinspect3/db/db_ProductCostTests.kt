package com.snapinspect.snapinspect3.db

import android.app.Application
import junit.framework.TestCase.assertTrue
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner
import org.robolectric.annotation.Config

@RunWith(RobolectricTestRunner::class)
@Config(application = Application::class, sdk = [32])
class db_ProductCostTests {

//    @BeforeEach
//    fun setUp() {
//        val context: Context = getApplicationContext()
//        SugarContext.init(context)
//    }
//
//    @AfterEach
//    fun tearDown() {
//        SugarContext.terminate()
//    }

//    @Test
//    fun testSaveProductCost() {
//        val productCost = ai_ProductCost().apply {
//            iSCostingID = 1
//        }
//        db_ProductCost.saveProductCost(productCost)
//        val retrievedProductCost = db_ProductCost.getProductCost(1)
//        assertEquals(productCost.iSCostingID, retrievedProductCost?.iSCostingID)
//    }

    @Test
    fun simpleTest() {
        assertTrue(true)
    }
}