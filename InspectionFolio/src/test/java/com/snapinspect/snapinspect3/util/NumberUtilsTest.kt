package com.snapinspect.snapinspect3.util

import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.Test
import java.util.*

class NumberUtilsTest {
    @Test
    fun testFormatCurrency() {
        val value = 12345.6789
        val result = NumberUtils.formatCurrency(value)
        assertEquals("$12,345.68", result)
    }

    @Test
    fun testFormatCurrencyWithoutGrouping() {
        val value = 12345.6789
        val result = NumberUtils.formatCurrencyWithoutGrouping(value)
        assertEquals("$12345.68", result)
    }

    @Test
    fun testFormatCurrencyWithCustomLocale() {
        val value = 12345.6789
        val result = NumberUtils.formatCurrency(value, locale = Locale.GERMANY, currencyCode = "EUR", symbol = "€")
        assertEquals("12.345,68 €", result)

        val result2 = NumberUtils.formatCurrency(value, locale = Locale.SIMPLIFIED_CHINESE, currencyCode = "CNY", symbol = "¥")
        assertEquals("¥12,345.68", result2)

        val result3 = NumberUtils.formatCurrencyWithoutGrouping(value, locale = Locale.UK, currencyCode = "GBP", symbol = "£")
        assertEquals("£12345.68", result3)

        val result4 = NumberUtils.formatCurrency(
            value, locale = Locale.US,
            currencyCode = "EUR",
            symbol = "€",
            maxFractionDigits = 3,
            useGrouping = true
        )
        assertEquals("€12,345.679", result4)
    }
}