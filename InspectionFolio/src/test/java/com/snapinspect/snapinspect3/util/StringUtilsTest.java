package com.snapinspect.snapinspect3.util;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for the StringUtils.java class
 */
class StringUtilsTest {
    @Test
    void testGetFirstLetters_NullOrEmpty() {
        // Assert that if the input string is null or empty,
        // the getFirstLetters method should return null
        assertNull(StringUtils.getFirstLetters(null));
        assertNull(StringUtils.getFirstLetters(""));
    }

    @Test
    void testGetFirstLetters_SingleWord() {
        // Assert that if the input string is a single word,
        // the getFirstLetters method should return the first letter of that word
        assertEquals("A", StringUtils.getFirstLetters("Apple"));
    }

    @Test
    void testGetFirstLetters_TwoWords() {
        // Assert that if the input string consists of two words,
        // the getFirstLetters method should return the first letters of both words
        assertEquals("AS", StringUtils.getFirstLetters("Apple Samsung"));
    }

    @Test
    void testGetFirstLetters_MultipleWords() {
        // Assert that if the input string consists of more than two words,
        // the getFirstLetters method should return the first letters of the first and last word
        assertEquals("AF", StringUtils.getFirstLetters("Apple Fruit Basket Fresh"));
    }

    /**
     * Test the getURLQueryParam method with a URL and a key that exists in the URL. The expected output would be the value
     * associated with that key in the URL's query parameters.
     */
    @Test
    void getURLQueryParam_exists() {
        String url = "http://example.com?param1=value1&param2=value2";
        String key = "param2";
        String expectedValue = "value2";

        String actualValue = StringUtils.getURLQueryParam(url, key);

        assertEquals(expectedValue, actualValue);
    }

    /**
     * Test the getURLQueryParam method with a URL and a key that doesn't exist in the URL. The expected output would be null,
     * as the method is supposed to return null when the key does not exist in the URL's query parameters.
     */
    @Test
    void getURLQueryParam_notExists() {
        String url = "http://example.com?param1=value1&param2=value2";
        String key = "param3";

        String actualValue = StringUtils.getURLQueryParam(url, key);

        assertNull(actualValue);
    }

    /**
     * Test the getURLQueryParam method with a URL that doesn't have any parameters. The expected output would be null,
     * as the method is supposed to return null when the URL has no query parameters.
     */
    @Test
    void getURLQueryParam_noParams() {
        String url = "http://example.com";
        String key = "param1";

        String actualValue = StringUtils.getURLQueryParam(url, key);

        assertNull(actualValue);
    }

    /**
     * Test the getURLQueryParam method with a URL, key and default value. The key doesn't exist in the URL, so
     * expected output should be the default value
     */
    @Test
    void getURLQueryParam_defaultValue() {
        String url = "http://example.com?param1=value1&param2=value2";
        String key = "param3";
        String defaultValue = "default";

        String actualValue = StringUtils.getURLQueryParam(url, key, defaultValue);

        assertEquals(defaultValue, actualValue);
    }

    @Test
    void testReplaceLast() {
        // Initial test string
        String testString = "Hello, world! This world is beautiful. world";

        // Replace the last 'world' occurrence with 'green earth'
        String result = StringUtils.replaceLast(testString, "world", "green earth");

        // True expected result
        String trueResult = "Hello, world! This world is beautiful. green earth";

        // Assert that the returned result and true result are equal
        assertEquals(trueResult, result);
    }

    @Test
    void testReplaceLastNonExistentTarget() {
        // Initial test string
        String testString = "Hello, world! This world is beautiful. world";

        // Try to replace 'universe' which does not exist in the test string
        String result = StringUtils.replaceLast(testString, "universe", "cosmos");

        // Assert that the returned result and the initial test string are equal as no replacement should occur
        assertEquals(testString, result);
    }

    @Test
    void testIfEmpty() {
        ifEmptyTest("TestA", "TestB", "TestA");
        ifEmptyTest(null, "TestB", "TestB");
        ifEmptyTest("", "TestB", "TestB");
        ifEmptyTest("", "", "");
        ifEmptyTest("   ", "TestB", "TestB");
        ifEmptyTest(null, null, null);
        ifEmptyTest("TestA", null, "TestA");
        ifEmptyTest("TestA", "", "TestA");
        ifEmptyTest("TestA", " ", "TestA");
        ifEmptyTest(" ", null, null);
        ifEmptyTest(" ", "", "");
        ifEmptyTest(" ", " ", " ");
    }

    private void ifEmptyTest(String a, String b, String expected) {
        if (!StringUtils.isEmpty(a) && !StringUtils.isEmpty(b)) {
            assertNotNull(StringUtils.ifEmpty(a, b));
        } else {
            assertNull(StringUtils.ifEmpty(null, null));
        }
        assertEquals(expected, StringUtils.ifEmpty(a, b));
    }

    @Test
    void testEnsureHttpsScheme() {
        // Test case 1: URL with http scheme
        assertEquals("https://www.example.com", StringUtils.ensureHttpsScheme("http://www.example.com"));

        // Test case 2: URL with https scheme
        assertEquals("https://www.example.com", StringUtils.ensureHttpsScheme("https://www.example.com"));

        // Test case 3: URL without scheme
        assertEquals("https://www.example.com", StringUtils.ensureHttpsScheme("www.example.com"));

        // Test case 4: Empty string
        assertNull(StringUtils.ensureHttpsScheme(""));

        // Test case 5: Null input
        assertNull(StringUtils.ensureHttpsScheme(null));

        // Test case 6: URL with subdomain and path
        assertEquals("https://subdomain.example.com/path", StringUtils.ensureHttpsScheme("subdomain.example.com/path"));

        // Test case 7: URL with port number
        assertEquals("https://www.example.com:8080", StringUtils.ensureHttpsScheme("http://www.example.com:8080"));

        // Test case 8: URL with query parameters
        assertEquals("https://www.example.com/search?q=test", StringUtils.ensureHttpsScheme("www.example.com/search?q=test"));

        // Test case 9: URL with fragment identifier
        assertEquals("https://www.example.com/page#section", StringUtils.ensureHttpsScheme("http://www.example.com/page#section"));
    }
}