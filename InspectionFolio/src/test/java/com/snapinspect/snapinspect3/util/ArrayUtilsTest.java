package com.snapinspect.snapinspect3.util;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

class ArrayUtilsTest {
    /**
     * This class is for testing the ArrayUtils class specifically the isEmpty method.
     * The method returns true if the provided list is either null or contains no elements, and false otherwise.
     */
    
    // Test when list is null
    @Test
    void isEmpty_NullList_ReturnsTrue() {
        List<Integer> testList = null;
        Assertions.assertTrue(ArrayUtils.isEmpty(testList));
    }

    // Test when list is empty
    @Test
    void isEmpty_EmptyList_ReturnsTrue() {
        List<Integer> testList = new ArrayList<>();
        boolean result = ArrayUtils.isEmpty(testList);
        Assertions.assertTrue(result);
    }

    // Test when list has elements
    @Test
    void isEmpty_ListHasElements_ReturnsFalse() {
        List<Integer> testList = Arrays.asList(1, 2, 3);
        boolean result = ArrayUtils.isEmpty(testList);
        Assertions.assertFalse(result);
    }


    /**
     * Tests the split method in the ArrayUtils class.
     */
    @Test
    void testSplit() {
        // A valid scenario would be to test splitting a list with certain elements

        List<Integer> list = Arrays.asList(1, 2, 3, 4, 5);
        List<List<Integer>> actualOutput = ArrayUtils.split(list, 2);
        List<Integer> firstChunk = Arrays.asList(1, 2);
        List<Integer> secondChunk = Arrays.asList(3, 4);
        List<Integer> thirdChunk = Arrays.asList(5);
        List<List<Integer>> expectedOutput = Arrays.asList(firstChunk, secondChunk, thirdChunk);

        assertEquals(expectedOutput, actualOutput);


        // An edge case would be to test split with an empty list
        list = Collections.emptyList();
        actualOutput = ArrayUtils.split(list, 2);
        assertTrue(ArrayUtils.isEmpty(actualOutput));

        // Another edge case would be to test split with a null list
        list = null;
        actualOutput = ArrayUtils.split(list, 2);
        assertTrue(ArrayUtils.isEmpty(actualOutput));
    }
}