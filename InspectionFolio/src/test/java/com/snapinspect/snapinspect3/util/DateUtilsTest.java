package com.snapinspect.snapinspect3.util;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Calendar;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;

class DateUtilsTest {

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void daysPassed() {
        Date d1 = DateUtils.parse("2022-01-01 11:11:11", "yyyy-MM-dd HH:mm:ss");
        Date d2 = DateUtils.parse("2022-01-02 11:11:12", "yyyy-MM-dd hh:mm:ss");
        Date d3 = DateUtils.parse("2022-01-02 11:11:10", "yyyy-MM-dd hh:mm:ss");
        Date d4 = DateUtils.parse("2022-01-02 11:11:11", "yyyy-MM-dd hh:mm:ss");
        assertNotNull(d1);
        assertNotNull(d2);
        assertNotNull(d3);
        assertNotNull(d4);
        assertEquals(1, DateUtils.daysPassed(d2, d1));
        assertEquals(0, DateUtils.daysPassed(d3, d1));
        assertEquals(1, DateUtils.daysPassed(d4, d1));
    }

    @Test
    void startDate() {
        Date d1 = DateUtils.parse("2022-01-01 11:11:11", "yyyy-MM-dd HH:mm:ss");
        Date d2 = DateUtils.parse("2022-01-01 00:00:00", "yyyy-MM-dd hh:mm:ss");
        assertEquals(d2, DateUtils.startDate(d1));
    }

    @Test
    void endDate() {
        Date d1 = DateUtils.parse("2022-01-01 11:11:11", "yyyy-MM-dd HH:mm:ss");
        Date d2 = DateUtils.parse("2022-01-01 23:59:59", "yyyy-MM-dd hh:mm:ss");
        assertEquals(d2, DateUtils.endDate(d1));
    }

    @Test
    void clockStringFromMilliseconds() {
        assertEquals("00:00", DateUtils.clockStringFromMilliseconds(0));
        assertEquals("00:01", DateUtils.clockStringFromMilliseconds(1000));
        assertEquals("00:10", DateUtils.clockStringFromMilliseconds(10000));
        assertEquals("01:00", DateUtils.clockStringFromMilliseconds(60000));
        assertEquals("04:32", DateUtils.clockStringFromMilliseconds(272000));
        assertEquals("10:00", DateUtils.clockStringFromMilliseconds(600000));
        assertEquals("01:00:00", DateUtils.clockStringFromMilliseconds(3600000));
        assertEquals("10:00:00", DateUtils.clockStringFromMilliseconds(36000000));
        assertEquals("99:59:59", DateUtils.clockStringFromMilliseconds(359999000L));
    }

    Date d1 = DateUtils.parse("2022-01-01 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d2 = DateUtils.parse("2022-01-02 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d3 = DateUtils.parse("2022-01-03 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d4 = DateUtils.parse("2022-01-04 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d5 = DateUtils.parse("2022-01-05 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d6 = DateUtils.parse("2022-01-06 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d7 = DateUtils.parse("2022-01-07 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d8 = DateUtils.parse("2022-01-08 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d9 = DateUtils.parse("2022-01-09 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d10 = DateUtils.parse("2022-01-10 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d11 = DateUtils.parse("2022-01-11 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d12 = DateUtils.parse("2022-01-12 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d13 = DateUtils.parse("2022-01-13 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d14 = DateUtils.parse("2022-01-14 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d15 = DateUtils.parse("2022-01-15 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d16 = DateUtils.parse("2022-01-16 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d17 = DateUtils.parse("2022-01-17 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d18 = DateUtils.parse("2022-01-18 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d19 = DateUtils.parse("2022-01-19 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d20 = DateUtils.parse("2022-01-20 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d21 = DateUtils.parse("2022-01-21 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d22 = DateUtils.parse("2022-01-22 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d23 = DateUtils.parse("2022-01-23 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d24 = DateUtils.parse("2022-01-24 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d25 = DateUtils.parse("2022-01-25 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d26 = DateUtils.parse("2022-01-26 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d27 = DateUtils.parse("2022-01-27 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d28 = DateUtils.parse("2022-01-28 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d29 = DateUtils.parse("2022-01-29 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d30 = DateUtils.parse("2022-01-30 11:11:11", "yyyy-MM-dd HH:mm:ss");
    Date d31 = DateUtils.parse("2022-01-31 11:11:11", "yyyy-MM-dd HH:mm:ss");

    @Test
    void ordinalDayOfMonth() {
        assertEquals("1st", DateUtils.ordinalDayOfMonth(d1));
        assertEquals("2nd", DateUtils.ordinalDayOfMonth(d2));
        assertEquals("3rd", DateUtils.ordinalDayOfMonth(d3));
        assertEquals("4th", DateUtils.ordinalDayOfMonth(d4));
        assertEquals("5th", DateUtils.ordinalDayOfMonth(d5));
        assertEquals("6th", DateUtils.ordinalDayOfMonth(d6));
        assertEquals("7th", DateUtils.ordinalDayOfMonth(d7));
        assertEquals("8th", DateUtils.ordinalDayOfMonth(d8));
        assertEquals("9th", DateUtils.ordinalDayOfMonth(d9));
        assertEquals("10th", DateUtils.ordinalDayOfMonth(d10));
        assertEquals("11th", DateUtils.ordinalDayOfMonth(d11));
        assertEquals("12th", DateUtils.ordinalDayOfMonth(d12));
        assertEquals("13th", DateUtils.ordinalDayOfMonth(d13));
        assertEquals("14th", DateUtils.ordinalDayOfMonth(d14));
        assertEquals("15th", DateUtils.ordinalDayOfMonth(d15));
        assertEquals("16th", DateUtils.ordinalDayOfMonth(d16));
        assertEquals("17th", DateUtils.ordinalDayOfMonth(d17));
        assertEquals("18th", DateUtils.ordinalDayOfMonth(d18));
        assertEquals("19th", DateUtils.ordinalDayOfMonth(d19));
        assertEquals("20th", DateUtils.ordinalDayOfMonth(d20));
        assertEquals("21st", DateUtils.ordinalDayOfMonth(d21));
        assertEquals("22nd", DateUtils.ordinalDayOfMonth(d22));
        assertEquals("23rd", DateUtils.ordinalDayOfMonth(d23));
        assertEquals("24th", DateUtils.ordinalDayOfMonth(d24));
        assertEquals("25th", DateUtils.ordinalDayOfMonth(d25));
        assertEquals("26th", DateUtils.ordinalDayOfMonth(d26));
        assertEquals("27th", DateUtils.ordinalDayOfMonth(d27));
        assertEquals("28th", DateUtils.ordinalDayOfMonth(d28));
        assertEquals("29th", DateUtils.ordinalDayOfMonth(d29));
        assertEquals("30th", DateUtils.ordinalDayOfMonth(d30));
        assertEquals("31st", DateUtils.ordinalDayOfMonth(d31));
    }

    @Test
    void startDateOfMonth() {
        assertEquals(DateUtils.parse("2022-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"),
                DateUtils.startDateOfMonth(d12));
        assertEquals(DateUtils.parse("2022-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"),
                DateUtils.startDateOfMonth(d22));
    }

    @Test
    void startDateOfWeek() {
        assertEquals(DateUtils.parse("2022-01-02 00:00:00", "yyyy-MM-dd HH:mm:ss"),
                DateUtils.startDateOfWeek(d3, Calendar.SUNDAY));
        assertEquals(DateUtils.parse("2022-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss"),
                DateUtils.startDateOfWeek(d3, Calendar.SATURDAY));
    }

    @Test
    void endDateOfWeek() {
        assertEquals(DateUtils.parse("2022-01-08 23:59:59", "yyyy-MM-dd HH:mm:ss"),
                DateUtils.endDateOfWeek(d3, Calendar.SUNDAY));
        assertEquals(DateUtils.parse("2022-01-07 23:59:59", "yyyy-MM-dd HH:mm:ss"),
                DateUtils.endDateOfWeek(d3, Calendar.SATURDAY));
    }

    @Test
    void endDateOfMonth() {
        assertEquals("2022-01-31 23:59:59",
                DateUtils.format(DateUtils.endDateOfMonth(d1), "yyyy-MM-dd HH:mm:ss"));
        assertEquals("2022-01-31 23:59:59",
                DateUtils.format(DateUtils.endDateOfMonth(d2), "yyyy-MM-dd HH:mm:ss"));

        Date date = DateUtils.parse("2022-02-01 11:11:11", "yyyy-MM-dd HH:mm:ss");
        assertEquals("2022-02-28 23:59:59",
                DateUtils.format(DateUtils.endDateOfMonth(date), "yyyy-MM-dd HH:mm:ss"));
	}

    void isValidDate() {
        Date d1 = DateUtils.parse("1979-01-01 11:11:11", "yyyy-MM-dd HH:mm:ss");
        Date d2 = DateUtils.parse("1981-01-01 11:11:11", "yyyy-MM-dd HH:mm:ss");
        Date d3 = DateUtils.parse("2026-01-01 11:11:11", "yyyy-MM-dd HH:mm:ss");
        assertFalse(DateUtils.isValidDate(d1));
        assertTrue(DateUtils.isValidDate(d2));
        assertTrue(DateUtils.isValidDate(d3));
    }

    @Test
    void cleanDateIfTimeIsZero() {
        assertEquals("2022-01-01", DateUtils.cleanDateIfTimeIsZero("2022-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd"));
        assertEquals("2022-01-01 00:00:01", DateUtils.cleanDateIfTimeIsZero("2022-01-01 00:00:01", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd"));
        assertEquals("2022-01-01 00:01:00", DateUtils.cleanDateIfTimeIsZero("2022-01-01 00:01:00", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd"));
        assertEquals("2022-01-01 01:00:00", DateUtils.cleanDateIfTimeIsZero("2022-01-01 01:00:00", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd"));
        assertNull(DateUtils.cleanDateIfTimeIsZero("1980-02-01 00:00:00", "yyyy-MM-dd'T'HH:mm:ss", "yyyy-MM-dd"));
        assertNull(DateUtils.cleanDateIfTimeIsZero("1980-01-01 00:00:00", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd"));
    }
}
