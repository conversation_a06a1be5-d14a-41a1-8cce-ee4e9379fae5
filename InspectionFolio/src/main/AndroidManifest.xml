<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:versionCode="218"
    android:versionName="3.9.33">

    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"
                     android:maxSdkVersion="29"
                     tools:replace="android:maxSdkVersion" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission
        android:name="android.permission.SYSTEM_ALERT_WINDOW"
        tools:remove="android:maxSdkVersion" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
    <uses-permission android:name="com.google.android.c2dm.permission.REGISTER" />
    <uses-permission android:name="android.permission.HIGH_SAMPLING_RATE_SENSORS"
                      tools:ignore="HighSamplingRate" />


    <permission
        android:name="com.snapinspect.snapinspect3.permission.C2D_MESSAGE"
        android:protectionLevel="signature" />

    <uses-feature
        android:name="android.hardware.camera.any" />
    <uses-feature
        android:name="android.hardware.camera"
        android:required="true" />
    <uses-feature
        android:name="android.hardware.camera.autofocus"
        android:required="false" />

    <uses-permission
        android:name="android.permission.READ_PHONE_STATE" />

    <uses-permission
        android:name="android.permission.POST_NOTIFICATIONS" />

    <application
        android:name=".app.App"
        android:allowBackup="true"
        android:icon="@drawable/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:theme="@style/AppTheme">

        <uses-library
            android:name="org.apache.http.legacy"
            android:required="false" />

        <meta-data
            android:name="com.google.firebase.messaging.default_notification_icon"
            android:resource="@drawable/ic_launcher" />
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyAl5ob9ggxfpjZ5gu42eMQMe_Hoo2h1tE4" />

        <service
            android:name=".activity.SyncService"
            android:exported="false" />
        <service
            android:name=".activity.UploadService"
            android:exported="false" />
        <service
            android:name=".activity.UploadLogService"
            android:exported="false" />
        <service
            android:name=".async.SyncProjectService"
            android:exported="false" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.provider"
            tools:replace="android:authorities"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"
                tools:replace="android:resource"/>
        </provider>

        <activity
            android:name=".activity.if_login"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/app_name"
            android:screenOrientation="portrait"
            android:theme="@style/AppCompactTheme"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.QRReaderActivity"
            android:label="SnapInspect 3"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.SEND" />
                <category android:name="android.intent.category.DEFAULT" />
                <data android:mimeType="image/*" />
            </intent-filter>
        </activity>
        <activity
            android:name=".activitynew.Home.if_HomeTab"
            android:configChanges="keyboardHidden|screenSize|orientation"
            android:label="@string/title_activity_if_home"
            android:theme="@style/AppCompactTheme"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".activity.if_NewAsset"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_newasset"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activitynew.if_existasset"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_existasset"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activitynew.if_EditInspection"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label=""
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activitynew.if_OrderInsItem"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label=""
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activitynew.if_OrderInspection"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label=""
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="adjustPan" />
        <activity
            android:name=".activity.if_AssetDetails"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_assetdetails"
            android:theme="@style/FullscreenTheme.Flat" />
        <activity
            android:name=".activity.if_UpdateAsset"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="Asset"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_Sync"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if__sync"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_EditComments"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_editcomments"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_EditText"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_EditText"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activity.if_quickphrase"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_quickphrase"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_smartcomment"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_smartcomment"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <!--
        <activity
            android:name=".if_photo"
            android:configChanges="keyboardHidden|screenSize"
            android:label="@string/title_activity_if_photo"
            android:theme="@style/FullscreenTheme" >
        </activity>
        -->
        <activity
            android:name=".activity.if_sign"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_sign"
            android:screenOrientation="portrait"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_video"
            android:configChanges="keyboardHidden|screenSize"
            android:label="@string/title_activity_if_video"
            android:screenOrientation="landscape"
            android:theme="@style/AppCompactTheme" />
        <activity
            android:name=".activity.if_video_old"
            android:configChanges="keyboardHidden|screenSize"
            android:label="@string/title_activity_if_video"
            android:screenOrientation="landscape"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_videoshow"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_videoshow"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_settings"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_settings"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_Asset_2nd"
            android:configChanges="keyboardHidden|screenSize"
            android:label="@string/title_activity_if__asset_2nd"
            android:theme="@style/FullscreenTheme.Flat" />
        <activity
            android:name=".activity.if_Notice"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".activity.if_selectrating"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_selectrating"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_ImageAnnotation"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if__image_annotation"
            android:screenOrientation="portrait"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_CameraX"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:screenOrientation="portrait"
            android:theme="@style/AppCompactTheme" />
        <activity
            android:name=".activitynew.CameraActivity"
            android:configChanges="orientation|screenSize|keyboardHidden"
            android:label="@string/title_activity_if__camera"
            android:screenOrientation="portrait"
            android:theme="@style/AppTheme.NoActionBar" />
        <activity
            android:name=".Scanner.ColorActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="Color"
            android:theme="@style/ScanThemeWithBack" />
        <activity
            android:name=".activity.if_NoticeList"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="Notification List"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_serveremail"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_serveremail"
            android:theme="@style/AppOwnTheme" />
        <activity
            android:name=".activity.DeepLinkActivity"
            android:launchMode="singleTask"
            android:exported="true">
            <intent-filter android:autoVerify="true">
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="https" />
                <data android:scheme="http" />
                <data android:host="@string/deeplink_url_host" />
                <data android:pathPrefix="/external/getrequestinspection" />
                <data android:pathPrefix="/external/schedule" />
                <data android:pathPrefix="/external/requestinspection" />
                <data android:pathPrefix="/SyncExternal/RequestExternalAccess" />
                <data android:pathPrefix="/applink/assets/details" />

                <!-- note that the leading "/" is required for pathPrefix -->
                <!-- Accepts URIs that begin with "example://gizmos” -->

                <!--
                <data
                    android:scheme="snapinspect"
                    android:host="inspection_app" />
                -->
            </intent-filter>
        </activity>
        <activity
            android:name=".activity.if_MapDetails"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if__map_details"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_InspectionMap"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if__inspection_map"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".QRScan.QRScannerActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:screenOrientation="portrait"
            android:theme="@style/AppOverlayTheme" />
        <!--
 ATTENTION: This was auto-generated to add Google Play services to your project for
     App Indexing.  See https://g.co/AppIndexing/AndroidStudio for more information.
        -->
        <activity
            android:name=".activity.if_RequestInspection"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".activitynew.Comments.if_InspectionComments"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if__inspection_comments"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateHidden" />
        <activity
            android:name=".activitynew.Inbox.if_Inbox"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if__inbox"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activity.if_CommentsLibrary"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if__comments_library"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activitynew.inspection3rd.if_Layout_3rd"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_Layout_3rd"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activitynew.inspection3rd.if_Ins_3rd"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_Inspection_3rd"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activitynew.inspection3rd.if_Ins_Edit_3rd"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_Inspection_3rd"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activitynew.inspection3rd.if_Ins_items_3rd"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_Inspection_3rd"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateHidden|adjustResize" />
        <activity
            android:name=".activitynew.Edit.if_EditContact_2nd"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activitynew.Edit.if_UpdateAsset_2nd"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activitynew.Photo.if_DisplayPhoto"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_photo"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateVisible|adjustResize" />
        <activity
            android:name=".activitynew.Photo.if_DisplayFile"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_photo"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activitynew.Photo.if_DisplayAllPhotos"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_all_photos"
            android:theme="@style/FullscreenTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activitynew.Edit.if_EditCustomInfo"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_edit_custom_ifo"
            android:theme="@style/AppCompactBarTheme"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".activitynew.Inbox.if_TaskComments"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_task_comments"
            android:theme="@style/FullscreenTheme" />
        <activity
            android:name=".activitynew.Inbox.if_InspectionInfo"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:label="@string/title_activity_if_inspection_info"
            android:theme="@style/AppCompactBarTheme" />
        <activity
            android:name=".activitynew.if_DateTimePicker"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/Theme.Transparent.DatePicker" />

        <activity
            android:name=".activitynew.Projects.if_ProjectInspection"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:theme="@style/FullscreenTheme.Flat" />

        <activity android:name=".activity.if_VideoClips"
                  android:configChanges="orientation|keyboardHidden|screenSize"
                  android:theme="@style/FullscreenTheme"
                  android:windowSoftInputMode="stateAlwaysHidden" />

        <activity android:name=".activitynew.if_DisplayFloorPlan"
                  android:configChanges="orientation|keyboardHidden|screenSize"
                  android:theme="@style/FullscreenTheme"
                  android:label="@string/title_activity_if_display_floor_plan"
                  android:windowSoftInputMode="stateAlwaysHidden" />

        <activity android:name=".activitynew.if_InsItemFloorPlans"
                  android:configChanges="orientation|keyboardHidden|screenSize"
                  android:theme="@style/FullscreenTheme"
                  android:windowSoftInputMode="stateAlwaysHidden" />

        <activity android:name=".activitynew.products.if_ProductCosts"
                  android:configChanges="orientation|keyboardHidden|screenSize"
                  android:theme="@style/FullscreenTheme"
                  android:windowSoftInputMode="stateAlwaysHidden" />

        <activity android:name=".activitynew.products.if_Products"
                  android:configChanges="orientation|keyboardHidden|screenSize"
                  android:theme="@style/FullscreenTheme"
                  android:windowSoftInputMode="stateAlwaysHidden" />

        <activity android:name=".activitynew.products.if_ProductDetails"
                  android:configChanges="orientation|keyboardHidden|screenSize"
                  android:theme="@style/FullscreenTheme"
                  android:windowSoftInputMode="stateAlwaysHidden" />

        <activity android:name=".activitynew.tasks.if_AssetTasks"
                  android:configChanges="orientation|keyboardHidden|screenSize"
                  android:theme="@style/FullscreenTheme"
                  android:windowSoftInputMode="stateAlwaysHidden" />

        <activity android:name=".activitynew.tasks.if_EditTask"
                  android:configChanges="orientation|keyboardHidden|screenSize"
                  android:theme="@style/FullscreenTheme"
                  android:windowSoftInputMode="stateAlwaysHidden" />

        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />


        <!--
        <receiver
            android:name="com.google.android.gms.gcm.GcmReceiver"
            android:exported="true"
            android:permission="com.google.android.c2dm.permission.SEND" >
            <intent-filter>
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
                <category android:name="com.snapinspect.snapinspect3" />
            </intent-filter>
        </receiver>
        -->
        <service android:name=".Helper.SIFcmListenerService" android:exported="false">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
            </intent-filter>
        </service>

        <!--Intent filter to capture authorization code response from the default browser on the device calling back to our app after interactive sign in -->
        <activity
            android:name="com.microsoft.identity.client.BrowserTabActivity"
            android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data
                    android:scheme="msauth"
                    android:host="${applicationId}"
                    android:path="@string/azure_sso_signature_hash" />
            </intent-filter>
        </activity>

        <!-- Trigger Google Play services to install the backported photo picker module. -->
        <!-- https://developer.android.com/training/data-storage/shared/photopicker#device-availability -->
        <!--suppress AndroidDomInspection -->
        <service android:name="com.google.android.gms.metadata.ModuleDependencies"
                 android:enabled="false" android:exported="false" tools:ignore="MissingClass">
            <intent-filter>
                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
            </intent-filter>
            <meta-data android:name="photopicker_activity:0:required" android:value="" />
        </service>
    </application>
</manifest>