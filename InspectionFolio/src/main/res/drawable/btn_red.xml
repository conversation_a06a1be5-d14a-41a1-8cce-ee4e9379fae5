<?xml version="1.0" encoding="utf-8"?>

<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true" >
        <shape android:shape="rectangle"  >
            <corners android:radius="3dip" />
            <stroke android:width="1dip" android:color="#0B4180" />
           <!-- <gradient  android:angle="-90"  android:startColor="#DB3633" android:endColor="#E6413E" /> -->
            <solid android:color="#E6403C" />
        </shape>
    </item>

    <item>
        <shape android:shape="rectangle"  >
            <corners android:radius="3dip" />
            <stroke android:width="1dip" android:color="#0B4180" />
           <!-- <gradient  android:angle="-90"  android:startColor="#E6403C" android:endColor="#F07162"  /> -->
            <solid android:color="#DB3633" />
        </shape>
    </item>
</selector>