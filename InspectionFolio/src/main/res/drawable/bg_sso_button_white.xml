<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:state_enabled="false">
        <shape android:shape="rectangle">
            <corners android:radius="3dp" />
            <solid android:color="#110d1a1e" />
        </shape>

    </item>

    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="3dp" />
            <solid android:color="@color/light_gray_color" />
        </shape>

    </item>

    <item>
        <shape android:shape="rectangle">
            <corners android:radius="3dp" />
            <solid android:color="@color/white_color" />
        </shape>
    </item>
</selector>