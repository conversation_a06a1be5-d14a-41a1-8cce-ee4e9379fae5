<?xml version="1.0" encoding="utf-8"?>

<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true"  >
        <shape android:shape="rectangle"  >
            <corners android:radius="3dip" />
            <stroke android:width="2dip" android:color="@color/SI_Button_Red_Highlight" />

        </shape>
    </item>

    <item>
        <shape android:shape="rectangle"  >
            <corners android:radius="3dip" />
            <stroke android:width="2dip" android:color="@color/SI_Button_Red" />

        </shape>
    </item>

</selector>