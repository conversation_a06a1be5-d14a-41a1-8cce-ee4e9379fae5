<resources>

    <!-- Declare custom theme attributes that allow changing which styles are
         used for button bars depending on the API level.
         ?android:attr/buttonBarStyle is new as of API 11 so this is
         necessary to support previous API levels. -->
    <declare-styleable name="ButtonBarContainerTheme">
        <!--<attr name="buttonBarStyle" format="reference" />
        <attr name="buttonBarButtonStyle" format="reference" /> -->
    </declare-styleable>
    <declare-styleable name="SegmentedGroup">
        <attr name="sc_corner_radius" format="dimension" />
        <attr name="sc_border_width" format="dimension" />
        <attr name="sc_tint_color" format="color" />
        <attr name="sc_checked_text_color" format="color" />
    </declare-styleable>

    <!--  CropImageView  -->
    <declare-styleable name="CropImageView">
        <attr name="cropGuidelines">
            <enum name="off" value="0"/>
            <enum name="onTouch" value="1"/>
            <enum name="on" value="2"/>
        </attr>
        <attr name="cropScaleType">
            <enum name="fitCenter" value="0"/>
            <enum name="center" value="1"/>
            <enum name="centerCrop" value="2"/>
            <enum name="centerInside" value="3"/>
        </attr>
        <attr name="cropShape">
            <enum name="rectangle" value="0"/>
            <enum name="oval" value="1"/>
        </attr>
        <attr name="cropAutoZoomEnabled" format="boolean"/>
        <attr name="cropMaxZoom" format="integer"/>
        <attr name="cropMultiTouchEnabled" format="boolean"/>
        <attr name="cropFixAspectRatio" format="boolean"/>
        <attr name="cropAspectRatioX" format="integer"/>
        <attr name="cropAspectRatioY" format="integer"/>
        <attr name="cropInitialCropWindowPaddingRatio" format="float"/>
        <attr name="cropBorderLineThickness" format="dimension"/>
        <attr name="cropBorderLineColor" format="color"/>
        <attr name="cropBorderCornerThickness" format="dimension"/>
        <attr name="cropBorderCornerOffset" format="dimension"/>
        <attr name="cropBorderCornerLength" format="dimension"/>
        <attr name="cropBorderCornerColor" format="color"/>
        <attr name="cropGuidelinesThickness" format="dimension"/>
        <attr name="cropGuidelinesColor" format="color"/>
        <attr name="cropBackgroundColor" format="color"/>
        <attr name="cropSnapRadius" format="dimension"/>
        <attr name="cropTouchRadius" format="dimension"/>
        <attr name="cropSaveBitmapToInstanceState" format="boolean"/>
        <attr name="cropShowCropOverlay" format="boolean"/>
        <attr name="cropShowProgressBar" format="boolean"/>
        <attr name="cropMinCropWindowWidth" format="dimension"/>
        <attr name="cropMinCropWindowHeight" format="dimension"/>
        <attr name="cropMinCropResultWidthPX" format="float"/>
        <attr name="cropMinCropResultHeightPX" format="float"/>
        <attr name="cropMaxCropResultWidthPX" format="float"/>
        <attr name="cropMaxCropResultHeightPX" format="float"/>
        <attr name="cropFlipHorizontally" format="boolean"/>
        <attr name="cropFlipVertically" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="EmptyDataView">
        <attr name="viewType">
            <enum name="projects" value="0"/>
            <enum name="projectInspections" value="1"/>
            <enum name="floorPlans" value="2"/>
        </attr>
    </declare-styleable>
</resources>
