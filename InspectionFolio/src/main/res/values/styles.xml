<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Base application theme. -->
    <style name="AppTheme" parent="android:Theme.Holo.Light.DarkActionBar">
        <item name="android:actionBarStyle">@style/FullscreenActionBarStyle</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="buttonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="buttonBarButtonStyle">?android:attr/buttonBarButtonStyle</item>
        <item name="android:background">@null</item>
        <item name="android:actionBarTabStyle">@style/MyActionBarTabTextStyle</item>
        <item name="android:popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="android:height">100dp</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="FullscreenTheme" parent="android:Theme.Holo.Light.DarkActionBar">
        <item name="alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="android:actionBarStyle">@style/FullscreenActionBarStyle</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="buttonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="buttonBarButtonStyle">?android:attr/buttonBarButtonStyle</item>
        <item name="android:background">@null</item>
        <item name="android:actionBarTabStyle">@style/MyActionBarTabTextStyle</item>
        <item name="android:popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="android:height">100dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="AppCompatAlertDialogStyle" parent="Theme.AppCompat.Light.Dialog.Alert">
        <item name="colorAccent">#4CAF50</item>
        <item name="android:textColorPrimary">#E91E63</item>
        <item name="android:background">#B0BEC5</item>
    </style>

    <!--  Theme for ImagePickerActivity  -->
    <style name="ImagePickerTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="actionButtonStyle">@style/AppTheme.Widget.ActionButton</item>
    </style>

    <style name="AppTheme.Widget.ActionButton" parent="@style/Widget.AppCompat.ActionButton">
        <item name="textAllCaps">false</item>
    </style>

    <!-- Popup Menu Background Color styles -->
    <style name="PopupMenuStyle" parent="@android:style/Widget.Holo.ListPopupWindow">
        <item name="android:popupBackground">@drawable/menu_dropdown_panel_example</item>
    </style>

    <style name="RemoveShadowActionBar" parent="@style/Widget.AppCompat.Light.ActionBar.Solid.Inverse">
        <!-- remove shadow below action bar -->
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>

    </style>

    <style name="FullscreenTheme.Flat" parent="FullscreenTheme">
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <style name="AppOwnTheme" parent="android:Theme.Holo.Light.DarkActionBar">
        <item name="android:actionBarStyle">@style/FullscreenActionBarStyle</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="buttonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="buttonBarButtonStyle">?android:attr/buttonBarButtonStyle</item>
        <item name="android:background">@null</item>
        <item name="android:actionBarTabStyle">@style/MyActionBarTabTextStyle</item>
        <item name="android:height">100dp</item>
        <item name="android:titleTextStyle">@style/MyActionBarTabTextColor</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="MyActionBarTabTextColor">
        <item name="android:textColor">@color/colorTitle</item>
    </style>

    <style name="AppCompactTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@android:color/black</item>
        <item name="colorAccent">#4CAF50</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="Theme.Transparent.DatePicker" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">false</item>

        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
    </style>

    <style name="AppCompactBarTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@android:color/black</item>
        <item name="colorAccent">#4CAF50</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="EditText" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="colorControlNormal">@color/colorPrimaryDark</item>
        <item name="colorControlActivated">@color/colorPrimary</item>
        <item name="colorControlHighlight">@color/colorPrimary</item>
        <item name="android:textColorHint">@color/black</item>
        <item name="android:background">@color/transparent</item>
    </style>

    <style name="EditTextTheme" parent="Theme.AppCompat.Light.DarkActionBar">
        <item name="android:singleLine">true</item>
        <item name="colorControlNormal">@color/colorPrimaryDark</item>
        <item name="colorControlActivated">@color/colorPrimary</item>
        <item name="colorControlHighlight">@color/colorPrimary</item>
    </style>

    <style name="MyActionBarTabTextStyle">
        <item name="android:textSize">14dip</item>
        <item name="android:padding">0dip</item>
    </style>

    <style name="ActionBarStyle" parent="android:Widget.Holo.ActionBar">
        <item name="android:background">@color/black_overlay</item>
    </style>

    <style name="Button.Blue" parent="Widget.AppCompat.Button">
        <item name="android:textColorHighlight">@color/intercom_white</item>
        <item name="android:textColor">#00aeef</item>
        <item name="android:background">@drawable/bg_button_blue</item>
    </style>

    <style name="Button.Red" parent="Widget.AppCompat.Button">
        <item name="android:textColorHighlight">@color/intercom_white</item>
        <item name="android:textColor">#fc6049</item>
        <item name="android:background">@drawable/bg_button_red</item>
    </style>

    <style name="edit_login">
        <item name="android:layout_marginLeft">20dp</item>
        <item name="android:layout_marginRight">20dp</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:padding">10dp</item>
        <item name="android:textColor">@android:color/black</item>
        <item name="android:background">#FFFFFF</item>
        <item name="android:singleLine">true</item>
    </style>

    <style name="ButtonBar">
        <item name="android:paddingLeft">2dp</item>
        <item name="android:paddingTop">5dp</item>
        <item name="android:paddingRight">2dp</item>
        <item name="android:paddingBottom">0dp</item>
        <item name="android:background">@android:drawable/bottom_bar</item>
    </style>

    <style name="ButtonBarButton" />

    <style name="Widget"></style>

    <style name="Widget.ATableView.Section.TextLabel" parent="@style/Widget">
        <item name="android:includeFontPadding">false</item>
        <item name="android:shadowDy">2</item>
        <item name="android:shadowRadius">1</item>
    </style>

    <style name="Widget.ATableView.Section.Plain.TextLabel" parent="@style/Widget.ATableView.Section.TextLabel">
        <item name="android:textColor">@android:color/white</item>
        <item name="android:shadowColor">@color/atv_plain_section_header_text</item>
        <item name="android:singleLine">true</item>
        <item name="android:ellipsize">end</item>
    </style>

    <style name="Widget.ATableView.Section.Grouped.TextLabel" parent="@style/Widget.ATableView.Section.TextLabel">
        <item name="android:textColor">@color/atv_grouped_section_header_text</item>
        <item name="android:shadowColor">@android:color/white</item>
    </style>

    <style name="Widget.ATableView.Section.Plain.Header.TextLabel" parent="@style/Widget.ATableView.Section.Plain.TextLabel">
        <item name="android:textSize">@dimen/atv_plain_header_text_size</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Widget.ATableView.Section.Grouped.Header.TextLabel" parent="@style/Widget.ATableView.Section.Grouped.TextLabel">
        <item name="android:textSize">@dimen/atv_grouped_section_header_text_size</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="Widget.ATableView.Section.Grouped.Footer.TextLabel" parent="@style/Widget.ATableView.Section.Grouped.TextLabel">
        <item name="android:textSize">@dimen/atv_grouped_section_footer_text_size</item>
        <item name="android:gravity">center_horizontal</item>
    </style>

    <style name="Widget.ATableViewCell">
        <item name="android:gravity">center_vertical</item>
    </style>

    <style name="Widget.ATableViewCell.ImageView" parent="@style/Widget">
        <item name="android:adjustViewBounds">true</item>
        <item name="android:scaleType">centerInside</item>
    </style>

    <style name="Widget.ATableViewCell.TextLabel" parent="@style/Widget">
        <item name="android:textStyle">bold</item>
        <item name="android:maxLines">1</item>
        <item name="android:ellipsize">end</item>
        <item name="android:includeFontPadding">false</item>
    </style>

    <style name="Widget.ATableViewCell.DetailTextLabel" parent="@style/Widget.ATableViewCell.TextLabel"></style>

    <style name="RadioButton">
        <item name="android:textColor">@drawable/button_text_color</item>
        <item name="android:minHeight">33dp</item>
        <item name="android:minWidth">70dp</item>
        <item name="android:gravity">center</item>
        <item name="android:button">@null</item>
        <item name="android:paddingLeft">5dp</item>
        <item name="android:paddingRight">5dp</item>
    </style>

    <style name="Widget.ATableViewCell.TextLabel.Default" parent="@style/Widget.ATableViewCell.TextLabel">
        <item name="android:textColor">@color/atv_default_cell_text_selector</item>
        <item name="android:textSize">@dimen/atv_default_cell_text_size</item>
    </style>

    <style name="Widget.ATableViewCell.TextLabel.Subtitle" parent="@style/Widget.ATableViewCell.TextLabel">
        <item name="android:textColor">@color/atv_subtitle_cell_text_selector</item>
        <item name="android:textSize">@dimen/atv_subtitle_cell_text_size</item>
    </style>

    <style name="Widget.ATableViewCell.DetailTextLabel.Subtitle" parent="@style/Widget.ATableViewCell.DetailTextLabel">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/atv_subtitle_cell_detail_text_selector</item>
        <item name="android:textSize">@dimen/atv_subtitle_cell_detail_text_size</item>
    </style>

    <style name="Widget.ATableViewCell.TextLabel.Value1" parent="@style/Widget.ATableViewCell.TextLabel">
        <item name="android:textColor">@color/atv_value1_cell_title_text_selector</item>
        <item name="android:textSize">@dimen/atv_value1_cell_text_size</item>
    </style>

    <style name="Widget.ATableViewCell.DetailTextLabel.Value1" parent="@style/Widget.ATableViewCell.DetailTextLabel">
        <item name="android:textStyle">normal</item>
        <item name="android:textColor">@color/atv_value1_cell_detail_text_selector</item>
        <item name="android:textSize">@dimen/atv_value1_cell_detail_text_size</item>
    </style>

    <style name="Widget.ATableViewCell.TextLabel.Value2" parent="@style/Widget.ATableViewCell.TextLabel">
        <item name="android:gravity">right</item>
        <item name="android:textColor">@color/atv_value2_cell_title_text_selector</item>
        <item name="android:textSize">@dimen/atv_value2_cell_text_size</item>
    </style>

    <style name="Widget.ATableViewCell.DetailTextLabel.Value2" parent="@style/Widget.ATableViewCell.DetailTextLabel">
        <item name="android:textColor">@color/atv_value2_cell_detail_text_selector</item>
        <item name="android:textSize">@dimen/atv_value2_cell_details_text_size</item>
    </style>

    <style name="SpinnerItem" parent="@android:style/Widget.TextView.SpinnerItem">
        <item name="android:textColor">#00FF00</item>
    </style>

    <style name="SpinnerItem.DropDownItem" parent="@android:style/Widget.DropDownItem.Spinner">
        <item name="android:textColor">#FF0000</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
    </style>

    <style name="ScanTheme" parent="@android:style/Theme.Holo.Light">
        <item name="android:actionBarStyle">@style/MyActionBarTheme</item>
        <item name="android:titleTextStyle">@style/MyTheme.ActionBar.TitleTextStyle</item>
        <item name="homeAsUpIndicator">@drawable/rotate</item>
        <item name="android:homeAsUpIndicator">@drawable/rotate</item>
        <item name="logo">@android:color/transparent</item>
        <item name="android:logo">@android:color/transparent</item>
    </style>

    <style name="ScanThemeWithBack" parent="@android:style/Theme.Holo.Light">
        <item name="android:actionBarStyle">@style/MyActionBarTheme</item>
        <item name="android:titleTextStyle">@style/MyTheme.ActionBar.TitleTextStyle</item>
        <item name="titleTextStyle">@style/MyTheme.ActionBar.TitleTextStyle</item>
        <item name="buttonBarStyle">@style/ButtonBar</item>
        <item name="buttonBarButtonStyle">@style/ButtonBarButton</item>
        <item name="android:textColor">@color/colorTitle</item>
        <item name="android:textSize">@dimen/atv_value2_cell_text_size</item>
    </style>

    <style name="MyActionBarTheme" parent="@android:style/Widget.Holo.Light.ActionBar">
        <item name="android:background">@color/colorPrimaryDark</item>
    </style>

    <style name="MyTheme.ActionBar.TitleTextStyle" parent="@android:style/TextAppearance">
        <item name="android:textColor">@color/colorTitle</item>
        <item name="android:textSize">18sp</item>
    </style>

    <style name="ProgressTheme" parent="android:Theme.Holo.Dialog">
        <item name="android:alertDialogStyle">@style/CustomAlertDialogStyle</item>
        <item name="android:windowBackground">@color/opacity</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="CustomAlertDialogStyle">
        <item name="android:bottomBright">@color/transparent</item>
        <item name="android:bottomDark">@color/transparent</item>
        <item name="android:bottomMedium">@color/transparent</item>
        <item name="android:centerBright">@color/transparent</item>
        <item name="android:centerDark">@color/transparent</item>
        <item name="android:centerMedium">@color/transparent</item>
        <item name="android:fullBright">@color/transparent</item>
        <item name="android:fullDark">@color/transparent</item>
        <item name="android:topBright">@color/transparent</item>
        <item name="android:topDark">@color/transparent</item>
    </style>

    <style name="DialogTheme" parent="android:Theme.Dialog">
        <item name="android:windowBackground">@drawable/dialog_background</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="DialogTheme_MultiSelect" parent="android:Theme.Dialog">
        <item name="android:windowBackground">@drawable/dialog_background</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="TransparentToolbar" parent="@style/ThemeOverlay.AppCompat.Dark.ActionBar">
        <item name="android:windowActionBarOverlay">true</item>
        <!-- Support Library compability -->
        <item name="windowActionBarOverlay">true</item>
    </style>

    <style name="AppOverlayTheme" parent="@style/Theme.AppCompat.Light">
        <item name="windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowContentOverlay">@null</item>
    </style>

    <declare-styleable name="AutoFitTextureView">
        <attr name="withRatio" format="integer" />
        <attr name="heightRatio" format="integer" />
    </declare-styleable>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="TabButton">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">12sp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">match_parent</item>
        <item name="android:textColor">@color/comments_tab_normal</item>
        <item name="android:background">@color/transparent</item>
        <item name="android:gravity">center</item>
        <item name="android:button">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:layout_weight">0</item>
        <item name="android:minWidth">0dp</item>
    </style>
    <style name="CommentsTabButton" parent="TabButton">
        <item name="android:paddingLeft">@dimen/margin_20</item>
        <item name="android:paddingRight">@dimen/margin_20</item>
    </style>
    <style name="SchedulesTabButton" parent="TabButton">
        <item name="android:paddingLeft">@dimen/margin_10</item>
        <item name="android:paddingRight">@dimen/margin_10</item>
    </style>
    <style name="SchedulesHeaderTitleSmall">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/dark_gray</item>
    </style>
    <style name="SchedulesHeaderTitleLarge">
        <item name="android:textSize">15sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/dark_gray</item>
    </style>

    <style name="asset_info_title">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/dark_gray</item>
    </style>
    <style name="notice_category_name_tag">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">normal</item>
    </style>
</resources>
