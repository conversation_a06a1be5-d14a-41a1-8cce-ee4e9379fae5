<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- general -->
    <dimen name="atv_stroke_width">1dp</dimen>
    <dimen name="atv_grouped_stroke_radius">7dp</dimen>
    <dimen name="radio_button_conner_radius">5dp</dimen>
    <dimen name="radio_button_stroke_border">1dp</dimen>
    <!-- plain table header -->
    <dimen name="atv_plain_header_text_size">18sp</dimen>
    <dimen name="atv_plain_section_header_padding_left">12dp</dimen>
    <dimen name="atv_plain_section_header_padding_right">18dp</dimen>
    <dimen name="atv_plain_section_header_height">24px</dimen>

    <!-- grouped table header & footer -->
    <dimen name="atv_grouped_section_header_footer_padding_top_bottom">8dp</dimen>
    <dimen name="atv_grouped_section_header_footer_padding_left_right">20dp</dimen>
    <dimen name="atv_grouped_section_header_footer_min_height">10dp</dimen>

    <!-- grouped table header -->
    <dimen name="atv_grouped_section_header_text_size">20sp</dimen>
    <dimen name="atv_grouped_section_header_first_row_padding_top">16dp</dimen>
    <dimen name="atv_grouped_section_header_padding_top">6dp</dimen>

    <!-- grouped table footer -->
    <dimen name="atv_grouped_section_footer_text_size">18sp</dimen>
    <dimen name="atv_grouped_section_footer_last_row_padding_bottom">10dp</dimen>
    <dimen name="atv_grouped_section_footer_padding_bottom">6dp</dimen>

    <!-- grouped style cell -->
    <dimen name="atv_cell_grouped_margins">10dp</dimen>

    <!-- cell default height given in px, later converted into dps ! -->
    <dimen name="atv_cell_default_row_height">44px</dimen>

    <!-- margins between labels and cell borders, used also by imageView and accessoryView -->
    <dimen name="atv_cell_content_margin">10dp</dimen>

    <!-- marginRight for ATableViewCellAccessoryType.DisclosureButton accessoryView -->
    <dimen name="atv_cell_disclosure_button_margin_right">8dp</dimen>

    <!-- ATableViewCellStyle.Default -->
    <dimen name="atv_default_cell_text_size">20sp</dimen>

    <!-- ATableViewCellStyle.Subtitle -->
    <dimen name="atv_subtitle_cell_text_size">18sp</dimen>
    <dimen name="atv_subtitle_cell_detail_text_size">14sp</dimen>

    <!-- ATableViewCellStyle.Value1 -->
    <dimen name="atv_value1_cell_text_size">17sp</dimen>
    <dimen name="atv_value1_cell_detail_text_size">17sp</dimen>

    <!-- ATableViewCellStyle.Value2 -->
    <dimen name="atv_value2_cell_text_label_width">67dp</dimen>
    <dimen name="atv_value2_cell_text_size">12sp</dimen>
    <dimen name="atv_value2_cell_details_text_size">15sp</dimen>
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="border_width">5dp</dimen>
    <dimen name="margin_small">8dp</dimen>
    <dimen name="margin_smaller">6dp</dimen>
    <dimen name="margin_smallest">4dp</dimen>
    <dimen name="margin_divider_normal">1dp</dimen>
    <dimen name="margin_divider_large">2dp</dimen>
    <dimen name="listview_margin_default">15dp</dimen>
    <dimen name="margin_default">23dp</dimen>
    <dimen name="default_height">35dp</dimen>
    <dimen name="default_width">200dp</dimen>
    <dimen name="margin_16">16dp</dimen>
    <dimen name="margin_10">10dp</dimen>
    <dimen name="margin_24">24dp</dimen>
    <dimen name="margin_30">30dp</dimen>
    <dimen name="margin_80">80dp</dimen>
    <dimen name="btn_size_30">30dp</dimen>
    <dimen name="margin_20">20dp</dimen>
    <dimen name="focus_view_size">100dp</dimen>

    <!-- Text sizes -->
    <dimen name="text_h0">28sp</dimen>
    <dimen name="text_h1">24sp</dimen>
    <dimen name="text_h2">22sp</dimen>
    <dimen name="text_h3">20sp</dimen>
    <dimen name="text_h4">18sp</dimen>
    <dimen name="text_h5">16sp</dimen>
    <dimen name="text_normal">14sp</dimen>
    <dimen name="text_small">12sp</dimen>
    <dimen name="text_smallest">11sp</dimen>

    <!-- Search bar -->
    <dimen name="search_bar_height">52dp</dimen>
    <dimen name="fab_margin">16dp</dimen>

    <!-- Calendar View -->
    <dimen name="calendar_tile_height">34dp</dimen>

    <!-- Simple ToolTip Style -->
    <dimen name="simpletooltip_overlay_offset">10dp</dimen>
    <dimen name="simpletooltip_margin">10dp</dimen>
    <dimen name="margin_25">25dp</dimen>
    <dimen name="margin_small_mid">6dp</dimen>
    <dimen name="zero_height">0dp</dimen>
    <dimen name="default_button_height">40dp</dimen>

    <dimen name="toolbar_height">60dp</dimen>
    <dimen name="button_min_width">64dp</dimen>
    <dimen name="button_min_height">44dp</dimen>
    <dimen name="floor_plan_image_height">120dp</dimen>

    <dimen name="drop_pin_icon_width">15dp</dimen>
    <dimen name="drop_pin_icon_height">20dp</dimen>
    <dimen name="button_width">54dp</dimen>

    <!-- Spinner -->
    <dimen name="spinner_item_height">40dp</dimen>
    <dimen name="spinner_popup_height">350dp</dimen>
    <dimen name="loading_indicator_size">16dp</dimen>

</resources>