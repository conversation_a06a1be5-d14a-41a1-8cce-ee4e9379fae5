<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="FullscreenTheme" parent="android:Theme.Holo.Light.DarkActionBar">
        <item name="alertDialogTheme">@style/AppCompatAlertDialogStyle</item>
        <item name="android:actionBarStyle">@style/FullscreenActionBarStyle</item>
        <item name="android:windowActionBarOverlay">true</item>
        <item name="android:windowBackground">@null</item>
        <item name="buttonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="buttonBarButtonStyle">?android:attr/buttonBarButtonStyle</item>
        <item name="android:popupMenuStyle">@style/PopupMenuStyle</item>
        <item name="android:background">@null</item>
        <item name="android:actionBarTabStyle">@style/MyActionBarTabTextStyle</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:windowOptOutEdgeToEdgeEnforcement" tools:targetApi="35">true</item>
    </style>

    <style name="FullscreenActionBarStyle" parent="android:Widget.Holo.ActionBar">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:titleTextStyle">@style/FullscreenTheme.ActionBar.TitleTextStyle</item>

    </style>
    <!-- Popup Menu Background Color styles -->
    <style name="PopupMenuStyle"
        parent="@android:style/Widget.Holo.ListPopupWindow">
        <item name="android:popupBackground">@drawable/menu_dropdown_panel_example</item>
    </style>

    <style name="FullscreenTheme.ActionBar.TitleTextStyle" parent="@android:style/TextAppearance.Holo.Widget.ActionBar.Title">
        <item name="android:textColor">@android:color/white</item>
    </style>
</resources>