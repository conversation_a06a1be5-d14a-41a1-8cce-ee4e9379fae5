<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="match_parent" android:id="@+id/frag_photocontainer">
    <LinearLayout
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_weight="1"
        android:gravity="center_vertical"
        android:background="@android:color/black"
        android:layout_marginTop="?android:attr/actionBarSize"
        android:layout_width="match_parent">
        <FrameLayout
            android:id="@+id/camera"

            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_gravity="center_vertical"/>
    </LinearLayout>
    <RelativeLayout
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:layout_marginTop="?android:attr/actionBarSize"
        android:background="@android:color/black"
        android:layout_width="wrap_content">
        <TextView
            android:layout_height="70dp"
            android:layout_width="wrap_content"  android:layout_alignParentTop="true"
            android:textSize="20dp"
            android:layout_centerHorizontal="true"
            android:textColor="@android:color/white"  android:layout_marginTop="10dp" android:id="@+id/txt_PhotoCount" />

        <Button android:layout_height="70dp" android:layout_centerVertical="true"  android:layout_marginRight="20dp" android:layout_width="70dp" android:background="@drawable/btn_camera" android:id="@+id/btn_Capture" android:text="    "></Button>
        <ImageView
            android:layout_height="70dp"
            android:layout_width="70dp" android:layout_marginBottom="10dp" android:layout_alignParentBottom="true" android:id="@+id/imgview_Thumb" />

    </RelativeLayout>
</LinearLayout>