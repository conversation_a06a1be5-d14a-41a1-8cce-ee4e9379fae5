<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:id="@+id/view_head"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_10"
        android:layout_marginEnd="@dimen/margin_10">
    </LinearLayout>
    <TextView
        android:id="@+id/txt_Sign_Inst"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:textSize="12sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_10"
        android:layout_marginEnd="@dimen/margin_10" />

    <LinearLayout
        android:id="@+id/ll_Sign"
        android:layout_width="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:minHeight="75dp"
        android:minWidth="150dp"
        android:maxWidth="200dp"
        android:maxHeight="150dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal">
        <ImageView
            android:visibility="gone"
            android:background="@drawable/imageview_border"
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:minHeight="75dp"
            android:minWidth="150dp"
            android:maxWidth="200dp"
            android:maxHeight="150dp"
            android:id="@+id/iv_Sign">
        </ImageView>
        <RelativeLayout
            android:layout_width="match_parent"
            android:orientation="horizontal"
            android:minWidth="150dp"
            android:maxWidth="200dp"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true">
                <TextView
                    android:textSize="9sp"
                    android:layout_width="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_alignParentStart="true"
                    android:id="@+id/txt_Signee_Name"
                    android:layout_height="wrap_content"  />
                <TextView
                    android:textSize="9sp"
                    android:layout_alignParentBottom="true"
                    android:id="@+id/txt_Signee_Email"
                    android:layout_width="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_height="wrap_content" />
            </LinearLayout>

            <TextView
                android:id="@+id/txt_Signee_Date"
                android:textSize="9sp"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_alignParentEnd="true" />
        </RelativeLayout>
    </LinearLayout>

    <Button
        android:layout_margin="10dp"
        android:minWidth="180dp"
        android:layout_gravity="center_horizontal"
        android:layout_height="65dp"
        android:id="@+id/btn_Sign"
        android:background="@drawable/btn_dark_action"
        android:textColor="@color/colorPrimaryDark"
        android:layout_width="wrap_content"
        android:text="Sign" />
</LinearLayout>