<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal">
        <ImageView
            android:id="@+id/img_photo_view"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <ProgressBar
            android:id="@+id/indicator_view"
            android:visibility="gone"
            style="?android:attr/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminate="true" />
    </FrameLayout>

    <EditText
        android:id="@+id/edit_PhotoComments"
        android:layout_gravity="center_horizontal"
        android:background="@drawable/edittext_border"
        android:minLines="4"
        android:singleLine="false"
        android:lines="6"
        android:maxLines="10"
        android:layout_height="wrap_content"
        android:gravity="top"
        android:layout_marginTop="20dp"
        android:padding="10dp"
        android:textColor="@android:color/black"
        android:inputType="textMultiLine"
        android:imeOptions="actionDone"
        android:layout_width="match_parent"
        android:cursorVisible="true"
        android:textCursorDrawable="@null"
        />
</LinearLayout>