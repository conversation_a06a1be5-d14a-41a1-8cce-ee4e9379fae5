<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drag_layout"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_gravity=""
    android:minHeight="45dp">

    <ImageButton
        android:layout_height="35dp"
        android:layout_width="35dp"
        android:layout_marginLeft="@dimen/margin_10"
        android:padding="5dp"
        android:id="@+id/btn_DeselectComment"
        android:layout_gravity="center_vertical"
        android:src="@drawable/red_minus"
        android:background="@color/transparent"
        />

    <LinearLayout
        android:layout_weight="1"
        android:padding="2dp"
        android:orientation="vertical"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_10"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft ="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_20"
        >

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:gravity="top"
            android:textStyle="bold"
            android:layout_marginBottom="@dimen/margin_10"
            />

        <TextView
            android:id="@+id/tv_body"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:singleLine="false"
            android:textSize="14sp"
            android:textColor="@android:color/black"
            android:gravity="top"
            />
    </LinearLayout>

    <ImageView
        android:id="@+id/order_handler"
        android:padding="7dp"
        android:layout_width="40dp"
        android:layout_height="30dp"
        android:layout_marginRight="@dimen/margin_10"
        android:layout_gravity="center_vertical"
        android:scaleType="fitXY"
        android:src="@drawable/ic_hamburger"
        tools:ignore="ContentDescription"
        />

</LinearLayout>
