<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/colorPrimary"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.if_VideoClips">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:layout_marginTop="?android:actionBarSize"
        android:orientation="vertical">

        <include
            android:id="@+id/view_main_clip"
            layout="@layout/cell_video_clip"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_10"
            android:layout_marginBottom="@dimen/margin_10"
            android:layout_marginHorizontal="@dimen/margin_20" />

        <TextView
            android:textColor="@color/dark_gray"
            android:textSize="17sp"
            android:textStyle="bold"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/margin_20"
            android:layout_marginVertical="@dimen/margin_10"
            android:text="@string/pending_video_clips"
        />

        <GridView
            android:id="@+id/gv_video_clips"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:verticalSpacing="20dp"
            android:horizontalSpacing="20dp"
            android:paddingHorizontal="@dimen/margin_20"
            android:layout_marginTop="@dimen/margin_10"
            android:layout_marginBottom="@dimen/margin_10"
            android:stretchMode="columnWidth"
        />
    </LinearLayout>

</FrameLayout>