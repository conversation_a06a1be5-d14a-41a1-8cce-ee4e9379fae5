<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:baselineAligned="false">
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginTop="@dimen/margin_10"
        android:layout_marginBottom="@dimen/margin_10"
        >
        <TextView
            android:id="@+id/txt_InsDate"
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/dark_gray"
            android:textStyle="normal"
            android:textSize="14sp"
            android:layout_marginBottom="6dp"
            />
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/comments_tab_selected"
            />
    </LinearLayout>
    <LinearLayout
        android:orientation="vertical"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_20"
        android:layout_marginEnd="@dimen/margin_20"
        android:layout_marginTop="@dimen/margin_10"
        android:layout_marginBottom="@dimen/margin_10"
        >
        <TextView
            android:id="@+id/txt_sInsTitle"
            android:textColor="@color/dark_gray"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textStyle="italic"
            android:textSize="14sp"
            android:layout_marginBottom="6dp"
            />
        <TextView
            android:id="@+id/txt_sAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/dark_gray"
            android:textStyle="bold"
            android:textSize="14sp"
            android:layout_marginBottom="6dp"
            />
        <TextView
            android:id="@+id/txt_sRef"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="@color/comments_tab_selected"
            android:textStyle="bold"
            android:textSize="14sp"
            />
    </LinearLayout>
    <FrameLayout
        android:id="@+id/layout_cell_accessory"
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginRight="@dimen/margin_20"
        android:layout_marginEnd="@dimen/margin_20"
        android:layout_marginTop="@dimen/margin_10"
        android:layout_marginBottom="@dimen/margin_10"
        >
        <ImageView
            android:id="@+id/cell_accessory_icon"
            android:layout_height="20dp"
            android:layout_width="20dp"
            android:layout_gravity="center_vertical"
            />
    </FrameLayout>
</LinearLayout>