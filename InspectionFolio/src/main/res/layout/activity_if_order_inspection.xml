<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:orientation="vertical"
    tools:context=".activitynew.if_OrderInspection">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="?android:attr/actionBarSize"
        tools:ignore="UselessParent"
        android:orientation="vertical"
        android:background="@android:color/white">
        <include
            android:id="@+id/prompt_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            layout="@layout/view_prompt"/>


    </LinearLayout>
    <com.woxthebox.draglistview.DragListView
        android:id="@+id/order_inspection_listview"
        android:descendantFocusability="afterDescendants"
        android:windowSoftInputMode="adjustPan"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:background="@android:color/white"
        android:layout_height="0dp" android:layout_weight="1"/>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal|bottom"
        android:background="#f8f6f6">

        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:text=""
            android:id="@+id/txt_timer"
            android:background="#00ffffff"
            android:layout_gravity="center_horizontal"
            android:textColor="#10518f"
            android:gravity="center_vertical|center_horizontal" />
    </LinearLayout>

</LinearLayout>
