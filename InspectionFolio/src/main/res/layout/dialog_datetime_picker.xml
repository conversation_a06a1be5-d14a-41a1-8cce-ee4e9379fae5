<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/dialog_date_time"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TabHost
        android:id="@+id/tabHost"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <TabWidget
                android:id="@android:id/tabs"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />

            <FrameLayout
                android:id="@android:id/tabcontent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
            >

                <LinearLayout
                    android:id="@+id/tab_date_picker"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <DatePicker
                        android:id="@+id/datePicker"
                        android:layout_gravity="center"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                    />
                </LinearLayout>

                <LinearLayout
                    android:id="@+id/tab_time_picker"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <TimePicker
                        android:id="@+id/timePicker"
                        android:timePickerMode="spinner"
                        android:layout_gravity="center"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                    />
                </LinearLayout>
            </FrameLayout>
        </LinearLayout>
    </TabHost>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <Button
            android:id="@+id/cancel_button"
            android:text="@string/menu_cancel"
            android:textStyle="bold"
            android:textColor="@color/light_gray"
            android:textAllCaps="false"
            android:background="@color/transparent"
            android:layout_width="0dp"
            android:layout_height="@dimen/default_button_height"
            android:layout_weight="1"
        />

        <Button
            android:id="@+id/clean_button"
            android:text="@string/menu_clear"
            android:textStyle="bold"
            android:textColor="@color/light_gray"
            android:textAllCaps="false"
            android:background="@color/transparent"
            android:layout_width="0dp"
            android:layout_height="@dimen/default_button_height"
            android:layout_weight="1"
        />

        <Button
            android:id="@+id/done_button"
            android:text="@string/menu_done"
            android:textStyle="bold"
            android:textColor="@color/colorPrimary"
            android:textAllCaps="false"
            android:background="@color/transparent"
            android:layout_width="0dp"
            android:layout_height="@dimen/default_button_height"
            android:layout_weight="1"
        />

    </androidx.appcompat.widget.LinearLayoutCompat>
</LinearLayout>