<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent" android:gravity="center_horizontal"
    android:layout_height="match_parent">
    <LinearLayout
        android:id="@+id/view_head"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginRight="10dp">
    </LinearLayout>
    <!--<TextView
        android:layout_height="wrap_content" android:layout_margin="10dp" android:textSize="14dp" android:textColor="@android:color/black"
        android:layout_width="match_parent" android:gravity="center_horizontal" android:id="@+id/txt_Sign" /> -->


    <TextView
        android:layout_height="wrap_content" android:layout_margin="10dp" android:textSize="12dp" android:textColor="@android:color/darker_gray"
        android:textStyle="italic"
        android:layout_width="match_parent" android:gravity="center_horizontal" android:id="@+id/txt_Sign_Inst" />

    <LinearLayout  android:id="@+id/ll_Sign"
        android:layout_width="wrap_content" android:gravity="center_horizontal" android:orientation="vertical"
        android:minHeight="75dp" android:minWidth="150dp" android:maxWidth="200dp" android:maxHeight="150dp"
        android:layout_height="wrap_content">
        <ImageView android:visibility="gone" android:background="@drawable/imageview_border" android:layout_height="wrap_content"
            android:layout_width="wrap_content" android:minHeight="75dp" android:minWidth="150dp"
            android:maxWidth="200dp" android:maxHeight="150dp" android:id="@+id/iv_Sign">

        </ImageView>
        <RelativeLayout
            android:layout_width="match_parent" android:orientation="horizontal"
           android:minWidth="150dp" android:maxWidth="200dp"

            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_width="wrap_content" android:layout_alignParentLeft="true"
                android:layout_height="wrap_content" android:orientation="vertical">
                <TextView android:textSize="9dp"
                android:layout_width="wrap_content" android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true" android:id="@+id/txt_Signee_Name"
                android:layout_height="wrap_content"  />
                <TextView android:textSize="9dp"
                    android:layout_alignParentBottom="true" android:id="@+id/txt_Signee_Email"
                    android:layout_width="wrap_content" android:layout_alignParentLeft="true"
                    android:layout_height="wrap_content" />
            </LinearLayout>

            <TextView android:id="@+id/txt_Signee_Date" android:textSize="9dp"
                android:layout_width="wrap_content" android:layout_alignParentRight="true"
                android:layout_height="wrap_content"  />
        </RelativeLayout>
    </LinearLayout>

        <Button android:layout_margin="10dp" android:minWidth="150dp"
            android:layout_height="wrap_content" android:id="@+id/btn_Sign" android:background="@drawable/btn_green_action" android:textColor="#90BC24"
            android:layout_width="wrap_content" android:text="Sign" />
</LinearLayout>