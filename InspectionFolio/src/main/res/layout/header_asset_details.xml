<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent" android:layout_height="match_parent"
    android:gravity="center_vertical"
    android:orientation="vertical">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="@dimen/margin_small"
        android:layout_marginBottom="@dimen/margin_small"
        android:layout_marginTop="@dimen/margin_small"
        android:id="@+id/header_asset_details_tv_title"
        android:textColor="@color/atv_grouped_section_header_text"
        android:textSize="18sp"
        android:textStyle="bold"/>
    <LinearLayout
        android:id="@+id/header_asset_details_view"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/gray_color"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="@dimen/margin_small"
        android:layout_marginBottom="@dimen/margin_small"
        android:orientation="horizontal" />
</LinearLayout>