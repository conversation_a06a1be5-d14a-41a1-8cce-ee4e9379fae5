<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

    <ImageButton
        android:layout_height="50dp"
        android:layout_width="50dp"
        android:id="@+id/btn_Delete"
        android:layout_gravity="center_vertical"
        android:src="@drawable/icon_del_ins_room"
        android:background="@color/transparent"
        />

    <EditText
        android:id="@+id/edit_Area_Name"
        android:padding="10dp"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@drawable/edittext_border"
        android:inputType="text"
        android:layout_weight="1"
        android:minHeight="@dimen/margin_30"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:layout_marginLeft="@dimen/margin_10"
        android:layout_marginStart="@dimen/margin_10"
        />

    <ImageView
        android:id="@+id/order_handler"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:scaleType="fitXY"
        android:src="@drawable/icon_sort_ins_room"
        android:layout_marginRight="15dp"
        android:layout_marginEnd="15dp"
        android:layout_marginLeft="@dimen/margin_10"
        android:layout_marginStart="@dimen/margin_10" />
</LinearLayout>