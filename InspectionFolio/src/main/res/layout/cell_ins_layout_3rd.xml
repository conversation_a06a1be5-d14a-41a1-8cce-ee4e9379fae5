<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_room_name"
        android:layout_width="0dp"
        android:textSize="18sp"
        android:textColor="@android:color/black"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        />
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="end"
        android:gravity="center_vertical"
        >
        <ImageButton
            android:id="@+id/btn_room_decrease"
            android:src="@drawable/btn_room_decrease"
            android:layout_width="40dp"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            />
        <TextView
            android:id="@+id/tx_room_num"
            android:textStyle="bold"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:layout_width="26dp"
            android:layout_height="26dp"
            android:gravity="center"
            android:background="@drawable/bg_round_room_num"
            android:layout_marginLeft="@dimen/margin_10"
            android:layout_marginStart="@dimen/margin_10"
            android:layout_marginRight="@dimen/margin_10"
            android:layout_marginEnd="@dimen/margin_10"
            />
        <ImageButton
            android:id="@+id/btn_room_increase"
            android:src="@drawable/btn_room_increase"
            android:layout_width="40dp"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            />
    </LinearLayout>
</LinearLayout>