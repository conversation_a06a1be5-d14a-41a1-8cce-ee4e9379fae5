<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:paddingLeft="58dp"
    android:paddingBottom="4dp"
    android:paddingTop="4dp">
    <LinearLayout
        android:id="@+id/cell_ins_edit_nothing_layer"
        android:layout_width="244dp"
        android:layout_height="28dp"
        android:background="@drawable/bg_edittext">
        <TextView
            android:id="@+id/cell_ins_edit_nothing_tv_control"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:paddingLeft="4dp"
            android:textSize="14sp"
            android:gravity="center_vertical"
            android:text="Control 3"/>
        <LinearLayout
            android:layout_width="1dp"
            android:layout_height="match_parent"
            android:background="@color/border_color"/>
        <ImageView
            android:layout_gravity="center_vertical"
            android:layout_width="30dp"
            android:layout_height="20dp"
            android:paddingLeft="5dp"
            android:paddingRight="5dp"
            android:layout_marginRight="2dp"
            android:src="@drawable/expand"/>
    </LinearLayout>
</LinearLayout>