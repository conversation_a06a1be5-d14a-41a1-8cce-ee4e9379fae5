<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@color/colorPrimary"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <EditText
        android:id="@+id/search_projects"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginLeft="@dimen/margin_16"
        android:layout_marginRight="@dimen/margin_16"
        android:layout_marginTop="@dimen/margin_small"
        android:layout_marginBottom="@dimen/margin_small"
        android:hint="@string/search"
        android:paddingStart="@dimen/margin_16"
        android:background="@drawable/bg_search_bar"
        android:textSize="16sp"
        style="@style/EditText"
        android:imeOptions="actionSearch"
        android:drawableStart="@drawable/icn_search"
        android:drawablePadding="8dp"
        android:inputType="textVisiblePassword"/>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/view_swipe_refresh"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <com.snapinspect.snapinspect3.views.EmptyDataView
            android:id="@+id/view_empty_data"
            app:viewType="projects"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <LinearLayout
        android:id="@+id/projects_container"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            android:orientation="horizontal"
            android:background="@android:color/white"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:text="@string/title_project_name"
                android:textStyle="bold"
                android:textSize="@dimen/text_small"
                android:textColor="@color/grey"
                android:layout_marginStart="@dimen/margin_16"
                android:layout_marginVertical="@dimen/margin_10"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <View
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="0dp"/>

            <TextView
                android:text="@string/title_project_completion_progress"
                android:minWidth="@dimen/button_min_width"
                android:textStyle="bold"
                android:textSize="@dimen/text_small"
                android:textColor="@color/grey"
                android:layout_marginEnd="@dimen/margin_16"
                android:layout_marginVertical="@dimen/margin_10"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="@android:color/white"
            android:paddingBottom="58dp">
            <com.softw4re.views.InfiniteListView
                android:id="@+id/lv_Projects"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/white"
                app:dividerVisible="false" />
        </RelativeLayout>
    </LinearLayout>
</LinearLayout>