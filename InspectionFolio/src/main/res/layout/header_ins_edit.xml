<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="45sp"
        android:gravity="center_vertical"
        android:layout_marginLeft="@dimen/margin_default"
        android:layout_marginRight="8dp">
        <TextView
            android:id="@+id/header_ins_edit_tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textSize="17sp"
            android:fontFamily="sans-serif-light"
            android:textColor="@color/atv_default_cell_text"
            android:text="MSEL"/>
        <LinearLayout
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:gravity="center"
            android:id="@+id/header_ins_edit_btn_menu">
            <ImageView
                android:id="@+id/cell_ins_edit_iv_menu"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/menu"
                android:focusable="false"
                android:focusableInTouchMode="false"
                tools:ignore="ContentDescription" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>