<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:id="@+id/framelayout_existasset"
    tools:context=".activitynew.if_existasset">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?android:attr/actionBarSize"
        tools:ignore="UselessParent"
        android:orientation="vertical">
        <include
            android:id="@+id/prompt_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            layout="@layout/view_prompt"/>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/if_exist_asset_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >
            <ExpandableListView
                android:background="@color/intercom_white"
                android:groupIndicator="@null"
                android:id="@+id/if_exist_asset_list_item"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:transcriptMode="disabled"
                android:childIndicatorRight="14.5sp"
                android:childDivider="@android:color/transparent"
                android:dividerHeight="0dp"/>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </LinearLayout>


</FrameLayout>
