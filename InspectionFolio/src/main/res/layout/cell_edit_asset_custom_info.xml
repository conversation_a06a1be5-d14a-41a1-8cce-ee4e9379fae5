<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:orientation="vertical"
    android:layout_height="match_parent">
    <TextView
        android:id="@+id/cell_asset_custom_info_tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/margin_16"
        android:layout_marginRight="@dimen/margin_16"
        android:layout_marginTop="@dimen/margin_smallest"
        android:layout_marginBottom="@dimen/margin_smallest"/>

    <EditText
        android:id="@+id/cell_asset_custom_info_et_value"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:inputType="textCapSentences|textMultiLine"
        android:layout_marginLeft="@dimen/margin_16"
        android:layout_marginRight="@dimen/margin_16"
        android:layout_marginBottom="@dimen/margin_smallest"
        android:padding="@dimen/margin_small"
        android:background="@drawable/bg_edit_text"
        android:maxLines="4"/>
</LinearLayout>