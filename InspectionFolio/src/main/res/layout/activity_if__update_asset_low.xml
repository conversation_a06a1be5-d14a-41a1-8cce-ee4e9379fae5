<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#1660AB"
    tools:context=".activity.if_UpdateAsset">



    <LinearLayout
        android:layout_marginTop="?android:attr/actionBarSize"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@android:color/white"
        android:layout_width="match_parent" android:orientation="vertical">
        <ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginLeft="@dimen/activity_vertical_margin"
                android:layout_marginRight="@dimen/activity_vertical_margin">
        <LinearLayout
            android:layout_height="wrap_content" android:layout_marginTop="10dp"
            android:layout_width="match_parent">
            <TextView
                android:layout_height="50dp"
                android:layout_width="match_parent"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:textSize="20sp"
                android:id="@+id/tv_UpdateAsset_Title"
                android:text="Update Asset"/>
        </LinearLayout>
        <LinearLayout
            android:layout_marginLeft="@dimen/margin_smallest"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@android:color/white"
            android:layout_width="match_parent">
            <LinearLayout android:layout_marginTop="@dimen/margin_smallest"
                android:layout_width="match_parent"
                android:orientation="vertical"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="70dp"
                    android:gravity="center_vertical"
                    android:layout_height="match_parent"
                    android:text="Address 1"/>
                <EditText
                    android:layout_height="wrap_content"
                    android:layout_width="match_parent"
                    android:layout_marginTop="@dimen/margin_smallest"
                    android:layout_marginBottom="@dimen/margin_smallest"
                    android:background="@android:color/transparent"
                    android:hint="@string/AddressLine1"
                    android:id="@+id/updateAsset_et_address1"
                    android:textColor="@android:color/black"

                    android:layout_gravity="center_horizontal|top" />
                <LinearLayout
                    android:id="@+id/updateAsset_line_address1"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/black"
                    android:orientation="horizontal" />
                <TextView
                    android:id="@+id/updateAsset_alert_address1"
                    android:layout_width="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/margin_smallest"
                    android:textColor="@color/pink_color"
                    android:visibility="invisible"/>
            </LinearLayout>
            <LinearLayout android:layout_marginTop="@dimen/margin_smallest"
                android:layout_width="match_parent" android:orientation="vertical"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="70dp"  android:gravity="center_vertical"
                    android:layout_height="wrap_content" android:text="Address 2"/>
                <EditText
                    android:layout_height="wrap_content"
                    android:layout_width="match_parent"
                    android:background="@android:color/transparent"
                    android:hint="@string/AddressLine2"

                    android:textColor="@android:color/black"
                    android:id="@+id/updateAsset_et_address2"
                    android:layout_marginTop="@dimen/margin_smallest"
                    android:layout_marginBottom="@dimen/margin_smallest"
                    android:layout_gravity="center_horizontal|top" />
                <LinearLayout
                    android:id="@+id/updateAsset_line_address2"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/black"
                    android:orientation="horizontal" />
                <TextView
                    android:id="@+id/updateAsset_alert_address2"
                    android:layout_width="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/margin_smallest"
                    android:textColor="@color/pink_color"
                    android:visibility="invisible"/>
            </LinearLayout>
            <LinearLayout android:layout_marginTop="@dimen/margin_smallest"
                android:layout_width="match_parent" android:orientation="vertical"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="70dp"  android:gravity="center_vertical"
                    android:layout_height="wrap_content" android:text="Key"/>
                <EditText

                    android:layout_height="wrap_content"
                    android:layout_width="match_parent"
                    android:background="@android:color/transparent"
                    android:layout_marginTop="@dimen/margin_smallest"
                    android:layout_marginBottom="@dimen/margin_smallest"
                    android:textColor="@android:color/black"
                    android:hint="Key"
                    android:id="@+id/updateAsset_et_key"
                    android:layout_gravity="center_horizontal|top" />
                <LinearLayout
                    android:id="@+id/updateAsset_line_key"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/black"
                    android:orientation="horizontal" />
                <TextView
                    android:id="@+id/updateAsset_alert_key"
                    android:layout_width="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/margin_smallest"
                    android:textColor="@color/pink_color"
                    android:visibility="invisible"/>
            </LinearLayout>
            <LinearLayout  android:layout_marginTop="@dimen/margin_smallest"
                android:layout_width="match_parent" android:orientation="vertical"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="70dp"  android:gravity="center_vertical"
                    android:layout_height="wrap_content" android:text="Alarm"/>
                <EditText

                    android:layout_height="wrap_content"
                    android:layout_width="match_parent"
                    android:background="@android:color/transparent"
                    android:layout_marginTop="@dimen/margin_smallest"
                    android:layout_marginBottom="@dimen/margin_smallest"
                    android:hint="Alarm"
                    android:id="@+id/updateAsset_et_alarm"
                    android:textColor="@android:color/black"
                    android:layout_gravity="center_horizontal|top" />
                <LinearLayout
                    android:id="@+id/updateAsset_line_alarm"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/black"
                    android:orientation="horizontal" />
                <TextView
                    android:id="@+id/updateAsset_alert_alarm"
                    android:layout_width="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/margin_smallest"
                    android:textColor="@color/pink_color"
                    android:visibility="invisible"/>
            </LinearLayout>
            <LinearLayout android:layout_marginTop="@dimen/margin_smallest"
                android:layout_width="match_parent" android:orientation="vertical"
                android:layout_height="wrap_content">
                <TextView
                    android:layout_width="70dp"  android:gravity="center_vertical"
                    android:layout_height="wrap_content" android:text="Due"/>
                <EditText
                    android:layout_height="wrap_content"
                    android:layout_width="match_parent"
                    android:background="@android:color/transparent"
                    android:hint="Inspection Due"
                    android:id="@+id/edit_UpdateAsset_Due"
                    android:layout_marginTop="@dimen/margin_smallest"
                    android:layout_marginBottom="@dimen/margin_smallest"
                    android:textColor="@android:color/black"
                    android:layout_gravity="center_horizontal|top" />
                <LinearLayout
                    android:id="@+id/updateAsset_line_due"
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/black"
                    android:orientation="horizontal" />
                <TextView
                    android:id="@+id/updateAsset_alert_due"
                    android:layout_width="wrap_content"
                    android:gravity="center_vertical"
                    android:layout_height="match_parent"
                    android:layout_marginTop="@dimen/margin_smallest"
                    android:textColor="@color/pink_color"
                    android:visibility="invisible"/>
            </LinearLayout>
        </LinearLayout>

        </LinearLayout>
        </ScrollView>
    </LinearLayout>
    <LinearLayout
        android:layout_height="wrap_content"
        android:layout_width="match_parent"
        android:orientation="vertical">
        <Button android:id="@+id/btn_UpdateAsset"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:textSize="20dp"
            android:textColor="@android:color/white"
            android:background="@color/gray_color"
            android:text="Save"
            android:layout_gravity="center">

        </Button>
    </LinearLayout>
</LinearLayout>
