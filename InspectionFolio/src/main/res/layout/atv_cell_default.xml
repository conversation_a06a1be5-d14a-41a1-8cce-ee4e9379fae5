<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android" >

    <FrameLayout
        android:id="@+id/backgroundView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <FrameLayout
        android:id="@+id/contentView"
        android:layout_width="match_parent"
        android:layout_height="match_parent" android:background="@drawable/atable_bottomborder">

        <com.nakardo.atableview.internal.ATableViewCellContainerView
            android:id="@+id/containerView"
            style="@style/Widget.ATableViewCell"
            android:layout_width="match_parent"
            android:layout_height="match_parent" >

            <ImageView
                android:id="@+id/imageView"
                style="@style/Widget.ATableViewCell.ImageView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <com.nakardo.atableview.uikit.UILabel
                android:id="@+id/textLabel"
                style="@style/Widget.ATableViewCell.TextLabel.Default"
                android:layout_width="0dip"
                android:layout_height="wrap_content"
                android:layout_marginLeft="@dimen/atv_cell_content_margin"
                android:layout_marginRight="@dimen/atv_cell_content_margin"
                android:layout_weight="1" />
        </com.nakardo.atableview.internal.ATableViewCellContainerView>
    </FrameLayout>

</merge>