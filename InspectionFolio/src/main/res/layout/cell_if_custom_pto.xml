<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/margin_20"
    android:paddingVertical="@dimen/margin_10"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_title"
        style="@style/asset_info_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>
    <Button
        android:id="@+id/btn_view_file"
        android:background="@color/transparent"
        android:textSize="16sp"
        android:textColor="@color/btn_text_blue_color"
        android:layout_gravity="center_vertical"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"/>

</LinearLayout>