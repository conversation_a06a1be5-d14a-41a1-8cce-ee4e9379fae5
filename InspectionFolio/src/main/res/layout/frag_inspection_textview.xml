<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="0dp" android:id="@+id/layout_Inspection_Action"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="@dimen/margin_smallest"
        android:layout_marginLeft="@dimen/margin_default">

        <ImageView
            android:id="@+id/iv_inspect_mark"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:src="@drawable/icon_house"
            android:layout_gravity="center_vertical"
            android:layout_marginRight="@dimen/margin_16"
            android:visibility="gone"/>
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">
            <TextView xmlns:android="http://schemas.android.com/apk/res/android"
                android:id="@+id/txt_InsAddress"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@android:color/black"
                android:textSize="14sp"
                android:gravity="center_vertical"
                />
            <TextView
                android:id="@+id/txt_Asset_Ref"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#447aff"
                android:textStyle="italic"
                android:minHeight="1dp"
                android:textSize="13sp"/>
            <TextView xmlns:android="http://schemas.android.com/apk/res/android"
                android:id="@+id/txt_InsInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#959595"
                android:textStyle="italic"
                android:textSize="13sp"
                android:gravity="center_vertical"
                />
            <TextView
                android:paddingLeft="@dimen/margin_small"
                android:layout_marginTop="@dimen/margin_smallest"
                android:paddingRight="@dimen/margin_small"
                android:id="@+id/txt_InsTag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@android:color/white"/>
        </LinearLayout>
    </LinearLayout>

    <RelativeLayout
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginTop="20dp"
        android:layout_marginBottom="@dimen/margin_smallest"
        android:layout_marginRight="8dp">
        <Button android:layout_width="match_parent" android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:textColor="@color/intercom_inbox_count_background" android:id="@+id/btn_Inspection_Action"
            android:background="@null" android:text="Upload"
            android:visibility="gone"/>

        <LinearLayout
            android:id="@+id/btn_option"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_centerInParent="true">
            <ImageView
                android:id="@+id/button_image"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:src="@drawable/ic_more"
                android:layout_gravity="center_vertical"/>
        </LinearLayout>
        <com.snapinspect.snapinspect3.views.CircularTextView
            android:id="@+id/btn_uploaded_inspection"
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:gravity="center"
            android:visibility="gone"
            android:layout_centerInParent="true"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="3dp"
            android:layout_centerVertical="true"
            android:layout_marginRight="3dp"/>
        </RelativeLayout>


</LinearLayout>