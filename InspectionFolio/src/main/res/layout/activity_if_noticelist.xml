<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_NoticeList">

    <LinearLayout
        android:layout_marginTop="?android:actionBarSize"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@android:color/white">
            <TextView
                android:id="@+id/txt_AssetAddress"
                android:textColor="@color/light_gray"
                android:textSize="17sp"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_20"
                android:layout_marginTop="@dimen/margin_10"
                />
            <TextView
                android:id="@+id/txt_Asset_Ref"
                android:textColor="@color/colorPrimary"
                android:textSize="15sp"
                android:textStyle="bold"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/margin_20"
                android:layout_marginBottom="@dimen/margin_10"
                />
        </LinearLayout>
        <ListView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/lv_noticelist"
            android:dividerHeight="0dp"
            android:divider="@color/transparent"
            android:background="@android:color/white">
        </ListView>
    </LinearLayout>

</FrameLayout>
