<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android" android:layout_width="match_parent"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/cell_tv_title"
            android:layout_marginTop="@dimen/margin_smallest"
            android:layout_marginBottom="@dimen/margin_smallest"
            android:layout_marginLeft="@dimen/margin_20"
            android:layout_marginRight="@dimen/margin_smallest"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:textColor="@android:color/black"/>
        <ImageView
            android:id="@+id/cell_iv_edit"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_edit"
            android:layout_marginRight="@dimen/margin_16"
            android:padding="4dp"
            android:layout_gravity="center_vertical"/>
    </LinearLayout>


</LinearLayout>