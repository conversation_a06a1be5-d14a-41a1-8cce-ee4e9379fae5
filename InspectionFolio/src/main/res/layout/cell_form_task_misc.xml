<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:background="@android:color/white"
    android:layout_width="match_parent"
    android:layout_height="120dp">

    <LinearLayout
        android:id="@+id/layout_dueDate"
        android:orientation="vertical"
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginVertical="@dimen/margin_16"
        android:layout_marginStart="@dimen/margin_25">
        <TextView
            android:id="@+id/txt_dueTitle"
            android:text="@string/title_due_date_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            style="@style/asset_info_title"
            android:paddingTop="@dimen/margin_10"
            android:textColor="@color/placeholder_color"
            />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            tools:ignore="NestedWeights">
            <ImageView
                android:src="@drawable/calendar_icon"
                android:layout_gravity="center_vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <TextView
                android:id="@+id/txt_dueDate"
                android:textColor="@color/task_item_text"
                android:gravity="center_vertical"
                android:paddingHorizontal="@dimen/margin_smallest"
                android:layout_gravity="center_vertical"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_assignTask"
        android:orientation="vertical"
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginVertical="@dimen/margin_16">
        <TextView
            android:id="@+id/txt_assignTitle"
            android:text="@string/title_assign_task"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAlignment="center"
            style="@style/asset_info_title"
            android:paddingTop="@dimen/margin_10"
            android:textColor="@color/placeholder_color"
            />
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            tools:ignore="NestedWeights">
            <ImageView
                android:id="@+id/icon_assigned_user"
                android:src="@drawable/task_assigned_to_icon"
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <com.snapinspect.snapinspect3.views.RoundTextView
                android:id="@+id/txt_assign_to"
                android:visibility="gone"
                android:gravity="center"
                android:layout_gravity="center"
                android:layout_width="44dp"
                android:layout_height="44dp"
                android:textAllCaps="true"
                android:textColor="@android:color/white"
                android:textSize="15sp"
                android:textStyle="bold"
                />
        </FrameLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/layout_taskPriority"
        android:orientation="vertical"
        android:layout_weight="1"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginVertical="@dimen/margin_16"
        android:layout_marginEnd="@dimen/margin_25">
        <TextView
            android:id="@+id/txt_PriorityTitle"
            android:text="@string/title_task_priority"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textAlignment="center"
            style="@style/asset_info_title"
            android:paddingTop="@dimen/margin_10"
            android:textColor="@color/placeholder_color"
            />
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            tools:ignore="NestedWeights">
            <com.snapinspect.snapinspect3.views.RoundTextView
                android:id="@+id/txt_PriorityName"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:gravity="center"
                android:layout_gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/margin_24"
                android:paddingHorizontal="@dimen/margin_16"
                />
        </FrameLayout>
    </LinearLayout>

</LinearLayout>