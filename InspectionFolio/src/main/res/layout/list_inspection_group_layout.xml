<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:background="#F2F2F2">
    <RelativeLayout
        android:layout_marginLeft="30dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">
        <LinearLayout
            android:layout_width="30dp"
            android:layout_height="40dp"
            android:gravity="center"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:visibility="gone"
            android:id="@+id/list_full_inspection_group_layout_btn_info">
            <ImageView
                android:layout_width="15dp"
                android:layout_height="15dp"
                android:layout_gravity="center"
                android:src="@drawable/icon_info"
                android:focusable="false"
                android:focusableInTouchMode="false"
                tools:ignore="ContentDescription" />
        </LinearLayout>
        <TextView
            android:id="@+id/list_full_inspection_group_layout_title"
            android:layout_width="0dp"
            android:layout_height="55dp"
            android:layout_weight="1"
            android:textSize="17sp"
            android:layout_marginLeft="0dp"
            android:layout_alignParentLeft="true"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@+id/expend_view"
            android:textColor="@color/atv_default_cell_text"
            android:gravity="center_vertical"/>
        <LinearLayout
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:gravity="center"
            android:id="@+id/expend_view"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_gravity="center_vertical">
            <ImageView
                android:id="@+id/full_inspection_group_layout_expand_iv"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/chevron_up"
                android:focusable="false"
                android:focusableInTouchMode="false"
                tools:ignore="ContentDescription" />
        </LinearLayout>
    </RelativeLayout>

</LinearLayout>