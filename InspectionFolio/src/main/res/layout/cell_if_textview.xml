<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/margin_20"
    android:paddingVertical="@dimen/margin_10"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/cell_tv_title"
        android:textSize="16sp"
        android:textColor="@color/dark_gray"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        />

    <Button
        android:id="@+id/cell_btn_view"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:textColor="@color/colorPrimary"
        android:textSize="17sp"
        android:textStyle="bold"
        android:layout_gravity="center_vertical"
        android:background="@color/transparent"
        />

</LinearLayout>