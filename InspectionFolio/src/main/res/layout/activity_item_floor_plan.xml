<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activitynew.if_InsItemFloorPlans">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/constraint_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?android:attr/actionBarSize"
        tools:ignore="UselessParent"
        android:orientation="vertical">

        <include
            android:id="@+id/inspection_item_title_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            layout="@layout/view_inspection_item_titles" />

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/list_refresh_layout"
            app:layout_constraintTop_toBottomOf="@id/inspection_item_title_view"
            app:layout_constraintBottom_toBottomOf="@+id/constraint_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp">
            <com.snapinspect.snapinspect3.views.StickyHeaderListView.StickyHeaderListView
                android:id="@+id/listview_floor_plan"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/intercom_white"
                android:childDivider="@android:color/transparent"
                android:childIndicatorRight="14.5sp"
                android:dividerHeight="0dp"
                android:groupIndicator="@null"
                android:transcriptMode="disabled"/>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/empty_data_refresh_layout"
            app:layout_constraintTop_toBottomOf="@id/inspection_item_title_view"
            app:layout_constraintBottom_toBottomOf="@+id/constraint_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp">
        <com.snapinspect.snapinspect3.views.EmptyDataView
            android:id="@+id/view_empty_data"
            app:viewType="floorPlans"
            app:layout_constraintTop_toBottomOf="@id/inspection_item_title_view"
            app:layout_constraintBottom_toBottomOf="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="0dp"/>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>