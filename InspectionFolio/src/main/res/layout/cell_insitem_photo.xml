<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="match_parent" android:layout_margin="10dp">
    <ImageButton
        android:layout_height="wrap_content" android:id="@+id/btn_TakePhoto" android:layout_marginLeft="20dp" android:layout_marginRight="20dp"
        android:layout_width="wrap_content" android:background="@drawable/ic_compact_camera" android:layout_gravity="center_vertical" />

    <HorizontalScrollView
        android:layout_height="wrap_content" android:minHeight="60dp" android:layout_gravity="center_vertical" android:id="@+id/sv_DisplayPhotos"
        android:layout_width="wrap_content" android:scrollbars="horizontal">
        <LinearLayout
            android:layout_height="match_parent" android:id="@+id/ll_PhotoList" android:orientation="horizontal"
            android:layout_width="wrap_content">

        </LinearLayout>
    </HorizontalScrollView>
</LinearLayout>