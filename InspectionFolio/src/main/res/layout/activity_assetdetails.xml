<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:keepScreenOn="true"
    tools:context=".activity.if_AssetDetails">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorPrimary"
        android:orientation="vertical">

        <com.snapinspect.snapinspect3.views.SegmentedControl
            android:layout_marginTop="?android:attr/actionBarSize"
            android:id="@+id/segmentedControl"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="@dimen/margin_16"
            android:layout_marginRight="@dimen/margin_16"
            android:layout_marginBottom="@dimen/margin_small"/>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ListView
                android:id="@+id/acitivity_assetdetails_listview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:divider="@null"
                android:background="@android:color/white"/>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/empty_data_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <com.snapinspect.snapinspect3.views.EmptyDataView
                android:id="@+id/view_empty_data"
                app:viewType="floorPlans"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    </LinearLayout>

    <ImageButton
        android:layout_width="@dimen/button_width"
        android:layout_height="@dimen/button_width"
        android:visibility="gone"
        android:id="@+id/button_add_blueprint"
        android:layout_marginTop="@dimen/margin_80"
        android:layout_marginEnd="@dimen/margin_16"
        android:layout_marginBottom="@dimen/margin_16"
        android:layout_gravity="bottom|end"
        android:src="@drawable/icn_add"
        android:background="@drawable/rounded_button_add"
        android:elevation="2dp"
        android:textColor="@android:color/white"
        android:contentDescription="@string/button_add_blueprint"/>
</FrameLayout>