<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activitynew.Photo.if_DisplayPhoto">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:layout_marginTop="?android:attr/actionBarSize"
        android:layout_marginBottom="49dp">

        <androidx.viewpager.widget.ViewPager
            android:id="@+id/view_pager"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/margin_16"
            android:layout_marginBottom="@dimen/margin_16"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

    </RelativeLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        >
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_inspection_bottom"
            />
        <ImageButton
            android:id="@+id/btn_Delete_Photo"
            android:src="@drawable/icon_trash_can_red"
            android:contentDescription="@string/btn_action_desc"
            android:background="@color/transparent"
            android:layout_width="44dp"
            android:layout_height="match_parent"
            android:layout_gravity="left"
            />
        <TextView
            android:id="@+id/tv_bottom_info"
            android:textColor="@color/dark_gray"
            android:textSize="14sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            />
        <Button
            android:id="@+id/btn_action_all_photos"
            android:text="@string/btn_action_all_photos"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@color/colorPrimary"
            android:background="@color/transparent"
            android:paddingHorizontal="@dimen/margin_10"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="right"
            />
    </FrameLayout>

    <include layout="@layout/view_input_accessory_toolbar"/>
</FrameLayout>