<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_sign">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?android:actionBarSize"
        android:background="#FFFFFF"
        android:orientation="vertical">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="4dp">

            <TextView
                android:id="@+id/sign_tv_declaration_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/declaration"
                android:textColor="@android:color/black"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/sign_tv_declaration"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@id/sign_tv_declaration_title"
                android:layout_marginTop="4dp"
                android:maxLines="5"
                android:textColor="@android:color/black" />

            <TextView
                android:id="@+id/sign_btn_more"
                android:layout_width="35dp"
                android:layout_height="20dp"
                android:layout_alignBottom="@id/sign_tv_declaration"
                android:layout_alignParentRight="true"
                android:background="@android:color/white"
                android:gravity="center"
                android:text="@string/more"
                android:textColor="@color/intercom_main_blue"
                android:visibility="gone"/>

        </RelativeLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/gray_color" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_margin="4dp"
            android:gravity="center_vertical">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/name"
                android:textColor="@android:color/black"/>

            <EditText
                android:id="@+id/sign_et_name"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:imeOptions="actionDone"
                android:inputType="text"
                android:textSize="14sp"
                android:background="@null"
                android:textColor="@android:color/black" />
            <Button
                android:id="@+id/sign_btn_add"
                android:layout_width="30dp"
                android:layout_height="30dp"
                android:background="@drawable/ic_plus"/>
        </LinearLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/gray_color" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_margin="4dp"
            android:gravity="center_vertical">
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/email"
                android:textColor="@android:color/black"/>

            <EditText
                android:id="@+id/sign_et_email"
                android:layout_width="0dp"
                android:layout_height="30dp"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="8dp"
                android:layout_weight="1"
                android:textSize="14sp"
                android:gravity="center_vertical"
                android:background="@null"
                android:imeOptions="actionDone"
                android:inputType="textEmailAddress"
                android:textColor="@android:color/black" />
        </LinearLayout>
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/gray_color" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/signature"
            android:textColor="@android:color/black"
            android:layout_marginLeft="4dp"
            android:layout_marginTop="4dp"
            android:textStyle="bold" />
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="4dp"
            android:background="@drawable/signature_border"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/ll_sign_area"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"

                android:layout_margin="2dp"
                android:orientation="vertical">

            </LinearLayout>
        </LinearLayout>



        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">


            <Button
                android:id="@+id/btn_clear_sign"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginRight="160dp"
                android:background="@drawable/btn_red_action"
                android:minWidth="100dp"
                android:text="@string/undo"
                android:textColor="@color/SI_Button_Red" />


            <Button
                android:id="@+id/btn_sign"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:layout_marginRight="20dp"
                android:height="20dp"
                android:background="@drawable/btn_green_action"
                android:minWidth="100dp"
                android:text="@string/save"
                android:textColor="@color/SI_Button_Green" />

        </RelativeLayout>
    </LinearLayout>
</FrameLayout>
