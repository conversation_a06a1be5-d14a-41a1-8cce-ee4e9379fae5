<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/margin_20"
        android:paddingVertical="@dimen/margin_small"
        android:background="@android:color/white">
    <RelativeLayout
            android:id="@+id/view_head_indicator"
            android:layout_width="40dp"
            android:layout_height="40dp">
        <com.snapinspect.snapinspect3.util.CircleView
                android:id="@+id/name_bg_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"/>
        <TextView
                android:id="@+id/view_inbox_cell_short_name"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:textSize="20sp"
                android:textColor="@android:color/white"/>
    </RelativeLayout>

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_smallest"
            android:orientation="vertical">
        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">
            <TextView
                    android:id="@+id/tv_notification_text"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:lineSpacingExtra="2dp"
                    android:layout_weight="1"
                    android:textColor="@android:color/black"
                    android:textSize="14sp"/>
            <ImageView
                    android:id="@+id/accessoryArrow"
                    android:src="@drawable/arrow_right"
                    android:layout_gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>
        </LinearLayout>
        <TextView
                android:id="@+id/tv_notification_time"
                android:textColor="#696969"
                android:layout_marginTop="@dimen/margin_smallest"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:ellipsize="end"
                android:maxLines="1"/>
    </LinearLayout>
</LinearLayout>