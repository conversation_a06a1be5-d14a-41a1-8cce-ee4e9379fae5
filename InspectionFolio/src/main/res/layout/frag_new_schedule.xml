<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:background="@color/colorPrimary">

        <EditText
            android:id="@+id/search_schedules"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:visibility="gone"
            android:layout_marginLeft="@dimen/margin_16"
            android:layout_marginRight="@dimen/margin_16"
            android:layout_marginTop="@dimen/margin_small"
            android:layout_marginBottom="@dimen/margin_small"
            android:hint="@string/search"
            android:paddingLeft="@dimen/margin_16"
            android:paddingRight="@dimen/margin_16"
            android:paddingStart="@dimen/margin_16"
            android:paddingEnd="@dimen/margin_16"
            android:background="@drawable/bg_search_bar"
            android:textSize="16sp"
            style="@style/EditText"
            android:imeOptions="actionSearch"
            android:drawableLeft="@drawable/icn_search"
            android:drawablePadding="8dp"
            android:inputType="textVisiblePassword"
            android:autofillHints=""
            android:drawableStart="@drawable/icn_search"
            />
    </LinearLayout>
    <FrameLayout
        android:id="@+id/schedules_tab_bar"
        android:layout_width="match_parent"
        android:layout_height="54dp"
        android:background="@android:color/white"
        >
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="@dimen/margin_10"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btn_schedule_today"
                android:text="@string/schedule_range_today"
                style="@style/SchedulesTabButton"
                />
            <Button
                android:id="@+id/btn_schedule_weekly"
                android:text="@string/schedule_range_week"
                style="@style/SchedulesTabButton"
                />
            <Button
                android:id="@+id/btn_schedule_monthly"
                android:text="@string/schedule_range_month"
                style="@style/SchedulesTabButton"
                />
            <Button
                android:id="@+id/btn_schedule_upcoming"
                android:text="@string/schedule_range_upcoming"
                style="@style/SchedulesTabButton"
                />
        </LinearLayout>
        <Button
            android:id="@+id/btn_schedule_overdue"
            android:text="@string/schedule_range_overdue"
            style="@style/SchedulesTabButton"
            android:textColor="@color/schedules_tab_overdue"
            android:layout_gravity="right"
            />
        <View
            android:id="@+id/line_selected_bottom"
            android:background="@color/comments_tab_selected"
            android:layout_width="30dp"
            android:layout_height="2dp"
            android:layout_gravity="bottom"
            android:layout_marginBottom="@dimen/margin_10"
            />
    </FrameLayout>

    <com.prolificinteractive.materialcalendarview.MaterialCalendarView
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/calendarView"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:mcv_tileHeight="@dimen/calendar_tile_height"
        app:mcv_firstDayOfWeek="sunday"
        app:mcv_showOtherDates="all"
        app:mcv_selectionMode="single"
        app:mcv_selectionColor="@color/calendar_tile_selected"
        android:background="@android:color/white"
        android:paddingBottom="@dimen/margin_10"
        />
    <FrameLayout
        android:id="@+id/today_header_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:background="@android:color/white">
        <TextView
            android:id="@+id/today_header_tv_title"
            android:background="@android:color/white"
            style="@style/SchedulesHeaderTitleLarge"
            android:layout_marginLeft="@dimen/margin_20"
            android:layout_marginTop="@dimen/margin_10"
            android:layout_marginBottom="@dimen/margin_10"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/margin_20" />
        <TextView
            android:id="@+id/today_header_tv_subtitle"
            style="@style/SchedulesHeaderTitleSmall"
            android:textColor="@color/light_gray"
            android:layout_gravity="end|center_vertical"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_10"
            android:layout_marginBottom="@dimen/margin_10"
            android:layout_marginRight="@dimen/margin_20"
            android:layout_marginEnd="@dimen/margin_20" />
    </FrameLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:paddingBottom="58dp">
        <LinearLayout
            android:id="@+id/schedule_empty_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:orientation="vertical"
            android:gravity="center_vertical"
            android:visibility="gone"
            >
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:src="@drawable/icon_empty_schedules"
                android:layout_marginBottom="@dimen/margin_20"
                />

            <TextView
                android:id="@+id/schedule_empty_text"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="40dp"
                android:layout_marginRight="40dp"
                android:singleLine="false"
                android:text="@string/schedule_empty_text_today"
                android:textAlignment="center"
                android:textColor="@color/dark_gray"
                android:textSize="14sp" />
        </LinearLayout>
        <com.snapinspect.snapinspect3.views.StickyHeaderListView.StickyHeaderListView
            android:id="@+id/lv_Schedules"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            />
    </RelativeLayout>
</LinearLayout>