<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:orientation="vertical"
        android:layout_marginHorizontal="@dimen/margin_16"
        android:layout_marginVertical="@dimen/margin_10"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_ins_title"
            android:textStyle="bold"
            android:textSize="@dimen/text_normal"
            android:textColor="@color/dark_gray"
            android:layout_marginBottom="@dimen/margin_smallest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_address"
            android:textSize="@dimen/text_normal"
            android:textColor="@color/dark_gray"
            android:layout_marginBottom="@dimen/margin_smallest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_ref"
            android:textSize="@dimen/text_normal"
            android:textColor="@color/dark_blue"
            android:layout_marginBottom="@dimen/margin_smallest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_inspector"
            android:textSize="@dimen/text_small"
            android:textColor="@color/dark_gray"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>
    </LinearLayout>
</FrameLayout>