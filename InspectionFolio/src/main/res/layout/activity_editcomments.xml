<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_EditComments">

    <LinearLayout
        android:orientation="vertical"
        android:layout_width="fill_parent"
        android:layout_marginTop="?android:actionBarSize"
        android:layout_height="fill_parent"
        android:background="@android:color/white"
        android:layout_gravity="center_horizontal|top">

        <TextView
            android:id="@+id/text_comment_tip"
            android:textColor="@color/placeholderColor"
            android:textSize="14sp"
            android:textStyle="italic"
            android:paddingTop="14dp"
            android:paddingBottom="10dp"
            android:paddingLeft="22dp"
            android:paddingRight="22dp"
            android:minHeight="26dp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            />

        <EditText
            android:id="@+id/txt_comments"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:lines="8"
            android:minLines="6"
            android:maxLines="10"
            android:scrollbars="vertical"
            android:gravity="top"
            android:cursorVisible="true"
            android:textCursorDrawable="@null"
            android:paddingLeft="22dp"
            android:paddingRight="22dp"
            android:paddingBottom="10dp"
            android:textColor="@android:color/black"
            android:background="@android:color/transparent"
            android:singleLine="false" />

        <ListView
            android:id="@+id/lv_Comment_QuickText"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:divider="#CCCCCC"
            android:dividerHeight="1dp"
            android:layout_weight="1"
            android:background="@android:color/white"
            />

        <include layout="@layout/view_input_accessory_toolbar"/>

    </LinearLayout>>

</FrameLayout>
