<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activitynew.Photo.if_DisplayFile">

    <RelativeLayout
        android:id="@+id/view_Full"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?android:attr/actionBarSize"
        android:background="@android:color/white"
        >
        <ImageView
            android:id="@+id/img_photo_view"
            android:layout_centerHorizontal="true"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/margin_16"
            android:layout_width="match_parent"
            />

        <ProgressBar
            android:id="@+id/progress_bar"
            android:visibility="gone"
            android:layout_centerInParent="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </RelativeLayout>
</FrameLayout>