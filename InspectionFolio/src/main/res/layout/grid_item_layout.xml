<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/grid_image_view"
        android:layout_gravity="center"
        android:layout_width="88dp"
        android:layout_height="88dp"
        android:layout_marginTop="5dp"
        android:layout_marginBottom="5dp"
        android:scaleType="fitXY"
        android:src="@drawable/ic_launcher" />

    <LinearLayout
        android:layout_width="88dp"
        android:layout_height="wrap_content"
        android:layout_below="@+id/grid_image_view"
        android:gravity="center_horizontal"
        android:paddingBottom="5dp">
    <TextView
        android:id="@+id/nameLab"
        android:layout_gravity="center"
        android:layout_width="35dp"
        android:layout_height="25dp"
        android:textSize="12sp"
        android:background="@drawable/crop_view_shape"
        android:textColor="#ffffff"
        android:gravity="center_horizontal" />
    </LinearLayout>

    <ImageView
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:id="@+id/delBtn"
        android:layout_alignTop="@+id/grid_image_view"
        android:layout_alignEnd="@+id/grid_image_view"
        android:background="@drawable/close"
        android:adjustViewBounds="true" />
</RelativeLayout>
