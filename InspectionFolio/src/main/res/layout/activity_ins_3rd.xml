<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".activitynew.inspection3rd.if_Ins_3rd"
    android:background="@color/colorPrimary"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ListView
        android:id="@+id/lv_Inspections_areas"
        android:background="@android:color/white"
        android:layout_width="fill_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="?android:actionBarSize"
        android:divider="@color/transparent"
        />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        >
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_inspection_bottom"
            />
        <TextView
            android:id="@+id/tv_inspection_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/margin_10"
            android:layout_marginStart="@dimen/margin_10"
            android:textColor="@color/dark_gray"
            android:textSize="14sp"
            android:layout_gravity="left|center_vertical"
            />
        <TextView
            android:id="@+id/tv_bottom_info"
            android:textColor="@color/dark_gray"
            android:textSize="14sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            />
        <ImageButton
            android:id="@+id/btn_Inspection_Action"
            android:src="@drawable/btn_inspection_action"
            android:contentDescription="@string/btn_action_desc"
            android:background="@color/transparent"
            android:layout_width="44dp"
            android:layout_height="match_parent"
            android:layout_gravity="right"
            />
    </FrameLayout>
</LinearLayout>