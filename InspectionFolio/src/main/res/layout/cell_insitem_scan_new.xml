<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginEnd="10dp">
        <ImageButton
            android:layout_height="45dp"
            android:layout_width="45dp"
            android:id="@+id/btn_TakeScan"
             android:background="@drawable/ic_gray_scan"/>

        <LinearLayout
            android:id="@+id/red_indicator"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginStart="37dp"
            android:background="@drawable/bg_red_indicator"
            android:orientation="horizontal"
            android:visibility="gone"/>
        <LinearLayout
            android:id="@+id/gray_indicator"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginStart="37dp"
            android:background="@drawable/bg_gray_indicator"
            android:orientation="horizontal"
            android:visibility="visible"/>
        <TextView
            android:id="@+id/tv_scan_indicator"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginStart="37dp"
            android:layout_marginTop="-1.5dp"
            android:textColor="@color/intercom_white"
            android:textSize="12sp"
            android:text="+"
            android:gravity="center"/>
    </RelativeLayout>
    <HorizontalScrollView
        android:layout_height="wrap_content"
        android:minHeight="60dp"
        android:layout_gravity="center_vertical"
        android:id="@+id/sv_DisplayScans"
        android:layout_width="wrap_content"
        android:scrollbars="horizontal">
        <LinearLayout
            android:layout_height="match_parent"
            android:id="@+id/ll_ScanList"
            android:orientation="horizontal"
            android:layout_width="wrap_content">

        </LinearLayout>
    </HorizontalScrollView>
</LinearLayout>