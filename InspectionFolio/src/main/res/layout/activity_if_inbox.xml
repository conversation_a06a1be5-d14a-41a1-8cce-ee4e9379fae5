<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorPrimary"
        tools:context=".activitynew.Inbox.if_Inbox">
    <com.softw4re.views.InfiniteListView
            android:id="@+id/inbox_list_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="?android:actionBarSize"
            android:dividerHeight="0dp"
            app:dividerVisible="false"
            android:divider="@color/transparent"
            android:background="@android:color/white">
    </com.softw4re.views.InfiniteListView>
    <RelativeLayout
            android:visibility="gone"
            android:id="@+id/empty_box"
            android:layout_marginTop="?android:actionBarSize"
            android:layout_width="match_parent"
            android:layout_height="200dp">
        <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/inbox"
                android:scaleType="fitCenter"/>
        <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:textColor="@color/colorPrimary"
                android:gravity="center"
                android:textSize="20sp"
                android:text="@string/you_have_no_new_notifications"/>
    </RelativeLayout>
</FrameLayout>

