<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.if_video">

    <RelativeLayout
        android:id="@+id/camera_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_centerInParent="true" android:background="#000000">
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="55dp"
        android:layout_height="match_parent"
        android:id="@+id/top_view"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:background="#aa000000">

        <Button
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:id="@+id/focusBtn"
            android:background="@drawable/a_focus"
            android:onClick="focusClicked"
            android:layout_alignParentTop="true"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="20dp" />
        <Button
            android:layout_width="50dp"
            android:layout_height="40dp"
            android:id="@+id/switchBtn"
            android:background="@drawable/swich"
            android:onClick="switchClicked"
            android:layout_below="@+id/focusBtn"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="10dp" />

        <Button
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:id="@+id/lightBtn"
            android:background="@drawable/light_off"
            android:onClick="lightClicked"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="20dp" />



        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:text="00:00"
            android:id="@+id/timeLab"
            android:layout_centerVertical="true"
            android:layout_centerHorizontal="true"
            android:textColor="#ffffff"
            android:gravity="center_vertical|center_horizontal" />
    </RelativeLayout>

    <RelativeLayout
        android:layout_width="90dp"
        android:layout_height="match_parent"
        android:id="@+id/bottom_view"
        android:layout_alignParentRight="true"
        android:layout_alignParentEnd="true"
        android:background="#aa000000">

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/menu_done"
            android:id="@+id/doneBtn"
            android:textColor="#ffffff"
            android:background="#00ffff00"
            android:onClick="doneClicked"
            android:layout_below="@+id/shutterBtn"
            android:layout_centerHorizontal="true"
            android:layout_alignParentBottom="true" />

        <Button
            android:id="@+id/shutterBtn"
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_centerInParent="true"
            android:background="@drawable/record_start"
            android:textColor="@android:color/white"/>

        <RelativeLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_above="@+id/shutterBtn"
            android:layout_centerHorizontal="true"
            android:layout_alignParentTop="true" >
            <ImageView
                android:id="@+id/previewImageView"
                android:layout_width="70dp"
                android:layout_height="70dp"
                android:scaleType="fitXY"
                android:onClick="previewClicked"
                android:layout_centerInParent="true"
                android:textColor="@android:color/white"/>
        </RelativeLayout>
    </RelativeLayout>

    <TextView
        android:id="@+id/zoomLevelText"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="@android:color/white"
        android:layout_marginTop="@dimen/margin_16"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        />

    <TextView
        android:layout_width="280dp"
        android:layout_height="120dp"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:text="@string/video_recording_orientation_alert"
        android:id="@+id/blinkLabel"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:textColor="#ffffff"
        android:layout_marginTop="20dp"
        android:gravity="center_vertical|center_horizontal"
        android:background="#aaff0000" />
</RelativeLayout>
