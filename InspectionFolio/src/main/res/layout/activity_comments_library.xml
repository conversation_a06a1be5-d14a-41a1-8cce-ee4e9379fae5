<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_CommentsLibrary">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_gravity="center_horizontal|top"
        android:layout_marginTop="?android:actionBarSize"
        android:orientation="vertical">

        <EditText
            android:id="@+id/search_asset"
            style="@style/EditText"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="@dimen/margin_20"
            android:layout_marginTop="@dimen/margin_16"
            android:layout_marginRight="@dimen/margin_20"
            android:layout_marginBottom="@dimen/margin_20"
            android:background="@drawable/bg_search_bar"
            android:drawableLeft="@drawable/icn_search"
            android:drawablePadding="8dp"
            android:hint="@string/search"
            android:imeOptions="actionSearch"
            android:inputType="textVisiblePassword"
            android:paddingLeft="@dimen/margin_16"
            android:paddingRight="@dimen/margin_16"
            android:textSize="16sp"
            android:drawableStart="@drawable/icn_search"
            android:paddingStart="@dimen/margin_16"
            />
        <FrameLayout
            android:id="@+id/layout_tab_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="54dp"
                android:background="@android:color/white"
                android:orientation="horizontal">

                <Button
                    android:id="@+id/btn_comments_global"
                    android:text="@string/comments_shared"
                    style="@style/CommentsTabButton"
                    />
                <Button
                    android:id="@+id/btn_comments_area_only"
                    android:text="@string/comments_area_only"
                    style="@style/CommentsTabButton"
                    />
                <Button
                    android:id="@+id/btn_comments_item_only"
                    android:text="@string/comments_item_only"
                    style="@style/CommentsTabButton"
                    />
                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_weight="1" />
                <FrameLayout
                    android:layout_width="wrap_content"
                    android:layout_height="match_parent"
                    android:layout_marginRight="@dimen/margin_small"
                    >
                    <Button
                        android:id="@+id/btn_comments_selected"
                        android:text="@string/comments_selected"
                        style="@style/CommentsTabButton"
                        />
                    <TextView
                        android:id="@+id/tv_comments_badge"
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:background="@drawable/bg_round_badge"
                        android:textAlignment="center"
                        android:layout_marginTop="3dp"
                        android:layout_marginRight="3dp"
                        android:layout_gravity="right"
                        android:textColor="@android:color/white"
                        android:layout_marginEnd="3dp"
                        />
                </FrameLayout>
            </LinearLayout>
            <View
                android:id="@+id/line_selected_bottom"
                android:background="@color/comments_tab_selected"
                android:layout_width="45dp"
                android:layout_height="2dp"
                android:layout_gravity="bottom"
                android:layout_marginBottom="@dimen/margin_10"
                />
        </FrameLayout>

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.woxthebox.draglistview.DragListView
                android:id="@+id/lv_comments_selected"
                android:descendantFocusability="afterDescendants"
                android:windowSoftInputMode="adjustPan"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/white"
                />
            <ListView
                android:id="@+id/lv_comments_library"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/white"
                android:divider="#CCCCCC"
                android:dividerHeight="0dp"
                />

        </FrameLayout>
    </LinearLayout>
</FrameLayout>
