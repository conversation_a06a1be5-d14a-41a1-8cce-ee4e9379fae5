<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <com.snapinspect.snapinspect3.activitynew.camera.AutoFitTextureView
        android:id="@+id/camera_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="9:16"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:withRatio="9"
        app:heightRatio="16"/>

    <com.snapinspect.snapinspect3.activitynew.camera.FocusView
        android:id="@+id/focusView"
        android:layout_width="@dimen/focus_view_size"
        android:layout_height="@dimen/focus_view_size"
        android:visibility="invisible"
        tools:ignore="MissingConstraints" />

    <LinearLayout
        android:id="@+id/middle_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="1:1"
        app:layout_constraintTop_toBottomOf="@+id/top_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:orientation="horizontal" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="#000000"
        app:layout_constraintBottom_toTopOf="@+id/bottom_view"
        app:layout_constraintTop_toBottomOf="@+id/middle_view" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:id="@+id/top_view"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:background="#000000"
        tools:ignore="MissingConstraints">

        <LinearLayout
            android:id="@+id/lightBtnView"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:gravity="center"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp">
            <ImageView
                android:layout_width="18dp"
                android:layout_height="25dp"
                android:id="@+id/lightBtn"
                android:src="@drawable/flash_auto"
                android:scaleType="fitXY"
                />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/brightBtnView"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:gravity="center"
            android:layout_toRightOf="@id/lightBtnView"
            android:layout_centerVertical="true"
            android:layout_marginLeft="20dp">
            <ImageView
                android:layout_width="27dp"
                android:layout_height="27dp"
                android:id="@+id/brightBtn"
                android:src="@drawable/icn_sun"
                android:scaleType="fitXY"
                />
        </LinearLayout>

        <Button
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:id="@+id/switchBtn"
            android:background="@drawable/icn_switchcam"
            android:onClick="switchClicked"
            android:visibility="gone"
            android:layout_centerInParent="true"/>

        <Button
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:id="@+id/libraryBtn"
            android:background="@drawable/icn_library"
            android:layout_marginTop="93dp"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"/>

        <Button
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:id="@+id/focusBtn"
            android:background="@drawable/a_focus"
            android:onClick="focusClicked"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="20dp"
            android:visibility="gone"/>

    </RelativeLayout>

    <RelativeLayout
        android:id="@+id/seekView"
        android:visibility="gone"
        android:layout_width="200dp"
        android:layout_height="45dp"
        android:background="#000000"
        android:layout_marginLeft="50dp"
        app:layout_constraintTop_toBottomOf="@+id/top_view"
        app:layout_constraintStart_toStartOf="parent">
        <SeekBar
            android:id="@+id/seekBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:progressBackgroundTint="@color/black"
            android:progressTint="@color/intercom_white"
            android:min="0"
            android:max="100" >
        </SeekBar>
    </RelativeLayout>
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:id="@+id/bottom_view"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="#000000">

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Done"
            android:id="@+id/doneBtn"
            android:textColor="#ffffff"
            android:background="#00ffff00"
            android:onClick="doneClicked"
            android:layout_toLeftOf="@+id/shutterBtn"
            android:layout_centerVertical="true"
            android:layout_alignParentLeft="true" />

        <Button
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:id="@+id/shutterBtn"
            android:background="@drawable/shutter_normal"
            android:layout_centerVertical="true"
            android:layout_centerHorizontal="true"
            android:onClick="shutterClicked" />

        <com.github.ybq.android.spinkit.SpinKitView
            android:id="@+id/loading"
            style="@style/SpinKitView.Circle"
            android:layout_width="55dp"
            android:layout_height="55dp"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            app:SpinKit_Color="@color/intercom_white"
            android:visibility="gone"/>

        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:id="@+id/imageView"
            android:baselineAlignBottom="false"
            android:background="@drawable/crop_view_shape"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:visibility="visible"/>

    </RelativeLayout>

    <TextView
        android:id="@+id/alertLabel"
        android:layout_width="280dp"
        android:layout_height="100dp"
        android:layout_marginTop="100dp"
        android:background="#aaff0000"
        android:gravity="center_vertical|center_horizontal"
        android:text="@string/allow_permission"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:textColor="#ffffff"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <!--<LinearLayout-->
    <!--android:id="@+id/camera_controls"-->
    <!--android:layout_width="match_parent"-->
    <!--android:layout_height="66dp"-->
    <!--android:orientation="horizontal"-->
    <!--app:layout_constraintBottom_toBottomOf="parent"-->
    <!--android:background="@color/black_overlay">-->

    <!--<TextView-->
    <!--android:id="@+id/blank_text"-->
    <!--android:layout_width="0dp"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:layout_weight="1"-->
    <!--android:text=""-->
    <!--android:textColor="@color/gray_color"-->
    <!--android:textSize="14sp"-->
    <!--/>-->

    <!--<ImageView-->
    <!--android:id="@+id/back_button"-->
    <!--android:layout_width="0dp"-->
    <!--android:layout_height="33dp"-->
    <!--android:layout_gravity="center"-->
    <!--android:layout_weight="3"-->
    <!--android:tint="@color/gray_color"-->
    <!--android:alpha="0.8"-->
    <!--app:srcCompat="@drawable/icn_back_filled" />-->

    <!--<ImageView-->
    <!--android:id="@+id/capture_image_button"-->
    <!--android:layout_width="0dp"-->
    <!--android:layout_height="44dp"-->
    <!--android:layout_gravity="center"-->
    <!--android:layout_weight="10"-->
    <!--android:alpha="0.5"-->
    <!--android:src="@drawable/camera" />-->

    <!--<ImageView-->
    <!--android:id="@+id/finished_button"-->
    <!--android:layout_width="0dp"-->
    <!--android:layout_height="33dp"-->
    <!--android:layout_gravity="center"-->
    <!--android:layout_weight="3"-->
    <!--android:tint="@color/gray_color"-->
    <!--android:alpha="0.8"-->
    <!--app:srcCompat="@drawable/icon_house" />-->

    <!--<TextView-->
    <!--android:id="@+id/photo_count"-->
    <!--android:layout_width="0dp"-->
    <!--android:layout_height="wrap_content"-->
    <!--android:layout_weight="1"-->
    <!--android:text=""-->
    <!--android:textColor="@color/gray_color"-->
    <!--android:alpha="0.8"-->
    <!--android:textSize="14sp"-->
    <!--/>-->

    <!--</LinearLayout>-->
</androidx.constraintlayout.widget.ConstraintLayout>