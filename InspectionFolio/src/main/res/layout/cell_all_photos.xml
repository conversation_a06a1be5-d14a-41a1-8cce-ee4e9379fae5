<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/img_photo_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:id="@+id/img_toggle_icon"
        android:src="@drawable/icon_toggle_select_photo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="right|bottom"
        android:layout_marginBottom="@dimen/margin_10"
        android:layout_marginRight="@dimen/margin_10"
        android:layout_marginEnd="@dimen/margin_10" />

    <ProgressBar
        android:id="@+id/indicator_view"
        android:visibility="gone"
        style="?android:attr/progress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:indeterminate="true" />

</FrameLayout>