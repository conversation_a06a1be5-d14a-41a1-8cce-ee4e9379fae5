<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_sign">
    <ScrollView
        android:layout_height="match_parent"
        android:layout_marginTop="?android:attr/actionBarSize"
        android:layout_width="match_parent">
    <LinearLayout android:orientation="vertical" android:layout_height="wrap_content" android:layout_width="match_parent">
        <TextView  android:textColor="@color/intercom_white"
            android:layout_width="match_parent" android:textSize="20dp" android:layout_marginLeft="20dp" android:layout_marginTop="10dp"
            android:layout_height="wrap_content" android:text="Please fill your information" />
        <EditText
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_marginTop="20dp"

            style="@style/edit_login"
            android:hint="@string/FirstName"
            android:id="@+id/signup_firstname"/>
        <EditText
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"

            style="@style/edit_login"
            android:hint="@string/LastName"
            android:id="@+id/signup_lastname"/>
        <EditText
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"

            style="@style/edit_login"
            android:hint="@string/CompanyName"
            android:id="@+id/signup_companyname"/>
        <EditText
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"

            style="@style/edit_login"
            android:hint="@string/Phone"
            android:id="@+id/signup_phone"/>

        <EditText
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"
            style="@style/edit_login"
            android:hint="Email (Username for SnapInspect 3)"
            android:inputType="textEmailAddress"
            android:id="@+id/signup_email"/>
        <EditText
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"
            style="@style/edit_login"
            android:password="true"
            android:hint="Password"
            android:id="@+id/signup_password"/>
        <EditText
            android:layout_height="wrap_content"
            android:layout_width="match_parent"
            android:layout_marginTop="10dp"
            style="@style/edit_login"
            android:password="true"
            android:hint="@string/RepeatPassword"
            android:id="@+id/signup_repeatpassword"/>
        <TextView android:textColor="@color/intercom_white"
            android:layout_width="match_parent" android:textSize="20dp" android:layout_marginLeft="20dp" android:layout_marginTop="10dp"
            android:layout_height="wrap_content" android:text="Please choose your inspection type" />
        <Spinner
            android:id="@+id/signup_country"
            android:spinnerMode="dialog"
            android:layout_margin="10dp"
            android:layout_marginTop="10dp"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content" />
        <Spinner
            android:id="@+id/signup_industry"
            android:layout_margin="10dp"

            android:spinnerMode="dialog"

            android:layout_width="fill_parent"
            android:layout_height="wrap_content" />
        <Spinner
            android:id="@+id/signup_template"
            android:layout_margin="10dp"

            android:spinnerMode="dialog"

            android:layout_width="fill_parent"
            android:layout_height="wrap_content" />
        <Button android:id="@+id/btn_Signup"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:padding="10dp"
            android:textSize="20dp"
            android:textColor="@android:color/white"
            android:background="#50000000"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:text="@string/btn_SignUp">

        </Button>
    </LinearLayout>
    </ScrollView>
</FrameLayout>
