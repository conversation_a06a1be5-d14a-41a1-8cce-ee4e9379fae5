<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".activitynew.inspection3rd.if_Ins_Edit_3rd"
    android:background="@color/colorPrimary"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.woxthebox.draglistview.DragListView
            android:id="@+id/lv_Inspections_areas"
            android:descendantFocusability="afterDescendants"
            android:windowSoftInputMode="adjustPan"
            android:orientation="vertical"
            android:layout_width="fill_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginTop="?android:actionBarSize"
            android:background="@android:color/white"
    />
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        >
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_inspection_bottom"
            />

        <ImageButton
            android:id="@+id/btn_inspection_add"
            android:src="@drawable/icon_add_ins_room"
            android:contentDescription="@string/btn_action_desc"
            android:background="@color/transparent"
            android:layout_width="49dp"
            android:layout_height="match_parent"
            android:layout_gravity="right|center_vertical"
            />
    </FrameLayout>
</LinearLayout>