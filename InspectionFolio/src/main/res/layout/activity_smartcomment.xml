<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_smartcomment">

    <LinearLayout
        android:layout_width="fill_parent"
        android:layout_height="fill_parent"
        android:layout_gravity="center_horizontal|top"
        android:layout_marginTop="?android:actionBarSize"
        android:orientation="vertical">

        <EditText
            android:id="@+id/search_asset"
            style="@style/EditText"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="@dimen/margin_16"
            android:layout_marginTop="@dimen/margin_small"
            android:layout_marginRight="@dimen/margin_16"
            android:layout_marginBottom="@dimen/margin_small"
            android:background="@drawable/bg_search_bar"
            android:drawableLeft="@drawable/icn_search"
            android:drawablePadding="8dp"
            android:hint="@string/search"
            android:imeOptions="actionSearch"
            android:inputType="textVisiblePassword"
            android:paddingLeft="@dimen/margin_16"
            android:textSize="16sp"
            android:drawableStart="@drawable/icn_search"
            android:paddingStart="@dimen/margin_16"
            />

        <ListView
            android:id="@+id/lv_quickphrase"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:divider="#CCCCCC"
            android:dividerHeight="2dp"
            />

    </LinearLayout>
</FrameLayout>
