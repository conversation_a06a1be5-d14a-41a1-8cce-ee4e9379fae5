<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    tools:context=".activity.if_videoshow">
    <LinearLayout
        android:layout_height="match_parent"
        android:weightSum="10"
        android:orientation="vertical"
        android:layout_width="match_parent">
        <LinearLayout
            android:layout_height="0dp" android:layout_weight="1" android:orientation="horizontal"
            android:layout_width="match_parent">
            <Button android:layout_height="match_parent" android:layout_width="wrap_content" android:id="@+id/btn_Back"
                android:background="@android:color/transparent" android:text="Back" android:textColor="@android:color/white" android:layout_weight="1">Back</Button>
            <View
                android:layout_width="wrap_content"
                android:layout_height="match_parent" android:layout_weight="8"
                android:background="@android:color/black"/>
        </LinearLayout>

    <LinearLayout android:gravity="center"
        android:layout_height="0dp" android:layout_weight="9"
        android:layout_width="match_parent">
        <VideoView
            android:id="@+id/video_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"/>
        </LinearLayout>

    </LinearLayout>

</FrameLayout>
