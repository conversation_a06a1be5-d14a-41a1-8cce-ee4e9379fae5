<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/margin_20"
    android:paddingVertical="@dimen/margin_10"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_title"
        style="@style/asset_info_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

    <CheckBox
        android:id="@+id/cb_item_checked"
        android:clickable="false"
        android:layout_marginStart="@dimen/margin_10"
        android:layout_gravity="center_vertical"
        android:button="@drawable/setting_option_radio_button"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"/>

</LinearLayout>