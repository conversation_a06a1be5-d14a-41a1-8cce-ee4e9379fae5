<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

    <ImageView
        android:id="@+id/icon_add_comment"
        android:layout_gravity="top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginLeft="@dimen/margin_24"
        android:src="@drawable/icon_add_comment"
        />

    <LinearLayout
        android:padding="2dp"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_10"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft ="@dimen/margin_24"
        android:layout_marginRight="31dp"
        >

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:gravity="top"
            android:textStyle="bold"
            android:layout_marginBottom="@dimen/margin_10"
            />

        <TextView
            android:id="@+id/tv_body"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:singleLine="false"
            android:textSize="14sp"
            android:textColor="@android:color/black"
            android:gravity="top"
            />
    </LinearLayout>
</LinearLayout>
