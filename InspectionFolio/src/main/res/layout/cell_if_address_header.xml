<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:paddingHorizontal="@dimen/margin_20"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_address_name"
        android:textStyle="bold"
        android:textSize="17sp"
        android:textColor="@color/placeholder_color"
        android:layout_marginTop="@dimen/margin_10"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <TextView
        android:id="@+id/tv_address"
        android:textStyle="bold"
        android:textSize="17sp"
        android:layout_marginTop="@dimen/margin_10"
        android:textColor="@android:color/black"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/margin_20"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/tv_info_name"
            android:layout_weight="1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:textStyle="bold"
            android:textSize="17sp"
            android:textColor="@color/placeholder_color"
            />
        <Button
            android:id="@+id/btn_action"
            android:textSize="14sp"
            android:textColor="@color/btn_text_blue_color"
            android:background="@color/transparent"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"/>
    </LinearLayout>
</LinearLayout>