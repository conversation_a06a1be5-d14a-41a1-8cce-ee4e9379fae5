<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageButton
            android:id="@+id/buttonBack"
            android:layout_width="@dimen/button_min_height"
            android:layout_height="@dimen/button_min_height"
            android:src="@drawable/icon_nav_back"
            android:visibility="visible"
            android:tint="@color/light_blue_color"
            android:background="@null"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:contentDescription="@string/button_nav_back"/>
        <TextView
            android:id="@+id/dialogTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/title_choose_inspection_type"
            android:textSize="@dimen/text_h5"
            android:paddingVertical="@dimen/margin_16"
            android:textColor="?android:textColorPrimary"
            android:textColorHint="?android:textColorSecondary"
            android:textAlignment="center"
            android:minLines="1"
            android:layout_centerInParent="true"/>
    </RelativeLayout>

    <EditText
        android:id="@+id/editTextSearch"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:hint="@string/search"
        android:paddingHorizontal="@dimen/margin_20"
        android:layout_marginHorizontal="@dimen/margin_small"
        android:background="@drawable/bg_search_bar_gray_border"
        android:textSize="16sp"
        style="@style/EditText"
        android:imeOptions="actionSearch"
        android:drawableStart="@drawable/icn_search"
        android:drawablePadding="8dp"
        android:layout_marginBottom="@dimen/margin_smallest"
        android:inputType="textVisiblePassword"/>

    <ListView
        android:id="@+id/listViewInspectionTypes"
        android:layout_width="match_parent"
        android:divider="@null"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <View android:layout_width="match_parent"
          android:layout_height="0.5dp"
          android:background="@color/gray_color_9C9C9C"/>

    <Button
        android:id="@+id/buttonCancel"
        android:background="@color/transparent"
        android:textColor="@color/light_blue_color"
        android:textSize="@dimen/text_h3"
        android:height="@dimen/button_min_height"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/action_cancel"
        android:layout_marginTop="@dimen/margin_smallest"/>

</LinearLayout>