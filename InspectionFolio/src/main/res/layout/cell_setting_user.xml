<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/icon_user_avatar"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_marginLeft="@dimen/margin_20"
            android:layout_marginTop="25dp"
            android:layout_marginBottom="25dp"
            android:layout_marginRight="17dp"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginRight="@dimen/margin_10"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_user_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/margin_20"
                android:layout_marginBottom="2dp"
                android:textSize="17sp"
                android:textColor="@color/light_gray"
                android:textStyle="bold"
                />
            <TextView
                android:id="@+id/tv_user_company"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/margin_20"
                android:textSize="17sp"
                android:textStyle="bold"
                android:textColor="@color/light_gray"
                android:layout_marginBottom="2dp"
                />
            <TextView
                android:id="@+id/tv_user_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="@dimen/margin_20"
                android:textColor="@color/light_gray"
                android:textSize="12sp"
                />

        </LinearLayout>

    </LinearLayout>
</LinearLayout>