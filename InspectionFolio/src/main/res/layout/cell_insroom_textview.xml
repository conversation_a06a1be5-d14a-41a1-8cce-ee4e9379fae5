<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" android:weightSum="10"
    android:orientation="horizontal" android:layout_width="match_parent" android:padding="10dp"
    android:layout_height="match_parent">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentLeft="true"
        android:layout_centerVertical="true"
        android:textSize="20dp"
        android:layout_gravity="center_vertical"
        android:layout_weight="9"
        android:singleLine="true"
        android:textColor="#FF000000"
        android:id="@+id/tv_InsRoom_Name"  android:focusable="false" />
    <LinearLayout
        android:layout_height="wrap_content" android:gravity="right" android:layout_gravity="center_vertical"
        android:layout_width="wrap_content" android:layout_weight="1" >
        <ImageView
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"

            android:id="@+id/img_insroom_status"
            />
        </LinearLayout>

</LinearLayout>