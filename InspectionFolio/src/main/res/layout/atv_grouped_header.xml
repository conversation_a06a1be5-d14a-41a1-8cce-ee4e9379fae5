<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/atv_grouped_section_header_footer_min_height" >

    <com.nakardo.atableview.uikit.UILabel
        android:id="@+id/textLabel"
        style="@style/Widget.ATableView.Section.Grouped.Header.TextLabel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone" />
    <Button android:layout_width="wrap_content" android:layout_gravity="right" android:height="10dp" android:visibility="gone"
        android:textColor="@color/intercom_main_blue" android:background="@android:color/transparent"
        android:layout_marginTop="-15dp"
         android:text="Edit" android:layout_height="wrap_content" android:id="@+id/btn_HeaderAction" />
</merge>