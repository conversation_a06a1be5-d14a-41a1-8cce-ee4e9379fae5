<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <LinearLayout
        android:id="@+id/layer_asset"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:paddingVertical="@dimen/margin_small">
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="22dp"
            android:layout_marginEnd="@dimen/margin_default"
            android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <LinearLayout
                    android:id="@+id/view_inspection_status"
                    android:layout_marginTop="@dimen/margin_small"
                    android:layout_marginEnd="@dimen/margin_small"
                    android:layout_width="6dp"
                    android:layout_height="6dp"
                    android:orientation="horizontal" />
                <TextView
                    android:id="@+id/list_exist_asset_child_layout_title"
                    android:textColor="#4A4A4A"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textSize="14sp"
                    android:gravity="center_vertical"/>
            </LinearLayout>


            <TextView
                android:layout_marginStart="14dp"
                android:id="@+id/list_exist_asset_child_layout_details"
                android:textColor="#9B9B9B"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="13sp"
                android:textStyle="italic"
                android:gravity="center_vertical"/>

            <TextView
                android:layout_marginStart="14dp"
                android:id="@+id/list_exist_asset_child_layout_inspector"
                android:visibility="gone"
                android:textColor="#9B9B9B"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="13sp"
                android:textStyle="italic"
                android:gravity="center_vertical"/>
        </LinearLayout>


        <RelativeLayout
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginEnd="@dimen/margin_small"
            android:layout_gravity="center_vertical"
            android:id="@+id/list_exist_asset_child_layout_btn_expand_upload"
            android:visibility="gone">
            <ImageView
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:src="@drawable/icon_upload"
                android:id="@+id/btn_Inspection_Action_ExistAsset"
                android:layout_centerInParent="true"/>
        </RelativeLayout>


        <RelativeLayout
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginEnd="@dimen/margin_small"
            android:layout_gravity="center_vertical"
            android:id="@+id/list_exist_asset_child_layout_btn_expand">
            <ImageView
                android:id="@+id/list_exist_asset_child_layout_expand_iv"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_centerInParent="true"
                android:src="@drawable/collpase"
                android:layout_marginLeft="6dp"
                android:layout_marginRight="6dp"
                android:focusable="false"
                android:focusableInTouchMode="false" />
            <com.snapinspect.snapinspect3.views.CircularTextView
                android:id="@+id/list_exist_asset_child_layout_expand_iv_textview"
                android:layout_width="35dp"
                android:layout_height="35dp"
                android:gravity="center"
                android:visibility="visible"
                android:layout_gravity="center_vertical"
                android:layout_marginLeft="3dp"
                android:layout_centerVertical="true"
                android:layout_marginRight="3dp" />
        </RelativeLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_no_inspection"
        android:layout_marginStart="28dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#4A4A4A"
        android:textSize="14sp"
        android:gravity="center_vertical"/>

</RelativeLayout>