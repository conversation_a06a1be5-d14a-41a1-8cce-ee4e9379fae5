<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:background="@android:color/white"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="vertical"
        android:layout_gravity="center_vertical"
        android:gravity="center_horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_title"
            android:text="@string/title_projects_empty_data"
            android:textStyle="bold"
            android:textColor="@color/dark_gray"
            android:textSize="@dimen/text_small"
            android:gravity="center_horizontal"
            android:layout_marginHorizontal="@dimen/margin_30"
            android:layout_marginBottom="@dimen/margin_small"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_description"
            android:text="@string/description_projects_empty_data"
            android:textColor="@color/dark_gray"
            android:textSize="@dimen/text_small"
            android:gravity="center_horizontal"
            android:lineSpacingMultiplier="1.1"
            android:layout_marginHorizontal="@dimen/margin_30"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <Button
            android:id="@+id/btn_learn_more"
            android:text="@string/button_learn_more"
            android:background="@color/transparent"
            android:textAllCaps="false"
            android:textColor="@color/colorPrimary"
            android:textSize="@dimen/text_small"
            android:minHeight="@dimen/button_min_height"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <ImageView
            android:id="@+id/iv_screenshot"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:scaleType="fitXY"
            android:src="@drawable/ic_screenshot_no_active_projects" />

    </LinearLayout>

</FrameLayout>