<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activitynew.Photo.if_DisplayAllPhotos">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?android:attr/actionBarSize"
        android:background="@android:color/white"
        android:paddingBottom="49dp"
        android:orientation="vertical">

        <GridView
            android:id="@+id/grid_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:verticalSpacing="10dp"
            android:horizontalSpacing="10dp"
            android:paddingHorizontal="@dimen/margin_10"
            android:layout_marginTop="@dimen/margin_10"
            android:layout_marginBottom="@dimen/margin_10"
            android:stretchMode="columnWidth"
            />
    </LinearLayout>

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="49dp"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        >
        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_inspection_bottom"
            />
        <ImageButton
            android:id="@+id/btn_Delete_Photo"
            android:src="@drawable/icon_trash_can_red"
            android:contentDescription="@string/btn_action_desc"
            android:background="@color/transparent"
            android:layout_width="44dp"
            android:layout_height="match_parent"
            android:layout_gravity="right"
            />
        <TextView
            android:id="@+id/tv_bottom_info"
            android:textColor="@color/dark_gray"
            android:textSize="14sp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            />
    </FrameLayout>

</FrameLayout>