<?xml version="1.0" encoding="utf-8"?>
<merge
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/view_notes_cell_layer"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <RelativeLayout
            android:id="@+id/view_head_indicator"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_margin="@dimen/margin_small">
            <com.snapinspect.snapinspect3.util.CircleView
                android:id="@+id/name_bg_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
            <TextView
                android:id="@+id/view_note_cell_short_name"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:textSize="17sp"
                android:textColor="@android:color/white"/>
        </RelativeLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="56dp"
            android:layout_marginRight="@dimen/margin_small"
            android:orientation="vertical">
            <TextView
                android:id="@+id/view_note_cell_tv_name"
                android:layout_width="wrap_content"
                android:layout_height="30dp"
                android:gravity="center_vertical"
                android:textColor="@android:color/black"
                android:textSize="14sp"
                android:layout_marginBottom="@dimen/margin_smallest"
                android:textStyle="bold"/>
            <LinearLayout
                android:id="@+id/view_desc"
                android:orientation="horizontal"
                android:layout_marginBottom="@dimen/margin_smallest"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <FrameLayout
                    android:layout_width="0dp"
                    android:layout_weight="1"
                    android:layout_height="wrap_content">
                    <TextView
                        android:visibility="gone"
                        android:id="@+id/view_note_cell_tv_desc"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:textSize="13sp"
                        android:textColor="@android:color/black"/>
                    <ImageView
                        android:visibility="gone"
                        android:id="@+id/view_note_cell_iv_comment"
                        android:layout_width="120dp"
                        android:layout_height="90dp"
                        android:scaleType="fitCenter"/>
                </FrameLayout>
                <TextView
                    android:id="@+id/view_note_cell_tv_time"
                    android:textAlignment="center"
                    android:layout_width="65dp"
                    android:layout_height="20dp"
                    android:gravity="center_vertical"
                    android:textColor="@color/details_color"
                    android:textSize="13sp"
                    android:textStyle="bold"/>
            </LinearLayout>


        </LinearLayout>
    </FrameLayout>
</merge>
