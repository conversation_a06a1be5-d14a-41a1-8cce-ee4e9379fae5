<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/more_light_gray"
    >
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/txt_SettingTitle"
            android:layout_width="0dip"
            android:layout_height="wrap_content"
            android:textColor="@color/dark_gray"
            android:textSize="14sp"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="12dp"
            android:layout_marginLeft="@dimen/margin_30"
            android:layout_marginStart="@dimen/margin_30"
            android:layout_marginRight="@dimen/margin_10"
            android:layout_marginEnd="@dimen/margin_10"
            android:layout_weight="9"
            />

        <CheckBox
            android:id="@+id/tb_Setting"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginRight="40dp"
            android:button="@drawable/setting_option_radio_button"
            />

        <ImageView
            android:id="@+id/icon_option_arrow"
            android:src="@drawable/icon_option_right_arrow"
            android:layout_width="53dp"
            android:layout_height="25dp"
            android:layout_marginRight="23dp"
            android:layout_marginEnd="23dp"
            android:scaleType="center"
            android:visibility="gone"
            />

        <Button
            android:id="@+id/tb_normal"
            android:textSize="11dp"
            android:textColor="@android:color/white"
            android:layout_width="64dp"
            android:layout_height="25dp"
            android:layout_marginEnd="23dp"
            android:background="@drawable/bg_setting_fixed_rotation"
            android:visibility="gone"
        />
    </LinearLayout>
</LinearLayout>