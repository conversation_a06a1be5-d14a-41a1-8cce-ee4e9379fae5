<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@android:color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_20"
        android:layout_marginBottom="8dp"
        android:layout_marginHorizontal="@dimen/margin_25">
        <TextView
            android:id="@+id/txt_form_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/asset_info_title"
            android:textColor="@color/placeholder_color"
            />
        <TextView
            android:id="@+id/txt_form_item_required"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/item_required_title_color"
            android:textSize="12sp"
            android:textStyle="bold|italic"
            android:layout_marginHorizontal="@dimen/margin_10"
            android:layout_weight="1" />
    </LinearLayout>

    <com.nex3z.flowlayout.FlowLayout
        android:id="@+id/tags_flow_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="@dimen/margin_25"
        app:flChildSpacing="@dimen/margin_small"
        app:flRowSpacing="@dimen/margin_small" />

</LinearLayout>