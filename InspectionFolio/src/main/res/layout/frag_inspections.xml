<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:background="@color/colorPrimary"
    xmlns:android="http://schemas.android.com/apk/res/android" >
    <LinearLayout
        android:orientation="vertical"
        android:layout_height="match_parent"
        android:layout_width="match_parent">
        <com.snapinspect.snapinspect3.views.SegmentedControl
            android:id="@+id/segmentedControl"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="@dimen/margin_16"
            android:layout_marginRight="@dimen/margin_16"
            android:layout_marginTop="@dimen/margin_small"
            android:layout_marginBottom="@dimen/margin_small"/>

        <EditText android:id="@+id/search_inspection"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="@dimen/margin_16"
            android:layout_marginRight="@dimen/margin_16"
            android:layout_marginBottom="@dimen/margin_small"
            android:hint="@string/search"
            android:paddingLeft="@dimen/margin_16"
            android:background="@drawable/bg_search_bar"
            android:textSize="16dp"
            style="@style/EditText"
            android:imeOptions="actionSearch"
            android:drawableLeft="@drawable/icn_search"
            android:drawablePadding="8dp"
            android:inputType="textVisiblePassword"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:paddingBottom="58dp">
            <ListView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/lv_Inspections"
                android:divider="@null">
            </ListView>
            <RelativeLayout
                android:visibility="gone"
                android:id="@+id/dead_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/white">
                <LinearLayout
                    android:layout_centerInParent="true"
                    android:layout_width="300dp"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center_horizontal">
                    <ImageView
                        android:id="@+id/btn_add_new"
                        android:layout_width="80dp"
                        android:layout_height="67dp"
                        android:scaleType="center"
                        android:src="@drawable/icn_add_ins"/>
                    <TextView
                        android:id="@+id/tv_none"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="It looks like you haven’t started an inspection yet. Go to 'Assets' tab to start a new inspection."
                        android:gravity="center"
                        android:textColor="#4A4A4A"
                        android:textSize="14sp"/>
                </LinearLayout>
            </RelativeLayout>
        </RelativeLayout>

    </LinearLayout>


</RelativeLayout>
