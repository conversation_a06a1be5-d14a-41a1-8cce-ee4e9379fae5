<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drag_layout"
    android:orientation="horizontal" android:layout_width="match_parent" android:layout_gravity=""
    android:layout_height="45dp" android:minHeight="45dp">
    <ImageButton
        android:layout_height="35dp"
        android:layout_width="35dp"
        android:layout_marginLeft="8dp"
        android:padding="5dp"
        android:id="@+id/btn_DeleteRoom"
        android:layout_gravity="center_vertical"
        android:src="@drawable/red_minus"
        android:background="@color/transparent"/>
    <EditText
        android:id="@+id/txt_RoomName"
        android:layout_marginLeft="8dp"
        android:padding="4dp"
        android:layout_height="30dp"
        android:focusable="true"
        android:editable="true"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:inputType="text"
       style="@style/EditTextTheme"
        android:imeOptions="actionDone"
        android:background="@drawable/bg_edittext"
        android:layout_gravity="center_vertical"
        android:layout_width="0dp"
        android:layout_marginRight="15dp"
        android:layout_weight="1"/>

    <ImageView
        android:id="@+id/order_handler"
        android:padding="7dp"
        android:layout_width="40dp"
        android:layout_height="30dp"
        android:layout_gravity="center_vertical"
        android:scaleType="fitXY"
        android:src="@drawable/ic_hamburger"
        tools:ignore="ContentDescription" />

</LinearLayout>