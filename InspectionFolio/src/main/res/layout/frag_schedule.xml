<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:background="@color/colorPrimary">
        <info.hoang8f.android.segmented.SegmentedGroup
                xmlns:segmentedgroup="http://schemas.android.com/apk/res-auto"
                android:id="@+id/segmented2"
                android:layout_width="match_parent"
                android:layout_height="35dp"
                android:orientation="horizontal"
                segmentedgroup:sc_border_width="1dp"
                segmentedgroup:sc_corner_radius="10dp"
                android:layout_marginLeft="15dp"
                android:layout_marginRight="15dp"
                segmentedgroup:sc_tint_color="#ffffff"
                segmentedgroup:sc_checked_text_color="@color/colorPrimary">

            <RadioButton
                    android:id="@+id/listBtn"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:text="List"
                    style="@style/RadioButton"/>

            <RadioButton
                    android:id="@+id/mapBtn"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="0.5"
                    android:text="Map"
                    style="@style/RadioButton"/>
        </info.hoang8f.android.segmented.SegmentedGroup>
    </LinearLayout>

    <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1">

        <com.google.android.gms.maps.MapView
            android:id="@+id/map"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ListView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/white"
                android:id="@+id/lv_Schedules"
                android:divider="#CCCCCC"
                android:dividerHeight="2px">
        </ListView>

    </RelativeLayout>

</LinearLayout>