<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:background="@color/dark_night"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.theartofdev.edmodo.cropper.CropImageView
        android:id="@+id/cropImageView"
        app:cropAutoZoomEnabled="true"
        app:cropGuidelines="onTouch"
        app:cropAspectRatioX="1"
        app:cropAspectRatioY="1"
        app:cropInitialCropWindowPaddingRatio="0"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"/>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="@dimen/toolbar_height">
        <Button
            android:id="@+id/cancel_button"
            android:text="@string/menu_cancel"
            android:background="@color/transparent"
            android:textColor="@color/white_color"
            android:paddingHorizontal="@dimen/margin_10"
            android:textAllCaps="false"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent" />

        <ImageButton
            android:id="@+id/btn_rotate_left"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            android:src="@drawable/ic_rotate_left"
            android:contentDescription="@string/rotate_left"/>

        <ImageButton
            android:id="@+id/btn_rotate_right"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            android:src="@drawable/ic_rotate_right"
            android:contentDescription="@string/rotate_right"/>

        <Button
            android:id="@+id/done_button"
            android:text="@string/menu_done"
            android:textStyle="bold"
            android:background="@color/transparent"
            android:textColor="@color/white_color"
            android:textAllCaps="false"
            android:paddingHorizontal="@dimen/margin_10"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="match_parent" />
    </LinearLayout>

</LinearLayout>