<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">
    <LinearLayout
        android:layout_marginTop="@dimen/margin_smallest"
        android:layout_marginBottom="@dimen/margin_default"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_marker"/>
        <LinearLayout

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">
            <TextView
                android:textColor="@android:color/black"
                android:id="@+id/tv_address_one"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"/>
            <TextView
                android:id="@+id/tv_address_two"
                android:textColor="@android:color/black"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"/>
        </LinearLayout>
        <ImageView
            android:id="@+id/btn_edit"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_edit"
            android:layout_marginLeft="@dimen/margin_small"
            android:layout_marginRight="@dimen/margin_small"/>

    </LinearLayout>

    <LinearLayout
        android:layout_marginTop="@dimen/margin_smallest"
        android:layout_marginBottom="@dimen/margin_default"

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_key"/>
        <TextView
            android:id="@+id/tv_key"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:layout_marginRight="@dimen/margin_small"/>
    </LinearLayout>
    <LinearLayout
        android:layout_marginTop="@dimen/margin_smallest"
        android:layout_marginBottom="@dimen/margin_default"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_home_alarm"/>
        <TextView
            android:id="@+id/tv_alarm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@android:color/black"
            android:layout_marginRight="@dimen/margin_small"/>
    </LinearLayout>

</LinearLayout>
