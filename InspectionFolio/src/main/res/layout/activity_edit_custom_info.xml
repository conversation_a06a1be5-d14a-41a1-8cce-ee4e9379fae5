<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".activitynew.Edit.if_EditCustomInfo"
    android:background="@color/colorPrimary"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">
        <ListView
            android:id="@+id/lv_formItems"
            android:background="@android:color/white"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:divider="@color/transparent"
            android:paddingBottom="82dp"
            android:clipToPadding="false"
            />
    </LinearLayout>

    <FrameLayout
        android:id="@+id/btn_bottom_container"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_button_bottom"
        android:layout_marginBottom="11dp"
        android:layout_marginLeft="15dp"
        android:layout_marginStart="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginEnd="15dp">

        <Button
            android:id="@+id/btn_bottom"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            android:textColor="@android:color/white"
            android:textSize="17sp"
            android:textStyle="bold"
            android:text="@string/save_changes"
            />
    </FrameLayout>
</FrameLayout>