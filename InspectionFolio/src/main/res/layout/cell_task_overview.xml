<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:layout_marginVertical="@dimen/margin_10"
        android:layout_marginHorizontal="@dimen/margin_16"
        android:orientation="vertical">
        <TextView
            android:id="@+id/txt_sInsTitle"
            android:textColor="@color/task_item_text"
            android:textSize="12sp"
            android:textStyle="bold"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            />
        <TextView
            android:id="@+id/txt_sName"
            android:textColor="@android:color/black"
            android:textSize="15sp"
            android:textStyle="bold"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_small_mid"
            />
        <TextView
            android:id="@+id/txt_sDescription"
            android:textColor="@android:color/black"
            android:textSize="12sp"
            android:textStyle="bold"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_small_mid"
            />

        <LinearLayout
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/margin_small"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.snapinspect.snapinspect3.views.RoundTextView
                android:id="@+id/txt_NoticeCategory"
                android:textColor="@android:color/black"
                android:textSize="12sp"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/margin_24"
                android:paddingHorizontal="@dimen/margin_16"
                android:layout_marginEnd="@dimen/margin_10"
                />
            <com.snapinspect.snapinspect3.views.RoundTextView
                android:id="@+id/txt_PriorityName"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:gravity="center"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/margin_24"
                android:paddingHorizontal="@dimen/margin_16"
                />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:layout_marginEnd="@dimen/margin_16"
        android:orientation="vertical">

        <TextView
            android:id="@+id/txt_NoticeDue"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="#696969"
            android:textSize="10sp"
            android:textStyle="normal"
            tools:ignore="SmallSp" />

        <com.snapinspect.snapinspect3.views.RoundTextView
            android:id="@+id/txt_assign_to"
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:layout_marginTop="@dimen/margin_small_mid"
            android:gravity="center"
            android:textAllCaps="true"
            android:textColor="#606060"
            android:textSize="15sp"
            android:textStyle="bold"
            />
    </LinearLayout>
</LinearLayout>

