<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:id="@+id/framelayout_existasset"
    tools:context=".activitynew.if_DisplayFloorPlan">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?android:attr/actionBarSize"
        tools:ignore="UselessParent"
        android:orientation="vertical">
        <include
            android:id="@+id/inspection_item_title_view"
            android:visibility="gone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            layout="@layout/view_inspection_item_titles" />

        <WebView
            android:id="@+id/webview_floor_plan"
            android:background="@color/white_color"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />
    </LinearLayout>
</FrameLayout>