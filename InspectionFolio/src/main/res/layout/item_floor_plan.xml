<?xml version="1.0" encoding="utf-8"?>
<com.snapinspect.snapinspect3.views.FloorPlanView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:gravity="center_horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="@dimen/margin_smallest"
        android:id="@+id/container"
        app:layout_constraintDimensionRatio="1.414:1"
    >
        <ImageView
            android:id="@+id/floor_plan_image"
            android:scaleType="centerCrop"
            android:padding="@dimen/margin_smallest"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:contentDescription="@string/floor_plan_image_desc"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/floor_plan_name"
        android:textStyle="italic|bold"
        android:lines="1"
        android:textSize="@dimen/text_normal"
        android:layout_marginTop="@dimen/margin_small"
        app:layout_constraintTop_toBottomOf="@id/container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

    <FrameLayout
        android:id="@+id/indicator_view"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/container"
        app:layout_constraintEnd_toEndOf="@+id/container"
        app:layout_constraintTop_toTopOf="@+id/container"
        app:layout_constraintBottom_toBottomOf="@+id/container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        >
        <ProgressBar
            style="?android:attr/progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:indeterminate="true" />
    </FrameLayout>

</com.snapinspect.snapinspect3.views.FloorPlanView>