<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:orientation="vertical"
        android:layout_marginStart="@dimen/margin_16"
        android:layout_marginVertical="@dimen/margin_10"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_title"
            android:textStyle="bold"
            android:textSize="@dimen/text_normal"
            android:textColor="@android:color/black"
            android:layout_marginBottom="@dimen/margin_smallest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <TextView
            android:id="@+id/tv_ref"
            android:textSize="@dimen/text_small"
            android:textColor="@color/dark_gray"
            android:layout_marginBottom="@dimen/margin_smallest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:text="@string/title_project_manager"
                android:textStyle="bold"
                android:textSize="@dimen/text_small"
                android:textColor="@color/dark_gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <TextView
                android:id="@+id/tv_manager"
                android:textSize="@dimen/text_small"
                android:textColor="@color/dark_gray"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content" />
        </LinearLayout>
    </LinearLayout>

    <TextView
        android:id="@+id/tv_progress"
        android:layout_gravity="center_vertical"
        android:gravity="center"
        android:layout_marginStart="@dimen/margin_10"
        android:layout_marginEnd="@dimen/margin_16"
        android:paddingHorizontal="@dimen/margin_16"
        android:minWidth="@dimen/button_min_width"
        android:minHeight="@dimen/btn_size_30"
        android:textSize="@dimen/text_small"
        android:textColor="@android:color/white"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content" />

</LinearLayout>