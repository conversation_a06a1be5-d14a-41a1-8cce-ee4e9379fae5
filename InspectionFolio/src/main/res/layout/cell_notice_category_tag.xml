<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_notice_cat_tag_normal">

    <ImageView
        android:id="@+id/tag_selected_icon"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_marginStart="9dp"
        android:src="@drawable/check_mark_small_icon" />

    <TextView
        android:id="@+id/tag_category_name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginEnd="@dimen/margin_10"
        android:layout_marginVertical="@dimen/margin_small_mid"
        android:padding="0dp"
        android:background="@color/transparent"
        />

</FrameLayout>


