<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:id="@+id/view_top_info"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary">
        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_10"
            android:layout_marginLeft="@dimen/margin_20"
            android:layout_marginStart="@dimen/margin_20"
            android:layout_marginRight="@dimen/margin_20"
            android:layout_marginEnd="@dimen/margin_20"
            android:layout_marginBottom="@dimen/margin_10">
            <ImageButton
                android:id="@+id/btn_instruction_tip"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_ins_item_info"
                android:background="@color/transparent"
                android:layout_marginTop="2dp"
                android:layout_marginRight="@dimen/margin_10"
                android:layout_marginEnd="@dimen/margin_10" />
            <TextView
                android:id="@+id/tv_ins_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textSize="18sp"
                android:textColor="@android:color/white"
                android:textStyle="bold" />
        </LinearLayout>
        <TextView
            android:id="@+id/tv_instruction"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="12sp"
            android:textColor="@android:color/white"
            android:layout_marginLeft="@dimen/margin_20"
            android:layout_marginStart="@dimen/margin_20"
            android:layout_marginRight="@dimen/margin_20"
            android:layout_marginEnd="@dimen/margin_20"
            android:layout_marginBottom="@dimen/margin_10" />
        <View
            android:layout_width="match_parent"
            android:layout_height="15dp"
            android:background="@drawable/bg_first_ins_item" />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/view_Full"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    </LinearLayout>

    <LinearLayout
        android:id="@+id/view_footer_info"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        android:orientation="vertical"
        android:visibility="gone"
        />
</LinearLayout>