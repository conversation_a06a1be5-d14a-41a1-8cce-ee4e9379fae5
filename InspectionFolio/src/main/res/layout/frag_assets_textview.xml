<?xml version="1.0" encoding="utf-8"?>

<!-- IMPORT NOTES, THIS xml is for Frag_Assets, and if_selectrating.  -->
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:minHeight="50dp"
    android:layout_height="match_parent">

    <LinearLayout
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="@dimen/margin_small"
        android:layout_marginBottom="@dimen/margin_small"
        android:layout_marginLeft="@dimen/margin_default">

        <TextView
            android:id="@+id/txt_AssetAddress"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:singleLine="false"
            android:textSize="14sp"
            android:textColor="@android:color/black"/>

        <TextView
            android:id="@+id/txt_AssetAddress_Info"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            android:singleLine="false"
            android:textColor="#447aff"
            android:textStyle="italic"
            android:minHeight="1dp"
            android:textSize="13sp"/>

    </LinearLayout>

    <ImageView
        android:layout_gravity="center_vertical"
        android:layout_marginLeft="@dimen/margin_small"
        android:layout_marginRight="@dimen/margin_16"
        android:layout_width="14dp"
        android:layout_height="14dp"
        android:src="@drawable/arrow_right"/>

</LinearLayout>
