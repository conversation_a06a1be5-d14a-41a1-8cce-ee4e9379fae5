<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:tools="http://schemas.android.com/tools"
android:layout_width="match_parent"
android:layout_height="match_parent"
android:background="@color/colorPrimary"
tools:context=".activity.if_MapDetails">
<LinearLayout
    android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_marginTop="?android:actionBarSize"
    android:layout_height="fill_parent"
    android:background="@android:color/white"
    android:layout_gravity="center_horizontal|top">
    <fragment
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:id="@+id/map"
        android:name="com.google.android.gms.maps.MapFragment"/>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <Button
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:id="@+id/routeBtn"
            android:background="#70de6f"
            android:text="Route"
            android:textColor="#ffffff"
            android:textSize="18sp"
            android:layout_marginTop="5dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp" />
        <Button
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:id="@+id/inspectionBtn"
            android:background="#FFFF5F42"
            android:text="Start Inspection"
            android:textColor="#ffffff"
            android:textSize="18sp"
            android:layout_marginTop="5dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="60dp"
            android:orientation="vertical"
            android:layout_marginTop="5dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:text="Address"
                android:id="@+id/textView4"
                android:textColor="#136ff8" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:text=""
                android:id="@+id/addText"
                android:textColor="#141515"
                android:layout_marginLeft="5dp"/>
        </LinearLayout>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:orientation="vertical"
            android:layout_marginTop="5dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:id="@+id/estimatedLayout">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:text="Estimated"
                android:id="@+id/textView5"
                android:textColor="#136ff8" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:text=""
                android:id="@+id/distanceText"
                android:textColor="#141515"
                android:layout_marginLeft="5dp"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:text=""
                android:id="@+id/durationText"
                android:textColor="#141515"
                android:layout_marginLeft="5dp"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:orientation="vertical"
            android:layout_marginTop="5dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="10dp"
            android:id="@+id/scheduleLayout">

            <TextView
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:text="Schedule"
                android:id="@+id/textView6"
                android:textColor="#136ff8" />
            <TextView
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:text=""
                android:id="@+id/dateText"
                android:textColor="#141515"
                android:layout_marginLeft="5dp"/>
            <TextView
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:textAppearance="?android:attr/textAppearanceMedium"
                android:text=""
                android:id="@+id/typeText"
                android:textColor="#141515"
                android:layout_marginLeft="5dp"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
</FrameLayout>
