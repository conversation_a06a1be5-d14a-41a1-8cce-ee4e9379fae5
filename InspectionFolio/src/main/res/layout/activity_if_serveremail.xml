<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_serveremail">
<LinearLayout
    android:layout_marginTop="?android:attr/actionBarSize"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="#ffffff">
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_weight="0"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:background="#ffffff">

        <TextView
            android:layout_width="45dp"
            android:layout_height="match_parent"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:text="@string/to"
            android:id="@+id/toText"
            android:gravity="center_vertical"
            android:textColor="#909090" />

        <EditText
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:inputType="textMultiLine"
            android:ems="10"
            android:id="@+id/title_id"
            android:layout_weight="1"
            android:textColor="#007aff"
            android:background="#00ffffff" />

    </LinearLayout>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_weight="0"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:background="#636161">
        </LinearLayout>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_weight="0"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:background="#ffffff">
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:text="@string/subject"
            android:id="@+id/textView3"
            android:gravity="center_vertical"
            android:textColor="#909090"
            android:layout_weight="0" />

        <EditText
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:inputType="textMultiLine"
            android:ems="10"
            android:id="@+id/subject_id"
            android:textColor="#000000"
            android:background="#00ffffff"
            android:gravity="top|left"
            android:layout_weight="1" />
    </LinearLayout>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_weight="0"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:background="#636161">
    </LinearLayout>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_weight="1"
        android:layout_alignParentTop="true"
        android:layout_centerHorizontal="true"
        android:background="#ffffff">

        <EditText
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:inputType="textMultiLine"
            android:ems="10"
            android:id="@+id/body_id"
            android:layout_weight="1"
            android:textColor="#000000"
            android:gravity="top|left" />
    </LinearLayout>
</LinearLayout>
</RelativeLayout>
