<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             xmlns:tools="http://schemas.android.com/tools"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
             android:background="@color/colorPrimary"
             tools:context=".activity.if_login">

    <LinearLayout
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:gravity="center_horizontal">
        <RelativeLayout
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_weight="2">


            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:layout_centerInParent="true"
                android:orientation="vertical">
                <ImageView
                    android:id="@+id/image_view_logo"
                    android:layout_width="60dp"
                    android:layout_height="60dp"
                    android:src="@drawable/logo"
                    android:contentDescription="@string/prompt_app_logo"/>
                <TextView
                    android:layout_height="40dp"
                    android:layout_width="wrap_content"
                    android:id="@+id/AppTitle"
                    android:textColor="@android:color/white"
                    android:gravity="center_horizontal|center_vertical"
                    android:textSize="30sp"
                    android:text="@string/AppName"/>
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="40dp"
                    android:text="@string/app_intro"
                    android:textSize="13sp"
                    android:textColor="@android:color/white"
                    android:gravity="center"/>
            </LinearLayout>


        </RelativeLayout>
        <LinearLayout
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            android:orientation="vertical"
            android:layout_weight="1.3"
            android:gravity="center_horizontal">

            <LinearLayout
                android:layout_width="260dp"
                android:layout_height="90dp"
                android:orientation="vertical"
                android:background="@drawable/bg_white_edittext">
                <EditText
                    android:layout_height="0dp"
                    android:layout_weight="0.5"
                    android:layout_width="match_parent"
                    android:hint="@string/Email"
                    android:inputType="textEmailAddress"
                    style="@style/EditText"
                    android:lines="1"
                    android:layout_marginStart="@dimen/margin_small"
                    android:id="@+id/edit_Email"/>
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="#979797"/>
                <EditText
                    android:layout_height="0dp"
                    android:layout_weight="0.5"
                    android:layout_width="match_parent"
                    android:layout_marginStart="@dimen/margin_small"
                    android:inputType="textPassword"
                    style="@style/EditText"
                    android:hint="@string/Password"
                    android:lines="1"
                    android:id="@+id/edit_Password"/>
            </LinearLayout>

            <Button android:id="@+id/btn_Login"
                    android:layout_marginTop="@dimen/margin_small"
                    android:layout_width="260dp"
                    android:layout_height="44dp"
                    android:textSize="17sp"
                    android:textColor="@android:color/white"
                    android:text="@string/btn_Login"
                    android:background="@drawable/bg_button_green"/>

            <Button
                android:background="@android:color/transparent"
                android:layout_width="match_parent"
                android:textColor="@android:color/white"
                android:textAlignment="center"
                android:layout_marginTop="20dp"
                android:layout_height="50dp"
                android:text=""
                android:id="@+id/btn_Signup"
                android:layout_gravity="center_horizontal" />

            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
            />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_small"
                android:layout_marginBottom="@dimen/margin_80"
                android:orientation="horizontal">
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">
                    <ImageButton
                        android:id="@+id/btn_Login_google"
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        android:textAllCaps="false"
                        android:src="@drawable/icon_google"
                        android:background="@drawable/bg_sso_button_white"
                        android:contentDescription="@string/btn_Login_google"/>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="@dimen/margin_smallest" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/title_google"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/text_normal"
                        android:layout_gravity="center_horizontal"/>
                </LinearLayout>
                <View android:layout_width="44dp"
                      android:layout_height="0dp" />
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="vertical">
                    <ImageButton
                        android:id="@+id/btn_Login_azure"
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        android:src="@drawable/icon_azure"
                        android:background="@drawable/bg_sso_button_white"
                        android:contentDescription="@string/btn_Login_azure"/>
                    <View
                        android:layout_width="0dp"
                        android:layout_height="@dimen/margin_smallest" />
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/title_azure"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/text_normal"
                        android:layout_gravity="center_horizontal"/>
                </LinearLayout>
            </LinearLayout>
            <Button
                android:id="@+id/btn_qrscan"
                android:layout_width="wrap_content"
                android:paddingHorizontal="@dimen/margin_20"
                android:layout_height="40dp"
                android:text="@string/scan_qr_code"
                android:textColor="@android:color/white"
                android:layout_gravity="center"
                android:background="@android:color/transparent"
                android:layout_marginVertical="@dimen/margin_20"
            />
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:textColor="@android:color/white"
            android:text=""
            android:visibility="gone"
            android:textSize="13sp"
            android:gravity="center"/>
    </LinearLayout>
</FrameLayout>
