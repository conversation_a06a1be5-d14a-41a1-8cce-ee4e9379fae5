<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
>

    <TextView
        android:id="@+id/title_inspection_type"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textAppearance="?android:attr/textAppearanceListItemSmall"
        android:gravity="center"
        android:ellipsize="end"
        android:paddingHorizontal="@dimen/margin_16"
        android:paddingVertical="@dimen/margin_small"
        android:textSize="@dimen/text_h3"
        android:textColor="@color/light_blue_color"
        android:minHeight="@dimen/default_button_height" />

    <ImageButton
        android:id="@+id/disclosureButton"
        android:layout_gravity="center_vertical"
        android:src="@drawable/arrow_right"
        android:background="@drawable/light_gray_circle"
        android:contentDescription="@string/more"
        android:layout_marginStart="@dimen/margin_16"
        android:layout_marginEnd="@dimen/margin_16"
        android:layout_width="@dimen/calendar_tile_height"
        android:layout_height="@dimen/calendar_tile_height" />
</LinearLayout>
