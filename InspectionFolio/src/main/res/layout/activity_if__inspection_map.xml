<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
xmlns:tools="http://schemas.android.com/tools"
android:layout_width="match_parent"
android:layout_height="match_parent"
android:background="@color/colorPrimary"
tools:context=".activity.if_InspectionMap">
<LinearLayout
    android:orientation="vertical"
    android:layout_width="fill_parent"
    android:layout_marginTop="?android:actionBarSize"
    android:layout_height="fill_parent"
    android:background="@android:color/white"
    android:layout_gravity="center_horizontal|top">
    <fragment
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:id="@+id/map"
        android:name="com.google.android.gms.maps.MapFragment"/>

</LinearLayout>
</FrameLayout>
