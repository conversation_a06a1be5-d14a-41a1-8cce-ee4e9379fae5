<?xml version="1.0" encoding="utf-8"?>
<com.snapinspect.snapinspect3.views.InputAccessoryToolbar
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/input_accessory_view"
    android:layout_width="match_parent"
    android:visibility="gone"
    android:layout_height="43dp"
    android:layout_gravity="bottom"
    android:background="@color/colorPrimary"
    android:orientation="horizontal">

    <ImageButton
        android:id="@+id/btn_qrcode"
        android:layout_width="43dp"
        android:layout_height="match_parent"
        android:src="@drawable/icon_toolbar_qrcode"
        android:background="@color/transparent"
        android:contentDescription="@string/scan_qr_code"/>

    <View
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
    />

    <Button
        android:id="@+id/btn_comments_library"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:text="@string/title_activity_if_quickphrase"
        android:background="@color/transparent"
        android:textColor="@android:color/white"
        android:textStyle="bold"
        android:textSize="14sp"
        android:layout_marginEnd="@dimen/margin_20"/>
</com.snapinspect.snapinspect3.views.InputAccessoryToolbar>