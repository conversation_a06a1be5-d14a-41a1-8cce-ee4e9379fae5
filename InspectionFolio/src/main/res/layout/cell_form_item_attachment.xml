<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/white"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_20"
            android:layout_marginBottom="8dp"
            android:layout_marginHorizontal="@dimen/margin_25">
            <TextView
                android:id="@+id/txt_form_item_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                style="@style/asset_info_title"
                />
            <TextView
                android:id="@+id/txt_form_item_required"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:textColor="@color/item_required_title_color"
                android:textSize="12sp"
                android:textStyle="bold|italic"
                android:layout_marginLeft="@dimen/margin_10"
                android:layout_marginStart="@dimen/margin_10"
                android:layout_weight="1"
                />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_10"
            android:layout_marginBottom="8dp"
            android:layout_marginHorizontal="@dimen/margin_25">
            <ImageView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/icon_custom_info_attachment"
                android:layout_gravity="center_vertical" />
            <TextView
                android:id="@+id/txt_form_item_value"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_small_mid"
                android:gravity="center_vertical"
                style="@style/asset_info_title"
                android:textColor="@color/btn_text_blue_color"
                android:textStyle="bold|italic"
                android:layout_marginLeft="@dimen/margin_10"
                android:layout_marginStart="@dimen/margin_10"
                android:layout_weight="1"
                />
        </LinearLayout>
    </LinearLayout>
    <Button
        android:id="@+id/btn_attach_file"
        android:background="@color/transparent"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>
</FrameLayout>