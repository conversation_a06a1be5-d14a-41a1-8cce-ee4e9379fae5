<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_20"
        android:layout_marginBottom="8dp"
        android:layout_marginHorizontal="@dimen/margin_25">
        <TextView
            android:id="@+id/txt_form_item_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/asset_info_title"
            />
        <TextView
            android:id="@+id/txt_form_item_required"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="@color/item_required_title_color"
            android:textSize="12sp"
            android:textStyle="bold|italic"
            android:layout_marginStart="@dimen/margin_10"
            android:layout_weight="1"
            />
    </LinearLayout>

    <EditText
        android:id="@+id/edit_item_value"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minLines="1"
        android:minHeight="40dp"
        android:lineSpacingExtra="@dimen/margin_smallest"
        android:paddingVertical="@dimen/margin_10"
        android:layout_marginHorizontal="@dimen/margin_25"
        android:background="@color/transparent"
        android:textColor="@color/item_edit_text_color"
        android:textSize="14sp" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="1.5dp"
        android:layout_marginRight="@dimen/margin_20"
        android:layout_marginEnd="@dimen/margin_20"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20">
        <View
            android:id="@+id/line_bottom_has_value"
            android:layout_width="match_parent"
            android:layout_gravity="center_horizontal"
            android:background="@color/item_edited_line_color"
            android:layout_height="match_parent"/>
        <View
            android:id="@+id/line_bottom_no_value"
            android:background="@color/item_gray_line_color"
            android:layout_width="match_parent"
            android:layout_gravity="center_vertical"
            android:layout_height="0.5dp"/>
    </FrameLayout>
</LinearLayout>