<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".activity.if_CameraX"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:id="@+id/top_view"
        android:layout_alignParentTop="true"
        android:layout_alignParentStart="true"
        android:background="#000000"
        android:gravity="center_vertical"
        tools:ignore="MissingConstraints">

        <TextView
            android:id="@+id/inspection_item_name"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:textSize="@dimen/text_h5"
            android:textColor="@color/white_color"
            android:layout_marginStart="@dimen/margin_20" />

        <FrameLayout
            android:id="@+id/libraryBtn"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:onClick="libraryClicked"
            android:layout_marginStart="@dimen/margin_small"
            android:layout_marginEnd="@dimen/margin_small"
            tools:ignore="UsingOnClickInXml">

            <ImageView
                android:layout_width="@dimen/btn_size_30"
                android:layout_height="@dimen/btn_size_30"
                android:layout_gravity="center"
                android:contentDescription="@string/btn_library"
                android:src="@drawable/icn_library"
                />
        </FrameLayout>

        <Button
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:id="@+id/focusBtn"
            android:background="@drawable/a_focus"
            android:layout_marginEnd="20dp"
            android:visibility="gone"/>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/middle_view"
        app:layout_constraintTop_toBottomOf="@+id/top_view"
        app:layout_constraintBottom_toTopOf="@+id/bottom_view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_height="0dp"
        android:layout_width="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

            <View
                android:id="@+id/preview_top_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.camera.view.PreviewView
                    android:id="@+id/viewFinder"
                    app:layout_constraintDimensionRatio="1:1"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                />

                <LinearLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="60dp"
                    android:id="@+id/middle_buttons_view"
                    app:layout_constraintTop_toBottomOf="@+id/viewFinder"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:gravity="center_vertical"
                    tools:ignore="MissingConstraints">

                    <LinearLayout
                        android:id="@+id/lightBtnView"
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        android:gravity="center"
                        android:layout_marginStart="@dimen/margin_small">
                        <ImageView
                            android:layout_width="18dp"
                            android:layout_height="25dp"
                            android:id="@+id/lightBtn"
                            android:src="@drawable/flash_off"
                            android:scaleType="fitXY"
                            android:contentDescription="@string/flash"/>
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/brightBtnView"
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        android:gravity="center"
                        android:layout_marginStart="@dimen/margin_smallest">
                        <ImageView
                            android:layout_width="27dp"
                            android:layout_height="27dp"
                            android:id="@+id/brightBtn"
                            android:src="@drawable/icn_sun"
                            android:contentDescription="@string/btn_adjust_bright"
                            android:scaleType="fitXY"
                        />
                    </LinearLayout>

                    <View android:layout_width="0dp"
                          android:layout_height="0dp"
                          android:layout_weight="1" />

                    <TextView
                        android:id="@+id/zoomLevelText"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:gravity="center"
                        android:textSize="@dimen/text_h5"
                        android:textColor="@color/white_color"
                    />

                    <View android:layout_width="0dp"
                          android:layout_height="0dp"
                          android:layout_marginEnd="30dp"
                          android:layout_weight="1" />

                    <FrameLayout
                        android:id="@+id/switchBtn"
                        android:layout_width="44dp"
                        android:layout_height="44dp"
                        android:layout_marginEnd="@dimen/margin_20">

                        <ImageView
                            android:layout_width="24dp"
                            android:layout_height="23dp"
                            android:layout_gravity="center"
                            android:contentDescription="@string/switch_camera"
                            android:src="@drawable/icn_switchcam"
                        />
                    </FrameLayout>
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
            <View
                android:id="@+id/preview_bottom_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1" />
        </LinearLayout>
        <FrameLayout
            android:id="@+id/seekView"
            android:visibility="gone"
            android:layout_width="200dp"
            android:layout_height="45dp"
            android:background="#000000"
            android:layout_marginStart="50dp"
            tools:ignore="MissingConstraints">
            <SeekBar
                android:id="@+id/seekBar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:progressBackgroundTint="@color/black"
                android:progressTint="@color/intercom_white"
                android:max="100" >
            </SeekBar>
        </FrameLayout>

        <com.snapinspect.snapinspect3.activitynew.camera.FocusView
            android:id="@+id/focusView"
            android:layout_width="@dimen/focus_view_size"
            android:layout_height="@dimen/focus_view_size"
            android:visibility="invisible"
            tools:ignore="MissingConstraints" />
    </FrameLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="120dp"
        android:id="@+id/bottom_view"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:background="#000000">

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/menu_done"
            android:textAllCaps="false"
            android:id="@+id/doneBtn"
            android:textColor="#ffffff"
            android:background="#00ffff00"
            android:onClick="doneClicked"
            android:layout_toStartOf="@+id/shutterBtn"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"
            tools:ignore="UsingOnClickInXml" />

        <Button
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:id="@+id/shutterBtn"
            android:background="@drawable/shutter_normal"
            android:layout_centerVertical="true"
            android:layout_centerHorizontal="true"
            android:onClick="shutterClicked"
            android:contentDescription="@string/btn_shutter"
            tools:ignore="UsingOnClickInXml" />

        <com.github.ybq.android.spinkit.SpinKitView
            android:id="@+id/loading"
            style="@style/SpinKitView.Circle"
            android:layout_width="55dp"
            android:layout_height="55dp"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            app:SpinKit_Color="@color/intercom_white"
            android:visibility="gone"/>

        <ImageView
            android:layout_width="100dp"
            android:layout_height="100dp"
            android:id="@+id/imageView"
            android:baselineAlignBottom="false"
            android:background="@drawable/crop_view_shape"
            android:layout_alignParentTop="true"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:contentDescription="@string/photo_thumbnail"
            android:visibility="visible"/>
    </RelativeLayout>

    <FrameLayout
        android:id="@+id/edit_photo_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>