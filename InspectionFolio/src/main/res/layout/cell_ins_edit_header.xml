<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/black_overlay"
        android:layout_marginLeft="@dimen/margin_default"
        android:layout_marginRight="8dp"/>
    <LinearLayout
        android:id="@+id/cell_ins_edit_header_container"
        android:layout_width="match_parent"
        android:layout_height="45sp"
        android:gravity="center_vertical"
        android:layout_marginLeft="@dimen/margin_default"
        android:layout_marginRight="8dp">
        <TextView
            android:id="@+id/cell_ins_edit_tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginLeft="30dp"
            android:gravity="center_vertical"
            android:textSize="17sp"
            android:textColor="@color/header_button_color"
            android:text="MSEL"/>
        <LinearLayout
            android:layout_width="44dp"
            android:layout_height="44dp"
            android:gravity="center"
            android:id="@+id/cell_ins_edit_btn_menu">
            <ImageView
                android:id="@+id/cell_ins_edit_iv_menu"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:layout_gravity="center_vertical"
                android:src="@drawable/menu"
                android:focusable="false"
                android:focusableInTouchMode="false"
                tools:ignore="ContentDescription" />
        </LinearLayout>
    </LinearLayout>
</LinearLayout>