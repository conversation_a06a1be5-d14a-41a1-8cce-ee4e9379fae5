<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@android:color/white"
    android:orientation="vertical">

    <TextView
        android:id="@+id/txt_form_item_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/asset_info_title"
        android:layout_marginHorizontal="@dimen/margin_25"
        android:text="@string/occupancy_period"/>
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginVertical="@dimen/margin_25"
        android:layout_marginHorizontal="@dimen/margin_25"
        android:layout_gravity="center_vertical"
        android:orientation="horizontal">
        <TextView
            android:id="@+id/dtFrom_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/edittext_border"
            android:paddingVertical="@dimen/margin_smallest"
            android:paddingHorizontal="@dimen/margin_small"
            />
        <View
            android:layout_width="@dimen/margin_10"
            android:layout_height="0dp"/>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/text_normal"
            android:textColor="@color/black"
            android:gravity="center"
            android:text="@string/occupancy_period_to"/>

        <View
            android:layout_width="@dimen/margin_10"
            android:layout_height="0dp"/>

        <TextView
            android:id="@+id/dtTo_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="@drawable/edittext_border"
            android:paddingVertical="@dimen/margin_smallest"
            android:paddingHorizontal="@dimen/margin_small"
            />
    </LinearLayout>
</LinearLayout>