<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".activity.if_NewAsset"
    android:background="@color/colorPrimary"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:layout_marginTop="?android:actionBarSize"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:orientation="vertical">
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/margin_25"
            android:textColor="@color/item_asset_address_color"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textAllCaps="true"
            android:text="@string/add_new_asset_and_start_inspection"
        />
        <ListView
            android:id="@+id/lv_formItems"
            android:background="@android:color/white"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:divider="@color/transparent"
            android:paddingBottom="82dp"
            android:clipToPadding="false"
        />
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_gravity="bottom"
            android:background="@drawable/bg_button_bottom"
            android:layout_marginBottom="11dp"
            android:layout_marginLeft="15dp"
            android:layout_marginStart="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginEnd="15dp">

            <Button
                android:id="@+id/btn_start_new_inspection"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/transparent"
                android:textColor="@android:color/white"
                android:textSize="17sp"
                android:textStyle="bold"
                android:text="@string/start_new_inspection"
            />
        </FrameLayout>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="@dimen/margin_25"
            android:textColor="@color/item_asset_address_color"
            android:textSize="14sp"
            android:textStyle="bold"
            android:text="@string/add_new_asset_and_start_inspection_desc"
        />

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="52dp"
            android:layout_gravity="bottom"
            android:background="@drawable/bg_button_bottom"
            android:layout_marginBottom="11dp"
            android:layout_marginLeft="15dp"
            android:layout_marginStart="15dp"
            android:layout_marginRight="15dp"
            android:layout_marginEnd="15dp">

            <Button
                android:id="@+id/btn_asset_list"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/transparent"
                android:textColor="@android:color/white"
                android:textSize="17sp"
                android:textStyle="bold"
                android:text="@string/assets_list"
            />
        </FrameLayout>

    </LinearLayout>


</FrameLayout>