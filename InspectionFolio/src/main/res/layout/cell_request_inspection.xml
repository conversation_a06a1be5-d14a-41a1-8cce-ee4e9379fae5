<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:id="@+id/tv_address"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_20"
        android:layout_marginTop="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginEnd="@dimen/margin_20" />

    <TextView
        android:id="@+id/tv_ins_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:textColor="@android:color/black"
        android:layout_marginTop="5dp"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginEnd="@dimen/margin_20" />

    <TextView
        android:id="@+id/tv_ins_date"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="12sp"
        android:textColor="@color/colorPrimary"
        android:layout_marginTop="5dp"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginEnd="@dimen/margin_20" />

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="5dp"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginEnd="@dimen/margin_20" >

        <Button
            android:id="@+id/btn_notes"
            android:text="@string/btn_name_notes"
            android:textStyle="bold"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:minWidth="0dp"
            android:textAllCaps="false"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:background="@color/transparent" />

        <Button
            android:id="@+id/btn_view_route"
            android:text="@string/btn_name_view_route"
            android:textStyle="bold"
            android:textColor="@color/colorPrimary"
            android:textSize="14sp"
            android:minWidth="0dp"
            android:textAllCaps="false"
            android:paddingHorizontal="@dimen/margin_20"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:focusable="false"
            android:focusableInTouchMode="false"
            android:background="@color/transparent" />

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_weight="1"/>

        <RelativeLayout
            android:layout_width="81dp"
            android:layout_height="match_parent">

            <View
                android:id="@+id/bg_btn_action"
                android:layout_width="81dp"
                android:layout_height="30dp"
                android:layout_centerVertical="true"
                android:background="@drawable/bg_button_request_inspection"
                />

            <Button
                android:id="@+id/btn_action"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:textStyle="bold"
                android:textColor="@android:color/white"
                android:textSize="14sp"
                android:textAllCaps="true"
                android:gravity="center"
                android:textAlignment="center"
                android:focusable="false"
                android:focusableInTouchMode="false"
                android:background="@color/transparent"
                />

        </RelativeLayout>

    </LinearLayout>

</LinearLayout>