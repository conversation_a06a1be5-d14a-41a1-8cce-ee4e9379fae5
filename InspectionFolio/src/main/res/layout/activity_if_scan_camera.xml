<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".activity.if_scan_camera"
    android:background="#000000">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:layout_marginTop="?android:attr/actionBarSize"
        android:orientation="horizontal"
        android:id="@+id/top_view"
        android:gravity="center_vertical"
        android:background="#000000">

        <FrameLayout
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:onClick="lightClicked"
            tools:ignore="UsingOnClickInXml">
            <Button
                android:layout_width="@dimen/margin_30"
                android:layout_height="@dimen/margin_30"
                android:id="@+id/lightBtn"
                android:layout_gravity="center"
                android:background="@drawable/light" />
        </FrameLayout>

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:id="@+id/scanLabel"
            android:paddingHorizontal="@dimen/margin_small_mid"
            android:textColor="#ffffff" />

        <FrameLayout
            android:id="@+id/libraryBtn"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:onClick="libraryClicked"
            android:layout_marginStart="@dimen/margin_small"
            tools:ignore="UsingOnClickInXml">

            <ImageView
                android:layout_width="@dimen/btn_size_30"
                android:layout_height="@dimen/btn_size_30"
                android:layout_gravity="center"
                android:contentDescription="@string/btn_library"
                android:src="@drawable/icn_library"
            />
        </FrameLayout>

    </LinearLayout>

    <RelativeLayout
        android:id="@+id/camera_view"
        android:layout_weight="1"
        android:layout_width="match_parent"
        android:layout_height="0dp"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="80dp"
        android:layout_gravity="center_vertical"
        android:id="@+id/bottom_view"
        android:background="#000000">

        <Button
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/button_nav_back"
            android:id="@+id/doneBtn"
            android:textColor="#ffffff"
            android:background="#00ffff00"
            android:onClick="doneClicked"
            android:layout_marginStart="@dimen/margin_small"
            android:layout_centerInParent="true"
            android:layout_alignParentStart="true" />

        <Button
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:id="@+id/shutterBtn"
            android:background="@drawable/camera_normal"
            android:layout_centerVertical="true"
            android:layout_centerHorizontal="true"
            android:onClick="shutterClicked" />

    </RelativeLayout>

</LinearLayout>
