<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    tools:context=".activitynew.inspection3rd.if_Layout_3rd"
    android:background="@color/colorPrimary"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ListView
        android:id="@+id/lv_Layout"
        android:background="@android:color/white"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?android:actionBarSize"
        android:divider="@color/transparent"
        android:paddingBottom="82dp"
        android:clipToPadding="false"
        />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:layout_gravity="bottom"
        android:background="@drawable/bg_button_bottom"
        android:layout_marginBottom="11dp"
        android:layout_marginLeft="15dp"
        android:layout_marginStart="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginEnd="15dp">

        <Button
            android:id="@+id/btn_start_inspection"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/transparent"
            />

        <LinearLayout
            android:orientation="horizontal"
            android:layout_gravity="center_horizontal"
            android:layout_width="wrap_content"
            android:layout_height="match_parent">

            <TextView
                android:text="@string/start_inspection"
                android:textColor="@android:color/white"
                android:textSize="17sp"
                android:textStyle="bold"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                />
            <View
                android:layout_width="@dimen/margin_10"
                android:layout_height="wrap_content" />
            <ImageView
                android:src="@drawable/btn_start_inspection"
                android:contentDescription="@string/btn_action_desc"
                android:background="@color/transparent"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                />
        </LinearLayout>
    </FrameLayout>
</FrameLayout>