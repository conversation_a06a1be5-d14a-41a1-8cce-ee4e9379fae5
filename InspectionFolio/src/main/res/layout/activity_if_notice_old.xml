<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_NoticeOld">

    <LinearLayout
        android:layout_marginTop="?android:actionBarSize"
        android:layout_width="match_parent"
        android:orientation="vertical"
        android:background="@color/intercom_white"
        android:layout_height="match_parent">
        <ScrollView
            android:id="@+id/scroll_EditNotice"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:orientation="vertical"
                android:background="@color/intercom_white"
                android:layout_width="match_parent"
                android:layout_height="match_parent">
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="30dp"
                    android:background="@color/intercom_semi_transparent">
                    <TextView
                        android:layout_width="match_parent"
                        android:textSize="20dp"
                        android:textColor="@color/black_overlay"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:text="Task Info" />
                </LinearLayout>
                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_gravity="center"
                    android:layout_height="0dp"
                    android:layout_weight=".80">

                    <EditText
                        android:id="@+id/txt_NoticeTitle"
                        android:layout_width="match_parent"
                        android:hint="Title"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:singleLine="true"
                        android:padding="10dp"
                        android:textColor="@color/black"
                        android:background="@drawable/edittext_border"
                        android:imeOptions="actionDone"
                        android:layout_marginTop="10dp"
                        android:layout_height="60dp" />
                    <EditText
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="5"
                        android:minLines="5"
                        android:maxLines="8"
                        android:hint="Description"
                        android:imeOptions="actionDone"
                        android:scrollbars="vertical"
                        android:gravity="top"
                        android:cursorVisible="true"
                        android:textCursorDrawable="@null"
                        android:padding="10dp"
                        android:id="@+id/txt_NoticeDesp"
                        android:textColor="@android:color/black"
                        android:background="@drawable/edittext_border"
                        android:singleLine="false"
                        android:layout_marginTop="10dp"
                        android:layout_marginRight="10dp"
                        android:layout_marginLeft="10dp"
                        android:layout_marginBottom="10dp" />
                    <EditText android:id="@+id/txt_NoticeDue"
                        android:background="@drawable/edittext_border"
                        android:layout_marginRight="10dp"
                        android:layout_marginLeft="10dp"
                        android:textColor="@color/black"
                        android:padding="10dp"
                        android:layout_width="match_parent"
                        android:hint="Due Date"
                        android:layout_height="60dp" />
                    <TextView
                        android:layout_width="match_parent"
                        android:textSize="20dp"
                        android:layout_marginTop="10dp"
                        android:textColor="@color/black"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:text="Priority" />

                    <com.snapinspect.snapinspect3.IF_Object.SegmentedGroup
                        xmlns:segmentedgroup="http://schemas.android.com/apk/res-auto"
                        android:id="@+id/segment_Priority"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_margin="10dp"
                        android:orientation="horizontal"
                        segmentedgroup:sc_border_width="2dp"
                        segmentedgroup:sc_corner_radius="10dp">
                        <RadioButton
                            android:id="@+id/chk_1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            style="@style/RadioButton"
                            android:text="Low"/>
                        <RadioButton
                            android:id="@+id/chk_2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            style="@style/RadioButton"
                            android:text="Mild"/>
                        <RadioButton
                            android:id="@+id/chk_3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            style="@style/RadioButton"
                            android:text="Normal"/>
                        <RadioButton
                            android:id="@+id/chk_4"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            style="@style/RadioButton"
                            android:text="High"/>
                        <RadioButton
                            android:id="@+id/chk_5"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            style="@style/RadioButton"
                            android:text="Urgent"/>
                    </com.snapinspect.snapinspect3.IF_Object.SegmentedGroup>
                    <EditText
                        android:id="@+id/txt_NoticeCategory"
                        android:layout_width="match_parent"
                        android:hint="Category"
                        android:layout_marginLeft="10dp"
                        android:layout_marginRight="10dp"
                        android:singleLine="true"
                        android:padding="10dp"
                        android:textColor="@color/black"
                        android:background="@drawable/edittext_border"
                        android:imeOptions="actionDone"
                        android:layout_marginTop="10dp"
                        android:layout_height="60dp" />
                </LinearLayout>
                <LinearLayout
                    android:id="@+id/layout_AttachMedia"
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:orientation="vertical"
                    android:background="@color/intercom_white"
                    android:layout_height="300dp">
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="30dp"
                        android:background="@color/intercom_semi_transparent">
                        <TextView
                            android:layout_width="match_parent"
                            android:textSize="20sp"
                            android:textColor="@color/black_overlay"
                            android:layout_height="30dp"
                            android:layout_gravity="center_vertical"
                            android:text="Attach Media" />
                    </LinearLayout>
                    <include
                        layout="@layout/cell_insitem_photo"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                    <include
                        layout="@layout/cell_ins_video"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />
                </LinearLayout>
            </LinearLayout>
        </ScrollView>
    </LinearLayout>
</FrameLayout>
