<?xml version="1.0" encoding="utf-8"?>

<com.snapinspect.snapinspect3.views.SquareFrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <ImageView
        android:id="@+id/img_thumb_view"
        android:scaleType="fitXY"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <ImageView
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_gravity="center"
        android:id="@+id/iv_Play"
        android:src="@drawable/ic_circle_play_1"
        tools:ignore="ContentDescription"/>

    <TextView
        android:id="@+id/tv_duration"
        android:textColor="@color/more_light_gray"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|right"
        android:layout_marginRight="@dimen/margin_smallest"
        android:layout_marginBottom="@dimen/margin_smallest"
        android:textSize="12sp"
        tools:ignore="RtlHardcoded"/>
</com.snapinspect.snapinspect3.views.SquareFrameLayout>
