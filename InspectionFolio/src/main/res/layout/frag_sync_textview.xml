<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/txt_SyncTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="123"
        android:textColor="@android:color/black"
        android:textSize="30dp"
        android:gravity="center_vertical"
        android:padding="5dp"
        />

    <ProgressBar
        android:layout_toRightOf="@+id/txt_SyncTitle"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:id="@+id/progress_Sync" />
    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/txt_SyncCompleted"
        android:layout_toRightOf="@+id/txt_SyncTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text=" - Completed"
        android:textColor="@android:color/black"
        android:textSize="30dp"
        android:gravity="center_vertical"
        android:padding="5dp"
        />

</RelativeLayout>