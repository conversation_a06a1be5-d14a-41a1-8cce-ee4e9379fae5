<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <TextView
        android:id="@+id/list_exist_asset_layout_title"
        android:layout_width="0dp"
        android:layout_height="44dp"
        android:layout_marginStart="22dp"
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_weight="1"
        android:textColor="#4A4A4A"
        android:gravity="center_vertical"/>
    <LinearLayout
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:layout_marginRight="4dp"
        android:id="@+id/list_exist_asset_layout_btn_download">
        <ImageView
            android:id="@+id/list_exist_asset_layout_img"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/sync_downloadins"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:focusable="false"
            android:focusableInTouchMode="false"/>
    </LinearLayout>

    <LinearLayout
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:gravity="center_vertical"
        android:id="@+id/list_exist_asset_layout_btn_expand">
        <ImageView
            android:id="@+id/list_exist_asset_layout_expand_iv"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:src="@drawable/ic_expand"
            android:layout_marginLeft="6dp"
            android:layout_marginRight="6dp"
            android:focusable="false"
            android:focusableInTouchMode="false"/>
    </LinearLayout>
</LinearLayout>