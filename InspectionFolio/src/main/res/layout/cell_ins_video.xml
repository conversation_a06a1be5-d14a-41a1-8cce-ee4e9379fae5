<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:id="@+id/view_head"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_10"
        android:layout_marginEnd="@dimen/margin_10">
    </LinearLayout>

    <TextView
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:layout_marginBottom="10dp"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_10"
        android:layout_marginEnd="@dimen/margin_10"
        android:textSize="12dp"
        android:textColor="@android:color/darker_gray"
        android:layout_width="match_parent"
        android:gravity="center_horizontal"
        android:id="@+id/txt_Video_Inst" />

    <RelativeLayout
        android:layout_weight="0.7"
        android:layout_height="0dp"
        android:layout_width="match_parent"
        android:id="@+id/view_video_container"
        >

        <ImageView
            android:id="@+id/iv_Video"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_30"
            android:layout_marginBottom="@dimen/margin_30"
            android:layout_centerInParent="true" />

        <ImageView
            android:layout_width="80dp"
            android:layout_height="80dp"
            android:layout_centerInParent="true"
            android:id="@+id/iv_Play"
            android:src="@drawable/ic_circle_play_1" />

        <TextView
            android:id="@+id/durationText"
            android:textColor="@android:color/white"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignBottom="@+id/iv_Video"
            android:layout_alignEnd="@+id/iv_Video"
            android:layout_marginEnd="@dimen/margin_10"
            android:layout_marginBottom="@dimen/margin_small_mid"
            android:textSize="12sp" />
        </RelativeLayout>

    <Button
        android:id="@+id/btn_Record"
        android:layout_gravity="center"
        android:layout_height="80dp"
        android:layout_width="80dp"
        android:layout_marginTop="@dimen/margin_30"
        android:layout_marginBottom="@dimen/margin_30"
        android:background="@drawable/ic_camera"/>
</LinearLayout>