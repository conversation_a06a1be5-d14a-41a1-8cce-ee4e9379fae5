<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="50dp"
    >
    <RelativeLayout
        android:layout_height="match_parent"
        android:layout_width="0dp"
        android:layout_weight="1">
        <TextView
            android:layout_height="match_parent"
            android:textColor="@android:color/black"
            android:layout_centerVertical="true"
            android:layout_width="wrap_content"
            android:layout_marginRight="10dp"
            android:layout_marginLeft="10dp"
            android:id="@+id/txt_LayoutName" android:text="123123"
            android:gravity="center_vertical" />
    </RelativeLayout>
    <LinearLayout
        android:layout_height="match_parent"
        android:orientation="horizontal" android:layout_width="wrap_content"
        android:gravity="center_vertical">
        <Button
            android:textColor="@android:color/black"
            android:layout_height="35dp"
            android:layout_width="35dp"
            android:id="@+id/btn_Minus"
            android:background="@drawable/ic_minus"/>
        <TextView
            android:layout_height="match_parent"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:textColor="@android:color/black"
            android:layout_width="30dp"
            android:id="@+id/txt_LayoutNumber"
            android:text="0"
            android:layout_gravity="center_vertical"
            android:gravity="center" />
        <Button
            android:textColor="@android:color/black"
            android:layout_height="35dp"
            android:layout_width="35dp"
            android:layout_marginRight="10dp"
            android:background="@drawable/ic_plus"
            android:id="@+id/btn_Plus" />
    </LinearLayout>
</LinearLayout>