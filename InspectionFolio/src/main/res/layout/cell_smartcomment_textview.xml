<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:padding="2dp"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:weightSum="1"
    >

        <TextView xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/txt_title"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:paddingTop="@dimen/margin_small"
            android:singleLine="true"
            android:textSize="15dp"
            android:textColor="@android:color/black"
            android:gravity="top"
            android:layout_weight="1"
            android:textStyle="bold"
            />

        <TextView xmlns:android="http://schemas.android.com/apk/res/android"
            android:id="@+id/txt_comments"
            android:layout_width="fill_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/margin_small"
            android:singleLine="false"
            android:textSize="10dp"
            android:textColor="@color/black"
            android:gravity="top"
            android:layout_weight="1"
            android:maxLines="3"
            android:textStyle="italic"
            />
</LinearLayout>
