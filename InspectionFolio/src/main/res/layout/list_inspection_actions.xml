<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textSize="14sp"
            android:textColor="@color/color_8a8a8a"
            android:padding="@dimen/margin_16"
            android:text="@string/alert_title_actions"
            />
        <ImageButton
            android:id="@+id/btn_action_close"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="12dp"
            android:background="@color/transparent"
            android:src="@drawable/ins_action_close"
            />
    </LinearLayout>

    <LinearLayout
        android:id="@+id/btn_action_view_tasks"
        android:orientation="horizontal"
        android:layout_marginHorizontal="@dimen/margin_30"
        android:paddingVertical="@dimen/margin_small"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/ins_action_view_tasks" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/margin_16"
            android:text="@string/ins_action_view_tasks"
            android:textColor="@android:color/black"
            android:textSize="14sp" />
    </LinearLayout>
    <LinearLayout
        android:id="@+id/btn_action_edit_ins"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/margin_10"
        android:layout_marginHorizontal="@dimen/margin_30"
        android:paddingVertical="@dimen/margin_small"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/ins_action_edit" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="18dp"
            android:text="@string/ins_action_edit"
            android:textColor="@android:color/black"
            android:textSize="14sp" />
    </LinearLayout>
    <LinearLayout
        android:id="@+id/btn_action_delete_ins"
        android:orientation="horizontal"
        android:layout_marginTop="@dimen/margin_10"
        android:layout_marginHorizontal="@dimen/margin_30"
        android:layout_marginBottom="@dimen/margin_16"
        android:paddingVertical="@dimen/margin_small"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/ins_action_delete" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/margin_16"
            android:text="@string/ins_action_delete"
            android:textColor="@android:color/black"
            android:textSize="14sp" />
    </LinearLayout>
</LinearLayout>