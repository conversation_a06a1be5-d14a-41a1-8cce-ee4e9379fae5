<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:background="@color/colorPrimary"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:orientation="vertical"
        android:layout_marginTop="?android:attr/actionBarSize"
        android:layout_height="match_parent"
        android:layout_width="match_parent">
        <com.snapinspect.snapinspect3.views.SegmentedControl
            android:id="@+id/segmentedControl"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="@dimen/margin_16"
            android:layout_marginRight="@dimen/margin_16"
            android:layout_marginTop="@dimen/margin_small"
            android:layout_marginBottom="@dimen/margin_small"/>

        <EditText
            android:id="@+id/search_inspection"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="@dimen/margin_16"
            android:layout_marginRight="@dimen/margin_16"
            android:layout_marginBottom="@dimen/margin_small"
            android:hint="@string/search"
            android:paddingStart="@dimen/margin_16"
            android:background="@drawable/bg_search_bar"
            android:textSize="16sp"
            style="@style/EditText"
            android:imeOptions="actionSearch"
            android:drawableStart="@drawable/icn_search"
            android:drawablePadding="8dp"
            android:inputType="textVisiblePassword"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white">

            <com.softw4re.views.InfiniteListView
                android:id="@+id/lv_Inspections"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@android:color/white"
                app:dividerVisible="false"/>

            <com.snapinspect.snapinspect3.views.EmptyDataView
                android:id="@+id/view_empty_data"
                app:viewType="projectInspections"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone" />

        </RelativeLayout>
    </LinearLayout>
</FrameLayout>
