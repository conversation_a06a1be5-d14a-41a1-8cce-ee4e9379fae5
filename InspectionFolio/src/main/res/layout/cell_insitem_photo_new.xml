<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical">

        <ImageButton
            android:layout_height="wrap_content"
            android:layout_width="wrap_content"
            android:id="@+id/btn_TakePhoto"
            android:scaleType="centerInside"
            android:background="@drawable/icon_camera" />
        <LinearLayout
            android:id="@+id/red_indicator"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:background="@drawable/bg_red_indicator"
            android:orientation="horizontal"
            android:visibility="gone"
            android:layout_marginStart="37dp" />
        <LinearLayout
            android:id="@+id/gray_indicator"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:background="@drawable/bg_gray_indicator"
            android:orientation="horizontal"
            android:visibility="visible"
            android:layout_marginStart="37dp" />
        <TextView
            android:id="@+id/tv_photo_indicator"
            android:layout_width="15dp"
            android:layout_height="15dp"
            android:layout_marginTop="-1.5dp"
            android:textColor="@color/intercom_white"
            android:textSize="12sp"
            android:text="@string/math_sign_plus"
            android:gravity="center"
            android:layout_marginStart="37dp" />
        </RelativeLayout>
    <HorizontalScrollView
        android:layout_height="wrap_content"
        android:minHeight="60dp"
        android:layout_gravity="center_vertical"
        android:id="@+id/sv_DisplayPhotos"
        android:layout_marginTop="4dp"
        android:layout_weight="1"
        android:layout_marginBottom="4dp"
        android:layout_width="0dp"
        android:scrollbars="horizontal">
        <LinearLayout
            android:layout_height="match_parent"
            android:id="@+id/ll_PhotoList"
            android:orientation="horizontal"
            android:layout_width="wrap_content">

        </LinearLayout>
    </HorizontalScrollView>

</LinearLayout>