<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout  xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" android:layout_width="match_parent"
    android:layout_height="match_parent"   android:background="@color/colorPrimary"
    tools:context=".activity.if_ImageAnnotation">
    <RelativeLayout
        android:layout_marginTop="?android:attr/actionBarSize"
        android:layout_height="match_parent" android:background="@android:color/white"
        android:layout_width="match_parent" android:orientation="vertical">
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginBottom="48dp">
            <ImageView android:id="@+id/imageView"
                android:scaleType="fitCenter"
                android:layout_height="wrap_content"
                android:layout_width="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:contentDescription="">
            </ImageView>

            <com.snapinspect.snapinspect3.ImageAnnotation.DrawingView
                android:id="@+id/drawing"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_centerVertical="true"
                android:background="@android:color/transparent">
            </com.snapinspect.snapinspect3.ImageAnnotation.DrawingView>
        </RelativeLayout>

    <RelativeLayout android:id="@+id/tool"
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="#000000"
        android:layout_alignParentBottom="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true">

        <RelativeLayout android:id="@+id/pen"
            android:layout_height="match_parent"
            android:layout_width="50dp"
            android:layout_alignParentTop="true"
            android:layout_alignParentLeft="true"
            android:layout_alignParentStart="true">

            <ImageButton
                android:layout_width="24dp"
                android:layout_height="20dp"
                android:id="@+id/imagePen"
                android:background="@drawable/pencil"
                android:layout_alignParentTop="true"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:layout_marginLeft="12dp"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:onClick="penBtnClicked" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="PEN"
                android:id="@+id/penBtn"
                android:background="#00ffffff"
                android:textColor="#ffffff"
                android:layout_below="@+id/imagePen"
                android:textSize="12dp" />

            <Button
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/toolBtn"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:background="#00ffffff"
                android:onClick="toolBtnClicked" />

        </RelativeLayout>

        <ImageView
            android:layout_width="1dp"
            android:layout_height="40dp"
            android:id="@+id/imageView1"
            android:background="@drawable/gray"
            android:layout_toRightOf="@id/pen"
            android:layout_toEndOf="@id/pen"
            android:layout_marginTop="5dp"
            android:layout_alignParentTop="true"
            android:layout_centerVertical="true" />

        <RelativeLayout android:id="@+id/stroke"
            android:layout_height="match_parent"
            android:layout_width="50dp"
            android:orientation="vertical"
            android:layout_alignBottom="@+id/imageView5"
            android:layout_toRightOf="@+id/imageView1"
            android:layout_toEndOf="@+id/imageView1">

            <ImageButton
                android:layout_width="40dp"
                android:layout_height="20dp"
                android:id="@+id/imageStroke"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:layout_alignParentTop="true"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:layout_marginLeft="5dp"
                android:background="#ff0000" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Stroke"
                android:id="@+id/strokeBtn"
                android:background="#00ffffff"
                android:textColor="#ffffff"
                android:textSize="12dp"
                android:layout_below="@+id/imageStroke"
                android:layout_marginTop="4dp"/>

            <Button
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/colorBtn"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:background="#00ffffff"
                android:onClick="colorBtnClicked" />

        </RelativeLayout>

        <ImageView
            android:layout_width="1dp"
            android:layout_height="40dp"
            android:id="@+id/imageView2"
            android:background="@drawable/gray"
            android:layout_alignParentTop="true"
            android:layout_marginTop="5dp"
            android:layout_toRightOf="@+id/stroke"
            android:layout_toEndOf="@+id/stroke" />

        <RelativeLayout android:id="@+id/fill"
            android:layout_height="match_parent"
            android:layout_width="50dp"
            android:orientation="vertical"
            android:layout_toRightOf="@id/imageView2"
            android:layout_toEndOf="@id/imageView2"
            android:layout_alignParentTop="true">

            <ImageButton
                android:layout_width="40dp"
                android:layout_height="20dp"
                android:id="@+id/imageFill"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:background="@drawable/transparency"
                android:layout_alignParentTop="true"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:layout_marginLeft="5dp"/>

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Fill"
                android:id="@+id/fillBtn"
                android:background="#00ffffff"
                android:textColor="#ffffff"
                android:layout_below="@+id/imageFill"
                android:textSize="12dp" />

            <Button
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/fillColorBtn"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:background="#00ffffff"
                android:onClick="fillColorBtnClicked" />

        </RelativeLayout>

        <ImageView
            android:layout_width="1dp"
            android:layout_height="40dp"
            android:id="@+id/imageView3"
            android:background="@drawable/gray"
            android:layout_alignParentTop="true"
            android:layout_toRightOf="@+id/fill"
            android:layout_toEndOf="@+id/fill"
            android:layout_marginTop="5dp"
            />

        <RelativeLayout android:id="@+id/width"
            android:layout_height="match_parent"
            android:layout_width="50dp"
            android:orientation="vertical"
            android:layout_alignParentTop="true"
            android:layout_toRightOf="@id/imageView3"
            android:layout_toEndOf="@id/imageView3">

            <Button
                android:layout_width="wrap_content"
                android:layout_height="20dp"
                android:text="FILL"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:id="@+id/widthV"
                android:background="#00ffffff"
                android:textColor="#ffffff"
                android:layout_alignParentTop="true"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:textSize="16dp" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Width"
                android:id="@+id/widthBtn"
                android:background="#00ffffff"
                android:textColor="#ffffff"
                android:textSize="12dp"
                android:layout_below="@+id/widthV"
                android:onClick="onWidthClicked" />

            <Button
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/lineWidthBtn"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:background="#00ffffff"
                android:onClick="onWidthBtnClicked" />

        </RelativeLayout>

        <ImageView
            android:layout_width="1dp"
            android:layout_height="40dp"
            android:id="@+id/imageView4"
            android:background="@drawable/gray"
            android:layout_alignParentTop="true"
            android:layout_toRightOf="@+id/width"
            android:layout_toEndOf="@+id/width"
            android:layout_marginTop="5dp" />

        <RelativeLayout android:id="@+id/redo"
            android:layout_height="match_parent"
            android:layout_width="50dp"
            android:orientation="vertical"
            android:layout_alignParentTop="true"
            android:layout_alignParentRight="true">

            <ImageButton
                android:layout_width="30dp"
                android:layout_height="20dp"
                android:id="@+id/imageRedo"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:background="@drawable/redo"
                android:onClick="redoClicked"
                android:textColor="#ffffff"
                android:layout_alignParentTop="true"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:layout_marginLeft="10dp"
                android:contentDescription="UNDO" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Redo"
                android:id="@+id/redoBtn"
                android:background="#00ffffff"
                android:textColor="#ffffff"
                android:layout_below="@+id/imageRedo"
                android:textSize="12dp" />

            <Button
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/redoButton"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:background="#00ffffff"
                android:onClick="onRedoBtnClicked" />
        </RelativeLayout>

        <ImageView
            android:layout_width="1dp"
            android:layout_height="40dp"
            android:id="@+id/imageView5"
            android:background="@drawable/gray"
            android:layout_alignParentTop="true"
            android:layout_toLeftOf="@id/redo"
            android:layout_toStartOf="@id/redo"
            android:layout_marginTop="5dp"
            />

        <RelativeLayout android:id="@+id/undo"
            android:layout_height="match_parent"
            android:layout_width="50dp"
            android:orientation="vertical"
            android:layout_alignParentTop="true"
            android:layout_toLeftOf="@id/imageView5"
            android:layout_toStartOf="@id/imageView5">

            <ImageButton
                android:layout_width="30dp"
                android:layout_height="20dp"
                android:id="@+id/imageUndo"
                android:layout_gravity="center"
                android:layout_marginTop="5dp"
                android:background="@drawable/undo"
                android:onClick="undoClicked"
                android:textColor="#ffffff"
                android:layout_alignParentTop="true"
                android:layout_alignParentLeft="true"
                android:layout_alignParentStart="true"
                android:layout_marginLeft="10dp"
                android:contentDescription="UNDO" />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Undo"
                android:id="@+id/undoBtn"
                android:background="#00ffffff"
                android:textColor="#ffffff"
                android:layout_below="@+id/imageUndo"
                android:textSize="12dp" />

            <Button
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:id="@+id/undoButton"
                android:layout_alignParentTop="true"
                android:layout_centerHorizontal="true"
                android:onClick="onUndoBtnClicked"
                android:background="#00ffffff" />
        </RelativeLayout>
        <ImageView
            android:layout_width="1dp"
            android:layout_height="40dp"
            android:id="@+id/imageView6"
            android:background="@drawable/gray"
            android:layout_alignParentTop="true"
            android:layout_toLeftOf="@id/undo"
            android:layout_toStartOf="@id/undo"
            android:layout_marginTop="5dp"/>
    </RelativeLayout>

    <LinearLayout
        android:id="@+id/editLay"
        android:layout_width="match_parent"
        android:layout_height="40dp"

        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:background="#df727273"
        android:visibility="invisible">

        <EditText
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:id="@+id/editText"
            android:layout_weight="1"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:textColor="#ffffff" />

        <ImageButton
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:id="@+id/boldBtn"
            android:layout_centerHorizontal="true"
            android:background="@drawable/bold"
            android:textAlignment="center"
            android:adjustViewBounds="false"
            android:layout_marginTop="10dp"
            android:layout_marginLeft="15dp"
            android:onClick="boldBtnClicked" />

        <ImageButton
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:id="@+id/italicBtn"
            android:background="@drawable/italic"
            android:layout_marginTop="10dp"
            android:layout_marginLeft="15dp"
            android:onClick="italicBtnClicked" />

        <ImageButton
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:id="@+id/underBtn"
            android:background="@drawable/under"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="10dp"
            android:onClick="underBtnClicked" />

        <ImageButton
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:id="@+id/fontBtn"
            android:background="@drawable/font"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="10dp"
            android:onClick="fontBtnClicked" />

        <ImageButton
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:id="@+id/trashBtn"
            android:background="@drawable/trash"
            android:layout_marginLeft="15dp"
            android:layout_marginTop="10dp"
            android:layout_marginRight="10dp"
            android:onClick="trashBtnClicked" />

    </LinearLayout>

    <LinearLayout
        android:orientation="horizontal"
        android:id="@+id/sliderLay"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_below="@+id/editLay"
        android:layout_alignParentLeft="true"
        android:layout_alignParentStart="true"
        android:background="#df727273"
        android:visibility="invisible">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Text size:"
            android:id="@+id/textView"
            android:layout_marginLeft="5dp"

            android:textAlignment="center"
            android:layout_marginTop="10dp"
            android:textColor="#ffffff" />

        <SeekBar
            android:layout_width="0dp"
            android:layout_height="30dp"
            android:id="@+id/seekBar"
            android:layout_weight="1"

            android:layout_marginTop="10dp" />
           <!-- android:contextClickable="false" -->

        <TextView
            android:layout_width="30dp"
            android:layout_height="wrap_content"
            android:id="@+id/sizeLabel"

            android:textAlignment="center"
            android:text="10"
            android:layout_marginTop="10dp"
            android:textColor="#ffffff"
            android:layout_marginRight="10dp" />
    </LinearLayout>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"

        android:layout_alignParentRight="true"
        android:layout_alignParentEnd="true"></RelativeLayout>
    </RelativeLayout>
</RelativeLayout>
