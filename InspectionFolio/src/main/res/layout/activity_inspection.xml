<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:orientation="vertical"
    tools:context=".activity.if_inspection">
    <ListView xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_marginTop="?android:actionBarSize"
        android:layout_width="match_parent" android:layout_height="0dp" android:layout_weight="1"
        android:id="@+id/lv_InsRoom"
        android:divider="#CCCCCC"
        android:dividerHeight="2px"
        android:background="@android:color/white">

    </ListView>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal|bottom"
        android:background="#f8f6f6">

        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:text=""
            android:id="@+id/txt_timer"
            android:background="#00ffffff"
            android:layout_gravity="center_horizontal"
            android:textColor="#10518f"
            android:gravity="center_vertical|center_horizontal" />
    </LinearLayout>

</LinearLayout>
