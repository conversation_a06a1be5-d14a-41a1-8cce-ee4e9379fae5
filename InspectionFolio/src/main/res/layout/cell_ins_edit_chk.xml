<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="50dp"
    android:gravity="center_vertical">
    <ImageView
        android:id="@+id/cell_ins_edit_chk_btn_minus"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginLeft="20dp"
        android:layout_marginRight="8dp"
        android:padding="4dp"
        android:src="@drawable/red_minus"/>

    <EditText
        android:id="@+id/cell_ins_edit_chk_et_name"
        android:layout_width="@dimen/default_width"
        android:layout_height="24dp"
        android:layout_marginRight="8dp"
        android:background="@drawable/bg_edittext"
        android:imeOptions="actionDone"
        android:textSize="14sp"
        style="@style/EditTextTheme"
        android:textColor="@color/atv_default_cell_text"
        android:fontFamily="sans-serif"
        android:text="ddddffdf"
        android:paddingLeft="4dp"
        android:lines="1"
        android:scrollHorizontally="true"
        android:ellipsize="end"
        android:inputType="text|textNoSuggestions" />
    <TextView
        android:id="@+id/cell_ins_edit_chk_tv_symbol"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:gravity="center"
        android:textSize="17sp"
        android:fontFamily="sans-serif-light"
        android:layout_marginBottom="4dp"
        android:layout_marginTop="4dp"
        android:textColor="@color/atv_default_cell_text"
        android:background="@drawable/bg_circle_textview"
        android:text="N"/>
</LinearLayout>