<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_signup_2">
    <ScrollView
        android:layout_height="match_parent"
        android:layout_marginTop="?android:attr/actionBarSize"
        android:layout_width="match_parent">
        <LinearLayout android:orientation="vertical" android:layout_height="wrap_content" android:layout_width="match_parent">
            <TextView
                android:layout_width="match_parent" android:textSize="16dp" android:layout_marginLeft="20dp" android:layout_marginTop="10dp"
                android:layout_height="wrap_content" android:text="Personal Info" />
            <EditText
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:layout_marginTop="40dp"

                style="@style/edit_login"
                android:hint="@string/FirstName"
                android:id="@+id/signup_firstname_2"/>
            <EditText
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"

                style="@style/edit_login"
                android:hint="@string/LastName"
                android:id="@+id/signup_lastname_2"/>
            <EditText
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"

                style="@style/edit_login"
                android:hint="@string/CompanyName"
                android:id="@+id/signup_companyname_2"/>
            <EditText
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"

                style="@style/edit_login"
                android:hint="@string/Phone"
                android:id="@+id/signup_phone_2"/>

            <EditText
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:layout_marginTop="10dp"
                style="@style/edit_login"
                android:hint="@string/Email"
                android:inputType="textEmailAddress"
                android:id="@+id/signup_email_2"/>
            <TextView
                android:layout_width="match_parent" android:textSize="16dp" android:layout_marginLeft="20dp" android:layout_marginTop="10dp"
                android:layout_height="wrap_content" android:text="Setup Info" />
            <Spinner
                android:id="@+id/signup_country_2"
                android:spinnerMode="dialog"
                android:layout_margin="10dp"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content" />
            <Spinner
                android:id="@+id/signup_industry_2"
                android:layout_margin="10dp"

                android:spinnerMode="dialog"

                android:layout_width="fill_parent"
                android:layout_height="wrap_content" />
            <Spinner
                android:id="@+id/signup_template_2"
                android:layout_margin="10dp"

                android:spinnerMode="dialog"

                android:layout_width="fill_parent"
                android:layout_height="wrap_content" />
            <Button android:id="@+id/btn_Signup_2"
                android:layout_width="fill_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:padding="10dp"
                android:textSize="20dp"
                android:textColor="@android:color/white"
                android:background="#50000000"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:text="@string/btn_Submit">

            </Button>
        </LinearLayout>
    </ScrollView>
</FrameLayout>
