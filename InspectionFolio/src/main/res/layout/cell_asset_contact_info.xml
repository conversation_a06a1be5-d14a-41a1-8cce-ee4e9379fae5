<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="horizontal"
    android:paddingLeft="@dimen/margin_20"
    android:paddingRight="@dimen/margin_20"
    android:paddingTop="@dimen/margin_16"
    android:paddingBottom="@dimen/margin_16">

    <LinearLayout
        android:id="@+id/contact_short_name_container"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:layout_gravity="center_vertical"
        android:background="@drawable/bg_green_circle">
        <TextView
            android:id="@+id/contact_short_name"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:textColor="@android:color/white"
            android:textSize="20sp"/>
    </LinearLayout>

    <LinearLayout
        android:layout_marginStart="@dimen/margin_16"
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:orientation="vertical">
        <TextView
            android:id="@+id/contact_full_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="14sp"
            android:textStyle="bold"
            android:textColor="@android:color/black"
            android:layout_marginBottom="@dimen/margin_smallest"/>
        <TextView
            android:id="@+id/contact_email"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/blue_light"
            android:textSize="14sp"
            android:visibility="gone"
            android:minHeight="1dp"
            />
        <TextView
            android:id="@+id/contact_phone_number_1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/blue_light"
            android:textSize="14sp"
            android:visibility="gone"
            android:minHeight="1dp"
            />
        <TextView
            android:id="@+id/contact_phone_number_2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/blue_light"
            android:textSize="14sp"
            android:visibility="gone"
            android:minHeight="1dp"
            />
        <LinearLayout
            android:id="@+id/contact_type_container"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/margin_smallest"
            android:paddingTop="2dp"
            android:paddingBottom="2dp"
            android:paddingLeft="@dimen/margin_smallest"
            android:paddingRight="@dimen/margin_smallest"
            android:background="@drawable/bg_green_round">
            <TextView
                android:id="@+id/contact_type"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="12sp"
                android:textColor="@android:color/white"/>
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/contact_btn_forward"
        android:layout_marginLeft="@dimen/margin_small"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:layout_gravity="center_vertical"
        android:gravity="center">
        <ImageView
            android:layout_width="10dp"
            android:layout_height="18dp"
            android:src="@drawable/icn_forward"/>
    </LinearLayout>

</LinearLayout>