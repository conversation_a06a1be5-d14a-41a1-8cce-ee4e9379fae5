<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimary">

        <EditText
            android:id="@+id/search_inspection"
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:layout_marginLeft="@dimen/margin_16"
            android:layout_marginRight="@dimen/margin_16"
            android:layout_marginTop="@dimen/margin_small"
            android:layout_marginBottom="@dimen/margin_small"
            android:hint="@string/search"
            android:paddingLeft="@dimen/margin_16"
            android:paddingRight="@dimen/margin_16"
            android:paddingStart="@dimen/margin_16"
            android:paddingEnd="@dimen/margin_16"
            android:background="@drawable/bg_search_bar"
            android:textSize="16sp"
            style="@style/EditText"
            android:imeOptions="actionSearch"
            android:drawableLeft="@drawable/icn_search"
            android:drawablePadding="8dp"
            android:inputType="textVisiblePassword"
            android:autofillHints=""
            android:drawableStart="@drawable/icn_search"
        />
    </LinearLayout>
    <ListView
        android:id="@+id/lv_Inspections"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:dividerHeight="0dp"
        android:divider="@color/transparent"
        android:background="@android:color/white"
    />
</LinearLayout>