<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">
    <LinearLayout
        android:layout_marginTop="@dimen/margin_smallest"
        android:layout_marginBottom="@dimen/margin_default"

        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_phone"
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_phone"/>
        <LinearLayout

            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">
            <TextView
                android:id="@+id/tv_contact_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@android:color/black"
                android:textSize="16sp"/>
            <TextView
                android:id="@+id/tv_contact_phone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/blue_light"
                android:textSize="16sp"/>
            <TextView
                android:id="@+id/tv_contact_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@android:color/black"
                android:textSize="16sp"/>
        </LinearLayout>
        <ImageView
            android:id="@+id/btn_contact_edit"
            android:layout_width="30dp"
            android:layout_height="30dp"
            android:src="@drawable/ic_edit"
            android:layout_marginLeft="@dimen/margin_small"
            android:layout_marginRight="@dimen/margin_small"/>

    </LinearLayout>

</LinearLayout>