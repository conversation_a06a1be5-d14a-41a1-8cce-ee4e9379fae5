<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white">
    <LinearLayout
        android:layout_marginTop="@dimen/margin_smallest"
        android:layout_marginBottom="@dimen/margin_default"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <RelativeLayout
            android:layout_marginLeft="20dp"
            android:layout_marginRight="20dp"
            android:layout_width="32dp"
            android:layout_height="32dp">
            <LinearLayout
                android:layout_width="10dp"
                android:layout_height="10dp"
                android:background="@drawable/btn_oval_blue"
                android:layout_centerInParent="true"/>
        </RelativeLayout>
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">
            <TextView
                android:id="@+id/tv_custom_check_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/blue_light"
                android:layout_marginBottom="@dimen/margin_small"
                android:textSize="16sp"/>
            <Switch
                android:enabled="false"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/tv_custom_check_switch"
                android:showText="false"
                android:thumb="@drawable/switch_selector"/>
        </LinearLayout>

    </LinearLayout>

</LinearLayout>