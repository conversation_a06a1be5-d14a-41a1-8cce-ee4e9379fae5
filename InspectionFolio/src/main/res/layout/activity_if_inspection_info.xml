<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/colorPrimary"
        tools:context=".activitynew.Inbox.if_InspectionInfo">
    <LinearLayout
            android:orientation="vertical"
            android:background="@color/white_color"
            android:layout_width="match_parent"
            android:layout_height="match_parent">
        <LinearLayout
                android:id="@+id/inspection_details"
                android:orientation="vertical"
                android:layout_marginHorizontal="18dp"
                android:layout_marginVertical="@dimen/margin_10"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
            <TextView
                    android:id="@+id/txt_inspection_details"
                    android:text="@string/inspection_details"
                    android:textSize="17sp"
                    android:textStyle="bold"
                    android:textColor="@color/gray_color_666"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>

            <LinearLayout
                    android:orientation="vertical"
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">
                <TextView
                        android:text="@string/inspection_title"
                        android:textSize="14sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/black"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                <TextView
                        android:id="@+id/txt_inspection_title"
                        android:layout_marginTop="2dp"
                        android:textSize="14sp"
                        android:textColor="@color/gray_color_9C9C9C"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>
                <LinearLayout
                        android:layout_marginTop="@dimen/margin_10"
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false">
                    <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content">
                        <TextView
                                android:text="@string/inspection_type"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@android:color/black"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                        <TextView
                                android:id="@+id/txt_inspection_type"
                                android:layout_marginTop="2dp"
                                android:textSize="14sp"
                                android:textColor="@color/gray_color_9C9C9C"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                    </LinearLayout>
                    <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content">
                        <TextView
                                android:text="@string/inspection_inspector"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@android:color/black"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                        <TextView
                                android:id="@+id/txt_inspection_inspector"
                                android:layout_marginTop="2dp"
                                android:textSize="14sp"
                                android:textColor="@color/gray_color_9C9C9C"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                    </LinearLayout>
                </LinearLayout>
                <LinearLayout
                        android:layout_marginTop="@dimen/margin_10"
                        android:orientation="horizontal"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:baselineAligned="false">
                    <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content">
                        <TextView
                                android:text="@string/inspection_start"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@android:color/black"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                        <TextView
                                android:id="@+id/txt_inspection_start"
                                android:layout_marginTop="2dp"
                                android:textSize="14sp"
                                android:textColor="@color/gray_color_9C9C9C"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                    </LinearLayout>
                    <LinearLayout
                            android:orientation="vertical"
                            android:layout_width="0dp"
                            android:layout_weight="1"
                            android:layout_height="wrap_content">
                        <TextView
                                android:text="@string/inspection_end"
                                android:textSize="14sp"
                                android:textStyle="bold"
                                android:textColor="@android:color/black"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                        <TextView
                                android:id="@+id/txt_inspection_end"
                                android:layout_marginTop="2dp"
                                android:textSize="14sp"
                                android:textColor="@color/gray_color_9C9C9C"
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
                android:id="@+id/inspection_summary"
                android:layout_marginHorizontal="18dp"
                android:layout_marginVertical="@dimen/margin_10"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
            <TextView
                    android:text="@string/inspection_summary"
                    android:textStyle="bold"
                    android:textSize="17sp"
                    android:textColor="@color/gray_color_666"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            <TextView
                    android:text="@string/inspection_location"
                    android:textStyle="bold"
                    android:textSize="14sp"
                    android:textColor="@android:color/black"
                    android:layout_marginTop="10dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            <TextView
                    android:id="@+id/txt_inspection_location"
                    android:textSize="14sp"
                    android:textColor="@color/colorPrimary"
                    android:layout_marginTop="2dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            <fragment
                    android:id="@+id/map_inspection_location"
                    android:layout_marginTop="@dimen/margin_10"
                    android:layout_width="match_parent"
                    android:layout_height="135dp"
                    class="com.google.android.gms.maps.SupportMapFragment" />
        </LinearLayout>

        <LinearLayout
                android:id="@+id/inspection_status"
                android:orientation="vertical"
                android:layout_marginHorizontal="18dp"
                android:layout_marginVertical="@dimen/margin_10"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
            <TextView
                    android:text="@string/inspection_status"
                    android:textStyle="bold"
                    android:textSize="17sp"
                    android:textColor="@color/gray_color_666"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"/>
            <com.snapinspect.snapinspect3.views.RoundTextView
                    android:id="@+id/txt_inspection_status"
                    android:layout_marginTop="10dp"
                    android:textSize="16sp"
                    android:textColor="@android:color/white"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="@dimen/margin_small_mid"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content" />
        </LinearLayout>

    </LinearLayout>
</FrameLayout>