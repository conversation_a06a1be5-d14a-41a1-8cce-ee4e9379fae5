<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:orientation="vertical"
    tools:context=".activity.if_inspection">

    <RelativeLayout
        android:id="@+id/top_layer"
        android:layout_marginTop="?android:actionBarSize"
        android:background="@android:color/white"
        android:layout_width="match_parent"
        android:layout_height="50dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_alignParentBottom="true"
            android:layout_marginBottom="0.5dp"
            android:background="@color/light_border_color"/>
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_centerInParent="true"
            android:orientation="vertical">
            <TextView
                android:id="@+id/comments_tv_title"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:minWidth="160dp"
                android:gravity="center"
                android:textSize="16sp"
                android:textColor="@android:color/black"/>
            <LinearLayout
                android:layout_width="match_parent"
                android:background="@color/blue_color"
                android:layout_height="2dp"
                android:minWidth="160dp"/>
        </LinearLayout>

    </RelativeLayout>

    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/comments_swipe_refresh"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/entry_view"
        android:layout_below="@id/top_layer">
        <androidx.recyclerview.widget.RecyclerView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/comments_list_view"
            android:divider="@null"
            android:background="@android:color/white" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
    <LinearLayout
        android:id="@+id/user_view"
        android:orientation="vertical"
        android:layout_width="240dp"
        android:layout_height="wrap_content"
        android:background="@color/intercom_white"
        android:layout_marginLeft="40dp"
        android:layout_above="@+id/entry_view"/>
    <LinearLayout
        android:id="@+id/entry_view"
        android:background="@android:color/white"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/light_border_color"/>
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical">
            <Button
                android:id="@+id/comments_btn_control"
                android:layout_margin="@dimen/margin_small"
                android:layout_width="25dp"
                android:layout_height="25dp"
                android:background="@drawable/ic_circle_add"/>

            <EditText
                android:id="@+id/comments_et_note"
                style="@style/EditText"
                android:layout_width="0dp"
                android:layout_weight="1"
                android:layout_gravity="center_vertical"
                android:background="@drawable/bg_edit_text"
                android:hint="@string/type_reply"
                android:textSize="13sp"
                android:maxLines="5"
                tools:ignore="RtlHardcoded"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_smallest"
                android:layout_marginBottom="@dimen/margin_smallest"
                android:textColor="@android:color/black"
                android:paddingLeft="@dimen/margin_small"
                android:paddingRight="@dimen/margin_small"
                android:paddingTop="@dimen/margin_smallest"
                android:paddingBottom="@dimen/margin_smallest"
                android:minHeight="35dp"/>

            <Button
                android:id="@+id/comments_btn_send"
                android:layout_width="50dp"
                android:layout_height="35dp"
                android:background="@null"
                android:text="@string/send"
                android:textSize="14sp"
                android:layout_marginLeft="@dimen/margin_small"
                android:layout_marginRight="@dimen/margin_small"
                android:textColor="@color/blue_color"/>
        </LinearLayout>



    </LinearLayout>


</RelativeLayout>
