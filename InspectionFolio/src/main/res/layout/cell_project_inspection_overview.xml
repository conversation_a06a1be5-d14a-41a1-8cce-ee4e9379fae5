<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#FBFBFB"
    xmlns:android="http://schemas.android.com/apk/res/android">
    <LinearLayout
        android:orientation="vertical"
        android:layout_marginHorizontal="@dimen/margin_16"
        android:layout_marginVertical="@dimen/margin_16"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/tv_title"
            android:textStyle="bold"
            android:textSize="@dimen/text_h4"
            android:textColor="@android:color/black"
            android:layout_marginBottom="@dimen/margin_smallest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <TextView
            android:id="@+id/tv_ref"
            android:textSize="@dimen/text_normal"
            android:textColor="@color/dark_gray"
            android:layout_marginBottom="@dimen/margin_smallest"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"/>

        <LinearLayout
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:text="@string/title_project_manager"
                android:textStyle="bold"
                android:textSize="@dimen/text_normal"
                android:textColor="@color/dark_gray"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
            <TextView
                android:id="@+id/tv_manager"
                android:textSize="@dimen/text_normal"
                android:textColor="@color/dark_gray"
                android:layout_weight="1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"/>
        </LinearLayout>
        <LinearLayout
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/margin_16"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <TextView
                android:id="@+id/tv_progress"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:paddingHorizontal="@dimen/margin_16"
                android:minWidth="@dimen/button_min_width"
                android:minHeight="@dimen/btn_size_30"
                android:textSize="@dimen/text_h5"
                android:textColor="@android:color/white"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1"/>
        </LinearLayout>

    </LinearLayout>
</FrameLayout>