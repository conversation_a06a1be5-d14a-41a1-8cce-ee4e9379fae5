<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_EditComments">

        <LinearLayout
            android:orientation="vertical"
            android:layout_width="fill_parent"
            android:layout_marginTop="?android:actionBarSize"
            android:layout_height="fill_parent"
            android:background="@android:color/white"
            android:layout_gravity="center_horizontal|top">

            <EditText
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:lines="8"
                android:minLines="6"
                android:maxLines="10"
                android:scrollbars="vertical"
                android:gravity="top"
                android:cursorVisible="true"
                android:textCursorDrawable="@null"
                android:padding="10dp"
                android:id="@+id/txt_comments"
                android:textColor="@android:color/black"
                android:background="@drawable/edittext_border"
                android:singleLine="false"
                android:layout_marginTop="10dp"
                android:layout_marginRight="10dp"
                android:layout_marginLeft="10dp"
                android:layout_marginBottom="20dp" />
            <EditText android:id="@+id/search_smarttext"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:hint="Search Smart Text..."
                android:background="@drawable/edittext_border"
                android:paddingLeft="10dp"
                android:textColor="@android:color/darker_gray"
                android:textSize="18dp"
                android:imeOptions="actionSearch"
                android:drawableLeft="@drawable/abc_ic_search"
                android:inputType="textVisiblePassword"
                android:layout_marginLeft="10dp"
                android:layout_marginRight="10dp">
                <!--android:drawableLeft="@drawable/ab" -->
            </EditText>
            <ListView xmlns:android="http://schemas.android.com/apk/res/android"
                android:layout_width="match_parent" android:layout_height="match_parent"
                android:id="@+id/lv_Comment_QuickText"
                android:divider="#CCCCCC"
                android:dividerHeight="2px"
                android:background="@android:color/white"
                android:layout_marginLeft="10dp">

            </ListView>
        </LinearLayout>

    
</FrameLayout>
