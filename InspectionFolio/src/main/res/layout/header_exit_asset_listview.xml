<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/intercom_white">
    <LinearLayout
        android:id="@+id/header_exist_asset_layer_pull_refresh"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="#ebecf2"
        android:layout_gravity="center_vertical">
        <TextView
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="33dp"
            android:textStyle="italic"
            android:textColor="@color/header_text_color"
            android:textSize="12sp"
            android:text="@string/pull_down_to_view_asset_photo"/>
    </LinearLayout>
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal">
        <com.snapinspect.snapinspect3.util.ScaleImageView
            android:id="@+id/header_exist_asset_iv_asset"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:scaleType="centerCrop"
            android:layout_marginTop="14dp"
            android:adjustViewBounds="true"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/header_exist_asset_layer_asset"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginRight="@dimen/margin_20"
        android:layout_marginTop="@dimen/margin_smallest"
        android:orientation="vertical">
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:orientation="horizontal"
                android:gravity="center_vertical">
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/asset_info"
                    android:background="@drawable/bg_underline"
                    android:textColor="@color/atv_default_cell_text"
                    android:textSize="18sp"/>
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="20dp"
                    android:layout_weight="1"
                    tools:ignore="UselessLeaf" />
                <TextView
                    android:id="@+id/header_exist_asset_btn_map"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/map"
                    android:textSize="18sp"
                    android:background="@drawable/bg_underline"
                    android:textColor="@color/atv_default_cell_text"/>
            </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="@dimen/margin_20">
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">
                <TextView
                    android:id="@+id/header_exist_asset_tv_address"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="@dimen/margin_small"
                    android:maxLines="10"
                    android:minLines="1"
                    android:textColor="@color/atv_default_cell_text"
                    android:textStyle="bold"
                    android:textSize="18sp"/>
                <TextView
                    android:id="@+id/header_exist_asset_tv_assigned"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="10"
                    android:minLines="1"
                    android:textColor="@color/atv_default_cell_text"
                    android:textSize="14sp"
                    android:textStyle="italic"/>
                <TextView
                    android:id="@+id/header_exist_asset_tv_last_inspection"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="10"
                    android:minLines="1"
                    android:textColor="@color/atv_default_cell_text"
                    android:textSize="14sp"
                    android:textStyle="italic"/>
            </LinearLayout>
            <LinearLayout
                android:id="@+id/header_exist_asset_listview_btn_address_edit"
                android:layout_width="25dp"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginLeft="@dimen/margin_small">
                <ImageView
                    android:layout_height="25dp"
                    android:layout_width="25dp"
                    android:src="@drawable/ic_forward"/>
            </LinearLayout>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:visibility="gone"
                android:id="@+id/header_exist_asset_layer_go_apartment"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="start"
                android:layout_marginTop="10dp">
                <TextView
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="UNITS"
                    android:textColor="@color/atv_default_cell_text"
                    android:textStyle="bold"
                    android:textSize="14sp"/>
                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="@dimen/margin_smallest"
                    android:src="@drawable/icon_next_arrow"/>
            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1"/>

            <LinearLayout
                android:visibility="gone"
                android:id="@+id/header_exist_asset_layer_go_tasks"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="end"
                android:layout_marginTop="10dp">
                <TextView
                    android:gravity="center_vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tasks"
                    android:textAllCaps="true"
                    android:textColor="@color/atv_default_cell_text"
                    android:textStyle="bold"
                    android:textSize="14sp"/>
                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginStart="@dimen/margin_smallest"
                    android:src="@drawable/icon_next_arrow"/>
            </LinearLayout>

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_gravity="center_horizontal">
            <Button
                android:id="@+id/header_exist_asset_listview_btn_schedule"
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_weight="0.4"
                android:layout_marginRight="20dp"
                android:text="@string/schedule"
                style="@style/Button.Blue"
                android:visibility="gone"
                android:textColor="@drawable/btn_blue_color"/>
            <!--<Button-->
                <!--android:id="@+id/header_exist_asset_listview_btn_inspection"-->
                <!--android:layout_width="0dp"-->
                <!--android:layout_height="45dp"-->
                <!--android:maxWidth="100dp"-->
                <!--android:layout_weight="0.4"-->
                <!--android:text="@string/start_inspection"-->
                <!--style="@style/Button.Red"-->
                <!--android:textColor="@drawable/btn_red_color"/>-->


            <RelativeLayout
                android:id="@+id/btn_start_inspection_container"
                android:layout_width="match_parent"
                android:layout_height="80dp">
                <TextView
                    android:id="@+id/header_exist_asset_listview_btn_inspection"
                    android:layout_width="204dp"
                    android:layout_height="42dp"
                    android:layout_centerInParent="true"
                    android:background="@drawable/bg_shadow_btn"
                    android:elevation="4dp"
                    android:gravity="center"
                    android:text="@string/new_inspection" android:textColor="@android:color/white"
                    android:textSize="17sp"/>

                <!--<ImageView-->
                    <!--android:layout_marginTop="@dimen/margin_small"-->
                    <!--android:id="@+id/upload_button"-->
                    <!--android:layout_width="215dp"-->
                    <!--android:layout_height="80dp"-->
                    <!--android:src="@drawable/btn_inspection"-->
                    <!--android:layout_centerInParent="true"/>-->
            </RelativeLayout>
        </LinearLayout>

    </LinearLayout>

    <!--<LinearLayout-->
        <!--android:id="@+id/header_exist_asset_layer_go_apartment" android:visibility="gone"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="wrap_content"-->
        <!--android:background="#FFFFFF"-->
        <!--android:layout_marginLeft="@dimen/listview_margin_default"-->
        <!--android:layout_marginRight="@dimen/listview_margin_default"-->
        <!--android:layout_gravity="center_vertical">-->

        <!--<TextView-->
            <!--android:gravity="left"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="35dp"-->
            <!--android:textColor="@color/header_text_color"-->
            <!--android:textSize="16sp"-->
            <!--android:layout_marginTop="3dp"-->
            <!--android:text="Apartments"/>-->
    <!--</LinearLayout>-->
</LinearLayout>