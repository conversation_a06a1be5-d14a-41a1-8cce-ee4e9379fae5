<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/drag_layout"
    android:orientation="horizontal" android:layout_width="match_parent" android:layout_gravity=""
    android:layout_height="50dp" android:minHeight="50dp">
    <ImageButton
        android:layout_height="wrap_content"
        android:layout_width="wrap_content" android:id="@+id/btn_DeleteRoom" android:layout_gravity="center_vertical"
        android:src="@android:drawable/ic_delete" android:background="@color/transparent" android:layout_weight="0.2" />
    <EditText
        android:layout_height="wrap_content" android:focusable="true"
        android:editable="true"
        android:textSize="20dp"
        android:textColor="@android:color/black"
        android:inputType="text"
        android:imeOptions="actionDone"
        android:layout_gravity="center_vertical"
        android:layout_width="match_parent" android:id="@+id/txt_RoomName" android:layout_weight="1.7"  />

</LinearLayout>