<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activitynew.Home.if_HomeTab"
    android:keepScreenOn="true">

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:gravity="center_vertical"
        android:layout_height="?android:actionBarSize"
        tools:ignore="MissingConstraints">

        <Button
            android:id="@+id/home_tab_btn_setting"
            android:layout_width="@dimen/btn_size_30"
            android:layout_height="@dimen/btn_size_30"
            android:layout_marginStart="@dimen/margin_16"
            android:background="@drawable/icn_setting"/>
        <Button
            android:id="@+id/home_tab_btn_menu"
            android:layout_width="@dimen/btn_size_30"
            android:layout_height="@dimen/btn_size_30"
            android:layout_marginStart="@dimen/margin_small"
            android:background="@drawable/icn_menu"/>

        <TextView
            android:id="@+id/home_tab_tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textAlignment="center"
            android:textSize="20sp"
            android:textStyle="bold"
            android:text="@string/app_name"
            android:textColor="@android:color/white"
            android:layout_marginStart="@dimen/margin_small"
            android:maxLines="1"
            android:ellipsize="end"
            android:layout_weight="1" />

        <Button
            android:id="@+id/home_tab_btn_add"
            android:layout_width="@dimen/btn_size_30"
            android:layout_height="@dimen/btn_size_30"
            android:layout_marginEnd="@dimen/margin_small"
            android:layout_marginStart="@dimen/margin_small"
            android:background="@drawable/icn_add"/>

        <Button
            android:id="@+id/home_tab_btn_sync"
            android:layout_marginEnd="@dimen/margin_16"
            android:layout_width="@dimen/btn_size_30"
            android:layout_height="@dimen/btn_size_30"
            android:background="@drawable/icn_sync"/>

    </LinearLayout>

    <FrameLayout
        android:id="@+id/container"
        android:layout_marginTop="?android:actionBarSize"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />


    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/navigationView"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="0dp"
        android:layout_marginStart="0dp"
        android:background="?android:attr/windowBackground"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:itemBackground="@android:color/white"
        app:itemIconTint="@color/bottom_nav_color"
        app:itemTextColor="@color/bottom_nav_color"
        app:labelVisibilityMode="labeled"
        />

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#C7C7CC"
        app:layout_constraintBottom_toTopOf="@id/navigationView"/>

</androidx.constraintlayout.widget.ConstraintLayout>
