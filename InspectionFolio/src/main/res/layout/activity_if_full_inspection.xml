<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    android:id="@+id/framelayout_existasset"
    tools:context=".activitynew.if_existasset">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="?android:attr/actionBarSize"
        tools:ignore="UselessParent"
        android:orientation="vertical">
        <include
            android:id="@+id/prompt_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            layout="@layout/view_prompt"/>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <ExpandableListView
                android:background="@color/intercom_white"
                android:groupIndicator="@null"
                android:id="@+id/if_full_inspection_list_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginBottom="59dp"
                android:childIndicatorRight="14.5sp"
                android:childDivider="@android:color/transparent"
                android:dividerHeight="0dp"/>
            <include
                android:id="@+id/group_view_layer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                layout="@layout/list_inspection_group_layout" />
        </RelativeLayout>

    </LinearLayout>

    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_gravity="center_horizontal|bottom"
        android:gravity="center_vertical"
        android:background="#F4F4F4">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:text=""
            android:id="@+id/txt_timer"
            android:background="#00ffffff"
            android:textColor="#10518f"
            android:layout_marginLeft="24dp"
            android:gravity="center" />
        <androidx.legacy.widget.Space
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"/>
        <ImageView
            android:id="@+id/btn_trash"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:padding="4dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:src="@drawable/icn_trash"/>
        <ImageView
            android:id="@+id/btn_edit"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:padding="4dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:src="@drawable/icn_edit"/>
        <ImageView
            android:id="@+id/btn_search"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:padding="4dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:src="@drawable/icn_search"/>
        <ImageView
            android:id="@+id/btn_collapse"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:padding="4dp"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="20dp"
            android:src="@drawable/icn_order"/>
    </LinearLayout>

</FrameLayout>
