<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:background="@android:color/white"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/schedule_header_background"
        />
    <TextView
        android:id="@+id/header_schedules_tv_title"
        style="@style/SchedulesHeaderTitleSmall"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginTop="@dimen/margin_10"
        android:layout_marginBottom="@dimen/margin_10"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/margin_20" />
    <TextView
        android:id="@+id/header_schedules_tv_subtitle"
        style="@style/SchedulesHeaderTitleSmall"
        android:textColor="@color/light_gray"
        android:layout_gravity="end"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/margin_10"
        android:layout_marginBottom="@dimen/margin_10"
        android:layout_marginRight="@dimen/margin_20"
        android:layout_marginEnd="@dimen/margin_20" />

</FrameLayout>