<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal" android:layout_width="match_parent"
    android:layout_height="match_parent">
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical" android:layout_width="match_parent" android:id="@+id/layout_Inspection_Action"
        android:layout_height="match_parent" android:layout_weight="0.1"
        android:layout_marginTop="@dimen/margin_small"
        android:layout_marginBottom="@dimen/margin_small"
        android:layout_marginLeft="@dimen/margin_16">
    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/txt_InsAddress"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text=""
        android:textColor="@android:color/black"
        android:textSize="14sp"
        android:gravity="center_vertical"


        />
        <TextView
            android:id="@+id/txt_Asset_Ref"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textColor="#447aff"
            android:textStyle="italic"
            android:minHeight="1dp"
            android:textSize="13sp"/>
    <TextView xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/txt_InsInfo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="#959595"
        android:textStyle="italic"
        android:text=""
        android:textSize="13sp"
        android:gravity="center_vertical"

        />
    </LinearLayout>
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:orientation="vertical" android:layout_width="match_parent"
        android:layout_height="match_parent" android:layout_weight="0.9">
        <ImageButton android:layout_width="25dp" android:layout_height="25dp"

            android:textColor="@color/intercom_inbox_count_background" android:id="@+id/btn_Inspection_Action"

            android:background="@drawable/info"
            android:layout_marginTop="15dp"
            android:layout_marginLeft="5dp" />

    </LinearLayout>
</LinearLayout>