<?xml version="1.0" encoding="utf-8"?>

<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="74dp">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/margin_25"
        android:layout_gravity="center_vertical">

        <ImageButton
            android:layout_height="48dp"
            android:layout_width="48dp"
            android:id="@+id/btn_TakeVideo"
            android:scaleType="centerInside"
            android:background="@drawable/ic_camera"
            />
    </RelativeLayout>
    <HorizontalScrollView
        android:layout_height="wrap_content"
        android:minHeight="60dp"
        android:layout_gravity="center_vertical"
        android:id="@+id/sv_DisplayPhotos"
        android:layout_marginTop="4dp"
        android:layout_weight="1"
        android:layout_marginBottom="4dp"
        android:layout_width="0dp"
        android:layout_marginStart="5dp"
        android:layout_marginEnd="@dimen/margin_25"
        android:scrollbars="horizontal">
        <LinearLayout
            android:layout_height="match_parent"
            android:id="@+id/ll_videoList"
            android:orientation="horizontal"
            android:layout_width="wrap_content">
        </LinearLayout>
    </HorizontalScrollView>
</LinearLayout>