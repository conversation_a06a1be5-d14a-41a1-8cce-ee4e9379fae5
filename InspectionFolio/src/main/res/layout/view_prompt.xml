<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:background="#3496F2"
    android:visibility="gone">
    <ImageView
        android:layout_margin="@dimen/margin_small"
        android:layout_width="45dp"
        android:layout_height="45dp"
        android:src="@drawable/ic_idea"
        tools:ignore="ContentDescription" />
    <TextView
        android:id="@+id/view_prompt_tv_message"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:textSize="12sp"
        android:textColor="@color/intercom_white"
        android:layout_weight="1"
        android:gravity="center_vertical"/>
    <Button
        android:id="@+id/view_prompt_btn_close"
        android:layout_margin="@dimen/margin_small"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:background="@drawable/intercom_close" />
</LinearLayout>