<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    android:orientation="vertical"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    android:background="@color/colorPrimary"
    xmlns:android="http://schemas.android.com/apk/res/android" >

    <EditText
        android:id="@+id/search_asset"
        android:layout_width="match_parent"
        android:layout_height="36dp"
        android:layout_marginLeft="@dimen/margin_16"
        android:layout_marginRight="@dimen/margin_16"
        android:layout_marginTop="@dimen/margin_small"
        android:layout_marginBottom="@dimen/margin_small"
        android:hint="@string/search"
        android:paddingLeft="@dimen/margin_16"
        android:background="@drawable/bg_search_bar"
        android:textSize="16dp"
        style="@style/EditText"
        android:imeOptions="actionSearch"
        android:drawableLeft="@drawable/icn_search"
        android:drawablePadding="8dp"
        android:inputType="textVisiblePassword"/>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/white"
        android:paddingBottom="58dp">
        <com.softw4re.views.InfiniteListView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:id="@+id/lv_Assets"
            android:divider="#C7C7CC"
            android:dividerHeight="1px"
            android:background="@android:color/white">
        </com.softw4re.views.InfiniteListView>

    </RelativeLayout>

</LinearLayout>