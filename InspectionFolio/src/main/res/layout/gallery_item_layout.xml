<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:id="@+id/gl_imageView"
        android:src="@drawable/camera"/>
    <TextView
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:textAppearance="?android:attr/textAppearanceMedium"
        android:id="@+id/page_label"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:background="@drawable/crop_view_shape"
        android:text="1"
        android:gravity="center"
        android:textColor="#ffffff"
        android:textSize="18sp" />
</RelativeLayout>
