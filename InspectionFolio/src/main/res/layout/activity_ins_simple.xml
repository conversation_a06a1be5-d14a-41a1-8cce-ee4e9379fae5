<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorPrimary"
    tools:context=".activity.if_ins_simple">

    <ListView xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_marginTop="?android:actionBarSize"
        android:layout_width="match_parent" android:layout_height="match_parent"
        android:id="@+id/lv_ins_simple"
        android:divider="#CCCCCC"
        android:dividerHeight="2px"
        android:background="@android:color/white">

    </ListView>
    <LinearLayout
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_gravity="center_horizontal|bottom"
        android:background="#f8f6f6">

        <TextView
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:textAppearance="?android:attr/textAppearanceMedium"
            android:text=""
            android:id="@+id/txt_simtimer"
            android:background="#00ffffff"
            android:layout_gravity="center_horizontal"
            android:textColor="#000000"
            android:gravity="center_vertical|center_horizontal" />
    </LinearLayout>
</FrameLayout>
