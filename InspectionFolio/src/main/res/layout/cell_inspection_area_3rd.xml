<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">
    <TextView
        android:id="@+id/tv_title"
        android:textSize="18sp"
        android:textColor="@android:color/black"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="15dp"
        android:layout_marginRight="@dimen/margin_10"
        android:layout_marginEnd="@dimen/margin_10"
        android:layout_marginLeft="@dimen/margin_20"
        android:layout_marginStart="@dimen/margin_20"
        android:layout_weight="1"
        />
    <ImageView
        android:id="@+id/cell_accessory_icon"
        android:src="@drawable/check"
        android:layout_height="20dp"
        android:layout_width="20dp"
        android:layout_gravity="center_vertical"
        android:layout_marginRight="@dimen/margin_20"
        android:layout_marginEnd="@dimen/margin_20"
        />
</LinearLayout>