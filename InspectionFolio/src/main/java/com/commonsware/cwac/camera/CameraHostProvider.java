/***
  Copyright (c) 2013 CommonsWare, LLC
  Portions Copyright (C) 2007 The Android Open Source Project
  
  Licensed under the Apache License, Version 2.0 (the "License"); you may
  not use this file except in compliance with the License. You may obtain
  a copy of the License at
    http://www.apache.org/licenses/LICENSE-2.0
  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
 */

package com.commonsware.cwac.camera;

/**
 * Interface that needs to be implemented on activities that
 * inflate layouts containing a CameraView widget, so that
 * the widget can obtain its CameraHost immediately.
 */
public interface CameraHostProvider {
  /**
   * @return the CameraHost to be used by the CameraView
   */
  CameraHost getCameraHost();
}
