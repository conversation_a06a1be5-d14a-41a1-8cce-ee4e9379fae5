package com.nakardo.atableview.internal;

import android.content.Context;
import android.view.LayoutInflater;
import android.widget.Button;
import android.widget.FrameLayout;

//import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.R;
import com.nakardo.atableview.uikit.UILabel;
import com.nakardo.atableview.view.ATableView;
import com.nakardo.atableview.view.ATableView.ATableViewStyle;

public class ATableViewHeaderFooterCell extends FrameLayout {	
	public enum ATableViewHeaderFooterCellType { Header, Footer };
	
	private UILabel mTextLabel;
    private Button oButton;
	
	protected static int getLayout(ATableViewHeaderFooterCellType type, ATableView tableView) {
		ATableViewStyle style = tableView.getStyle();
		if (ATableViewStyle.Grouped == style) {
			if (ATableViewHeaderFooterCellType.Header == type) {
				return R.layout.atv_grouped_header;
			}
			
			return R.layout.atv_grouped_footer;
		}
		
		return R.layout.atv_plain_header_footer;
	}
	
	public ATableViewHeaderFooterCell(ATableViewHeaderFooterCellType type, ATableView tableView) {
		super(tableView.getContext());
		LayoutInflater.from(getContext()).inflate(getLayout(type, tableView), this, true);
		
		mTextLabel = (UILabel) findViewById(R.id.textLabel);
        if (type == ATableViewHeaderFooterCellType.Header){
            oButton = (Button) findViewById(R.id.btn_HeaderAction);
       }
	}
	
	public ATableViewHeaderFooterCell(Context context) {
		super(context);
	}
	
	public UILabel getTextLabel() {
		return mTextLabel;
	}
    public Button getButton(){
        return oButton;
    }
}
