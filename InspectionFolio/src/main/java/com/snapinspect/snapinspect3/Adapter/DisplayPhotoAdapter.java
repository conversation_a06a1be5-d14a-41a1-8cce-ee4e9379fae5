package com.snapinspect.snapinspect3.Adapter;

import android.app.Activity;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.viewpager.widget.PagerAdapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.ProgressBar;

import com.snapinspect.snapinspect3.Helper.CommonUI_Photo;
import com.snapinspect.snapinspect3.Helper.ViewUtils;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.ThrottledSearch;

import java.util.ArrayList;
import java.util.List;

public class DisplayPhotoAdapter extends PagerAdapter {

    public interface Callback {
        void commentsChanged(String sComments);
    }

    private List<ai_Photo> photos;
    private final LayoutInflater inflater;
    private final Context mContext;
    private final int photoWidth;
    private final int photoHeight;
    private Callback mCallback;

    public DisplayPhotoAdapter(Context context, int photoWidth, int photoHeight) {
        mContext = context;
        inflater = LayoutInflater.from(context);
        photos = new ArrayList<>();
        this.photoWidth = photoWidth;
        this.photoHeight = photoHeight;
    }

    public void setPhotos(List<ai_Photo> photos) {
        this.photos = photos;
        this.notifyDataSetChanged();
    }

    public void setCallback(Callback callback) {
        mCallback = callback;
    }

    @Override
    public void destroyItem(ViewGroup container, int position, Object object) {
        container.removeView((View) object);
    }

    @Override
    public int getItemPosition(@NonNull Object object) {
        return POSITION_NONE;
    }

    @Override
    public int getCount() {
        return photos.size();
    }

    @Override
    public Object instantiateItem(ViewGroup view, int position) {
        View cell = inflater.inflate(R.layout.cell_photo_comments, view, false);

        ImageView photoView = cell.findViewById(R.id.img_photo_view);
        ViewUtils.setViewSize(photoView, photoWidth, photoHeight);

        ProgressBar progressBar = cell.findViewById(R.id.indicator_view);
        ai_Photo photo = photos.get(position);
        if (photo != null) {
            CommonUI_Photo.showFullFile(mContext, photo.getId(), photoView, progressBar);
        }

        EditText tvComments = cell.findViewById(R.id.edit_PhotoComments);
        ViewUtils.setViewWidth(tvComments, photoWidth);
        tvComments.setText(photo.sComments);

        if (mContext instanceof Activity) {
            new ThrottledSearch((Activity) mContext, this::sCommentsChanged).bindTo(tvComments);
        }

        view.addView(cell, 0);
        return cell;
    }

    private void sCommentsChanged(String s) {
        if (mCallback != null) mCallback.commentsChanged(s);
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view.equals(object);
    }

}