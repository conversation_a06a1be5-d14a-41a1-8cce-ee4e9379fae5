package com.snapinspect.snapinspect3.Adapter;

import android.app.Activity;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageButton;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.SimpleTextWatcher;
import com.woxthebox.draglistview.DragItem;
import com.woxthebox.draglistview.DragItemAdapter;

import java.util.ArrayList;
import java.util.List;

public final class InsRoomEditAdapter extends DragItemAdapter<Pair<Long, ai_InsItem>, InsRoomEditAdapter.ViewHolder> {
    private final Context mContext;
    private SharedListener mCallback;
    private boolean bRequestIns = false;

    public InsRoomEditAdapter(Context context) {
        mContext = context;
        setHasStableIds(true);
    }

    public InsRoomEditAdapter(List<ai_InsItem> dataSource, Context context) {
        mContext = context;
        setHasStableIds(true);
        setDataSource(dataSource);
    }

    public List<ai_InsItem> getDataSource() {
        ArrayList<ai_InsItem> insItems = new ArrayList<>();
        List<Pair<Long, ai_InsItem>> dataSource = getItemList();

        if (dataSource == null || dataSource.isEmpty()) return new ArrayList<>();
        for (int i = 0; i < dataSource.size(); i++) {
            insItems.add(dataSource.get(i).second);
        }
        return insItems;
    }

    public void setDataSource(List<ai_InsItem> dataSource) {
        ArrayList<Pair<Long, ai_InsItem>> pairs = new ArrayList<>();
        for (int i = 0; i < dataSource.size(); i++) {
            pairs.add(new Pair<>((long) i, dataSource.get(i)));
        }
        setItemList(pairs);
    }

    public void SetRequestIns(boolean _bRequestIns) {
        bRequestIns = _bRequestIns;
    }


    @Override
    public long getUniqueItemId(int position) {
        return mItemList.get(position).first;
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        super.onBindViewHolder(holder, position);
        holder.editAreaName.setTag(position);
        holder.btnDelete.setTag(position);

        ai_InsItem item = mItemList.get(position).second;
        holder.editAreaName.setText(item.sName);
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(R.layout.cell_if_inspection_edit_3rd, parent, false);
        return new ViewHolder(view);
    }

    public void setSharedListener(SharedListener mCallback) {
        this.mCallback = mCallback;
    }

    public interface SharedListener {
        void deleteInsItem(ai_InsItem insItem);

        void updateInsRoomName(String sName, ai_InsItem insItem);
    }

    public static final class InsRoomDragItem extends DragItem {
        private boolean bRequestIns = false;

        public InsRoomDragItem(Context context, boolean _bRequestIns) {
            super(context, R.layout.cell_if_inspection_edit_3rd);
            bRequestIns = _bRequestIns;
        }

        @Override
        public void onBindDragView(View clickedView, View dragView) {
            CharSequence areaName = ((EditText) clickedView.findViewById(R.id.edit_Area_Name)).getText();
            Drawable btnDelDrawable = ((ImageButton) clickedView.findViewById(R.id.btn_Delete)).getDrawable();

            EditText editName = dragView.findViewById(R.id.edit_Area_Name);
            editName.setText(areaName);

            ImageButton btnDel = dragView.findViewById(R.id.btn_Delete);
            btnDel.setImageDrawable(btnDelDrawable);
            if (bRequestIns) {
                btnDel.setVisibility(View.GONE);
            }

            dragView.setBackgroundColor(dragView.getResources().getColor(R.color.gray_color));
        }
    }

    class ViewHolder extends DragItemAdapter.ViewHolder {
        EditText editAreaName;
        ImageButton btnDelete;

        ViewHolder(final View itemView) {
            super(itemView, R.id.order_handler, false);
            editAreaName = itemView.findViewById(R.id.edit_Area_Name);
            btnDelete = itemView.findViewById(R.id.btn_Delete);

            if (bRequestIns) {
                editAreaName.setEnabled(false);

                btnDelete.setVisibility(View.GONE);
            }

            btnDelete.setOnClickListener(v -> {
                int pos = (int) btnDelete.getTag();
                ai_InsItem item = mItemList.get(pos).second;
                mCallback.deleteInsItem(item);
            });

            editAreaName.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                    String sName = editAreaName.getText().toString();
                    int pos = (int) btnDelete.getTag();
                    ai_InsItem insItem = mItemList.get(pos).second;
                    if (insItem != null && !insItem.sName.equals(sName)) {
                        mCallback.updateInsRoomName(sName, insItem);
                    }
                }
            });

            if (mContext instanceof Activity) {
                Activity editAty = (Activity) mContext;
                editAreaName.setOnFocusChangeListener((v, hasFocus) -> {
                    if (!hasFocus) CommonHelper.hideSoftKeyboard(editAty);
                });

                editAreaName.setOnEditorActionListener((v, actionId, event) -> {
                    if (actionId == EditorInfo.IME_ACTION_DONE) {
                        CommonHelper.hideSoftKeyboard(editAty);
                        return true;
                    }
                    return false;
                });
            }
        }

        @Override
        public void onItemClicked(View view) {
        }

        @Override
        public boolean onItemLongClicked(View view) {
            return true;
        }
    }
}
