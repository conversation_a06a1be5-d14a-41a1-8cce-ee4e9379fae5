package com.snapinspect.snapinspect3.Adapter;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.EditText;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.ColorRes;
import androidx.annotation.LayoutRes;
import androidx.annotation.NonNull;

import androidx.core.content.res.ResourcesCompat;
import com.nex3z.flowlayout.FlowLayout;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonDB_Notification;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.CommonUI_Photo;
import com.snapinspect.snapinspect3.Helper.ViewUtils;
import com.snapinspect.snapinspect3.IF_Object.TaskPriority;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_NoticeCategory;
import com.snapinspect.snapinspect3.IF_Object.ai_Notification;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.IF_Object.ai_User;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;
import com.snapinspect.snapinspect3.IF_Object.if_FormItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activity.if_Notice;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.TextViewUtils;
import com.snapinspect.snapinspect3.util.ThrottledSearch;
import com.snapinspect.snapinspect3.views.RoundTextView;
import com.snapinspect.snapinspect3.views.RoundedImageView;

import java.io.File;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static android.view.Gravity.CENTER;
import static android.view.View.GONE;

public final class NoticeFormsAdapter extends BaseAdapter {

    // The maximum number of photos allowed to be displayed
    private final int maximumPhotos;

    public interface Delegate {
        void reloadData(int position);

        void didSelectDueDate();
        void didSelectAssignUser();
        void didSelectTaskPriority();

        void updateNoticeParam(String identifier, String value);

        void didTakePhotos();
        void didTakeVideos();

        void didViewAllPhotos();
        void didViewPhoto(int photoId);
        void didPlayVideo(long iVideoID, String sVideoFilePath);
    }

    private final Delegate mDelegate;
    private final Context mContext;
    private final LayoutInflater mInflater;
    private final HashMap<Integer, if_FormItem> mDataSource = new HashMap<>();
    private final List<ai_NoticeCategory> arrNoticeCategory = CommonDB_Notification.getAllNoticeCategories();

    public NoticeFormsAdapter(List<if_FormItem>items, Context ctx, @NonNull Delegate delegate) {
        mContext = ctx;
        setDataSource(items);
        mInflater = LayoutInflater.from(ctx);
        mDelegate = delegate;
        maximumPhotos = CommonUI.isTablet(ctx) ? 10 : 5;
    }

    public void setDataSource(List<if_FormItem>items) {
        mDataSource.clear();
        for (int i = 0; i < items.size(); i++) {
            mDataSource.put(i, items.get(i));
        }
    }

    public List<if_FormItem> getDataSource() {
        return new ArrayList<>(mDataSource.values());
    }

    @Override
    public int getCount() {
        return mDataSource.size();
    }

    @Override
    public Object getItem(int position) {
        return mDataSource.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @SuppressLint({"SetTextI18n", "ViewHolder"})
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if_FormItem item = (if_FormItem) getItem(position);

        @LayoutRes int layoutRes = 0;
        if (item.viewType == if_FormItem.ViewType.unknown) {
            if (item.identifier.equals(if_FormItem.Identifiers.taskMisc)) {
                layoutRes = R.layout.cell_form_task_misc;
            }
        } else {
            layoutRes = item.getLayoutRes();
        }

        if (convertView != null && convertView.getTag() != null) {
            ViewHolder viewHolder = (ViewHolder) convertView.getTag();
            // Unbind ThrottledSearch
            if (viewHolder != null && viewHolder.throttledSearch != null) {
                viewHolder.throttledSearch.unbind();
            }
        }

        convertView = mInflater.inflate(layoutRes, parent, false);
        ViewHolder viewHolder = new ViewHolder(convertView);
        convertView.setTag(viewHolder);

        // Update Styles
        if (item.viewType == if_FormItem.ViewType.textInput) {
            boolean isSingleLine = !item.identifier.equals(if_FormItem.Identifiers.taskDescription);
            viewHolder.editText.setSingleLine(isSingleLine);
            viewHolder.editText.setText(item.value);

            updateTextColor(viewHolder.txtTitle, R.color.placeholder_color);
            updateTextColor(viewHolder.editText, R.color.dark_input_text_color);

            viewHolder.throttledSearch = new ThrottledSearch((Activity) mContext, value -> {
                if (!StringUtils.isEmpty(value) && !value.equalsIgnoreCase(item.value))
                    item.validateResult = if_FormItem.ValidateResult.none;
                item.value = value;
                updateBottomLine(viewHolder, item);
                mDelegate.updateNoticeParam(item.identifier, value);
            });
            viewHolder.throttledSearch.bindTo(viewHolder.editText);
        } else if (item.viewType == if_FormItem.ViewType.tagSelect) {
            viewHolder.tagsLayout.removeAllViews();
            for (ai_NoticeCategory noticeCategory : arrNoticeCategory) {
                viewHolder.tagsLayout.addView(buildCategoryTagView(position, noticeCategory, item));
            }
        } else if (item.viewType == if_FormItem.ViewType.photos) {
            ArrayList<String> photoIds = new ArrayList<>();
            if (!StringUtils.isEmpty(item.value)) {
                photoIds.addAll(Arrays.asList(item.value.split(",")));
            }

            viewHolder.btnTakePhoto.setOnClickListener(v -> mDelegate.didTakePhotos());
            ViewUtils.setVisible(viewHolder.viewSignPlus, photoIds.isEmpty());
            ViewUtils.setVisible(viewHolder.viewNumOfPhotos, !photoIds.isEmpty());
            viewHolder.txtNumOfPhotos.setText("" + photoIds.size());

            viewHolder.layoutPhotoList.removeAllViews();
            final int maxPhotos = Math.min(maximumPhotos, photoIds.size());
            for (int i = 0; i < maxPhotos; i++) {
                View photoView = buildPhotoView(CommonHelper.getInt(photoIds.get(i)), false);
                if (photoView == null) continue;
                viewHolder.layoutPhotoList.addView(photoView);
            }

            if (photoIds.size() > maxPhotos) { // Show All
                viewHolder.layoutPhotoList.addView(buildPhotoView(-1, true));
            }
        } else if (item.viewType == if_FormItem.ViewType.videos) {
            viewHolder.btnTakeVideo.setOnClickListener(v -> mDelegate.didTakeVideos());
            viewHolder.layoutVideoList.removeAllViews();

            long videoId = CommonHelper.getInt(item.value);
            if (videoId > 0) {
                FrameLayout frameLayout = new FrameLayout(mContext);
                FrameLayout.LayoutParams photoLayoutParams = new FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
                );
                RoundedImageView oImageView = new RoundedImageView(mContext);
                oImageView.setLayoutParams(photoLayoutParams);
                frameLayout.addView(oImageView);

                final int width = CommonHelper.pxFromDp(mContext, 48);
                final int height = CommonHelper.pxFromDp(mContext, 48);
                LinearLayout.LayoutParams frameLayoutParams = new LinearLayout.LayoutParams(width, height);
                frameLayoutParams.setMargins(
                        CommonHelper.pxFromDp(mContext, 10), CommonHelper.pxFromDp(mContext, 5),
                        CommonHelper.pxFromDp(mContext, 0), CommonHelper.pxFromDp(mContext, 5));
                frameLayout.setLayoutParams(frameLayoutParams);

                viewHolder.layoutVideoList.addView(frameLayout);

                final ai_Video video = CommonDB.GetVideoByIdSugar(videoId);
                final Bitmap bitmap = BitmapFactory.decodeFile(video.getThumb());
                oImageView.setImageBitmap(bitmap);
                oImageView.setOnClickListener(v -> mDelegate.didPlayVideo(videoId, video.getFile()));
            }
        } else if (item.viewType == if_FormItem.ViewType.unknown) {
            if (item.identifier.equals(if_FormItem.Identifiers.taskMisc)) {
                String dueDate = CommonJson.GetJsonKeyValue(ai_Notification.CustomKeys.dueDate, item.value);
                try {
                    Date date = if_Notice.dueDateFormat.parse(dueDate);
                    dueDate = DateUtils.formatAsTaskDueDate(date);
                } catch (Exception ignored) { }
                viewHolder.txtDueDate.setText(StringUtils.isEmpty(dueDate) ? "Due Date" : dueDate);

                String priorityName = CommonJson.GetJsonKeyValue(ai_Notification.CustomKeys.priority, item.value);
                TaskPriority taskPriority = TaskPriority.getTaskPriority(CommonHelper.getInt(priorityName));
                if (taskPriority != null) {
                    TextViewUtils.updateText(viewHolder.txtPriorityName, taskPriority.getDisplayName());
                    viewHolder.txtPriorityName.setCornerRadius(10);
                    viewHolder.txtPriorityName.setSolidColor(
                        ResourcesCompat.getColor(mContext.getResources(), taskPriority.getBackgroundColor(), null)
                    );
                }

                int assignedUserID = CommonHelper.getInt(
                    CommonJson.GetJsonKeyValue(ai_Notification.CustomKeys.assignedToUser, item.value)
                );
                ai_User assigned = CommonDB.GetUserSugar(assignedUserID);
                if (assigned.iCustomerID > 0) {
                    TextViewUtils.updateText(viewHolder.txtAssignTo, assigned.getNameLetters());
                    viewHolder.txtAssignTo.setCornerRadius(22);
                    viewHolder.txtAssignTo.setSolidColor(
                        ResourcesCompat.getColor(mContext.getResources(), R.color.task_assign_round_background, null)
                    );
                    viewHolder.iconAssignedUser.setVisibility(View.GONE);
                } else {
                    viewHolder.iconAssignedUser.setVisibility(View.VISIBLE);
                    viewHolder.txtAssignTo.setVisibility(View.GONE);
                }

                viewHolder.layoutDueDate.setOnClickListener(v -> mDelegate.didSelectDueDate());
                viewHolder.layoutAssignTask.setOnClickListener(v -> mDelegate.didSelectAssignUser());
                viewHolder.layoutTaskPriority.setOnClickListener(v -> mDelegate.didSelectTaskPriority());
            }
        }

        TextViewUtils.updateText(viewHolder.txtTitle, item.title);
        updateBottomLine(viewHolder, item);

        return convertView;
    }

    private View buildPhotoView(final int photoId, boolean showAll) {
        final int width = CommonHelper.pxFromDp(mContext, 48);
        final int height = CommonHelper.pxFromDp(mContext, 48);
        final int radius = CommonHelper.pxFromDp(mContext, 24);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(width, height);
        layoutParams.setMargins(
                CommonHelper.pxFromDp(mContext, 10), CommonHelper.pxFromDp(mContext, 5),
                CommonHelper.pxFromDp(mContext, 0), CommonHelper.pxFromDp(mContext, 5));

        View view = null;
        if (showAll) {
            RoundTextView tvMore = new RoundTextView(mContext);
            tvMore.setSolidColor(mContext.getResources().getColor(R.color.gray_border_color));
            tvMore.setText("Show\nAll");
            tvMore.setCornerRadius(radius);
            tvMore.setTextColor(mContext.getResources().getColor(R.color.placeholderColor));
            tvMore.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
            tvMore.setGravity(Gravity.CENTER);
            tvMore.setLayoutParams(layoutParams);
            tvMore.setOnClickListener(v -> mDelegate.didViewAllPhotos());
            view = tvMore;
        } else {
            ai_Photo oPhoto = CommonDB.GetPhotoByIdSugar(photoId);
            if (oPhoto.getId() > 0) {
                FrameLayout frameLayout = new FrameLayout(mContext);
                FrameLayout.LayoutParams photoLayoutParams = new FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
                );
                RoundedImageView oImageView = new RoundedImageView(mContext);
                oImageView.setLayoutParams(photoLayoutParams);
                frameLayout.addView(oImageView);

                ProgressBar progressBar = new ProgressBar(mContext);
                progressBar.setIndeterminate(true);
                progressBar.setVisibility(GONE);

                int barSize = (int) (width * 0.4);
                FrameLayout.LayoutParams barLayoutParams = new FrameLayout.LayoutParams(barSize, barSize);
                barLayoutParams.gravity = CENTER;
                progressBar.setLayoutParams(barLayoutParams);
                frameLayout.addView(progressBar);

                LinearLayout.LayoutParams frameLayoutParams = new LinearLayout.LayoutParams(width, height);
                frameLayoutParams.setMargins(
                        CommonHelper.pxFromDp(mContext, 10), CommonHelper.pxFromDp(mContext, 5),
                        CommonHelper.pxFromDp(mContext, 0), CommonHelper.pxFromDp(mContext, 5));
                frameLayout.setLayoutParams(frameLayoutParams);
                CommonUI_Photo.showPhotoThumbFile(mContext, oPhoto.getId(), oImageView, progressBar);

                if (new File(oPhoto.getThumb()).exists()) {
                    oImageView.setOnClickListener(v -> mDelegate.didViewPhoto(photoId));
                }

                view = frameLayout;
            }
        }

        return view;
    }

    @SuppressLint("UseCompatLoadingForDrawables")
    private View buildCategoryTagView(int position, ai_NoticeCategory noticeCategory, if_FormItem formItem) {
        View tagView = mInflater.inflate(R.layout.cell_notice_category_tag, null);
        TextView textName = tagView.findViewById(R.id.tag_category_name);
        textName.setText(noticeCategory.sName);
        ImageView selectIcon = tagView.findViewById(R.id.tag_selected_icon);

        Resources resources = mContext.getResources();
        boolean equal = !StringUtils.isEmpty(formItem.value) && noticeCategory.sName.equals(formItem.value);
        if (equal) {
            tagView.setBackground(ResourcesCompat.getDrawable(resources, R.drawable.bg_notice_cat_tag_selected, null));
            updateTextColor(textName, R.color.white_color);
            selectIcon.setVisibility(View.VISIBLE);
        } else {
            tagView.setBackground(ResourcesCompat.getDrawable(resources, R.drawable.bg_notice_cat_tag_normal, null));
            updateTextColor(textName, R.color.notice_cat_tag_text);
            selectIcon.setVisibility(View.GONE);
        }

        tagView.setOnClickListener(v -> {
            formItem.value = noticeCategory.sName;
            mDelegate.updateNoticeParam(if_FormItem.Identifiers.taskCategory, noticeCategory.sName);
            mDelegate.reloadData(position);
        });

        return tagView;
    }

    private void updateTextColor(TextView textView, @ColorRes int colorRes) {
        if (textView == null) return;
        textView.setTextColor(mContext.getResources().getColor(colorRes));
    }

    private void updateBottomLine(ViewHolder viewHolder, if_FormItem item) {
        if (viewHolder.lBottomHasValue != null && viewHolder.lBottomNoValue != null) {
            Resources resources = mContext.getResources();
            boolean validateFailed = item.validateResult == if_FormItem.ValidateResult.failure;
            viewHolder.lBottomHasValue.setBackgroundColor(resources.getColor(
                 validateFailed ? R.color.item_invalid_line_color : R.color.item_edited_line_color
            ));
            boolean hasValue = (!StringUtils.isEmpty(item.value) || validateFailed) && item.selectable;
            viewHolder.lBottomHasValue.setVisibility(hasValue ? View.VISIBLE : View.GONE);
            viewHolder.lBottomNoValue.setVisibility(!hasValue ? View.VISIBLE : View.GONE);
        }
    }

    private static class ViewHolder {
        ThrottledSearch throttledSearch = null;
        TextView txtTitle;
        TextView txtDueDate;
        TextView txtNumOfPhotos;
        RoundTextView txtPriorityName;
        RoundTextView txtAssignTo;
        EditText editText;
        View lBottomHasValue;
        View lBottomNoValue;
        View viewNumOfPhotos;
        View viewSignPlus;
        FlowLayout tagsLayout;
        LinearLayout layoutDueDate;
        LinearLayout layoutAssignTask;
        LinearLayout layoutTaskPriority;
        LinearLayout layoutPhotoList;
        ImageView iconAssignedUser;
        ImageButton btnTakePhoto;
        ImageButton btnTakeVideo;
        LinearLayout layoutVideoList;

        ViewHolder(@NonNull View rootView) {
            try {
                txtTitle = rootView.findViewById(R.id.txt_form_item_title);
                editText = rootView.findViewById(R.id.edit_item_value);
                lBottomHasValue = rootView.findViewById(R.id.line_bottom_has_value);
                lBottomNoValue = rootView.findViewById(R.id.line_bottom_no_value);
                tagsLayout = rootView.findViewById(R.id.tags_flow_layout);

                layoutDueDate = rootView.findViewById(R.id.layout_dueDate);
                layoutAssignTask = rootView.findViewById(R.id.layout_assignTask);
                layoutTaskPriority = rootView.findViewById(R.id.layout_taskPriority);

                txtDueDate = rootView.findViewById(R.id.txt_dueDate);
                txtPriorityName = rootView.findViewById(R.id.txt_PriorityName);
                txtAssignTo = rootView.findViewById(R.id.txt_assign_to);
                iconAssignedUser = rootView.findViewById(R.id.icon_assigned_user);

                txtNumOfPhotos = rootView.findViewById(R.id.txt_numOfPhotos);
                viewNumOfPhotos = rootView.findViewById(R.id.view_numOfPhotos);
                viewSignPlus = rootView.findViewById(R.id.view_math_sign_plus);
                btnTakePhoto = rootView.findViewById(R.id.btn_TakePhoto);
                layoutPhotoList = rootView.findViewById(R.id.ll_PhotoList);

                btnTakeVideo = rootView.findViewById(R.id.btn_TakeVideo);
                layoutVideoList = rootView.findViewById(R.id.ll_videoList);
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception(ex);
            }
        }
    }
}
