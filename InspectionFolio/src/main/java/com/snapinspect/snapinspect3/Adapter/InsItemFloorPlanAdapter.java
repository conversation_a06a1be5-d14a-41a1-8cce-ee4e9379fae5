package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.core.content.ContextCompat;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_FloorPlan;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.if_InsItemFloorPlans.FloorPlanAnnotation;
import com.snapinspect.snapinspect3.util.ArrayUtils;
import com.snapinspect.snapinspect3.views.FloorPlanView;
import com.snapinspect.snapinspect3.views.StickyHeaderListView.StickyHeaderAdapter;

import java.util.ArrayList;
import java.util.List;

public class InsItemFloorPlanAdapter extends StickyHeaderAdapter {
    public interface Listener {
        void onClickOnFloorPlan(ai_FloorPlan floorPlan, FloorPlanType floorPlanType);
    }

    public enum FloorPlanType {
        ANNOTATED, AVAILABLE;

        public String title() {
            switch (this) {
                case ANNOTATED:
                    return "Blueprint Annotated";
                case AVAILABLE:
                    return "Blueprint Available";
                default:
                    return "";
            }
        }
    }
    private final Context mContext;
    private final Listener mListener;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private final List<Pair<FloorPlanView.FloorPlanViewModel, FloorPlanView.FloorPlanViewModel>> lsAnnotatedFloorPlanPair;
    private final List<Pair<FloorPlanView.FloorPlanViewModel, FloorPlanView.FloorPlanViewModel>> lsFloorPlanPair;

    /**
     * Initializes an instance of InsItemFloorPlanAdapter.
     *
     * @param context              The context in which the adapter is being used.
     * @param lsAnnotatedFloorPlan A list of annotated blueprints.
     * @param lsFloorPlan          A list of available blueprints.
     * @param listener             The listener for item click events.
     */
    public InsItemFloorPlanAdapter(Context context, List<FloorPlanAnnotation> lsAnnotatedFloorPlan, List<ai_FloorPlan> lsFloorPlan, Listener listener) {
        super();
        mContext = context;
        mListener = listener;

        // Available Blueprints
        List<Pair<FloorPlanView.FloorPlanViewModel, FloorPlanView.FloorPlanViewModel>> lsFloorPlanAvailable = new ArrayList<>();
        List<List<ai_FloorPlan>> groups = ArrayUtils.split(lsFloorPlan, Constants.Limits.kFloorPlanGroupSize);
        for (List<ai_FloorPlan> group : groups) {
            Pair<FloorPlanView.FloorPlanViewModel, FloorPlanView.FloorPlanViewModel> item = new Pair<>(
                new FloorPlanView.FloorPlanViewModel(group.get(0), 0, 0),
                group.size() > 1 ? new FloorPlanView.FloorPlanViewModel(group.get(1), 0, 0) : null);
            lsFloorPlanAvailable.add(item);
        }
        lsFloorPlanPair = lsFloorPlanAvailable;

        // Annotated Blueprints
        List<Pair<FloorPlanView.FloorPlanViewModel, FloorPlanView.FloorPlanViewModel>> lsFloorPlanAnnotated = new ArrayList<>();
        List<List<FloorPlanAnnotation>> groupsAnnotated = ArrayUtils.split(lsAnnotatedFloorPlan, Constants.Limits.kFloorPlanGroupSize);
        for (List<FloorPlanAnnotation> group : groupsAnnotated) {
            Pair<FloorPlanView.FloorPlanViewModel, FloorPlanView.FloorPlanViewModel> item = new Pair<>(
                new FloorPlanView.FloorPlanViewModel(group.get(0)),
                group.size() > 1 ? new FloorPlanView.FloorPlanViewModel(group.get(1)) : null);

            lsFloorPlanAnnotated.add(item);
        }
        lsAnnotatedFloorPlanPair = lsFloorPlanAnnotated;
    }

    @Override
    public int sectionCounts() {
        return FloorPlanType.values().length;
    }

    @Override
    public int rowCounts(int section) {
        if (section < 0 || section >= FloorPlanType.values().length) return 0;
        switch (FloorPlanType.values()[section]) {
            case ANNOTATED:
                return lsAnnotatedFloorPlanPair.size();
            case AVAILABLE:
                return lsFloorPlanPair.size();
            default:
                return 0;
        }
    }

    @Override
    public View getRowView(int section, int row, View convertView, ViewGroup parent) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.cell_floor_plan, null);
        try {
            Pair<FloorPlanView.FloorPlanViewModel, FloorPlanView.FloorPlanViewModel> item
                    = (Pair<FloorPlanView.FloorPlanViewModel, FloorPlanView.FloorPlanViewModel>) getRowItem(section, row);
            FloorPlanView firstFloorPlanView = view.findViewById(R.id.floor_plan_view_first);
            FloorPlanView lastFloorPlanView = view.findViewById(R.id.floor_plan_view_last);
            firstFloorPlanView.setFloorPlan(item.first);
            lastFloorPlanView.setFloorPlan(item.second);
            firstFloorPlanView.setOnFloorPlanClickListener(floorPlan -> {
                mListener.onClickOnFloorPlan(floorPlan, FloorPlanType.values()[section]);
            });
            lastFloorPlanView.setOnFloorPlanClickListener(floorPlan -> {
                mListener.onClickOnFloorPlan(floorPlan, FloorPlanType.values()[section]);
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_settings.SettingCustomCell", ex, mContext);
        }

        return view;
    }

    @Override
    public View getSectionHeaderView(int section, View convertView, ViewGroup parent) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.header_settings, null);
        view.setBackgroundColor(ContextCompat.getColor(mContext, R.color.white_color));
        TextView textview = view.findViewById(R.id.header_settings_tv_title);
        textview.setText(FloorPlanType.values()[section].title());
        textview.setTextColor(ContextCompat.getColor(mContext, R.color.black));
        LinearLayout.LayoutParams layoutParams = (LinearLayout.LayoutParams) textview.getLayoutParams();
        layoutParams.topMargin = (int) mContext.getResources().getDimension(R.dimen.margin_10);
        layoutParams.bottomMargin = (int) mContext.getResources().getDimension(R.dimen.margin_10);
        textview.setLayoutParams(layoutParams);
        return view;
    }

    @Override
    public Object getRowItem(int section, int row) {
        switch (FloorPlanType.values()[section]) {
            case ANNOTATED:
                return lsAnnotatedFloorPlanPair.get(row);
            case AVAILABLE:
                return lsFloorPlanPair.get(row);
            default:
                return null;
        }
    }

    @Override
    public boolean hasSectionHeaderView(int section) {
        return rowCounts(section) > 0;
    }

}

