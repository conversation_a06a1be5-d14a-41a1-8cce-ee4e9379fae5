package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.R;

import java.util.HashMap;
import java.util.List;

public final class InsAreaAdapter extends BaseAdapter {

    private List<ai_InsItem> mInsItems;
    private final Context mContext;
    private HashMap<ai_InsItem, Integer> mInsMap;

    public InsAreaAdapter(List<ai_InsItem> insItems, Context context) {
        mContext = context;
        setInsItems(insItems);
    }

    public void setInsItems(List<ai_InsItem> insItems) {
        mInsItems = insItems;
        HashMap<ai_InsItem, Integer> map = new HashMap<>();
        for (int i = 0; i < insItems.size(); i++) {
            map.put(insItems.get(i), i);
        }
        mInsMap = map;
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return mInsItems.size();
    }

    @Override
    public Object getItem(int position) {
        return mInsItems.get(position);
    }

    @Override
    public long getItemId(int position) {
        if (position < 0 || position >= mInsMap.size()) return -1;
        return mInsMap.get(getItem(position));
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        View cell = LayoutInflater.from(mContext).inflate(R.layout.cell_inspection_area_3rd, null);
        ai_InsItem insItem = (ai_InsItem) getItem(position);
        TextView titleTxt = cell.findViewById(R.id.tv_title);
        titleTxt.setText(insItem.sName);
        ImageView accessoryIcon = cell.findViewById(R.id.cell_accessory_icon);
        if (!insItem.bInspectionByPassCompulsory &&
                insItem.checkIfNeedValidatingCompulsoryItem(null) && insItem.hasCompulsoryItems()) {
            accessoryIcon.setImageResource(R.drawable.icon_exclamation_sign);
        } else {
            accessoryIcon.setImageResource(insItem.bCompleted ? R.drawable.check : R.drawable.ic_forward);
        }
        return cell;
    }
}
