package com.snapinspect.snapinspect3.Adapter;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.text.format.DateUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.IF_Object.ai_Inbox;
import com.snapinspect.snapinspect3.IF_Object.ai_User;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.CircleView;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.TextViewUtils;
import com.softw4re.views.InfiniteListAdapter;
import fj.data.Either;

import java.util.ArrayList;

import static android.text.format.DateUtils.FORMAT_SHOW_TIME;
import static android.text.format.DateUtils.MINUTE_IN_MILLIS;

public class InboxAdapter extends InfiniteListAdapter<Either<String, ai_Inbox>> {

    public interface Callback {
        void onNewLoadRequired();
        void onRefresh();
        void onItemClick(ai_Inbox item);
        void onItemLongClick(ai_Inbox item);
    }

    private final Callback callback;
    public InboxAdapter(Activity activity, int itemLayoutRes, ArrayList<Either<String, ai_Inbox>> itemList, Callback callback) {
        super(activity, itemLayoutRes, itemList);
        this.callback = callback;
    }

    @SuppressLint("ViewHolder")
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        LayoutInflater inflater = (LayoutInflater) this.getContext().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        Either<String, ai_Inbox> item = getItem(position);
        View cell = null;
        if (item.isRight()) {
            cell = inflater.inflate(R.layout.cell_inbox, parent, false);
            ai_Inbox inbox = item.right().value();
            ai_User oUser = CommonDB.GetUserSugar(inbox.iFromCustomerID);

            TextView txtContent = cell.findViewById(R.id.tv_notification_text);
            String content = "<span style=\"color:black;\">" + oUser.sName + "</span> " +
                    "<span style=\"color:#9C9C9C;\">" + inbox.sComments + "</span>";
            txtContent.setText(StringUtils.convertHtml(content));

            TextView txtTime = cell.findViewById(R.id.tv_notification_time);
            CharSequence relativeDate = DateUtils.getRelativeTimeSpanString(
                    inbox.dateTime.getTime(), System.currentTimeMillis(), MINUTE_IN_MILLIS, FORMAT_SHOW_TIME);
            TextViewUtils.updateText(txtTime, relativeDate.toString());

            TextViewUtils.updateText(cell.findViewById(R.id.view_inbox_cell_short_name), oUser.getNameLetters());

            CircleView nameBgView = cell.findViewById(R.id.name_bg_view);
            nameBgView.setCircleColor(getContext().getResources().getColor(CommonUI.getThemeColor(inbox.iFromCustomerID)));

            ImageView accessoryArrow = cell.findViewById(R.id.accessoryArrow);
            accessoryArrow.setVisibility(inbox.isAvailable() ? View.VISIBLE : View.INVISIBLE);
        } else if (item.isLeft()) {
            cell = inflater.inflate(R.layout.view_inbox_head_cell, parent, false);
            TextViewUtils.updateText(cell.findViewById(R.id.header_cell_tv_title), item.left().value());
            cell.setClickable(false);
        }
        return cell;
    }

    @Override
    public void onNewLoadRequired() {
        callback.onNewLoadRequired();
    }

    @Override
    public void onRefresh() {
        callback.onRefresh();
    }

    @Override
    public void onItemClick(int i) {
        if (getItem(i).isRight())
            callback.onItemClick(getItem(i).right().value());
    }

    @Override
    public void onItemLongClick(int i) {
        if (getItem(i).isRight())
            callback.onItemLongClick(getItem(i).right().value());
    }
}
