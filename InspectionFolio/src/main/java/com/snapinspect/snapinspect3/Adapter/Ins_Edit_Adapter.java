package com.snapinspect.snapinspect3.Adapter;

/*
 * @Created by <PERSON><PERSON><PERSON> on 6/23/17.
 */

import android.annotation.SuppressLint;
import android.content.Context;

import androidx.annotation.NonNull;
import android.text.InputType;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.BaseExpandableListAdapter;
import android.widget.EditText;
import android.widget.ExpandableListView;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
//import com.snapinspect.snapinspect3.Helper.IF_InsRoomHelper;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.R;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_CHK;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_CMT;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_MCHK;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_MSEL;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_NUM;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_PTO;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_SCAN;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_SCHK;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_SEL;


public class Ins_Edit_Adapter extends BaseExpandableListAdapter {
    private final LayoutInflater inflater;
    private final Context mContext;
    private HeaderViewHolder headHolder;
    private ViewHolder viewHolder;
    private ai_InsItem oInsItem, oPInsItem;
    private final Listener mListener;
    public Ins_Edit_Adapter(Context context, Listener listener) {
        inflater = LayoutInflater.from(context);
        mContext = context;
        mListener = listener;
    }

    public interface Listener {
        void onMenuClicked(int position, String sConfig);
        void updateConfig(int position, String sConfig);
        void addConfig(int position);
        void editCommit(int position, String sConfig);
    }

    public void setInsItem(ai_InsItem insItem, ai_InsItem pInsItem) {
        oInsItem = insItem;
        oPInsItem = pInsItem;

        notifyDataSetChanged();
    }

    @Override
    public int getGroupCount() {
        if (oInsItem == null) {
            return 0;
        }
        return 6;
    }

    @Override
    public int getChildrenCount(int i) {
        if (oInsItem == null) {
            return 0;
        }

        String sConfig = CommonHelper.GetConfig(i+1, oInsItem, oPInsItem);
        if (sConfig != null && !sConfig.isEmpty()) {
            if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK)
                    || sConfig.startsWith(SI_S_CONFIG_KEY_SCHK) || sConfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
                String[] arr = getItemsFromsConfig(sConfig);
                if (arr != null) {
                    return arr.length + 1;
                }
            } else if (sConfig.startsWith("{")) {
                String[] arr = getItemsFromsConfig(sConfig);
                if (arr != null) {
                    return arr.length + 1;
                }
            }
        }

        return 1;
    }

    @Override
    public Object getGroup(int i) {
        return null;
    }

    @Override
    public Object getChild(int i, int i1) {
        return null;
    }

    @Override
    public long getGroupId(int i) {
        return i;
    }

    @Override
    public long getChildId(int i, int i1) {
        return i1;
    }

    @Override
    public boolean hasStableIds() {
        return false;
    }

    @Override
    public View getGroupView(int i, boolean b, View convertView, final ViewGroup parent) {
        if(convertView == null){
            headHolder = new HeaderViewHolder();
            convertView = inflater.inflate(R.layout.cell_ins_edit_header, parent, false);

            headHolder.hcontainer = convertView.findViewById(R.id.cell_ins_edit_header_container);
            headHolder.btnMenu = convertView.findViewById(R.id.cell_ins_edit_btn_menu);
            headHolder.tvTitle = convertView.findViewById(R.id.cell_ins_edit_tv_title);
            convertView.setTag(headHolder);

        }else{
            headHolder = (HeaderViewHolder)convertView.getTag();
        }


        final String sConfig = CommonHelper.GetConfig(i+1, oInsItem, oPInsItem);

        if (sConfig != null && !sConfig.isEmpty()) {
            headHolder.tvTitle.setText(getTitleFromsConfig(sConfig));
            headHolder.hcontainer.setVisibility(View.VISIBLE);
        } else {
            headHolder.hcontainer.setVisibility(View.GONE);
        }

        headHolder.btnMenu.setTag(i);
        headHolder.btnMenu.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                int position = Integer.parseInt(view.getTag().toString());

                mListener.onMenuClicked(position+1, sConfig);
            }
        });
        ExpandableListView mExpandableListView = (ExpandableListView) parent;
        mExpandableListView.expandGroup(i);

        return convertView;
    }

    @SuppressLint("SetTextI18n")
    @Override
    public View getChildView(int groupPosition, int childPosition, boolean isExpand, View view, ViewGroup viewGroup) {
        if(view == null){
            viewHolder = new ViewHolder();
            view = inflater.inflate(R.layout.cell_ins_edit_view, viewGroup, false);

            viewHolder.container = view.findViewById(R.id.cell_ins_edit_view_container);

            view.setTag(viewHolder);

        }else{
            viewHolder = (ViewHolder) view.getTag();
        }

        viewHolder.container.removeAllViews();
        final String sConfig = CommonHelper.GetConfig(groupPosition+1, oInsItem, oPInsItem);
        if (sConfig != null) {
            if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK)
                    || sConfig.startsWith(SI_S_CONFIG_KEY_SCHK) || sConfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
                String[] values = getItemsFromsConfig(sConfig);
                if (values != null && childPosition < values.length) {
                    View inflatedLayout = inflater.inflate(R.layout.cell_ins_edit_chk, null, false);
                    viewHolder.container.addView(inflatedLayout);

                    EditText etName = inflatedLayout.findViewById(R.id.cell_ins_edit_chk_et_name);
                    TextView tvSymbol = inflatedLayout.findViewById(R.id.cell_ins_edit_chk_tv_symbol);

                    etName.setText(values[childPosition]);
                    tvSymbol.setText(String.valueOf(values[childPosition].charAt(0)).toUpperCase());
                    ImageView ivMinus = inflatedLayout.findViewById(R.id.cell_ins_edit_chk_btn_minus);
                    ivMinus.setTag(childPosition);
                    ivMinus.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(final View view) {
                            final int position = Integer.parseInt(view.getTag().toString());

//                            AlertDialog.Builder builder = CommonUI.GetAlertBuilder("Message", "Are you sure to Delete this item?",
//                                    mContext, true, false);
//
//                            builder.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
//                                public void onClick(DialogInterface dialog, int which) {
//
//                                    updateSConfigbyDelete(sConfig, position);
//                                }
//                            });
//
//                            builder.show();

                            new MaterialDialog.Builder(mContext)
                                    .title("Message")
                                    .content(R.string.lbl_DeleteItemPrompt, oInsItem.sName)
                                    .positiveText(R.string.tv_ok)
                                    .onPositive(new MaterialDialog.SingleButtonCallback() {
                                        @Override
                                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                            updateSConfigbyDelete(sConfig, position);
                                        }
                                    })
                                    .show();
                        }
                    });

                    etName.setTag(childPosition);

                    etName.setOnEditorActionListener(
                            new EditText.OnEditorActionListener() {
                                @Override
                                public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                                    if (actionId == EditorInfo.IME_ACTION_DONE) {
                                        int position = Integer.parseInt(v.getTag().toString());
                                        final EditText Caption = (EditText) v;

                                        updateSConfig(sConfig, position, Caption.getText().toString());

                                        InputMethodManager inputManager =
                                                (InputMethodManager) mContext.
                                                        getSystemService(Context.INPUT_METHOD_SERVICE);
                                        inputManager.hideSoftInputFromWindow(
                                                v.getApplicationWindowToken(),
                                                InputMethodManager.HIDE_NOT_ALWAYS);

                                        return true;
                                    }
                                    return false;
                                }
                            }
                    );
                } else {
                    View inflatedLayout = inflater.inflate(R.layout.cell_ins_edit_new_rating, null, false);
                    viewHolder.container.addView(inflatedLayout);

                    inflatedLayout.findViewById(R.id.cell_ins_edit_new_rating_btn_add).setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            AddItem(sConfig);
                        }
                    });
                }

            } else if (sConfig.startsWith(SI_S_CONFIG_KEY_CMT)) {
                View inflatedLayout = inflater.inflate(R.layout.cell_ins_edit_cmt, null, false);
                viewHolder.container.addView(inflatedLayout);


                String commit = sConfig != null && sConfig.length() > 5 ? sConfig.substring(4, sConfig.length() - 1) : "";

                EditText etName = inflatedLayout.findViewById(R.id.cell_ins_edit_cmt_et_name);
                etName.setText(commit);
                etName.setTag(groupPosition + 1);
                etName.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        int position = Integer.parseInt(view.getTag().toString());
                        mListener.editCommit(position, sConfig);
                    }
                });
            } else if (sConfig.startsWith(SI_S_CONFIG_KEY_PTO)) {
                View inflatedLayout = inflater.inflate(R.layout.cell_ins_edit_pto, null, false);
                viewHolder.container.addView(inflatedLayout);
            } else if (sConfig.startsWith(SI_S_CONFIG_KEY_SCAN)) {
                View inflatedLayout = inflater.inflate(R.layout.cell_ins_edit_scan, null, false);
                viewHolder.container.addView(inflatedLayout);
            } else if (sConfig.startsWith("{")) {
                String[] values = getItemsFromsConfig(sConfig);
                if (values != null && childPosition < values.length) {
                    View inflatedLayout = inflater.inflate(R.layout.cell_ins_edit_chk, null, false);
                    viewHolder.container.addView(inflatedLayout);

                    EditText etName = inflatedLayout.findViewById(R.id.cell_ins_edit_chk_et_name);
                    TextView tvSymbol = inflatedLayout.findViewById(R.id.cell_ins_edit_chk_tv_symbol);

                    etName.setText(values[childPosition]);
                    tvSymbol.setText(String.valueOf(values[childPosition].charAt(0)).toUpperCase());
                    ImageView ivBtn = inflatedLayout.findViewById(R.id.cell_ins_edit_chk_btn_minus);
                    ivBtn.setTag(childPosition);
                    ivBtn.setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            int position = Integer.parseInt(view.getTag().toString());

                            updateSConfigbyDelete(sConfig, position);
                        }
                    });

                    etName.setTag(childPosition);

                    etName.setOnEditorActionListener(
                            new EditText.OnEditorActionListener() {
                                @Override
                                public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                                    if (actionId == EditorInfo.IME_ACTION_DONE) {
                                        int position = Integer.parseInt(v.getTag().toString());
                                        final EditText Caption = (EditText) v;

                                        updateSConfig(sConfig, position, Caption.getText().toString());

                                        InputMethodManager inputManager =
                                                (InputMethodManager) mContext.
                                                        getSystemService(Context.INPUT_METHOD_SERVICE);
                                        inputManager.hideSoftInputFromWindow(
                                                v.getApplicationWindowToken(),
                                                InputMethodManager.HIDE_NOT_ALWAYS);

                                        return true;
                                    }
                                    return false;
                                }
                            }
                    );
                } else {
                    View inflatedLayout = inflater.inflate(R.layout.cell_ins_edit_new_rating, null, false);
                    viewHolder.container.addView(inflatedLayout);

                    inflatedLayout.findViewById(R.id.cell_ins_edit_new_rating_btn_add).setOnClickListener(new View.OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            AddItem(sConfig);
                        }
                    });
                }

            } else {
                View inflatedLayout = inflater.inflate(R.layout.cell_ins_edit_nothing, null, false);
                viewHolder.container.addView(inflatedLayout);
                TextView tvControl = inflatedLayout.findViewById(R.id.cell_ins_edit_nothing_tv_control);
                tvControl.setText("Control " + (groupPosition + 1));
                LinearLayout lay = view.findViewById(R.id.cell_ins_edit_nothing_layer);

                lay.setTag(groupPosition + 1);
                lay.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        int position = Integer.parseInt(view.getTag().toString());
                        mListener.addConfig(position);
                    }
                });
            }
        }
        else {
            View inflatedLayout = inflater.inflate(R.layout.cell_ins_edit_nothing, null, false);
            viewHolder.container.addView(inflatedLayout);
            TextView tvControl = inflatedLayout.findViewById(R.id.cell_ins_edit_nothing_tv_control);
            tvControl.setText("Control " + (groupPosition + 1));
            LinearLayout lay = view.findViewById(R.id.cell_ins_edit_nothing_layer);

            lay.setTag(groupPosition + 1);
            lay.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    int position = Integer.parseInt(view.getTag().toString());
                    mListener.addConfig(position);
                }
            });
        }

        return view;
    }

    @Override
    public boolean isChildSelectable(int i, int i1) {
        return false;
    }

    private int getConfigId(String prefix) {
        int idx = 0;

        for (int i = 1; i <= 6; i++) {
            final String sConfig = CommonHelper.GetConfig(i, oInsItem, oPInsItem);
            if (sConfig.startsWith(prefix)) {
                idx = i;
                break;
            }
        }

        return idx;
    }

    private void updateSConfigbyDelete(String sConfig, int position) {

        try {
            if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK) || sConfig.startsWith(SI_S_CONFIG_KEY_SCHK) || sConfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
                String prefix = "";
                if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK)) {
                    prefix = "CHK";
                } else if (sConfig.startsWith(SI_S_CONFIG_KEY_SCHK)) {
                    prefix = "SCHK";
                } else if (sConfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
                    prefix = "MCHK";
                }

                String[] values = getItemsFromsConfig(sConfig);
                String value = "";
                if (values != null && values.length > 0) {
                    for (int i = 0; i < values.length; i++) {
                        if (i == position) {
                            continue;
                        }

                        if (value.equals("")) {
                            value = values[i];
                        } else {
                            value = value + "[|]" + values[i];
                        }
                    }
                }
                String result = prefix + "(" + value + ")";
                Log.e("Osama", "updates" + result);

                mListener.updateConfig(getConfigId(prefix), result);
            }
            else if (sConfig.startsWith("{")) {
                String[] contents = getItemsFromsConfig(sConfig);
                String content = "";
                if (contents != null && contents.length > 0) {
                    for (int i = 0; i < contents.length; i++) {
                        if (i == position) {
                            continue;
                        }
                        if (content.equals("")) {
                            content = contents[i];
                        } else {
                            content = content + "," + contents[i];
                        }
                    }
                }
                CommonJson.RemoveJsonKey("Content", sConfig);
                String result = CommonJson.AddJsonKeyValue(sConfig, "Content", content);
                Log.e("Osama", "updates" + result);
                mListener.updateConfig(getConfigId("{"), result);
            }

        }catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void AddItem(final String sConfig) {

        try {
            final EditText input = new EditText(mContext);
            input.setText("");
            String sTitleText = "Add New Rating";


//            AlertDialog.Builder alert = CommonUI.GetAlertBuilder(sTitleText, "", mContext, true, false);
//                    alert.setView(input);
//            input.setTextColor(Color.DKGRAY);
//            InputMethodManager inputMethodManager = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
//            if (inputMethodManager != null) {
//                inputMethodManager.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0);
//            }
//            alert.setPositiveButton("OK", new DialogInterface.OnClickListener() {
//                public void onClick(DialogInterface dialog, int whichButton) {
//                    String sValue = input.getText().toString();
//                    input.requestFocus();
//                    InputMethodManager inputMethodManager = (InputMethodManager) mContext.getSystemService(Context.INPUT_METHOD_SERVICE);
//                    if (inputMethodManager != null) {
//                        inputMethodManager.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0);
//                    }
//                    if (!sValue.isEmpty()) {
//                        updateSConfigByAdding(sConfig, sValue);
//                    }
//
//                }
//            });
//
//
//            alert.show();

            new MaterialDialog.Builder(mContext)
                    .title(sTitleText)
                    .inputType(InputType.TYPE_CLASS_TEXT)
                    .input("","", new MaterialDialog.InputCallback() {
                        @Override
                        public void onInput(MaterialDialog dialog, CharSequence input) {
                            String sValue = input.toString();

                            if (!sValue.isEmpty()) {
                                updateSConfigByAdding(sConfig, sValue);
                            }

                        }
                    }).show();
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_ins_full.ShowAddAlert", ex, mContext);
        }

    }

    private void updateSConfigByAdding(String sConfig, String updates) {

        try {
            if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK)
                    || sConfig.startsWith(SI_S_CONFIG_KEY_SCHK) || sConfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
                String prefix = "";
                if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK)) {
                    prefix = "CHK";
                } else if (sConfig.startsWith(SI_S_CONFIG_KEY_SCHK)) {
                    prefix = "SCHK";
                } else if (sConfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
                    prefix = "MCHK";
                }

                String[] values = getItemsFromsConfig(sConfig);
                String value = "";
                if (values != null && values.length > 0) {
                    for (int i = 0; i < values.length; i++) {
                        if (i == 0) {
                            value = values[i];
                        } else {
                            value = value + "[|]" + values[i];
                        }
                    }
                }

                if (value.equals("")) {
                    value = updates;
                } else {
                    value = value  + "[|]" + updates;
                }

                String result = prefix + "(" + value + ")";
                Log.e("Osama", "updates" + result);

                mListener.updateConfig(getConfigId(prefix), result);
            }
            else if (sConfig.startsWith("{")) {
                String[] contents = getItemsFromsConfig(sConfig);
                String content = "";
                if (contents != null && contents.length > 0) {
                    for (int i = 0; i < contents.length; i++) {
                        if (i == 0) {
                            content = contents[i];
                        } else {
                            content = content + "," + contents[i];
                        }
                    }
                }
                if (content.equals("")) {
                    content = updates;
                } else {
                    content = content  + "," + updates;
                }

                CommonJson.RemoveJsonKey("Content", sConfig);
                String result = CommonJson.AddJsonKeyValue(sConfig, "Content", content);
                Log.e("Osama", "updates" + result);
                mListener.updateConfig(getConfigId("{"), result);
            }

        }catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void updateSConfig(String sConfig, int position, String updates) {
        Log.e("Osama", "ddd "  + sConfig + " - " + position + " - " + updates);

        try {
            if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK)
                    || sConfig.startsWith(SI_S_CONFIG_KEY_SCHK) || sConfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
                String prefix = "";
                if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK)) {
                    prefix = "CHK";
                } else if (sConfig.startsWith(SI_S_CONFIG_KEY_SCHK)) {
                    prefix = "SCHK";
                } else if (sConfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
                    prefix = "MCHK";
                }

                String[] values = getItemsFromsConfig(sConfig);
                String value = "";
                if (values != null && values.length > 0) {
                    for (int i = 0; i < values.length; i++) {
                        String str = i == position ? updates : values[i];
                        if (i == 0) {
                            value = str;
                        } else {
                            value = value + "[|]" + str;
                        }
                    }
                }
                String result = prefix + "(" + value + ")";
                Log.e("Osama", "updates" + result);

                mListener.updateConfig(getConfigId(prefix), result);
            }
            else if (sConfig.startsWith("{")) {
                String[] contents = getItemsFromsConfig(sConfig);
                String content = "";
                if (contents != null && contents.length > 0) {
                    for (int i = 0; i < contents.length; i++) {
                        String str = i == position ? updates : contents[i];
                        if (i == 0) {
                            content = str;
                        } else {
                            content = content + "," + str;
                        }
                    }
                }
                CommonJson.RemoveJsonKey("Content", sConfig);
                String result = CommonJson.AddJsonKeyValue(sConfig, "Content", content);
                Log.e("Osama", "updates" + result);
                mListener.updateConfig(getConfigId("{"), result);
            }

        }catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private String getTitleFromsConfig(String sConfig) {
        try {
            if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK)) {
                return "Select Rating - Yes/No Check";
            } else if (sConfig.startsWith(SI_S_CONFIG_KEY_SCHK)) {
                return "Select Rating - Single Check";
            } else if (sConfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
                return "Select Rating - Multi Check";
            } else if (sConfig.startsWith(SI_S_CONFIG_KEY_CMT)) {
                return "CommentsBox";
            } else if (sConfig.startsWith(SI_S_CONFIG_KEY_PTO)) {
                return "Photos";
            } else if (sConfig.startsWith(SI_S_CONFIG_KEY_SCAN)) {
                return "Scanner";
            } else if (sConfig.startsWith("{")) {
                String sType = CommonJson.GetJsonKeyValue("sType", sConfig);
                if (sType != null && sType.equalsIgnoreCase(SI_S_CONFIG_KEY_SEL)) {
                    return "SEL";
                } else if (sType.equalsIgnoreCase(SI_S_CONFIG_KEY_MSEL)) {
                    return "MSEL";
                } else if (sType.equalsIgnoreCase(SI_S_CONFIG_KEY_NUM)) {
                    return "NUM";
                }
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }

         return "";
    }

    private String[] getItemsFromsConfig(String sConfig) {
        try {
            if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK) && sConfig.length() > 5) {
                return sConfig.substring(4, sConfig.length() - 1).split("\\[\\|\\]");
            } else if (sConfig.startsWith(SI_S_CONFIG_KEY_SCHK) && sConfig.length() > 6) {
                return sConfig.substring(5, sConfig.length() - 1).split("\\[\\|\\]");
            } else if (sConfig.startsWith(SI_S_CONFIG_KEY_MCHK) && sConfig.length() > 6) {
                return sConfig.substring(5, sConfig.length() - 1).split("\\[\\|\\]");
            }
             else if (sConfig.startsWith("{")) {
                String content = CommonJson.GetJsonKeyValue("Content", sConfig);

                if (content != null && !content.isEmpty()) {
                    return content.split(",");
                }
            }

        }catch (Exception ex) {
            ex.printStackTrace();
        }

        return null;
    }

    private class HeaderViewHolder {
        TextView tvTitle;
        LinearLayout btnMenu;
        LinearLayout hcontainer;
    }

    private class ViewHolder {
        FrameLayout container;
    }
}
