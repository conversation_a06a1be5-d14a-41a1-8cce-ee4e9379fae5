package com.snapinspect.snapinspect3.Adapter;

import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.util.Pair;
import android.util.TypedValue;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.TextView;
import androidx.core.content.ContextCompat;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.IF_Object.O_ContactType;
import com.snapinspect.snapinspect3.IF_Object.O_MapItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Contact;
import com.snapinspect.snapinspect3.IF_Object.ai_FloorPlan;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.TextViewUtils;
import com.snapinspect.snapinspect3.views.FloorPlanView;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.O_MapItem.CustomInfoType.checkBox;
import static com.snapinspect.snapinspect3.IF_Object.O_MapItem.CustomInfoType.pto;

/**
 * @Created by crane on 9/20/17.
 */

public class AssetDetailsAdapter extends BaseAdapter {

    private static final String COORDINATE_PATTERN = "[-+]?([1-8]?\\d(\\.\\d+)?|90(\\.0+)?)," +
            "\\s*[-+]?(180(\\.0+)?|((1[0-7]\\d)|([1-9]?\\d))(\\.\\d+)?)";
    private final Context mContext;
    private List<O_MapItem<?>> lsAssetInfos = new ArrayList<>();
    private final Listener mListener;

    public AssetDetailsAdapter(Context context, Listener listener) {
        mContext = context;
        mListener = listener;
    }

    public interface Listener {
        void onClickOnAddressHeaderView();
        void onEditContact(ai_Contact contact);
        void onClickOnFloorPlan(ai_FloorPlan floorPlan);
    }

    public void setAssetInfos(List<O_MapItem<?>> list) {
        lsAssetInfos = list;

        this.notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return lsAssetInfos.size();
    }

    @Override
    public Object getItem(int i) {
        return lsAssetInfos.get(i);
    }

    @Override
    public long getItemId(int i) {
        return i;
    }

    @Override
    public View getView(int i, View convertView, ViewGroup viewGroup) {
        int type = lsAssetInfos.get(i).sType;
        View view;
        if (type == O_MapItem.kAddress) {
            O_MapItem<String> item = (O_MapItem<String>) lsAssetInfos.get(i);
            view = LayoutInflater.from(mContext).inflate(R.layout.cell_if_address, null);
            TextView tvAddress = view.findViewById(R.id.tv_address);
            TextViewUtils.updateText(tvAddress, item.value);

        } else if (type == O_MapItem.kAddressInfo) {
            O_MapItem<String> item = (O_MapItem<String>) lsAssetInfos.get(i);
            view = LayoutInflater.from(mContext).inflate(R.layout.cell_if_address_header, null);
            TextView tvAddressName = view.findViewById(R.id.tv_address_name);
            TextViewUtils.updateText(mContext, tvAddressName, R.string.address_name);

            TextView tvAddress = view.findViewById(R.id.tv_address);
            TextViewUtils.updateText(tvAddress, item.value);

            TextView tvInfoName = view.findViewById(R.id.tv_info_name);
            TextViewUtils.updateText(tvInfoName, item.sInfoName);

            Button btnAction = view.findViewById(R.id.btn_action);
            btnAction.setText(item.sActionName);
            btnAction.setOnClickListener(v -> mListener.onClickOnAddressHeaderView());
            btnAction.setVisibility(CommonJson.disableEditAsset(mContext) ? View.GONE : View.VISIBLE);
        } else if (type == O_MapItem.kContact) {
            O_MapItem<ai_Contact> item = (O_MapItem<ai_Contact>) lsAssetInfos.get(i);
            final ai_Contact contact = item.value;
            view = LayoutInflater.from(mContext).inflate(R.layout.cell_asset_contact_info, null);
            TextView tvShortName = view.findViewById(R.id.contact_short_name);
            TextView tvFullName = view.findViewById(R.id.contact_full_name);
            TextView tvEmail = view.findViewById(R.id.contact_email);
            TextView tvPhoneNumber1 = view.findViewById(R.id.contact_phone_number_1);
            TextView tvPhoneNumber2 = view.findViewById(R.id.contact_phone_number_2);
            TextView tvType = view.findViewById(R.id.contact_type);

            tvFullName.setText(String.format("%s %s", contact.sFirstName, contact.sLastName));
            try {
                O_ContactType contactType = contact.getContactType(mContext);
                String sColorCode = O_ContactType.CONTACT_COLOR_DEFAULT;
                if (contactType != null && !StringUtils.isEmpty(contactType.sColorCode)) {
                    sColorCode = contactType.sColorCode;
                }

                View shortNameContainerView = view.findViewById(R.id.contact_short_name_container);
                updateViewShapeBackgroundColor(shortNameContainerView, R.drawable.bg_green_circle, sColorCode);

                View typeContainerView = view.findViewById(R.id.contact_type_container);
                updateViewShapeBackgroundColor(typeContainerView, R.drawable.bg_green_round, sColorCode);

                tvShortName.setText(CommonHelper.GetStatusCode(contact.sFirstName == null ? ""
                        : (contact.sFirstName + " ") + (contact.sLastName == null ? "" : contact.sLastName)));
                if (contact.sEmail != null && !contact.sEmail.trim().isEmpty()) {
                    tvEmail.setVisibility(View.VISIBLE);
                    tvEmail.setText(contact.sEmail);
                    tvEmail.setOnClickListener(v -> {
                        String mailto = "mailto:" + contact.sEmail;
                        Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
                        emailIntent.setData(Uri.parse(mailto));
                        try {
                            mContext.startActivity(emailIntent);
                        } catch (ActivityNotFoundException e) {
                            //TODO: Handle case where no email app is available
                        }
                    });
                } else {
                    tvEmail.setVisibility(View.GONE);
                }

                if (contact.sPhone != null && !contact.sPhone.trim().isEmpty()) {
                    tvPhoneNumber1.setVisibility(View.VISIBLE);
                    tvPhoneNumber1.setText(contact.sPhone);
                    tvPhoneNumber1.setOnClickListener(v -> {
                        //TODO - go to message dialog
                        Intent callIntent = new Intent(Intent.ACTION_DIAL);
                        callIntent.setData(Uri.parse("tel:" + contact.sPhone));
                        mContext.startActivity(callIntent);
                    });
                } else {
                    tvPhoneNumber1.setVisibility(View.GONE);
                }

                if (contact.sMobile != null && !contact.sMobile.trim().isEmpty()) {
                    tvPhoneNumber2.setVisibility(View.VISIBLE);
                    tvPhoneNumber2.setText(contact.sMobile);
                    tvPhoneNumber2.setOnClickListener(v -> {
                        //TODO - go to message dialog
                        Intent callIntent = new Intent(Intent.ACTION_DIAL);
                        callIntent.setData(Uri.parse("tel:" + contact.sMobile));
                        mContext.startActivity(callIntent);
                    });
                } else {
                    tvPhoneNumber2.setVisibility(View.GONE);
                }

                tvType.setText(contact.sTag);

            } catch (Exception ex) {
                ex.printStackTrace();
            }

            View btnForward = view.findViewById(R.id.contact_btn_forward);
            btnForward.setVisibility(CommonJson.disableEditAsset(mContext) ? View.GONE : View.VISIBLE);
        } else if (type == O_MapItem.kFloorPlan) {
            O_MapItem<Pair<FloorPlanView.FloorPlanViewModel, FloorPlanView.FloorPlanViewModel>> item
                    = (O_MapItem<Pair<FloorPlanView.FloorPlanViewModel, FloorPlanView.FloorPlanViewModel>>) lsAssetInfos.get(i);
            view = LayoutInflater.from(mContext).inflate(R.layout.cell_floor_plan, null);
            FloorPlanView firstFloorPlanView = view.findViewById(R.id.floor_plan_view_first);
            FloorPlanView lastFloorPlanView = view.findViewById(R.id.floor_plan_view_last);
            firstFloorPlanView.setFloorPlan(item.value.first);
            lastFloorPlanView.setFloorPlan(item.value.second);
            firstFloorPlanView.setOnFloorPlanClickListener(mListener::onClickOnFloorPlan);
            lastFloorPlanView.setOnFloorPlanClickListener(mListener::onClickOnFloorPlan);

        } else if (type == O_MapItem.kMapData) {
            O_MapItem<String> item = (O_MapItem<String>) lsAssetInfos.get(i);
            if (item.customInfoType == pto) {
                view = LayoutInflater.from(mContext).inflate(R.layout.cell_if_custom_pto, null);
                TextView tvTitle = view.findViewById(R.id.tv_title);
                TextViewUtils.updateText(tvTitle, item.sKey + ": ");
                Button btnFile = view.findViewById(R.id.btn_view_file);

                btnFile.setVisibility(item.hasPTO ? View.VISIBLE : View.GONE);
                btnFile.setEllipsize(TextUtils.TruncateAt.MIDDLE);
                if (btnFile.getVisibility() == View.VISIBLE) {
                    String sName = null;
                    int fileId = 0;
                    JSONObject object = null;
                    try {
                        object = new JSONObject(item.value);
                        fileId = object.getInt("iFileID");
                        sName = object.getString("sName");
                    } catch (Exception ignored) { }

                    fileId = fileId > 0 ? fileId : CommonHelper.getInt(item.value);
                    String viewFile = mContext.getResources().getString(R.string.button_view_file);
                    btnFile.setText(!StringUtils.isEmpty(sName) ? sName : viewFile);

                    final int finalFileId = fileId;
                    btnFile.setOnClickListener(v -> CommonUI.viewRemoteFile(mContext, finalFileId));
                }
            } else if (item.customInfoType == checkBox) {
                view = LayoutInflater.from(mContext).inflate(R.layout.cell_if_custom_checkbox, null);
                TextView tvTitle = view.findViewById(R.id.tv_title);
                TextViewUtils.updateText(tvTitle, item.sKey + ": ");
                CheckBox checkBox = view.findViewById(R.id.cb_item_checked);
                checkBox.setChecked(CommonHelper.getBoolean(item.value));
            } else {
                view = LayoutInflater.from(mContext).inflate(R.layout.cell_if_textview, null);
                TextView tvTitle = view.findViewById(R.id.cell_tv_title);
                String html = "";
                if (!StringUtils.isEmpty(item.sKey)) {
                    html += "<b>" + item.sKey + ": </b>";
                }
                if (!StringUtils.isEmpty(item.value)) {
                    html += item.value.replaceAll(
                            COORDINATE_PATTERN,
                         "<font color=\"#3E6BFF\">" +
                                 "<a style=\"text-decoration:none !important\" href=\"http://maps.google.com/maps?daddr=$0\">$0</a>" +
                                 "</font>"
                    );
                }
                tvTitle.setText(StringUtils.convertHtml(html));
                tvTitle.setMovementMethod(LinkMovementMethod.getInstance());
            }
        } else {
            O_MapItem<String> item = (O_MapItem<String>) lsAssetInfos.get(i);
            view = LayoutInflater.from(mContext).inflate(R.layout.cell_if_textview, null);
            TextView tvTitle = view.findViewById(R.id.cell_tv_title);
            tvTitle.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            tvTitle.setText(item.value);
        }

        return view;
    }

    private void updateViewShapeBackgroundColor(View view, int drawableResourceId, String colorCode) {
        GradientDrawable drawable = (GradientDrawable) ContextCompat.getDrawable(mContext, drawableResourceId);
        if (drawable != null && view != null) {
            int color = Color.parseColor(colorCode);
            drawable.setColor(color);
            view.setBackground(drawable);
        }
    }
}
