package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import com.snapinspect.snapinspect3.IF_Object.v_Schedule;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.views.StickyHeaderListView.StickyHeaderAdapter;

import java.util.Date;
import java.util.List;

public class SchedulesAdapter extends StickyHeaderAdapter {

    public enum Type {
        Today, Weekly, Monthly, Upcoming, Overdue
    }

    private final Context mContext;
    public List<ScheduleSectionModel> mSectionModels;
    private final Type mType;

    public SchedulesAdapter(Context context, List<ScheduleSectionModel> schedules, Type type) {
        this.mContext = context;
        this.mSectionModels = schedules;
        this.mType = type;
    }

    @Override
    public int sectionCounts() {
        return mSectionModels.size();
    }

    @Override
    public int rowCounts(int section) {
        if (section < 0 || mSectionModels.isEmpty()) return 0;
        return mSectionModels.get(section).schedules.size();
    }

    @Override
    public View getRowView(int section, int row, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.cell_frag_schedules, null);
        }

        View cell = convertView;
        v_Schedule item = (v_Schedule) getRowItem(section, row);

        TextView tvDate = cell.findViewById(R.id.txt_InsDate);
        updateTextViewWithText(tvDate, DateUtils.formatAsCellDate(mContext, new Date(item.iUnixTime)));
        TextView tvTitle = cell.findViewById(R.id.txt_sInsTitle);
        updateTextViewWithText(tvTitle, item.sInsTitle);
        int color = mType == Type.Overdue ? R.color.schedules_tab_overdue : R.color.dark_gray;
        tvDate.setTextColor(mContext.getResources().getColor(color));

        TextView tvAddress = cell.findViewById(R.id.txt_sAddress);
        updateTextViewWithText(tvAddress, item.GetAddress());

        TextView tvRef = cell.findViewById(R.id.txt_sRef);
        updateTextViewWithText(tvRef, item.sRef);

        int iconResId = 0;
        if (!item.bRecurred) {
            if (item.isInsInProcess()) {
                iconResId = R.drawable.ic_forward;
            } else if (item.isInsCompleted()) {
                iconResId = R.drawable.check;
            }
        }

        View accessoryLayout = cell.findViewById(R.id.layout_cell_accessory);
        if (iconResId != 0) {
            ImageView accessoryIcon = cell.findViewById(R.id.cell_accessory_icon);
            accessoryIcon.setImageResource(iconResId);
            accessoryLayout.setVisibility(View.VISIBLE);
        } else {
            accessoryLayout.setVisibility(View.GONE);
        }

        return cell;
    }

    @Override
    public View getSectionHeaderView(int section, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = LayoutInflater.from(mContext).inflate(R.layout.header_frag_schedules, null);
        }
        View headerView = convertView;

        if (section > -1 && section < mSectionModels.size()) {
            ScheduleSectionModel sectionModel = mSectionModels.get(section);
            TextView tvTitle = headerView.findViewById(R.id.header_schedules_tv_title);
            tvTitle.setText(sectionModel.title);
            TextView tvSubtitle = headerView.findViewById(R.id.header_schedules_tv_subtitle);
            tvSubtitle.setText(sectionModel.subtitle);
        }

        return headerView;
    }

    @Override
    public boolean hasSectionHeaderView(int section) {
        boolean mIsToday = mType == Type.Today;
        return !mIsToday;
    }

    @Override
    public Object getRowItem(int section, int row) {
        ScheduleSectionModel model = mSectionModels.get(section);
        return model.schedules.get(row);
    }

    private void updateTextViewWithText(TextView textView, String text) {
        boolean hasContent = !StringUtils.isEmpty(text) && text.trim().length() > 0;
        textView.setVisibility(hasContent ? View.VISIBLE : View.GONE);
        textView.setText(hasContent ? text.trim() : "");
    }
}
