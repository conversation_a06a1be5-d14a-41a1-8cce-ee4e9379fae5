package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageButton;
import android.widget.TextView;

import com.snapinspect.snapinspect3.IF_Object.ai_Layout;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.SimpleTextWatcher;

import java.util.List;

public final class InsLayoutAdapter extends BaseListAdapter<ai_Layout> {

    public InsLayoutAdapter(List<ai_Layout> mList, Context context) {
        super(mList, context);
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        View cell = mInflater.inflate(R.layout.cell_ins_layout_3rd, null);
        ai_Layout layout = getItem(position);

        TextView titleTxt = cell.findViewById(R.id.tv_room_name);
        titleTxt.setText(layout.sName);

        int numOfRoom = 0;
        try {
            numOfRoom = Integer.parseInt(layout.sFieldOne);
        } catch (Exception e) { }

        TextView numTxt = cell.findViewById(R.id.tx_room_num);
        numTxt.setText(String.format("%d", numOfRoom));

        ImageButton btnIncrease = cell.findViewById(R.id.btn_room_increase);
        ImageButton btnDecrease = cell.findViewById(R.id.btn_room_decrease);
        btnIncrease.setOnClickListener(v -> {
            int num = Integer.parseInt(numTxt.getText().toString());
            numTxt.setText(String.format("%d", ++num));
        });

        btnDecrease.setOnClickListener(v -> {
            int num = Integer.parseInt(numTxt.getText().toString());
            numTxt.setText(String.format("%d", Math.max(--num, 0)));
        });

        numTxt.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                layout.sFieldOne = s.toString();
            }
        });

        return cell;
    }
}
