package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * @Created by <PERSON><PERSON><PERSON> on 20/03/14.
 */
public class InsRoom_Adapter extends BaseAdapter {
    ArrayList<ai_InsItem> lsInsItem;
    Context oContext;
    HashMap<ai_InsItem, Integer> mIdMap = new HashMap<ai_InsItem, Integer>();
   // private boolean bEditMode = false;
    final int INVALID_ID = -1;
    public InsRoom_Adapter(ArrayList<ai_InsItem> _lsInsItem, Context _oContext) {
        try {
            oContext = _oContext;
            lsInsItem = _lsInsItem;
            for (int i = 0; i < _lsInsItem.size(); ++i) {
                mIdMap.put(_lsInsItem.get(i), i);
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "InsRoom_Adapter.main", ex, oContext);
        }
    }
   // public void setEdit(boolean _bEditMode){
   //     bEditMode = _bEditMode;
  //  }
    @Override
    public int getCount() {
        // TODO Auto-generated method stub
        return mIdMap.size();
    }
    @Override
    public Object getItem(int arg0) {
        return lsInsItem.get(arg0);
    }
    @Override
    public long getItemId(int position) {
        // TODO Auto-generated method stub
        if (position < 0 || position >= mIdMap.size()) {
            return INVALID_ID;
        }
        ai_InsItem item = (ai_InsItem)getItem(position);
        // Log.v("position", position + "   " + mIdMap.get(item));
        return mIdMap.get(item);
    }
    @Override
    public boolean hasStableIds() {
        return true;
    }
    @Override
    public boolean isEnabled(int position)
    {
        return true;
    }
    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        try {
            /*if (bEditMode) {
                LayoutInflater inflater = (LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
                View row;
                ai_InsItem oInsItem = lsInsItem.get(position);
                row = inflater.inflate(com.snapinspect.snapinspect3.R.layout.cell_insroom_edit, parent, false);
                EditText oRoomName = (EditText) row.findViewById(com.snapinspect.snapinspect3.R.id.txt_RoomName);
               // LinearLayout.LayoutParams oLayoutParams = new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
                //oLayoutParams.setMargins(20, 40, 20, 40);
                //oLayoutParams.leftMargin
               // oLayoutParams.setMargins(oLayoutParams.leftMargin, 30, oLayoutParams.rightMargin, 30);

               // oRoomName.setLayoutParams(oLayoutParams);
                oRoomName.setText(oInsItem.sName);
                oRoomName.setTag(position);
                oRoomName.setOnFocusChangeListener(new View.OnFocusChangeListener() {
                    public void onFocusChange(View v, boolean hasFocus) {
                        if (!hasFocus) {
                            // final int position = v.getId();
                            final EditText Caption = (EditText) v;
                            int position = Integer.parseInt(v.getTag().toString());
                            lsInsItem.get(position).sName = Caption.getText().toString();
                            lsInsItem.get(position).save();
                        }
                    }
                });
                oRoomName.setOnEditorActionListener(
                        new EditText.OnEditorActionListener() {
                            @Override
                            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                                if (actionId == EditorInfo.IME_ACTION_DONE) {
                                    int position = Integer.parseInt(v.getTag().toString());
                                    lsInsItem.get(position).sName = v.getText().toString();
                                    lsInsItem.get(position).save();
                                    InputMethodManager inputManager =
                                            (InputMethodManager) oContext.
                                                    getSystemService(Context.INPUT_METHOD_SERVICE);
                                    inputManager.hideSoftInputFromWindow(
                                            ((EditText) v).getApplicationWindowToken(),
                                            InputMethodManager.HIDE_NOT_ALWAYS);
                                    return true;
                                }
                                return false;
                            }
                        }
                );
                ImageButton btn_Delete = (ImageButton) row.findViewById(com.snapinspect.snapinspect3.R.id.btn_DeleteRoom);
                btn_Delete.setTag(position);
                final InsRoom_Adapter oAdapter = this;
                btn_Delete.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        final int position = Integer.parseInt(view.getTag().toString());

                        AlertDialog.Builder builder = new AlertDialog.Builder(oContext);
                        builder.setTitle("Message");
                        builder.setMessage("Are you sure to Delete this room?");
                        builder.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                ai_InsItem oInsItem = lsInsItem.get(position);
                                if (CommonHelper.bIns1DefaultAdd(oInsItem.sConfig)){
                                    Toast.makeText(oContext, "Error! Compulsory section can not be deleted!", Toast.LENGTH_LONG).show();
                                    return;
                                }
                                oInsItem.bDeleted = true;
                                oInsItem.save();
                                lsInsItem.remove(position);
                                //Remember to delete all photos and videos, and delete all children ins item, shared with 2nd level InsItem Deletation

                                ((if_inspection) oContext).EditInsItem();

                            }
                        });
                        builder.setNegativeButton("No", new DialogInterface.OnClickListener() {
                            public void onClick(DialogInterface dialog, int which) {
                                dialog.dismiss();
                            }
                        }).show();





                    }
                });
                return (row);
            } else {*/
                LayoutInflater inflater = (LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
                View row;
                ai_InsItem oInsItem = lsInsItem.get(position);
                row = inflater.inflate(com.snapinspect.snapinspect3.R.layout.cell_insroom_textview, parent, false);
                TextView oRoomName = row.findViewById(com.snapinspect.snapinspect3.R.id.tv_InsRoom_Name);
                oRoomName.setText(oInsItem.sName);
                ImageView oImageView = row.findViewById(com.snapinspect.snapinspect3.R.id.img_insroom_status);
                if (oInsItem.bCompleted) {
                    oImageView.setBackgroundResource(com.snapinspect.snapinspect3.R.drawable.checkmark_custom);
                }
                return (row);
           // }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "InsRoom_Adapter.getView", ex, oContext);
            LayoutInflater inflater = (LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            View row;
            row = inflater.inflate(com.snapinspect.snapinspect3.R.layout.cell_insroom_edit, parent, false);
            return row;
        }
    }


}
