package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;

import java.util.List;

public class BaseListAdapter<T> extends BaseAdapter {

    private static final int ZERO = 0;

    private List<T> mList;
    protected Context mContext;
    protected LayoutInflater mInflater;

    public BaseListAdapter(List<T> mList, Context context) {
        this.mList = mList;
        this.mContext = context;
        this.mInflater = LayoutInflater.from(context);
    }

    public List<T> getData() {
        return mList;
    }

    public void updateData(List<T> mList) {
        this.mList = mList;
        notifyDataSetChanged();
    }

    public boolean isNotEmpty() {
        return mList != null && !mList.isEmpty();
    }

    @Override
    public int getCount() {
        return mList != null ? mList.size() : ZERO;
    }

    @Override
    public T getItem(int position) {
        return mList.get(position);
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        return convertView;
    }
}