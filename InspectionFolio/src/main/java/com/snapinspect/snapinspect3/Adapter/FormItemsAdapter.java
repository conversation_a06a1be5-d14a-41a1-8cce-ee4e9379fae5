package com.snapinspect.snapinspect3.Adapter;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.EditText;
import android.widget.TextView;

import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.if_FormItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.TextViewUtils;
import com.snapinspect.snapinspect3.util.ThrottledSearch;

import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.ViewType.*;
import static com.snapinspect.snapinspect3.util.DateUtils.DATE_RANGE_FORMAT;

public final class FormItemsAdapter extends BaseAdapter {
    private static final String DATE_TEXT_FORMAT = "MMM dd, yyyy";

    public interface Delegate {
        void itemSelect(if_FormItem item, int position);
        void attachmentSelect(if_FormItem item, int position);
        void dateRangeSelect(if_FormItem item, Constants.DateRange dateRange, String value, int position);
    }

    private static final class ViewHolder {
        Button button;
        CheckBox btnChecked;
        TextView txtTitle, txtRequiredTitle, txtValue;
        View lBottomNoValue, lBottomHasValue;

        EditText editItemValue;
        ThrottledSearch throttledSearch;
        TextView dtFromText, dtToText;
    }

    private final Context mContext;
    private Delegate mDelegate = null;
    private List<if_FormItem> mFormItems;
    private HashMap<if_FormItem, Integer> mInsMap;

    public FormItemsAdapter(Context ctx, List<if_FormItem> formItems, Delegate delegate) {
        mContext = ctx;
        mDelegate = delegate;
        setFormItems(formItems);
    }

    public FormItemsAdapter(Context ctx, List<if_FormItem> formItems) {
        mContext = ctx;
        setFormItems(formItems);
    }

    public List<if_FormItem> getFormItems() {
        return mFormItems;
    }

    public void setFormItems(List<if_FormItem> items) {
        mFormItems = items;
        HashMap<if_FormItem, Integer> map = new HashMap<>();
        for (int i = 0; i < items.size(); i++) {
            map.put(items.get(i), i);
        }
        mInsMap = map;
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return mFormItems.size();
    }

    @Override
    public Object getItem(int position) {
        return mFormItems.get(position);
    }

    @Override
    public long getItemId(int position) {
        if (mInsMap == null || position < 0 || position >= mInsMap.size()) return -1;
        return mInsMap.get(getItem(position));
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if_FormItem item = (if_FormItem) getItem(position);
        int layoutRes = item.getLayoutRes();
        if (layoutRes == 0) return new View(mContext);

        ViewHolder holder = new ViewHolder();
        View view= LayoutInflater.from(mContext).inflate(layoutRes, parent, false);
        holder.txtTitle = view.findViewById(R.id.txt_form_item_title);
        if (item.viewType == textInput || item.viewType == textArea) {
            holder.txtRequiredTitle = view.findViewById(R.id.txt_form_item_required);
            holder.lBottomHasValue = view.findViewById(R.id.line_bottom_has_value);
            holder.lBottomNoValue = view.findViewById(R.id.line_bottom_no_value);
            holder.editItemValue = view.findViewById(R.id.edit_item_value);
            holder.throttledSearch = new ThrottledSearch((Activity) mContext, value -> {
                if (!StringUtils.isEmpty(value) && !value.equalsIgnoreCase(item.value))
                    item.validateResult = if_FormItem.ValidateResult.none;
                item.value = value;
                updateBottomLine(holder, item);
            });
            holder.throttledSearch.bindTo(holder.editItemValue);
        } else if (item.viewType == checkBox) {
            holder.btnChecked = view.findViewById(R.id.cb_item_checked);
            holder.btnChecked.setOnCheckedChangeListener(
                    (buttonView, isChecked) -> item.value = isChecked ? "1" : "0"
            );
        } else if (item.viewType == textSelect) {
            holder.txtRequiredTitle = view.findViewById(R.id.txt_form_item_required);
            holder.lBottomHasValue = view.findViewById(R.id.line_bottom_has_value);
            holder.lBottomNoValue = view.findViewById(R.id.line_bottom_no_value);
            holder.button = view.findViewById(R.id.btn_item_select);
            holder.button.setOnClickListener(v -> {
                if (mDelegate != null) mDelegate.itemSelect(item, position);
            });
        } else if (item.viewType == attachment) {
            holder.txtRequiredTitle = view.findViewById(R.id.txt_form_item_required);
            holder.txtValue = view.findViewById(R.id.txt_form_item_value);
            holder.button = view.findViewById(R.id.btn_attach_file);
            holder.button.setOnClickListener(v -> {
                if (mDelegate != null) mDelegate.attachmentSelect(item, position);
            });
        } else if (item.viewType == dateRange) {
            holder.dtFromText = view.findViewById(R.id.dtFrom_text);
            holder.dtFromText.setOnClickListener(v -> {
                if (mDelegate != null) mDelegate.dateRangeSelect(item, Constants.DateRange.FROM, item.value, position);
            });

            holder.dtToText = view.findViewById(R.id.dtTo_text);
            holder.dtToText.setOnClickListener(v -> {
                if (mDelegate != null) mDelegate.dateRangeSelect(item, Constants.DateRange.TO, item.value2, position);
            });
        }

        TextViewUtils.updateText(holder.txtTitle, item.title);
        TextViewUtils.updateText(holder.txtRequiredTitle, item.requiredTitle);
        TextViewUtils.updateText(holder.txtValue, item.sFileName);
        if (holder.editItemValue != null) {
            holder.editItemValue.setInputType(item.inputType);
            TextViewUtils.updateEditText(holder.editItemValue, item.value);
        }

        if (holder.dtFromText != null) {
            Date dtFrom = DateUtils.parse(item.value, DATE_RANGE_FORMAT);
            if (dtFrom == null) dtFrom = new Date();
            TextViewUtils.updateText(holder.dtFromText, DateUtils.format(dtFrom, DATE_TEXT_FORMAT));
        }

        if (holder.dtToText != null) {
            Date dtTo = DateUtils.parse(item.value2, DATE_RANGE_FORMAT);
            if (dtTo == null) dtTo = new Date();
            TextViewUtils.updateText(holder.dtToText, DateUtils.format(dtTo, DATE_TEXT_FORMAT));
        }

        updateBottomLine(holder, item);

        if (holder.button != null)
            holder.button.setText(item.value);

        if (holder.btnChecked != null) {
            holder.btnChecked.setChecked(CommonHelper.getBoolean(item.value));
        }
        return view;
    }

    private void updateBottomLine(ViewHolder viewHolder, if_FormItem item) {
        if (viewHolder.lBottomHasValue != null && viewHolder.lBottomNoValue != null) {
            Resources resources = mContext.getResources();
            boolean validateFailed = item.validateResult == if_FormItem.ValidateResult.failure;
            if (validateFailed) {
                viewHolder.lBottomHasValue.setBackgroundColor(resources.getColor(R.color.item_invalid_line_color));
            } else {
                viewHolder.lBottomHasValue.setBackgroundColor(resources.getColor(R.color.item_edited_line_color));
            }
            boolean hasValue = (!StringUtils.isEmpty(item.value) || validateFailed) && item.selectable;
            viewHolder.lBottomHasValue.setVisibility(hasValue ? View.VISIBLE : View.GONE);
            viewHolder.lBottomNoValue.setVisibility(!hasValue ? View.VISIBLE : View.GONE);
        }
    }
}
