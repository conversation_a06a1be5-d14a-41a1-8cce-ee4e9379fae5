package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;
import android.graphics.BitmapFactory;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.ListAdapter;
import android.widget.TextView;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.ViewUtils;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activity.if_VideoClips;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.TextViewUtils;

import java.util.ArrayList;
import java.util.List;

import static com.snapinspect.snapinspect3.app.App.getContext;

public class VideoClipsAdapter extends BaseListAdapter<ai_Video> {

    private int columnWidth = 0, columnHeight = 0;
    private ViewHolder viewHolder;

    public VideoClipsAdapter(Context context, List<ai_Video> mList, int width, int height) {
        super(mList, context);
        columnWidth = width;
        columnHeight = height;
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null) {
            convertView = mInflater.inflate(R.layout.cell_video_clip, null);
            viewHolder = new ViewHolder(convertView);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }

        ViewUtils.setViewSize(viewHolder.thumbView, columnWidth, columnHeight);

        ImageView playIcon = convertView.findViewById(R.id.iv_Play);
        ViewUtils.setViewSize(playIcon, columnWidth / 3, columnWidth / 3);

        ai_Video oVideo = getItem(position);
        long duration = CommonHelper.getVideoDuration(mContext, oVideo.getFile());
        TextViewUtils.updateText(viewHolder.durationTextView, DateUtils.clockStringFromMilliseconds(duration));

        viewHolder.thumbView.setImageBitmap(BitmapFactory.decodeFile(oVideo.getThumb()));
        return convertView;
    }

    private static class ViewHolder {
        ImageView thumbView;
        TextView durationTextView;

        ViewHolder(View rootView) {
            thumbView = rootView.findViewById(R.id.img_thumb_view);
            durationTextView = rootView.findViewById(R.id.tv_duration);
        }
    }
}
