package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.TextView;

import androidx.core.content.res.ResourcesCompat;
import com.snapinspect.snapinspect3.IF_Object.v_RequestInspection;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.TextViewUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.v_RequestInspection.State.COMPLETED;
import static com.snapinspect.snapinspect3.IF_Object.v_RequestInspection.State.ONGOING;
import static com.snapinspect.snapinspect3.IF_Object.v_RequestInspection.State.UPLOADED;

public class RequestInspectionAdapter extends BaseAdapter {

    private static class ViewHolder {
        TextView tvAddress;
        TextView tvInsTitle;
        TextView tvDate;
        Button btnNotes;
        Button btnViewRoute;
        Button btnAction;
        View bgBtnActionView;

        ViewHolder(View rootView) {
            tvAddress = rootView.findViewById(R.id.tv_address);
            tvInsTitle = rootView.findViewById(R.id.tv_ins_title);
            tvDate = rootView.findViewById(R.id.tv_ins_date);
            btnNotes = rootView.findViewById(R.id.btn_notes);
            btnViewRoute = rootView.findViewById(R.id.btn_view_route);
            btnAction = rootView.findViewById(R.id.btn_action);
            bgBtnActionView = rootView.findViewById(R.id.bg_btn_action);
        }
    }

    private final SharedOnClickListener mClickListener;
    private List<v_RequestInspection> mInspections;
    private final Context mContext;
    private HashMap<v_RequestInspection, Integer> mInsMap;

    public RequestInspectionAdapter(Context ctx, SharedOnClickListener listener) {
        mClickListener = listener;
        mContext = ctx;
        setDataSource(new ArrayList<>());
    }

    public void setDataSource(List<v_RequestInspection> inspections) {
        mInspections = inspections;
        HashMap<v_RequestInspection, Integer> map = new HashMap<>();
        for (int i = 0; i < inspections.size(); i++) {
            map.put(inspections.get(i), i);
        }
        mInsMap = map;
        notifyDataSetChanged();
    }

    @Override
    public boolean isEnabled(int position) {
        v_RequestInspection ins = (v_RequestInspection) getItem(position);
        v_RequestInspection.State state = ins.getState();
        return state == ONGOING || state == COMPLETED;
    }

    @Override
    public int getCount() {
        return mInspections.size();
    }

    @Override
    public Object getItem(int position) {
        return mInspections.get(position);
    }

    @Override
    public long getItemId(int position) {
        if (position < 0 || position >= mInsMap.size()) return -1;
        return mInsMap.get(getItem(position));
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        ViewHolder viewHolder;
        if (convertView == null) {
            View cell = LayoutInflater.from(mContext).inflate(R.layout.cell_request_inspection, null);
            viewHolder = new ViewHolder(cell);
            cell.setTag(viewHolder);
            convertView = cell;
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }

        v_RequestInspection ins = (v_RequestInspection) getItem(position);
        TextViewUtils.updateText(viewHolder.tvAddress, ins.getAddress());
        TextViewUtils.updateText(viewHolder.tvInsTitle, ins.getInsTitle());

        String sDate = DateUtils.cleanDateIfTimeIsZero(
                ins.sDateTime, "MMM dd, yyyy HH:mm", "MMM dd, yyyy");
        TextViewUtils.updateText(viewHolder.tvDate, sDate);

        v_RequestInspection.State state = ins.getState();
        int btnActionRes = R.color.colorPrimary, btnActionTitleRes = R.string.btn_action_start;
        if (state == ONGOING || state == COMPLETED) {
            btnActionRes = R.color.bg_btn_action_upload;
            btnActionTitleRes = R.string.btn_action_upload;
        } else if (state == UPLOADED) {
            btnActionRes = R.color.bg_btn_action_report;
            btnActionTitleRes = R.string.btn_action_report;
        }
        viewHolder.btnAction.setText(btnActionTitleRes);

        viewHolder.btnNotes.setOnClickListener(v -> mClickListener.viewNotes(ins));
        viewHolder.btnViewRoute.setOnClickListener(v -> mClickListener.viewRoute(ins));
        viewHolder.btnAction.setOnClickListener(v -> {
            switch (state) {
                case UPLOADED:
                    mClickListener.reportInspection(ins);
                    break;
                case COMPLETED:
                case ONGOING:
                    mClickListener.uploadInspection(ins);
                    break;
                default:
                    mClickListener.startInspection(ins);
                    break;
            }
        });
        viewHolder.tvAddress.setOnClickListener(v -> {
            switch (state) {
                case UPLOADED:
                    mClickListener.reportInspection(ins);
                    break;
                case ONGOING:
                case COMPLETED:
                default:
                    mClickListener.startInspection(ins);
                    break;
            }
        });

        GradientDrawable bgBtnAction = (GradientDrawable) ResourcesCompat.getDrawable(
                mContext.getResources(), R.drawable.bg_button_request_inspection, null);
        if (bgBtnAction != null) {
            bgBtnAction.setColor(ResourcesCompat.getColor(mContext.getResources(), btnActionRes, null));
            viewHolder.bgBtnActionView.setBackground(bgBtnAction);
        }

        return convertView;
    }

    public interface SharedOnClickListener {
        void startInspection(v_RequestInspection ins);
        void uploadInspection(v_RequestInspection ins);
        void reportInspection(v_RequestInspection ins);
        void viewNotes(v_RequestInspection ins);
        void viewRoute(v_RequestInspection ins);
    }
}
