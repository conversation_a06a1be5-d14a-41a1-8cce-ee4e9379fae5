package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.net.Uri;
import android.os.Handler;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activity.if_settings;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.views.StickyHeaderListView.StickyHeaderAdapter;
import io.intercom.android.sdk.Intercom;

import java.util.LinkedHashMap;
import java.util.List;

import com.snapinspect.snapinspect3.Helper.CommonHelper;

/**
 * @Created by crane on 9/18/17.
 */

public class SettingsAdapter extends StickyHeaderAdapter {
    private static final int TAP_3_TIMES = 3;
    private static final int TAP_5_TIMES = 5;
    private static final int mDelayMilliseconds = 800;

    private OnSharedClickListener mCallback;

    private final Context mContext;
    private final LinkedHashMap<String, List<if_settings.Setting>> mapSetting;
    private int countOfTapped = 0;
    private final Handler handler = new Handler();

    public SettingsAdapter(Context context, LinkedHashMap<String, List<if_settings.Setting>> map) {
        this.mContext = context;
        this.mapSetting = map;
    }

    @Override
    public int sectionCounts() {
        return mapSetting.keySet().toArray().length;
    }

    @Override
    public int rowCounts(int section) {
        if (section < 0) return 0;
        Object[] key = mapSetting.keySet().toArray();
        return mapSetting.get(key[section]).size();
    }

    @Override
    public View getRowView(int section, int row, View convertView, ViewGroup parent) {
        View view = LayoutInflater.from(mContext).inflate(R.layout.cell_settings_item, null);
        try {
            Object[] keys = mapSetting.keySet().toArray();
            String key = (String) keys[section];
            final if_settings.Setting item = mapSetting.get(key).get(row);

            if (section == 0) { // User cell
                view = LayoutInflater.from(mContext).inflate(R.layout.cell_setting_user, null);
                ImageView iconAvatar = view.findViewById(R.id.icon_user_avatar);
                TextView tvName = view.findViewById(R.id.tv_user_name);
                TextView tvCompany = view.findViewById(R.id.tv_user_company);
                TextView tvEmail = view.findViewById(R.id.tv_user_email);
                if_settings.User user = item.user;

                tvName.setText(user.name);
                tvCompany.setText(user.company);
                tvEmail.setText(user.email);

                final String nameLetters = user.getLetters();
                Bitmap avatar = ViewUtils.getLetterTile(
                        nameLetters,
                        26,
                        mContext.getResources().getColor(R.color.setting_red_color),
                        mContext.getResources().getColor(R.color.more_light_gray),
                        60, 60
                    );
                iconAvatar.setImageBitmap(avatar);
            } else if (section == sectionCounts() - 1) { // bottom
                view = LayoutInflater.from(mContext).inflate(R.layout.cell_setting_bottom_logout, null);
                Button btnLogout = view.findViewById(R.id.btn_logout);
                btnLogout.setOnClickListener(v -> mCallback.onClick(SharedClickType.LOG_OUT));
            } else { // option item
                view = LayoutInflater.from(mContext).inflate(R.layout.cell_settings_item, null);

                TextView tvTitle = view.findViewById(R.id.txt_SettingTitle);
                ImageView iconArrow = view.findViewById(R.id.icon_option_arrow);
                CheckBox toggleButton = view.findViewById(R.id.tb_Setting);
                final Button btnVal = view.findViewById(R.id.tb_normal);

                final int lastOptionSection = sectionCounts() - 2;
                if (section < lastOptionSection - 1) {
                    tvTitle.setText(item.title);
                    iconArrow.setVisibility(View.GONE);
                    if (item.type == if_settings.Setting.Type.FIX_CAMERA) {
                        toggleButton.setVisibility(View.GONE);
                        btnVal.setVisibility(View.VISIBLE);

                        String str = CommonHelper.GetPreferenceString(mContext, item.value);
                        if (StringUtils.isEmpty(str)) str = Constants.FixCameraOption.disable;
                        btnVal.setText(str);

                        btnVal.setOnClickListener(v -> {
                            String option = Constants.FixCameraOption.disable;
                            if (btnVal.getText().equals(Constants.FixCameraOption.disable)) {
                                option = Constants.FixCameraOption.portrait;
                            } else if (btnVal.getText().equals(Constants.FixCameraOption.portrait)) {
                                option = Constants.FixCameraOption.landscape;
                            } else if (btnVal.getText().equals(Constants.FixCameraOption.landscape)) {
                                option = Constants.FixCameraOption.disable;
                            }
                            btnVal.setText(option);
                            CommonHelper.SavePreference(mContext, item.value, option);
                        });

                    } else if (item.type == if_settings.Setting.Type.CAM_ROTATION) {
                        toggleButton.setVisibility(View.GONE);
                        btnVal.setVisibility(View.VISIBLE);

                        String str = CommonHelper.GetPreferenceString(mContext, item.value);
                        if (StringUtils.isEmpty(str)) str = "0";

                        btnVal.setText(str);
                        btnVal.setOnClickListener(v -> {
                            if (btnVal.getText().equals("0")) {
                                btnVal.setText("90");
                                CommonHelper.SavePreference(mContext, item.value, "90");
                            } else if (btnVal.getText().equals("90")) {
                                btnVal.setText("180");
                                CommonHelper.SavePreference(mContext, item.value, "180");
                            } else if (btnVal.getText().equals("180")) {
                                btnVal.setText("270");
                                CommonHelper.SavePreference(mContext, item.value, "270");
                            } else if (btnVal.getText().equals("270")) {
                                btnVal.setText("0");
                                CommonHelper.SavePreference(mContext, item.value, "0");
                            }
                        });
                    } else {
                        toggleButton.setVisibility(View.VISIBLE);
                        btnVal.setVisibility(View.GONE);
                        boolean bTurnOn = CommonHelper.GetPreferenceBoolean(mContext, item.value);
                        toggleButton.setChecked(bTurnOn);
                        toggleButton.setTextColor(Color.WHITE);
                        toggleButton.setOnCheckedChangeListener(
                            (buttonView, isChecked) -> {
                                if (!validateOptionEditable(item.type)) {
                                    toggleButton.setChecked(!isChecked); // Revert change
                                    CommonUI.ShowAlert(mContext, "Error",
                                        String.format("The %s cannot be changed due to the role restriction.", item.title));
                                    return;
                                }
                                CommonHelper.SavePreferenceBoolean(mContext, item.value, isChecked);
                            }
                        );
                    }
                } else {
                    tvTitle.setText(item.title);
                    toggleButton.setVisibility(View.GONE);
                    btnVal.setVisibility(View.GONE);

                    if (section < lastOptionSection) {
                        iconArrow.setVisibility(View.VISIBLE);
                        switch (item.type) {
                            case HOW_TO_VIDEOS:
                                view.setOnClickListener(v -> {
                                    Uri uriUrl = Uri.parse("http://www.snapinspect.com/snap3-videotutorial");
                                    Intent launchBrowser = new Intent(Intent.ACTION_VIEW, uriUrl);
                                    mContext.startActivity(launchBrowser);
                                });
                                break;
                            case ASK_SUPPORT:
                                view.setOnClickListener(v -> {
                                    Intercom.client().displayMessageComposer();
                                });
                                break;
                        }
                    } else {
                        iconArrow.setVisibility(View.GONE);
                        tvTitle.setSingleLine(true);
                        tvTitle.setEllipsize(TextUtils.TruncateAt.END);
                        if (item.type == if_settings.Setting.Type.APP_VERSION) {
                            view.setOnClickListener(v -> {
                                handler.removeCallbacksAndMessages(null);
                                switch (++countOfTapped) {
                                    case TAP_3_TIMES:
                                        mCallback.onClick(SharedClickType.FORCE_SUBMIT_DATA_TIP);
                                        break;
                                    case TAP_5_TIMES:
                                        mCallback.onClick(SharedClickType.FORCE_SUBMIT_DATA);
                                        break;
                                }
                                handler.postDelayed(() -> countOfTapped = 0, mDelayMilliseconds);
                            });
                        }
                    }
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_settings.SettingCustomCell", ex, mContext);
        }

        return view;
    }

    @Override
    public View getSectionHeaderView(int section, View convertView, ViewGroup parent) {
        Object[] keys = mapSetting.keySet().toArray();
        String key = (String) keys[section];
        View view = LayoutInflater.from(mContext).inflate(R.layout.header_settings, null);
        TextView textview = view.findViewById(R.id.header_settings_tv_title);
        textview.setText(key);

        boolean hidesHeaderView = !(section == 0 || section == sectionCounts() - 1);
        textview.setVisibility(hidesHeaderView ? View.VISIBLE : View.GONE);
        return view;
    }

    @Override
    public Object getRowItem(int section, int row) {
        Object[] key = mapSetting.keySet().toArray();
        return mapSetting.get((String) key[section]).get(row);
    }

    @Override
    public boolean hasSectionHeaderView(int section) {
        return true;
    }

    public enum SharedClickType {
        LOG_OUT, FORCE_SUBMIT_DATA, FORCE_SUBMIT_DATA_TIP
    }

    public void setOnSharedClickedListener(OnSharedClickListener mCallback) {
        this.mCallback = mCallback;
    }

    public interface OnSharedClickListener {
        void onClick(SharedClickType type);
    }

    private boolean validateOptionEditable(if_settings.Setting.Type optionType) {
        switch (optionType) {
            case DISPLAY_INST:
                return !CommonJson.shouldTurnOnInstruction(mContext);
            case PHOTO_STAMP:
                return !CommonJson.shouldTurnOnPhotoDate(mContext);
            case PHOTO_GEO_TAG:
                return !CommonJson.shouldTurnOnLocation(mContext);
            default:
                return true;
        }
    }
}
