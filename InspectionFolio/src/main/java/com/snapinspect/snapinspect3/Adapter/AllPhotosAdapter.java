package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;

import com.snapinspect.snapinspect3.Helper.CommonUI_Photo;
import com.snapinspect.snapinspect3.Helper.ViewUtils;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.R;

import java.util.ArrayList;
import java.util.List;


public class AllPhotosAdapter extends BaseListAdapter<ai_Photo> {

    private int columnWidth = 0, columnHeight = 0;
    private ViewHolder viewHolder;
    private List<ai_Photo> mSelectedPhotos = new ArrayList<>();

    public AllPhotosAdapter(List<ai_Photo> mList, Context context) {
        super(mList, context);
    }

    public void updateColumnSize(int width, int height) {
        columnWidth = width;
        columnHeight = height;
    }

    public void updateSelectedPhotos(List<ai_Photo> photos) {
        mSelectedPhotos = photos;
        notifyDataSetChanged();
    }

    @Override
    public View getView(int position, View convertView, ViewGroup parent) {
        if (convertView == null){
            convertView = mInflater.inflate(R.layout.cell_all_photos, null);
            viewHolder = new ViewHolder(convertView);
            convertView.setTag(viewHolder);
        } else {
            viewHolder = (ViewHolder) convertView.getTag();
        }

        ViewUtils.setViewSize(viewHolder.photoView, columnWidth, columnHeight);

        ai_Photo photo = getItem(position);
        boolean hasSelected = mSelectedPhotos.contains(photo);
        viewHolder.toggleIconView.setVisibility(hasSelected ? View.VISIBLE: View.GONE);

        CommonUI_Photo.showFullFile(mContext, photo.getId(), viewHolder.photoView, viewHolder.indicatorView);

        return convertView;
    }

    private class ViewHolder {
        ImageView photoView;
        ImageView toggleIconView;
        View indicatorView;

        ViewHolder(View rootView) {
            photoView = rootView.findViewById(R.id.img_photo_view);
            toggleIconView = rootView.findViewById(R.id.img_toggle_icon);
            indicatorView = rootView.findViewById(R.id.indicator_view);
        }
    }
}
