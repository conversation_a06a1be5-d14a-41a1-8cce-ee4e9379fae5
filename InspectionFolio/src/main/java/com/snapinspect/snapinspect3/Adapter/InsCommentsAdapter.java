package com.snapinspect.snapinspect3.Adapter;

import android.annotation.SuppressLint;
import android.content.Context;
import android.view.ViewGroup;

import androidx.recyclerview.widget.RecyclerView;

import com.snapinspect.snapinspect3.IF_Object.ai_Comment;
import com.snapinspect.snapinspect3.views.CellViews.InsCommentHeaderCell;
import com.snapinspect.snapinspect3.views.CellViews.InsCommentsRowCell;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 12.02.18
 */
public class InsCommentsAdapter extends RecyclerView.Adapter<InsCommentsAdapter.ViewHolder> {
    private static final int VIEW_TYPE_HEADER = 0;
    private static final int VIEW_TYPE_ROW = 1;
    private final Context context;
    private final List<Item> items = new ArrayList<>();

    private static class Item {
        private final String header;
        private final boolean isHeader;
        private final ai_Comment oComment;
        private boolean isFirstRow = false;

        Item(String header) {
            this.header = header;
            this.oComment = null;
            this.isHeader = true;
        }

        Item(ai_Comment cmt, boolean isFirst) {
            this.oComment = cmt;
            this.header = null;
            this.isHeader = false;
            this.isFirstRow = isFirst;
        }
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        private final InsCommentHeaderCell headerCell;
        private final InsCommentsRowCell rowCell;

        public ViewHolder(InsCommentHeaderCell view) {
            super(view);
            this.headerCell = view;
            this.rowCell = null;
        }

        public ViewHolder(InsCommentsRowCell view) {
            super(view);
            this.rowCell = view;
            this.headerCell = null;
        }
    }

    public InsCommentsAdapter(Context context) {
        this.context = context;
    }

    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        switch (viewType) {
            case VIEW_TYPE_HEADER:
                return new ViewHolder(new InsCommentHeaderCell(this.context));
            case VIEW_TYPE_ROW:
                return new ViewHolder(new InsCommentsRowCell(this.context));
            default:
                throw new RuntimeException();
        }
    }

    @SuppressLint({"SetTextI18n"})
    public void onBindViewHolder(ViewHolder holder, int position) {
        final Item item = this.items.get(position);
        if (item.isHeader) {
            if (holder.headerCell != null) {
                holder.headerCell.displayItem(item.header);
            }
        } else if (holder.rowCell != null) {
            if (item.oComment != null) {
                holder.rowCell.displayComment(item.oComment, item.isFirstRow);
            }
        }
    }

    public int getItemCount() {
        return this.items.size();
    }

    public int getItemViewType(int position) {
        return (items.get(position)).isHeader ? VIEW_TYPE_HEADER : VIEW_TYPE_ROW;
    }

    public void setItems(Map<String, List<ai_Comment>> map, List<String> headers) {
        items.clear();
        boolean isFirst = false;

        if (map != null) {
            for (String header : headers) {
                List<ai_Comment> t = map.get(header);
                if (!(t == null || t.isEmpty())) {
                    for (int i = 0; i < t.size(); i++) {
                        if (i == 0) {
                            isFirst = true;
                            this.items.add(new Item(header));
                        }

                        this.items.add(new Item(t.get(i), isFirst));

                        isFirst = false;
                    }
                }
            }
        }

        this.notifyDataSetChanged();
    }

}
