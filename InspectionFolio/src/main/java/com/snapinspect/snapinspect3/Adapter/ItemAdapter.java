
package com.snapinspect.snapinspect3.Adapter;

import android.content.Context;

import androidx.annotation.NonNull;
import androidx.core.util.Pair;

import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.if_OrderInspection;
import com.woxthebox.draglistview.DragItemAdapter;

import java.util.ArrayList;
import java.util.List;

/*
 * @Created by o<PERSON><PERSON> on 16/06/17.
 */
public class ItemAdapter extends DragItemAdapter<Pair<Long, ai_InsItem>, ItemAdapter.ViewHolder> {

    private final int mLayoutId;
    private final int mGrabHandleId;
    private final boolean mDragOnLongPress;
    private final Context oContext;
    public ItemAdapter(ArrayList<Pair<Long, ai_InsItem>> list, int layoutId, int grabHandleId, boolean dragOnLongPress, Context context) {
        mLayoutId = layoutId;
        mGrabHandleId = grabHandleId;
        mDragOnLongPress = dragOnLongPress;
        oContext = context;
        setHasStableIds(true);
        setItemList(list);
    }

    public void setItemArray(ArrayList<Pair<Long, ai_InsItem>> list) {
        setItemList(list);
    }

    @Override
    public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext()).inflate(mLayoutId, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ViewHolder holder, int position) {
        super.onBindViewHolder(holder, position);
        ai_InsItem oInsItem = mItemList.get(position).second;//
        holder.mText.setText(oInsItem.sName);
        holder.itemView.setTag(mItemList.get(position));

        holder.mText.setTag(position);
        holder.mText.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            public void onFocusChange(View v, boolean hasFocus) {
                if (!hasFocus) {
                    // final int position = v.getId();
                    final EditText Caption = (EditText) v;
                    int position = Integer.parseInt(v.getTag().toString());
                    mItemList.get(position).second.sName = Caption.getText().toString();
                    mItemList.get(position).second.save();
                    InputMethodManager inputManager =
                            (InputMethodManager) oContext.
                                    getSystemService(Context.INPUT_METHOD_SERVICE);
                    inputManager.hideSoftInputFromWindow(
                            v.getApplicationWindowToken(),
                            InputMethodManager.HIDE_NOT_ALWAYS);
                }
            }
        });
        holder.mText.setOnEditorActionListener(
                new EditText.OnEditorActionListener() {
                    @Override
                    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                        if (actionId == EditorInfo.IME_ACTION_DONE) {
                            int position = Integer.parseInt(v.getTag().toString());
                            mItemList.get(position).second.sName = v.getText().toString();
                            mItemList.get(position).second.save();

                            InputMethodManager inputManager =
                                    (InputMethodManager) oContext.
                                            getSystemService(Context.INPUT_METHOD_SERVICE);
                            inputManager.hideSoftInputFromWindow(
                                    v.getApplicationWindowToken(),
                                    InputMethodManager.HIDE_NOT_ALWAYS);

                            return true;
                        }
                        return false;
                    }
                }
        );
        holder.btnDelete.setTag(position);
        holder.btnDelete.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                final int position = Integer.parseInt(view.getTag().toString());

//                AlertDialog.Builder builder = CommonUI.GetAlertBuilder("Message", "Are you sure to Delete this room?", oContext, true, false);
//
//                builder.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
//                    public void onClick(DialogInterface dialog, int which) {
//                        ai_InsItem insItem = mItemList.get(position).second;
//
//                        if (CommonHelper.bIns1DefaultAdd(insItem.sConfig)){
//                            Toast.makeText(oContext, "Error! Compulsory section can not be deleted!", Toast.LENGTH_LONG).show();
//                            return;
//                        }
//                        insItem.bDeleted = true;
//                        insItem.save();
//                        mItemList.remove(position);
////                        notifyDataSetChanged();
//                        ((if_OrderInspection) oContext).updateInsItem();
//                    }
//                });
//                builder.show();

                new MaterialDialog.Builder(oContext)
                        .title("Message")
                        .content(R.string.lbl_DeleteItemPrompt, oInsItem.sName)
                        .positiveText(R.string.tv_ok)
                        .onPositive(new MaterialDialog.SingleButtonCallback() {
                            @Override
                            public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                ai_InsItem insItem = mItemList.get(position).second;

                                if (CommonHelper.bIns1DefaultAdd(insItem.sConfig)){
                                    Toast.makeText(oContext, "Error! Compulsory section can not be deleted!", Toast.LENGTH_LONG).show();
                                    return;
                                }
                                insItem.bDeleted = true;
                                insItem.save();
                                mItemList.remove(position);
//                        notifyDataSetChanged();
                                ((if_OrderInspection) oContext).updateInsItem();
                            }
                        })
                        .show();
            }
        });
    }

    @Override
    public long getUniqueItemId(int position) {
        return mItemList.get(position).first;
    }

    @Override
    public List<Pair<Long, ai_InsItem>> getItemList() {
        return mItemList;
    }

    class ViewHolder extends DragItemAdapter.ViewHolder {
        EditText mText;
        ImageButton btnDelete;
        ViewHolder(final View itemView) {
            super(itemView, mGrabHandleId, mDragOnLongPress);
            mText = itemView.findViewById(R.id.txt_RoomName);
            btnDelete = itemView.findViewById(R.id.btn_DeleteRoom);
        }

        @Override
        public void onItemClicked(View view) {
         //   Toast.makeText(view.getContext(), "Item clicked", Toast.LENGTH_SHORT).show();
        }

        @Override
        public boolean onItemLongClicked(View view) {
           // Toast.makeText(view.getContext(), "Item long clicked", Toast.LENGTH_SHORT).show();
            return true;
        }
    }
}
