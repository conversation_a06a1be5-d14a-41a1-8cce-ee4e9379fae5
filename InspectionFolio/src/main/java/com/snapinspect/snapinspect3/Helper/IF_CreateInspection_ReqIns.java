package com.snapinspect.snapinspect3.Helper;

import android.content.Context;

import android.location.Location;
import android.text.TextUtils;
import com.snapinspect.snapinspect3.IF_Object.ai_AssetLayout;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_InsType;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.IF_Object.ai_Layout;
import com.snapinspect.snapinspect3.database.entities.InsItem;
import com.snapinspect.snapinspect3.database.entities.Layout;

import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 18/03/14.
 */
public class IF_CreateInspection_ReqIns {
    public void LoadInspectionDefaultOrder(int iInsID, String sPath){
        List<ai_Layout> lsLayout = CommonRequestInspection.LoadLayout(0, sPath);
        List<ai_InsItem> lsInsItem = CommonDB.GetChildInsItem(0, iInsID);
        int iCount = 1;
        for (ai_Layout oLayout: lsLayout){
            for (ai_InsItem oInsItem: lsInsItem){
                if (oLayout.iSLayoutID == oInsItem.iSLayoutID){
                    oInsItem.iSort = iCount;
                    CommonDB.saveInsItem(oInsItem);
                    iCount ++;
                }
            }
        }

    }
    public int CreateInspectionFromAssetLayout(
            Context oContext, ArrayList<ai_AssetLayout> lsAssetLayout, ai_InsType oInsType, int iSAssetID,
            String sComments, String dtStartDate, String dtEndDate, String sAddress1, String sAddress2,
            int iSScheduleID, String sScheduleCustom1, String sFilePath, String sDtSchedule) {
        try {
            Location location = GeoLocationManager.getInstance(oContext).getLocation();
            String sLat = String.valueOf(location != null ? location.getLatitude() : "");
            String sLong = String.valueOf(location != null ? location.getLongitude() : "");
            String sCustom1 = iSScheduleID > 0 && !StringUtils.isEmpty(sDtSchedule)
                    ? CommonJson.AddJsonKeyValue("", Constants.Keys.DATE_SCHEDULE_PICKED, sDtSchedule)
                    : "";
            ai_Inspection oInspection = new ai_Inspection(oInsType.iSInsTypeID, iSAssetID, oInsType.sPTC, oInsType.sType, oInsType.sInsTitle,
                    sAddress1 + ", " + sAddress2, sComments, dtStartDate, dtEndDate, sLat, sLong, sAddress1, sAddress2,
                    iSScheduleID, sCustom1, sScheduleCustom1);
            CommonDB.saveInspection(oInspection);
            long lInspectionID = oInspection.getId();
            int iInspectionID = (int) lInspectionID;
            if (iInspectionID > 0) {
                List<ai_Layout> lsParentLayout = CommonRequestInspection.LoadLayout(0, sFilePath);
                String sType = oInspection.sType;


                int iCount1st = 0;
                for (ai_AssetLayout oAssetLayout : lsAssetLayout) {
                    ai_Layout o1stLayout = GetLayout(lsParentLayout, oAssetLayout.iSLayoutID);
                    if (CommonDB_Inspection.bPassLayoutValidation(o1stLayout, oInsType)) continue;
                    if (o1stLayout != null) {
                        if (oInsType.sType.equalsIgnoreCase("S") &&
                                (!o1stLayout.sQType.equalsIgnoreCase("V")) &&
                                (!o1stLayout.sQType.equalsIgnoreCase("S"))) {
                            iCount1st++;
                            String sValue1 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVOneConfig);
                            String sValue2 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVTwoConfig);
                            String sValue3 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVThreeConfig);
                            String sValue4 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVFourConfig);
                            String sValue5 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVFiveConfig);
                            String sValue6 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVSixConfig);

                            ai_InsItem oInsItem = new ai_InsItem(0, iInspectionID, o1stLayout.iSLayoutID, oAssetLayout.sName,
                                    sValue1, sValue2, sValue3, sValue4, sValue5, sValue6, o1stLayout.sQType, o1stLayout.sSVOneConfig,
                                    o1stLayout.sSVTwoConfig, o1stLayout.sSVThreeConfig, o1stLayout.sSVFourConfig,
                                    o1stLayout.sSVFiveConfig, o1stLayout.sSVSixConfig, o1stLayout.sSConfig, false,
                                    iCount1st, oAssetLayout.iSAssetLayoutID, "", "", "");
                            CommonDB.saveInsItem(oInsItem);

                            if (o1stLayout.sSConfig != null && CommonJson.GetJsonKeyValue("ADD", o1stLayout.sSConfig) != null) {
                                o1stLayout.sSConfig = CommonJson.RemoveJsonKey("ADD", o1stLayout.sSConfig);
                            }
                        } else {
                            iCount1st++;
                            String sDefaultValue1 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVOneConfig);
                            String sDefaultValue2 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVTwoConfig);
                            String sDefaultValue3 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVThreeConfig);
                            String sDefaultValue4 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVFourConfig);
                            String sDefaultValue5 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVFiveConfig);
                            String sDefaultValue6 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVSixConfig);
                            ai_InsItem oInsItem = new ai_InsItem(0, iInspectionID, o1stLayout.iSLayoutID, oAssetLayout.sName,
                                    sDefaultValue1, sDefaultValue2, sDefaultValue3, sDefaultValue4, sDefaultValue5, sDefaultValue6,
                                    o1stLayout.sQType, o1stLayout.sFVOneConfig,
                                    o1stLayout.sFVTwoConfig, o1stLayout.sFVThreeConfig, o1stLayout.sFVFourConfig,
                                    o1stLayout.sFVFiveConfig, o1stLayout.sFVSixConfig, o1stLayout.sFConfig, false, iCount1st,
                                    oAssetLayout.iSAssetLayoutID, "", "", "");
                            CommonDB.saveInsItem(oInsItem);

                            // For request inspection, load the child layout items from the xml file directly
                            List<ai_Layout> ls2ndLayout = CommonRequestInspection.LoadLayout(o1stLayout.iSLayoutID, sFilePath);
                            ls2ndLayout = CommonDB_Inspection.bypassLayouts(ls2ndLayout, oInsType);

                            if (ls2ndLayout != null && ls2ndLayout.size() > 0) {
                                List<Layout> roomLayouts = new ArrayList<>();
                                for (ai_Layout aiLayout : ls2ndLayout) {
                                    roomLayouts.add(new Layout(aiLayout));
                                }
                                CommonDB.AddChildLayout(new InsItem(oInsItem), oContext, iInspectionID, roomLayouts);
                            }

                            if (sType.equals("F") && o1stLayout.sFConfig != null && CommonJson.GetJsonKeyValue("ADD", o1stLayout.sFConfig) != null) {
                                o1stLayout.sFConfig = CommonJson.RemoveJsonKey("ADD", o1stLayout.sFConfig);
                            } else if (sType.equals("S") && o1stLayout.sSConfig != null && CommonJson.GetJsonKeyValue("ADD", o1stLayout.sSConfig) != null) {
                                o1stLayout.sSConfig = CommonJson.RemoveJsonKey("ADD", o1stLayout.sSConfig);
                            }
                        }
                    }
                }
                iCount1st++;
                for (ai_Layout o1stLayout : lsParentLayout) {
                    if (CommonDB_Inspection.bPassLayoutValidation(o1stLayout, oInsType)) continue;
                    String sConfig = sType.equals("F") ? (o1stLayout.sFConfig == null ? "" : o1stLayout.sFConfig) : (o1stLayout.sSConfig == null ? "" : o1stLayout.sSConfig);
                    if (sConfig != null && CommonJson.GetJsonKeyValue("ADD", sConfig) != null) {
                        if (oInsType.sType.equalsIgnoreCase("S") && (!o1stLayout.sQType.equalsIgnoreCase("V")) && (!o1stLayout.sQType.equalsIgnoreCase("S"))) {

                            String sValue1 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVOneConfig);
                            String sValue2 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVTwoConfig);
                            String sValue3 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVThreeConfig);
                            String sValue4 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVFourConfig);
                            String sValue5 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVFiveConfig);
                            String sValue6 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sSVSixConfig);

                            ai_InsItem oInsItem = new ai_InsItem(0, iInspectionID, o1stLayout.iSLayoutID, o1stLayout.sName,
                                    sValue1, sValue2, sValue3, sValue4, sValue5, sValue6, o1stLayout.sQType, o1stLayout.sSVOneConfig,
                                    o1stLayout.sSVTwoConfig, o1stLayout.sSVThreeConfig, o1stLayout.sSVFourConfig,
                                    o1stLayout.sSVFiveConfig, o1stLayout.sSVSixConfig, o1stLayout.sSConfig, false, iCount1st, 0, "", "", "");
                            CommonDB.saveInsItem(oInsItem);
                            // CommonDB.SaveChildLayoutItemDefault(oInsItem, oContext, iInspectionID);
                        } else {
                            String sDefaultValue1 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVOneConfig);
                            String sDefaultValue2 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVTwoConfig);
                            String sDefaultValue3 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVThreeConfig);
                            String sDefaultValue4 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVFourConfig);
                            String sDefaultValue5 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVFiveConfig);
                            String sDefaultValue6 = CommonHelper.GetDefaultCheckboxValue(o1stLayout.sFVSixConfig);
                            ai_InsItem oInsItem = new ai_InsItem(0, iInspectionID, o1stLayout.iSLayoutID, o1stLayout.sName,
                                    sDefaultValue1, sDefaultValue2, sDefaultValue3, sDefaultValue4, sDefaultValue5, sDefaultValue6,
                                    o1stLayout.sQType, o1stLayout.sFVOneConfig,
                                    o1stLayout.sFVTwoConfig, o1stLayout.sFVThreeConfig, o1stLayout.sFVFourConfig,
                                    o1stLayout.sFVFiveConfig, o1stLayout.sFVSixConfig, o1stLayout.sFConfig, false, iCount1st, 0, "", "", "");
                            CommonDB.saveInsItem(oInsItem);
                            CommonRequestInspection.SaveChildLayoutItemDefault(oInsItem, oContext, iInspectionID, sFilePath, oInsType);
                        }
                        iCount1st++;
                    }
                }
            }
            return iInspectionID;
        }catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_CreateInspection_ReqIns.CreateInspectionFromAssetLayout", ex, oContext);
            return 0;
        }

    }

//    private List<ai_Layout> GetLayoutsByIDString(Context oContext, String sChildID, int iSPLayoutID, String sPTC, String sQType, String sPath){
//        try {
//            ArrayList<ai_Layout> arReturn = new ArrayList<ai_Layout>();
//            if (sChildID == null || sChildID.trim().length() == 0) {
//                sChildID = "";
//            } else {
//                JSONArray arrObject = new JSONArray(sChildID);
//                if (arrObject != null && arrObject.length() > 0) {
//                    List<ai_Layout> lsChild = CommonRequestInspection.LoadLayout(iSPLayoutID, sPath);
//                    for (int x = 0; x < arrObject.length(); x++) {
//                        JSONObject oTemp = arrObject.getJSONObject(x);
//                        if (oTemp.getInt("i") > 0) {
//                            ai_Layout oTempLayout = GetLayout(lsChild, oTemp.getInt("i"));
//
//
//                                if (oTemp.has("n") && oTemp.getString("n").length() > 0) {
//                                    oTempLayout.sName = oTemp.getString("n");
//                                    oTempLayout.sFieldOne = "c";
//                                }
//                                else{
//                                    oTempLayout.sFieldOne = "";
//                                }
//                                arReturn.add(oTempLayout);
//
//                        }
//                        else{
//                            ai_Layout oTempLayout = new ai_Layout();
//                            oTempLayout.iSLayoutID = 0;
//                            oTempLayout.iSPLayoutID = iSPLayoutID;
//                            oTempLayout.sPTC = sPTC;
//                            oTempLayout.sQType = sQType;
//                            oTempLayout.sName = oTemp.getString("n");
//                            oTempLayout.sFVOneConfig = "";
//                            oTempLayout.sFVTwoConfig = "";
//                            oTempLayout.sFVThreeConfig = "";
//                            oTempLayout.sFVFourConfig = "";
//                            oTempLayout.sFVFiveConfig = "";
//                            oTempLayout.sFVSixConfig = "";
//                            oTempLayout.sSVOneConfig = "";
//                            oTempLayout.sSVTwoConfig = "";
//                            oTempLayout.sSVThreeConfig = "";
//                            oTempLayout.sSVFourConfig = "";
//                            oTempLayout.sSVFiveConfig = "";
//                            oTempLayout.sSVSixConfig = "";
//                            oTempLayout.sFConfig = "";
//                            oTempLayout.sSConfig = "";
//                            oTempLayout.sFieldOne = "c";
//                            arReturn.add(oTempLayout);
//                        }
//                    }
//                }
//                return arReturn;
//
//            }
//        }catch(Exception ex){
//            ai_BugHandler.ai_Handler_Exception("Exception", "IF_CreateInspection_ReqIns.GetLayoutsByIDString - " + iSPLayoutID, ex, oContext);
//
//        }
//        return null;
//    }
    private ai_Layout GetLayout(List<ai_Layout> lsLayout, int iSLayoutID){
        for (ai_Layout layout : lsLayout) {
            if (layout.iSLayoutID == iSLayoutID) {
                return layout;
            }
        }
        return null;
    }
}
