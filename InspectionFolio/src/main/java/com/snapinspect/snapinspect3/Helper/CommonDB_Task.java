package com.snapinspect.snapinspect3.Helper;

import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_TaskList;

public class CommonDB_Task {
    public static void CreateTaskView() {
        String statement = "CREATE VIEW IF NOT EXISTS v_task " +
                "(iTaskID, iSTaskID, iSubmitCustomerID, iFolllowUpCustomerID, iSAssetID, iInsItemID, iInspectionID, sCategory, iPriority, sCode, sTitle, sDescription, sPhotoURL, sVideoURL, dtDateDue, sCustom1, sCustom2, bClosed, bDeleted, dtComplete, dtUpdate, iPTaskID, sRef, sBuildingAddress, sUnitAddress, sRoomAddress, bApartment, sSearchTerm) AS SELECT c.iTaskID, c.iSTaskID, c.iSubmitCustomerID, c.iFolllowUpCustomerID, c.iSAssetID, c.iInsItemID, c.iInspectionID, c.sCategory, c.iPriority, c.sCode, c.sTitle, c.sDescription, c.sPhotoURL, c.sVideoURL, c.dtDateDue, c.sCustom1, c.sCustom2, c.bClosed, c.bDeleted, c.dtComplete, c.dtUpdate, c.iPTaskID, d.sRef, d.sBuildingAddress, d.sUnitAddress, d.sRoomAddress, d.bApartment, d.sSearchTerm FROM ai_Tasks c left join v_asset d on c.iSAssetID == d.iSAssetID where c.bDeleted == 0 AND c.bClosed == 0";
        try {
            ai_TaskList.executeQuery(statement);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Task.CreateTaskView", ex);
        }
    }
}
