package com.snapinspect.snapinspect3.Helper;

import android.content.Context;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.event.ProgressEvent;
import com.amazonaws.event.ProgressListener;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;

import static com.snapinspect.snapinspect3.activity.UploadService.UPDATE_PROGRESS;

public class CommonUploadAction {

    public static boolean UploadVideoAction(Context context, ai_Video oVideo, int iCount, JSONObject oJson, ProgressCallback callback) {
        try {
            String sAccessKey = oJson.getString("sAccessKey");
            String sSecretKey = oJson.getString("sSecretKey");
            String sToken = oJson.getString("sToken");
            String sS3VideoURL = oJson.getString("sVideoURL");
            String sS3ThumbURL = oJson.getString("sThumbURL");
            String sBucket = oJson.getString("sBucket");
            if (!SendVideoToS3(context, oVideo, iCount, sAccessKey, sSecretKey, sToken, sS3VideoURL, sS3ThumbURL, sBucket, callback)) {
                if (!SendVideoToS3(context, oVideo, iCount, sAccessKey, sSecretKey, sToken, sS3VideoURL, sS3ThumbURL, sBucket, callback)) {
                    return false;
                }
            }
            oVideo.sSFile = sS3VideoURL;
            oVideo.sSThumb = sS3ThumbURL;
            return true;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadVideoAction", ex, context);
            return false;
        }
    }

    public static boolean SendVideoToS3(Context context, ai_Video oVideo, int iCount, String sAccessKey, String sScrectKey, String sToken, String sS3VideoURL,
                                        String sS3ThumbURL, String sBucket, ProgressCallback callback) throws IOException {
        try {
            ClientConfiguration oConfig = new ClientConfiguration();
            oConfig.setConnectionTimeout(5 * 60 * 1000);
            oConfig.setSocketTimeout(5 * 60 * 1000);

            AmazonS3 oClient = new AmazonS3Client(new BasicSessionCredentials(sAccessKey, sScrectKey, sToken), oConfig);
            if (!SendFile(context, oVideo.getThumb(), sS3ThumbURL, oClient, iCount, sBucket, callback)) {
                SendFile(context, oVideo.getThumb(), sS3ThumbURL, oClient, iCount, sBucket, callback);
            }
            if (!SendFile(context, oVideo.getFile(), sS3VideoURL, oClient, iCount, sBucket, callback)) {
                if (!SendFile(context, oVideo.getFile(), sS3VideoURL, oClient, iCount, sBucket, callback)) {
                    return false;
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.SendVideoToS3", ex, context);
        }
        return true;
    }

    public static boolean SendFile(Context context, String sFileURL, String sS3URL, AmazonS3 oClient, final int iCount, String sBucket, ProgressCallback callback) {
        try {
            File oFile = new File(sFileURL);

            if (oFile.exists()) {
                final String sMss = "Uploading Video " + (iCount) + " - ";
                if (oFile.length() <= 5242880) {
                    final long lFileLength = oFile.length();
                    //TransferManager oManager = new TransferManager(oClient);

                    PutObjectRequest oRequest = new PutObjectRequest(sBucket, sS3URL, oFile);
                    oRequest.setCannedAcl(CannedAccessControlList.PublicRead);
                    oRequest.setGeneralProgressListener(new ProgressListener() {
                        long lBytesTransfered = 0;

                        @Override
                        public void progressChanged(ProgressEvent progressEvent) {
                            lBytesTransfered = lBytesTransfered + progressEvent.getBytesTransferred();
                            if (callback != null) {
                                float progressPercentage = (lBytesTransfered / (float) lFileLength) * 100;
                                String progress = String.format("%.2f%%", progressPercentage);
                                callback.onProgress(UPDATE_PROGRESS, "", sMss + progress);
                            }
                        }
                    });
                    oClient.putObject(oRequest);
                    CommonDB.InsertLog(context, "Upload Video", "Success " + sS3URL);

                    return true;
                } else {
                    long iFilePosition = 0;
                    long iPartSize = 5242880;
                    final long iContentLength = oFile.length();
                    InitiateMultipartUploadRequest oRequest = new InitiateMultipartUploadRequest(sBucket, sS3URL);
                    oRequest.setCannedACL(CannedAccessControlList.PublicRead);

                    InitiateMultipartUploadResult oResult = oClient.initiateMultipartUpload(oRequest);
                    ArrayList<PartETag> lsTag = new ArrayList<PartETag>();
                    for (int i = 1; iFilePosition < iContentLength; i++) {
                        iPartSize = Math.min(iPartSize, iContentLength - iFilePosition);
                        UploadPartRequest oTempRequest = new UploadPartRequest().withBucketName(sBucket).withKey(sS3URL).withUploadId(oResult.getUploadId())
                                .withPartNumber(i).withFileOffset(iFilePosition).withFile(oFile).withPartSize(iPartSize);

                        final long lTempFilePosition = iFilePosition;
                        oTempRequest.setGeneralProgressListener(new ProgressListener() {
                            long lBytesTransfered = lTempFilePosition;

                            @Override
                            public void progressChanged(ProgressEvent progressEvent) {
                                lBytesTransfered = lBytesTransfered + progressEvent.getBytesTransferred();
                                if (callback != null) {
                                    float progressPercentage = (lBytesTransfered / (float) iContentLength) * 100;
                                    String progress = String.format("%.2f%%", progressPercentage);
                                    callback.onProgress(UPDATE_PROGRESS, "", sMss + progress);
                                }
                            }
                        });
                        boolean bPartSuccess = false;
                        while (!bPartSuccess) {
                            if (lsTag.add(oClient.uploadPart(oTempRequest).getPartETag())) {
                                bPartSuccess = true;
                                iFilePosition = iFilePosition + iPartSize;
                            } else {
                                bPartSuccess = false;
                            }
                        }
                    }
                    CompleteMultipartUploadRequest oCompleteRequest = new CompleteMultipartUploadRequest(sBucket, sS3URL, oResult.getUploadId(), lsTag);

                    CompleteMultipartUploadResult oFinalReport = oClient.completeMultipartUpload(oCompleteRequest);

                    if (oFinalReport.getBucketName().equalsIgnoreCase(sBucket)) {
                        CommonDB.InsertLog(context, "Upload Video", "Success " + sS3URL);
                        return true;
                    } else {
                        CommonDB.InsertLog(context, "Upload Video", "Failed " + sS3URL);
                        return false;
                    }

                }
            } else {
                CommonDB.InsertLog(context, "Upload Video", "Failed No File " + sS3URL + " - " + sFileURL);
                return false;
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.SendFile", ex, context);
            CommonDB.InsertLog(context, "Upload Video", "Failed Exception " + sS3URL);
            return false;
        }
    }
}
