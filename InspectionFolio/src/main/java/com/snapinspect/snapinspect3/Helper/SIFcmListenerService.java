package com.snapinspect.snapinspect3.Helper;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Build;
import androidx.annotation.NonNull;
import androidx.core.app.NotificationCompat;
import androidx.core.app.NotificationCompat.Builder;
import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;
import com.snapinspect.snapinspect3.IF_Object.InboxActionType;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Inbox;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.Inbox.if_Inbox;
import com.snapinspect.snapinspect3.activitynew.tasks.if_EditTask;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.jetbrains.annotations.NotNull;

import static android.os.Build.VERSION.SDK_INT;

public class SIFcmListenerService extends FirebaseMessagingService {

    @Override
    public void onMessageReceived(@NotNull RemoteMessage remoteMessage) {
        try {
            String sSI_Action = remoteMessage.getData().get("si_action");
            String sSI_Param = remoteMessage.getData().get("si_param");
            String sAlert = remoteMessage.getData().get("alert");

            String sTitle = CommonJson.GetJsonKeyValue("title", sAlert);
            String sBody = CommonJson.GetJsonKeyValue("body", sAlert);
            try {
                String bServerSync = remoteMessage.getData().get(Constants.Keys.bServerSync);
                if (bServerSync != null && bServerSync.equalsIgnoreCase("1")) {
                    CommonHelper.SavePreference(getApplicationContext(), Constants.Keys.bServerSync, "1");
                }
            } catch (Exception eex) {

            }

            int notifyID = 1;
            String CHANNEL_ID = "my_channel_01";// The id of the channel.
            NotificationManager mNotificationManager =
                    (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);
            int importance;

            if (SDK_INT >= android.os.Build.VERSION_CODES.O) {
                importance = NotificationManager.IMPORTANCE_HIGH;
                NotificationChannel mChannel = new NotificationChannel(CHANNEL_ID, "SnapInspect", importance);
                mNotificationManager.createNotificationChannel(mChannel);
            }

            Builder oBuilder = new Builder(this, CHANNEL_ID)
                    .setBadgeIconType(NotificationCompat.BADGE_ICON_SMALL)
                    .setSmallIcon(R.drawable.ic_launcher)
                    .setLargeIcon(BitmapFactory.decodeResource(getApplicationContext().getResources(), R.drawable.ic_launcher))
                    .setContentTitle(sTitle)
                    .setContentText(sBody)
                    .setAutoCancel(true);

            if (!StringUtils.isEmpty(sSI_Action)) {
                Intent actionIntent;
                InboxActionType actionType = ai_Inbox.getActionType(sSI_Action);
                if (InboxActionType.VIEW_TASK_COMMENT == actionType) {
                    actionIntent = new Intent(this, if_EditTask.class);
                    int iTaskID = CommonHelper.getInt(CommonJson.GetJsonKeyValue("iTaskID", sSI_Param));
                    actionIntent.putExtra(Constants.Extras.iSTaskID, iTaskID);
                    actionIntent.putExtra(Constants.Extras.SCROLL_TO_BOTTOM, true);
                } else {
                    actionIntent = new Intent(this, if_Inbox.class);
                    actionIntent.putExtra(Constants.Extras.iActionID, Integer.parseInt(sSI_Param));
                    actionIntent.putExtra(Constants.Extras.iActionType, sSI_Action);
                }

                int flags = PendingIntent.FLAG_ONE_SHOT;
                if (SDK_INT >= Build.VERSION_CODES.M) flags |= PendingIntent.FLAG_IMMUTABLE;

                PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, actionIntent, flags);
                oBuilder.setContentIntent(pendingIntent);
            }

            Notification notification = oBuilder.build();
            mNotificationManager.notify(notifyID, notification);
        } catch (Exception eee) {
            ai_BugHandler.ai_Handler_Exception(eee);
        }
    }

    @Override
    public void onNewToken(@NonNull @NotNull String s) {
        super.onNewToken(s);
    }
}
