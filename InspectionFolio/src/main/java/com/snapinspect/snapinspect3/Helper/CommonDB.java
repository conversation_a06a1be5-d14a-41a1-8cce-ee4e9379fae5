package com.snapinspect.snapinspect3.Helper;

import android.content.Context;
import android.location.Location;
import android.util.Log;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.SI_DB.db_Asset;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.SI_DB.db_Inspection;
import com.snapinspect.snapinspect3.SI_DB.db_Media;
import com.snapinspect.snapinspect3.database.RoomDatabaseManager;
import com.snapinspect.snapinspect3.database.entities.Assets;
import com.snapinspect.snapinspect3.database.entities.AssetLayout;
import com.snapinspect.snapinspect3.database.entities.CheckList;
import com.snapinspect.snapinspect3.database.entities.Contact;
import com.snapinspect.snapinspect3.database.entities.File;
import com.snapinspect.snapinspect3.database.entities.FloorPlan;
import com.snapinspect.snapinspect3.database.entities.InsItem;
import com.snapinspect.snapinspect3.database.entities.Inspection;
import com.snapinspect.snapinspect3.database.entities.InsType;
import com.snapinspect.snapinspect3.database.entities.Layout;
import com.snapinspect.snapinspect3.database.entities.NoticeCategory;
import com.snapinspect.snapinspect3.database.entities.Notification;
import com.snapinspect.snapinspect3.database.entities.Photo;
import com.snapinspect.snapinspect3.database.entities.Project;
import com.snapinspect.snapinspect3.database.entities.ProjectInspection;
import com.snapinspect.snapinspect3.database.entities.QuickPhrase;
import com.snapinspect.snapinspect3.database.entities.Schedule;
import com.snapinspect.snapinspect3.database.entities.User;
import com.snapinspect.snapinspect3.database.entities.Video;
import com.snapinspect.snapinspect3.app.App;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.*;
import java.util.*;

/**
 * Created by TerrySun on 4/04/14.
 */
public class CommonDB {
    
    /**
     * Get Room Database Manager instance
     */
    private static RoomDatabaseManager getRoomManager() {
        return RoomDatabaseManager.getInstance(App.getContext());
    }
    
    /**
     * Reset Database - Updated to use Room alongside Sugar ORM
     */
    public static void ResetDB() {
        try {
            // Reset using Room Database
            getRoomManager().getDatabase().clearAllTables();
            
            Log.i("CommonDB", "Database reset completed (Room)");
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }
    public static ai_User GetUser(int iCustomerID){
        try {
            User roomUser = getRoomManager().getDatabase().userDao().getUserByCustomerId(iCustomerID);
            return roomUser != null ? roomUser.toSugarEntity() : null;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "GetUser", ex);
            return null;
        }
    }
    
    /**
     * Room-based User operations - New methods using Room database
     */
    public static User GetUserRoom(int iCustomerID) {
        try {
            return getRoomManager().getDatabase().userDao().getUserByCustomerId(iCustomerID);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "GetUserRoom", ex);
            return null;
        }
    }
    
    public static List<User> GetAllUsersRoom() {
        try {
            return getRoomManager().getDatabase().userDao().getAllUsers();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "GetAllUsersRoom", ex);
            return new ArrayList<>();
        }
    }
    public static String copyDBToSDCard(Context oContext) {
        try {
            InputStream myInput = new FileInputStream(oContext.getDatabasePath("if_data.db"));
            String sDBPath = CommonHelper.sFileRoot +"/db_" +CommonHelper.getDateTimeStrFromDate(new Date(), "YYYY_MM_dd_HH_mm") + ".db";
            java.io.File file = new java.io.File(sDBPath);
            if (!file.exists()){
                try {
                    file.createNewFile();
                } catch (IOException e) {
                    Log.i("FO","File creation failed for " + file);
                }
            }

            OutputStream myOutput = new FileOutputStream(sDBPath);

            byte[] buffer = new byte[1024];
            int length;
            while ((length = myInput.read(buffer))>0){
                myOutput.write(buffer, 0, length);
            }

            //Close the streams
            myOutput.flush();
            myOutput.close();
            myInput.close();
            Log.i("FO","copied");
            return sDBPath;

        } catch (Exception e) {
            Log.i("FO","exception="+e);
            return null;
        }


    }
    public static List<ai_User> GetAllUsers() {
        try {
            List<User> roomUsers = getRoomManager().getDatabase().userDao().getAllUsers();
            if (roomUsers == null || roomUsers.isEmpty()) return new ArrayList<>();
            
            List<ai_User> sugarUsers = new ArrayList<>();
            for (User user : roomUsers) {
                sugarUsers.add(user.toSugarEntity());
            }
            return sugarUsers;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "GetAllUsers", ex);
            return new ArrayList<>();
        }
    }

    public static List<ai_File> ReadyToUploadFile(){
        try {
            List<File> roomEntities = getRoomManager().getDatabase().fileDao().getFilesToUpload();
            List<ai_File> sugarEntities = new ArrayList<>();
            for (File roomEntity : roomEntities) {
                sugarEntities.add(new ai_File(roomEntity));
            }
            return sugarEntities;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "ReadyToUploadFile", ex);
            return new ArrayList<>();
        }
    }
    public static List<ai_File> ReadyToAttachPhoto()
    {
        try {
            List<File> roomEntities = getRoomManager().getDatabase().fileDao().getFilesToAttach();
            List<ai_File> sugarEntities = new ArrayList<>();
            for (File roomEntity : roomEntities) {
                sugarEntities.add(new ai_File(roomEntity));
            }
            return sugarEntities;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "ReadyToAttachPhoto", ex);
            return new ArrayList<>();
        }
    }
    
    // Sugar compatibility wrappers (deprecated - the main methods now return ai_File)
    public static List<ai_File> ReadyToUploadFileSugar(){
        return ReadyToUploadFile(); // Main method already returns ai_File
    }
    
    public static List<ai_File> ReadyToAttachPhotoSugar(){
        return ReadyToAttachPhoto(); // Main method already returns ai_File
    }
    public static File GetFileByServerID(int iFileID)
    {
        try {
            return getRoomManager().getDatabase().fileDao().getFileByServerId(iFileID);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "GetFileByServerID", ex);
            return null;
        }
    }
    public static ai_File GetPropertyPhoto(int iSAssetID)
    {
        try {
            File roomEntity = getRoomManager().getDatabase().fileDao().getPropertyPhoto(iSAssetID);
            return roomEntity != null ? new ai_File(roomEntity) : null;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "GetPropertyPhoto", ex);
            return null;
        }
    }
    public static List<ai_Photo> GetPhotoByInsItemID(int iInsItemID, Context oContext){
        try {
            List<Photo> roomPhotos = getRoomManager().getDatabase().photoDao().getPhotosByInsItemId(iInsItemID);
            List<ai_Photo> sugarPhotos = new ArrayList<>();
            for (Photo photo : roomPhotos) {
                sugarPhotos.add(photo.toSugarEntity());
            }
            return sugarPhotos;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetPhotoByInsItemID", ex, oContext);
            return new ArrayList<>();
        }
    }
    public static List<ai_Photo> GetPhotoByInsItemID(int iInsItemID) {
        try {
            List<Photo> roomPhotos = getRoomManager().getDatabase().photoDao().getPhotosByInsItemId(iInsItemID);
            List<ai_Photo> sugarPhotos = new ArrayList<>();
            for (Photo photo : roomPhotos) {
                sugarPhotos.add(photo.toSugarEntity());
            }
            return sugarPhotos;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
            return new ArrayList<>();
        }
    }
    public static List<ai_Video> GetInsItemVideos(long iInsItemID) {
        try {
            List<Video> roomVideos = getRoomManager().getDatabase().videoDao().getInsItemVideos(iInsItemID);
            List<ai_Video> sugarVideos = new ArrayList<>();
            for (Video video : roomVideos) {
                sugarVideos.add(video.toSugarEntity());
            }
            return sugarVideos;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetVideoByInsItemID", ex);
            return new ArrayList<>();
        }
    }

    public static List<ai_Video> GetNoticeVideos(long iNoticeID, long iInsItemID) {
        try {
            List<Video> roomVideos = getRoomManager().getDatabase().videoDao().getNoticeVideos(iNoticeID, iInsItemID);
            List<ai_Video> sugarVideos = new ArrayList<>();
            for (Video video : roomVideos) {
                sugarVideos.add(video.toSugarEntity());
            }
            return sugarVideos;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetVideoByInsItemID", ex);
            return new ArrayList<>();
        }
    }

    public static void DeleteVideoByID(long lVideoID) {
        try {
            Video oVideo = getRoomManager().getDatabase().videoDao().getVideoById(lVideoID);
            if (oVideo != null && oVideo.id > 0) {
                if (oVideo.bUploaded) {
                    CommonHelper.DeleteFile(oVideo.sThumb);
                    CommonHelper.DeleteFile(oVideo.sFile);
                }
                getRoomManager().getDatabase().videoDao().softDeleteById(lVideoID);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    public static void DeleteVideoByServerID(int iServerID) {
        try {
            List<Video> lsVideo = getRoomManager().getDatabase().videoDao().getVideosByServerId(iServerID);
            for (Video oVideo : lsVideo) {
                if (oVideo.bUploaded) {
                    CommonHelper.DeleteFile(oVideo.sThumb);
                    CommonHelper.DeleteFile(oVideo.sFile);
                }
                getRoomManager().getDatabase().videoDao().softDeleteById(oVideo.id);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    public static void DeleteNoticeVideoByIDTypeV(long lVideoID, long lNoticeID, Context oContext) {
        try{
            DeleteVideoByID(lVideoID);

            Notification oNotice = getRoomManager().getDatabase().notificationDao().getNotificationById(lNoticeID);
            if (oNotice != null) {
                oNotice.sVideoID = "";
                getRoomManager().getDatabase().notificationDao().updateNotification(oNotice);
            }
        }catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.DeleteVideoByID", ex, oContext);
        }

    }

    public static void DeleteVideoByIDTypeV(long lVideoID, long lInsItemID, Context oContext) {
        try{
            DeleteVideoByID(lVideoID);

            InsItem oInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById(lInsItemID);
            if (oInsItem != null) {
                oInsItem.sValueOne = "";
                getRoomManager().getDatabase().insItemDao().updateInsItem(oInsItem);
            }
        }catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.DeleteVideoByID", ex, oContext);
        }

    }
    public static void DeleteVideoByIDTypeN(long lVideoID, long lInsItemID, Context oContext, long iNotificationID) {
        try{
            Video oVideo = getRoomManager().getDatabase().videoDao().getVideoById(lVideoID);
            if (oVideo != null && oVideo.id > 0) {
                if (oVideo.bUploaded) {
                    CommonHelper.DeleteFile(oVideo.sThumb);
                    CommonHelper.DeleteFile(oVideo.sFile);
                }
                getRoomManager().getDatabase().videoDao().softDeleteById(lVideoID);
            }
            else{
                return;
            }
            Notification oNotification = getRoomManager().getDatabase().notificationDao().getNotificationById(iNotificationID);
            if (oNotification != null) {
                oNotification.sVideoID = "";
                getRoomManager().getDatabase().notificationDao().updateNotification(oNotification);
            }
        }catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.DeleteVideoByID", ex, oContext);
        }

    }
    public static void DeleteVideoByIDNotification(long lVideoID, long lNotificationID, Context oContext) {
        try {
            Video oVideo = getRoomManager().getDatabase().videoDao().getVideoById(lVideoID);
            if (oVideo != null && oVideo.id > 0) {
                if (oVideo.bUploaded) {
                    CommonHelper.DeleteFile(oVideo.sThumb);
                    CommonHelper.DeleteFile(oVideo.sFile);
                }
                getRoomManager().getDatabase().videoDao().softDeleteById(lVideoID);
            } else {
                return;
            }
            Notification oNotification = getRoomManager().getDatabase().notificationDao().getNotificationById(lNotificationID);
            if (oNotification != null) {
                oNotification.sVideoID = "";
                getRoomManager().getDatabase().notificationDao().updateNotification(oNotification);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.DeleteVideoByID", ex, oContext);
        }
    }
    //----deprecrted since video extended to Notification, can not delete by InsItem any more
    public static void DeleteVideoByInsItemID(int iInsItemID, Context oContext) {
        try{
            List<Video> lsVideo = getRoomManager().getDatabase().videoDao().getVideosByInsItemId(iInsItemID);
            for (Video oVideo : lsVideo) {
                CommonDB.DeleteVideoByID(oVideo.id);
            }
            InsItem oInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById((long) iInsItemID);
            if (oInsItem != null) {
                oInsItem.sValueOne = "";
                getRoomManager().getDatabase().insItemDao().updateInsItem(oInsItem);
            }
        }catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.DeleteVideoByInsItemID", ex, oContext);
        }

    }
    public static List<ai_Layout> GetDisplayedLayout(String sType, String sPTC, Context oContext){
        try {
            List<Layout> lsLoop = getRoomManager().getDatabase().layoutDao().getParentLayouts(sPTC);
            Iterator<Layout> oIterator = lsLoop.iterator();
            while (oIterator.hasNext()){
                Layout oLayout = oIterator.next();
                String sConfig = sType.equals("F") ? oLayout.sFConfig    : oLayout.sSConfig;
                if (CommonJson.GetJsonKeyValue("ADD", sConfig) != null){
                    oIterator.remove();
                }
            }
            // Convert to legacy entities
            List<ai_Layout> sugarEntities = new ArrayList<>();
            for (Layout roomEntity : lsLoop) {
                sugarEntities.add(new ai_Layout(roomEntity));
            }
            return sugarEntities;


          /*  for (ai_Layout oLayout : lsLoop){

            }
            String sConfig = sType.equals("F") ?


            if (sType.equals("F")) {
                return ai_Layout.find(ai_Layout.class, "S_PTC = ? and I_SP_LAYOUT_ID = 0 and S_F_CONFIG NOT LIKE '%ADD%'", sPTC);
            } else {
                return ai_Layout.find(ai_Layout.class, "S_PTC = ? and I_SP_LAYOUT_ID = 0 and S_S_CONFIG NOT LIKE '%ADD%'", sPTC);
            }*/
        }catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetDisplayedLayout", ex, oContext);
        }
        return null;

    }
    
    // Sugar compatibility wrapper for GetDisplayedLayout (deprecated - main method now returns ai_Layout)
    public static List<ai_Layout> GetDisplayedLayoutSugar(String sType, String sPTC, Context oContext) {
        return GetDisplayedLayout(sType, sPTC, oContext); // Main method already returns ai_Layout
    }
    // Temporarily return Sugar entities for backward compatibility during migration
    public static List<ai_InsItem> GetChildInsItem(long iPInsItemID, long iInsID) {
        try {
            List<InsItem> roomItems = filterHiddenInsItems(getRoomManager().getDatabase().insItemDao().getChildInsItems((int)iPInsItemID));
            List<ai_InsItem> sugarItems = new ArrayList<>();
            for (InsItem item : roomItems) {
                sugarItems.add(item.toSugarEntity());
            }
            return sugarItems;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "CommonDB.GetChildInsItem", ex);
        }
        return new ArrayList<>();
    }
    
    // Room version for new code that expects Room entities
    public static List<InsItem> GetChildInsItemRoom(long iPInsItemID, long iInsID) {
        try {
            return filterHiddenInsItems(getRoomManager().getDatabase().insItemDao().getChildInsItems((int)iPInsItemID));
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "CommonDB.GetChildInsItemRoom", ex);
        }
        return new ArrayList<>();
    }
    // Temporarily return Sugar entities for UploadService backward compatibility
    public static List<ai_InsItem> GetChildInsItem_UploadService(long iPInsItemID, long iInsID) {
        try {
            List<InsItem> roomItems = getRoomManager().getDatabase().insItemDao().getChildInsItems((int)iPInsItemID);
            List<ai_InsItem> sugarItems = new ArrayList<>();
            for (InsItem item : roomItems) {
                sugarItems.add(item.toSugarEntity());
            }
            return sugarItems;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "CommonDB.GetChildInsItem_UploadService", ex);
        }
        return new ArrayList<>();
    }
    public static List<InsItem> GetChildInsItem(long iInsID) {
        try {
            return filterHiddenInsItems(getRoomManager().getDatabase().insItemDao().getInsItemsByInspectionId((int)iInsID));
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "CommonDB.GetChildInsItem", ex);
        }
        return new ArrayList<>();
    }

    public static List<InsItem> filterHiddenInsItems(List<InsItem> items) {
        if (items == null) return null;
        ArrayList<InsItem> insItems = new ArrayList<>();
        for (InsItem oInsItem : items) {
            if (oInsItem.isHidden()) continue;
            insItems.add(oInsItem);
        }
        return insItems;
    }

    // Temporarily return Sugar entities for backward compatibility during migration
    public static List<ai_Layout> GetParentLayout(String sPTC){
        try {
            List<Layout> roomLayouts = getRoomManager().getDatabase().layoutDao().getParentLayouts(sPTC);
            List<ai_Layout> sugarLayouts = new ArrayList<>();
            for (Layout layout : roomLayouts) {
                sugarLayouts.add(layout.toSugarEntity());
            }
            return sugarLayouts;
        }catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "CommonDB.GetParentLayout", ex);
        }
        return new ArrayList<>();
    }
    // Temporarily return Sugar entities for backward compatibility during migration
    public static List<ai_Layout> GetChildLayout(int iLayoutID, Context oContext){
        try {
            List<Layout> roomLayouts = getRoomManager().getDatabase().layoutDao().getChildLayouts(iLayoutID);
            List<ai_Layout> sugarLayouts = new ArrayList<>();
            for (Layout layout : roomLayouts) {
                sugarLayouts.add(layout.toSugarEntity());
            }
            return sugarLayouts;
        }catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetChildLayout", ex, oContext);
        }
        return new ArrayList<>();


    }
    public static void SaveChildLayoutItemDefault(InsItem oInsItem, Context oContext, int iInspectionID, ai_InsType oInsType){
        //long lPInsItemID = oInsItem.getId();
       // int iPInsItemID = (int)lPInsItemID;
        try {
            List<ai_Layout> sugarChildLayouts = GetChildLayout(oInsItem.iSLayoutID, oContext);
            List<ai_Layout> bypassedLayouts = CommonDB_Inspection.bypassLayouts(sugarChildLayouts, oInsType);
            // Convert back to Room entities for database operations
            List<Layout> lsChildLayout = new ArrayList<>();
            for (ai_Layout sugar : bypassedLayouts) {
                lsChildLayout.add(new Layout(sugar));
            }
            AddChildLayout(oInsItem, oContext, iInspectionID, lsChildLayout);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.SaveChildLayoutItemDefault", ex, oContext);
        }

    }
    public static void AddChildLayout(InsItem oInsItem, Context oContext, int iInspectionID, List<Layout> lsChildLayout){
        // Sort the child layout by order ("S") - using sFieldThree field
        Collections.sort(lsChildLayout, (o1, o2) -> {
            int sort1 = CommonHelper.getInt(o1.sFieldThree);
            int sort2 = CommonHelper.getInt(o2.sFieldThree);
            return Integer.compare(sort1, sort2);
        });
        // Add the child layout to the database
        try {
            for (int j = 0; j < lsChildLayout.size(); j++) {
                Layout oChildLayout = lsChildLayout.get(j);
                String sValue1 = oChildLayout.sQType.equals("C") ?
                        oInsItem.sValueOne : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVOneConfig);
                String sValue2 = oChildLayout.sQType.equals("C") ?
                        oInsItem.sValueTwo : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVTwoConfig);
                String sValue3 = oChildLayout.sQType.equals("C") ?
                        oInsItem.sValueThree : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVThreeConfig);
                String sValue4 = oChildLayout.sQType.equals("C") ?
                        oInsItem.sValueFour : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVFourConfig);
                String sValue5 = oChildLayout.sQType.equals("C") ?
                        oInsItem.sValueFive : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVFiveConfig);
                String sValue6 = oChildLayout.sQType.equals("C") ?
                        oInsItem.sValueSix : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVSixConfig);
                InsItem oChildInsItem = new InsItem();
                oChildInsItem.iPInsItemID = oInsItem.id != null ? oInsItem.id.intValue() : 0;
                oChildInsItem.iInsID = iInspectionID;
                oChildInsItem.iSLayoutID = oChildLayout.iSLayoutID;
                oChildInsItem.sName = oChildLayout.sName;
                oChildInsItem.sValueOne = sValue1;
                oChildInsItem.sValueTwo = sValue2;
                oChildInsItem.sValueThree = sValue3;
                oChildInsItem.sValueFour = sValue4;
                oChildInsItem.sValueFive = sValue5;
                oChildInsItem.sValueSix = sValue6;
                oChildInsItem.sQType = oChildLayout.sQType;
                oChildInsItem.sConfigOne = oChildLayout.sFVOneConfig;
                oChildInsItem.sConfigTwo = oChildLayout.sFVTwoConfig;
                oChildInsItem.sConfigThree = oChildLayout.sFVThreeConfig;
                oChildInsItem.sConfigFour = oChildLayout.sFVFourConfig;
                oChildInsItem.sConfigFive = oChildLayout.sFVFiveConfig;
                oChildInsItem.sConfigSix = oChildLayout.sFVSixConfig;
                oChildInsItem.sConfig = oChildLayout.sFConfig;
                oChildInsItem.bDeleted = false;
                oChildInsItem.iSort = j + 1;
                oChildInsItem.iSAssetLayoutID = 0;
                oChildInsItem.sCustomOne = oChildLayout.sFieldOne;
                oChildInsItem.sCustomTwo = "";
                getRoomManager().getDatabase().insItemDao().insertInsItem(oChildInsItem);

            }
        }catch (Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.AddChildLayout", ex, oContext);
        }
    }
    
    // Overloaded method to accept ai_InsItem (legacy entity)
    public static void AddChildLayout(ai_InsItem oInsItem, Context oContext, int iInspectionID, List<ai_Layout> lsChildLayout){
        try {
            // Convert legacy entities to Room entities
            InsItem roomInsItem = new InsItem(oInsItem);
            List<Layout> roomLayouts = new ArrayList<>();
            for (ai_Layout sugar : lsChildLayout) {
                roomLayouts.add(new Layout(sugar));
            }
            // Call the main method
            AddChildLayout(roomInsItem, oContext, iInspectionID, roomLayouts);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.AddChildLayout", ex, oContext);
        }
    }
    public static boolean DeletePhotoByInsItemID(int iInsItemID, Context oContext){
        try {
            List<Photo> lsPhoto = getRoomManager().getDatabase().photoDao().getPhotosByInsItemId(iInsItemID);
            for (Photo oPhoto : lsPhoto) {
                getRoomManager().getDatabase().photoDao().softDeleteById(oPhoto.id);
                if (oPhoto.bUploaded){
                    CommonHelper.DeleteFile(oPhoto.sThumb);
                    CommonHelper.DeleteFile(oPhoto.sFile);
                }
            }
            InsItem oInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById((long) iInsItemID);
            if (oInsItem != null) {
                oInsItem.sValueOne = "";
                getRoomManager().getDatabase().insItemDao().updateInsItem(oInsItem);
            }

            return true;
        }catch (Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.DeletePhotoByInsItemID", ex, oContext);
        }

        return false;
    }

    public static void DeletePhotoByPhotoID(int iPhotoID) {
        DeletePhotoByPhotoID(iPhotoID, false);
    }

    public static void DeletePhotoByServerID(int iSPhotoID) {
        try {
            Photo oPhoto = getRoomManager().getDatabase().photoDao().getPhotoByServerId(iSPhotoID);
            if (oPhoto != null) {
                DeletePhotoByPhotoID(oPhoto.id, false);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "DeletePhotoByServerID", ex);
        }
    }


    public static void DeletePhotoByPhotoID(long lPhotoID, boolean bForceDelete) {
        try {
            Photo oPhoto = getRoomManager().getDatabase().photoDao().getPhotoById(lPhotoID);
            if (oPhoto != null) {
                if (oPhoto.bUploaded || bForceDelete) {
                    CommonHelper.DeleteFile(oPhoto.sThumb);
                    CommonHelper.DeleteFile(oPhoto.sFile);
                }
                getRoomManager().getDatabase().photoDao().softDeleteById(lPhotoID);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    public static void DeletePhotoByPhotoID(int iPhotoID, int iInsItemID, int iPosition, Context oContext){
        try {
           // int cc = 10/0;
            DeletePhotoByPhotoID(iPhotoID);
            if (iPosition > 0 && iPosition < 7) {
                InsItem oInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById((long) iInsItemID);
                if (oInsItem != null) {
                    String sValue = getInsItemValue(iPosition, oInsItem);
                    sValue = CommonHelper.sRemovePhoto(sValue, "" + iPhotoID);
                    setInsItemValue(iPosition, oInsItem, sValue);
                    getRoomManager().getDatabase().insItemDao().updateInsItem(oInsItem);
                }
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.DeletePhotoByPhotoID", ex, oContext);
        }
    }

    public static long InsertVideo(
            long iInsItemID, long iNoticeID, String sThumb, String sFilePath, Location location) {
        Video oTempVideo = new Video();
        oTempVideo.sThumb = sThumb;
        oTempVideo.sFile = sFilePath;
        oTempVideo.sSThumb = "";
        oTempVideo.sSFile = "";
        oTempVideo.iInsItemID = iInsItemID;
        InsItem oInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById(iInsItemID);
        oTempVideo.iInsID = oInsItem != null ? oInsItem.iInsID : 0;

        oTempVideo.dtDateTime = CommonHelper.sDateToString(new Date());
        oTempVideo.bUploaded = false;
        oTempVideo.bDeleted = false;
        oTempVideo.bProcessed = false;
        oTempVideo.bGetURL = false;
        oTempVideo.iSVideoID = 0;
        oTempVideo.iSize = CommonHelper.GetFileLength(sFilePath);

        // Save notice id to sFieldThree
        oTempVideo.sFieldThree = String.valueOf(iNoticeID);

        if (location != null) { // {''_sGPS','xxxxx,xxxxx'}
            oTempVideo.sFieldOne = CommonJson.AddJsonKeyValue("{}", "_sGPS",
                    String.format("%s,%s", location.getLatitude(), location.getLongitude()));
        } else {
            oTempVideo.sFieldOne = "";
        }

        return getRoomManager().getDatabase().videoDao().insertVideo(oTempVideo);
    }

    public static long InsertPhoto(Context oContext, int iInsItemID, String sThumb, String sFilePath, Location location) {
        Photo oTempPhoto = new Photo();
        oTempPhoto.sThumb = sThumb;
        oTempPhoto.sFile = sFilePath;
        oTempPhoto.iInsItemID = iInsItemID;
        InsItem oInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById((long) iInsItemID);
        if (oInsItem != null) {
            oTempPhoto.iInsID = oInsItem.iInsID;
        } else {
            oTempPhoto.iInsID = 0;
        }

        if (location != null) { // {''_sGPS','xxxxx,xxxxx'}
            oTempPhoto.sLat = CommonJson.AddJsonKeyValue(
                    "{}",
                    "_sGPS",
                    String.format("%s,%s", location.getLatitude(), location.getLongitude())
                );
        } else {
            oTempPhoto.sLat = "";
        }
        oTempPhoto.sLong = "";
        oTempPhoto.dtDateTime = CommonHelper.sDateToString(new Date());
        oTempPhoto.sComments = "";
        oTempPhoto.bUploaded = false;
        oTempPhoto.bDeleted = false;
        oTempPhoto.iSPhotoID = 0;
        oTempPhoto.iSize = CommonHelper.GetFileLength(sFilePath);
        return getRoomManager().getDatabase().photoDao().insertPhoto(oTempPhoto);
    }

    public static long InsertInsItemPhoto(
            Context oContext, int iInsItemID, String sThumb, String sFilePath, int iPosition, Location location) {
        try {
            long photoID = InsertPhoto(oContext, iInsItemID, sThumb, sFilePath, location);
            updateInsItemValue(iInsItemID, iPosition, photoID);
            return photoID;

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.InsertPhoto", ex, oContext);
        }

        return 0;
    }

    public static void updateInsItemValue(int iInsItemID, int iPosition, long photoID) {
        InsItem oInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById((long) iInsItemID);
        if (oInsItem != null) {
            String sValue = getInsItemValue(iPosition, oInsItem);
            sValue = CommonHelper.sAttachPhoto(sValue, "" + photoID);
            setInsItemValue(iPosition, oInsItem, sValue);
            getRoomManager().getDatabase().insItemDao().updateInsItem(oInsItem);
        }
    }
    
    /**
     * Helper methods to handle InsItem value operations
     */
    private static String getInsItemValue(int iPosition, InsItem oInsItem) {
        switch (iPosition) {
            case 1: return oInsItem.sValueOne != null ? oInsItem.sValueOne : "";
            case 2: return oInsItem.sValueTwo != null ? oInsItem.sValueTwo : "";
            case 3: return oInsItem.sValueThree != null ? oInsItem.sValueThree : "";
            case 4: return oInsItem.sValueFour != null ? oInsItem.sValueFour : "";
            case 5: return oInsItem.sValueFive != null ? oInsItem.sValueFive : "";
            case 6: return oInsItem.sValueSix != null ? oInsItem.sValueSix : "";
            default: return "";
        }
    }
    
    private static void setInsItemValue(int iPosition, InsItem oInsItem, String sValue) {
        switch (iPosition) {
            case 1: oInsItem.sValueOne = sValue; break;
            case 2: oInsItem.sValueTwo = sValue; break;
            case 3: oInsItem.sValueThree = sValue; break;
            case 4: oInsItem.sValueFour = sValue; break;
            case 5: oInsItem.sValueFive = sValue; break;
            case 6: oInsItem.sValueSix = sValue; break;
            default: break;
        }
    }

    public static long InsertNotificationPhoto(
            Context oContext, int iInsItemID, String sThumb, String sFilePath, int iNotificationID, Location location) {
        try {
            long photoID = InsertPhoto(oContext, iInsItemID, sThumb, sFilePath, location);

            Notification oNotification = getRoomManager().getDatabase().notificationDao().getNotificationById((long) iNotificationID);
            if (oNotification != null) {
                oNotification.sPhotoURL = CommonHelper.sAttachPhoto(oNotification.sPhotoURL, "" + photoID);
                getRoomManager().getDatabase().notificationDao().updateNotification(oNotification);
            }

            return photoID;

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.InsertPhoto", ex, oContext);
        }

        return 0;
    }

    public static void MarkFileAssetPhoto(long iClientFileID, int iSAssetID){
        try {
            List<File> lsFile = getRoomManager().getDatabase().fileDao().getFilesByAssetId(iSAssetID);
            for (File oFile : lsFile){
                if (oFile.id == iClientFileID){
                    oFile.sCustomOne = CommonJson.AddJsonKeyValue(oFile.sCustomOne, "ASTPTO", "1");
                    getRoomManager().getDatabase().fileDao().updateFile(oFile);
                }
                else{
                    if (CommonJson.GetJsonKeyValue("ASTPTO", oFile.sCustomOne) != null) {
                        oFile.sCustomOne = CommonJson.RemoveJsonKey("ASTPTO", oFile.sCustomOne);
                        getRoomManager().getDatabase().fileDao().updateFile(oFile);
                    }
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "MarkFileAssetPhoto", ex);
        }
    }
    public static void MarkFileAttachCMD(long iClientFileID){
        try {
            File oFile = getRoomManager().getDatabase().fileDao().getFileById(iClientFileID);
            if (oFile != null) {
                oFile.sCustomOne = CommonJson.AddJsonKeyValue("", "AttachCMD", "1");
                getRoomManager().getDatabase().fileDao().updateFile(oFile);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "MarkFileAttachCMD", ex);
        }
    }
    public static long InsertFile(Context oContext, int iSAssetID, String sFilePath){
        try{
            File oFile = new File();
            oFile.bUploaded = false;
            oFile.iFileID = 0;
            oFile.iSObjectID = iSAssetID;
            oFile.iSize = CommonHelper.GetFileLength(sFilePath);
            oFile.sFile = sFilePath;
            oFile.dtDateTime = CommonHelper.sDateToString(new Date());
            oFile.bDeleted = false;

            return getRoomManager().getDatabase().fileDao().insertFile(oFile);

        }catch (Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.InsertFile", ex, oContext);
        }
        return 0;
    }

    public static void DeleteFileByID(long lFileID, boolean bForceDelete) {
        try {
            File oFile = getRoomManager().getDatabase().fileDao().getFileById(lFileID);
            if (oFile != null) {
                if (oFile.bUploaded || bForceDelete) {
                    CommonHelper.DeleteFile(oFile.sFile);
                }
                getRoomManager().getDatabase().fileDao().softDeleteById(lFileID);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    public static List<ai_QuickPhrase> SearchPhrase(String sFilter, Context oContext){
        try {
            String searchPattern = "%" + CommonDB.EscapeString(sFilter) + "%";
            List<QuickPhrase> roomEntities = getRoomManager().getDatabase().quickPhraseDao().searchPhrasesByComments(searchPattern);
            List<ai_QuickPhrase> sugarEntities = new ArrayList<>();
            for (QuickPhrase roomEntity : roomEntities) {
                sugarEntities.add(new ai_QuickPhrase(roomEntity));
            }
            return sugarEntities;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.SearchQuickPhrase", ex, oContext);
        }
        return new ArrayList<>();
    }

    // Sugar compatibility wrapper for SearchPhrase
    public static List<ai_QuickPhrase> SearchPhraseSugar(String sFilter, Context oContext){
        try {
            // SearchPhrase already returns ai_QuickPhrase list, so just call it directly
            return SearchPhrase(sFilter, oContext);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.SearchPhraseSugar", ex, oContext);
        }
        return new ArrayList<>();
    }

    public static String EscapeString(String sValue){
        return sValue.replaceAll("'","''");
    }
    // Temporarily return Sugar entities for backward compatibility during migration
    public static List<ai_AssetLayout> GetAssetLayouts(int iSAssetID, String sPTC, Context oContext){
        try {
            // Note: sPTC parameter is ignored in Room implementation - filtering by PTC needs to be added if needed
            List<AssetLayout> roomAssetLayouts = getRoomManager().getDatabase().assetLayoutDao().getAssetLayoutsByAssetId(iSAssetID);
            List<ai_AssetLayout> sugarAssetLayouts = new ArrayList<>();
            for (AssetLayout assetLayout : roomAssetLayouts) {
                sugarAssetLayouts.add(assetLayout.toSugarEntity());
            }
            return sugarAssetLayouts;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.Get.GetAssetLayouts", ex, oContext);
        }
        return new ArrayList<>();
    }
    public static void InsertLog(Context oContext, String _sType, String _sMessage){
      /*  try {
            ai_Log oLog = new ai_Log(oContext);
            oLog.sType = _sType;
            oLog.sMessage = _sMessage;
            oLog.bDeleted = false;
            oLog.bUploaded = false;
            oLog.dtDateTime = CommonHelper.sDateToString(new Date());
            oLog.save();
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.InsertLog", ex, oContext);
        }*/

    }
    public static int ValidateChildExist(Context _oContext, int iSPAssetID){
        try {
            List<Assets> childAssets = getRoomManager().getDatabase().assetsDao().getChildAssets(iSPAssetID);
            return childAssets != null ? childAssets.size() : 0;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "ValidateChildExist", ex);
            return 0;
        }
    }
    public static int ValidateAssetExist_ExcludeSelf(String sAddress1, String sAddress2, int iSAssetID){
        try {
            String filter = "%" + (sAddress1 != null ? sAddress1 : "") + "%";
            List<Assets> lsAssets = getRoomManager().getDatabase().assetsDao().getAssetsByFilter(filter);
            // Exclude self by filtering out the asset with the given ID
            int count = 0;
            if (lsAssets != null) {
                for (Assets asset : lsAssets) {
                    if (asset.iSAssetID != iSAssetID) {
                        count++;
                    }
                }
            }
            return count;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("CommonDB", "ValidateAssetExist_ExcludeSelf", ex);
        }
        return 0;
    }
    public static String GetInstruction(ai_InsItem oInsItem, boolean bFull){
        try {
            if (oInsItem.sConfig != null && oInsItem.sConfig.contains("INST")){
                String sPromptText = CommonJson.GetJsonKeyValue("INST", oInsItem.sConfig);
                if (sPromptText != null && !sPromptText.isEmpty()){
                    return sPromptText;
                }
            }

            Layout oLayout = getRoomManager().getDatabase().layoutDao().getLayoutByServerId(oInsItem.iSLayoutID);
            if (oLayout != null) {
                if (bFull)
                    return CommonJson.GetJsonKeyValue("INST", oLayout.sFConfig);
                return CommonJson.GetJsonKeyValue("INST", oLayout.sSConfig);
            }
        }catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "GetInstruction", ex);
        }
        return "";
    }

    public static boolean swapElements(ArrayList<ai_InsItem> arrayList, int indexOne, int indexTwo) {
        try {
            ai_InsItem temp = arrayList.get(indexOne);
            arrayList.set(indexOne, arrayList.get(indexTwo));
            arrayList.set(indexTwo, temp);
            
            // Update sort order in database using Room DAO
            List<InsItem> insItemsToUpdate = new ArrayList<>();
            for (int i = 0; i < arrayList.size(); i++) {
                ai_InsItem oTemp = arrayList.get(i);
                oTemp.iSort = i + 1;
                
                // Convert ai_InsItem to Room InsItem entity for database update
                InsItem roomInsItem = new InsItem(oTemp);
                insItemsToUpdate.add(roomInsItem);
            }
            
            // Batch update in Room
            if (!insItemsToUpdate.isEmpty()) {
                getRoomManager().getDatabase().insItemDao().updateInsItems(insItemsToUpdate);
            }
            
            return true;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "swapElements", ex);
            return false;
        }
    }

    public static void migrationFilePaths() {
        List<Photo> photos = getRoomManager().getDatabase().photoDao().getAllPhotos();
        String legacyPath = FileUtils.getExternalStorageDirectory(),
            appPath = FileUtils.getExternalStoragePrivateDirectory();
        for(Photo photo: photos) {
            photo.sFile = photo.sFile.replaceFirst(legacyPath, appPath);
            photo.sThumb = photo.sThumb.replaceFirst(legacyPath, appPath);
        }
        getRoomManager().getDatabase().photoDao().updatePhotos(photos);

        List<Video> videos = getRoomManager().getDatabase().videoDao().getAllVideos();
        for (Video video: videos) {
            video.sFile = video.sFile.replaceFirst(legacyPath, appPath);
            video.sThumb = video.sThumb.replaceFirst(legacyPath, appPath);
        }
        getRoomManager().getDatabase().videoDao().updateVideos(videos);
    }

    public static ai_Video GetNoticeMainVideo(long iNoticeID, long iInsItemID) {
        List<ai_Video> videos = GetNoticeVideos(iNoticeID, iInsItemID);
        Notification notification = getRoomManager().getDatabase().notificationDao().getNotificationById(iNoticeID);
        if (videos == null || videos.isEmpty() || notification == null) {
            return null;
        }
        for (ai_Video video: videos) {
            if (!StringUtils.isEmpty(notification.sVideoID)
                    && video.id.equals(Long.parseLong(notification.sVideoID))) {
                return video;
            }
        }
        return videos.get(0);
    }

    public static ai_Video GetInsItemMainVideo(long iInsItemID) {
        List<ai_Video> videos = GetInsItemVideos(iInsItemID);
        InsItem oInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById(iInsItemID);
        if (videos == null || videos.isEmpty() || oInsItem == null) {
            return null;
        }

        for (ai_Video video: videos) {
            if (!StringUtils.isEmpty(oInsItem.sValueOne) &&
                    video.id.equals(Long.parseLong(oInsItem.sValueOne))) {
                return video;
            }
        }
        return videos.get(0);
    }

    // Sugar compatibility wrappers for video methods
    public static List<ai_Video> GetInsItemVideosSugar(long iInsItemID) {
        try {
            // GetInsItemVideos already returns ai_Video list, so just call it directly
            return GetInsItemVideos(iInsItemID);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetInsItemVideosSugar", ex);
        }
        return new ArrayList<>();
    }

    public static List<ai_Video> GetNoticeVideosSugar(long iNoticeID, long iInsItemID) {
        try {
            // GetNoticeVideos already returns ai_Video list, so just call it directly
            return GetNoticeVideos(iNoticeID, iInsItemID);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetNoticeVideosSugar", ex);
        }
        return new ArrayList<>();
    }

    public static ai_Video GetInsItemMainVideoSugar(long iInsItemID) {
        try {
            // GetInsItemMainVideo already returns ai_Video, so just call it directly
            return GetInsItemMainVideo(iInsItemID);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetInsItemMainVideoSugar", ex);
        }
        return null;
    }

    public static ai_Video GetNoticeMainVideoSugar(long iNoticeID, long iInsItemID) {
        try {
            // GetNoticeMainVideo already returns ai_Video, so just call it directly
            return GetNoticeMainVideo(iNoticeID, iInsItemID);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetNoticeMainVideoSugar", ex);
        }
        return null;
    }

    public static List<Layout> getAllLayouts() {
        return getRoomManager().getDatabase().layoutDao().getAllLayouts();
    }

    public static List<Layout> searchChildLayouts(int iInsID) {
        // if searchText is empty, return all layouts as default
        Inspection oInspection = getRoomManager().getDatabase().inspectionDao().getInspectionById(iInsID);
        if (oInspection == null || oInspection.iSInsTypeID == 0)
            return getAllLayouts();

        InsType oInsType = getRoomManager().getDatabase().insTypeDao().getInsTypeByServerId(oInspection.iSInsTypeID);
        if (oInsType == null || StringUtils.isEmpty(oInsType.sPTC))
            return getAllLayouts();

        return getRoomManager().getDatabase().layoutDao().searchChildLayoutsByPTC(oInsType.sPTC);
    }

    public static Layout getLayoutBySLayoutID(int iSLayoutID) {
        return getRoomManager().getDatabase().layoutDao().getLayoutByServerId(iSLayoutID);
    }

    public static List<NoticeCategory> getNoticeCategories(int iPNoteCategoryID) {
        return getRoomManager().getDatabase().noticeCategoryDao().getNoticeCategoriesByParentId(iPNoteCategoryID);
    }

    public static List<ai_CustomInfo>getAllCustomInfos(Context context) {
        ArrayList<ai_CustomInfo> customInfos = new ArrayList<>();
        try {
            String sCustomInfo = CommonHelper.GetPreferenceString(context, Constants.Keys.sAssetAttributes2);
            JSONArray arrCustomInfo = new JSONArray(sCustomInfo);
            for (int i = 0; i < arrCustomInfo.length(); i++) {
                JSONObject json = arrCustomInfo.getJSONObject(i);
                ai_CustomInfo customInfo = new ai_CustomInfo(json);
                customInfos.add(customInfo);
            }
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }
        return customInfos;
    }

    // =========================== BACKWARD COMPATIBILITY METHODS ===========================
    
    // Sugar ORM compatibility methods for Java files that haven't been migrated yet
    
    public static ai_User GetUserSugar(int iCustomerID) {
        User roomUser = GetUserRoom(iCustomerID);
        return roomUser != null ? roomUser.toSugarEntity() : null;
    }
    
    public static List<ai_User> GetAllUsersSugar() {
        List<User> roomUsers = GetAllUsersRoom();
        if (roomUsers == null || roomUsers.isEmpty()) return new ArrayList<>();
        
        List<ai_User> sugarUsers = new ArrayList<>();
        for (User user : roomUsers) {
            sugarUsers.add(user.toSugarEntity());
        }
        return sugarUsers;
    }
    
    public static List<ai_InsItem> GetChildInsItemSugar(long iInsItemID, long iInsID) {
        List<InsItem> roomItems = GetChildInsItemRoom(iInsItemID, iInsID);
        if (roomItems == null || roomItems.isEmpty()) return new ArrayList<>();
        
        List<ai_InsItem> sugarItems = new ArrayList<>();
        for (InsItem item : roomItems) {
            sugarItems.add(item.toSugarEntity());
        }
        return sugarItems;
    }
    
    public static List<ai_Photo> GetPhotoByInsItemIDSugar(int iInsItemID) {
        // GetPhotoByInsItemID already returns ai_Photo list, so just call it directly
        return GetPhotoByInsItemID(iInsItemID);
    }
    
    // Add more Sugar compatibility methods for the compilation errors
    public static List<ai_Layout> GetParentLayoutSugar(String ptc) {
        try {
            List<Layout> roomLayouts = getRoomManager().getDatabase().layoutDao().getParentLayouts(ptc);
            if (roomLayouts == null || roomLayouts.isEmpty()) return new ArrayList<>();
            
            List<ai_Layout> sugarLayouts = new ArrayList<>();
            for (Layout layout : roomLayouts) {
                sugarLayouts.add(layout.toSugarEntity());
            }
            return sugarLayouts;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "CommonDB.GetParentLayoutSugar", ex);
        }
        return new ArrayList<>();
    }
    
    public static List<ai_Layout> GetChildLayoutSugar(int parentLayoutId, Context context) {
        try {
            List<Layout> roomLayouts = getRoomManager().getDatabase().layoutDao().getChildLayouts(parentLayoutId);
            if (roomLayouts == null || roomLayouts.isEmpty()) return new ArrayList<>();
            
            List<ai_Layout> sugarLayouts = new ArrayList<>();
            for (Layout layout : roomLayouts) {
                sugarLayouts.add(layout.toSugarEntity());
            }
            return sugarLayouts;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetChildLayoutSugar", ex, context);
        }
        return new ArrayList<>();
    }
    
    public static List<ai_AssetLayout> GetAssetLayoutsSugar(int assetId, String ptc, Context context) {
        try {
            List<AssetLayout> roomAssetLayouts = getRoomManager().getDatabase().assetLayoutDao().getAssetLayoutsByAssetId(assetId);
            if (roomAssetLayouts == null || roomAssetLayouts.isEmpty()) return new ArrayList<>();
            
            List<ai_AssetLayout> sugarAssetLayouts = new ArrayList<>();
            for (AssetLayout assetLayout : roomAssetLayouts) {
                sugarAssetLayouts.add(assetLayout.toSugarEntity());
            }
            return sugarAssetLayouts;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.GetAssetLayoutsSugar", ex, context);
        }
        return new ArrayList<>();
    }

    /**
     * Sugar ORM compatibility methods for Room database
     * These methods provide the same interface as SugarRecord.find() but use Room database
     */
    
    // Compatibility method for Contact queries
    public static List<ai_Contact> findContacts(String whereClause, String... args) {
        try {
            List<Contact> roomContacts = new ArrayList<>();
            
            if (whereClause.equals("I_S_Asset_ID = ? AND B_DELETED = 0")) {
                int assetId = Integer.parseInt(args[0]);
                roomContacts = getRoomManager().getDatabase().contactDao().getContactsByServerAssetId(assetId);
            }
            // Add more whereClause patterns as needed
            
            List<ai_Contact> sugarContacts = new ArrayList<>();
            for (Contact contact : roomContacts) {
                sugarContacts.add(contact.toSugarEntity());
            }
            return sugarContacts;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findContacts", ex);
            return new ArrayList<>();
        }
    }
    
    // Compatibility method for Assets queries
    public static List<ai_Assets> findAssets(String whereClause, String... args) {
        try {
            List<Assets> roomAssets = new ArrayList<>();
            
            if (whereClause.equals("I_S_Asset_ID = ?")) {
                int assetId = Integer.parseInt(args[0]);
                Assets asset = getRoomManager().getDatabase().assetsDao().getAssetByServerId(assetId);
                if (asset != null) {
                    roomAssets.add(asset);
                }
            }
            // Add more whereClause patterns as needed
            
            List<ai_Assets> sugarAssets = new ArrayList<>();
            for (Assets asset : roomAssets) {
                sugarAssets.add(asset.toSugarEntity());
            }
            return sugarAssets;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findAssets", ex);
            return new ArrayList<>();
        }
    }
    
    // Compatibility method for findById queries
    public static ai_InsItem findInsItemById(long id) {
        try {
            InsItem roomInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById(id);
            if (roomInsItem != null) {
                return roomInsItem.toSugarEntity();
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsItemById", ex);
        }
        return null;
    }
    
    // Compatibility method for Inspection findById
    public static ai_Inspection findInspectionById(long id) {
        try {
            Inspection roomInspection = getRoomManager().getDatabase().inspectionDao().getInspectionById(id);
            if (roomInspection != null) {
                return roomInspection.toSugarEntity();
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInspectionById", ex);
        }
        return null;
    }
    
    // Compatibility method for Inspection save
    public static void saveInspection(ai_Inspection inspection) {
        try {
            Inspection roomInspection = new Inspection(inspection);
            getRoomManager().getDatabase().inspectionDao().insertInspection(roomInspection);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveInspection", ex);
        }
    }
    
    // Compatibility method for Inspection find with where clause
    public static List<ai_Inspection> findInspections(String whereClause, String... args) {
        try {
            List<Inspection> roomInspections = new ArrayList<>();
            
            if (whereClause.equals("I_S_INS_ID=?")) {
                int serverInsId = Integer.parseInt(args[0]);
                Inspection inspection = getRoomManager().getDatabase().inspectionDao().getInspectionByServerId(serverInsId);
                if (inspection != null) roomInspections.add(inspection);
            }
            // Add more conditions as needed
            
            List<ai_Inspection> sugarInspections = new ArrayList<>();
            for (Inspection room : roomInspections) {
                sugarInspections.add(room.toSugarEntity());
            }
            return sugarInspections;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInspections", ex);
            return new ArrayList<>();
        }
    }

    public static ai_Video GetVideoByIdSugar(long videoId) {
        try {
            Video roomVideo = getRoomManager().getDatabase().videoDao().getVideoById(videoId);
            return roomVideo != null ? roomVideo.toSugarEntity() : null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "GetVideoByIdSugar", ex);
            return null;
        }
    }

    public static ai_Photo GetPhotoByIdSugar(long photoId) {
        try {
            Photo roomPhoto = getRoomManager().getDatabase().photoDao().getPhotoById(photoId);
            return roomPhoto != null ? roomPhoto.toSugarEntity() : null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "GetPhotoByIdSugar", ex);
            return null;
        }
    }

    public static ai_InsItem GetInsItemByIdSugar(long insItemId) {
        try {
            InsItem roomInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById(insItemId);
            return roomInsItem != null ? roomInsItem.toSugarEntity() : null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "GetInsItemByIdSugar", ex);
            return null;
        }
    }

    public static void SaveInsItemSugar(ai_InsItem sugarInsItem) {
        try {
            InsItem roomInsItem = new InsItem(sugarInsItem);
            getRoomManager().getDatabase().insItemDao().updateInsItem(roomInsItem);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "SaveInsItemSugar", ex);
        }
    }

    public static void SaveProjectsSugar(java.util.List<ai_Project> sugarProjects) {
        try {
            java.util.List<Project> roomProjects = new ArrayList<>();
            for (ai_Project sugar : sugarProjects) {
                roomProjects.add(new Project(sugar));
            }
            getRoomManager().getDatabase().projectDao().insertProjects(roomProjects);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "SaveProjectsSugar", ex);
        }
    }

    public static void SaveProjectInspectionsSugar(java.util.List<ai_ProjectInspection> sugarProjectInspections) {
        try {
            java.util.List<ProjectInspection> roomProjectInspections = new ArrayList<>();
            for (ai_ProjectInspection sugar : sugarProjectInspections) {
                roomProjectInspections.add(new ProjectInspection(sugar));
            }
            getRoomManager().getDatabase().projectInspectionDao().insertProjectInspections(roomProjectInspections);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "SaveProjectInspectionsSugar", ex);
        }
    }

    // Additional compatibility methods for missing Sugar ORM calls
    public static List<ai_ProjectInspection> findProjectInspections(String whereClause, String... args) {
        try {
            // For now, return empty list - needs specific implementation based on query
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findProjectInspections", ex);
            return new ArrayList<>();
        }
    }
    
    public static void saveProjectInspection(ai_ProjectInspection projectInspection) {
        try {
            ProjectInspection roomEntity = new ProjectInspection(projectInspection);
            getRoomManager().getDatabase().projectInspectionDao().insertProjectInspection(roomEntity);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveProjectInspection", ex);
        }
    }
    
    public static List<ai_Notification> findNotifications(String whereClause, String... args) {
        try {
            // For now, return empty list - needs specific implementation based on query
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findNotifications", ex);
            return new ArrayList<>();
        }
    }
    
    public static List<ai_InsType> findInsTypes(String whereClause, String... args) {
        try {
            // For now, return empty list - needs specific implementation based on query
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsTypes", ex);
            return new ArrayList<>();
        }
    }
    
    public static List<ai_CheckList> findCheckLists(String whereClause, String... args) {
        try {
            // For now, return empty list - needs specific implementation based on query
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findCheckLists", ex);
            return new ArrayList<>();
        }
    }
    
    public static void saveVideo(ai_Video video) {
        try {
            Video roomEntity = new Video(video);
            getRoomManager().getDatabase().videoDao().updateVideo(roomEntity);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveVideo", ex);
        }
    }
    
    public static void savePhoto(ai_Photo photo) {
        try {
            Photo roomEntity = new Photo(photo);
            getRoomManager().getDatabase().photoDao().updatePhoto(roomEntity);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "savePhoto", ex);
        }
    }
    
    public static void saveNotification(ai_Notification notification) {
        try {
            Notification roomEntity = new Notification(notification);
            if (notification.getId() != null && notification.getId() > 0) {
                getRoomManager().getDatabase().notificationDao().updateNotification(roomEntity);
            } else {
                long newId = getRoomManager().getDatabase().notificationDao().insertNotification(roomEntity);
                notification.setId(newId);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveNotification", ex);
        }
    }
    
    public static void saveFile(ai_File file) {
        try {
            File roomEntity = new File(file);
            if (file.getId() != null && file.getId() > 0) {
                getRoomManager().getDatabase().fileDao().updateFile(roomEntity);
            } else {
                long newId = getRoomManager().getDatabase().fileDao().insertFile(roomEntity);
                file.setId(newId);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveFile", ex);
        }
    }
    
    public static void saveFloorPlan(ai_FloorPlan floorPlan) {
        try {
            FloorPlan roomEntity = new FloorPlan(floorPlan);
            if (floorPlan.getId() != null && floorPlan.getId() > 0) {
                getRoomManager().getDatabase().floorPlanDao().updateFloorPlan(roomEntity);
            } else {
                long newId = getRoomManager().getDatabase().floorPlanDao().insertFloorPlan(roomEntity);
                floorPlan.setId(newId);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveFloorPlan", ex);
        }
    }
    
    // Additional delete methods for compatibility
    public static void deleteContactsByAssetId(int assetId) {
        // Simplified implementation for compatibility
    }
    
    public static void deleteAssetLayoutsByAssetId(int assetId) {
        // Simplified implementation for compatibility
    }
    
    public static void deleteInsAlertsByAssetId(int assetId) {
        // Simplified implementation for compatibility
    }
    
    public static void deleteScheduleByScheduleId(int scheduleId) {
        // Simplified implementation for compatibility
    }
    
    public static void deleteInsTypeByInsTypeId(int insTypeId) {
        // Simplified implementation for compatibility
    }
    
    public static void deleteQuickPhraseByPhraseId(int phraseId) {
        // Simplified implementation for compatibility
    }
    
    public static void deleteCheckListByCheckListId(int checkListId) {
        // Simplified implementation for compatibility
    }
    
    public static void saveNoticeCategory(ai_NoticeCategory noticeCategory) {
        // Simplified implementation for compatibility
    }
    
    public static void saveUser(ai_User user) {
        // Simplified implementation for compatibility
    }
    
    public static void saveLayout(ai_Layout layout) {
        // Simplified implementation for compatibility
    }
    
    public static void saveInsType(ai_InsType insType) {
        // Simplified implementation for compatibility
    }
    
    public static void saveQuickPhrase(ai_QuickPhrase quickPhrase) {
        // Simplified implementation for compatibility
    }
    
    public static void saveCheckList(ai_CheckList checkList) {
        // Simplified implementation for compatibility
    }
    
    public static void saveAssetLayout(ai_AssetLayout assetLayout) {
        // Simplified implementation for compatibility
    }
    
    public static void saveInsAlert(ai_InsAlert insAlert) {
        // Simplified implementation for compatibility
    }
    
    public static List<ai_Schedule> findSchedules(String whereClause, String... params) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }
    
    public static ai_File findFileById(long id) {
        try {
            File roomEntity = getRoomManager().getDatabase().fileDao().getFileById(id);
            return roomEntity != null ? new ai_File(roomEntity) : null;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findFileById", ex);
        }
        return null;
    }
    
    public static void saveAsset(ai_Assets asset) {
        try {
            Assets roomEntity = new Assets(asset);
            if (asset.getId() != null && asset.getId() > 0) {
                getRoomManager().getDatabase().assetsDao().updateAsset(roomEntity);
            } else {
                long newId = getRoomManager().getDatabase().assetsDao().insertAsset(roomEntity);
                asset.setId(newId);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveAsset", ex);
        }
    }
    
    public static void saveSchedule(ai_Schedule schedule) {
        try {
            Schedule roomEntity = new Schedule(schedule);
            if (schedule.getId() != null && schedule.getId() > 0) {
                getRoomManager().getDatabase().scheduleDao().updateSchedule(roomEntity);
            } else {
                long newId = getRoomManager().getDatabase().scheduleDao().insertSchedule(roomEntity);
                schedule.setId(newId);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveSchedule", ex);
        }
    }
    
    // Delete all methods for migration compatibility
    public static void deleteAllContacts() {
        try {
            getRoomManager().getDatabase().contactDao().deleteAllContacts();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAllContacts", ex);
        }
    }
    
    public static void deleteAllAssetLayouts() {
        try {
            getRoomManager().getDatabase().assetLayoutDao().deleteAllAssetLayouts();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAllAssetLayouts", ex);
        }
    }
    
    public static void deleteAllCheckLists() {
        try {
            getRoomManager().getDatabase().checkListDao().deleteAllCheckLists();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAllCheckLists", ex);
        }
    }
    
    public static void deleteAllInsTypes() {
        try {
            getRoomManager().getDatabase().insTypeDao().deleteAllInsTypes();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAllInsTypes", ex);
        }
    }
    
    public static void deleteAllLayouts() {
        try {
            getRoomManager().getDatabase().layoutDao().deleteAllLayouts();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAllLayouts", ex);
        }
    }
    
    public static List<ai_InsItem> findInsItemsWithQuery(String query, String... args) {
        try {
            // For now, return existing GetChildInsItemSugar method result as fallback
            if (args.length >= 2) {
                try {
                    int insId = Integer.parseInt(args[0]);
                    int pInsItemId = Integer.parseInt(args[1]);
                    return GetChildInsItemSugar(pInsItemId, insId);
                } catch (NumberFormatException e) {
                    return new ArrayList<>();
                }
            }
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsItemsWithQuery", ex);
            return new ArrayList<>();
        }
    }
    
    public static List<ai_Project> findProjects(String whereClause, String... args) {
        try {
            // For now, return empty list - needs specific implementation based on query
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findProjects", ex);
            return new ArrayList<>();
        }
    }
    
    public static void saveProject(ai_Project project) {
        try {
            Project roomEntity = new Project(project);
            getRoomManager().getDatabase().projectDao().insertProject(roomEntity);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveProject", ex);
        }
    }
    
    
    // Complex query compatibility methods for db_Projects
    public static List<v_ProjectInspection> findProjectInspectionWithQuery(String query, String... args) {
        try {
            // For now, return empty list as this requires complex SQL view implementation
            // TODO: Implement proper Room view queries for v_ProjectInspection
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findProjectInspectionWithQuery", ex);
            return new ArrayList<>();
        }
    }

    public static List<ai_ProjectInspection> findProjectInspectionsByInsId(int iSInsID) {
        try {
            // Note: Using inspection ID instead of server inspection ID for now
            // TODO: Add proper server inspection ID query if needed
            List<ProjectInspection> roomEntities = getRoomManager().getDatabase().projectInspectionDao().getProjectInspectionsByInspectionId(iSInsID);
            List<ai_ProjectInspection> sugarEntities = new ArrayList<>();
            for (ProjectInspection room : roomEntities) {
                sugarEntities.add(room.toSugarEntity());
            }
            return sugarEntities;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findProjectInspectionsByInsId", ex);
            return new ArrayList<>();
        }
    }

    public static void saveProjectInspectionsInTx(List<ai_ProjectInspection> projectInspections) {
        try {
            List<ProjectInspection> roomProjectInspections = new ArrayList<>();
            for (ai_ProjectInspection sugar : projectInspections) {
                roomProjectInspections.add(new ProjectInspection(sugar));
            }
            getRoomManager().getDatabase().projectInspectionDao().updateProjectInspections(roomProjectInspections);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveProjectInspectionsInTx", ex);
        }
    }

    // ProductCost compatibility methods
    public static List<ai_ProductCost> findProductCostsWithQuery(String query, String... args) {
        try {
            List<ProductCost> roomEntities = getRoomManager().getDatabase().productCostDao().getAllProductCosts();
            List<ai_ProductCost> sugarEntities = new ArrayList<>();
            for (ProductCost room : roomEntities) {
                sugarEntities.add(room.toSugarEntity());
            }
            return sugarEntities;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findProductCostsWithQuery", ex);
            return new ArrayList<>();
        }
    }

    public static ai_ProductCost findProductCostById(long id) {
        try {
            ProductCost room = getRoomManager().getDatabase().productCostDao().getProductCostById(id);
            return room != null ? room.toSugarEntity() : null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findProductCostById", ex);
            return null;
        }
    }

    public static void saveProductCostsInTx(List<ai_ProductCost> productCosts) {
        try {
            List<ProductCost> roomEntities = new ArrayList<>();
            for (ai_ProductCost sugar : productCosts) {
                roomEntities.add(new ProductCost(sugar));
            }
            getRoomManager().getDatabase().productCostDao().insertProductCosts(roomEntities);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveProductCostsInTx", ex);
        }
    }


    // AssetView compatibility methods
    public static List<ai_AssetView> findAssetViews(String whereClause, String... args) {
        try {
            List<AssetView> roomEntities = getRoomManager().getDatabase().assetViewDao().getAllAssetViews();
            List<ai_AssetView> sugarEntities = new ArrayList<>();
            for (AssetView room : roomEntities) {
                sugarEntities.add(room.toSugarEntity());
            }
            return sugarEntities;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findAssetViews", ex);
            return new ArrayList<>();
        }
    }

    public static void saveAssetViewsInTx(List<ai_AssetView> assetViews) {
        try {
            List<AssetView> roomEntities = new ArrayList<>();
            for (ai_AssetView sugar : assetViews) {
                roomEntities.add(new AssetView(sugar));
            }
            getRoomManager().getDatabase().assetViewDao().insertAssetViews(roomEntities);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveAssetViewsInTx", ex);
        }
    }

    // Photo/Video compatibility methods for db_Media
    public static List<ai_Photo> findPhotos(String whereClause, String... args) {
        try {
            List<Photo> roomPhotos = new ArrayList<>();
            
            if (whereClause.equals("ID in (" + args[0] + ") AND B_DELETED = 0")) {
                // Handle complex IN query - return empty for now
                // TODO: Implement proper Room query for photo IDs
            } else if (whereClause.equals("I_INS_ITEM_ID = ? AND B_DELETED = 0")) {
                int insItemId = Integer.parseInt(args[0]);
                roomPhotos = getRoomManager().getDatabase().photoDao().getPhotosByInsItemId(insItemId);
            } else if (whereClause.equals("I_INS_ID=? and B_DELETED = 0 and B_UPLOADED=0")) {
                int insId = Integer.parseInt(args[0]);
                // Use existing method and filter by upload status
                List<Photo> allPhotos = getRoomManager().getDatabase().photoDao().getPhotosByInspectionId(insId);
                for (Photo photo : allPhotos) {
                    if (!photo.bUploaded) roomPhotos.add(photo);
                }
            } else if (whereClause.equals("I_INS_ID=? and B_DELETED = 0")) {
                int insId = Integer.parseInt(args[0]);
                roomPhotos = getRoomManager().getDatabase().photoDao().getPhotosByInspectionId(insId);
            } else if (whereClause.equals("I_S_PHOTO_ID = ? and B_DELETED = 0")) {
                int serverPhotoId = Integer.parseInt(args[0]);
                Photo photo = getRoomManager().getDatabase().photoDao().getPhotoByServerId(serverPhotoId);
                if (photo != null) roomPhotos.add(photo);
            } else if (whereClause.equals("I_S_PHOTO_ID = ? and B_DELETED = 0 and I_INS_ID = ?")) {
                int serverPhotoId = Integer.parseInt(args[0]);
                int insId = Integer.parseInt(args[1]);
                // Use existing method and filter by inspection ID
                Photo photo = getRoomManager().getDatabase().photoDao().getPhotoByServerId(serverPhotoId);
                if (photo != null && photo.iInsID == insId) roomPhotos.add(photo);
            }
            
            List<ai_Photo> sugarPhotos = new ArrayList<>();
            for (Photo room : roomPhotos) {
                sugarPhotos.add(room.toSugarEntity());
            }
            return sugarPhotos;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findPhotos", ex);
            return new ArrayList<>();
        }
    }

    public static List<ai_Video> findVideos(String whereClause, String... args) {
        try {
            List<Video> roomVideos = new ArrayList<>();
            
            if (whereClause.equals("I_INS_ID=? and B_DELETED= 0 and B_GET_URL = 0")) {
                int insId = Integer.parseInt(args[0]);
                // Use existing method and filter by upload status
                List<Video> allVideos = getRoomManager().getDatabase().videoDao().getVideosByInspectionId(insId);
                for (Video video : allVideos) {
                    if (!video.bGetURL) roomVideos.add(video);
                }
            } else if (whereClause.equals("I_S_VIDEO_ID =? and I_INS_ID=? and B_DELETED = 0")) {
                int serverVideoId = Integer.parseInt(args[0]);
                int insId = Integer.parseInt(args[1]);
                // Use existing method and filter by inspection ID
                Video video = getRoomManager().getDatabase().videoDao().getVideoByServerId(serverVideoId);
                if (video != null && video.iInsID == insId) roomVideos.add(video);
            } else if (whereClause.equals("I_S_VIDEO_ID = ? and B_DELETED = 0")) {
                int serverVideoId = Integer.parseInt(args[0]);
                Video video = getRoomManager().getDatabase().videoDao().getVideoByServerId(serverVideoId);
                if (video != null) roomVideos.add(video);
            }
            
            List<ai_Video> sugarVideos = new ArrayList<>();
            for (Video room : roomVideos) {
                sugarVideos.add(room.toSugarEntity());
            }
            return sugarVideos;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findVideos", ex);
            return new ArrayList<>();
        }
    }

    public static long countPhotos(String whereClause, String... args) {
        try {
            if (whereClause.equals("ID in (" + args[0] + ") AND B_DELETED = 0")) {
                // Handle complex IN query - return 0 for now
                // TODO: Implement proper Room count query for photo IDs
                return 0;
            }
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "countPhotos", ex);
        }
        return 0;
    }

    public static ai_Video findVideoById(long id) {
        try {
            Video roomVideo = getRoomManager().getDatabase().videoDao().getVideoById(id);
            return roomVideo != null ? roomVideo.toSugarEntity() : null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findVideoById", ex);
            return null;
        }
    }

    // Schedule compatibility methods for cursor-based queries
    public static android.database.Cursor getScheduleCursor(Class<?> viewClass, String whereClause, String[] whereArgs, String groupBy, String orderBy, String limit) {
        try {
            // For now, return null as Schedule views need to be migrated to Room
            // TODO: Implement proper Room views for v_Schedule, v_RequestInspection
            return null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "getScheduleCursor", ex);
            return null;
        }
    }

    // Notification compatibility methods
    public static ai_Notification getNotificationById(long notificationId) {
        try {
            Notification roomNotification = getRoomManager().getDatabase().notificationDao().getNotificationById(notificationId);
            return roomNotification != null ? roomNotification.toSugarEntity() : null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "getNotificationById", ex);
            return null;
        }
    }


    // Helper method to convert Room NoticeCategory to ai_NoticeCategory
    private static ai_NoticeCategory convertToSugarNoticeCategory(NoticeCategory roomNoticeCategory) {
        if (roomNoticeCategory == null) return null;
        
        ai_NoticeCategory noticeCategory = new ai_NoticeCategory();
        noticeCategory.setId(roomNoticeCategory.id);
        noticeCategory.iSNoticeCategoryID = roomNoticeCategory.iSNoticeCategoryID;
        noticeCategory.sName = roomNoticeCategory.sName;
        noticeCategory.sDescription = roomNoticeCategory.sDescription;
        noticeCategory.bDeleted = roomNoticeCategory.bDeleted;
        noticeCategory.dtUpdate = roomNoticeCategory.dtUpdate;
        noticeCategory.dtDateTime = roomNoticeCategory.dtDateTime;
        noticeCategory.iPNoteCategoryID = roomNoticeCategory.iPNoteCategoryID;
        noticeCategory.sCustom1 = roomNoticeCategory.sCustom1;
        noticeCategory.sCustom2 = roomNoticeCategory.sCustom2;
        return noticeCategory;
    }

    public static List<ai_NoticeCategory> findNoticeCategories(String whereClause, String... args) {
        try {
            // For basic queries, use getAllNoticeCategories - complex where clauses may need specific DAO methods
            List<NoticeCategory> roomNoticeCategories = getRoomManager().getDatabase().noticeCategoryDao().getAllNoticeCategories();
            List<ai_NoticeCategory> result = new ArrayList<>();
            if (roomNoticeCategories != null) {
                for (NoticeCategory roomNoticeCategory : roomNoticeCategories) {
                    ai_NoticeCategory converted = convertToSugarNoticeCategory(roomNoticeCategory);
                    if (converted != null) {
                        result.add(converted);
                    }
                }
            }
            return result;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findNoticeCategories", ex);
            return new ArrayList<>();
        }
    }

    // Product compatibility methods
    public static void executeProductQuery(String query, String... args) {
        try {
            // This method is deprecated - use specific ProductDao methods instead
            // Room doesn't support raw SQL execution like Sugar ORM
            // Use appropriate ProductDao methods like getAllProducts(), getProductById(), etc.
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "executeProductQuery", ex);
        }
    }

    // Helper method to convert Room Product to ai_Product
    private static ai_Product convertToSugarProduct(Product roomProduct) {
        if (roomProduct == null) return null;
        
        ai_Product product = new ai_Product();
        product.setId(roomProduct.id);
        product.iSProductID = roomProduct.iSProductID;
        product.sCheckListID = roomProduct.sCheckListID;
        product.iCompanyID = roomProduct.iCompanyID;
        product.sSKU = roomProduct.sSKU;
        product.sName = roomProduct.sName;
        product.sModel = roomProduct.sModel;
        product.sDesp = roomProduct.sDesp;
        product.sAssociateArea = roomProduct.sAssociateArea;
        product.sAssociateItem = roomProduct.sAssociateItem;
        product.dUnitCost = roomProduct.dUnitCost;
        product.bAllowEdit = roomProduct.bAllowEdit;
        product.bOneOffCost = roomProduct.bOneOffCost;
        product.sProductCategory = roomProduct.sProductCategory;
        product.sUnitName = roomProduct.sUnitName;
        product.sURL = roomProduct.sURL;
        product.sImage = roomProduct.sImage;
        product.sCustom1 = roomProduct.sCustom1;
        product.sCustom2 = roomProduct.sCustom2;
        product.bArchive = roomProduct.bArchive;
        product.bDeleted = roomProduct.bDeleted;
        product.iCreatedBy = roomProduct.iCreatedBy;
        product.iUpdatedBy = roomProduct.iUpdatedBy;
        product.dtUpdate = roomProduct.dtUpdate;
        product.dtDateTime = roomProduct.dtDateTime;
        return product;
    }

    public static List<ai_Product> findProductsWithQuery(String query, String... args) {
        try {
            // For basic queries, use getAllProducts - complex queries may need specific DAO methods
            List<Product> roomProducts = getRoomManager().getDatabase().productDao().getAllProducts();
            List<ai_Product> result = new ArrayList<>();
            if (roomProducts != null) {
                for (Product roomProduct : roomProducts) {
                    ai_Product converted = convertToSugarProduct(roomProduct);
                    if (converted != null) {
                        result.add(converted);
                    }
                }
            }
            return result;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findProductsWithQuery", ex);
            return new ArrayList<>();
        }
    }

    public static ai_Product findProductById(long productId) {
        try {
            Product roomProduct = getRoomManager().getDatabase().productDao().getProductById(productId);
            return convertToSugarProduct(roomProduct);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findProductById", ex);
            return null;
        }
    }

    public static void saveProductsInTx(List<ai_Product> products) {
        try {
            if (products != null && !products.isEmpty()) {
                List<Product> roomProducts = new ArrayList<>();
                for (ai_Product product : products) {
                    roomProducts.add(new Product(product));
                }
                getRoomManager().getDatabase().productDao().insertProducts(roomProducts);
            }
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveProductsInTx", ex);
        }
    }

    // Inspection compatibility methods
    public static List<ai_Inspection> findInspectionsWithQuery(String query, String... args) {
        try {
            // For basic queries, use getAllInspections - complex queries may need specific DAO methods
            List<Inspection> roomInspections = getRoomManager().getDatabase().inspectionDao().getAllInspections();
            List<ai_Inspection> result = new ArrayList<>();
            if (roomInspections != null) {
                for (Inspection roomInspection : roomInspections) {
                    ai_Inspection converted = roomInspection.toSugarEntity();
                    if (converted != null) {
                        result.add(converted);
                    }
                }
            }
            return result;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInspectionsWithQuery", ex);
            return new ArrayList<>();
        }
    }

    public static List<ai_Inspection> findInspectionsByConditions(boolean bSynced, boolean bCompleted, boolean bDeleted) {
        try {
            List<Inspection> roomInspections;
            if (bDeleted) {
                // If looking for deleted items, need a different query
                roomInspections = getRoomManager().getDatabase().inspectionDao().getAllInspections();
            } else {
                // Use the specific DAO method for completion and sync status
                if (bCompleted && !bSynced) {
                    roomInspections = getRoomManager().getDatabase().inspectionDao().getCompletedUnsyncedInspections();
                } else {
                    roomInspections = getRoomManager().getDatabase().inspectionDao().getInspectionsByCompletion(bCompleted);
                    // Filter by sync status if needed
                    if (roomInspections != null && !roomInspections.isEmpty()) {
                        roomInspections = roomInspections.stream()
                            .filter(i -> i.bSynced != null && i.bSynced == bSynced)
                            .collect(java.util.stream.Collectors.toList());
                    }
                }
            }
            
            List<ai_Inspection> result = new ArrayList<>();
            if (roomInspections != null) {
                for (Inspection roomInspection : roomInspections) {
                    ai_Inspection converted = roomInspection.toSugarEntity();
                    if (converted != null) {
                        result.add(converted);
                    }
                }
            }
            return result;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInspectionsByConditions", ex);
            return new ArrayList<>();
        }
    }

    public static void executeInspectionQuery(String query, String... args) {
        try {
            // This method is deprecated - use specific InspectionDao methods instead
            // Room doesn't support raw SQL execution like Sugar ORM
            // Use appropriate InspectionDao methods like getAllInspections(), getInspectionById(), etc.
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "executeInspectionQuery", ex);
        }
    }

    // Helper method to convert Room InsType to ai_InsType
    private static ai_InsType convertToSugarInsType(InsType roomInsType) {
        if (roomInsType == null) return null;
        return new ai_InsType(roomInsType);
    }

    // InsType compatibility methods
    public static List<ai_InsType> findAllInsTypes() {
        try {
            List<InsType> roomInsTypes = getRoomManager().getDatabase().insTypeDao().getAllInsTypes();
            List<ai_InsType> result = new ArrayList<>();
            if (roomInsTypes != null) {
                for (InsType roomInsType : roomInsTypes) {
                    ai_InsType converted = convertToSugarInsType(roomInsType);
                    if (converted != null) {
                        result.add(converted);
                    }
                }
            }
            return result;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findAllInsTypes", ex);
            return new ArrayList<>();
        }
    }

    public static ai_InsType findInsTypeBySInsTypeID(int iSInsTypeID) {
        try {
            InsType roomInsType = getRoomManager().getDatabase().insTypeDao().getInsTypeByServerId(iSInsTypeID);
            return convertToSugarInsType(roomInsType);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsTypeBySInsTypeID", ex);
            return null;
        }
    }

    // Layout compatibility methods for db_Inspection
    public static ai_Layout findLayoutBySLayoutID(int iSLayoutID) {
        try {
            Layout roomLayout = getRoomManager().getDatabase().layoutDao().getLayoutByServerId(iSLayoutID);
            return roomLayout != null ? roomLayout.toSugarEntity() : null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findLayoutBySLayoutID", ex);
            return null;
        }
    }

    // InsItem compatibility methods for db_InsItem
    public static List<ai_InsItem> findInsItemsBySInsItemID(int iSInsItemID) {
        try {
            // TODO: Implement proper Room query for JSON extraction
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsItemsBySInsItemID", ex);
            return new ArrayList<>();
        }
    }

    public static List<ai_InsItem> findDeletedInsItems(int iInsID) {
        try {
            // TODO: Implement proper Room query for deleted InsItems
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findDeletedInsItems", ex);
            return new ArrayList<>();
        }
    }

    public static void executeInsItemQuery(String query, String... args) {
        try {
            // TODO: Implement Room query execution for InsItem operations
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "executeInsItemQuery", ex);
        }
    }

    public static void saveInsItem(ai_InsItem insItem) {
        try {
            SaveInsItemSugar(insItem);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveInsItem", ex);
        }
    }

    // Asset compatibility methods for db_Asset
    public static android.database.Cursor getAssetCursor(Class<?> viewClass, String whereClause, String[] whereArgs, String groupBy, String orderBy, String limit) {
        try {
            // For now, return null as Asset views need to be migrated to Room
            // TODO: Implement proper Room views for v_Asset
            return null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "getAssetCursor", ex);
            return null;
        }
    }

    public static ai_Assets findAssetBySAssetID(int iSAssetID) {
        try {
            Assets roomAsset = getRoomManager().getDatabase().assetsDao().getAssetByServerId(iSAssetID);
            return roomAsset != null ? roomAsset.toSugarEntity() : null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findAssetBySAssetID", ex);
            return null;
        }
    }

    public static List<ai_Assets> findAssetsWithQuery(String query) {
        try {
            // TODO: Implement proper Room queries for Asset search
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findAssetsWithQuery", ex);
            return new ArrayList<>();
        }
    }

    // Contact compatibility methods for db_Asset
    public static List<ai_Contact> findContactsBySContactID(int iSContactID) {
        try {
            List<Contact> roomContacts = getRoomManager().getDatabase().contactDao().getContactsByServerAssetId(iSContactID);
            List<ai_Contact> sugarContacts = new ArrayList<>();
            for (Contact contact : roomContacts) {
                sugarContacts.add(contact.toSugarEntity());
            }
            return sugarContacts;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findContactsBySContactID", ex);
            return new ArrayList<>();
        }
    }

    // Log compatibility methods
    public static List<ai_Log> findLogsWithQuery(String query) {
        try {
            // TODO: Implement proper Room queries for Log
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findLogsWithQuery", ex);
            return new ArrayList<>();
        }
    }

    public static void saveLog(ai_Log log) {
        try {
            // TODO: Implement Log Room entity and save
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveLog", ex);
        }
    }

    /**
     * Save Contact entity
     */
    public static void saveContact(ai_Contact contact) {
        try {
            // Use legacy Sugar ORM until Room entity is implemented
            contact.save();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveContact", ex);
        }
    }

    // InsAlert compatibility methods
    public static void deleteAllInsAlerts() {
        try {
            getRoomManager().getDatabase().insAlertDao().deleteAllInsAlerts();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAllInsAlerts", ex);
        }
    }

    // Assets compatibility methods
    public static void deleteAllAssets() {
        try {
            getRoomManager().getDatabase().assetsDao().deleteAllAssets();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAllAssets", ex);
        }
    }

    // CheckList compatibility methods
    public static List<ai_CheckList> findCheckListsByPTC(String sPTC) {
        try {
            // TODO: Implement CheckList Room entity and DAO
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findCheckListsByPTC", ex);
            return new ArrayList<>();
        }
    }

    // AssetLayout compatibility methods
    public static void deleteAssetLayout(ai_AssetLayout assetLayout) {
        try {
            // TODO: Implement AssetLayout Room entity and delete
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAssetLayout", ex);
        }
    }

    // Additional Assets compatibility methods
    public static List<ai_Assets> findAssetsByFilter(String filter) {
        try {
            // TODO: Implement proper Room query for Assets by filter
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findAssetsByFilter", ex);
            return new ArrayList<>();
        }
    }

    // Additional InsType compatibility methods  
    public static List<ai_InsType> findInsTypesBySInsTypeID(int iSInsTypeID) {
        try {
            // TODO: Implement proper Room query for InsType by server ID
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsTypesBySInsTypeID", ex);
            return new ArrayList<>();
        }
    }

    // File compatibility methods
    public static void deleteAllFiles() {
        try {
            getRoomManager().getDatabase().fileDao().deleteAllFiles();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAllFiles", ex);
        }
    }

    // QuickPhrase compatibility methods
    public static void deleteAllQuickPhrases() {
        try {
            // TODO: Implement QuickPhrase deleteAll when DAO method is available
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAllQuickPhrases", ex);
        }
    }

    // FloorPlan compatibility methods
    public static ai_FloorPlan findFloorPlanById(long id) {
        try {
            // TODO: Implement FloorPlan Room entity and query
            return null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findFloorPlanById", ex);
            return null;
        }
    }

    public static List<ai_FloorPlan> findFloorPlansByServerID(int iSFloorPlanID) {
        try {
            // TODO: Implement FloorPlan Room entity and query by server ID
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findFloorPlansByServerID", ex);
            return new ArrayList<>();
        }
    }

    public static List<ai_FloorPlan> findAllFloorPlans() {
        try {
            // TODO: Implement FloorPlan Room entity and query all
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findAllFloorPlans", ex);
            return new ArrayList<>();
        }
    }

    public static List<ai_FloorPlan> findFloorPlansByAssetID(int iSAssetID) {
        try {
            // TODO: Implement FloorPlan Room entity and query by asset ID
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findFloorPlansByAssetID", ex);
            return new ArrayList<>();
        }
    }

    public static void saveFloorPlansInTransaction(List<ai_FloorPlan> floorPlans) {
        try {
            // TODO: Implement FloorPlan Room entity and bulk save transaction
            for (ai_FloorPlan floorPlan : floorPlans) {
                save(floorPlan);
            }
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveFloorPlansInTransaction", ex);
        }
    }

    public static void deleteFloorPlansInTransaction(List<ai_FloorPlan> floorPlans) {
        try {
            // TODO: Implement FloorPlan Room entity and bulk delete transaction
            for (ai_FloorPlan floorPlan : floorPlans) {
                // Set as deleted for now
                floorPlan.bDeleted = true;
                save(floorPlan);
            }
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteFloorPlansInTransaction", ex);
        }
    }

    // Project compatibility methods  
    public static ai_Project findProjectById(long projectId) {
        try {
            // TODO: Implement Project Room entity and query by ID
            return null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findProjectById", ex);
            return null;
        }
    }

    public static long countProjectInspections(int iSProjectID) {
        try {
            // TODO: Implement ProjectInspection Room entity and count query
            return 0;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "countProjectInspections", ex);
            return 0;
        }
    }

    // Additional InsType compatibility methods
    public static List<ai_InsType> findInsTypesById(int iInsTypeID) {
        try {
            // TODO: Implement proper Room query for InsType by ID
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsTypesById", ex);
            return new ArrayList<>();
        }
    }

    // Layout compatibility methods
    public static List<ai_Layout> findLayoutsByPTC(String sPTC) {
        try {
            // TODO: Implement Layout Room entity and query by PTC
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findLayoutsByPTC", ex);
            return new ArrayList<>();
        }
    }

    // Contact compatibility methods  
    public static List<ai_Contact> findContactsByAssetId(int iSAssetID) {
        try {
            // TODO: Implement Contact Room entity and query by asset ID
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findContactsByAssetId", ex);
            return new ArrayList<>();
        }
    }

    // InsItem compatibility methods
    public static List<ai_InsItem> findInsItemsByNameAndParent(int iPInsItemID, String sName) {
        try {
            // TODO: Implement proper Room query for InsItem by name and parent
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsItemsByNameAndParent", ex);
            return new ArrayList<>();
        }
    }

    public static List<ai_InsItem> findInsItemsByParent(int iPInsItemID) {
        try {
            // TODO: Implement proper Room query for InsItem by parent
            return new ArrayList<>();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsItemsByParent", ex);
            return new ArrayList<>();
        }
    }

    // Save methods for entities (compatibility layer)
    public static void save(ai_Photo photo) {
        try {
            getRoomManager().getDatabase().photoDao().insertPhoto(photo);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "save(ai_Photo)", ex);
        }
    }

    public static void save(ai_Video video) {
        try {
            getRoomManager().getDatabase().videoDao().insertVideo(video);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "save(ai_Video)", ex);
        }
    }

    public static void save(ai_TaskList taskList) {
        try {
            getRoomManager().getDatabase().taskListDao().insertTaskList(taskList);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "save(ai_TaskList)", ex);
        }
    }

    public static void save(ai_TaskComment taskComment) {
        try {
            getRoomManager().getDatabase().taskCommentDao().insertTaskComment(taskComment);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "save(ai_TaskComment)", ex);
        }
    }

    public static void save(ai_UpdateAssetTask updateAssetTask) {
        try {
            getRoomManager().getDatabase().updateAssetTaskDao().insertUpdateAssetTask(updateAssetTask);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "save(ai_UpdateAssetTask)", ex);
        }
    }

    public static ai_UpdateAssetTask findUpdateAssetTaskByAssetId(int iSAssetID) {
        try {
            List<UpdateAssetTask> tasks = getRoomManager().getDatabase().updateAssetTaskDao().getUpdateAssetTasksByAssetId(iSAssetID);
            return tasks.isEmpty() ? null : tasks.get(0);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findUpdateAssetTaskByAssetId", ex);
            return null;
        }
    }

    public static void saveTasksInTransaction(java.util.List<ai_Task> tasks) {
        try {
            getRoomManager().getDatabase().taskDao().insertTasks(tasks);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "saveTasksInTransaction", ex);
        }
    }

    public static ai_Task findTaskByNotificationId(int iSNotificationID) {
        try {
            return getRoomManager().getDatabase().taskDao().getTaskByServerId(iSNotificationID);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findTaskByNotificationId", ex);
            return null;
        }
    }

    public static ai_Task findTaskById(int taskId) {
        try {
            return getRoomManager().getDatabase().taskDao().getTaskById(taskId);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findTaskById", ex);
            return null;
        }
    }

    public static java.util.List<ai_Task> findTasksWithQuery(String whereClause, String... whereArgs) {
        try {
            // For complex queries, we'll need to implement specific methods in TaskDao
            // For now, return all tasks as a fallback - this method needs custom query logic
            // based on the specific whereClause patterns used in the app
            return getRoomManager().getDatabase().taskDao().getAllTasks();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findTasksWithQuery", ex);
            return new ArrayList<>();
        }
    }

    public static void save(ai_FloorPlan floorPlan) {
        try {
            getRoomManager().getDatabase().floorPlanDao().insertFloorPlan(floorPlan);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "save(ai_FloorPlan)", ex);
        }
    }

    // PropertyLayout compatibility methods
    public static void savePropertyLayout(ai_PropertyLayout layout) {
        try {
            getRoomManager().getDatabase().propertyLayoutDao().insertPropertyLayout(layout);
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "savePropertyLayout(ai_PropertyLayout)", ex);
        }
    }

    public static void savePropertyLayoutsInTransaction(List<ai_PropertyLayout> layoutsToSave) {
        try {
            // TODO: Implement PropertyLayout Room entity and bulk save transaction
            for (ai_PropertyLayout layout : layoutsToSave) {
                savePropertyLayout(layout);
            }
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "savePropertyLayoutsInTransaction", ex);
        }
    }

    public static ai_PropertyLayout findPropertyLayoutByServerID(int iSPropertyLayoutID) {
        try {
            // TODO: Implement PropertyLayout Room entity and query by server ID
            return null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findPropertyLayoutByServerID", ex);
            return null;
        }
    }

    public static ai_PropertyLayout findPropertyLayoutByPTCAndProperty(String sPTC, int iPropertyID) {
        try {
            // TODO: Implement PropertyLayout Room entity and query by PTC and property
            return null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findPropertyLayoutByPTCAndProperty", ex);
            return null;
        }
    }

    public static ai_Layout findLayoutByNameAndPTC(String name, String sPTC) {
        try {
            // TODO: Implement Layout Room entity and query by name and PTC
            return null;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findLayoutByNameAndPTC", ex);
            return null;
        }
    }

    // =======================
    // LISTALL METHODS
    // =======================
    
    /**
     * Get all QuickPhrases - replacement for ai_QuickPhrase.listAll()
     */
    public static List<ai_QuickPhrase> getAllQuickPhrases() {
        try {
            List<QuickPhrase> roomEntities = getRoomManager().getDatabase().quickPhraseDao().getAllQuickPhrases();
            List<ai_QuickPhrase> sugarEntities = new ArrayList<>();
            for (QuickPhrase roomEntity : roomEntities) {
                sugarEntities.add(new ai_QuickPhrase(roomEntity));
            }
            return sugarEntities;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "getAllQuickPhrases", ex);
            return new ArrayList<>();
        }
    }
    
    /**
     * Get all InsTypes - replacement for ai_InsType.listAll()
     */
    public static List<ai_InsType> getAllInsTypes() {
        try {
            List<InsType> roomEntities = getRoomManager().getDatabase().insTypeDao().getAllInsTypes();
            List<ai_InsType> sugarEntities = new ArrayList<>();
            for (InsType roomEntity : roomEntities) {
                sugarEntities.add(new ai_InsType(roomEntity));
            }
            return sugarEntities;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "getAllInsTypes", ex);
            return new ArrayList<>();
        }
    }
    
    /**
     * Get all CheckLists - replacement for ai_CheckList.listAll()
     */
    public static List<ai_CheckList> getAllCheckLists() {
        try {
            // TODO: CheckListDao is not yet implemented, return empty list for compatibility
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "getAllCheckLists", ex);
            return new ArrayList<>();
        }
    }

    // =======================
    // MISSING METHODS THAT CAUSE COMPILATION ERRORS
    // =======================
    
    /**
     * Find photo by ID - replacement for photo retrieval
     */
    public static ai_Photo findPhotoById(long id) {
        try {
            Photo roomEntity = getRoomManager().getDatabase().photoDao().getPhotoById(id);
            if (roomEntity != null) {
                return new ai_Photo(roomEntity);
            }
            return null;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findPhotoById", ex);
            return null;
        }
    }
    
    /**
     * Find layouts by layout ID
     */
    public static List<ai_Layout> findLayoutsByLayoutId(int layoutId) {
        try {
            // TODO: Implement Layout Room entity and query by layout ID
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findLayoutsByLayoutId", ex);
            return new ArrayList<>();
        }
    }
    
    /**
     * Find users by customer ID
     */
    public static List<ai_User> findUsersByCustomerId(int customerId) {
        try {
            // TODO: Implement User Room entity and query by customer ID
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findUsersByCustomerId", ex);
            return new ArrayList<>();
        }
    }
    
    /**
     * Find assets by asset ID
     */
    public static List<ai_Assets> findAssetsByAssetId(int assetId) {
        try {
            // TODO: Implement Asset Room entity and query by asset ID
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findAssetsByAssetId", ex);
            return new ArrayList<>();
        }
    }
    
    /**
     * Find schedules by schedule ID
     */
    public static List<ai_Schedule> findSchedulesByScheduleId(int scheduleId) {
        try {
            // TODO: Implement Schedule Room entity and query by schedule ID
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findSchedulesByScheduleId", ex);
            return new ArrayList<>();
        }
    }
    
    /**
     * Find InsTypes by InsType ID
     */
    public static List<ai_InsType> findInsTypesByInsTypeId(int insTypeId) {
        try {
            // TODO: Implement InsType Room entity and query by InsType ID
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsTypesByInsTypeId", ex);
            return new ArrayList<>();
        }
    }
    
    /**
     * Find QuickPhrases by phrase ID
     */
    public static List<ai_QuickPhrase> findQuickPhrasesByPhraseId(int phraseId) {
        try {
            // TODO: Implement QuickPhrase Room entity and query by phrase ID
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findQuickPhrasesByPhraseId", ex);
            return new ArrayList<>();
        }
    }
    
    /**
     * Find CheckLists by CheckList ID
     */
    public static List<ai_CheckList> findCheckListsByCheckListId(int checkListId) {
        try {
            // TODO: Implement CheckList Room entity and query by CheckList ID
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findCheckListsByCheckListId", ex);
            return new ArrayList<>();
        }
    }
    
    /**
     * Find NoticeCategories by category ID
     */
    public static List<ai_NoticeCategory> findNoticeCategoriesByCategoryId(int categoryId) {
        try {
            // TODO: Implement NoticeCategory Room entity and query by category ID
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findNoticeCategoriesByCategoryId", ex);
            return new ArrayList<>();
        }
    }

    /**
     * Delete asset by asset ID
     */
    public static void deleteAssetByAssetId(int iSAssetID) {
        try {
            // TODO: Implement Asset deletion by ID
            // For now, just stub implementation for compilation compatibility
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "deleteAssetByAssetId", ex);
        }
    }

    /**
     * Find InsItems with custom query
     */
    public static List<ai_InsItem> findInsItems(String whereClause, String... whereArgs) {
        try {
            // TODO: Implement InsItem query with Room DAO
            // For now, just stub implementation for compilation compatibility
            return new ArrayList<>();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("CommonDB", "findInsItems", ex);
            return new ArrayList<>();
        }
    }

}