package com.snapinspect.snapinspect3.Helper;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Project;
import com.snapinspect.snapinspect3.IF_Object.ai_ProjectInspection;
import com.snapinspect.snapinspect3.R;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;

public final class NetworkUtils {

    public interface Callback {
        interface GetProject {
            void onComplete(ai_Project project);
        }

        interface GetProjectInspection {
            void onComplete(ai_ProjectInspection projectInspection);
        }

        interface LockProjectInspection {
            void onComplete(ai_ProjectInspection projectInspection);
        }
    }

    public static boolean isNetworkAvailable(Context context) {
        if (context == null) return false;
        
        ConnectivityManager connectivityManager = (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Network nw = connectivityManager.getActiveNetwork();
            if (nw == null) return false;
            NetworkCapabilities actNw = connectivityManager.getNetworkCapabilities(nw);
            return actNw != null && (actNw.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)
                    || actNw.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)
                    || actNw.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)
                    || actNw.hasTransport(NetworkCapabilities.TRANSPORT_BLUETOOTH));
        } else {
            NetworkInfo nwInfo = connectivityManager.getActiveNetworkInfo();
            return nwInfo != null && nwInfo.isConnected();
        }
    }

    public static void getProject(Context ctx, int iSProjectID, Callback.GetProject callback) {
        if (!isNetworkAvailable(ctx)) {
            CommonUI.ShowAlert(ctx, ctx.getString(R.string.title_alert_error), ctx.getString(R.string.error_unavailable_network_projects));
            if (callback != null) callback.onComplete(null);
            return;
        }

        HashMap<String, String> lsParams = new HashMap<>();
        lsParams.put(Constants.Keys.iCustomerID, CommonHelper.GetPreferenceString(ctx, Constants.Keys.iCustomerID));
        lsParams.put(Constants.Keys.sToken, CommonHelper.GetPreferenceString(ctx, Constants.Keys.sToken));
        lsParams.put(Constants.Extras.iProjectID, "" + iSProjectID);
        new Thread(() -> {
            JSONObject oReturn = IF_SyncClient.PostRequest("/IOAPI/GetProject", lsParams);
            new Handler(Looper.getMainLooper()).post(() -> {
                if (oReturn != null) {
                    try {
                        if (oReturn.getBoolean("success")) {
                            if (callback != null) callback.onComplete(new ai_Project(
                                    oReturn.getJSONObject("oProject")
                            ));
                        } else {
                            if (callback != null) callback.onComplete(null);
                            CommonUI.ShowAlert(ctx, "Error", oReturn.getString("message"));
                        }
                    } catch (JSONException e) {
                        ai_BugHandler.ai_Handler_Exception(e);
                        if (callback != null) callback.onComplete(null);
                    }
                } else {
                    CommonUI.ShowAlert(ctx, "Error", ctx.getString(R.string.error_retrieve_data));
                    if (callback != null) callback.onComplete(null);
                }
            });
        }).start();
    }

    public static void getProjectInspection(
            Context ctx, int iSProjectAssetInsTypeID, Callback.GetProjectInspection callback) {
        if (!isNetworkAvailable(ctx)) {
            CommonUI.ShowAlert(ctx, "Error", ctx.getString(R.string.error_unavailable_network_projects));
            if (callback != null) callback.onComplete(null);
            return;
        }

        HashMap<String, String> lsParams = new HashMap<>();
        lsParams.put(Constants.Keys.iCustomerID, CommonHelper.GetPreferenceString(ctx, Constants.Keys.iCustomerID));
        lsParams.put(Constants.Keys.sToken, CommonHelper.GetPreferenceString(ctx, Constants.Keys.sToken));
        lsParams.put("iProjectAssetInsTypeID", "" + iSProjectAssetInsTypeID);
        new Thread(() -> {
            JSONObject oReturn = IF_SyncClient.PostRequest("/IOAPI/GetProjectInspection", lsParams);
            new Handler(Looper.getMainLooper()).post(() -> {
                if (oReturn != null) {
                    try {
                        if (oReturn.getBoolean("success")) {
                            if (callback != null) callback.onComplete(new ai_ProjectInspection(
                                    oReturn.getJSONObject("oProjectInspection")
                            ));
                        } else {
                            if (callback != null) callback.onComplete(null);
                            CommonUI.ShowAlert(ctx, "Error", oReturn.getString("message"));
                        }
                    } catch (JSONException e) {
                        ai_BugHandler.ai_Handler_Exception(e);
                        if (callback != null) callback.onComplete(null);
                    }
                } else {
                    CommonUI.ShowAlert(ctx, "Error", ctx.getString(R.string.error_retrieve_data));
                    if (callback != null) callback.onComplete(null);
                }
            });
        }).start();
    }

    public static void lockProjectInspection(Context ctx, int iSProjectAssetInsTypeID, int iInspectorID, Callback.LockProjectInspection callback) {
        if (!isNetworkAvailable(ctx)) {
            CommonUI.ShowAlert(ctx, "Error", ctx.getString(R.string.error_unavailable_network_projects));
            if (callback != null) callback.onComplete(null);
            return;
        }

        HashMap<String, String> lsParams = new HashMap<>();
        lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(ctx, Constants.Keys.iCustomerID));
        lsParams.put("sToken", CommonHelper.GetPreferenceString(ctx, Constants.Keys.sToken));
        lsParams.put("iProjectAssetInsTypeID", "" + iSProjectAssetInsTypeID);
        lsParams.put("iInspectorID", "" + iInspectorID);
        new Thread(() -> {
            JSONObject oReturn = IF_SyncClient.PostRequest("/IOAPI/LockProjectInspection", lsParams);
            new Handler(Looper.getMainLooper()).post(() -> {
                if (oReturn != null) {
                    try {
                        if (oReturn.getBoolean("success")) {
                            if (callback != null) callback.onComplete(
                                    new ai_ProjectInspection(oReturn.getJSONObject("oProjectInspection")));
                        } else {
                            if (callback != null) callback.onComplete(null);
                            CommonUI.ShowAlert(ctx, "Error", oReturn.getString("message"));
                        }
                    } catch (JSONException e) {
                        ai_BugHandler.ai_Handler_Exception(e);
                        if (callback != null) callback.onComplete(null);
                    }
                } else {
                    CommonUI.ShowAlert(ctx, "Error", ctx.getString(R.string.error_retrieve_data));
                    if (callback != null) callback.onComplete(null);
                }
            });
        }).start();
    }
}
