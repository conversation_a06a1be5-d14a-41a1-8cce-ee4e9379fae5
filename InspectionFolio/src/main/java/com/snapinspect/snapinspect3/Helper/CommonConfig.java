package com.snapinspect.snapinspect3.Helper;

import android.content.Context;

import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;

import java.util.ArrayList;
import java.util.List;

public class CommonConfig {
    public static final String sMenu_DeleteItem = "Delete Item";
    public static final String sMenu_EditItemName = "Edit Name";
    public static final String sMenu_DuplicateItem = "Duplicate Item";
    public static final String sMenu_ReviewNotes = "Review Notes";
    public static final String sMenu_ResumeRecording_Video = "Resume Recording";
    public static final String sMenu_Delete_Video = "Delete Video";
    public static final String sMenu_ItemTask = "Tasks";
    public static final String sMenu_RemoveSignature_Sign = "Remove Signature";
    public static final String sMenu_FloorPlan_DropPin = "Blueprint Annotation";


    public static boolean bHideAddVideo(Context oContext, ai_InsItem oPInsItem){
        try{
            if ((oPInsItem.sQType.equals("V") && (CommonJson.GetJsonKeyValue("VADD", oPInsItem.sConfig) == null))){
                return true;
            }
        }catch(Exception ex){}
        return false;
    }
    public static void bNotification_Save(Context oContext, String sValue){
        try{
            CommonHelper.SavePreference(oContext, "bNotification", sValue);
        }catch(Exception ex){

        }
    }
    public static boolean bNotification_Enabled(Context oContext){
        try{
            return CommonHelper.GetPreferenceBoolean(oContext, "bNotification");
        }catch(Exception ex){

        }
        return false;
    }
    public static boolean bDisplayInstruction(Context oContext){
        try {
            boolean bDisplayInst = CommonHelper.GetPreferenceBoolean(oContext, Constants.Settings.bDisplayInst);
            boolean bKiosk = CommonHelper.isKioskMode(oContext);
            boolean bTurnOnInstruction = CommonJson.shouldTurnOnInstruction(oContext);
            if (bDisplayInst || bKiosk || bTurnOnInstruction) {
                return true;
            }
        }catch(Exception ex){

        }
        return false;
    }

    public static String[] MenuText_ItemsView_C_Type(
            Context oContext, boolean bRequestInspection, boolean bReview, boolean bFloorPlan) {
        ArrayList<String> lsMenu = new ArrayList<> ();
        lsMenu.add(sMenu_DeleteItem);
        if (bFloorPlan) {
            lsMenu.add(sMenu_FloorPlan_DropPin);
        }
        lsMenu.add(sMenu_EditItemName);
        lsMenu.add(sMenu_DuplicateItem);
        if (bRequestInspection){
            return lsMenu.toArray(new String[0]);
        }

        if (CommonConfig.bNotification_Enabled(oContext)){
            lsMenu.add(sMenu_ItemTask);
        }
        if (bReview){
            lsMenu.add(sMenu_ReviewNotes);
        }

        return lsMenu.toArray(new String[0]);
    }
    public static String[] MenuText_ItemsView_Sign_Type(
            Context oContext, boolean bRequestInspection, boolean bReview, List<ai_Photo> lsPhoto) {

        ArrayList<String> lsMenu = new ArrayList<String> ();
        lsMenu.add(sMenu_DeleteItem);
        lsMenu.add(sMenu_EditItemName);
        lsMenu.add(sMenu_DuplicateItem);
        if (lsPhoto != null && lsPhoto.size() == 1){
            lsMenu.add(sMenu_RemoveSignature_Sign);

        }
        if (bReview){
            lsMenu.add(sMenu_ReviewNotes);
        }
        return lsMenu.toArray(new String[lsMenu.size()]);
    }
    public static String[] MenuText_ItemsView_Video_Type(Context oContext, boolean bRequestInspection, boolean bReview, ai_Video oVideo, boolean bHideVideoAdd){
        ArrayList<String> lsMenu = new ArrayList<>();
        if (bRequestInspection) {
            // Only display resume recording option for the requested inspection item
            if (oVideo != null) {
                lsMenu.add(sMenu_ResumeRecording_Video);
                lsMenu.add(sMenu_Delete_Video);
            }
        } else {
            lsMenu.add(sMenu_DeleteItem);
            lsMenu.add(sMenu_EditItemName);
            if (!bHideVideoAdd) lsMenu.add(sMenu_DuplicateItem);
            if (oVideo != null) {
                lsMenu.add(sMenu_ResumeRecording_Video);
                lsMenu.add(sMenu_Delete_Video);
            }
            if (bReview) lsMenu.add(sMenu_ReviewNotes);
        }
        return lsMenu.toArray(new String[lsMenu.size()]);
    }

    public static String[] MenuText_ItemsView_GA_Type(Context oContext, boolean bRequestInspection, boolean bReview){
        ArrayList<String> lsMenu = new ArrayList<String> ();
        if (bRequestInspection){
            return lsMenu.toArray(new String[lsMenu.size()]);
        }


        if (CommonConfig.bNotification_Enabled(oContext)){
            lsMenu.add(sMenu_ItemTask);
        }
        if (bReview){
            lsMenu.add(sMenu_ReviewNotes);
        }

        return lsMenu.toArray(new String[lsMenu.size()]);

    }
}

