package com.snapinspect.snapinspect3.Helper;

import android.database.Cursor;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Schedule;
import com.snapinspect.snapinspect3.IF_Object.v_RequestInspection;
import com.snapinspect.snapinspect3.IF_Object.v_Schedule;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.database.SnapInspectDatabase;
import com.snapinspect.snapinspect3.database.views.VScheduleView;
import com.snapinspect.snapinspect3.database.views.VRequestInspectionView;
import com.snapinspect.snapinspect3.app.App;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by Terry<PERSON> on 2/08/17.
 */

public class CommonDB_Schedule {

    public static void CreateScheduleView() {
        try{
            String sPropertyCreate = "CREATE VIEW IF NOT EXISTS VASSET AS SELECT c.ID as iID, c.I_S_ASSET_ID as iAssetID, d.I_S_ASSET_ID as iPAssetID," +
                    "e.I_S_ASSET_ID as iPPAssetID, c.S_FIELD_THREE as sRef, " +
                    "((CASE WHEN d.I_SP_ASSET_ID > 0 THEN ' ' || e.S_ADDRESS_ONE || ' ' || e.S_ADDRESS_TWO || ' | ' ELSE '' END) || " +
                    "(CASE WHEN c.I_SP_ASSET_ID > 0 THEN ' ' || d.S_ADDRESS_ONE || ' ' || d.S_ADDRESS_TWO || ' | ' ELSE '' END ) || " +
                    "' ' || c.S_ADDRESS_ONE || ' ' || c.S_ADDRESS_TWO) as sSearchTerm, " +
                    "(CASE WHEN d.I_SP_Asset_ID > 0 THEN e.S_ADDRESS_ONE || ', ' || e.S_ADDRESS_TWO WHEN C.I_SP_ASSET_ID > 0 THEN d.S_ADDRESS_ONE || ', ' || d.S_ADDRESS_TWO " +
                    "ELSE c.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO END) as sBuildingAddress, " +
                    "(CASE WHEN d.I_SP_ASSET_ID > 0 then d.S_ADDRESS_ONE || ', ' || d.S_ADDRESS_TWO WHEN C.I_SP_ASSET_ID > 0 THEN c.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO " +
                    "ELSE '' END) as sUnitAddress, " +
                    "(CASE WHEN C.I_SP_ASSET_ID > 0 and d.I_SP_ASSET_ID > 0 THEN C.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO ELSE '' END) as sRoomAddress, " +
                    "c.I_CUSTOMER_ID as iCustomerID, c.I_GROUP_ID as iGroupID, c.B_PUSH as bApartment, c.S_FIELD_ONE as sCustom1, c.S_FIELD_TWO as sCustom2 FROM AIASSETS c LEFT JOIN AIASSETS d on c.I_SP_ASSET_ID = d.I_S_ASSET_ID LEFT JOIN " +
                    "AIASSETS e on d.I_SP_ASSET_ID = e.I_S_ASSET_ID WHERE c.B_DELETED = 0";
            // Views are now automatically created by Room from @DatabaseView annotations
            // VScheduleView, VAssetView, VInspectionView, and VRequestInspectionView are defined in the database
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Schedule.CreateScheduleView", ex);
        }
    }

    public static void CreateRequestInspectionView() {
        try {
            // Views are now automatically created by Room from @DatabaseView annotations
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    public static ai_Schedule GetScheduleBySScheduleID(int iSScheduleID){
        try {
            List<ai_Schedule> lsSchedule = CommonDB.findSchedules(
                    "I_S_SCHEDULE_ID = ?", String.valueOf(iSScheduleID)
            );
            if (!lsSchedule.isEmpty())
                return lsSchedule.get(0);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Schedule.GetScheduleBySScheduleID", ex);
        }
        return null;
    }

    public static v_Schedule GetTheEarliestSchedule() {
        try {
            VScheduleView earliest = getDatabase().vScheduleDao().getEarliestSchedule();
            return convertToVSchedule(earliest);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Schedule.GetTheEarliestSchedule", ex);
        }
        return null;
    }

    public static List<v_Schedule> SearchSchedules(String sSearchTerm) {
        return SearchSchedules(sSearchTerm, null, null);
    }

    public static List<v_Schedule> SearchSchedules(String sSearchTerm, Date start, Date end) {
        ArrayList<v_Schedule> results = new ArrayList<>();
        try {
            List<VScheduleView> schedules;
            
            if (!StringUtils.isEmpty(sSearchTerm) && start != null && end != null) {
                // Search with term and date range - need to filter manually for now
                List<VScheduleView> allByTerm = getDatabase().vScheduleDao().searchSchedulesByTerm("%" + sSearchTerm + "%");
                schedules = new ArrayList<>();
                long startTime = start.getTime();
                long endTime = end.getTime();
                for (VScheduleView schedule : allByTerm) {
                    if (schedule.iUnixTime >= startTime && schedule.iUnixTime <= endTime) {
                        schedules.add(schedule);
                    }
                }
            } else if (!StringUtils.isEmpty(sSearchTerm)) {
                // Search by term only
                String[] searchItems = sSearchTerm.split(",| |;");
                schedules = new ArrayList<>();
                for (String item : searchItems) {
                    String trimmed = item.trim();
                    if (!StringUtils.isEmpty(trimmed)) {
                        List<VScheduleView> termResults = getDatabase().vScheduleDao().searchSchedulesByTerm("%" + trimmed + "%");
                        for (VScheduleView schedule : termResults) {
                            if (!schedules.contains(schedule)) {
                                schedules.add(schedule);
                            }
                        }
                    }
                }
            } else if (start != null && end != null) {
                // Search by date range only
                schedules = getDatabase().vScheduleDao().searchSchedulesByDateRange(start.getTime(), end.getTime());
            } else {
                // No filters - return empty for safety
                schedules = new ArrayList<>();
            }
            
            for (VScheduleView schedule : schedules) {
                v_Schedule converted = convertToVSchedule(schedule);
                if (converted != null) {
                    results.add(converted);
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Schedule.SearchSchedule", ex);
        }

        return results;
    }

    public static List<v_Schedule> SearchRecurSchedules(String sSearchTerm) {
        ArrayList<v_Schedule> results = new ArrayList<>();
        try {
            List<VScheduleView> schedules;
            
            if (!StringUtils.isEmpty(sSearchTerm)) {
                schedules = getDatabase().vScheduleDao().searchRecurringSchedulesByTerm("%" + sSearchTerm + "%");
            } else {
                schedules = getDatabase().vScheduleDao().getRecurringSchedules();
            }
            
            for (VScheduleView schedule : schedules) {
                v_Schedule converted = convertToVSchedule(schedule);
                if (converted != null) {
                    results.add(converted);
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Schedule.SearchRecurSchedule", ex);
        }
        return results;
    }

    private static String whereClause(String sSearchTerm, Date start, Date end, boolean isRecursive) {
        List<String> wheres = new ArrayList<>();
        if (!StringUtils.isEmpty(sSearchTerm)) {
            String[] searchItems = sSearchTerm.split(",| |;");
            for (String item: searchItems) {
                String trimmed = item.trim();
                if (!StringUtils.isEmpty(trimmed))
                    wheres.add("sSearchTerm LIKE '%"+ trimmed +"%'");
            }
        }

        if (isRecursive)
            wheres.add("(LENGTH (sRRule) > 0 OR LENGTH (sEXRule) > 0)");
        else {
            if (start != null)
                wheres.add("iUnixTime >= " + start.getTime());
            if (end != null)
                wheres.add("iUnixTime <= " + end.getTime());
        }

        return !wheres.isEmpty() ? String.join(" AND ", wheres) : null;
    }

    public static List<v_RequestInspection> GetRequestInspectionsWithText(String text) {
        ArrayList<v_RequestInspection> results = new ArrayList<>();
        try {
            List<VRequestInspectionView> requestInspections;
            
            if (!StringUtils.isEmpty(text)) {
                String[] searchItems = text.split(",| |;");
                requestInspections = new ArrayList<>();
                
                for (String item : searchItems) {
                    String trimmed = item.trim();
                    if (!StringUtils.isEmpty(trimmed)) {
                        List<VRequestInspectionView> termResults = getDatabase().vRequestInspectionDao()
                            .getRequestInspectionsWithText("%" + trimmed + "%");
                        for (VRequestInspectionView requestInspection : termResults) {
                            if (!requestInspections.contains(requestInspection)) {
                                requestInspections.add(requestInspection);
                            }
                        }
                    }
                }
            } else {
                requestInspections = getDatabase().vRequestInspectionDao().getAllRequestInspections();
            }
            
            for (VRequestInspectionView requestInspection : requestInspections) {
                v_RequestInspection converted = convertToVRequestInspection(requestInspection);
                if (converted != null) {
                    results.add(converted);
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return results;
    }

    public static v_Schedule GetVScheduleBySScheduleID(int iSScheduleID) {
        try {
            // Use a custom query to find schedule by server schedule ID
            List<VScheduleView> schedules = getDatabase().vScheduleDao().searchSchedulesByServerId(iSScheduleID);
            if (!schedules.isEmpty()) {
                return convertToVSchedule(schedules.get(0));
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Schedule.GetScheduleBySScheduleID", ex);
        }
        return null;
    }

    public static List<v_Schedule> getSchedulesByAssetID(int iSAssetID, int iSInsTypeID) {
        List<v_Schedule> results = new ArrayList<>();
        try {
            List<VScheduleView> schedules = getDatabase().vScheduleDao().searchSchedulesByAssetAndType(iSAssetID, iSInsTypeID);

            for (VScheduleView schedule : schedules) {
                v_Schedule converted = convertToVSchedule(schedule);
                if (converted != null) {
                    results.add(converted);
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Schedule.getSchedulesByAssetID", ex);
        }
        return results;
    }
    
    /**
     * Helper method to convert VScheduleView to v_Schedule
     */
    private static v_Schedule convertToVSchedule(VScheduleView view) {
        if (view == null) return null;
        
        v_Schedule schedule = new v_Schedule();
        
        // Schedule fields
        schedule.iScheduleID = view.iScheduleID;
        schedule.iSScheduleID = view.iSScheduleID;
        schedule.iSInsTypeID = view.iSInsTypeID;
        schedule.sPTC = view.sPTC;
        schedule.sInsTitle = view.sInsTitle;
        schedule.sDateTime = view.sDateTime;
        schedule.iUnixTime = view.iUnixTime;
        schedule.bCompleted = view.bCompleted;
        schedule.sCustom1 = view.sCustom1;
        schedule.sCustom2 = view.sCustom2;
        schedule.sType = view.sType;
        schedule.sAddress1 = view.sAddress1;
        schedule.sAddress2 = view.sAddress2;
        schedule.sRRule = view.sRRule;
        schedule.sEXRule = view.sEXRule;
        
        // Asset fields from the view
        schedule.iAssetID = view.iID;
        schedule.iSAssetID = view.iAssetID;
        schedule.iPSAssetID = view.iPAssetID;
        schedule.iPPSAssetID = view.iPPAssetID;
        schedule.sRef = view.sRef;
        schedule.sBuildingAddress = view.sBuildingAddress;
        schedule.sUnitAddress = view.sUnitAddress;
        schedule.sRoomAddress = view.sRoomAddress;
        schedule.iCustomerID = view.iCustomerID;
        schedule.iGroupID = view.iGroupID;
        schedule.bApartment = view.bApartment;
        schedule.sA_Custom1 = view.sCustom1;
        schedule.sA_Custom2 = view.sCustom2;
        
        return schedule;
    }
    
    /**
     * Helper method to convert VRequestInspectionView to v_RequestInspection
     */
    private static v_RequestInspection convertToVRequestInspection(VRequestInspectionView view) {
        if (view == null) return null;
        
        try {
            // Create a mock cursor using MockCursor utility
            MockCursor cursor = new MockCursor();
            
            // Add all the fields that v_RequestInspection expects
            cursor.addColumn("iInspectionID", view.iInspectionID);
            cursor.addColumn("sInspectionTitle", view.sInspectionTitle);
            cursor.addColumn("sTitle", view.sTitle);
            cursor.addColumn("bComplete", view.bComplete ? 1 : 0);
            cursor.addColumn("bSynced", view.bSynced ? 1 : 0);
            cursor.addColumn("iSInsID", view.iSInsID);
            cursor.addColumn("sInsSearchTerm", view.sInsSearchTerm);
            
            // v_Schedule fields (parent class)
            cursor.addColumn("iScheduleID", view.iScheduleID);
            cursor.addColumn("iSScheduleID", view.iSScheduleID);
            cursor.addColumn("iSInsTypeID", view.iSInsTypeID);
            cursor.addColumn("sPTC", view.sPTC != null ? view.sPTC : "");
            cursor.addColumn("sInsTitle", view.sInsTitle != null ? view.sInsTitle : "");
            cursor.addColumn("sDateTime", view.sDateTime != null ? view.sDateTime : "");
            cursor.addColumn("iUnixTime", view.iUnixTime);
            cursor.addColumn("bCompleted", view.bCompleted ? 1 : 0);
            cursor.addColumn("sType", view.sType != null ? view.sType : "");
            cursor.addColumn("sCustom1", view.sCustom1 != null ? view.sCustom1 : "");
            cursor.addColumn("sCustom2", view.sCustom2 != null ? view.sCustom2 : "");
            cursor.addColumn("sAddress1", view.sAddress1 != null ? view.sAddress1 : "");
            cursor.addColumn("sAddress2", view.sAddress2 != null ? view.sAddress2 : "");
            cursor.addColumn("sRRule", view.sRRule != null ? view.sRRule : "");
            cursor.addColumn("sEXRule", view.sEXRule != null ? view.sEXRule : "");
            
            // v_Asset fields (grandparent class)
            cursor.addColumn("iID", view.iID);
            cursor.addColumn("iAssetID", view.iAssetID);
            cursor.addColumn("iPAssetID", view.iPAssetID);
            cursor.addColumn("iPPAssetID", view.iPPAssetID);
            cursor.addColumn("sRef", view.sRef != null ? view.sRef : "");
            cursor.addColumn("sBuildingAddress", view.sBuildingAddress != null ? view.sBuildingAddress : "");
            cursor.addColumn("sUnitAddress", view.sUnitAddress != null ? view.sUnitAddress : "");
            cursor.addColumn("sRoomAddress", view.sRoomAddress != null ? view.sRoomAddress : "");
            cursor.addColumn("iCustomerID", view.iCustomerID);
            cursor.addColumn("iGroupID", view.iGroupID);
            cursor.addColumn("bApartment", view.bApartment ? 1 : 0);
            
            cursor.moveToFirst();
            return new v_RequestInspection(cursor);
            
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception("Exception", "convertToVRequestInspection", e);
            return null;
        }
    }
    
    /**
     * Get the database instance
     */
    private static SnapInspectDatabase getDatabase() {
        return SnapInspectDatabase.getInstance(App.getContext());
    }
}
