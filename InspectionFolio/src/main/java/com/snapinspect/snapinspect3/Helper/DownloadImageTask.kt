package com.snapinspect.snapinspect3.helper

import android.graphics.Bitmap
import android.graphics.BitmapFactory
import com.snapinspect.snapinspect3.Helper.CommonHelper
import kotlinx.coroutines.*
import java.net.URL

class DownloadImageTask(
    private val savedPath: String, private val onComplete: (Bitmap?) -> Unit
) {

    private val scope = CoroutineScope(Dispatchers.Main + SupervisorJob())

    fun load(imageUrl: String) {
        if (imageUrl.isBlank() || savedPath.isBlank()) {
            onComplete(null)
            return
        }

        scope.launch {
            val result = downloadImage(imageUrl)
            result?.let {
                CommonHelper.SaveImage(savedPath, it)
                // Avoid recycling the bitmap here to ensure onComplete can use it
            }
            onComplete(result)
            result?.recycle()
        }
    }

    private suspend fun downloadImage(url: String): Bitmap? = withContext(Dispatchers.IO) {
        return@withContext try {
            BitmapFactory.decodeStream(URL(url).openStream())
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    fun cancel() {
        scope.cancel()
    }
}