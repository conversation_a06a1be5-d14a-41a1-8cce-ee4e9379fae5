package com.snapinspect.snapinspect3.Helper

import android.util.Range
import android.util.Size
import androidx.camera.video.Quality

enum class VideoQuality(
    val qualityName: String,
    val quality: Quality,
    val resolution: Size,
    var frameRateRange: Range<Int>,
    val bitRate: Int,

    // don't find how to update audio encoding settings
    // these might be the default values (https://developer.android.com/media/platform/supported-formats?#video-encoding)
    // so will adjust them on iOS side
    val audioBitRate: Int, 
    val audioChannels: Int, 
    val audioCodec: String 
) {
    LOW(
        qualityName = "SD", 
        quality = Quality.SD,
        resolution = Size(640, 480),
        frameRateRange = Range(25, 25),
        bitRate = 2_000_000,
        audioBitRate = 128_000,
        audioChannels = 1,
        audioCodec = "AAC-LC"
    ),
    MEDIUM(
        qualityName = "HD", 
        quality = Quality.HD,
        resolution = Size(1280, 720),
        frameRateRange = Range(25, 25),
        bitRate = 2_200_000,
        audioBitRate = 128_000,
        audioChannels = 2,
        audioCodec = "AAC-LC"
    ),
    HIGH(
        qualityName = "FHD", 
        quality = Quality.FHD,
        resolution = Size(1920, 1080),
        frameRateRange = Range(25, 25),
        bitRate = 4_000_000,
        audioBitRate = 192_000,
        audioChannels = 2,
        audioCodec = "AAC-LC"
    );

    companion object {
        @JvmStatic
        fun fromQualityName(qualityName: String): VideoQuality? {
            return entries.find { it.qualityName.equals(qualityName, ignoreCase = true) }
        }
    }
}
