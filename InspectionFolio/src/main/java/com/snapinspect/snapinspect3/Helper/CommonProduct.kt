package com.snapinspect.snapinspect3.Helper

import android.content.Context
import com.snapinspect.snapinspect3.IF_Object.ai_Product
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.SI_DB.db_Product
import com.snapinspect.snapinspect3.activity.SyncService.SYNC_PROGRESS
import com.snapinspect.snapinspect3.util.StringUtils

object CommonProduct {
    @JvmStatic
    fun syncAndSaveAllProduct(context: Context, progressCallback: ProgressCallback) {
        val lastSyncTime = CommonHelper.GetPreferenceString(context, Constants.Keys.DATE_SYNC_PRODUCTS)
        val sDateTime = StringUtils.ifEmpty(lastSyncTime, "1980-01-01 00:00:00")
        var iStartIndex = 0
        val iLength = 1000

        val allProducts = mutableListOf<ai_Product>()
        while (true) {
            val result = syncProduct(context, sDateTime, iStartIndex, iLength)
            when {
                result.isSuccess -> {
                    val products = result.getOrNull() ?: emptyList()
                    allProducts.addAll(products)

                    // save products to database
                    db_Product.saveProducts(products)

                    // Update progress
                    progressCallback.onProgress(
                        SYNC_PROGRESS, context.getString(R.string.syncing),
                        "Processed ${allProducts.size} Asset Layouts. Please Wait"
                    )
                    if (products.size < iLength) break  // No more products to fetch
                    iStartIndex += iLength
                }

                result.isFailure -> {
                    val error = result.exceptionOrNull()
                    CommonUI.ShowAlert(context, "Error", error?.message ?: "Unknown error occurred")
                    break
                }
            }
        }
    }

    private fun syncProduct(
        context: Context?, sDateTime: String, iStartIndex: Int, iLength: Int
    ): Result<List<ai_Product>> {
        return try {
            val params = HashMap<String, String>().apply {
                put("iCustomerID", CommonHelper.GetPreferenceString(context, "iCustomerID") ?: "")
                put("sToken", CommonHelper.GetPreferenceString(context, "sToken") ?: "")
                put("sDateTime", sDateTime)
                put("iStartIndex", iStartIndex.toString())
                put("iLength", iLength.toString())
            }

            val response = IF_SyncClient.PostRequest("/Sync/SyncProduct", params)
            if (response.getBoolean("success")) {
                val lsProduct = response.getJSONArray("lsProduct")
                val products = List(lsProduct.length()) { i ->
                    ai_Product(lsProduct.getJSONObject(i))
                }
                // save the dtSyncDate when retrieving the first batch
                if (iStartIndex == 0) {
                    CommonHelper.SavePreference(
                        context, Constants.Keys.DATE_SYNC_PRODUCTS,
                        response.optString("dtSyncDate") ?: ""
                    )
                }
                Result.success(products)
            } else {
                Result.failure(Exception("API request was not successful"))
            }
        } catch (ex: Exception) {
            Result.failure(ex)
        }
    }
}