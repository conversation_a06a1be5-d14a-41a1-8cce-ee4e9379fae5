package com.snapinspect.snapinspect3.Helper

enum class ApiEndpoint(val path: String) {
    // Task endpoints
    TASK_COMPLETE("/App/Task_Complete"),
    TASK_DELETE("/sync/delete_task"),
    TASK_UPDATE_STATUS("/sync/update_status"),
    TASK_SAVE_DETAILS("/App/Task_SaveDetails_2024"),
    TASK_SAVE_PHOTO("/App/Task_SavePhoto"),
    TASK_SAVE_VIDEO("/App/Task_SaveVideo"),
    TASK_SAVE_COMMENT("/App/TaskComments_Save"),
    TASK_GET_COMMENTS("/App/TaskComments_Get"),
    TASK_ASSET_TASKS("/app/task/asset_tasks"),
    TASK_DETAILS("/app/task/details"),
    
    // Photo endpoints
    PHOTO_SAVE_COMMENTS("/App/SavePhotoComments"),
    PHOTO_UPLOAD("/Sync/UploadPhoto"),
    PHOTO_UPLOAD_SUCCESS("/Sync/UploadPhotoSuccess"),
    PHOTO_THUMB_URL("/IOAPI/GetPhotoThumb"),

    // Video endpoints
    VIDEO_GET_TOKEN("/Sync/GetVideoToken"),
    VIDEO_UPLOAD_SUCCESS("/Sync/UploadVideoSuccess"),

    // Property layout endpoints
    PROPERTY_LAYOUT_SYNC("/app/propertylayout/sync"),
    ASSET_LAYOUT_LAYOUT_V3("/IOAPI/GetAssetLayoutLayout_V3"),

    // Customer tasks endpoints
    CUSTOMER_TASKS_CATCH("/app/task/customer_tasks_catch"),
    CUSTOMER_TASKS("/app/task/customer_tasks");

} 