package com.snapinspect.snapinspect3.Helper;

// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.IF_Object.ai_FloorPlan;

import java.util.List;

public class CommonDB_FloorPlan {

    public static ai_FloorPlan getFloorPlanWithID(long iFloorPlanID) {
        return CommonDB.findFloorPlanById(iFloorPlanID);
    }
    public static ai_FloorPlan getFloorPlan(int iSFloorPlanID) {
        List<ai_FloorPlan> aiFloorPlans = CommonDB.findFloorPlansByServerID(iSFloorPlanID);
        return !aiFloorPlans.isEmpty() ? aiFloorPlans.get(0) : null;
    }

    public static List<ai_FloorPlan> getFloorPlans() {
        return CommonDB.findAllFloorPlans();
    }

    public static List<ai_FloorPlan> getFloorPlans(int iSAssetID) {
        return CommonDB.findFloorPlansByAssetID(iSAssetID);
    }

}
