package com.snapinspect.snapinspect3.Helper;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Criteria;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import android.os.Looper;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresPermission;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.PermissionChecker;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.util.StringUtils;

import static android.Manifest.permission.ACCESS_COARSE_LOCATION;
import static android.Manifest.permission.ACCESS_FINE_LOCATION;
import static android.content.Context.LOCATION_SERVICE;

public class GeoLocationManager implements LocationListener {

    private final Context mContext;
    private static final Object mLock = new Object();
    private static GeoLocationManager mInstance;
    private final LocationManager mLocationManager;
    private Location mLocation;
    public static GeoLocationManager getInstance(@NonNull Context context) {
        synchronized (mLock) {
            if (mInstance == null) {
                mInstance = new GeoLocationManager(context);
            }
            return mInstance;
        }
    }

    private GeoLocationManager(Context context) {
        mContext = context.getApplicationContext();
        mLocationManager = (LocationManager) context.getSystemService(LOCATION_SERVICE);
    }

    public boolean locationPermissionDenied() {
        LocationManager lm = (LocationManager) mContext.getSystemService(LOCATION_SERVICE);
        String provider = lm.getBestProvider(new Criteria(), true);
        boolean isLocationServiceEnabled = !StringUtils.isEmpty(provider) && !LocationManager.PASSIVE_PROVIDER.equals(provider);
        return !isLocationServiceEnabled ||
            ((ActivityCompat.checkSelfPermission(mContext, ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED
                && ActivityCompat.checkSelfPermission(mContext, ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED));
    }

    @SuppressLint("MissingPermission")
    public void startUpdateLocation() {
        try {
            if (mLocationManager == null || locationPermissionDenied()) return;

            mLocation = getLastKnownLocation();

            String provider;
            if (mLocationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                provider = LocationManager.GPS_PROVIDER;
            } else if (mLocationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                provider = LocationManager.NETWORK_PROVIDER;
            } else {
                provider = LocationManager.PASSIVE_PROVIDER;
            }
            mLocationManager.requestLocationUpdates(
                    provider, 1000L, 500.0f, this, Looper.myLooper());
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "GeoLocationManager.startUpdateLocation", ex, mContext);
        }
    }

    public void stopUpdateLocation() {
        try {
            if (mLocationManager == null) return;
            mLocationManager.removeUpdates(this);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "GeoLocationManager.stopUpdateLocation", ex, mContext);
        }
    }

    @Override
    public void onLocationChanged(Location location) {
        GeoLocationManager.this.mLocation = location;
    }

    @Override
    public void onStatusChanged(String provider, int status, Bundle extras) {
    }

    @Override
    public void onProviderEnabled(String provider) {
    }

    @Override
    public void onProviderDisabled(String provider) {
    }

    public Location getLocation() {
        return mLocation;
    }

    @SuppressLint("MissingPermission") // permissions are checked for the needed call.
    private Location getLastKnownLocation() {
        Location coarseLoc = null;
        Location fineLoc = null;

        int permission = PermissionChecker.checkSelfPermission(mContext,
                Manifest.permission.ACCESS_COARSE_LOCATION);
        if (permission == PermissionChecker.PERMISSION_GRANTED) {
            coarseLoc = getLastKnownLocationForProvider(LocationManager.NETWORK_PROVIDER);
        }

        permission = PermissionChecker.checkSelfPermission(mContext,
                Manifest.permission.ACCESS_FINE_LOCATION);
        if (permission == PermissionChecker.PERMISSION_GRANTED) {
            fineLoc = getLastKnownLocationForProvider(LocationManager.GPS_PROVIDER);
        }

        if (fineLoc != null && coarseLoc != null) {
            // If we have both a fine and coarse location, use the latest
            return fineLoc.getTime() > coarseLoc.getTime() ? fineLoc : coarseLoc;
        } else {
            // Else, return the non-null one (if there is one)
            return fineLoc != null ? fineLoc : coarseLoc;
        }
    }

    @RequiresPermission(anyOf = {ACCESS_COARSE_LOCATION, ACCESS_FINE_LOCATION})
    private Location getLastKnownLocationForProvider(String provider) {
        try {
            if (mLocationManager.isProviderEnabled(provider)) {
                return mLocationManager.getLastKnownLocation(provider);
            }
        } catch (Exception e) {
            Log.e("GeoLocationManager", "Failed to get last known location", e);
        }
        return null;
    }
}
