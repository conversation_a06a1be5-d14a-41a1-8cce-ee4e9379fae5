package com.snapinspect.snapinspect3.Helper;

import android.database.AbstractCursor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Simple mock cursor implementation for converting Room views to legacy objects
 */
public class MockCursor extends AbstractCursor {
    private final List<String> columnNames = new ArrayList<>();
    private final Map<String, Object> values = new HashMap<>();
    private boolean isAtFirst = false;
    
    public void addColumn(String name, Object value) {
        if (!columnNames.contains(name)) {
            columnNames.add(name);
        }
        values.put(name, value);
    }
    
    @Override
    public int getCount() {
        return 1;
    }
    
    @Override
    public String[] getColumnNames() {
        return columnNames.toArray(new String[0]);
    }
    
    @Override
    public int getColumnIndex(String columnName) {
        int index = columnNames.indexOf(columnName);
        return index >= 0 ? index : -1;
    }
    
    @Override
    public int getColumnIndexOrThrow(String columnName) {
        int index = getColumnIndex(columnName);
        if (index < 0) {
            throw new IllegalArgumentException("Column '" + columnName + "' does not exist");
        }
        return index;
    }
    
    @Override
    public String getString(int columnIndex) {
        if (columnIndex < 0 || columnIndex >= columnNames.size()) return "";
        String columnName = columnNames.get(columnIndex);
        Object value = values.get(columnName);
        return value != null ? value.toString() : "";
    }
    
    @Override
    public int getInt(int columnIndex) {
        if (columnIndex < 0 || columnIndex >= columnNames.size()) return 0;
        String columnName = columnNames.get(columnIndex);
        Object value = values.get(columnName);
        if (value instanceof Integer) {
            return (Integer) value;
        }
        return 0;
    }
    
    @Override
    public long getLong(int columnIndex) {
        if (columnIndex < 0 || columnIndex >= columnNames.size()) return 0L;
        String columnName = columnNames.get(columnIndex);
        Object value = values.get(columnName);
        if (value instanceof Long) {
            return (Long) value;
        } else if (value instanceof Integer) {
            return ((Integer) value).longValue();
        }
        return 0L;
    }
    
    @Override
    public short getShort(int columnIndex) {
        return (short) getInt(columnIndex);
    }
    
    @Override
    public float getFloat(int columnIndex) {
        return (float) getInt(columnIndex);
    }
    
    @Override
    public double getDouble(int columnIndex) {
        return (double) getInt(columnIndex);
    }
    
    @Override
    public boolean isNull(int columnIndex) {
        if (columnIndex < 0 || columnIndex >= columnNames.size()) return true;
        String columnName = columnNames.get(columnIndex);
        Object value = values.get(columnName);
        return value == null;
    }
    
    @Override
    public boolean moveToFirst() {
        isAtFirst = true;
        return true;
    }
    
    @Override
    public boolean moveToNext() {
        if (!isAtFirst) {
            isAtFirst = true;
            return true;
        }
        return false;
    }
}