package com.snapinspect.snapinspect3.Helper;
import com.snapinspect.snapinspect3.IF_Object.ai_Assets;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.database.RoomDatabaseManager;
import com.snapinspect.snapinspect3.database.entities.Assets;
import com.snapinspect.snapinspect3.app.App;

import java.util.List;

/**
 * Created by Terry<PERSON> on 2/08/17.
 */

public class CommonDB_Assets {
    
    /**
     * Get Room Database Manager instance
     */
    private static RoomDatabaseManager getRoomManager() {
        return RoomDatabaseManager.getInstance(App.getContext());
    }
    
    /**
     * Sugar ORM version - kept for backward compatibility
     */
    public static ai_Assets GetAssetBy_iSAssetID(int iSAssetID){
        try{
            List<ai_Assets> lsAssets = ai_Assets.find(ai_Assets.class, "I_S_Asset_ID = ?", "" + iSAssetID);
            if (lsAssets != null && lsAssets.size() == 1) {
                ai_Assets oAsset  = lsAssets.get(0);
                return oAsset;
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Assets.GetAssetBy_iSAssetID", ex);
        }
        return null;
    }
    
    /**
     * Room version - new implementation using Room database
     */
    public static Assets GetAssetBy_iSAssetID_Room(int iSAssetID) {
        try {
            return getRoomManager().getDatabase().assetsDao().getAssetByServerId(iSAssetID);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Assets.GetAssetBy_iSAssetID_Room", ex);
            return null;
        }
    }
    
    /**
     * Get all assets using Room
     */
    public static List<Assets> GetAllAssetsRoom() {
        try {
            return getRoomManager().getDatabase().assetsDao().getAllAssets();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Assets.GetAllAssetsRoom", ex);
            return new java.util.ArrayList<>();
        }
    }
}
