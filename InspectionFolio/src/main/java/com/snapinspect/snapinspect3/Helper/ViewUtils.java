package com.snapinspect.snapinspect3.Helper;

import android.annotation.SuppressLint;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.Typeface;

import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;

import androidx.annotation.NonNull;

import com.google.android.material.bottomnavigation.BottomNavigationItemView;
import com.google.android.material.bottomnavigation.BottomNavigationMenuView;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.google.android.material.bottomnavigation.LabelVisibilityMode;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

public final class ViewUtils {

    public static Point convertPoint(Point fromPoint, View fromView, View toView) {
        int[] fromCoordinates = new int[2];
        int[] toCoordinates = new int[2];

        fromView.getLocationOnScreen(fromCoordinates);
        toView.getLocationOnScreen(toCoordinates);

        return new Point(
                fromCoordinates[0] - toCoordinates[0] + fromPoint.x,
                fromCoordinates[1] - toCoordinates[1] + fromPoint.y
            );
    }

    public static Rect convertRect(Rect fromRect, View fromView, View toView) {
        int[] fromCoordinates = new int[2];
        int[] toCoordinates = new int[2];

        fromView.getLocationOnScreen(fromCoordinates);
        toView.getLocationOnScreen(toCoordinates);

        int xShift = fromCoordinates[0] - toCoordinates[0];
        int yShift = fromCoordinates[1] - toCoordinates[1];

        return new Rect(
                fromRect.left + xShift,
                fromRect.top + yShift,
                fromRect.right + xShift,
                fromRect.bottom + yShift
            );
    }

    public static Bitmap getLetterTile(
            final String displayLetter,
            final int textSize,
            final int textColor,
            final int backgroundColor,
            final int width, final int height
    ) {
        if (StringUtils.isEmpty(displayLetter)) { return null; }

        Paint textPaint = new Paint(Paint.ANTI_ALIAS_FLAG);
        textPaint.setColor(textColor);
        textPaint.setStyle(Paint.Style.FILL);
        textPaint.setTypeface(Typeface.DEFAULT);
        textPaint.setAntiAlias(true);
        textPaint.setFilterBitmap(true);
        textPaint.setDither(true);

        textPaint.setTextSize(textSize);

        Rect bounds = new Rect();
        textPaint.getTextBounds(displayLetter, 0, displayLetter.length(), bounds);

        int textWidth = bounds.width(), textHeight = bounds.height();
        int drawStartX = (width - textWidth) /2, drawStartY = (height + textHeight) /2;

        Paint circlePaint = new Paint();
        circlePaint.setColor(backgroundColor);

        Bitmap bitmap = Bitmap.createBitmap(width, height, Bitmap.Config.ARGB_8888);
        Canvas canvas = new Canvas(bitmap);
        int minEdge = Math.min(width, height);
        canvas.drawCircle(width/2, height/2, minEdge/2, circlePaint);
        canvas.drawText(displayLetter, drawStartX, drawStartY, textPaint);

        return bitmap;
    }

    @SuppressLint("RestrictedApi")
    public static void disableShiftMode(BottomNavigationView nav) {
        BottomNavigationMenuView menuView = (BottomNavigationMenuView) nav.getChildAt(0);
        try {
            Field shiftingMode = menuView.getClass().getDeclaredField("mShiftingMode");
            shiftingMode.setAccessible(true);
            shiftingMode.setBoolean(menuView, false);
            shiftingMode.setAccessible(false);
            for (int i = 0; i < menuView.getChildCount(); i++) {
                BottomNavigationItemView item = (BottomNavigationItemView) menuView.getChildAt(i);
                item.setLabelVisibilityMode(LabelVisibilityMode.LABEL_VISIBILITY_LABELED);
                item.setChecked(item.getItemData().isChecked());
            }
        } catch (Exception e) {

        }
    }

    public static <T extends View> ArrayList<T> getChildrenWithClass(ViewGroup parent, Class<T> clazz) {
        ArrayList<T> children = new ArrayList<>();

        int childCount = parent.getChildCount();
        for (int i = 0; i < childCount; i++) {
            View child = parent.getChildAt(i);
            if (child instanceof ViewGroup) {
                children.addAll(getChildrenWithClass((ViewGroup) child, clazz));
                if (child.getClass().equals(clazz)) {
                    children.add((T) child);
                }
            }
        }

        return children;
    }

    public static void setViewWidth(View view, int width) {
        ViewGroup.LayoutParams params = view.getLayoutParams();
        params.width = width;
        view.setLayoutParams(params);
    }

    public static void setViewHeight(View view, int height) {
        ViewGroup.LayoutParams params = view.getLayoutParams();
        params.height = height;
        view.setLayoutParams(params);
    }

    public static void setViewSize(View view, int width, int height) {
        ViewGroup.LayoutParams params = view.getLayoutParams();
        params.width = width;
        params.height = height;
        view.setLayoutParams(params);
    }

    public static void setViewBottomMargin(View view, int margin) {
        if (view.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
            if (layoutParams.bottomMargin != margin) {
                layoutParams.bottomMargin = margin;
                view.setLayoutParams(layoutParams);
            }
        }
    }

    public static void setViewTopMargin(View view, int margin) {
        if (view.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
            if (layoutParams.topMargin != margin) {
                layoutParams.topMargin = margin;
                view.setLayoutParams(layoutParams);
            }
        }
    }

    public static void setViewMargins(View view, int left, int top, int right, int bottom) {
        if (view.getLayoutParams() instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams layoutParams = (ViewGroup.MarginLayoutParams) view.getLayoutParams();
            if (layoutParams.topMargin != top) layoutParams.topMargin = top;
            if (layoutParams.leftMargin!= left) layoutParams.leftMargin = left;
            if (layoutParams.rightMargin != right) layoutParams.rightMargin = right;
            if (layoutParams.bottomMargin!= bottom) layoutParams.bottomMargin = bottom;

            view.setLayoutParams(layoutParams);
        }
    }

    public static void fade(final View view, long duration, float startAlpha, float endAlpha) {
        view.setVisibility(View.VISIBLE);
        AlphaAnimation anim = new AlphaAnimation(startAlpha, endAlpha);
        anim.setDuration(duration);
        anim.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) {
            }

            @Override
            public void onAnimationEnd(Animation animation) {
                view.setVisibility(View.INVISIBLE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) { }
        });
        view.startAnimation(anim);
    }

    public static void setVisible(@NonNull View view, boolean isVisible) {
        view.setVisibility(isVisible ? VISIBLE : GONE);
    }
}
