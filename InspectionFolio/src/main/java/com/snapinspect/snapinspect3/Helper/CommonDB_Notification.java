package com.snapinspect.snapinspect3.Helper;

import com.snapinspect.snapinspect3.IF_Object.ai_NoticeCategory;
import com.snapinspect.snapinspect3.IF_Object.ai_Notification;

import java.util.List;

public final class CommonDB_Notification {

    public static List<ai_NoticeCategory>getAllNoticeCategories () {
        return CommonDB.findNoticeCategories("B_DELETED=?", "0");
    }

    public static ai_Notification getNotification(long iNoticeID) {
        return CommonDB.getNotificationById(iNoticeID);
    }

    public static List<ai_Notification> getNotifications(int iInsID, int iInsItemID) {
        return iInsItemID > 0 ?
                CommonDB.findNotifications("I_Ins_ID = ? and I_Ins_Item_ID = ? and B_Deleted = 0", "" + iInsID, "" + iInsItemID) :
                CommonDB.findNotifications("I_Ins_ID = ? and B_Deleted = 0", "" + iInsID);
    }
}
