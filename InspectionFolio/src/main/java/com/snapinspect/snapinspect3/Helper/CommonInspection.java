package com.snapinspect.snapinspect3.Helper;

import android.app.Activity;
import android.content.Context;

import android.os.Handler;
import android.os.Looper;
import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
// Removed: import com.orm.SugarRecord;
// Removed: import com.orm.query.Condition;
// Removed: import com.orm.query.Select;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.*;

import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Ins_3rd;
import com.snapinspect.snapinspect3.util.StringUtils;
import kotlin.Unit;
import kotlinx.coroutines.Dispatchers;

import org.apache.http.Header;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_CONTROL_TYPE_PTO;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_CONTROL_TYPE_SCAN;

/**
 * Created by TerryS on 8/09/17.
 */

public class CommonInspection {
    public static  void DeleteInspection(ai_Inspection oInspection, Context oContext) {
        try {
            try {
                List<ai_Photo> lsPhoto = CommonDB.findPhotos("I_INS_ID = ? and B_DELETED = 0", String.valueOf(oInspection.getId()));
                for (ai_Photo oPhoto : lsPhoto) {
                    try {
                        if (!oPhoto.bUploaded || oPhoto.iSPhotoID == 0) continue;
                        CommonHelper.DeleteFile(oPhoto.getThumb());
                        CommonHelper.DeleteFile(oPhoto.getFile());
                        oPhoto.bDeleted = true;
                        CommonDB.savePhoto(oPhoto);
                    } catch (Exception e) {
                        ai_BugHandler.ai_Handler_Exception("Exception", "CommonInspection.DeleteInspection.Photo", e, oContext);
                    }
                }
            } catch (Exception eee) {
                String cc = "bb";
            }
            try {
                List<ai_Video> lsVideo = CommonDB.findVideos("I_INS_ID = ? and B_DELETED = 0", String.valueOf(oInspection.getId()));
                for (ai_Video oVideo : lsVideo) {
                    try {
                        if (!oVideo.bUploaded || oVideo.iSVideoID == 0) continue;
                        CommonHelper.DeleteFile(oVideo.getThumb());
                        CommonHelper.DeleteFile(oVideo.getFile());
                        oVideo.bDeleted = true;
                        CommonDB.saveVideo(oVideo);
                    } catch (Exception e) {
                        ai_BugHandler.ai_Handler_Exception("Exception", "CommonInspection.DeleteInspection.Video", e, oContext);
                    }
                }
            } catch (Exception eee) {
                String cc = "bb";
            }
            /*try{
            List<ai_InsItem> lsInsItem = ai_InsItem.find(ai_InsItem.class, "I_INS_ID = ? and B_DELETED = 0", "" + iInspectionID);
            for (ai_InsItem oInsItem : lsInsItem) {
                oInsItem.bDeleted = true;
                CommonDB.saveInsItem(oInsItem);
            }*/
            try {
                db_InsItem.BulkClearInsItem(oInspection.getId().intValue());
                db_ProductCost.deleteAllCosts(oInspection.getId());
            } catch (Exception eee) {
                String cc = "bb";
            }
            //ai_Inspection oTemp = ai_Inspection.findById(ai_Inspection.class, (long)iInspectionID);
            if (oInspection.iSInsID <= 0) {
                oInspection.bDeleted = true;
                CommonDB.saveInspection(oInspection);
            } else {
                oInspection.bComplete = true;
                oInspection.bSynced = true;
                CommonDB.saveInspection(oInspection);
            }

            //oTemp.bDeleted = true;
            //oTemp.update();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_InsRoomHelper.DeleteInspection", ex, oContext);
        }
        //ai_Inspection oInspection = ai_Inspection.executeQuery();
    }

    public static void ReconsolidateInspectionWithServer(JSONObject oOb){
        try {
            int iInsID = oOb.getInt("iInspectionID");
            int iSAssetID = oOb.getInt("iPropertyID");
           // String sPTC = "";
            int iInsTypeID = oOb.getInt("iInsTypeID");

          //  for (int ii = 0; ii < lsInsTypes.size(); ii++) {
          //      if (iInsTypeID == lsInsTypes.get(ii).iSInsTypeID) {
          //          ai_InsType aType = lsInsTypes.get(ii);
          //          if (aType != null || aType.iSInsTypeID > 0) {
          //              sPTC = lsInsTypes.get(ii).sPTC;
          //          }
          //      }
         //   }

            String sType = oOb.getString("sType");
            String sInsTitle = oOb.getString("sInsTitle");
            String sTitle = oOb.getString("sTitle");
            String sComments = "";
            String dtStartDate = oOb.getString("dtStart");
            String dtEndDate = oOb.getString("dtEnd");
            String sLat = "";
            String sLong = "";
            String sAddress1 = oOb.getString("sAddress1");
            String sAddress2 = oOb.getString("sAddress2");
            String sCustom1 = oOb.getString("sCustom1");
            String sCustom2 = oOb.getString("sCustom2");
            int iSScheduleID = 0;
            //ai_Inspection oIns = new ai_Inspection();
            ai_Inspection oIns = db_Inspection.GetInspection_BySInsID(iInsID);

            if (oIns == null || oIns.iSInsID <= 0) {
                ai_InsType oInsType = db_Inspection.GetInsTypeBySInsTypeID(iInsTypeID);
                oIns = new ai_Inspection(iInsTypeID, iSAssetID, oInsType == null ? "" : oInsType.sPTC, sType, sInsTitle, sTitle, sComments,
                        dtStartDate, dtEndDate, sLat, sLong, sAddress1, sAddress2, iSScheduleID, sCustom1, sCustom2);
                oIns.bComplete = true;
                oIns.bDeleted = false;
                oIns.bSynced = true;
                oIns.iSInsID = iInsID;
                CommonDB.saveInspection(oIns);
            }
            else if ((oIns.bSynced && oIns.bComplete) || oIns.bDeleted){

               // oIns.sType = sType;
                oIns.iSAssetID = iSAssetID;
                oIns.iSInsTypeID = iInsTypeID;
                oIns.sType = sType;
                oIns.sInsTitle = sInsTitle;
                oIns.sTitle = sTitle;
                oIns.sComments = sComments;
                oIns.dtStartDate = dtStartDate;
                oIns.dtEndDate = dtEndDate;
                oIns.sLat = sLat;
                oIns.sLong = sLong;
                oIns.sAddressOne = sAddress1;
                oIns.sAddressTwo = sAddress2;
                oIns.iSScheduleID = iSScheduleID;
                oIns.sCustomOne = sCustom1;
                oIns.sCustomTwo = sCustom2;
                oIns.bComplete = true;
                oIns.bDeleted = false;
                oIns.bSynced = true;
                oIns.iSInsID = iInsID;
                CommonDB.saveInspection(oIns);
                //oIns.save();
            }




           /* boolean contained = false;
            for (int j = 0; j < lsUploadedInspection.size(); j++) {
                ai_Inspection tmpIns = lsUploadedInspection.get(j);
                if (tmpIns.iSInsID == oIns.iSInsID) {
                    contained = true;
                    oIns.setId(tmpIns.getId());
                    break;
                }
            }
            //  if (!contained) {
            CommonDB.saveInspection(oIns);
            lsUploadedInspection.add(oIns);*/
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception_New("Exception", "CommonInspection.ReconsolidateInspectionWithServer", ex);
        }
    }
    public static int CopyInspection(JSONObject response, boolean bMediaTransfer, ai_InsType oInsType, Context oContext){


                                  /*  new Handler().post(new Runnable() {
                                        @Override
                                        public void run() {*/
        try {
            // oDialog = ProgressDialog.show(if_existasset.this, "Message", "Prepare Inspection ...");
            // oDialog.setCancelable(false);
            // oDialog.setCanceledOnTouchOutside(false);
            final JSONObject oItems = response.getJSONObject("oInspection");
            //oDialog.show();
            ai_Inspection aIns = new ai_Inspection();
            aIns.iSInsTypeID = oInsType.iSInsTypeID;
            aIns.iSAssetID = oItems.getInt("iPropertyID");
            aIns.sPTC = oInsType.sPTC;
            aIns.sType = oInsType.sType;
            aIns.sInsTitle = oInsType.sInsTitle;
            aIns.sTitle = oItems.getString("sTitle");

            Date oNow = new Date();
            aIns.dtStartDate = CommonHelper.sDateToString(oNow);
            aIns.sAddressOne = oItems.getString("sAddress1");
            aIns.sAddressTwo = oItems.getString("sAddress2");
            aIns.iSScheduleID = 0;
            try {
                aIns.sCustomTwo = CommonJson.AddJsonKeyValue("", "_iCopyInsID", "" + oItems.getInt("iInspectionID"));
            } catch (Exception eeee) {

            }
            boolean bReview = false;
            try {
                String sReview = CommonJson.GetJsonKeyValue("_bReview", oItems.getString("sCustom2"));
                if (sReview != null && sReview.trim().equalsIgnoreCase("1")) {
                    bReview = true;
                    aIns.sCustomTwo = CommonJson.AddJsonKeyValue(aIns.sCustomTwo, "_bReview", "1");
                }
            } catch (Exception eeee) {

            }
            CommonDB.saveInspection(aIns);
            Long iInspectionID = aIns.getId();

            JSONArray arrInsItem = response.getJSONArray("lsInsItem");
            ArrayList<JSONObject> arrParent = new ArrayList<JSONObject>();
            for (int i = 0; i < arrInsItem.length(); i++) {
                JSONObject oOb = arrInsItem.getJSONObject(i);
                if (oOb.getInt("iPInsItemID") == 0) {
                    arrParent.add(oOb);
                }
            }

            for (int i = 0; i < arrParent.size(); i++) {
                JSONObject oOb = arrParent.get(i);
                ai_InsItem oItem = new ai_InsItem();
                oItem.iPInsItemID = 0;
                oItem.iInsID = iInspectionID.intValue();
                oItem.iSLayoutID = oOb.optInt("iLayoutID", 0);
                oItem.sName = oOb.optString("sName", "");
                oItem.sValueOne = oOb.optString("sValue1", "");
                oItem.sValueTwo = oOb.optString("sValue2", "");
                oItem.sValueThree = oOb.optString("sValue3", "");
                oItem.sValueFour = oOb.optString("sValue4", "");
                oItem.sValueFive = oOb.optString("sValue5", "");
                oItem.sValueSix = oOb.optString("sValue6", "");
                oItem.sQType = oOb.optString("sQType", "");
                oItem.sConfigOne = oOb.optString("sConfig1", "");
                oItem.sConfigTwo = oOb.optString("sConfig2", "");
                oItem.sConfigThree = oOb.optString("sConfig3", "");
                oItem.sConfigFour = oOb.optString("sConfig4", "");
                oItem.sConfigFive = oOb.optString("sConfig5", "");
                oItem.sConfigSix = oOb.optString("sConfig6", "");
                oItem.sConfig = oOb.optString("sConfig", "");
                oItem.iSort = oOb.optInt("iOrder", 0);
                oItem.iSAssetLayoutID = 0;
                oItem.sNameChanged = "";
                if (bReview) {
                    try {
                        oItem.sCustomOne = CommonJson.AddJsonKeyValue("", "_iCopyInsItemID", "" + oOb.getInt("iInsItemID"));
                    } catch (Exception eeee) {

                    }
                }

                CommonDB.saveInsItem(oItem);
                int iPInsItemID = oItem.getId().intValue();
                int iSPInsItemID = oOb.getInt("iInsItemID");

                for (int j = 0; j < arrInsItem.length(); j++) {
                    JSONObject oTempChild = arrInsItem.getJSONObject(j);
                    int iSInsItemID = oTempChild.getInt("iPInsItemID");
                    if (iSInsItemID == iSPInsItemID) {
                        ai_InsItem cInsItem = new ai_InsItem();
                        cInsItem.iPInsItemID = iPInsItemID;
                        cInsItem.iInsID = iInspectionID.intValue();
                        cInsItem.iSLayoutID = oTempChild.optInt("iLayoutID", 0);
                        cInsItem.sName = oTempChild.optString("sName", "");
                        cInsItem.sValueOne = oTempChild.optString("sValue1", "");
                        cInsItem.sValueTwo = oTempChild.optString("sValue2", "");
                        cInsItem.sValueThree = oTempChild.optString("sValue3", "");
                        cInsItem.sValueFour = oTempChild.optString("sValue4", "");
                        cInsItem.sValueFive = oTempChild.optString("sValue5", "");
                        cInsItem.sValueSix = oTempChild.optString("sValue6", "");
                        cInsItem.sQType = oTempChild.optString("sQType", "");
                        cInsItem.sConfigOne = oTempChild.optString("sConfig1", "");
                        cInsItem.sConfigTwo = oTempChild.optString("sConfig2", "");
                        cInsItem.sConfigThree = oTempChild.optString("sConfig3", "");
                        cInsItem.sConfigFour = oTempChild.optString("sConfig4", "");
                        cInsItem.sConfigFive = oTempChild.optString("sConfig5", "");
                        cInsItem.sConfigSix = oTempChild.optString("sConfig6", "");
                        cInsItem.sConfig = oTempChild.optString("sConfig", "");
                        cInsItem.iSort = oTempChild.optInt("iOrder", 0);
                        cInsItem.iSAssetLayoutID = 0;
                        cInsItem.sNameChanged = "";
                        if (bReview) {
                            try {
                                cInsItem.sCustomOne = CommonJson.AddJsonKeyValue("", "_iCopyInsItemID", "" + oTempChild.getInt("iInsItemID"));
                            } catch (Exception eeee) {

                            }
                        }
                        cInsItem.sCustomTwo = oTempChild.optString("sCustom2", "");
                        CommonDB.saveInsItem(cInsItem);
                    }
                }
            }
            if (bMediaTransfer) {
                JSONArray arrPhoto = response.getJSONArray("lsPhoto");

                for (int i = 0; i < arrPhoto.length(); i++) {
                    JSONObject oTemp = arrPhoto.getJSONObject(i);
                    ai_Photo oPhoto = new ai_Photo();
                    oPhoto.iInsID = iInspectionID.intValue();
                    oPhoto.iInsItemID = 0;
                    oPhoto.sThumb = "";
                    oPhoto.sFile = "";
                    oPhoto.sComments = oTemp.optString("sPhotoComment", "");
                    oPhoto.bDeleted = false;
                    oPhoto.dtDateTime = oTemp.optString("dtCreate", "");
                    oPhoto.iSPhotoID = oTemp.optInt("iPhotoID", 0);
                    oPhoto.iSize = oTemp.optInt("iSize", 0);
                    oPhoto.bUploaded = true;
                    CommonDB.savePhoto(oPhoto);
                }
                DuplicateInspection_ProcessPhoto(iInspectionID.intValue());
            }
            return aIns.getId().intValue();
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.duplicateInspection.3", ex, oContext);
        }
        return 0;
    }
    public static int createOrUpdateInspection(JSONObject response, Context oContext){
        try{
            JSONObject oInspectionJson = response.getJSONObject("oInspection");
            CommonInspection.ReconsolidateInspectionWithServer(oInspectionJson);

            int iSInsID = oInspectionJson.getInt("iInspectionID");
            ai_Inspection oInspection = db_Inspection.GetInspection_BySInsID(iSInsID);
            if (oInspection == null) return -1;

            int iInspectionID = oInspection.getId().intValue();
            JSONArray arrInsItem = response.getJSONArray("lsInsItem");
            ArrayList<JSONObject> arrParent = new ArrayList<>();
            for (int i = 0; i < arrInsItem.length(); i++) {
                JSONObject oOb = arrInsItem.getJSONObject(i);
                if (oOb.getInt("iPInsItemID") == 0) {
                    arrParent.add(oOb);
                }
            }

            for (int i = 0; i < arrParent.size(); i++) {
                JSONObject oOb = arrParent.get(i);
                ai_InsItem oItem = new ai_InsItem();
                oItem.iPInsItemID = 0;
                oItem.iInsID = iInspectionID;
                int iSPInsItemID = oOb.getInt("iInsItemID");
                oItem.iSLayoutID = oOb.optInt("iLayoutID", 0);
                oItem.sName = oOb.optString("sName", "");
                oItem.sValueOne = oOb.optString("sValue1", "");
                oItem.sValueTwo = oOb.optString("sValue2", "");
                oItem.sValueThree = oOb.optString("sValue3", "");
                oItem.sValueFour = oOb.optString("sValue4", "");
                oItem.sValueFive = oOb.optString("sValue5", "");
                oItem.sValueSix = oOb.optString("sValue6", "");
                oItem.sQType = oOb.optString("sQType", "");
                oItem.sConfigOne = oOb.optString("sConfig1", "");
                oItem.sConfigTwo = oOb.optString("sConfig2", "");
                oItem.sConfigThree = oOb.optString("sConfig3", "");
                oItem.sConfigFour = oOb.optString("sConfig4", "");
                oItem.sConfigFive = oOb.optString("sConfig5", "");
                oItem.sConfigSix = oOb.optString("sConfig6", "");
                oItem.sConfig = oOb.optString("sConfig", "");
                oItem.iSort = oOb.optInt("iOrder", 0);
                oItem.iSAssetLayoutID = 0;
                oItem.sNameChanged = "";
                oItem.sCustomOne = CommonJson.AddJsonKeyValue(oItem.sCustomOne, "_iInsItemID", "" + iSPInsItemID);

                CommonDB.saveInsItem(oItem);
                int iPInsItemID = oItem.getId().intValue();


                for (int j = 0; j < arrInsItem.length(); j++) {
                    JSONObject oTempChild = arrInsItem.getJSONObject(j);
                    int iChild_SPInsItemID = oTempChild.getInt("iPInsItemID");
                    if (iChild_SPInsItemID == iSPInsItemID) {
                        int iSInsItemID = oTempChild.getInt("iInsItemID");
                        ai_InsItem cInsItem = new ai_InsItem();
                        cInsItem.iPInsItemID = iPInsItemID;
                        cInsItem.iInsID = iInspectionID;
                        cInsItem.iSLayoutID = oTempChild.optInt("iLayoutID", 0);
                        cInsItem.sName = oTempChild.optString("sName", "");
                        cInsItem.sValueOne = oTempChild.optString("sValue1", "");
                        cInsItem.sValueTwo = oTempChild.optString("sValue2", "");
                        cInsItem.sValueThree = oTempChild.optString("sValue3", "");
                        cInsItem.sValueFour = oTempChild.optString("sValue4", "");
                        cInsItem.sValueFive = oTempChild.optString("sValue5", "");
                        cInsItem.sValueSix = oTempChild.optString("sValue6", "");
                        cInsItem.sQType = oTempChild.optString("sQType", "");
                        cInsItem.sConfigOne = oTempChild.optString("sConfig1", "");
                        cInsItem.sConfigTwo = oTempChild.optString("sConfig2", "");
                        cInsItem.sConfigThree = oTempChild.optString("sConfig3", "");
                        cInsItem.sConfigFour = oTempChild.optString("sConfig4", "");
                        cInsItem.sConfigFive = oTempChild.optString("sConfig5", "");
                        cInsItem.sConfigSix = oTempChild.optString("sConfig6", "");
                        cInsItem.sConfig = oTempChild.optString("sConfig", "");
                        cInsItem.iSort = oTempChild.optInt("iOrder", 0);
                        cInsItem.iSAssetLayoutID = 0;
                        cInsItem.sNameChanged = "";
                        cInsItem.sCustomOne = CommonJson.AddJsonKeyValue(cInsItem.sCustomOne, "_iInsItemID", "" + iSInsItemID);
                        cInsItem.sCustomTwo = oTempChild.optString("sCustom2", "");
                        CommonDB.saveInsItem(cInsItem);
                    }
                }
            }
            // if (bMediaTransfer) {
            JSONArray arrPhoto = response.getJSONArray("lsPhoto");

            for (int i = 0; i < arrPhoto.length(); i++) {
                JSONObject oTemp = arrPhoto.getJSONObject(i);
                ai_Photo oPhoto = new ai_Photo();
                oPhoto.iInsID =  iInspectionID;
                oPhoto.iInsItemID = 0;
                oPhoto.sThumb = "";
                oPhoto.sFile = "";
                oPhoto.sComments = oTemp.optString("sPhotoComment", "");
                oPhoto.bDeleted = false;
                oPhoto.dtDateTime = oTemp.optString("dtCreate", "");
                oPhoto.iSPhotoID = oTemp.optInt("iPhotoID", 0);
                oPhoto.iSize = oTemp.optInt("iSize", 0);
                oPhoto.bUploaded = true;
                CommonDB.savePhoto(oPhoto);
            }
            DuplicateInspection_ProcessPhoto(iInspectionID);
            JSONArray arrVideo = response.getJSONArray("lsVideo");
            for (int i = 0; i < arrVideo.length(); i++) {
                JSONObject oTemp = arrVideo.getJSONObject(i);
                ai_Video oVideo = new ai_Video();

                oVideo.iInsID =  iInspectionID;
                oVideo.iSVideoID = oTemp.optInt("iVideoID", 0);
                oVideo.iInsItemID = 0;
                oVideo.sThumb = "";
                oVideo.sFile = "";
                oVideo.sSThumb = oTemp.optString("sThumbURL", "");
                oVideo.sSFile = oTemp.optString("sVideoURL", "");
                oVideo.bDeleted = false;
                //  oPhoto.dtDateTime = oTemp.isNull("dtCreate") ? "" :oTemp.getString("dtCreate");
                // oPhoto.iSPhotoID = oTemp.isNull("iPhotoID") ? 0 :oTemp.getInt("iPhotoID");
                //  oPhoto.iSize = oTemp.isNull("iSize") ? 0 :oTemp.getInt("iSize");

                oVideo.bUploaded = true;
                oVideo.bProcessed = true;
                oVideo.bGetURL = true;
                oVideo.iSize = oTemp.optInt("iVideoSize", 0);
                CommonDB.saveVideo(oVideo);
            }

            CommonInspection.DuplicateInspection_ProcessVideo(iInspectionID);
            oInspection.bSynced = false;
            oInspection.bComplete = false;
            oInspection.bDeleted = false;
            CommonDB.saveInspection(oInspection);
            return iInspectionID;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.editinspection.3", ex, oContext);
        }
        return 0;
    }
    private static String GetPhotoSequenceID(String sValue, int iInsItemID, int iInsID) {
        try {
            if (sValue != null) {
                List<String> arrList = Arrays.asList(sValue.trim().split(","));
                ArrayList<String> oFinal = new ArrayList<>();
                for (int i = 0; i<arrList.size(); i++) {
                    String cc = arrList.get(i);
                    try {
                        if (cc!= null && cc.trim().length() > 0 && Integer.valueOf(cc) > 0) {
                            int isphotoid = Integer.valueOf(cc);
                            ai_Photo oPhoto = db_Media.GetPhotoByServerPhotoID(isphotoid, iInsID);
                            if (oPhoto != null && oPhoto.getId() > 0) {
                                oPhoto.iInsItemID = iInsItemID;
                                CommonDB.savePhoto(oPhoto);
                                oFinal.add(String.valueOf(oPhoto.getId()));
                            }


                        }
                    } catch (Exception ex) {
                       // ex.printStackTrace();
                    }
                }
                return convertArraytoString(oFinal);
            } else {
                return "";
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return "";
    }
    private static String convertArraytoString(ArrayList<String> arr) {
        StringBuilder builder = new StringBuilder();
        for(int i = 0; i < arr.size(); i++) {
            String s = arr.get(i);
            builder.append(s);
            if (i < arr.size() - 1) {
                builder.append(",");
            }
        }
        return builder.toString();
    }
    private static boolean TestIsPhotoType(String sConfig) {
        int sControlType = CommonInsItem.getControlType(sConfig);
        return sControlType == SI_CONTROL_TYPE_PTO || sControlType == SI_CONTROL_TYPE_SCAN;
    }
    private static void ProcessInsItem_Photo(ai_InsItem oInsItem, String sConfig1,  String sConfig2, String sConfig3,  String sConfig4, String sConfig5,  String sConfig6) {
        if (oInsItem.sQType.equalsIgnoreCase("S")){
            oInsItem.sValueOne = GetPhotoSequenceID(oInsItem.sValueOne, oInsItem.getId().intValue(), oInsItem.iInsID);
        }
        else {

            if (TestIsPhotoType(sConfig1)) {
                oInsItem.sValueOne = GetPhotoSequenceID(oInsItem.sValueOne, oInsItem.getId().intValue(), oInsItem.iInsID);
                // UpdateInsItemValue(oInsItem);
            }
            if (TestIsPhotoType(sConfig2)) {
                oInsItem.sValueTwo = GetPhotoSequenceID(oInsItem.sValueTwo, oInsItem.getId().intValue(), oInsItem.iInsID);
                // UpdateInsItemValue(oInsItem);
            }
            if (TestIsPhotoType(sConfig3)) {
                oInsItem.sValueThree = GetPhotoSequenceID(oInsItem.sValueThree, oInsItem.getId().intValue(), oInsItem.iInsID);
                //  UpdateInsItemValue(oInsItem);
            }
            if (TestIsPhotoType(sConfig4)) {
                oInsItem.sValueFour = GetPhotoSequenceID(oInsItem.sValueFour, oInsItem.getId().intValue(), oInsItem.iInsID);
                //  UpdateInsItemValue(oInsItem);
            }
            if (TestIsPhotoType(sConfig5)) {
                oInsItem.sValueFive = GetPhotoSequenceID(oInsItem.sValueFive, oInsItem.getId().intValue(), oInsItem.iInsID);
                //  UpdateInsItemValue(oInsItem);
            }
            if (TestIsPhotoType(sConfig6)) {
                oInsItem.sValueSix = GetPhotoSequenceID(oInsItem.sValueSix, oInsItem.getId().intValue(), oInsItem.iInsID);
                //  UpdateInsItemValue(oInsItem);
            }
        }
        CommonDB.saveInsItem(oInsItem);
    }
    private static void DuplicateInspection_ProcessPhoto(int iInsID) {
        try {
            List<ai_InsItem> InsItemArray = CommonDB.findInsItemsWithQuery("I_INS_ID = ?", String.valueOf(iInsID));
            for (int i = 0; i<InsItemArray.size(); i++) {
                ai_InsItem aiInsItem = InsItemArray.get(i);
                String sPConfig1 = aiInsItem.sConfigOne;
                String sPConfig2 = aiInsItem.sConfigTwo;
                String sPConfig3 = aiInsItem.sConfigThree;
                String sPConfig4 = aiInsItem.sConfigFour;
                String sPConfig5 = aiInsItem.sConfigFive;
                String sPConfig6 = aiInsItem.sConfigSix;
                ProcessInsItem_Photo(aiInsItem, sPConfig1, sPConfig2, sPConfig3, sPConfig4, sPConfig5, sPConfig6);
                List<ai_InsItem> lsChild = CommonDB.findInsItemsWithQuery("I_P_INS_ITEM_ID = ? AND I_INS_ID = ?", String.valueOf(aiInsItem.getId()), String.valueOf(iInsID));
                for (ai_InsItem oChild : lsChild) {
                    ProcessInsItem_Photo(oChild,
                            oChild.sQType.equals("P")? oChild.sConfigOne : sPConfig1,
                            oChild.sQType.equals("P")? oChild.sConfigTwo : sPConfig2,
                            oChild.sQType.equals("P")? oChild.sConfigThree : sPConfig3,
                            oChild.sQType.equals("P")? oChild.sConfigFour : sPConfig4,
                            oChild.sQType.equals("P")? oChild.sConfigFive : sPConfig5,
                            oChild.sQType.equals("P")? oChild.sConfigSix : sPConfig6);
                }
            }
        }catch (Exception ex) {
            ex.printStackTrace();
        }
    }
    private static void DuplicateInspection_ProcessVideo(int iInsID){
        try {
            List<ai_InsItem> InsItemArray = CommonDB.findInsItemsWithQuery("I_INS_ID = ?", String.valueOf(iInsID));
            for (int i = 0; i<InsItemArray.size(); i++) {
                ai_InsItem aiInsItem = InsItemArray.get(i);
                if (aiInsItem.sQType.equalsIgnoreCase("V")) {
                    List<ai_InsItem> lsChild = CommonDB.findInsItemsWithQuery("I_P_INS_ITEM_ID = ? AND I_INS_ID = ?", String.valueOf(aiInsItem.getId()), String.valueOf(iInsID));
                    for (ai_InsItem oChild : lsChild) {
                        int iSVideoID = 0;
                        try {
                            iSVideoID = Integer.parseInt(oChild.sValueOne);
                        }catch(Exception exx){

                        }
                        if (iSVideoID > 0) {
                            ai_Video oVideo = db_Media.GetVideoByServerVideoID(iSVideoID, iInsID);
                            if (oVideo != null && oVideo.getId() > 0){
                                oChild.sValueOne = "" + oVideo.getId();
                                oVideo.iInsItemID = oChild.getId();
                                CommonDB.saveVideo(oVideo);
                                CommonDB.saveInsItem(oChild);

                            }
                        }
                    }
                }
            }
        }catch (Exception ex) {
            ex.printStackTrace();
        }

    }

    public interface FetchAssetLayoutListener {
        void didFetch(List<ai_Layout> assetLayouts);
    }
    public static void fetchAssetLayoutsIfNeed(Context context, int iSAssetID, int iSInsTypeID, FetchAssetLayoutListener listener) {
        ai_InsType oInsType = db_Inspection.GetInsTypeBySInsTypeID(iSInsTypeID);
        if (oInsType == null || !CommonJson.canUseLayoutV2(context)) {
            listener.didFetch(null);
            return;
        }

        // Fetch asset layout from local database `ai_PropertyLayout`
        if (!StringUtils.isEmpty(oInsType.sPTC)) {
            List<ai_Layout> assetLayouts = db_PropertyLayout.getPropertyLayouts(oInsType.sPTC, iSAssetID);
            if (!assetLayouts.isEmpty()) {
                listener.didFetch(assetLayouts);
                return;
            }
        }

        // Check network connection before fetching asset layout from server
        if (!NetworkUtils.isNetworkAvailable(context)) {
            listener.didFetch(null);
            return;
        }

        // Fetch asset layout from server
        MaterialDialog materialDialog = CommonUI.ShowMaterialProgressDialog(context, null, "Loading...");
        CommonPropertyLayout.getAssetLayoutV3(
            Dispatchers.getIO(), context, iSAssetID, iSInsTypeID, oInsType.sPTC, (results, error) -> {
            CommonUI.DismissMaterialProgressDialog(materialDialog);
            if (error == null) {
                @SuppressWarnings("unchecked")
                List<ai_Layout> assetLayouts = (List<ai_Layout>) results;
                listener.didFetch(assetLayouts);
            } else {
                listener.didFetch(null);
            }
            return Unit.INSTANCE;
        });
    }

    public interface UpdateInspectionStatusListener {
        void didUpdate();
    }
    public static void updateInspectionStatus(Context context, int iSInsID, UpdateInspectionStatusListener listener) {
        ArrayList<String> lsOption = new ArrayList<>();
        List<ai_JsonStatus> arrStatus = null;
        try {
            arrStatus = CommonHelper.GetInspectionStatus(context);
            for (int i = 0; i < arrStatus.size(); i++) {
                try {
                    ai_JsonStatus oStatus = arrStatus.get(i);
                    lsOption.add(oStatus.sName);
                } catch (Exception exception) {
                    // ignore the one with exception
                }
            }
        } catch (Exception exception) {
            // exception.printStackTrace();
        }

        if (arrStatus == null || arrStatus.isEmpty()) {
            return;
        }

        final List<ai_JsonStatus> finalArrStatus = arrStatus;
        String[] options = lsOption.toArray(new String[0]);
        new MaterialDialog.Builder(context)
                .title("Update Status")
                .items(options)
                .itemsCallback((dialog, itemView, position, text) -> {
                    MaterialDialog oDialog;
                    try {
                        ai_JsonStatus oStatus = finalArrStatus.get(position);
                        if (NetworkUtils.isNetworkAvailable(context)) {
                            try {
                                oDialog = CommonUI.ShowMaterialProgressDialog(context, "Message", "Processing...");
                                RequestParams oParams = new RequestParams();
                                oParams.add("iCustomerID", CommonHelper.GetPreferenceString(context, "iCustomerID"));
                                oParams.add("sToken", CommonHelper.GetPreferenceString(context, "sToken"));
                                oParams.add("iInspectionID", String.valueOf(iSInsID));
                                oParams.add("sStatus", oStatus.sName);
                                oParams.add("sColorCode", oStatus.sColorCode);

                                final String sURL = "/IOAPI/UpdateInspectionStatusAPI";
                                IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                                    @Override
                                    public void onSuccess(int statusCode, Header[] headers, final JSONObject response) {
                                        CommonUI.DismissMaterialProgressDialog(oDialog);
                                        if (statusCode == 200) {
                                            try {
                                                if (response.getBoolean("success")) {
                                                    try {
                                                        // update inspection status
                                                        ai_Inspection oIns = db_Inspection.GetInspection_BySInsID(iSInsID);
                                                        String sCustom1 = response.getJSONObject("oInspection").getString("sCustom1");
                                                        if (oIns != null) {
                                                            oIns.sCustomOne = sCustom1;
                                                            CommonDB.saveInspection(oIns);
                                                        }

                                                        // Sync inspection status to project inspection with the same iSInsID
                                                        List<ai_ProjectInspection> projectInspections = db_Projects.getProjectInspections(iSInsID);
                                                        String status = CommonJson.GetJsonKeyValue("Status", sCustom1);
                                                        String insStatusCode = CommonJson.GetJsonKeyValue("S_Code", sCustom1);
                                                        for (ai_ProjectInspection projectInspection : projectInspections) {
                                                            String sCustom = CommonJson.AddJsonKeyValue(
                                                                    projectInspection.sCustom, ai_ProjectInspection.Keys.INS_STATUS, status);
                                                            sCustom = CommonJson.AddJsonKeyValue(
                                                                    sCustom, ai_ProjectInspection.Keys.INS_STATUS_CODE, insStatusCode);
                                                            projectInspection.sCustom = sCustom;
                                                        }
                                                        CommonDB.saveProjectInspectionsInTx(projectInspections);
                                                        new Handler(Looper.getMainLooper()).post(listener::didUpdate);
                                                    } catch (Exception e) {
                                                        ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.UpdateStatusPopup.3", e, context);
                                                    }
                                                } else if (!response.getBoolean("success")) {
                                                    new Handler(Looper.getMainLooper()).post(() -> {
                                                        CommonUI.DismissMaterialProgressDialog(oDialog);
                                                        try {
                                                            CommonUI.ShowAlert(context, "Error", response.getString("message"));
                                                        } catch (Exception ex) {
                                                            CommonUI.ShowAlert(context, "Error", "Update Status Response Error. <NAME_EMAIL>.");
                                                        }
                                                    });

                                                } else {
                                                    new Handler(Looper.getMainLooper()).post(() -> {
                                                        CommonUI.DismissMaterialProgressDialog(oDialog);
                                                        CommonUI.ShowAlert(context, "Error", "Update Status failed. <NAME_EMAIL>");
                                                    });

                                                }
                                            } catch (Exception exx) {
                                                ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.UpdateStatusPopup", exx, context);
                                            }

                                        }
                                    }
                                    @Override
                                    public void onFailure(int statusCode, Header[] headers, Throwable e, JSONObject errorResponse) {
                                        new Handler(Looper.getMainLooper()).post(new Runnable() {
                                            @Override
                                            public void run() {
                                                CommonUI.DismissMaterialProgressDialog(oDialog);
                                                CommonUI.ShowAlert(context, "Error", "No server response. <NAME_EMAIL>");

                                            }
                                        });
                                    }
                                });
                            } catch (Exception ex) {
                                ai_BugHandler.ai_Handler_Exception("Exception", "if_exist.EditInspection", ex, context);
                            }
                        } else {
                            CommonUI.ShowAlert(context,"Error", "Please connect to internet to copy inspection!");
                        }

                    } catch (Exception eeee) {

                    }
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    public static void editInspection(Context context, final int iSInsID) {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            CommonUI.ShowAlert(context, "Error", "Please connect to internet to copy inspection!");
            return;
        }

        if (context instanceof Activity && !CommonUI.bAppPermission((Activity) context)) {
            return;
        }

        ai_Inspection oInspection = db_Inspection.GetInspection_BySInsID(iSInsID);
        if (oInspection != null && oInspection.iSInsID > 0 && !oInspection.bSynced) {
            gotoInspection(context, oInspection.getId().intValue());
            return;
        }

        MaterialDialog oDialog;
        try {
            oDialog = CommonUI.ShowMaterialProgressDialog(context, "Message", "Downloading Data ...");
            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(context, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(context, "sToken"));
            oParams.add("iInspectionID", String.valueOf(iSInsID));
            IF_RestClient.post("/IOAPI/EditInspectionAPI", oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    if (statusCode == 200) {
                        try {
                            if (response.getBoolean("success")) {
                                try {
                                    // reset `iSInsID` to 0 and mark as deleted
                                    CommonInspection.deleteAndUnassignInspection(iSInsID);

                                    // will directly create a new inspection
                                    int iInsID = CommonInspection.createOrUpdateInspection(response, context);
                                    CommonUI.DismissMaterialProgressDialog(oDialog);
                                    if (iInsID > 0) {
                                        db_Projects.updateProjectInspections(iSInsID, iInsID);
                                        gotoInspection(context, iInsID);
                                    }
                                } catch (Exception e) {
                                    ai_BugHandler.ai_Handler_Exception("Exception", "CommonInspection.editInspection", e, context);
                                }
                            } else if (!response.getBoolean("success")) {
                                new Handler(Looper.getMainLooper()).post(() -> {
                                    CommonUI.DismissMaterialProgressDialog(oDialog);
                                    try {
                                        CommonUI.ShowAlert(context, "Error", response.getString("message"));
                                    } catch (Exception ex) {
                                        CommonUI.ShowAlert(context, "Error", "Asset Response Error. <NAME_EMAIL>.");
                                    }
                                });

                            } else {
                                new Handler(Looper.getMainLooper()).post(() -> {
                                    CommonUI.DismissMaterialProgressDialog(oDialog);
                                    CommonUI.ShowAlert(context, "Error", "Edit inspection failed. <NAME_EMAIL>");
                                });

                            }
                        } catch (Exception exx) {
                            ai_BugHandler.ai_Handler_Exception("Exception", "CommonInspection.editInspection", exx, context);
                        }
                    }
                }

                @Override
                public void onFailure(int statusCode, org.apache.http.Header[] headers, java.lang.Throwable e, org.json.JSONObject errorResponse) {
                    new Handler(Looper.getMainLooper()).post(() -> {
                        CommonUI.DismissMaterialProgressDialog(oDialog);
                        CommonUI.ShowAlert(context, "Error", "No server response. <NAME_EMAIL>");
                    });
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_exist.EditInspection", ex, context);
        }
    }

    private static void deleteAndUnassignInspection(int iSInsID) {
        ai_Inspection inspection = db_Inspection.GetInspection_BySInsID(iSInsID);
        if (inspection != null) {
            inspection.bDeleted = true;
            inspection.iSInsID = 0;
            CommonDB.saveInspection(inspection);
        }
    }

    public static void gotoInspection(Context context, int iInspectionID) {
        context.startActivity(if_Ins_3rd.newIntent(context, iInspectionID));
    }

}
