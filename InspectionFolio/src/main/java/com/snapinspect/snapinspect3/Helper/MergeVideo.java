package com.snapinspect.snapinspect3.Helper;

import android.app.Activity;
import android.content.Context;
import android.os.AsyncTask;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;
import com.snapinspect.snapinspect3.util.FileUtils;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class MergeVideo extends AsyncTask<String, Integer, O_FileName> {
    public interface MergeVideoListener {
        void onMergeCompleted(O_FileName oFileName);
    }

    private MaterialDialog progressDialog;
    private final WeakReference<Activity> weakReference;
    private final MergeVideoListener mListener;
    private final ai_Video mainVideo;
    private final List<ai_Video> allVideos;

    public MergeVideo(
            Activity activity,
            ai_Video mainVideo,
            List<ai_Video> allVideos,
            MergeVideoListener listener
    ) {
        weakReference = new WeakReference<>(activity);
        mListener = listener;
        this.mainVideo = mainVideo;
        this.allVideos = allVideos;
    }

    private O_FileName mergeAllVideos() throws Exception {
        Activity activity = weakReference.get();
        if (activity == null) return null;

        // Prepare list of video paths
        List<String> videoPaths = new ArrayList<>();
        videoPaths.add(mainVideo.getFile()); // Add main video first

        // Add all other videos
        for (ai_Video video : allVideos) {
            if (!Objects.equals(video.getId(), mainVideo.getId())) {
                videoPaths.add(video.getFile());
            }
        }

        // Merge all videos at once
        String[] videoPathsArray = videoPaths.toArray(new String[0]);
        O_FileName oFileName = VideoUtils.mergeVideosWithMediaCodec(activity, videoPathsArray);

        // Validate merged file
        int videoFileLength = CommonHelper.GetFileLength(oFileName.sFilePath);
        if (videoFileLength <= 0) {
            throw new Exception("Merge video failed, video file length is 0");
        }

        // Generate thumbnail for merged video
        VideoUtils.saveVideoThumbnail(oFileName);

        // Delete all original videos except main video
        for (ai_Video video : allVideos) {
            if (!Objects.equals(video.getId(), mainVideo.getId())) {
                CommonDB.DeleteVideoByID(video.getId());
            }
        }

        // Clean up main video files
        FileUtils.deleteFile(new File(mainVideo.getThumb()));
        FileUtils.deleteFile(new File(mainVideo.getFile()));

        // Update main video metadata
        updateVideoMetadata(mainVideo, oFileName, videoFileLength);

        return oFileName;
    }

    private void updateVideoMetadata(ai_Video video, O_FileName oFileName, int videoFileLength) {
        video.sFile = oFileName.sFilePath;
        video.sThumb = oFileName.sThumbNail;
        video.sSThumb = "";
        video.sSFile = "";
        video.dtDateTime = CommonHelper.sDateToString(new Date());
        video.bUploaded = false;
        video.bDeleted = false;
        video.bProcessed = false;
        video.bGetURL = false;
        video.iSVideoID = 0;
        video.iSize = videoFileLength;
        CommonDB.saveVideo(video);
    }

    @Override
    protected void onPreExecute() {
        if (progressDialog != null && progressDialog.isShowing()) {
            progressDialog.dismiss();
        }
        progressDialog = CommonUI.ShowMaterialProgressDialog(
                weakReference.get(), "Message", "Merge video, please wait ...");
    }

    @Override
    protected O_FileName doInBackground(String... params) {
        Context activity = weakReference.get();
        if (activity == null) return null;

        O_FileName oFileName = null;

        try {
            CommonHelper.trackEvent(activity, "Attempting to merge all videos at once using MediaMuxer");
            oFileName = mergeAllVideos();
            CommonHelper.trackEvent(activity, "Successfully merged all videos using MediaMuxer");
        } catch (Exception e) {
            CommonHelper.trackEvent(activity, "Error merging videos with MediaMuxer: " + e.getMessage());
            ai_BugHandler.logException(e);
        }
        return oFileName;
    }

    @Override
    protected void onPostExecute(O_FileName oFileName) {
        super.onPostExecute(oFileName);

        if (mListener != null) {
            mListener.onMergeCompleted(oFileName);
        }

        Activity activity = weakReference.get();
        if (activity == null) return;
        CommonUI.DismissMaterialProgressDialog(activity, progressDialog);
        activity.finish();
    }
}
