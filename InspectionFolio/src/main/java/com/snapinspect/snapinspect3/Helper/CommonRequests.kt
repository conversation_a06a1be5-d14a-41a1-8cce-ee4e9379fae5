package com.snapinspect.snapinspect3.Helper

import android.content.Context
import org.json.JSONObject

object CommonRequests {
    fun buildRequestParams(
        context: Context?,
        params: Map<String, String>
    ): HashMap<String, String> {
        return hashMapOf<String, String>().apply {
            context?.let { ctx ->
                CommonHelper.GetPreferenceString(ctx, Constants.Keys.iCustomerID)?.let { customerId -> 
                    put(Constants.Keys.iCustomerID, customerId) 
                }
                CommonHelper.GetPreferenceString(ctx, Constants.Keys.sToken)?.let { token -> 
                    put(Constants.Keys.sToken, token) 
                }
            }
            putAll(params)
        }
    }

    fun <T> makeConnectApiRequest(
        endpoint: ApiEndpoint,
        params: HashMap<String, String>,
        processResponse: (JSONObject) -> T
    ): Result<T> = makeRequest(IF_SyncClient.PostRequestConnect(endpoint.path, params), processResponse)

    fun <T> makeApiRequest(
        endpoint: ApiEndpoint,
        params: HashMap<String, String>,
        processResponse: (JSONObject) -> T
    ): Result<T> = makeRequest(IF_SyncClient.PostRequest(endpoint.path, params), processResponse)

    private fun <T> makeRequest(
        response: JSONObject,
        processResponse: (JSONObject) -> T
    ): Result<T> {
        return try {
            when {
                response.optBoolean("success", false) -> {
                    Result.success(processResponse(response))
                }
                response.has("message") -> {
                    val errorMessage = response.getString("message")
                    Result.failure(TaskRequestError.Failed(errorMessage))
                }
                else -> {
                    Result.failure(TaskRequestError.Unknown)
                }
            }
        } catch (e: Exception) {
            Result.failure(
                when (e) {
                    is TaskRequestError -> e
                    else -> TaskRequestError.Unknown
                }
            )
        }
    }

    fun getPhotoThumbURL(context: Context, photoId: Int): Result<String?> {
        val params = buildRequestParams(context, mapOf("iPhotoID" to photoId.toString()))
        return makeApiRequest(ApiEndpoint.PHOTO_THUMB_URL, params) {
            it.optString("sURL")
        }
    }
}
