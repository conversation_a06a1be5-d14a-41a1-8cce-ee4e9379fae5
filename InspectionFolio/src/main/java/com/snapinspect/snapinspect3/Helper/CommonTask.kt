package com.snapinspect.snapinspect3.Helper

import android.app.Activity
import android.content.Context
import com.snapinspect.snapinspect3.Helper.CommonUploadAction.UploadVideoAction
import com.snapinspect.snapinspect3.IF_Object.*
import com.snapinspect.snapinspect3.SI_DB.db_Media
import com.snapinspect.snapinspect3.SI_DB.db_Tasks
import com.snapinspect.snapinspect3.util.DateUtils
import kotlinx.coroutines.*
import org.json.JSONObject
import java.util.*

sealed class TaskRequestError : Exception() {
    data object Unknown : TaskRequestError() {
        private fun readResolve(): Any = Unknown
    }
    data class Failed(override val message: String) : TaskRequestError()
}

object CommonTask {
    fun completeTask(context: Context, taskId: Int): Result<ai_Task> {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            return Result.failure(TaskRequestError.Failed("Please go online to complete the task"))
        }

        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf("iTaskID" to taskId.toString())
        )

        return CommonRequests.makeApiRequest(
            endpoint = ApiEndpoint.TASK_COMPLETE,
            params = params,
            processResponse = { response ->
                val notification = response.getJSONObject("oNotice")
                val task = ai_Task(notification)
                db_Tasks.saveTask(task)
                task
            }
        )
    }

    fun deleteTask(context: Context, taskID: Int): Result<ai_Task> {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            return Result.failure(TaskRequestError.Failed("Please go online to delete the task"))
        }

        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf("iTaskID" to taskID.toString())
        )

        return CommonRequests.makeConnectApiRequest(
            endpoint = ApiEndpoint.TASK_DELETE,
            params = params,
            processResponse = { response ->
                val notification = response.getJSONObject("oNotification")
                val task = ai_Task(notification)
                db_Tasks.saveTask(task)
                task
            }
        )
    }

    fun updateStatus(context: Context, taskID: Int, sStatus: String): Result<ai_Task> {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            return Result.failure(TaskRequestError.Failed("Please go online to update the task status"))
        }

        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf("iTaskID" to taskID.toString(), "sStatus" to sStatus)
        )

        return CommonRequests.makeConnectApiRequest(
            endpoint = ApiEndpoint.TASK_UPDATE_STATUS,
            params = params,
            processResponse = { response ->
                val notification = response.getJSONObject("oNotification")
                val task = ai_Task(notification)
                db_Tasks.saveTask(task)
                task
            }
        )
    }

    fun loadAssetTasks(
        context: Context,
        assetID: Int,
        iDisplayLength: Int = Constants.Limits.iDisplayLengthForTasks
    ): List<ai_Task> {
        var iDisplayStart = 0
        val allTasks = mutableListOf<ai_Task>()

        while (true) {
            val result = loadAssetTasksBatch(context, assetID, iDisplayStart, iDisplayLength)
            when {
                result.isSuccess -> {
                    val tasks = result.getOrNull() ?: emptyList()
                    allTasks.addAll(tasks)
                    if (tasks.size < iDisplayLength) break  // No more tasks to fetch
                    iDisplayStart += iDisplayLength
                }

                result.isFailure -> {
                    val error = result.exceptionOrNull()
                    CommonUI.ShowAlert(context, "Error", error?.message ?: "Unknown error occurred")
                    break
                }
            }
        }

        // Save all tasks to database
        db_Tasks.saveTasks(allTasks)

        return allTasks
    }

    private fun loadAssetTasksBatch(
        context: Context,
        assetID: Int,
        iDisplayStart: Int,
        iDisplayLength: Int
    ): Result<List<ai_Task>> {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            return Result.failure(TaskRequestError.Failed("Please go online to load the tasks"))
        }

        val dtSync = db_Tasks.lastSync(assetID)
        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf(
                "iAssetID" to assetID.toString(),
                "dtSync" to (dtSync ?: Constants.Values.sSyncDateDefault),
                "iDisplayStart" to iDisplayStart.toString(),
                "iDisplayLength" to iDisplayLength.toString()
            )
        )

        return CommonRequests.makeConnectApiRequest(
            endpoint = ApiEndpoint.TASK_ASSET_TASKS,
            params = params,
            processResponse = { response ->
                val lsResult = response.getJSONArray("lsTask") ?: throw TaskRequestError.Unknown

                // Save sync date
                if (lsResult.length() > 0) {
                    db_Tasks.updateLastSync(assetID, response.optString("dtSync") ?: "")
                }

                List(lsResult.length()) { i ->
                    ai_Task(lsResult.getJSONObject(i))
                }
            }
        )
    }

    fun loadTaskDetails(context: Context, taskID: Int): Result<ai_TaskDetails> {
        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf("iTaskID" to taskID.toString())
        )

        return CommonRequests.makeConnectApiRequest(
            endpoint = ApiEndpoint.TASK_DETAILS,
            params = params,
            processResponse = { response ->
                val taskDetails = ai_TaskDetails(response)
                saveTaskDetailsToDatabase(taskDetails, taskID)
                taskDetails
            }
        )
    }

    private fun saveTaskDetailsToDatabase(taskDetails: ai_TaskDetails, taskID: Int) {
        // Update task in database
        db_Tasks.saveTask(taskDetails.task)

        // Update photos
        taskDetails.photos.forEach { photo ->
            db_Media.getPhotoOrCreateByServerID(photo.iSPhotoID)?.let { existingPhoto ->
                existingPhoto.apply {
                    iInsID = photo.iInsID
                    iInsItemID = photo.iInsItemID
                    sComments = photo.sComments
                    bUploaded = photo.bUploaded
                    bDeleted = photo.bDeleted
                    dtDateTime = photo.dtDateTime
                    iSize = photo.iSize
                    sFieldOne = CommonJson.AddJsonKeyValue(
                        photo.sFieldOne, Constants.Keys.iTaskID, taskID.toString()
                    )
                }
                CommonDB.save(existingPhoto)
            } ?: CommonDB.save(photo)
        }

        // Update videos
        taskDetails.videos.forEach { video ->
            db_Media.getVideoOrCreateByServerID(video.iSVideoID)?.let { existingVideo ->
                existingVideo.apply {
                    iInsID = video.iInsID
                    iInsItemID = video.iInsItemID
                    sSThumb = video.sSThumb
                    sSFile = video.sSFile
                    bUploaded = video.bUploaded
                    bProcessed = video.bProcessed
                    bDeleted = video.bDeleted
                    iSize = video.iSize
                    sFieldOne = video.sFieldOne
                    sFieldThree = taskID.toString()
                    dtDateTime = video.dtDateTime
                }
                CommonDB.save(existingVideo)
            } ?: CommonDB.save(video)
        }

        // Save sub tasks
        db_Tasks.saveTasks(taskDetails.subTasks)
    }

    private fun saveTaskDetails(
        context: Context?,
        taskID: Int = 0,
        title: String,
        description: String?,
        taskDue: String,
        categoryID: Int?,
        followUpCustomerID: Int,
        priority: Int,
        reminderEmailTemplate: String,
        status: String,
        additionalCustom1: String,
        members: List<String> = emptyList(),
        assetID: Int,
        tzID: String? = TimeZone.getDefault().id,
        parentTaskID: Int? = null
    ): Result<ai_Task> {
        val params = buildTaskDetailsParams(
            context = context,
            taskID = taskID,
            title = title,
            description = description,
            taskDue = taskDue,
            categoryID = categoryID,
            followUpCustomerID = followUpCustomerID,
            priority = priority,
            reminderEmailTemplate = reminderEmailTemplate,
            status = status,
            additionalCustom1 = additionalCustom1,
            members = members,
            assetID = assetID,
            tzID = tzID,
            parentTaskID = parentTaskID
        )

        return CommonRequests.makeApiRequest(
            endpoint = ApiEndpoint.TASK_SAVE_DETAILS,
            params = params,
            processResponse = { response ->
                val task = ai_Task(response.getJSONObject("oTask"))
                db_Tasks.saveTask(task)
                updateTaskMedia(task)
                task
            }
        )
    }

    private fun buildTaskDetailsParams(
        context: Context?,
        taskID: Int,
        title: String,
        description: String?,
        taskDue: String,
        categoryID: Int?,
        followUpCustomerID: Int,
        priority: Int,
        reminderEmailTemplate: String,
        status: String,
        additionalCustom1: String,
        members: List<String>,
        assetID: Int,
        tzID: String?,
        parentTaskID: Int?
    ): HashMap<String, String> {
        return CommonRequests.buildRequestParams(
            context, mapOf(
            "iTaskID" to taskID.toString(),
            "sTitle" to title,
            "sDescription" to (description?.takeIf { it.isNotBlank() } ?: ""),
            "iPriority" to priority.toString(),
            "sReminder_EmailTemplate" to reminderEmailTemplate,
            "sStatus" to status,
            "sAdditionalCustom1" to additionalCustom1,
            "arrMember" to members.joinToString(","),
            "iAssetID" to assetID.toString()
        )).apply {
            taskDue.takeIf { it.isNotEmpty() }?.let { put("sTaskDue", it) }

            put(
                "iFollowUpCustomerID", when {
                    followUpCustomerID > 0 -> followUpCustomerID.toString()
                    else -> CommonHelper.getCurrentUserID(context).toString()
                }
            )

            categoryID?.takeIf { it > 0 }?.toString()?.let { put("iCategoryID", it) }
            tzID?.let { put("sTZID", it) }
            parentTaskID?.takeIf { it > 0 }?.toString()?.let { put("iPTaskID", it) }
        }
    }

    // create a new task
    fun createTask(
        context: Context,
        iPropertyID: Int,
        sTitle: String,
        sDescription: String?,
        iParentTaskID: Int? = null,
        settings: TaskSettings
    ): Result<ai_Task> {
        return saveTaskDetails(
            context = context,
            taskID = 0,
            title = sTitle,
            description = sDescription,
            taskDue = settings.dueDate?.let { DateUtils.format(it, DateUtils.DateFormat.DATE_TIME) } ?: "",
            categoryID = settings.taskCategoryID,
            followUpCustomerID = settings.assignTo ?: 0,
            priority = settings.priority.value,
            reminderEmailTemplate = "",
            status = settings.status ?: "",
            additionalCustom1 = "",
            members = settings.members.map { it.toString() },
            assetID = iPropertyID,
            tzID = TimeZone.getDefault().id,
            parentTaskID = iParentTaskID
        )
    }

    // update an existing task
    fun updateTask(
        context: Context,
        task: ai_Task,
        settings: TaskSettings,
        customInfos: List<ai_CustomInfo>,
    ): Result<ai_Task> {
        // fatal error if taskID is 0
        if (task.iSNotificationID == 0) {
            return Result.failure(TaskRequestError.Failed("Task ID is required"))
        }

        val sCustom1 = updateCustomInfos(task.sCustom1, customInfos)

        return saveTaskDetails(
            context = context,
            taskID = task.iSNotificationID,
            title = task.sTitle,
            description = task.sDescription,
            taskDue = settings.dueDate?.let { DateUtils.format(it, DateUtils.DateFormat.DATE_TIME) } ?: "",
            categoryID = settings.taskCategoryID,
            followUpCustomerID = settings.assignTo ?: 0,
            priority = settings.priority.value,
            reminderEmailTemplate = task.sReminder,
            status = settings.status ?: "",
            additionalCustom1 = sCustom1,
            members = settings.members.map { it.toString() },
            assetID = task.iPropertyID,
            tzID = TimeZone.getDefault().id,
            parentTaskID = task.iPTaskID
        )
    }

    private fun updateCustomInfos(sCustom1: String, customInfos: List<ai_CustomInfo>): String {
        var updatedCustom1 = sCustom1
        try {
            val sCustom1Json = JSONObject(sCustom1)
            customInfos.forEach { customInfo ->
                val key = Constants.Values.kCustomInfoPrefix + customInfo.iCustomInfoID
                customInfo.takeIf {
                    it.sValue.isNotBlank() || CommonJson.GetJsonKeyValue_String(key, sCustom1Json)
                        ?.isNotEmpty() ?: false
                }?.let {
                    updatedCustom1 = CommonJson.AddJsonKeyValue(updatedCustom1, key, customInfo.sValue ?: "")
                }
            }
        } catch (e: Exception) {
            // Ignore JSON parsing errors
        }
        return updatedCustom1
    }

    private fun updateTaskMedia(task: ai_Task) {
        val attachments = task.attachments
        attachments.photos.forEach { photo ->
            db_Media.getPhotoByServerID(photo.id)?.let {
                it.sFieldOne = CommonJson.AddJsonKeyValue(
                    it.sFieldOne,
                    Constants.Keys.iTaskID,
                    task.iSNotificationID.toString()
                )
                CommonDB.save(it)
            }
        }

        // only one video id
        val videoID = task.attachments.video?.id
        videoID?.let { videoId ->
            db_Media.getVideoByServerID(videoId)?.let { video ->
                video.sFieldThree = task.iSNotificationID.toString()
                CommonDB.save(video)
            }
        }
    }

    fun saveTaskPhoto(
        context: Context,
        photoID: Int,
        taskID: Int,
        bDeleted: Boolean = false
    ): Result<ai_Task> {
        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf(
                "iPhotoID" to photoID.toString(),
                "iTaskID" to taskID.toString(),
                "bDeleted" to bDeleted.toString()
            )
        )

        return CommonRequests.makeApiRequest(
            endpoint = ApiEndpoint.TASK_SAVE_PHOTO,
            params = params,
            processResponse = { response ->
                ai_Task(response.getJSONObject("oNotification"))
            }
        )
    }

    fun saveTaskVideo(context: Context, videoID: Int, taskID: Int, bDeleted: Boolean = false): Result<ai_Task> {
        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf(
                "iVideoID" to videoID.toString(),
                "iTaskID" to taskID.toString(),
                "bDeleted" to bDeleted.toString()
            )
        )

        return CommonRequests.makeApiRequest(
            endpoint = ApiEndpoint.TASK_SAVE_VIDEO,
            params = params,
            processResponse = { response ->
                ai_Task(response.getJSONObject("oNotification"))
            }
        )
    }

    fun savePhotoComment(context: Context, photoID: Int, comments: String): Result<ai_Photo> {
        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf(
                "iSPhotoID" to photoID.toString(),
                "sComments" to comments
            )
        )

        return CommonRequests.makeApiRequest(
            endpoint = ApiEndpoint.PHOTO_SAVE_COMMENTS,
            params = params,
            processResponse = { response ->
                ai_Photo(response.getJSONObject("oPhoto"))
            }
        )
    }

    fun saveTaskComment(
        context: Context,
        taskID: Int,
        comments: String,
        sType: String = "C",
        sSource: String = "a", // a for android, o for ios
        sDateTime: String = DateUtils.format(Date(), DateUtils.DateFormat.DATE_TIME_SHORT)
    ): Result<ai_TaskComment> {
        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf(
                "iTaskID" to taskID.toString(),
                "sComments" to comments,
                "sType" to sType,
                "sSource" to sSource,
                "sDateTime" to sDateTime
            )
        )

        return CommonRequests.makeApiRequest(
            endpoint = ApiEndpoint.TASK_SAVE_COMMENT,
            params = params,
            processResponse = { response ->
                ai_TaskComment(response.getJSONObject("oComment"))
            }
        )
    }

    fun loadTaskComments(
        context: Context,
        taskID: Int,
        iStart: Int = 0,
        iLength: Int = 10
    ): Result<List<ai_TaskComment>> {
        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf(
                "iTaskID" to taskID.toString(),
                "iStart" to iStart.toString(),
                "iLength" to iLength.toString()
            )
        )

        return CommonRequests.makeApiRequest(
            endpoint = ApiEndpoint.TASK_GET_COMMENTS,
            params = params,
            processResponse = {
                it.optJSONArray("lsComments")
                    ?.let { lsComments ->
                        (0 until lsComments.length()).map { index ->
                            ai_TaskComment(lsComments.getJSONObject(index))
                        }
                    } ?: emptyList()
            }
        )
    }

    fun uploadVideo(context: Context, video: ai_Video): Result<ai_Video> {
        if (!CommonHelper.bFileExist(video.file)) {
            return Result.failure(TaskRequestError.Failed("Video file does not exist"))
        }

        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf(
                "iVideoSize" to video.iSize.toString(),
                "sGeo" to (video.geo ?: "")
            )
        )

        return CommonRequests.makeApiRequest(
            endpoint = ApiEndpoint.VIDEO_GET_TOKEN,
            params = params,
            processResponse = { response ->
                // Try upload video twice
                if (!UploadVideoAction(context, video, 1, response, null)) {
                    if (!UploadVideoAction(context, video, 1, response, null)) {
                        throw TaskRequestError.Failed("Failed to upload video")
                    }
                }

                val videoId = response.getInt("iVideoID")
                if (videoId <= 0) {
                    throw TaskRequestError.Failed("Invalid video ID received")
                }

                val confirmParams = CommonRequests.buildRequestParams(
                    context = context,
                    params = mapOf("iVideoID" to videoId.toString())
                )

                // Try to confirm upload twice
                val confirmResult = CommonRequests.makeApiRequest(
                    endpoint = ApiEndpoint.VIDEO_UPLOAD_SUCCESS,
                    params = confirmParams,
                    processResponse = {
                        video.apply {
                            iSVideoID = videoId
                            bUploaded = true
                            bProcessed = true
                            bGetURL = true
                        }
                        CommonDB.save(video)
                    }
                )

                if (confirmResult.isFailure) {
                    CommonRequests.makeApiRequest(
                        endpoint = ApiEndpoint.VIDEO_UPLOAD_SUCCESS,
                        params = confirmParams,
                        processResponse = {
                            video.apply {
                                iSVideoID = videoId
                                bUploaded = true
                                bProcessed = true
                                bGetURL = true
                            }
                            CommonDB.save(video)
                        }
                    )
                }

                video
            }
        )
    }

    fun uploadPhoto(context: Context, photo: ai_Photo): Result<ai_Photo> {
        if (!CommonHelper.bFileExist(photo.file)) {
            return Result.failure(TaskRequestError.Failed("Photo file does not exist"))
        }

        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf(
                "dtCreated" to photo.dtDateTime,
                "sPhotoComment" to photo.sComments,
                "iSize" to photo.iSize.toString(),
                "sGeo" to (photo.geo ?: "")
            )
        )

        return CommonRequests.makeApiRequest(
            endpoint = ApiEndpoint.PHOTO_UPLOAD,
            params = params,
            processResponse = { response ->
                val uploadUrl = response.getString("sURL")
                val uploadResult = IF_SyncClient.UploadPhoto(uploadUrl, photo)

                if (uploadResult != 200) {
                    // Retry once
                    val retryResult = IF_SyncClient.UploadPhoto(uploadUrl, photo)
                    if (retryResult != 200) {
                        throw TaskRequestError.Failed("Failed to upload photo")
                    }
                }

                val photoId = response.getInt("iSPhotoID")
                if (photoId <= 0) {
                    throw TaskRequestError.Failed("Invalid photo ID received")
                }

                val confirmParams = CommonRequests.buildRequestParams(
                    context = context,
                    params = mapOf("iSPhotoID" to photoId.toString())
                )

                CommonRequests.makeApiRequest(
                    endpoint = ApiEndpoint.PHOTO_UPLOAD_SUCCESS,
                    params = confirmParams,
                    processResponse = {
                        photo.apply {
                            iSPhotoID = photoId
                            bUploaded = true
                        }
                        CommonDB.save(photo)
                        photo
                    }
                ).getOrThrow()
            }
        )
    }

    /**
     * Compares two lists of custom task information to determine if they are equivalent.
     *
     * @param originalCustomInfos The original list of custom information objects
     * @param newCustomInfos The new list of custom information objects to compare against
     * @return true if both lists contain the same custom information (matching IDs and values), false otherwise
     */
    fun compareTaskCustomInfos(
        originalCustomInfos: List<ai_CustomInfo>,
        newCustomInfos: List<ai_CustomInfo>
    ): Boolean {
        if (originalCustomInfos.size != newCustomInfos.size) {
            return false
        }
        
        return originalCustomInfos.all { original ->
            newCustomInfos.any { new -> original.iCustomInfoID == new.iCustomInfoID && original.sValue == new.sValue }
        }
	}

    private fun loadCustomerTasks(
        context: Context,
        sDateTime: String,
        iDisplayStart: Int = 0,
        iDisplayLength: Int = 1000
    ): Result<ai_CustomerTaskResponse> {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            return Result.failure(TaskRequestError.Failed("Please go online to load customer tasks"))
        }

        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf(
                "dtSync" to sDateTime,
                "iDisplayStart" to iDisplayStart.toString(),
                "iDisplayLength" to iDisplayLength.toString()
            )
        )

        return CommonRequests.makeConnectApiRequest(
            endpoint = ApiEndpoint.CUSTOMER_TASKS,
            params = params,
            processResponse = { response ->
                ai_CustomerTaskResponse(response)
            }
        )
    }

    private fun loadCustomerTasksCatch(
        context: Context,
        sDateTime: String,
        iDisplayStart: Int = 0,
        iDisplayLength: Int = 1000
    ): Result<ai_CustomerTaskResponse> {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            return Result.failure(TaskRequestError.Failed("Please go online to load customer tasks"))
        }

        val params = CommonRequests.buildRequestParams(
            context = context,
            params = mapOf(
                "dtSync" to sDateTime,
                "iDisplayStart" to iDisplayStart.toString(),
                "iDisplayLength" to iDisplayLength.toString()
            )
        )

        return CommonRequests.makeConnectApiRequest(
            endpoint = ApiEndpoint.CUSTOMER_TASKS_CATCH,
            params = params,
            processResponse = { response ->
                ai_CustomerTaskResponse(response)
            }
        )
    }

    fun syncCustomerTasks(
        context: Context,
        sDateTime: String,
        iDisplayLength: Int = 1000
    ): Result<List<ai_Task>> {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            return Result.failure(TaskRequestError.Failed("Please go online to load customer tasks"))
        }

        val allTasks = mutableListOf<ai_Task>()
        var lastSyncDate: String? = null

        fun loadTasks(loadFunction: (Int) -> Result<ai_CustomerTaskResponse>) {
            var iDisplayStart = 0
            while (true) {
                val result = loadFunction(iDisplayStart)
                if (result.isFailure) break
                val response = result.getOrNull() ?: break
                // Save tasks to database
                db_Tasks.saveTasks(response.lsTask)
                // Update last sync date
                if (iDisplayStart == 0) {
                    lastSyncDate = response.dtSync
                }
                allTasks.addAll(response.lsTask)

                if (response.lsTask.size < iDisplayLength) break
                iDisplayStart += iDisplayLength
            }
        }

        loadTasks { start -> loadCustomerTasks(context, sDateTime, start, iDisplayLength) }
        // Save last sync date to preferences
        lastSyncDate?.let {
            CommonHelper.SavePreference(context, Constants.Keys.DATE_SYNC_CUSTOMER_TASKS, it)
        }

        // Only load catch tasks if the date is valid
        DateUtils.parse(sDateTime, Constants.UTC, DateUtils.possibleDateFormats)
            ?.takeIf { DateUtils.isValidDate(it) }
            ?.let {
                loadTasks { start -> loadCustomerTasksCatch(context, sDateTime, start, iDisplayLength) }
            }
        return Result.success(allTasks)
    }
}

fun JSONObject.optDate(
    key: String,
    formats: Array<String> = DateUtils.possibleDateFormats
): Date? {
    return opt(key)?.let { value ->
        when (value) {
            is String -> formats.firstNotNullOfOrNull { format -> 
                DateUtils.parse(value, format)
            }
            else -> null
        }
    }
}

fun Activity.savePhotoComment(
    dispatcher: CoroutineDispatcher = Dispatchers.IO,
    photoID: Int, comments: String, completion: (Result<ai_Photo>) -> Unit
) {
    if (!NetworkUtils.isNetworkAvailable(this)) {
        CommonUI.ShowAlert(this, "Error", "Please go online to save photo comments")
        return
    }

    val dialog = CommonUI.ShowMaterialProgressDialog(this, "Message", "Saving photo comments...")

    CoroutineScope(Dispatchers.Main).launch {
        try {
            val result = withContext(dispatcher) {
                CommonTask.savePhotoComment(this@savePhotoComment, photoID, comments)
            }
            completion(result)
        } catch (e: Exception) {
            completion(Result.failure(e))
        } finally {
            CommonUI.DismissMaterialProgressDialog(this@savePhotoComment, dialog)
        }
    }
}