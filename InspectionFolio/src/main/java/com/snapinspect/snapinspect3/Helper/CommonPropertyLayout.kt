package com.snapinspect.snapinspect3.Helper

import android.content.Context
import com.snapinspect.snapinspect3.IF_Object.ai_Layout
import com.snapinspect.snapinspect3.IF_Object.ai_PropertyLayout
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.SI_DB.db_PropertyLayout
import com.snapinspect.snapinspect3.SI_DB.db_PropertyLayout.getLayoutWithLayoutItem
import com.snapinspect.snapinspect3.activity.SyncService.SYNC_PROGRESS
import com.snapinspect.snapinspect3.util.DateUtils
import com.snapinspect.snapinspect3.util.DateUtils.DateFormat
import com.snapinspect.snapinspect3.util.StringUtils
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object CommonPropertyLayout {
    @JvmStatic
    fun syncAndSaveAllPropertyLayouts(context: Context, pageSize: Int = 1000, progressCallback: ProgressCallback): List<ai_PropertyLayout> {
        val lastSyncTime = CommonHelper.GetPreferenceString(context, Constants.Keys.DATE_SYNC_PROPERTY_LAYOUTS)
        val sDateTime = StringUtils.ifEmpty(lastSyncTime, Constants.Values.sSyncDateDefault)
        var iStartIndex = 0
        var dtSync: String? = null

        val allPropertyLayouts = mutableListOf<ai_PropertyLayout>()
        while (true) {
            val (propertyLayouts, syncDate) = syncPropertyLayouts(context, sDateTime, iStartIndex, pageSize)
            allPropertyLayouts.addAll(propertyLayouts)

            // Save property layouts
            db_PropertyLayout.savePropertyLayouts(propertyLayouts)

            // Update progress
            progressCallback.onProgress(
                SYNC_PROGRESS, context.getString(R.string.syncing),
                "Processed ${allPropertyLayouts.size} Asset Layouts. Please Wait"
            )

            // Store dtSync from first batch
            if (iStartIndex == 0) {
                dtSync = syncDate
            }

            if (propertyLayouts.size < pageSize) break
            iStartIndex += pageSize
        }

        // Save the latest sync date
        dtSync?.let { syncDate ->
            DateUtils.parse(syncDate, Constants.UTC,  DateUtils.possibleDateFormats)?.let { date ->
                val formattedDate = DateUtils.format(date, DateFormat.DATE_TIME, Constants.UTC)
                CommonHelper.SavePreference(context, Constants.Keys.DATE_SYNC_PROPERTY_LAYOUTS, formattedDate)
            }
        }

        return allPropertyLayouts
    }

    private fun syncPropertyLayouts(
        context: Context?,
        sDateTime: String,
        iStartIndex: Int,
        iLength: Int
    ): Pair<List<ai_PropertyLayout>, String?> {
        val params = CommonRequests.buildRequestParams(context, mapOf(
            "sDateTime" to sDateTime,
            "iStartIndex" to iStartIndex.toString(),
            "iLength" to iLength.toString()
        ))

        return CommonRequests.makeConnectApiRequest(
            ApiEndpoint.PROPERTY_LAYOUT_SYNC,
            params
        ) { response ->
            val lsPropertyLayout = response.optJSONArray("lsPropertyLayout")
                ?: return@makeConnectApiRequest Pair(emptyList(), null)
            val propertyLayouts = List(lsPropertyLayout.length()) { i ->
                ai_PropertyLayout(lsPropertyLayout.optJSONObject(i))
            }
            Pair(propertyLayouts, response.optString("dtSync"))
        }.getOrElse { Pair(emptyList(), null) }
    }

    @JvmStatic
    fun getAssetLayoutV3(
        requestDispatcher: CoroutineDispatcher = Dispatchers.IO,
        context: Context, iAssetID: Int, iInsTypeID: Int, sPTC: String,
        callback: (List<ai_Layout>, String?) -> Unit
    ) {
        if (!NetworkUtils.isNetworkAvailable(context)) {
            callback(emptyList(), "No network connection")
            return
        }

        val params = CommonRequests.buildRequestParams(context, mapOf(
            "iAssetID" to iAssetID.toString(),
            "iInsTypeID" to iInsTypeID.toString()
        ))

        CoroutineScope(Dispatchers.Main).launch {
            runCatching {
                withContext(requestDispatcher) {
                    CommonRequests.makeApiRequest(ApiEndpoint.ASSET_LAYOUT_LAYOUT_V3, params) { response ->
                        val layoutJson = response.optString("arrLayout", "")
                        ai_PropertyLayout.convertLayoutItems(layoutJson)
                            .mapIndexed { index, layoutItem ->
                                getLayoutWithLayoutItem(layoutItem, sPTC).apply {
                                    sName = layoutItem.getLayoutName()
                                    sFieldThree = index.toString()
                                    sFieldOne = "1"
                                }
                            }
                    }
                }
            }.fold(
                onSuccess = { layouts -> callback(layouts.getOrNull() ?: emptyList(), null) },
                onFailure = { error -> callback(emptyList(), error.message) }
            )
        }
    }
}