package com.snapinspect.snapinspect3.Helper;

import android.util.Size;

import java.text.SimpleDateFormat;
import java.util.Locale;
import java.util.TimeZone;

import static android.Manifest.permission.ACCESS_COARSE_LOCATION;
import static android.Manifest.permission.ACCESS_FINE_LOCATION;

public final class Constants {
    private Constants() {
        // This utility class is not publicly instantiable
    }

    public static final String[] LOCATION_PERMISSIONS = new String[]{ACCESS_FINE_LOCATION, ACCESS_COARSE_LOCATION};
    public static final String[] USER_COLORS = new String[] {
            "#22BC90", "#f40056", "#3c59fd", "#691a99", "#d400f9",
            "#00e575", "#ffaa00", "#0090e9", "#ff5151", "#4A148C",
            "#880E4F", "#B71C1C", "#0D47A1", "#1A237E", "#311B92",
            "#004D40", "#006064", "#01579B", "#827717", "#33691E",
            "#1B5E20", "#E65100", "#FF6F00", "#3E2723", "#263238",
            "#000000"
    };

    public interface OnConfirmListener {
        void onConfirm();
    }

    public enum DateRange {
        FROM, TO
    }

    public static final class FloorPlanFiles {
        private FloorPlanFiles() {
            // This utility class is not publicly instantiable
        }
        public static final String FLOOR_PLAN_HTML_FOLDER = "floor_plan_html";
        // floor_plan.html
        public static final String FLOOR_PLAN_HTML_FILE_NAME = "floor_plan.html";
        // dist_iife_index.js
        public static final String FLOOR_PLAN_DIST_IIFF_INDEX_JS = "dist_iife_index.js";
        // 18.2.0_umd_react-dom.development.js
        public static final String FLOOR_PLAN_UMD_REACT_DOM_DEVELOPMENT_JS = "18.2.0_umd_react-dom.development.js";
        // 18.2.0_umd_react.development.js
        public static final String FLOOR_PLAN_UMD_REACT_DEVELOPMENT_JS = "18.2.0_umd_react.development.js";
        // 6.0.7_styled-components.js
        public static final String FLOOR_PLAN_STYLED_COMPONENTS_JS = "6.0.7_styled-components.js";
        // 5.3.1_fabric.js
        public static final String FLOOR_PLAN_FABRIC_JS = "5.3.1_fabric.js";
    }

    public static class Urls {

        private Urls() {
            // This utility class is not publicly instantiable
        }
        public static final String URL_SCHEME = "https://";
        public static final String BASE_URL = "my.snapinspect.com";
        public static final String VIDEO_URL = "https://snap3tube.s3.amazonaws.com/";
    }

    public static class Paths {
        public static final String tempFolder = "/SnapTemp";
        public static final String prefs = "TIPref";
        public static final String FLOOR_PLAN_FOLDER = "/FloorPlans";
        public static final String PRODUCT_PHOTOS = "/ProductPhotos";
        private Paths() {
            // This utility class is not publicly instantiable
        }
    }

    public static class Tokens {
        public static final String dataDog = "pub0a423b677d9a62fac0c985b317b986a4";
        private Tokens() {
            // This utility class is not publicly instantiable
        }
    }

    public static class Broadcasts {
        public static final String sInsItemChanged = "InsItem-Changed";
        public static final String sReloadInsItem = "InsItem-Reload";
        public static final String sDeleteInsItem = "InsItem-Delete-Action";
        public static final String sActionInsItem = "InsItem-Action";
        public static final String sActionDownloadAllPhotos = "Action-DownloadAllPhotos";
        public static final String sEditTextFocused = "InsItem-EditTextFocused";
        public static final String sInsertNoticePhoto = "Insert-Notice-Photo";
        public static final String sDeleteNoticePhoto = "Delete-Notice-Photo";
        public static final String sPreviewNoticePhoto = "Preview-Notice-Photo";
        public static final String sInsertNoticeVideo = "Insert-Notice-Video";
        public static final String sDeleteNoticeVideo = "Delete-Notice-Video";
        public static final String sHomeTabSwitched = "HomeTab-Switched";
        public static final String sReloadTask = "Task-Reload";
        public static final String ACTION_SCAN_DOCUMENT = "com.snapinspect.snapinspect3.ACTION_SCAN_DOCUMENT";
        private Broadcasts() {
            // This utility class is not publicly instantiable
        }
    }

    public static class Settings {
        public static final String bCommentSortDesc = "bCommentSortDesc";
        private Settings() {
            // This utility class is not publicly instantiable
        }
        // public static final String bNewInspectionUI = "bNewInspectionUI";
        public static final String bAutoLogin = "bAutoLogin";
        public static final String bDisplayInst = "bDisplayInst";
        public static final String bDisplayInspectionDuration = "bDisplayInspectionDuration";
        public static final String bMultiFamily = "bMultiFamily";
        public static final String bRoom = "bRoom";
        public static final String bKiosk = "bKiosk";
        public static final String bFixCamRotation = "bFixCamRotation";
        public static final String bPhotoStamp = "bPhotoStamp";
        public static final String bSaveLocal = "bSaveLocal";
        public static final String bShowGeoTag = "bShowGeoTag";
        public static final String bFixCamera = "bFixCamera";
    }

    public static class Extras {
        private Extras() {
            // This utility class is not publicly instantiable
        }

        public static final String iInspectionID = "iInspectionID";
        public static final String sType = "sType";
        public static final String sPTC = "sPTC";

        public static final String iSAssetID = "iSAssetID";
        public static final String iSInsTypeID = "iSInsTypeID";
        public static final String sAddress1 = "sAddress1";
        public static final String sAddress2 = "sAddress2";
        public static final String iSScheduleID = "iSScheduleID";
        public static final String sScheduleCustom1 = "sScheduleCustom1";

        public static final String iInsItemID = "iInsItemID";
        public static final String iPosition = "iPosition";
        public static final String iNotificationID = "iNotificationID";
        public static final String oSaveOption = "oSaveOption";

        public static final String iSPAssetID = "iSPAssetID";
        public static final String iInsID = "iInsID";
        public static final String iNoticeID = "iNoticeID";

        public static final String mailto = "mailto";
        public static final String subject = "subject";
        public static final String body = "body";

        public static final String iSContactID = "iSContactID";
        public static final String iTextPosition = "iTextPosition";
        public static final String sWords = "sWords";

        public static final String iPInsItemID = "iPInsItemID";
        public static final String bFullInspection = "bFullInspection";
        public static final String bRequestInspection = "bRequestInspection";
        public static final String iPLayoutID = "iPLayoutID";
        public static final String singlePhoto = "singlePhoto";

        public static final String iVideoID = "iVideoID";
        public static final String sFilePath = "sFilePath";
        public static final String bFull = "bFull";
        public static final String iPhotoID = "iPhotoID";
        public static final String iFileID = "iFileID";
        public static final String sComments = "sComments";
        public static final String sFieldOne = "sFieldOne";
        public static final String sURL = "sRemoteURL";
        public static final String sPhotoIds = "sPhotoIds";
        public static final String bNoticePhotos = "bNoticePhotos";
        public static final String bFromAllPhotos = "bFromAllPhotos";

        public static final String iSInsID = "iSInsID";
        public static final String iSTaskID = "iSTaskID";
        public static final String iPSTaskID = "iPSTaskID";
        public static final String iActionID = "iActionID";
        public static final String iActionType = "iActionType";

        public static final String bForceSubmitData = "bForceSubmitData";

        public static final String sDatePattern = "sDatePattern";
        public static final String bitmap = "bitmap";
        public static final String sKioskUri = "sKioskUri";
        public static final String iProjectID = "iProjectID";
        public static final String iSProjectInspectionID = "iSProjectInspectionID";
        public static final String iTabIndex = "iTabIndex";
        public static final String iVideoLength = "iVideoLength";
        public static final String iFloorPlanID = "iFloorPlanID";
        public static final String floorPlanType = "floorPlanType";
        public static final String floorPlanLayerType = "floorPlanLayerType";
        public static final String sResult = "result";

        public static final String SCAN_NAME = "Scan_Name";
        public static final String SCAN_TYPE = "Scan_Type";
        public static final String CROP_PATH = "Crop_Path";
        public static final String COLOR = "Color";
        public static final String CAMERA_RESPONSE = "CAMERA_RESPONSE";
        public static final String CLIENT_FILE_ID = "iClientFileID";
        public static final String IS_INITIAL_FLOOR_PLAN = "isInitialFloorPlan";
        public static final String S_DT_SCHEDULE = "sDtSchedule";
        public static final String I_PRODUCT_ID = "iProductID";

        public static final String I_MAX_PHOTO_COUNT = "iMaxPhotoCount";
        public static final String SCROLL_TO_BOTTOM = "scrollToBottom";
        public static final String VIDEO_QUALITY = "sVideoQuality";
    }

    public static class Keys {
        private Keys() {
            // This utility class is not publicly instantiable
        }
        public static final String bC = "bC";
        public static final String sCameraFlash = "sCameraFlash";
        public static final String sCameraFlash_V2 = "sCameraFlash_V2";
        public static final String sBrightness = "sBrightness";
        public static final String sCustomRole = "sCustomRole";
        public static final String sAssetView = "sAssetView";
        public static final String bAppHideAssetTab = "bM_Assets_Hide";
        public static final String bServerSync = "bServerSync";
        public static final String bcType = "bC_T";
        public static final String bcAndV = "bC_AND_V";
        public static final String bcOrV = "bC_OR_V";
        public static final String sTitle = "Title";
        public static final String sMessage = "Message";
        public static final String sProjectsLastUpdated = "ProjectsLastUpdated";
        public static final String sToken = "sToken";
        public static final String iCustomerID = "iCustomerID";
        public static final String bProject = "bProject";
        public static final String bMProjectTabHide = "bM_ProjectTab_Hide";
        public static final String bInitialSync = "bInitialSync";
        public static final String sCompanyCustom1 = "sCompanyCustom1";
        public static final String bForceSyncProjects = "bForceSyncProjects";
        public static final String oResultReceiver = "oResultReceiver";
        public static final String kDisableEditAsset = "bDis_EditAsset";
        public static final String kDisableNewInspection = "bDis_NewIns";
        public static final String kDisableCopyInspection = "bDis_Copy";
        public static final String kDisableQuickEdit = "bW_QuickEdit_Hide";
        public static final String bUseLayoutV2 = "bLayout_V2";
        public static final String kHiddenInsTypeIDs = "_iInsTypeID_Hide";
        public static final String sRole = "sRole";
        public static final String kSelectedAssetViewID = "kSelectedAssetViewID";
        public static final String sAstView = "sAstView";
        public static final String kFolderPermission = "b_FolderPermission";
        public static final String kFolder = "_bFolder";
        public static final String kProLayoutAreaOnly = "_ProLayout_AreaOnly";
        public static final String kFloorPlan = "_bFloorPlan";
        public static final String kEnableProduct = "_bCosting";
        public static final String customInfo1st = "CustomInfo1st";
        public static final String customInfo2nd = "CustomInfo2nd";
        public static final String sAssetAttributes = "_AssetAttributes";
        public static final String sAssetAttributes2 = "_Attributes2";
        public static final String kContactType = "sContactType";
        public static final String kDateFrom = "sFrom";
        public static final String kDateTo = "sTo";
        public static final String sEmail = "sEmail";
        public static final String bUpdateComment = "bUpdateComment";
        public static final String iFloorPlanID = "iFloorPlanID";
        public static final String sMarks = "sMarks";
        public static final String LAYER_1_DATA = "getLayer1Data";
        public static final String canvasImageData = "image";
        public static final String LAYER_2_DATA = "getLayer2Data";
        public static final String objects = "objects";
        public static final String sZoomLevel = "sZoomLevel";
        public static final String sVideoZoomLevel = "sVideoZoomLevel";
        public static final String iPhotoID = "iPhotoID";
        public static final String iSPhotoID = "iSPhotoID";
        public static final String iAssetID = "iAssetID";
        public static final String iTaskID = "iTaskID";

        public static final String BYPASS_COMPULSORY = "_bByPass_Compulsory";
        public static final String LAYOUT_ID = "iLayoutID";
        public static final String RATING = "sRating";
        public static final String DATE_SCHEDULE_PICKED = "dtSchedule";
        public static final String RECENT_INS_TYPES = "recentInsTypes";
        public static final String RECENT_LAYOUT_IDS = "recentLayoutIds";
        public static final String RECENT_NOTICE_CATEGORIES = "recentNoticeCategories";

        public static final String DATE_SYNC_PRODUCTS = "dateSyncProducts";
        public static final String PRODUCT_AUTO_LOAD_ITEM = "bProduct_AutoLoadItem";

        public static final String DATE_SYNC_PROPERTY_LAYOUTS = "dateSyncPropertyLayouts";
        
        public static final String DATE_SYNC_CUSTOMER_TASKS = "dateSyncCustomerTasks";
    }

    public static class InboxType {
        private InboxType() {
            // This utility class is not publicly instantiable
        }
        public static final String CREATE_SCHEDULE = "CreateSchedule";
        public static final String UPDATE_SCHEDULE = "UpdateSchedule";
        public static final String CREATE_INS = "CreateIns";
        public static final String CREATE_TASK = "CreateTask";
        public static final String UPDATE_INS_STATUS = "UpdateInsStatus";
        public static final String UPDATE_TASK_STATUS = "UpdateTaskStatus";
        public static final String UPDATE_ASSET_STATUS = "UpdateAssetStatus";
        public static final String VIEW_INS_COMMENT = "ViewInsComments";
        public static final String VIEW_TASK_COMMENT = "Task.Comments";
    }

    public static class FixCameraOption {
        public static final String disable = "Disable";
        public static final String portrait = "Portrait";
        public static final String landscape = "Landscape";
        private FixCameraOption() {
            // This utility class is not publicly instantiable
        }
    }

    public static class CompulsoryType {
        public static final String rating = "RT";
        private CompulsoryType() {
            // This utility class is not publicly instantiable
        }
    }

    public static class ExternalLinks {
        public static final String request = "/request";
        public static final String externalRequest = "/external/request";
        public static final String syncExternalRequest = "/syncexternal/requestexternalaccess";
        public static final String getRequestInspection = "/external/getrequestinspection";
        public static final String externalSchedule = "/external/schedule";
        public static final String assetDetails = "/applink/assets/details";
        private ExternalLinks() {
            // This utility class is not publicly instantiable
        }
    }

    public static class Limits {
        // Video duration in milliseconds
        public static final int iMaxVideoDuration = 300000;
        public static final int iMaxPhotoCount = 10;
        public static final int iSinglePhotoLimit = 1;
        public static final int kFloorPlanGroupSize = 2;

        public static final int RECENT_INS_TYPES = 5;
        public static final int RECENT_LAYOUT_IDS = 5;

        public static final int iMaxiumVideoLevel = 8;
        public static final int iDisplayLengthForTasks = 200;

        public static final int PAGE_COMMENTS_DISPLAY = 10;


        public static final int PROPERTY_LAYOUT_SYNC_LIMIT = 1000;
        
        private Limits() {
            // This utility class is not publicly instantiable
        }
    }

    public static class RequestCodes {
        public static final int iSelectPhotoFromGallery = 1;
        public static final int iTakePhoto = 2;
        public static final int iVideoClips = 3;

        public static final int QR_SCANNER_REQUEST = 1000;
        public static final int CAMERA_REQUEST = 1001;
        public static final int SCAN_DOCUMENT_REQUEST = 1002;

        public static final int ADD_PRODUCT = 1003;
        public static final int REQUEST_TAKE_PHOTO = 1004;
        public static final int CUSTOM_INFO_TAKE_PHOTO = 1005;
        public static final int TASK_COMMENT_TAKE_PHOTO = 1006;
        private RequestCodes() {
            // This utility class is not publicly instantiable
        }
    }

    public static class ResultCodes {
        public static final int FORCE_SYNC = 10000;
        public static final int CAMERA_RESPONSE = 10001;
        public static final int ADD_PRODUCT_RESULT = 10002;
        public static final int REQUEST_OPEN_DOCUMENT = 10003;
        private ResultCodes() {
            // This utility class is not publicly instantiable
        }
    }

    public static class Role {
        private Role() {
            // This utility class is not publicly instantiable
        }
        public static final String COMPANY_ADMIN = "CM";
        public static final String PROPERTY_MANAGER = "PM";
        public static final String CONTRACTOR = "CT";
        public static final String CUSTOM = "CUSTOM";
    }

    public static final SimpleDateFormat dateFormat =
            new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss", Locale.US);
    
    // Timezone for UTC
    public static final TimeZone UTC = TimeZone.getTimeZone("UTC");

    public static class Values {
        public static final int kAllAssetsAssetViewID       = -1;
        public static final String kAssetPropertyPrefix     = "CV_";
        public static final String kUnitName                = "Unit";
        public static final String kNoValue                 = "--";
        public static final String sSyncDateDefault         = "1980-1-1 0:0";
        public static final String kCustomInfoDefaultLabel  = "Custom Info";
        public static final String kCustomInfoPrefix        = "CI_";
        public static final String ANONYMOUS_USER           = "Anonymous";

        public static final Size DEFAULT_IMAGE_SIZE         = new Size(900, 900);
        public static final VideoQuality DEFAULT_VIDEO_QUALITY = VideoQuality.LOW;

        public static final int AUDIO_BITRATE               = 128000;
        public static final int AUDIO_CHANNELS              = 2;
        public static final int AUDIO_SAMPLING_RATE         = 44100;
        private Values() {
            // This utility class is not publicly instantiable
        }
    }
}