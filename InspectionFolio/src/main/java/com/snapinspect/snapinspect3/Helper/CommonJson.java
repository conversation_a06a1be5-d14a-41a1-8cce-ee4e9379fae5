package com.snapinspect.snapinspect3.Helper;

import android.content.Context;

import com.google.gson.Gson;
import com.snapinspect.snapinspect3.IF_Object.*;

import com.snapinspect.snapinspect3.util.StringUtils;
import fj.parser.Result;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

/**
 * Created by terrysun1 on 11/07/15.
 */
public class CommonJson {

    public static int getCurrentCustomerID(Context context) {
        return CommonHelper.getInt(CommonHelper.GetPreferenceString(context, Constants.Keys.iCustomerID));
    }

    public static JSONObject removeNullAttributes(JSONObject jsonObject) throws JSONException {
        JSONObject result = new JSONObject();
        Iterator<String> keys = jsonObject.keys();
        while (keys.hasNext()) {
            String key = keys.next();
            Object value = jsonObject.opt(key);
            if (!jsonObject.isNull(key) && value != null && !JSONObject.NULL.equals(value)) {
                result.put(key, value);
            }
        }
        return result;
    }

    public static String AddJsonKeyValue(String sJson, String sKey, String sValue){
        JSONObject oReturn = null;
        try {
            oReturn = new JSONObject(sJson);
            oReturn.put(sKey, sValue);
        } catch(Exception ex) {
            try {
                oReturn = new JSONObject();
                oReturn.put(sKey, sValue);
            } catch (JSONException e) {
                ai_BugHandler.ai_Handler_Exception("Exception", "CommonJson.AddJsonKeyValue", e, null);
            }
        }
        return oReturn.toString();
    }
    public static boolean GetJsonKeyValue_Bool(String sKey, JSONObject oObject){
        try {
            if (oObject != null && oObject.has(sKey)){
               // if (oObject.has(sKey)){
                    return oObject.getBoolean(sKey);
                //}
            }

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonJson.GetJsonKeyValue_Bool", ex, null);
        }
        return false;
    }
    public static String GetJsonKeyValue_String(String sKey, JSONObject oObject){
        try {
            if (oObject != null && oObject.has(sKey)){
                Object value = oObject.get(sKey);
                if (value instanceof JSONObject) {
                    return value.toString();
                } else if (value instanceof String) {
                    return (String)value;
                }
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonJson.GetJsonKeyValue_String", ex, null);
        }
        return "";
    }
    public static int GetJsonKeyValue_Int(String sKey, JSONObject oObject){
        try {
            if (oObject != null && oObject.has(sKey)){
                // if (oObject.has(sKey)){
                return oObject.getInt(sKey);
                //}
            }

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonJson.GetJsonKeyValue_Int", ex, null);
        }
        return 0;
    }

    public static String[] GetStringsFromJsonArray(String sJson) {
        try {
            if (!StringUtils.isEmpty(sJson)) {
                return new Gson().fromJson(sJson, String[].class);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonJson.getJSONArrayValue - Json:" + sJson, ex, null);
        }
        return new String[] { };
    }

    public static JSONArray GetJSONArrayFromJson(String sJson) {
        try {
            if (!StringUtils.isEmpty(sJson)) {
                return new JSONArray(sJson);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonJson.getJSONArrayValue - Json:" + sJson, ex, null);
        }
        return new JSONArray();
    }

    public static JSONArray GetJSONArrayValue(String sKey, String sJson) {
        try {
            if (sJson != null && sJson.length() > 0) {
                JSONObject oReturn = new JSONObject(sJson);
                if (oReturn.has(sKey)) {
                    return oReturn.getJSONArray(sKey);
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonJson.getJSONArrayValue - Key:" + sKey + " - Json:" + sJson, ex, null);
        }
        return null;
    }

    public static List<String> GetStringsValue(String sJson, String key) {
        ArrayList<String> items = new ArrayList<>();
        try {
            if (sJson != null && sJson.length() > 0) {
                JSONArray array = new JSONArray(sJson);
                for (int i = 0, len = array.length(); i < len; i++) {
                    JSONObject object = (JSONObject) array.get(i);
                    items.add(object.has(key) ? object.getString(key) : "");
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonJson.getJSONArrayValue - Json:" + sJson, ex, null);
        }
        return items;
    }

    public static String GetJsonKeyValue(String sKey, String sJson){
        try {
            if (sJson != null && sJson.length() > 0){
                JSONObject oReturn = new JSONObject(sJson);
                if (oReturn.has(sKey)){
                    return oReturn.getString(sKey);
                }
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonJson.GetJsonKeyValue - Key:" + sKey + " - Json:" + sJson, ex, null);
        }
        return null;
    }

    public static String GetJsonKeyValue(String sKey, String sJson, String defaultValue) {
        String value = GetJsonKeyValue(sKey, sJson);
        return StringUtils.isEmpty(value) ? defaultValue : value;
    }

    public static String RemoveJsonKey(String sKey, String sJson){
        try{
            if (sJson != null && sJson.length() > 0){
                JSONObject oReturn = new JSONObject(sJson);
                if (oReturn.has(sKey)){
                    oReturn.remove(sKey);
                    return oReturn.toString();
                }
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonJson.RemoveJsonKey", ex, null);
        }
        return sJson;
    }
    public static ai_Comment oJsonToComments(JSONObject json){
        ai_Comment oComment = new ai_Comment();

        try {
            oComment.iCommentID = json.getInt("iCommentID");
            oComment.iCompanyID = json.getInt("iCompanyID");
            oComment.iCustomerID = json.getInt("iCustomerID");
            oComment.iInspectionID = json.getLong("iInspectionID");
            oComment.colorResId = CommonUI.getThemeColor(oComment.iCustomerID);

            if (null != json.getString("iTaskID")) {
                oComment.iTaskID = json.getString("iTaskID");
            }
            if (null != json.getString("sCustom1")) {
                oComment.sCustom1 = json.getString("sCustom1");
            }

            if (!json.isNull("sName") && null != json.getString("sName")) {
                oComment.sName = json.getString("sName");
            }

            if (null != json.getString("sType")) {
                oComment.sType = json.getString("sType");
            } else {
                oComment.sType = "";
            }

            if (null != json.getString("sDesp")) {
                oComment.sDescription = json.getString("sDesp");
            }

            if (null != json.getString("dtDateTime")) {
                String sDateTime = json.getString("dtDateTime");
                // Log.e("osama", "dataTime : " + sDateTime);
                Date date = CommonHelper.getDateFromString(sDateTime, "MMM dd, yyyy HH:mm");
                oComment.date = date;

                oComment.sDate = CommonHelper.getDateTimeStrFromDate(date, "EEE d/M/yyyy");
                oComment.sTime = CommonHelper.getDateTimeStrFromDate(date, "h:mm a");
            }

            if (null != json.getString("bDeleted")) {
                oComment.bDeleted = json.getBoolean("bDeleted");
            } else {
                oComment.bDeleted = false;
            }

            /*if (isPhotoComment(oComment)) {
                getDownloadableUrl(oComment, oComment.sDescription);
            }*/

        } catch (JSONException e) {
            e.printStackTrace();
        }

        return  oComment;
    }

    public static boolean isUserConfigEnabled(Context context, String sKey) {
        String configValue = CommonJson.GetJsonKeyValue(sKey,
                CommonHelper.GetPreferenceString(context, Constants.Keys.sCustomRole));
        return CommonHelper.getInt(configValue) == 1;
    }

    public static boolean isProjectModuleEnable(Context context) {
        return CommonHelper.getInt(CommonHelper.GetPreferenceString(context, Constants.Keys.bProject)) == 1 &&
                CommonHelper.getInt(CommonJson.GetJsonKeyValue(Constants.Keys.bMProjectTabHide,
                        CommonHelper.GetPreferenceString(context, Constants.Keys.sCustomRole))) != 1;
    }

    public static boolean disableEditAsset(Context context) {
        return isUserConfigEnabled(context, Constants.Keys.kDisableEditAsset);
    }

    public static boolean disableNewInspection(Context context) {
        return isUserConfigEnabled(context, Constants.Keys.kDisableNewInspection);
    }

    public static boolean disableCopyInspection(Context context) {
        return isUserConfigEnabled(context, Constants.Keys.kDisableCopyInspection);
    }

    public static boolean disableEditInspection(Context context) {
        return isUserConfigEnabled(context, Constants.Keys.kDisableQuickEdit);
    }

    public static List<Integer> getHiddenInsTypeIDs(Context context) {
        List<Integer> hiddenInsTypeIDs = new ArrayList<>();
        if (isCustomRole(context)) {
            String sHiddenInsTypeIDs = CommonJson.GetJsonKeyValue(Constants.Keys.kHiddenInsTypeIDs,
                    CommonHelper.GetPreferenceString(context, Constants.Keys.sCustomRole));
            if (!StringUtils.isEmpty(sHiddenInsTypeIDs)) {
                String[] insTypeIDs = sHiddenInsTypeIDs.split(",");
                for (String insTypeID : insTypeIDs) {
                    hiddenInsTypeIDs.add(CommonHelper.getInt(insTypeID));
                }
            }
        }
        return hiddenInsTypeIDs;
    }

    public static boolean canUseLayoutV2(Context context) {
        return CommonHelper.getInt(CommonHelper.GetPreferenceString(context, Constants.Keys.bUseLayoutV2)) == 1;
    }

    public static String getPropertyInspector(int iCustomerID) {
        List<ai_User> userList = CommonDB.GetAllUsersSugar();
        for (ai_User user : userList) {
            if (iCustomerID == user.iCustomerID) return user.sName;
        }
        return "";
    }

    public static boolean isCompanyAdmin(Context context) {
        return Constants.Role.COMPANY_ADMIN
                .equalsIgnoreCase(CommonHelper.GetPreferenceString(context, Constants.Keys.sRole));
    }

    public static boolean isPropertyManager(Context context) {
        return Constants.Role.PROPERTY_MANAGER
                .equalsIgnoreCase(CommonHelper.GetPreferenceString(context, Constants.Keys.sRole));
    }

    public static boolean isContractor(Context context) {
        return Constants.Role.CONTRACTOR
                .equalsIgnoreCase(CommonHelper.GetPreferenceString(context, Constants.Keys.sRole));
    }

    public static boolean isCustomRole(Context context) {
        return Constants.Role.CUSTOM
                .equalsIgnoreCase(CommonHelper.GetPreferenceString(context, Constants.Keys.sRole));
    }

    public static String sFolderPermission(Context context) {
        return CommonJson.GetJsonKeyValue(Constants.Keys.kFolderPermission,
                CommonHelper.GetPreferenceString(context, Constants.Keys.sCustomRole));
    }

    public static String sFolder(Context context) {
        return CommonJson.GetJsonKeyValue(Constants.Keys.kFolder, getCompanyCustom1(context));
    }

    public static boolean isProLayoutAreaOnly(Context context) {
        return 1 == CommonHelper.getInt(CommonJson.GetJsonKeyValue(
            Constants.Keys.kProLayoutAreaOnly, getCompanyCustom1(context)
        ));
    }

    public static boolean isEnabledProduct(Context context) {
        return 1 == CommonHelper.getInt(CommonJson.GetJsonKeyValue(
            Constants.Keys.kEnableProduct, getCompanyCustom1(context)
        ));
    }

    public static String getAssetPropertyKeyWhenCustom1HasDefLayout(Context ctx, String sLabel) {
        try {
            String sCustomInfo = CommonHelper.GetPreferenceString(ctx, Constants.Keys.sAssetAttributes);
            JSONArray arrCustomInfo = new JSONArray(sCustomInfo);
            for (int i = 0; i < arrCustomInfo.length(); i++) {
                try {
                    JSONObject oObject = arrCustomInfo.getJSONObject(i);
                    if (sLabel.equalsIgnoreCase(oObject.getString("sLabel")) &&
                            "1".equalsIgnoreCase(CommonJson.GetJsonKeyValue("_bLayout", oObject.getString("sCustom1")))) {
                        return Constants.Values.kAssetPropertyPrefix + oObject.getString("iAssetAttributeID");
                    }
                } catch (Exception ex) {
                    //
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return null;
    }

    public static List<O_ContactType> getContactTypes(Context context) {
        JSONArray lsContactType = GetJSONArrayValue("lsContactType",
                CommonHelper.GetPreferenceString(context, Constants.Keys.kContactType));
        if (lsContactType == null || lsContactType.length() == 0) return null;

        List<O_ContactType> contactTypes = new ArrayList<>();
        for (int i = 0; i < lsContactType.length(); i++) {
            try {
                JSONObject oObject = lsContactType.getJSONObject(i);
                O_ContactType contactType = new O_ContactType(oObject);
                contactTypes.add(contactType);
            } catch (Exception ex) {
                //
            }
        }
        return contactTypes;
    }

    // "NOT" 1 for enable tasks, otherwise disable tasks
    public static boolean bEnableTasks(Context context) {
        return 1 == CommonHelper.getInt(CommonJson.GetJsonKeyValue("NOT", getCompanyCustom1(context)));
    }

    private static String getCompanyCustom1(Context context) {
        return CommonHelper.GetPreferenceString(context, Constants.Keys.sCompanyCustom1);
    }

    // _bGPS
    // if User is Custom Role
    // if Custom Role contains _bGPS = “1”, test local GPS turned on, and can get location, (app’s config is turned on, and mobile app gps permission is turned on) otherwise stop the push and display message “Please turn on GPS location to start the inspection”.
    // When restore the session, can be test if the GPS permission still turned on or not? If not turned on then
    public static boolean shouldTurnOnLocation(Context context) {
        return "1".equalsIgnoreCase(CommonJson.GetJsonKeyValue("_bGPS", 
            CommonHelper.GetPreferenceString(context, Constants.Keys.sCustomRole)));
    }

    // _bPhoto_Date
    // if User is Custom Role,
    // if Custom Role contains _bPhoto_Date = “1” test local Photo DateTime turned on, otherwise stop the push and display message “Please turn on Photo Date/Time Stamp to start the inspection”.
    public static boolean shouldTurnOnPhotoDate(Context context) {
        return "1".equalsIgnoreCase(CommonJson.GetJsonKeyValue("_bPhoto_Date", 
            CommonHelper.GetPreferenceString(context, Constants.Keys.sCustomRole)));
    }

    // _bInst
    // if User is Custom Role, if Custom Role contains _bInst == “1”,
    // test local Instruction turned on, otherwise stop the push and display message “Please turn on Instruction from  to start the inspection”
    public static boolean shouldTurnOnInstruction(Context context) {
        return "1".equalsIgnoreCase(CommonJson.GetJsonKeyValue("_bInst",
            CommonHelper.GetPreferenceString(context, Constants.Keys.sCustomRole)));
    }

    public static Result<Boolean, ValidateCustomRoleError> validateNewInspection(Context context) {
        if (!isCustomRole(context)) return Result.result(true, null);
        if (shouldTurnOnLocation(context) && GeoLocationManager.getInstance(context).locationPermissionDenied()) {
            return Result.result(false, ValidateCustomRoleError.LOCATION);
        }
        return Result.result(true, null);
    }

    public enum ValidateCustomRoleError {
        LOCATION, // location permission denied
        GEO_TAG,
        PHOTO_DATE,
        INSTRUCTION;
    
        public String message() {
            switch (this) {
            case LOCATION: return "Please allow Location permission to Geo Tag photo.";
            case GEO_TAG: return "Please turn on GPS location to start the inspection";
            case PHOTO_DATE: return "Please turn on Photo Date/Time Stamp to start the inspection";
            case INSTRUCTION: return "Please turn on Instruction from to start the inspection";
            }
            return "";
        }
    }
}
