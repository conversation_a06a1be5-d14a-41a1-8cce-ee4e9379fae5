package com.snapinspect.snapinspect3.Helper;

import android.util.Log;
import com.google.gson.Gson;
import com.loopj.android.http.SyncHttpClient;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.util.StringUtils;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.FileEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntity;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.entity.mime.content.StringBody;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.json.JSONObject;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

//import javax.net.ssl.SSLSocketFactory;

/**
 * Created by TerrySun on 14/03/14.
 */
public class IF_SyncClient {

    // JSON: parameters are encoded as JSON string in the http body
    // FORM_DATA: parameters are encoded as form data in the http body
    public enum HTTPBodyFormat {
        JSON, FORM_DATA
    }

    private static SyncHttpClient oClient;
    
    public static int UploadFile(String sURL, String sFilePath) {
        try {
            if (oClient == null) {
                oClient = new SyncHttpClient(true, 80, 443);
            }
            //URL oURL = new URL(sURL);

            HttpPut oPut = new HttpPut(sURL);

            //SyncHttpClient dd= new SyncHttpClient();
            //String sFileName = oPhoto.sFile.replace("//", "/");
            File oFile = new File(sFilePath);
            //Log.v("sLength", sFileName);
            // if (oFile.exists()) {
            //  String bb = "cc";
            //     long bbc = oFile.length();
            // }
            oPut.setEntity(new FileEntity(oFile, "application/octet-stream"));
            // oPut.setHeader("Content-Length","" + oFile.length() );
            // Log.v("sLength", "" + oFile.length() * 8);
            HttpResponse oResponse = oClient.getHttpClient().execute(oPut);
            int iStatusCode = oResponse.getStatusLine().getStatusCode();
            String sResponse = EntityUtils.toString(oResponse.getEntity());
            Log.v("sResponseCode", "" + oResponse.getStatusLine());
            return iStatusCode;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_SyncClient.UploadPhoto", ex, null);
            return 0;
        }
    }
    public static int UploadPhoto(String sURL, ai_Photo oPhoto){
       /* try {
            URL oURL = new URL(sURL);
            Log.v("sURL", sURL);
            HttpURLConnection connection=(HttpURLConnection) oURL.openConnection();
            connection.setDoOutput(true);
            connection.setRequestMethod("PUT");
            connection.setRequestProperty("Content-Type", "image/jpeg");
            OutputStreamWriter out = new OutputStreamWriter(
                    connection.getOutputStream());
            out.write("asdfasdf");
            out.close();
            int responseCode = connection.getResponseCode();
          //  System.out.println("Service returned response code " + responseCode);
            int iStatusCode = connection.getResponseCode();
            Log.v("StatusCode", "" + iStatusCode);
            return iStatusCode;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_SyncClient.UploadPhoto", ex, null);
            return 0;
        }*/
        try{


            if (oClient == null){
                oClient = new SyncHttpClient(true, 80, 443);

            }
            //URL oURL = new URL(sURL);

            HttpPut oPut = new HttpPut(sURL);

            //SyncHttpClient dd= new SyncHttpClient();
            //String sFileName = oPhoto.sFile.replace("//", "/");
            File oFile = new File(oPhoto.getFile());
            //Log.v("sLength", sFileName);
            // if (oFile.exists()) {
            //  String bb = "cc";
            //     long bbc = oFile.length();
            // }
            oPut.setEntity(new FileEntity(oFile, "application/octet-stream"));
            // oPut.setHeader("Content-Length","" + oFile.length() );
            // Log.v("sLength", "" + oFile.length() * 8);
            HttpResponse oResponse = oClient.getHttpClient().execute(oPut);
            int iStatusCode = oResponse.getStatusLine().getStatusCode();
            String sResponse = EntityUtils.toString(oResponse.getEntity());
            Log.v("sResponseCode", "" + oResponse.getStatusLine());
            return iStatusCode;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_SyncClient.UploadPhoto", ex, null);
            return 0;
        }
    }

    public static JSONObject PostRequest(String endpointPath,  HashMap<String, String> oParameter){
        return PostRequestWithUrl(IF_RestClient.getAbsoluteUrl(endpointPath), oParameter, HTTPBodyFormat.FORM_DATA);
    }

    public static JSONObject PostRequestConnect(String endpointPath,  HashMap<String, String> oParameter){
        return PostRequestWithUrl(IF_RestClient.getConnectUrl(endpointPath), oParameter, HTTPBodyFormat.JSON);
    }

    public static JSONObject PostRequestWithUrl(String sURL,  HashMap<String, String> oParameter, HTTPBodyFormat bodyFormat) {
        try {
            if (oClient == null) {
                oClient = new SyncHttpClient(true, 80, 443);
                oClient.setTimeout(120000);
            }
            HttpPost oPost = new HttpPost(sURL);
            switch (bodyFormat) {
                case FORM_DATA: {
                    ArrayList<NameValuePair> postParameters = new ArrayList<NameValuePair>();
                    for (Map.Entry<String, String> entry : oParameter.entrySet()) {
                        //  builder.addFormDataPart(entry.getKey(), entry.getValue());
                        postParameters.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                    }
                    oPost.setEntity(new UrlEncodedFormEntity(postParameters));
                }
                case JSON: {
                    oPost.setHeader("Content-Type", "application/json");
                    oPost.setEntity(new StringEntity((new Gson().toJson(oParameter))));
                }
            }

            //SyncHttpClient oClient = new SyncHttpClient(true, 80, 443);
            HttpResponse oResponse = oClient.getHttpClient().execute(oPost);

            String sResponse = EntityUtils.toString(oResponse.getEntity());
            JSONObject oReturn = new JSONObject(sResponse);
            return oReturn;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_SyncClient.PostRequestWithUrl", ex, null);
            return null;
        }
    }

    public static JSONObject UploadData(String sURL, HashMap<String, String> oParameter, String sFilePath) {
        return UploadData(sURL, oParameter, sFilePath, "oFile");
    }

    public static JSONObject UploadData(
            String sURL, HashMap<String, String> oParameter,
            String sFilePath, String sFileFieldName){
        try{
            File oFile = new File(sFilePath);
            if (oFile.exists()){
                if (oClient == null){
                    oClient = new SyncHttpClient(true, 80, 443);
                }
                HttpPost oPost = new HttpPost(IF_RestClient.getAbsoluteUrl(sURL));
                MultipartEntity oEntity = new MultipartEntity();
               /* for (int i=0; i< lsParams.size(); i++){
                    NameValuePair oName = lsParams.get(i);
                    oEntity.addPart(oName.getName(), new StringBody(oName.getValue()));
                }*/


                for (Map.Entry<String, String> entry : oParameter.entrySet()){
                    //  builder.addFormDataPart(entry.getKey(), entry.getValue());
                    //postParameters.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                    oEntity.addPart(entry.getKey(), new StringBody(entry.getValue()));
                }


                oEntity.addPart(sFileFieldName, new FileBody(oFile));
                oPost.setEntity(oEntity);
                HttpResponse oResponse = oClient.getHttpClient().execute(oPost);

                String sResponse = EntityUtils.toString(oResponse.getEntity());
                //JSONTokener tokener = new JSONTokener(sResponse);
                JSONObject oReturn = new JSONObject(sResponse);

                return oReturn;
            }
            return null;
        }catch(Exception ex){

            ai_BugHandler.ai_Handler_Exception("Exception", "IF_SyncClient.UploadData", ex, null);
            return null;
        }
    }

    public static String DownloadFile(String sURL, String sFileName) {
        try{
            String sResult = DownloadData(sURL);
            if (!StringUtils.isEmpty(sResult)){
                CommonHelper.SaveStringToFilesDir(sFileName, sResult);
                return sFileName;
            }
            return null;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_SyncClient.DownloadFile", ex, null);
            return null;
        }
    }
    public static String DownloadData(String sURL) {
        try {
            if (!StringUtils.isEmpty(sURL)) {
                OkHttpClient client = new OkHttpClient();
                Request request = new Request.Builder().url(sURL).build();
                okhttp3.Response response = client.newCall(request).execute();
                if (response.isSuccessful()) {
                    return response.body().string();
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_SyncClient.DownloadData", ex, null);
        }
        return null;
    }
}