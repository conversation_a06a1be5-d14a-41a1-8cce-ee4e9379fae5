package com.snapinspect.snapinspect3.Helper;

import android.content.Context;
// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.IF_Object.ai_ProjectInspection;
import com.snapinspect.snapinspect3.R;

import java.util.List;

public class CommonProjectInspection {
    private CommonProjectInspection() {
        throw new IllegalStateException("Utility class");
    }

    public static void startInspection(Context context, int iSProjectInspectionID, int iInspectionID) {
        // Update iInspectionID in ai_ProjectInspection
        List<ai_ProjectInspection> lsPIns =
                CommonDB.findProjectInspectionsByInsId(iSProjectInspectionID);
        if (!lsPIns.isEmpty()) {
            ai_ProjectInspection oProjectInspection = lsPIns.get(0);
            oProjectInspection.iInspectionID = iInspectionID;
            // TODO: Implement save method for ProjectInspection in CommonDB
        }

        // Lock project inspection
        NetworkUtils.lockProjectInspection(context, iSProjectInspectionID,
                CommonHelper.getCurrentUserID(context), projectInspection -> {
                    if (projectInspection != null)
                        projectInspection.checkIfExistAndSave();
                });
    }
}
