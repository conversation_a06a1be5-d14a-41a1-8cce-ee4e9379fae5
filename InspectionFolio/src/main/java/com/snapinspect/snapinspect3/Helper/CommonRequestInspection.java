package com.snapinspect.snapinspect3.Helper;

import android.content.Context;

import com.snapinspect.snapinspect3.IF_Object.ai_AssetLayout;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_InsType;
import com.snapinspect.snapinspect3.IF_Object.ai_Layout;
import com.snapinspect.snapinspect3.database.entities.InsItem;
import com.snapinspect.snapinspect3.database.entities.Layout;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Created by <PERSON><PERSON> on 17/01/17.
 */
public class CommonRequestInspection {
    private static JSONObject LoadRequestInspectionJson(String sFilePath){
        try{
            String sText = ReadFileToText(sFilePath);
            if (sText != null && sText.length() > 0){
                JSONObject oReturn = new JSONObject(sText);
                return oReturn;
            }
        }catch(Exception ex){

        }
        return null;
    }
    public static void SaveChildLayoutItemDefault(ai_InsItem oInsItem, Context oContext, int iInspectionID, String sFilePath, ai_InsType oInsType){
        //long lPInsItemID = oInsItem.getId();
        // int iPInsItemID = (int)lPInsItemID;
        try {
            List<ai_Layout> lsChildLayout = CommonDB_Inspection.bypassLayouts(LoadLayout(oInsItem.iSLayoutID, sFilePath), oInsType);
            List<Layout> roomLayouts = new ArrayList<>();
            for (ai_Layout aiLayout : lsChildLayout) {
                roomLayouts.add(new Layout(aiLayout));
            }
            CommonDB.AddChildLayout(new InsItem(oInsItem), oContext, iInspectionID, roomLayouts);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.SaveChildLayoutItemDefault", ex, oContext);
        }

    }
    public static ai_InsType LoadInsType(String sFilePath){
        JSONObject oReturn = LoadRequestInspectionJson(sFilePath);

        if (oReturn != null) {
            try {
                JSONObject oObject = oReturn.getJSONObject("oInsType");
                if (oObject != null){
                    ai_InsType oInsType = new ai_InsType();
                    oInsType.iSInsTypeID = CommonJson.GetJsonKeyValue_Int("iInsTypeID", oObject);
                    oInsType.sPTC = CommonJson.GetJsonKeyValue_String("sPTC", oObject);
                    oInsType.sInsTitle = CommonJson.GetJsonKeyValue_String("sTitle", oObject);
                    oInsType.sType = CommonJson.GetJsonKeyValue_String("sType", oObject);
                    oInsType.bRemLayout = CommonJson.GetJsonKeyValue_Bool("bRemLayout", oObject);
                    oInsType.sFieldOne = CommonJson.GetJsonKeyValue_String("sCustom1", oObject);
                    oInsType.sFieldTwo = CommonJson.GetJsonKeyValue_String("sCustom2", oObject);
                    oInsType.bDeleted = CommonJson.GetJsonKeyValue_Bool("bDeleted", oObject);
                    return oInsType;
                }

            }catch(Exception ee){

            }
        }
        return null;
    }

    public static List<ai_Layout> GetDisplayedLayout(String sType, String sFilePath){
        JSONObject oReturn = LoadRequestInspectionJson(sFilePath);

        if (oReturn != null) {
            try {
                ArrayList<ai_Layout> lsLayout = new ArrayList<ai_Layout>();
                JSONArray arAssetLayout = oReturn.getJSONArray("lsLayout");
                for (int i=0; i< arAssetLayout.length(); i++){
                    JSONObject oObject = arAssetLayout.getJSONObject(i);
                    String sConfig = sType.equals("F") ? CommonJson.GetJsonKeyValue_String("sFConfig", oObject)    : CommonJson.GetJsonKeyValue_String("sSConfig", oObject);
                    if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null && (CommonJson.GetJsonKeyValue_Int("iPLayoutID", oObject) == 0)){
                        lsLayout.add(JObjectToLayout(oObject));
                    }
                }
                return lsLayout;
            }catch(Exception ee){

            }
        }
        return null;


    }
    //sType = 'F' or 'S'
    public static List<ai_Layout> LoadLayout(int iPLayoutID, String sFilePath){
        JSONObject oReturn = LoadRequestInspectionJson(sFilePath);

        if (oReturn != null) {
            try {
                ArrayList<ai_Layout> lsLayout = new ArrayList<ai_Layout>();
                JSONArray arAssetLayout = oReturn.getJSONArray("lsLayout");
                for (int i=0; i< arAssetLayout.length(); i++){
                    JSONObject oObject = arAssetLayout.getJSONObject(i);
                    ai_Layout oLayout = JObjectToLayout(oObject);
                    if (oLayout.iSPLayoutID == iPLayoutID) {
                        lsLayout.add(oLayout);
                    }
                }
                return lsLayout;
            }catch(Exception ee){

            }
        }
        return null;
    }
    private static ai_Layout JObjectToLayout(JSONObject oObject){
        ai_Layout oLayout = new ai_Layout();
        oLayout.iSLayoutID = CommonJson.GetJsonKeyValue_Int("iLayoutID", oObject);
        oLayout.iSPLayoutID = CommonJson.GetJsonKeyValue_Int("iPLayoutID", oObject);
        oLayout.sPTC = CommonJson.GetJsonKeyValue_String("sPTC", oObject);
        oLayout.sQType = CommonJson.GetJsonKeyValue_String("sQType", oObject);
        oLayout.sName = CommonJson.GetJsonKeyValue_String("sName", oObject);
        oLayout.sFVOneConfig = CommonJson.GetJsonKeyValue_String("sFV1Config", oObject);
        oLayout.sFVTwoConfig = CommonJson.GetJsonKeyValue_String("sFV2Config", oObject);
        oLayout.sFVThreeConfig = CommonJson.GetJsonKeyValue_String("sFV3Config", oObject);
        oLayout.sFVFourConfig = CommonJson.GetJsonKeyValue_String("sFV4Config", oObject);
        oLayout.sFVFiveConfig = CommonJson.GetJsonKeyValue_String("sFV5Config", oObject);
        oLayout.sFVSixConfig = CommonJson.GetJsonKeyValue_String("sFV6Config", oObject);
        oLayout.sSVOneConfig = CommonJson.GetJsonKeyValue_String("sSV1Config", oObject);
        oLayout.sSVTwoConfig = CommonJson.GetJsonKeyValue_String("sSV2Config", oObject);
        oLayout.sSVThreeConfig = CommonJson.GetJsonKeyValue_String("sSV3Config", oObject);
        oLayout.sSVFourConfig = CommonJson.GetJsonKeyValue_String("sSV4Config", oObject);
        oLayout.sSVFiveConfig = CommonJson.GetJsonKeyValue_String("sSV5Config", oObject);
        oLayout.sSVSixConfig= CommonJson.GetJsonKeyValue_String("sSV6Config", oObject);
        oLayout.sFConfig = CommonJson.GetJsonKeyValue_String("sFConfig", oObject);
        oLayout.sSConfig = CommonJson.GetJsonKeyValue_String("sSConfig", oObject);
        oLayout.sFieldTwo = CommonJson.GetJsonKeyValue_String("iMark", oObject);
        oLayout.sFieldThree = CommonJson.GetJsonKeyValue_String("iSort", oObject);
        return oLayout;
    }
    public static List<ai_AssetLayout> LoadAssetLayout(String sFilePath){
        JSONObject oReturn = LoadRequestInspectionJson(sFilePath);
        ArrayList<ai_AssetLayout> lsAssetLayout = new ArrayList<ai_AssetLayout>();
        if (oReturn != null && oReturn.length() > 0) {
            try {
                JSONArray arAssetLayout = oReturn.getJSONArray("lsPropertyLayout");
                for (int i=0; i< arAssetLayout.length(); i++){
                    JSONObject oObject = arAssetLayout.getJSONObject(i);
                    ai_AssetLayout oAssetLayout = new ai_AssetLayout();
                    oAssetLayout.iSAssetLayoutID = CommonJson.GetJsonKeyValue_Int("iPropertyLayoutID", oObject);
                    oAssetLayout.iSLayoutID = CommonJson.GetJsonKeyValue_Int("iLayoutID",oObject);

                    oAssetLayout.iSAssetID = CommonJson.GetJsonKeyValue_Int("iPropertyID",oObject);
                    oAssetLayout.iSort = CommonJson.GetJsonKeyValue_Int("iSort",oObject);
                    oAssetLayout.sName = CommonJson.GetJsonKeyValue_String("sName",oObject);
                    oAssetLayout.sChildID = CommonJson.GetJsonKeyValue_String("sChildID",oObject);
                    oAssetLayout.sMoreItems = CommonJson.GetJsonKeyValue_String("sMoreItems",oObject);
                    lsAssetLayout.add(oAssetLayout);
                }

                Collections.sort(lsAssetLayout, new Comparator<ai_AssetLayout>() {
                    @Override
                    public int compare(ai_AssetLayout o1, ai_AssetLayout o2) {
                        return o1.iSort - o2.iSort;
                    }
                });

                return lsAssetLayout;
            }catch(Exception ee){

            }
        }
        return null;
    }
    public static String ReadFileToText(String sPath){
        try {
            File file = new File(sPath);
            FileInputStream stream = new FileInputStream(file);
            String jsonStr = null;
            try {
                FileChannel fc = stream.getChannel();
                MappedByteBuffer bb = fc.map(FileChannel.MapMode.READ_ONLY, 0, fc.size());

                jsonStr = Charset.defaultCharset().decode(bb).toString();
                stream.close();
                return jsonStr;
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                stream.close();
            }
        }catch(Exception ee){

        }
        return null;
    }
}
