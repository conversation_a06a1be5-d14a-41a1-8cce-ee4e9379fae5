package com.snapinspect.snapinspect3.Helper;

import android.content.Context;
import com.google.gson.*;
import com.loopj.android.http.RequestParams;
// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_FloorPlan;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.helper.DownloadImageTask;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.squareup.picasso.Picasso;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * The CommonFloorPlan class provides methods to load blueprints from the server.
 */
public class CommonFloorPlan {
    public interface RequestCompletion<T> {
        void onSuccess(T result);
        void onFailure(Error error);
    }

    /**
     * This interface represents a listener for blueprint updates.
     */
    private interface UpdateFloorPlanListener {
        void onUpdating(ai_FloorPlan floorPlan);
        void onCompletion();
    }

    /**
     * The UpdatingFloorPlansListener interface provides a callback method for updating blueprints progress.
     */
    public interface UpdatingFloorPlansListener {
        void onUpdating(int progress, int total);
    }

    /**
     * Loads blueprints for a given asset ID.
     *
     * @param context                     the Android context
     * @param iSAssetID                   the asset ID (Server ID)
     * @param requestCompletion           the completion block for handling success or failure
     * @param updatingFloorPlansListener  the listener for updating blueprints progress
     */
    public static void loadFloorPlans(
        Context context, int iSAssetID,
        RequestCompletion<List<ai_FloorPlan>> requestCompletion,
        UpdatingFloorPlansListener updatingFloorPlansListener
    ) {
        CommonRequest.requestURL(context, "/IOAPI/GetAssetFloorPlans",
            () -> {
                RequestParams oParams = new RequestParams();
                oParams.add(Constants.Keys.iCustomerID, CommonHelper.GetPreferenceString(context, Constants.Keys.iCustomerID));
                oParams.add(Constants.Keys.sToken, CommonHelper.GetPreferenceString(context, Constants.Keys.sToken));
                oParams.add("iAssetID", "" + iSAssetID);
                return oParams;
            },
            (response, error) -> {
                if (error != null) {
                    requestCompletion.onFailure(error);
                    return;
                }
                try {
                    if (response != null && response.getBoolean("success")) {
                        CommonHelper.validateFloorPlanFolderExist();
                        JSONArray lsFloorPlanJson = response.getJSONArray("lsFloorPlan");
                        List<ai_FloorPlan> floorPlans = new ArrayList<>();
                        List<ai_FloorPlan> needUpdateFloorPlans = new ArrayList<>();
                        for (int i = 0; i < lsFloorPlanJson.length(); i++) {
                            JSONObject floorPlanJson = lsFloorPlanJson.getJSONObject(i);
                            ai_FloorPlan floorPlan = new ai_FloorPlan(floorPlanJson);
                            ai_FloorPlan existingPlan = CommonDB_FloorPlan.getFloorPlan(floorPlan.iSFloorPlanID);
                            if (existingPlan != null) {
                                // set the local id
                                floorPlan.setId(existingPlan.getId());
                                // if there is existing blueprint, check if it needs to be updated
                                if (existingPlan.dtUpdate == null || floorPlan.dtUpdate.after(existingPlan.dtUpdate)) {
                                    needUpdateFloorPlans.add(floorPlan);
                                }
                            } else {
                                needUpdateFloorPlans.add(floorPlan);
                            }

                            floorPlan.sPlanPath = floorPlan.getPlanFileName();
                            floorPlan.sImagePath = floorPlan.getImageFileName();
                            floorPlans.add(floorPlan);
                        }

                        CommonDB.saveFloorPlansInTransaction(floorPlans);

                        // removed the blueprints that is not in the latest list
                        List<ai_FloorPlan> existingFloorPlans = CommonDB_FloorPlan.getFloorPlans(iSAssetID);
                        List<ai_FloorPlan> needRemoveFloorPlans = new ArrayList<>();
                        for (ai_FloorPlan existingFloorPlan : existingFloorPlans) {
                            if (!floorPlans.contains(existingFloorPlan)) {
                                needRemoveFloorPlans.add(existingFloorPlan);
                            }
                        }
                        CommonDB.deleteFloorPlansInTransaction(needRemoveFloorPlans);

                        // Update the blueprints that need to be updated
                        int countOfNeedUpdateFloorPlans = needUpdateFloorPlans.size();
                        final int[] currentProgress = {0};
                        updateFloorPlans(context, needUpdateFloorPlans, new UpdateFloorPlanListener() {
                            @Override
                            public void onUpdating(ai_FloorPlan floorPlan) {
                                currentProgress[0]++;
                                updatingFloorPlansListener.onUpdating(currentProgress[0], countOfNeedUpdateFloorPlans);
                            }

                            @Override
                            public void onCompletion() {
                                requestCompletion.onSuccess(floorPlans);
                            }
                        });
                    } else {
                        requestCompletion.onFailure(new Error(response.getString("message")));
                    }
                } catch (Exception ex) {
                    requestCompletion.onFailure(new Error(context.getString(R.string.error_load_floor_plan)));
                }
            }
        );
    }

    /**
     * Updates the blueprints based on the provided list of blueprints.
     *
     * @param context - The Android context
     * @param floorPlans - The list of blueprints to update
     * @param listener - The listener for updating the blueprints progress
     */
    private static void updateFloorPlans(Context context, List<ai_FloorPlan> floorPlans, UpdateFloorPlanListener listener) {
        if (floorPlans.isEmpty()) {
            listener.onCompletion();
            return;
        }
        ai_FloorPlan floorPlan = floorPlans.get(0);
        listener.onUpdating(floorPlan);
        loadFloorPlan(context, floorPlan.iSFloorPlanID, new RequestCompletion<ai_FloorPlan>() {
            @Override
            public void onSuccess(ai_FloorPlan result) {
                floorPlans.remove(0);
                updateFloorPlans(context, floorPlans, listener);
            }

            @Override
            public void onFailure(Error error) {
                floorPlans.remove(0);
                updateFloorPlans(context, floorPlans, listener);
            }
        });
    }

    public static void loadFloorPlan(Context context, int iSFloorPlanID, RequestCompletion<ai_FloorPlan> completion) {
        CommonRequest.requestURL(context, "/IOAPI/GetFloorPlan",
            () -> {
                RequestParams oParams = new RequestParams();
                oParams.add(Constants.Keys.iCustomerID, CommonHelper.GetPreferenceString(context, Constants.Keys.iCustomerID));
                oParams.add(Constants.Keys.sToken, CommonHelper.GetPreferenceString(context, Constants.Keys.sToken));
                oParams.add(Constants.Extras.iFloorPlanID, "" + iSFloorPlanID);
                return oParams;
            },
            (response, error) -> {
                if (error != null) {
                    completion.onFailure(error);
                    return;
                }
                try {
                    if (response != null && response.getBoolean("success")) {
                        JSONObject oFloorPlanJson = response.getJSONObject("oFloorPlan");
                        ai_FloorPlan oFloorPlan = new ai_FloorPlan(oFloorPlanJson);

                        // Assign the local id if the floor oFloorPlan already exists
                        ai_FloorPlan existingPlan = CommonDB_FloorPlan.getFloorPlan(oFloorPlan.iSFloorPlanID);
                        if (existingPlan != null) oFloorPlan.setId(existingPlan.getId());

                        downloadFloorPlanImages(oFloorPlan, completion);
                    } else {
                        completion.onFailure(new Error(response.getString("message")));
                    }
                } catch (Exception ex) {
                    completion.onFailure(new Error(context.getString(R.string.error_load_floor_plan)));
                }
            }
        );
    }

    private static void downloadFloorPlanImages(ai_FloorPlan oFloorPlan, RequestCompletion<ai_FloorPlan> completion) throws IOException {
        final boolean[] isDownloadPlanFile = {true};
        final boolean[] isDownloadImageFile = {true};
        // download the floor oFloorPlan image if it doesn't exist
        if (!StringUtils.isEmpty(oFloorPlan.sPlanPath) && !CommonHelper.bFileExist(oFloorPlan.savedPlanFilePath())) {
            isDownloadPlanFile[0] = false;
            String sPlanPath = oFloorPlan.sPlanPath;
            new DownloadImageTask(oFloorPlan.savedPlanFilePath(), bitmap -> {
                isDownloadPlanFile[0] = true;
                if (isDownloadImageFile[0]) {
                    CommonDB.saveFloorPlan(oFloorPlan);
                    completion.onSuccess(oFloorPlan);
                }
                // clear the Picasso cache
                Picasso.get().invalidate(new File(oFloorPlan.savedPlanFilePath()));
                return null;
            }).load(sPlanPath);
        }
        oFloorPlan.sPlanPath = oFloorPlan.getPlanFileName();

        if (StringUtils.isEmpty(oFloorPlan.sImagePath)) {
            // if the floor oFloorPlan image is not available, use the floor oFloorPlan image
            FileUtils.copyFile(new File(oFloorPlan.savedPlanFilePath()), new File(oFloorPlan.savedImageFilePath()), false);
            // clear the Picasso cache
            Picasso.get().invalidate(new File(oFloorPlan.savedImageFilePath()));
        } else {
            // download the floor oFloorPlan thumbnail if it doesn't exist
            isDownloadImageFile[0] = false;
            String sImageURL = oFloorPlan.sImagePath;
            new DownloadImageTask(oFloorPlan.savedImageFilePath(), bitmap -> {
                isDownloadImageFile[0] = true;
                if (isDownloadPlanFile[0]) {
                    CommonDB.saveFloorPlan(oFloorPlan);
                    completion.onSuccess(oFloorPlan);
                }
                // clear the Picasso cache
                Picasso.get().invalidate(new File(oFloorPlan.savedImageFilePath()));
                return null;
            }).load(sImageURL);
        }
        oFloorPlan.sImagePath = oFloorPlan.getImageFileName();

        if (isDownloadPlanFile[0] && isDownloadImageFile[0]) {
            CommonDB.saveFloorPlan(oFloorPlan);
            completion.onSuccess(oFloorPlan);
        }
    }

    /**
     * Load blueprints for a given inspection item ID.
     *
     * @param context     the Android context
     * @param iSInsItemID the inspection item ID
     * @param completion  the completion block for handling success or failure
     */
    public static void loadInsItemFloorPlans(Context context, int iSInsItemID, RequestCompletion<List<ai_FloorPlan>> completion) {
        CommonRequest.requestURL(context, "/IOAPI/GetItemFloorPlans",
            () -> {
                RequestParams oParams = new RequestParams();
                oParams.add(Constants.Keys.iCustomerID, CommonHelper.GetPreferenceString(context, Constants.Keys.iCustomerID));
                oParams.add(Constants.Keys.sToken, CommonHelper.GetPreferenceString(context, Constants.Keys.sToken));
                oParams.add("iInsItemID", "" + iSInsItemID);
                return oParams;
            },
            (response, error) -> {
                if (error != null) {
                    completion.onFailure(error);
                    return;
                }
                try {
                    if (response != null && response.getBoolean("success")) {
                        CommonHelper.validateFloorPlanFolderExist();
                        completion.onSuccess(new ArrayList<>());
                    } else {
                        completion.onFailure(new Error(response.getString("message")));
                    }
                } catch (Exception ex) {
                    completion.onFailure(new Error(context.getString(R.string.error_load_floor_plan)));
                }
            }
        );
    }

    public static String processInsItemFloorPlanPhoto(ai_InsItem insItem, String sCustomTwo) {
        // If you have iPhotoID > 0 in sCustomTwo, then find the iSPhotoID and add it to the JObject.
        // [{”iFloorPlanID”:1111, “iPhotoID”:1111, “iSPhotoID”:6754833}]
        if (StringUtils.isEmpty(sCustomTwo)) return sCustomTwo;
        List<Map<String, Object>> dropPins = db_InsItem.getDropPinData(insItem);
        for (Map<String, Object> dropPin : dropPins) {
            try {
                Object iPhotoIDObj = dropPin.get(Constants.Keys.iPhotoID);
                if (!(iPhotoIDObj instanceof Double)) continue;

                int iPhotoID = ((Double) iPhotoIDObj).intValue();
                if (iPhotoID > 0) {
                    ai_Photo photo = CommonDB.GetPhotoByIdSugar(iPhotoID);
                    if (photo != null && photo.iSPhotoID > 0) {
                        dropPin.put(Constants.Keys.iSPhotoID, photo.iSPhotoID);
                        dropPin.remove(Constants.Keys.iPhotoID);
                    }
                }
            } catch (Exception e) {
                ai_BugHandler.ai_Handler_Exception("if_InsItemFloorPlans.dropPinsToFloorPlans", "Exception:" + e.getMessage(), e);
            }
        }

        Gson gson = new GsonBuilder().registerTypeAdapter(Double.class,
                (JsonSerializer<Double>) (src, typeOfSrc, context) -> {
            if (src == src.longValue()) return new JsonPrimitive(src.longValue());
            return new JsonPrimitive(src);
        }).create();
        return gson.toJson(dropPins);
    }
}
