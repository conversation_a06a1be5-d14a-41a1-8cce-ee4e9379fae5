package com.snapinspect.snapinspect3.Helper;

import android.annotation.SuppressLint;
import android.app.ActionBar;
import android.app.Activity;
import android.app.AlertDialog;
import android.app.ProgressDialog;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.graphics.Rect;
import android.hardware.camera2.CameraCharacteristics;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.Window;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Toast;
import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.camera.camera2.interop.Camera2CameraInfo;
import androidx.camera.core.CameraInfo;
import androidx.camera.core.CameraSelector;
import androidx.camera.core.ZoomState;
import androidx.camera.lifecycle.ProcessCameraProvider;
import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_User;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayFile;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONException;

import java.util.ArrayList;
import java.util.List;

import static android.provider.Settings.ACTION_APPLICATION_DETAILS_SETTINGS;

/**
 * Created by terrysun on 6/02/18.
 */

public class CommonUI {

    public static boolean isTablet(Context ctx) {
        int screenLayout = ctx.getResources().getConfiguration().screenLayout
                & Configuration.SCREENLAYOUT_SIZE_MASK;
        boolean xlarge = screenLayout == Configuration.SCREENLAYOUT_SIZE_XLARGE;
        boolean large = screenLayout == Configuration.SCREENLAYOUT_SIZE_LARGE;
        return xlarge || large;
    }

    public static boolean bAppPermission(Activity oContext) {
        if (!CommonValidate.Permission_Validate(oContext)) {
            Toast.makeText(oContext, "Please enable permission.", Toast.LENGTH_LONG).show();
            return false;
        }
        CommonHelper.ValidateTempFolderExist();
        return true;
    }

    public static boolean bEnableWord(Context oContext) {
        try {
            return CommonHelper.GetPreferenceBoolean(oContext, "bEnableWord");
        } catch (Exception ex) {

        }
        return false;
    }

    public static ProgressDialog GetProgressDialog(Context oContext, String sTitle, String sMessage, boolean bShow) {
        try {
            ProgressDialog oDialog;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                try {
                    oDialog = new ProgressDialog(oContext);
                } catch (Exception eee) {
                    try {
                        oDialog = new ProgressDialog(oContext);
                    } catch (Exception eeee) {
                        oDialog = new ProgressDialog(oContext);
                    }
                }
            } else {
                oDialog = new ProgressDialog(oContext);

            }
            if (sTitle != null && sTitle.trim().length() > 0) {
                oDialog.setTitle(sTitle);

            }
            if (sMessage != null && sMessage.trim().length() > 0) {
                oDialog.setMessage(sMessage);

            }
            if (bShow) {
                oDialog.show();
            }
            oDialog.setCancelable(false);
            oDialog.setCanceledOnTouchOutside(false);
            return oDialog;
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return new ProgressDialog(oContext);
    }

    public static MaterialDialog ShowMaterialProgressDialog(Context oContext, String sTitle, String sMessage) {
        return new MaterialDialog.Builder(oContext)
                .title(sTitle)
                .content(sMessage)
                .progress(true, 0)
                .cancelable(false)
                .show();

    }

    public static void DismissMaterialProgressDialog(MaterialDialog oDialog) {
        if (oDialog != null && oDialog.isShowing()) oDialog.dismiss();
    }

    public static String GetUserColorString(int iCustomerId) {
        List<ai_User> users = CommonDB.GetAllUsersSugar();

        int count = 0;
        for (int i = 0, length = users.size(); i < length; i++) {
            if (users.get(i).iCustomerID == iCustomerId) {
                count = i;
                break;
            }
        }

        int idx = count % Constants.USER_COLORS.length;

        return Constants.USER_COLORS[idx];
    }

    public static Boolean bBelowLollipop() {
        return Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP;
    }

    public static AlertDialog.Builder GetAlertBuilder(String sTitle, String sMessage, Context oContext, boolean bShowNegativeButton, boolean bOKDismiss) {
        try {
            AlertDialog.Builder builder;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                try {
                    builder = new AlertDialog.Builder(oContext);
                } catch (Exception eee) {
                    try {
                        builder = new AlertDialog.Builder(oContext);
                    } catch (Exception eeee) {
                        builder = new AlertDialog.Builder(oContext);
                    }
                }
            } else {
                builder = new AlertDialog.Builder(oContext);
            }

            builder.setTitle(sTitle);
            if (sMessage != null && sMessage.trim().length() > 0) {
                builder.setMessage(sMessage);
            }
            if (bOKDismiss) {
                builder.setPositiveButton("OK", (dialog, which) -> dialog.dismiss());
            } else {
                if (bShowNegativeButton) {
                    builder.setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss());
                }
            }
            return builder;
        } catch (Exception ex) {

        }
        return new AlertDialog.Builder(oContext, R.style.DialogTheme);
    }

    public static void ShowAlert(Context oContext, String sTitle, String sMessage) {
        ShowAlert(oContext, sTitle, sMessage, R.string.tv_ok);
    }

    public static void ShowAlert(Context oContext, String sTitle, String sMessage, int positiveText) {
        ShowAlert(oContext, sTitle, sMessage, positiveText, null);
    }

    public static void ShowAlert(Context oContext, String sTitle, String sMessage, int positiveText, Constants.OnConfirmListener listener) {
        ShowAlert(oContext, sTitle, sMessage, positiveText, 0, listener);
    }

    public static void ShowAlert(Context oContext, String sTitle, String sMessage, int positiveText, int negativeText, Constants.OnConfirmListener listener) {
        if (oContext == null) return;

        if (oContext instanceof Activity) {
            Activity activity = (Activity) oContext;
            if (activity.isFinishing() || activity.isDestroyed()) {
                return;
            }
        }

        try {
            new MaterialDialog.Builder(oContext)
                    .title(sTitle)
                    .content(sMessage)
                    .positiveText(positiveText)
                    .negativeText(negativeText)
                    .onPositive((dialog, which) -> {
                        if (listener != null) listener.onConfirm();
                    })
                    .show();
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }
    }

    public static float dpToPx(float dp, Context ctx) {
        return dp * ((float) ctx.getResources().getDisplayMetrics().densityDpi / DisplayMetrics.DENSITY_DEFAULT);
    }

    public static int dpToPx(int dp, Context ctx) {
        return dp * (ctx.getResources().getDisplayMetrics().densityDpi / DisplayMetrics.DENSITY_DEFAULT);
    }

    public static int pxToDp(int px, Context ctx) {
        return px / (ctx.getResources().getDisplayMetrics().densityDpi / DisplayMetrics.DENSITY_DEFAULT);
    }

    public static float pxToDp(float px, Context ctx) {
        return px / (ctx.getResources().getDisplayMetrics().densityDpi / DisplayMetrics.DENSITY_DEFAULT);
    }

    public static int getScreenWidth() {
        return Resources.getSystem().getDisplayMetrics().widthPixels;
    }

    public static int getScreenHeight() {
        return Resources.getSystem().getDisplayMetrics().heightPixels;
    }

    public static int getStatusBarHeight(Activity activity) {
        Rect rectangle = new Rect();
        Window window = activity.getWindow();
        window.getDecorView().getWindowVisibleDisplayFrame(rectangle);
        int statusBarHeight = rectangle.top;
        int contentViewTop =
                window.findViewById(Window.ID_ANDROID_CONTENT).getTop();
        return contentViewTop - statusBarHeight;
    }

    public static void insertText(EditText oEditText, String sSelectedText) {
        if (StringUtils.isEmpty(sSelectedText)) return;
        String sText = oEditText.getText().toString();
        int start = 0, end = sText.length() - 1, select = oEditText.getSelectionStart();

        String prefix = sText.substring(start, select);
        if (!StringUtils.isEmpty(prefix) && !prefix.endsWith(" ")) {
            sSelectedText = " " + sSelectedText;
        }

        String suffix = sText.substring(oEditText.getSelectionEnd());
        if (!StringUtils.isEmpty(suffix) && suffix.startsWith(" ")) {
            oEditText.getText().replace(select, end, suffix.substring(1));
        }

        oEditText.getText().insert(select, sSelectedText + " ");
    }

    public static void longToast(Context ctx, String message) {
        CommonHelper.trackEvent(ctx, "LONG_TOAST", "message", message);
        new Handler(Looper.getMainLooper()).post(() -> Toast.makeText(ctx, message, Toast.LENGTH_LONG).show());
    }

    public static void longToast(Context ctx, @StringRes int message) {
        CommonHelper.trackEvent(ctx, "LONG_TOAST", "message", ctx.getString(message));
        new Handler(Looper.getMainLooper()).post(() -> Toast.makeText(ctx, message, Toast.LENGTH_LONG).show());
    }

    public static void reloadListViewRow(ListView mListView, int position) {
        if (mListView == null) return;
        int visiblePosition = mListView.getFirstVisiblePosition();
        View view = mListView.getChildAt(position - visiblePosition);
        mListView.getAdapter().getView(position, view, mListView);
    }

    public static void viewRemoteFile(Context mContext, final int finalFileId) {
        final MaterialDialog progressDialog = ShowMaterialProgressDialog(
                mContext, "Message", "Processing. Please wait ...");
        CommonRequest.getFileDownloadableURL(mContext, finalFileId, (response, error) -> {
            CommonUI.DismissMaterialProgressDialog(progressDialog);
            if (response != null) {
                try {
                    String url = response.getString("sDownloadURL");
                    if (StringUtils.isEmpty(url)) return;
                    String mimeType = FileUtils.getMimeType(url);
                    if (!StringUtils.isEmpty(mimeType)) {
                        if (mimeType.startsWith("image")) {
                            mContext.startActivity(if_DisplayFile.newIntent(mContext, url));
                        } else {
                            Uri uri = Uri.parse(url);
                            Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                            mContext.startActivity(intent);
                        }
                    }
                } catch (JSONException e) {
                    CommonUI.ShowAlert(mContext, "Error", "Please try again later.");
                }
            } else if (error != null) {
                CommonUI.ShowAlert(mContext, "Error", error.getMessage());
            }

            CommonUI.DismissMaterialProgressDialog(progressDialog);
        });
    }

    @ColorRes
    public static int getThemeColor(int iCustomerID) {
        int[] allColors = {
                R.color.user_theme_color_1,
                R.color.user_theme_color_2,
                R.color.user_theme_color_3,
                R.color.user_theme_color_4,
                R.color.user_theme_color_5,
                R.color.user_theme_color_6,
                R.color.user_theme_color_7,
                R.color.user_theme_color_8,
                R.color.user_theme_color_9,
                R.color.user_theme_color_10,
                R.color.user_theme_color_11,
                R.color.user_theme_color_12,
                R.color.user_theme_color_13,
                R.color.user_theme_color_14,
                R.color.user_theme_color_15,
                R.color.user_theme_color_16,
                R.color.user_theme_color_17,
                R.color.user_theme_color_18,
                R.color.user_theme_color_19,
                R.color.user_theme_color_20,
        };

        List<ai_User> userList = CommonDB.GetAllUsersSugar();
        for (int i = 0; i < userList.size(); i++) {
            if (userList.get(i).iCustomerID == iCustomerID) {
                int colorIdx = i % allColors.length;
                return allColors[colorIdx];
            }
        }
        return allColors[0];
    }

    public static void DismissMaterialProgressDialog(Activity activity, MaterialDialog dialog) {
        if (activity != null && !activity.isFinishing() && dialog != null && dialog.isShowing()) {
            dialog.dismiss();
        }
    }

    public static int getProjectProgressBackgroundRes(int iCompleted, int iTotal) {
        int backgroundResId;
        if (iCompleted == 0 && iTotal > 0) {
            backgroundResId = R.drawable.bg_round_project_not_started;
        } else if (iCompleted == iTotal) {
            backgroundResId = R.drawable.bg_round_project_completed_all;
        } else {
            backgroundResId = R.drawable.bg_round_project_in_progress;
        }
        return backgroundResId;
    }

    public static void showToast(Context context, String s) {
        Toast.makeText(context, s, Toast.LENGTH_LONG).show();
    }

    public static void showConfirmDialog(
            Context context, String title, String message,
            String positiveText, String negativeText,
            MaterialDialog.SingleButtonCallback positiveCallback, MaterialDialog.SingleButtonCallback negativeCallback) {
        MaterialDialog.Builder content = new MaterialDialog.Builder(context)
                .title(title)
                .content(message);
        if (!StringUtils.isEmpty(positiveText)) {
            content.positiveText(positiveText);
            content.onPositive(positiveCallback);
        }
        if (!StringUtils.isEmpty(negativeText)) {
            content.negativeText(negativeText);
            content.onNegative(negativeCallback);
        }
        content.show();
    }

    public static void showConfirmDialog(
            Context context, @StringRes int title, @StringRes int message,
            @StringRes int positiveText, @StringRes int negativeText,
            MaterialDialog.SingleButtonCallback positiveCallback, MaterialDialog.SingleButtonCallback negativeCallback) {
        MaterialDialog.Builder content = new MaterialDialog.Builder(context)
                .title(title)
                .content(message);
        if (positiveText != 0) {
            content.positiveText(positiveText);
            content.onPositive(positiveCallback);
        }
        if (negativeText != 0) {
            content.negativeText(negativeText);
            content.onNegative(negativeCallback);
        }
        content.show();
	}

    public static void showOptionsDialog(
        Context context, String title, String[] options, MaterialDialog.ListCallbackSingleChoice callback
    ) {
        new MaterialDialog.Builder(context)
                .title(title)
                .items(options)
                .itemsCallbackSingleChoice(-1, callback)
                .negativeText(R.string.md_cancel_label)
                .onNegative((dialog, which) -> dialog.dismiss())
                .show();
    }

    public static void setActionBar(Activity activity, @StringRes int titleRes, boolean isEnabled) {
        try {
            ActionBar oBar = activity.getActionBar();
            oBar.show();
            oBar.setTitle(titleRes);
            oBar.setDisplayHomeAsUpEnabled(isEnabled);
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }
    }

    @SuppressLint({"RestrictedApi", "UnsafeOptInUsageError"})
    public static CameraSelector getWideAngleCameraSelector(ProcessCameraProvider provider) {
        String wideCameraId = null;
        try {
            for (CameraInfo info : provider.getAvailableCameraInfos()) {
                String cameraId = Camera2CameraInfo.from(info).getCameraId();
                CameraCharacteristics characteristics =
                        Camera2CameraInfo.from(info).getCameraCharacteristicsMap().get(cameraId);
                if (characteristics != null
                        && characteristics.get(CameraCharacteristics.LENS_FACING) == CameraSelector.LENS_FACING_BACK) {
                    ZoomState zoomState = info.getZoomState().getValue();
                    if (zoomState != null && zoomState.getMinZoomRatio() < 1.0F && zoomState.getMinZoomRatio() > 0) {
                        wideCameraId = cameraId;
                        break;
                    }
                }
            }

            if (!StringUtils.isEmpty(wideCameraId)) {
                String finalWideCameraId = wideCameraId;
                return new CameraSelector.Builder().addCameraFilter(cameraInfo -> {
                    List<CameraInfo> cameraFiltered = new ArrayList<>();
                    for (CameraInfo c : cameraInfo) {
                        if (finalWideCameraId.equals(Camera2CameraInfo.from(c).getCameraId()))
                            cameraFiltered.add(c);
                    }
                    return cameraFiltered;
                }).build();
            }
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }

        return null;
    }

    public static void showLocationPermissionDeniedAlert(Context context, String message) {
        ShowAlert(
            context,
            "Location Permission Denied",
            message,
            R.string.action_settings,
            R.string.action_cancel,
            () -> {
                Intent intent = new Intent(ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                context.startActivity(intent);
            }
        );
    }  
}