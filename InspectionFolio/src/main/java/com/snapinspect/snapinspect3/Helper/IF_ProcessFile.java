package com.snapinspect.snapinspect3.Helper;

import android.content.Context;

import android.system.OsConstants;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.SI_DB.db_AssetView;
import com.snapinspect.snapinspect3.database.entities.File;
import org.json.JSONArray;
import org.json.JSONObject;
import org.xmlpull.v1.XmlPullParser;
import org.xmlpull.v1.XmlPullParserException;
import org.xmlpull.v1.XmlPullParserFactory;

import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.snapinspect.snapinspect3.activity.SyncService;

/**
 * Created by <PERSON><PERSON><PERSON> on 15/03/14.
 */
public class IF_ProcessFile {
    private final Context oContext;

    public IF_ProcessFile(Context _context) {
        oContext = _context;
    }

    public boolean ProcessDownloadFile(String sXML, SyncService oSyncService) throws XmlPullParserException, IOException {
        try {
            XmlPullParserFactory oFactory = XmlPullParserFactory.newInstance();
            oFactory.setNamespaceAware(true);
            XmlPullParser oXML = oFactory.newPullParser();

            oXML.setInput(new StringReader(sXML));
            int oEvent = oXML.getEventType();
            boolean bProcessConfig = true;
            boolean bProcessAsset = false;
            boolean bProcessSchedule = false;
            boolean bProcessLayout = false;
            boolean bProcessInsType = false;
            boolean bProcessQP = false;
            boolean bProcessCheckList = false;
            boolean bProcessContact = false;
            boolean bProcessAssetLayout = false;
            boolean bProcessAlert = false;
            boolean bProcessUser = false;
            ai_Assets oAsset = null;
            ai_Schedule oSchedule = null;
            ai_Layout oLayout = null;
            ai_InsType oInsType = null;
            ai_QuickPhrase oQP = null;
            ai_CheckList oCheckList = null;
            ai_Contact oContact = null;
            ai_AssetLayout oAssetLayout = null;
            ai_InsAlert oInsAlert = null;
            ai_User oUser = null;
            ai_User.deleteAll(ai_User.class);
            ai_AssetAttribute oAssetAttribute = null;
            JSONArray arrAssetAttribute = new JSONArray();
            CommonHelper.trackEvent(oContext, "Android Sync Start Processing", null);
            //ai_Schedule.deleteAll(ai_Schedule.class);
            //ai_InsType.deleteAll(ai_InsType.class);

            //ArrayList<String> lsDeletedLayout = new ArrayList<String>();
            //boolean bQPDeleted = false;
            String sValue = "";
            int iAssetCount = 0;
            int iAssetLayoutCount = 0;
            int iContactCount = 0;
            boolean bRecoverData = false;
            while (oEvent != XmlPullParser.END_DOCUMENT) {
                String sTagName = oXML.getName();
                switch (oEvent) {
                    case XmlPullParser.START_TAG:
                        if (bProcessConfig) {
                            if (sTagName.equalsIgnoreCase("Config")) {
                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {
                                    CommonHelper.SavePreference(oContext, "iFileCustomerID", oAttribute.get("CustomerID"));
                                    CommonHelper.SavePreference(oContext, "iCompanyID", oAttribute.get("CompanyID"));
                                    CommonHelper.SavePreference(oContext, "iCountryID", oAttribute.get("iCountryID"));
                                    CommonHelper.SavePreference(oContext, "iIndustryID", oAttribute.get("iIndustryID"));
                                    CommonHelper.SavePreference(oContext, "sIndustry", oAttribute.containsKey("sIndustry") ? oAttribute.get("sIndustry") : "");
                                    CommonConfig.bNotification_Save(oContext, oAttribute.containsKey("sNotification") ? oAttribute.get("sNotification") : "");
                                    //   CommonHelper.SavePreference(oContext, "bNotification", oAttribute.containsKey("sNotification") ? oAttribute.get("sNotification") : "" );
                                    CommonHelper.SavePreference(oContext, Constants.Keys.bProject, oAttribute.get("bProject"));
                                    CommonHelper.SavePreference(oContext, Constants.Keys.bUseLayoutV2, oAttribute.get("bLayout_V2"));
                                    if (oAttribute.containsKey("CompanyReview")) {
                                        CommonHelper.SavePreference(oContext, "bCompanyReview", oAttribute.get("CompanyReview"));
                                    }
                                    if (oAttribute.containsKey("CompanyGroup")) {
                                        CommonHelper.SavePreference(oContext, "bCompanyGroup", oAttribute.get("CompanyGroup"));
                                    }
                                }
                            }
                        } else if (bProcessUser) {
                            if (sTagName.equalsIgnoreCase("SU")) {
                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {
                                    int iCustomerID = Integer.parseInt(oAttribute.get("I"));
                                    List<ai_User> lsUser = CommonDB.findUsersByCustomerId(iCustomerID);
                                    if (lsUser != null && lsUser.size() == 1) {
                                        oUser = lsUser.get(0);
                                    } else {
                                        oUser = new ai_User();
                                        oUser.iCustomerID = iCustomerID;

                                    }
                                }
                            }
                        } else if (bProcessAsset) {

                            if (sTagName.equalsIgnoreCase("OP")) {
                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {
                                    iAssetCount++;
                                    if (iAssetCount % 300 == 0 && iAssetCount > 290) {
                                        oSyncService.PublishProgress(SyncService.SYNC_PROGRESS, "Processing", "Processed " + iAssetCount + " assets");
                                    }
                                    int iSAssetID = Integer.parseInt(oAttribute.get("P"));
                                    int iSPAssetID = oAttribute.containsKey("PP") ? Integer.parseInt(oAttribute.get("PP")) : 0;

                                    boolean bDeleted = Boolean.parseBoolean(oAttribute.get("N"));
                                    if (bDeleted) {
                                        oAsset = null;
                                        CommonDB.deleteAssetByAssetId(iSAssetID);
                                        //ai_Contact.executeQuery("UPDATE AICONTACT SET B_DELETED = 1 WHERE I_S_ASSET_ID = ?", "" + iSAssetID);
                                        ai_Contact.executeQuery("DELETE FROM AICONTACT WHERE I_S_ASSET_ID=?", "" + iSAssetID);
                                        ai_AssetLayout.executeQuery("DELETE FROM AIASSET_LAYOUT WHERE I_S_ASSET_ID = ?", "" + iSAssetID);
                                        ai_InsAlert.executeQuery("DELETE FROM AIINS_ALERT WHERE I_S_ASSET_ID=?", "" + iSAssetID);

                                    } else {
                                        List<ai_Assets> lsAsset = CommonDB.findAssetsByAssetId(iSAssetID);
                                        if (lsAsset != null && lsAsset.size() == 1) {
                                            oAsset = lsAsset.get(0);
                                        } else {
                                            oAsset = new ai_Assets();
                                            oAsset.bDeleted = false;


                                        }
                                        int iCustomerID = Integer.parseInt(oAttribute.get("M"));
                                        int iPLVerID = Integer.parseInt(oAttribute.get("V"));
                                        int iGroupID = 0;
                                        try {
                                            iGroupID = Integer.parseInt(oAttribute.get("GD"));
                                        } catch (Exception eeee) {

                                        }
                                        try {
                                            oAsset.bPush = Integer.parseInt(oAttribute.get("BA")) == 1;
                                        } catch (Exception eeee) {
                                            oAsset.bPush = false;
                                        }
                                        oAsset.iGroupID = iGroupID;
                                        oAsset.iSAssetID = iSAssetID;
                                        oAsset.iSPAssetID = iSPAssetID;
                                        oAsset.iCustomerID = iCustomerID;
                                        oAsset.bDeleted = bDeleted;
                                        oAsset.iPLVerID = iPLVerID;
                                        //ai_Contact.executeQuery("UPDATE AICONTACT SET B_DELETED = 1 WHERE I_S_ASSET_ID = ?", "" + iSAssetID);
                                        CommonDB.deleteContactsByAssetId(iSAssetID);
                                        CommonDB.deleteAssetLayoutsByAssetId(iSAssetID);
                                        CommonDB.deleteInsAlertsByAssetId(iSAssetID);

                                        try {
                                            int iPTOID = Integer.parseInt(oAttribute.get("PTOID"));
                                            if (iPTOID > 0) {
                                                File roomFile = CommonDB.GetFileByServerID(iPTOID);
                                                ai_File oFile = roomFile != null ? roomFile.toSugarEntity() : null;
                                                if (oFile == null || oFile.getId() == 0) {

                                                    oFile = new ai_File();
                                                    oFile.iSObjectID = iSAssetID;
                                                    oFile.iFileID = iPTOID;
                                                    oFile.sFile = "";
                                                    oFile.sLat = "";
                                                    oFile.sLong = "";
                                                    oFile.sComments = "";
                                                    oFile.bUploaded = true;
                                                    oFile.bDeleted = false;
                                                    oFile.dtDateTime = "";
                                                    oFile.iSize = 0;
                                                    oFile.sCustomOne = "";
                                                    oFile.sCustomTwo = "";
                                                    CommonDB.saveFile(oFile);

                                                }
                                                CommonDB.MarkFileAssetPhoto(oFile.getId(), oFile.iSObjectID);
                                            }
                                        } catch (Exception eeeee) {

                                        }

                                    }
                                }

                            }
                        } else if (bProcessSchedule) {
                            if (sTagName.equalsIgnoreCase("SC")) {
                                oSchedule = new ai_Schedule();
                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {

                                    int iScheduleID = Integer.parseInt(oAttribute.get("I"));
                                    boolean bDeleted = Boolean.parseBoolean(oAttribute.get("M"));
                                    boolean bCompleted = Boolean.parseBoolean(oAttribute.get("N"));
                                    if (bDeleted || bCompleted) {
                                        oSchedule = null;
                                        CommonDB.deleteScheduleByScheduleId(iScheduleID);
                                    } else {
                                        List<ai_Schedule> lsSchedule = CommonDB.findSchedulesByScheduleId(iScheduleID);
                                        if (lsSchedule != null && lsSchedule.size() == 1) {
                                            oSchedule = lsSchedule.get(0);
                                        } else {
                                            oSchedule = new ai_Schedule();
                                        }
                                        int iSAssetID = Integer.parseInt(oAttribute.get("A"));
                                        int iSInsTypeID = Integer.parseInt(oAttribute.get("T"));
                                        String sType = oAttribute.get("S");
                                        String sPTC = oAttribute.get("P");
                                        oSchedule.iSScheduleID = iScheduleID;
                                        oSchedule.iSAssetID = iSAssetID;
                                        oSchedule.iSInsTypeID = iSInsTypeID;
                                        oSchedule.sType = sType;
                                        oSchedule.sPTC = sPTC;
                                        oSchedule.bCompleted = false;
                                        oSchedule.bDeleted = false;
                                        String rule = oAttribute.get("RR"), ex = oAttribute.get("ER");
                                        oSchedule.sRRule = !StringUtils.isEmpty(rule) ? rule.trim() : "";
                                        oSchedule.sEXRule = !StringUtils.isEmpty(ex) ? ex.trim() : "";
                                    }

                                }
                            }
                        } else if (bProcessLayout) {
                            if (sTagName.equalsIgnoreCase("LT")) {
                                oLayout = new ai_Layout();

                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {
                                    int iSLayoutID = Integer.parseInt(oAttribute.get("U"));
                                    int iPSLayoutID = Integer.parseInt(oAttribute.get("O"));
                                    int iMark = 0;
                                    int iOrder = 0;
                                    try {
                                        iMark = Integer.parseInt(oAttribute.get(("V")));
                                        iOrder = Integer.parseInt(oAttribute.get(("S")));
                                    } catch (Exception ee) {

                                    }
                                    String sPTC = oAttribute.get("P");
                                    String sQT = oAttribute.get("Q");
                                    //boolean bAutoAdd = Boolean.parseBoolean(oAttribute.get("AD"));

                                    oLayout.iSLayoutID = iSLayoutID;
                                    oLayout.iSPLayoutID = iPSLayoutID;
                                    oLayout.sPTC = sPTC;
                                    oLayout.sQType = sQT;
                                    // Here we are setting the sFieldThree and sFieldTwo to the order and mark
                                    // sFieldOne is for count of areas
                                    // this is not same as the iOS version does, because "iMark" had taken the place of "sFieldTwo" already
                                    oLayout.sFieldTwo = "" + iMark;
                                    oLayout.sFieldThree= "" + iOrder;
                                    // oLayout.bAutoAdd = bAutoAdd;
                                    oLayout.bDeleted = false;

                                }
                            }

                        } else if (bProcessInsType) {
                            if (sTagName.equalsIgnoreCase("INT")) {
                                oInsType = new ai_InsType();

                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {
                                    int iSInsTypeID = Integer.parseInt(oAttribute.get("I"));
                                    boolean bDeleted = Boolean.parseBoolean(oAttribute.get("B"));
                                    if (bDeleted) {
                                        oInsType = null;
                                        CommonDB.deleteInsTypeByInsTypeId(iSInsTypeID);
                                    } else {
                                        List<ai_InsType> lsInsType = CommonDB.findInsTypesByInsTypeId(iSInsTypeID);
                                        if (lsInsType != null && lsInsType.size() == 1) {
                                            oInsType = lsInsType.get(0);

                                        } else {
                                            oInsType = new ai_InsType();
                                            oInsType.bDeleted = Boolean.parseBoolean(oAttribute.get("B"));
                                        }
                                        String sPTC = oAttribute.get("P");
                                        String sConfig = oAttribute.get("T");


                                        oInsType.iSInsTypeID = iSInsTypeID;
                                        oInsType.sPTC = sPTC;
                                        oInsType.sType = sConfig;
                                        oInsType.bRemLayout = false;
                                    }


                                }
                            }
                        } else if (bProcessQP) {
                            if (sTagName.equalsIgnoreCase("QP")) {
                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {
                                    int iSQuickPhraseID = Integer.parseInt(oAttribute.get("S"));
                                    boolean bDeleted = Boolean.parseBoolean(oAttribute.get("B"));
                                    if (bDeleted) {
                                        oQP = null;
                                        CommonDB.deleteQuickPhraseByPhraseId(iSQuickPhraseID);
                                    } else {
                                        List<ai_QuickPhrase> lsQuickPhrase = CommonDB.findQuickPhrasesByPhraseId(iSQuickPhraseID);

                                        if (lsQuickPhrase != null && lsQuickPhrase.size() == 1) {
                                            oQP = lsQuickPhrase.get(0);
                                        } else {
                                            oQP = new ai_QuickPhrase();
                                            oQP.iQuickPhraseID = iSQuickPhraseID;
                                            oQP.iSLayoutID = 0;
                                        }
                                    }

                                }
                            }
                        } else if (bProcessCheckList) {
                            if (sTagName.equalsIgnoreCase("CLST")) {
                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {
                                    int iCheckListID = Integer.parseInt(oAttribute.get("C"));
                                    boolean bDeleted = Boolean.parseBoolean(oAttribute.get("B"));

                                    if (bDeleted) {
                                        oCheckList = null;
                                        CommonDB.deleteCheckListByCheckListId(iCheckListID);
                                    } else {
                                        List<ai_CheckList> lsCheckList = CommonDB.findCheckListsByCheckListId(iCheckListID);
                                        if (lsCheckList != null && lsCheckList.size() == 1) {
                                            oCheckList = lsCheckList.get(0);
                                        } else {
                                            oCheckList = new ai_CheckList();
                                        }
                                        int iVerID = Integer.parseInt(oAttribute.get("V"));
                                        oCheckList.iLayoutVerID = iVerID;
                                        oCheckList.iSCheckListID = iCheckListID;

                                    }

                                }
                            }
                        } else if (bProcessContact) {
                            if (sTagName.equalsIgnoreCase("CN")) {
                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {
                                    iContactCount++;
                                    if (iContactCount % 300 == 0 && iContactCount > 290) {
                                        oSyncService.PublishProgress(SyncService.SYNC_PROGRESS, "Processing", "Processed " + iContactCount + " Contacts");
                                    }
                                    int iSContactID = Integer.parseInt(oAttribute.get("C"));
                                    int iSAssetID = Integer.parseInt(oAttribute.get("A"));
                                    oContact = new ai_Contact();
                                    oContact.iSContactID = iSContactID;
                                    oContact.iSAssetID = iSAssetID;
                                    oContact.sFieldThree = CommonJson.AddJsonKeyValue(oContact.sFieldThree,
                                            ai_Contact.Keys.kIsPrimary, oAttribute.get(ai_Contact.Keys.kIsPrimary));
                                    oContact.bDeleted = false;
                                }
                            }
                        } else if (bProcessAssetLayout) {
                            if (sTagName.equalsIgnoreCase("AL")) {
                                iAssetLayoutCount++;
                                if (iAssetLayoutCount % 300 == 0 && iAssetLayoutCount > 290) {

                                    oSyncService.PublishProgress(SyncService.SYNC_PROGRESS, "Processing", "Processed " + iAssetLayoutCount + " asset layouts.");
                                }
                                oAssetLayout = new ai_AssetLayout();

                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {
                                    int iSAssetID = Integer.parseInt(oAttribute.get("I"));
                                    int iSLayoutID = Integer.parseInt(oAttribute.get("L"));
                                    String sALID = oAttribute.get("P");
                                    int iSAssetLayoutID = (sALID == null || sALID == "") ? 0 : Integer.parseInt(sALID);
                                    // String sPTC = oAttribute.get("sPTC");
                                    oAssetLayout.iSAssetID = iSAssetID;
                                    oAssetLayout.iSLayoutID = iSLayoutID;
                                    oAssetLayout.iSAssetLayoutID = iSAssetLayoutID;
                                    oAssetLayout.iSort = Integer.parseInt(oAttribute.get("S"));
                                    // List<ai_CheckList> lsCheckList = ai_CheckList.find(ai_CheckList.class, "S_PTC = ?", sPTC);
                                    // if (lsCheckList != null && lsCheckList.size() == 1){
                                    //     oAssetLayout.sFieldThree = "" + lsCheckList.get(0).iLayoutVerID;
                                    // }


                                }
                            }
                        } else if (bProcessAlert) {
                            if (sTagName.equalsIgnoreCase("IA")) {
                                oInsAlert = new ai_InsAlert();

                                Map<String, String> oAttribute = getAttributes(oXML);
                                if (oAttribute != null && oAttribute.size() > 0) {
                                    int iSAssetID = Integer.parseInt(oAttribute.get("M"));
                                    int iSLayoutID = Integer.parseInt(oAttribute.get("L"));
                                    oInsAlert.iSAssetID = iSAssetID;
                                    oInsAlert.iSLayoutID = iSLayoutID;
                                }
                            }
                        } else {
                            if (sTagName.equalsIgnoreCase("Config")) {
                                CommonHelper.trackEvent(oContext, "Android Sync Process Config", null);
                                bProcessConfig = true;
                                bProcessAsset = false;
                                bProcessSchedule = false;
                                bProcessLayout = false;
                                bProcessInsType = false;
                                bProcessQP = false;
                                bProcessCheckList = false;
                                bProcessContact = false;
                                bProcessAssetLayout = false;
                                bProcessAlert = false;
                                bProcessUser = false;
                            } else if (sTagName.equalsIgnoreCase("Properties")) {
                                CommonHelper.trackEvent(oContext, "Android Sync Process Properties", null);
                                bProcessConfig = false;
                                bProcessAsset = true;
                                bProcessSchedule = false;
                                bProcessLayout = false;
                                bProcessInsType = false;
                                bProcessQP = false;
                                bProcessCheckList = false;
                                bProcessContact = false;
                                bProcessAssetLayout = false;
                                bProcessAlert = false;
                                bProcessUser = false;
                            } else if (sTagName.equalsIgnoreCase("Schedules")) {
                                CommonHelper.trackEvent(oContext, "Android Sync Process Schedules", null);
                                bProcessConfig = false;
                                bProcessAsset = false;
                                bProcessSchedule = true;
                                bProcessLayout = false;
                                bProcessInsType = false;
                                bProcessQP = false;
                                bProcessCheckList = false;
                                bProcessContact = false;
                                bProcessAssetLayout = false;
                                bProcessAlert = false;
                                bProcessUser = false;
                            } else if (sTagName.equalsIgnoreCase("Users")) {
                                bProcessUser = true;
                                bProcessConfig = false;
                                bProcessAsset = false;
                                bProcessSchedule = false;
                                bProcessLayout = false;
                                bProcessInsType = false;
                                bProcessQP = false;
                                bProcessCheckList = false;
                                bProcessContact = false;
                                bProcessAssetLayout = false;
                                bProcessAlert = false;
                            } else if (sTagName.equalsIgnoreCase("Layouts")) {
                                CommonHelper.trackEvent(oContext, "Android Sync Process Layouts", null);
                                bProcessConfig = false;
                                bProcessAsset = false;
                                bProcessSchedule = false;
                                bProcessLayout = true;
                                bProcessInsType = false;
                                bProcessQP = false;
                                bProcessCheckList = false;
                                bProcessContact = false;
                                bProcessAssetLayout = false;
                                bProcessAlert = false;
                                bProcessUser = false;
                            } else if (sTagName.equalsIgnoreCase("InsTypes")) {
                                CommonHelper.trackEvent(oContext, "Android Sync Process InsTypes", null);
                                bProcessConfig = false;
                                bProcessAsset = false;
                                bProcessSchedule = false;
                                bProcessLayout = false;
                                bProcessInsType = true;
                                bProcessQP = false;
                                bProcessCheckList = false;
                                bProcessContact = false;
                                bProcessAssetLayout = false;
                                bProcessAlert = false;
                                bProcessUser = false;
                            } else if (sTagName.equalsIgnoreCase("QuickPhrases")) {
                                CommonHelper.trackEvent(oContext, "Android Sync Process QuickPhrases", null);
                                bProcessConfig = false;
                                bProcessAsset = false;
                                bProcessSchedule = false;
                                bProcessLayout = false;
                                bProcessInsType = false;
                                bProcessQP = true;
                                bProcessCheckList = false;
                                bProcessContact = false;
                                bProcessAssetLayout = false;
                                bProcessAlert = false;
                                bProcessUser = false;
                            } else if (sTagName.equalsIgnoreCase("CheckList")) {
                                CommonHelper.trackEvent(oContext, "Android Sync Process CheckList", null);
                                bProcessConfig = false;
                                bProcessAsset = false;
                                bProcessSchedule = false;
                                bProcessLayout = false;
                                bProcessInsType = false;
                                bProcessQP = false;
                                bProcessCheckList = true;
                                bProcessContact = false;
                                bProcessAssetLayout = false;
                                bProcessAlert = false;
                                bProcessUser = false;
                            } else if (sTagName.equalsIgnoreCase("Contacts")) {
                                CommonHelper.trackEvent(oContext, "Android Sync Process Contacts", null);
                                bProcessConfig = false;
                                bProcessAsset = false;
                                bProcessSchedule = false;
                                bProcessLayout = false;
                                bProcessInsType = false;
                                bProcessQP = false;
                                bProcessCheckList = false;
                                bProcessContact = true;
                                bProcessAssetLayout = false;
                                bProcessAlert = false;
                                bProcessUser = false;
                            } else if (sTagName.equalsIgnoreCase("AssetLayouts")) {
                                CommonHelper.trackEvent(oContext, "Android Sync Process AssetLayouts", null);
                                bProcessConfig = false;
                                bProcessAsset = false;
                                bProcessSchedule = false;
                                bProcessLayout = false;
                                bProcessInsType = false;
                                bProcessQP = false;
                                bProcessCheckList = false;
                                bProcessContact = false;
                                bProcessAssetLayout = true;
                                bProcessAlert = false;
                                bProcessUser = false;
                            } else if (sTagName.equalsIgnoreCase("AssetAttributes")) {
                                bProcessConfig = false;
                                bProcessAsset = false;
                                bProcessSchedule = false;
                                bProcessLayout = false;
                                bProcessInsType = false;
                                bProcessQP = false;
                                bProcessCheckList = false;
                                bProcessContact = false;
                                bProcessAssetLayout = false;
                                bProcessAlert = false;
                                bProcessUser = false;
                            } else if (sTagName.equalsIgnoreCase("InsAlerts")) {
                                CommonHelper.trackEvent(oContext, "Android Sync Process InsAlerts", null);
                                bProcessConfig = false;
                                bProcessAsset = false;
                                bProcessSchedule = false;
                                bProcessLayout = false;
                                bProcessInsType = false;
                                bProcessQP = false;
                                bProcessCheckList = false;
                                bProcessContact = false;
                                bProcessAssetLayout = false;
                                bProcessAlert = true;
                                bProcessUser = false;
                            }

                        }
                        break;
                    case XmlPullParser.TEXT:
                        sValue = oXML.getText() == null ? "" : oXML.getText();
                        break;
                    case XmlPullParser.END_TAG:
                        if (bProcessConfig) {

                            if (sTagName.equalsIgnoreCase("FirstName")) {
                                CommonHelper.SavePreference(oContext, "FirstName", sValue);
                            } else if (sTagName.equalsIgnoreCase("LastName")) {
                                CommonHelper.SavePreference(oContext, "LastName", sValue);
                            } else if (sTagName.equalsIgnoreCase("CompanyName")) {
                                CommonHelper.SavePreference(oContext, "CompanyName", sValue);
                            } else if (sTagName.equalsIgnoreCase("iQPVerID")) {
                                CommonHelper.SavePreference(oContext, "iQPVerID", sValue);
                            } else if (sTagName.equalsIgnoreCase("iLayoutVerID")) {
                                CommonHelper.SavePreference(oContext, "iLayoutVerID", sValue);
                            } else if (sTagName.equalsIgnoreCase("Config")) {
                                bProcessConfig = false;
                            } else if (sTagName.equalsIgnoreCase("sSyncDate")) {
                                CommonHelper.SavePreference(oContext, "sSyncDate", sValue);
                            } else if (sTagName.equalsIgnoreCase("sCommand")) {
                                if (sValue != null) {
                                    if (sValue.contains("SUBMIT")) {
                                        bRecoverData = true;
                                    }
                                }
                            } else if (sTagName.equalsIgnoreCase("Category")) {
                                if (sValue != null && !sValue.isEmpty()) {
                                    try {
                                        JSONArray arrCategory = new JSONArray(sValue);
                                        for (int i = 0; i < arrCategory.length(); i++) {
                                            JSONObject oObject = arrCategory.getJSONObject(i);
                                            ai_NoticeCategory noticeCategory = new ai_NoticeCategory(oObject);
                                            List<ai_NoticeCategory> lsCategory = CommonDB.findNoticeCategoriesByCategoryId(noticeCategory.iSNoticeCategoryID);

                                            // Update notice category id if it already exists
                                            if (lsCategory != null && lsCategory.size() == 1) {
                                                noticeCategory.setId(lsCategory.get(0).getId());
                                            }
                                            CommonDB.saveNoticeCategory(noticeCategory);
                                        }
                                    } catch (Exception ex) {

                                    }
                                }
                            } else if (sTagName.equalsIgnoreCase("PerDetail")) {
                                CommonHelper.SavePreference(oContext, "PerDetail", sValue);
                            } else if (sTagName.equalsIgnoreCase("jsonStatus")) {
                                CommonHelper.SavePreference(oContext, "jsonStatus", sValue);
                            } else if (sTagName.equalsIgnoreCase("Email")) {
                                try {
                                    CommonHelper.SavePreference(oContext, "sFileEmail", sValue);
                                } catch (Exception eec1) {
                                }
                            } else if (sTagName.equalsIgnoreCase("sPlanCode")) {
                                try {
                                    CommonHelper.SavePreference(oContext, "sPlanCode", sValue);
                                } catch (Exception eec1) {
                                }
                            } else if (sTagName.equalsIgnoreCase("sPlanName")) {
                                try {
                                    CommonHelper.SavePreference(oContext, "sPlanName", sValue);
                                } catch (Exception eec1) {
                                }
                            } else if (sTagName.equalsIgnoreCase("sPlanDetails")) {
                                try {
                                    CommonHelper.SavePreference(oContext, "sPlanDetails", sValue);
                                } catch (Exception eec1) {
                                }
                            } else if (sTagName.equalsIgnoreCase("sRole")) {
                                try {
                                    CommonHelper.SavePreference(oContext, "sRole", sValue);
                                } catch (Exception eec1) {
                                }
                            } else if (sTagName.equalsIgnoreCase(Constants.Settings.bMultiFamily)) {
                                try {
                                    CommonHelper.SavePreference(oContext, Constants.Settings.bMultiFamily, sValue);
                                } catch (Exception eec1) {
                                }
                            } else if (sTagName.equalsIgnoreCase(Constants.Settings.bRoom)) {
                                try {
                                    CommonHelper.SavePreference(oContext, Constants.Settings.bRoom, sValue);
                                } catch (Exception eec1) {
                                }
                            } else if (sTagName.equalsIgnoreCase(Constants.Keys.sCustomRole)) {
                                try {
                                    CommonHelper.SavePreference(oContext, Constants.Keys.sCustomRole, sValue);
                                } catch (Exception eec1) {
                                }
                            } else if (sTagName.equalsIgnoreCase(Constants.Keys.sAssetView)) {
                                try {
                                    db_AssetView.saveAssetView(sValue);
                                } catch (Exception exception) {
                                    //
                                }
                            } else if (sTagName.equalsIgnoreCase(Constants.Keys.sCompanyCustom1)) {
                                try {
                                    CommonHelper.SavePreference(oContext, Constants.Keys.sCompanyCustom1, sValue);
                                } catch (Exception exception) {
                                    //
                                }
                            } else if (sTagName.equalsIgnoreCase(Constants.Keys.customInfo1st)) {
                                try {
                                    CommonHelper.SavePreference(oContext, Constants.Keys.sAssetAttributes, sValue);
                                } catch (Exception exception) {
                                    //
                                }
                            } else if (sTagName.equalsIgnoreCase(Constants.Keys.customInfo2nd)) {
                                try {
                                    CommonHelper.SavePreference(oContext, Constants.Keys.sAssetAttributes2, sValue);
                                } catch (Exception exception) {
                                    //
                                }
                            } else if (sTagName.equalsIgnoreCase(Constants.Keys.kContactType)) {
                                // Save "sContactType" to preference
                                CommonHelper.SavePreference(oContext, Constants.Keys.kContactType, sValue);
                            }
                        } else if (bProcessUser) {
                            if (oUser != null) {
                                if (sTagName.equalsIgnoreCase("N")) {
                                    oUser.sName = sValue;
                                } else if (sTagName.equalsIgnoreCase("E")) {
                                    oUser.sEmail = sValue;
                                } else if (sTagName.equalsIgnoreCase("SU")) {
                                    CommonDB.saveUser(oUser);

                                } else if (sTagName.equalsIgnoreCase("Users")) {
                                    bProcessUser = false;
                                }
                            }
                        } else if (bProcessAsset) {
                            if (oAsset != null) {
                                if (sTagName.equalsIgnoreCase("A")) {
                                    oAsset.sAddressOne = sValue;
                                } else if (sTagName.equalsIgnoreCase("B")) {
                                    oAsset.sAddressTwo = sValue;
                                    oAsset.sFilter = CommonHelper.GetFilter(oAsset.sAddressOne, sValue);
                                } else if (sTagName.equalsIgnoreCase("D")) {
                                    oAsset.dtInsDue = sValue;
                                } else if (sTagName.equalsIgnoreCase("S")) {
                                    oAsset.sAlarm = sValue;
                                } else if (sTagName.equalsIgnoreCase("K")) {
                                    oAsset.sKey = sValue;
                                } else if (sTagName.equalsIgnoreCase("R")) {
                                    oAsset.sFieldThree = sValue;
                                } else if (sTagName.equalsIgnoreCase("CUS1")) {
                                    oAsset.sFieldOne = sValue;
                                    // Copy "sAstView" to "sFieldTwo"
                                    String sAstView = CommonJson.GetJsonKeyValue(Constants.Keys.sAstView, sValue);
                                    if (sAstView != null && sAstView.length() > 0) {
                                        // Convert 1,3,4 to [1],[3],[4]
                                        String[] lsAstViewId = sAstView.split(",");
                                        for (int i = 0; i < lsAstViewId.length; i++) {
                                            lsAstViewId[i] = "[" + lsAstViewId[i] + "]";
                                        }
                                        oAsset.sFieldTwo = String.join(",", lsAstViewId);
                                    } else {
                                        oAsset.sFieldTwo = "";
                                    }
                                } else if (sTagName.equalsIgnoreCase("OP")) {
                                    if (oAsset.bDeleted && oAsset.iSAssetID > 0) {
                                        CommonDB.deleteAssetByAssetId(oAsset.iSAssetID);
                                        CommonDB.deleteContactsByAssetId(oAsset.iSAssetID);
                                        CommonDB.deleteAssetLayoutsByAssetId(oAsset.iSAssetID);
                                        CommonDB.deleteInsAlertsByAssetId(oAsset.iSAssetID);
                                    } else {
                                        CommonDB.saveAsset(oAsset);
                                    }

                                } else if (sTagName.equalsIgnoreCase("Properties")) {
                                    bProcessAsset = false;
                                }
                            }
                        } else if (bProcessSchedule) {
                            if (sTagName.equalsIgnoreCase("Schedules")) {
                                bProcessSchedule = false;
                            } else if (oSchedule == null) {

                            } else if (sTagName.equalsIgnoreCase("X")) {
                                oSchedule.sInsTitle = sValue;
                            } else if (sTagName.equalsIgnoreCase("B")) {
                                oSchedule.sAddressOne = sValue;
                            } else if (sTagName.equalsIgnoreCase("C")) {
                                oSchedule.sAddressTwo = sValue;
                            } else if (sTagName.equalsIgnoreCase("D")) {
                                oSchedule.dtDateTime = sValue;
                                oSchedule.iUnixTime = CommonHelper.GetUnixTime(sValue);

                            } else if (sTagName.equalsIgnoreCase("AD")) {
                                oSchedule.sCustomOne = CommonJson.AddJsonKeyValue(oSchedule.sCustomOne, "sAddInfo", sValue);
                            } else if (sTagName.equalsIgnoreCase("RE")) {
                                oSchedule.sCustomOne = CommonJson.AddJsonKeyValue(oSchedule.sCustomOne, "REmail", sValue);
                            } else if (sTagName.equalsIgnoreCase("RN")) {
                                oSchedule.sCustomOne = CommonJson.AddJsonKeyValue(oSchedule.sCustomOne, "RName", sValue);
                            } else if (sTagName.equalsIgnoreCase("RR")) {
                                oSchedule.sRRule = !StringUtils.isEmpty(sValue) ? sValue.trim() : "";
                            } else if (sTagName.equalsIgnoreCase("ER")) {
                                oSchedule.sEXRule = !StringUtils.isEmpty(sValue) ? sValue.trim() : "";
                            } else if (sTagName.equalsIgnoreCase("SC")) {
                                CommonDB.saveSchedule(oSchedule);
                            }
                        } else if (bProcessLayout) {
                            if (sTagName.equalsIgnoreCase("T")) {
                                oLayout.sName = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("A")) {
                                oLayout.sFVOneConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("B")) {
                                oLayout.sFVTwoConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("C")) {
                                oLayout.sFVThreeConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("D")) {
                                oLayout.sFVFourConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("E")) {
                                oLayout.sFVFiveConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("F")) {
                                oLayout.sFVSixConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("G")) {
                                oLayout.sSVOneConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("H")) {
                                oLayout.sSVTwoConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("I")) {
                                oLayout.sSVThreeConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("J")) {
                                oLayout.sSVFourConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("K")) {
                                oLayout.sSVFiveConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("L")) {
                                oLayout.sSVSixConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("M")) {
                                oLayout.sFConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("N")) {
                                oLayout.sSConfig = sValue.trim();
                            } else if (sTagName.equalsIgnoreCase("LT")) {
                                CommonDB.saveLayout(oLayout);

                            } else if (sTagName.equalsIgnoreCase("Layouts")) {
                                bProcessLayout = false;
                            }
                        } else if (bProcessInsType) {
                            if (sTagName.equalsIgnoreCase("InsTypes")) {
                                bProcessInsType = false;
                            } else if (oInsType == null) {

                            } else if (sTagName.equalsIgnoreCase("E")) {
                                oInsType.sInsTitle = sValue;
                            } else if (sTagName.equalsIgnoreCase("CUS1")) {
                                oInsType.sFieldOne = sValue;

                            } else if (sTagName.equalsIgnoreCase("INT")) {
                                CommonDB.saveInsType(oInsType);
                            }

                        } else if (bProcessQP) {
                            if (sTagName.equalsIgnoreCase("QuickPhrases")) {
                                bProcessQP = false;
                            } else if (oQP == null) {

                            } else if (sTagName.equalsIgnoreCase("Q")) {
                                oQP.sComments = sValue;
                            } else if (sTagName.equalsIgnoreCase("QP")) {
                                CommonDB.saveQuickPhrase(oQP);
                            }
                        } else if (bProcessCheckList) {
                            if (sTagName.equalsIgnoreCase("CheckList")) {
                                bProcessCheckList = false;
                            } else if (oCheckList == null) {

                            } else if (sTagName.equalsIgnoreCase("P")) {
                                oCheckList.sPTC = sValue;
                            } else if (sTagName.equalsIgnoreCase("L")) {
                                oCheckList.sTitle = sValue;
                            } else if (sTagName.equalsIgnoreCase("CLST")) {
                                if (oCheckList != null) {
                                    ai_Layout.deleteAll(ai_Layout.class, "s_PTC = ?", oCheckList.sPTC);
                                    CommonDB.saveCheckList(oCheckList);
                                }

                            }
                        } else if (bProcessContact) {
                            if (sTagName.equalsIgnoreCase("F")) {
                                oContact.sFirstName = sValue;
                            } else if (sTagName.equalsIgnoreCase("L")) {
                                oContact.sLastName = sValue;
                            } else if (sTagName.equalsIgnoreCase("P")) {
                                oContact.sPhone = sValue;
                            } else if (sTagName.equalsIgnoreCase("M")) {
                                oContact.sMobile = sValue;
                            } else if (sTagName.equalsIgnoreCase("E")) {
                                oContact.sEmail = sValue;
                            } else if (sTagName.equalsIgnoreCase("T")) {
                                oContact.sTag = sValue;
                            } else if (sTagName.equalsIgnoreCase("Cus")) {
                                oContact.sFieldOne = sValue;
                            } else if (sTagName.equalsIgnoreCase("Cus2")) {
                                oContact.sFieldTwo = sValue;
                            } else if (sTagName.equalsIgnoreCase("dtF")) {
                                oContact.sFieldThree = CommonJson.AddJsonKeyValue(oContact.sFieldThree, ai_Contact.Keys.kDateFrom, sValue);
                            } else if (sTagName.equalsIgnoreCase("dtT")) {
                                oContact.sFieldThree = CommonJson.AddJsonKeyValue(oContact.sFieldThree, ai_Contact.Keys.kDateTo, sValue);
                            } else if (sTagName.equalsIgnoreCase("CN")) {
                                CommonDB.saveContact(oContact);

                            } else if (sTagName.equalsIgnoreCase("Contacts")) {
                                bProcessContact = false;
                            }

                        } else if (bProcessAssetLayout) {
                            if (sTagName.equalsIgnoreCase("M")) {
                                oAssetLayout.sMoreItems = sValue;
                            } else if (sTagName.equalsIgnoreCase("N")) {
                                oAssetLayout.sName = sValue;
                            } else if (sTagName.equalsIgnoreCase("S")) {
                                oAssetLayout.iSort = Integer.parseInt(sValue);
                            } else if (sTagName.equalsIgnoreCase("A")) {
                                oAssetLayout.sChildID = sValue;
                            } else if (sTagName.equalsIgnoreCase("AL")) {
                                CommonDB.saveAssetLayout(oAssetLayout);

                            } else if (sTagName.equalsIgnoreCase("AssetLayouts")) {
                                bProcessAssetLayout = false;
                            }
                        } else if (bProcessAlert) {
                            if (sTagName.equalsIgnoreCase("P")) {
                                oInsAlert.sPTC = sValue;
                            } else if (sTagName.equalsIgnoreCase("A")) {
                                oInsAlert.sValueOne = sValue;
                            } else if (sTagName.equalsIgnoreCase("B")) {
                                oInsAlert.sValueTwo = sValue;
                            } else if (sTagName.equalsIgnoreCase("C")) {
                                oInsAlert.sValueThree = sValue;
                            } else if (sTagName.equalsIgnoreCase("D")) {
                                oInsAlert.sValueFour = sValue;
                            } else if (sTagName.equalsIgnoreCase("E")) {
                                oInsAlert.sValueFive = sValue;
                            } else if (sTagName.equalsIgnoreCase("F")) {
                                oInsAlert.sValueSix = sValue;
                            } else if (sTagName.equalsIgnoreCase("IA")) {
                                CommonDB.saveInsAlert(oInsAlert);
                            } else if (sTagName.equalsIgnoreCase("InsAlerts")) {
                                bProcessAlert = false;
                            }
                        }
                        break;
                    default:
                        break;
                }
                oEvent = oXML.next();
            }
            if (bRecoverData) {
                CommonHelper.SavePreference(oContext, "bSubmit", "true");
            } else {
                CommonHelper.SavePreference(oContext, "bSubmit", "false");
            }

            return true;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_ProcessFile.ProcessDownloadFile", ex, oContext);
            // ai_BugHandler.ai_Handler_Exception("Per", "IF_ProcessFile.ProcessDownloadFile", ex);
            return false;
        }
    }

    private Map<String, String> getAttributes(XmlPullParser parser) {
        try {
            Map<String, String> attrs = null;
            int acount = parser.getAttributeCount();
            if (acount != -1) {
                // Log.d(MY_DEBUG_TAG,"Attributes for ["+parser.getName()+"]");
                attrs = new HashMap<String, String>(acount);
                for (int x = 0; x < acount; x++) {
                    // Log.d(MY_DEBUG_TAG,"\t["+parser.getAttributeName(x)+"]=" +
                    //       "["+parser.getAttributeValue(x)+"]");
                    attrs.put(parser.getAttributeName(x), parser.getAttributeValue(x));
                }
            }
            return attrs;
        } catch (Exception ex) {
            //ai_BugHandler.ai_Handler_Exception("Source", "SyncProcessFile.getAttributes", ex);
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_ProcessFile.getAttributes", ex, oContext);
            return null;
        }
    }
}
