package com.snapinspect.snapinspect3.Helper;

import android.content.Context;

import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_File;
import com.snapinspect.snapinspect3.R;
import org.apache.http.Header;
import org.json.JSONObject;

import java.util.HashMap;

public final class CommonRequest {

    public interface RequestParamsCreator {
        RequestParams create();
    }
    public interface Completion {
        void didRequestCompletion(JSONObject response, Error error);
    }

    public static void getFileDownloadableURL(Context ctx, final int iFileID, Completion completion) {
        requestURL(ctx, "/IOAPI/GetAssetFile", () -> {
            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(ctx, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(ctx, "sToken"));
            oParams.add("iFileID", "" + iFileID);
            return oParams;
        }, completion);
    }

    public static void requestURL(Context ctx, String sEndpoint, RequestParamsCreator paramsCreator, Completion completion) {
        if (!NetworkUtils.isNetworkAvailable(ctx)) {
            completion.didRequestCompletion(null, new Error(ctx.getString(R.string.failed_connection)));
            return;
        }

        IF_RestClient.post(sEndpoint, paramsCreator.create(), new JsonHttpResponseHandler() {
            @Override
            public void onSuccess(int statusCode, Header[] headers, JSONObject response) {
                try {
                    if (statusCode == 200 && response.getBoolean(ctx.getString(R.string.response_success))) {
                        completion.didRequestCompletion(response, null);
                    } else {
                        completion.didRequestCompletion(null, new Error(response.getString("message")));
                    }
                    return;
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
                completion.didRequestCompletion(null, new Error(ctx.getString(R.string.request_failed)));
            }

            @Override
            public void onFailure(int statusCode, Header[] headers, Throwable e, JSONObject errorResponse) {
                completion.didRequestCompletion(null, new Error(ctx.getString(R.string.request_failed)));
            }
        });
    }

    public static void uploadFile(Context context, ai_File oFile, Completion completion) {
        if (!CommonHelper.bFileExist(oFile.sFile)) {
            completion.didRequestCompletion(null, new Error("File was not found"));
            return;
        }
        HashMap<String, String> lsParams = new HashMap<>();
        lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(context, "iCustomerID"));
        lsParams.put("sToken", CommonHelper.GetPreferenceString(context, "sToken"));
        lsParams.put("iPropertyID", "" + oFile.iSObjectID);
        lsParams.put("dtDateTaken", oFile.dtDateTime);
        lsParams.put("iSize", "" + oFile.iSize);
        lsParams.put("sClientFileName", oFile.sFile.substring(oFile.sFile.lastIndexOf("/") + 1));

        JSONObject oJson = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFile", lsParams);
        try {
            if (oJson != null && oJson.getBoolean(context.getString(R.string.response_success))) {
                if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200) {
                    if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200) {
                        completion.didRequestCompletion(null, new Error("Failed to upload file"));
                        return;
                    }
                }
            } else {
                oJson = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFile", lsParams);
                if (oJson != null && oJson.getBoolean(context.getString(R.string.response_success))) {
                    if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200) {
                        if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200) {
                            completion.didRequestCompletion(null, new Error("Failed to upload file"));
                            return;
                        }
                    }
                } else {
                    completion.didRequestCompletion(null, new Error("Failed to upload file"));
                    return;
                }
            }
            int iSFileID = oJson.getJSONObject("oFile").getInt("iFileID");
            if (iSFileID > 0) {
                lsParams.clear();
                lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(context, "iCustomerID"));
                lsParams.put("sToken", CommonHelper.GetPreferenceString(context, "sToken"));
                lsParams.put("iSFileID", "" + iSFileID);
                JSONObject oReturn = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFileSuccess", lsParams);
                if (oReturn != null && oReturn.getBoolean(context.getString(R.string.response_success))) {
                    oFile.iFileID = iSFileID;
                    oFile.bUploaded = true;
                    CommonDB.saveFile(oFile);
                    completion.didRequestCompletion(oReturn, null);
                } else {
                    oReturn = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFileSuccess", lsParams);
                    if (oReturn != null && oReturn.getBoolean(context.getString(R.string.response_success))) {
                        oFile.iFileID = iSFileID;
                        oFile.bUploaded = true;
                        CommonDB.saveFile(oFile);
                        completion.didRequestCompletion(oReturn, null);
                    } else {
                        completion.didRequestCompletion(null, new Error("Failed to confirm uploading file"));
                    }
                }
            } else {
                completion.didRequestCompletion(null, new Error("Failed to upload file"));
            }
        } catch (Exception ex) {
            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto.InterException", ex);
            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.UploadAssetFile.InterException", ex, context);
        }
    }
}
