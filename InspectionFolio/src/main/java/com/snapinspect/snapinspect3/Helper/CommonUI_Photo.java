package com.snapinspect.snapinspect3.Helper;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.view.View;
import android.widget.ImageView;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.github.dhaval2404.imagepicker.constant.ImageProvider;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.RequestCreator;
import com.squareup.picasso.Target;
import org.apache.http.Header;
import org.json.JSONObject;

import java.io.File;

public class CommonUI_Photo {

    public static void didInsertNoticePhoto(Context oContext, long iPhoneID) {
        Intent intent = new Intent(Constants.Broadcasts.sInsertNoticePhoto);
        // You can also include some extra data.
        intent.putExtra(Constants.Extras.iPhotoID, iPhoneID);
        LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
    }

    public static void showPhotoThumbFile(Context oContext, long iPhotoID, final ImageView imageView, final View indicatorView) {
        int imagePlaceholderDrawable = R.drawable.image_placeholder;
        try {
            final ai_Photo oPhoto = ai_Photo.findById(ai_Photo.class, iPhotoID);
            if (!StringUtils.isEmpty(oPhoto.sThumb) && CommonHelper.bFileExist(oPhoto.getThumb())) {
                Picasso.get()
                        .load(new File(oPhoto.getThumb()))
                        .error(imagePlaceholderDrawable)
                        .into(imageView);
            } else if (oPhoto.iSPhotoID > 0 && oPhoto.bUploaded) {
                indicatorView.setVisibility(View.VISIBLE);

                RequestParams oParams = new RequestParams();
                oParams.add("iCustomerID", CommonHelper.GetPreferenceString(oContext, "iCustomerID"));
                oParams.add("sToken", CommonHelper.GetPreferenceString(oContext, "sToken"));
                oParams.add("iPhotoID", String.valueOf(oPhoto.iSPhotoID));

                final String sURL = "/IOAPI/GetPhotoThumb";

                IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                    @Override
                    public void onSuccess(int statusCode, Header[] headers, final JSONObject response) {
                        try {
                            if (statusCode == 200 && response.getBoolean("success")) {
                                String sURL = response.getString("sURL");
                                RequestCreator request = Picasso.get()
                                        .load(sURL)
                                        .error(imagePlaceholderDrawable);

                                request.into(new Target() {
                                    @Override
                                    public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
                                        if (bitmap != null) {
                                            final ai_Photo oPhoto = ai_Photo.findById(ai_Photo.class, iPhotoID);
                                            O_FileName oFileName = O_FileName.getPhotoFileName();
                                            CommonHelper.SaveImage(oFileName.sThumbNail, bitmap);
                                            oPhoto.sThumb = oFileName.sThumbNail;
                                            CommonDB.savePhoto(oPhoto);
                                        }
                                    }
                                    @Override
                                    public void onBitmapFailed(Exception e, Drawable errorDrawable) { }
                                    @Override
                                    public void onPrepareLoad(Drawable placeHolderDrawable) { }
                                });

                                request.into(imageView, new Callback() {
                                    @Override
                                    public void onSuccess() {
                                        indicatorView.setVisibility(View.GONE);
                                    }

                                    @Override
                                    public void onError(Exception e) {
                                        indicatorView.setVisibility(View.GONE);
                                    }
                                });
                            } else {
                                new Handler().post(() -> {
                                    indicatorView.setVisibility(View.GONE);
                                    imageView.setImageResource(imagePlaceholderDrawable);
                                });
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception(ex);
                            new Handler().post(() -> {
                                indicatorView.setVisibility(View.GONE);
                                imageView.setImageResource(imagePlaceholderDrawable);
                            });
                        }
                    }

                    @Override
                    public void onFailure(int statusCode, Header[] headers, Throwable e, JSONObject errorResponse) {
                        new Handler().post(() -> {
                            indicatorView.setVisibility(View.GONE);
                            imageView.setImageResource(imagePlaceholderDrawable);
                        });
                    }
                });
            } else {
                imageView.setImageResource(imagePlaceholderDrawable);
            }

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
            imageView.setImageResource(imagePlaceholderDrawable);
        }
    }

    public static void showFullFile(Context oContext, long iPhotoID, final ImageView imageView, final View indicatorView) {
        final int imagePlaceholderDrawable = R.drawable.image_placeholder_full;
        try {
            final ai_Photo oPhoto = CommonDB.GetPhotoByIdSugar(iPhotoID);
            if (!StringUtils.isEmpty(oPhoto.sFile) && CommonHelper.bFileExist(oPhoto.getFile())) {
                Picasso.get()
                        .load(new File(oPhoto.getFile()))
                        .error(imagePlaceholderDrawable)
                        .into(imageView);
            } else if (oPhoto.iSPhotoID > 0 && oPhoto.bUploaded) {
                indicatorView.setVisibility(View.VISIBLE);

                RequestParams oParams = new RequestParams();
                oParams.add("iCustomerID", CommonHelper.GetPreferenceString(oContext, "iCustomerID"));
                oParams.add("sToken", CommonHelper.GetPreferenceString(oContext, "sToken"));
                oParams.add("iPhotoID", String.valueOf(oPhoto.iSPhotoID));

                final String sURL = "/IOAPI/GetPhoto";

                IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                    @Override
                    public void onSuccess(int statusCode, Header[] headers, final JSONObject response) {
                        try {
                            if (statusCode == 200 && response.getBoolean("success")) {
                                String sURL = response.getString("sURL");
                                RequestCreator request = Picasso.get()
                                        .load(sURL)
                                        .error(imagePlaceholderDrawable);

                                request.into(new Target() {
                                    @Override
                                    public void onBitmapLoaded(Bitmap bitmap, Picasso.LoadedFrom from) {
                                        if (bitmap != null) {
                                            final ai_Photo oPhoto = ai_Photo.findById(ai_Photo.class, iPhotoID);
                                            O_FileName oFileName = O_FileName.getPhotoFileName();
                                            CommonHelper.SaveImage(oFileName.sFilePath, bitmap);
                                            oPhoto.sFile = oFileName.sFilePath;
                                            if (CommonHelper.bFileExist(oPhoto.getThumb())) {
                                                CommonHelper.SaveThumb(oFileName.sThumbNail, bitmap);
                                                oPhoto.sThumb = oFileName.sThumbNail;
                                            }
                                            CommonDB.savePhoto(oPhoto);
                                        }
                                    }
                                    @Override
                                    public void onBitmapFailed(Exception e, Drawable errorDrawable) { }
                                    @Override
                                    public void onPrepareLoad(Drawable placeHolderDrawable) { }
                                });

                                request.into(imageView, new Callback() {
                                    @Override
                                    public void onSuccess() {
                                        indicatorView.setVisibility(View.GONE);
                                    }

                                    @Override
                                    public void onError(Exception e) {
                                        indicatorView.setVisibility(View.GONE);
                                    }
                                });
                            } else {
                                new Handler().post(() -> {
                                    indicatorView.setVisibility(View.GONE);
                                    imageView.setImageResource(imagePlaceholderDrawable);
                                });
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception(ex);
                            new Handler().post(() -> {
                                indicatorView.setVisibility(View.GONE);
                                imageView.setImageResource(imagePlaceholderDrawable);
                            });
                        }
                    }

                    @Override
                    public void onFailure(int statusCode, Header[] headers, Throwable e, JSONObject errorResponse) {
                        new Handler().post(() -> {
                            indicatorView.setVisibility(View.GONE);
                            imageView.setImageResource(imagePlaceholderDrawable);
                        });
                    }
                });
            } else {
                imageView.setImageResource(imagePlaceholderDrawable);
            }

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
            imageView.setImageResource(imagePlaceholderDrawable);
        }
    }

    /**
     * Launches the camera to take a photo using the ImagePicker library.
     *
     * @param activity The Activity context to use for launching the picker.
     *                 The calling Activity should handle the result in onActivityResult or Activity Result API.
     *
     * Requirements:
     * - The ImagePicker library must be included as a dependency.
     * - REQUEST_TAKE_PHOTO should be a unique integer request code defined in your app.
     * - ImageProvider.CAMERA must be imported from the ImagePicker library.
     *
     * Example usage:
     * <pre>
     *     CommonUI_Photo.takePhoto(this);
     * </pre>
     */
    public static void takePhoto(Activity activity, int requestCode) {
        new com.github.dhaval2404.imagepicker.ImagePicker.Builder(activity)
                .saveDir(FileUtils.getRootPath() + Constants.Paths.tempFolder)
                .provider(ImageProvider.CAMERA)
                .start(requestCode);
    }
}
