package com.snapinspect.snapinspect3.Helper

import android.annotation.SuppressLint
import android.content.Context
import android.content.SharedPreferences

class SharedConfig private constructor(private val context: Context) {
    companion object {
        @SuppressLint("StaticFieldLeak")
        private var instance: SharedConfig? = null
        @JvmStatic
        fun getInstance(context: Context): SharedConfig {
            if (instance == null) {
                instance = SharedConfig(context)
            }
            return instance!!
        }
    }

    private val prefs: SharedPreferences by lazy {
        context.getSharedPreferences(Constants.Paths.prefs, Context.MODE_PRIVATE)
    }

    fun saveInsType(id: Int) {
        val recentIds = getRecentSavedIds(Constants.Keys.RECENT_INS_TYPES).toMutableList()
        recentIds.removeAll { it == id} // Remove if already exists
        recentIds.add(0, id) // Add to the beginning
        if (recentIds.size > Constants.Limits.RECENT_INS_TYPES) {
            recentIds.removeAt(recentIds.lastIndex) // Remove the oldest if more than 5
        }
        prefs.edit().putString(Constants.Keys.RECENT_INS_TYPES, recentIds.joinToString(",")).apply()
    }

    fun getRecentInsTypes(): List<Int> {
        return getRecentSavedIds(Constants.Keys.RECENT_INS_TYPES)
    }

    fun saveLayoutID(id: Int) {
        val recentIds = getRecentSavedIds(Constants.Keys.RECENT_LAYOUT_IDS).toMutableList()
        recentIds.removeAll { it == id} // Remove if already exists
        recentIds.add(0, id) // Add to the beginning
        if (recentIds.size > Constants.Limits.RECENT_LAYOUT_IDS) {
            recentIds.removeAt(recentIds.lastIndex) // Remove the oldest if more than 5
        }
        prefs.edit().putString(Constants.Keys.RECENT_LAYOUT_IDS, recentIds.joinToString(",")).apply()
    }

    fun getRecentLayoutIds(): List<Int> {
        return getRecentSavedIds(Constants.Keys.RECENT_LAYOUT_IDS)
    }

    private fun getRecentSavedIds(key: String): List<Int> {
        val idsString = prefs.getString(key, "") ?: ""
        return if (idsString.isEmpty()) emptyList() else idsString.split(",").map { it.toInt() }
    }

    fun getRecentNoticeCategories(): List<Int> {
        return getRecentSavedIds(Constants.Keys.RECENT_NOTICE_CATEGORIES)
    }

    fun saveNoticeCategory(id: Int) {
        val recentIds = getRecentSavedIds(Constants.Keys.RECENT_NOTICE_CATEGORIES).toMutableList()
        recentIds.removeAll { it == id} // Remove if already exists
        recentIds.add(0, id) // Add to the beginning
        prefs.edit().putString(Constants.Keys.RECENT_NOTICE_CATEGORIES, recentIds.joinToString(",")).apply()
    }
}
