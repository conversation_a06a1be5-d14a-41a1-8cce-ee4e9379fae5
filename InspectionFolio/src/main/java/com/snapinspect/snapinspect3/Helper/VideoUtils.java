package com.snapinspect.snapinspect3.Helper;

import android.content.Context;
import android.graphics.Bitmap;
import android.media.MediaCodec;
import android.media.MediaExtractor;
import android.media.MediaFormat;
import android.media.MediaMuxer;
import android.media.ThumbnailUtils;
import android.util.Log;

import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Map;

import static android.provider.MediaStore.Images.Thumbnails.MINI_KIND;

/**
 * Utility class for video processing operations including merging,
 * thumbnail generation, and other video-related tasks.
 */
public class VideoUtils {
    private static final String TAG = "VideoUtils";

    /** Default buffer size (1 MiB). */
    private static final int DEFAULT_BUFFER_SIZE = 1024 * 1024;

    /** Default JPEG quality for thumbnails. */
    private static final int DEFAULT_THUMBNAIL_QUALITY = 100;

    /** Gap between video frames in microseconds (33ms ≈ 30fps) */
    private static final long FRAME_GAP_US = 33_000;

    /*────────────────────────── Strategies ──────────────────────────*/

    public interface VideoMergeStrategy {
        O_FileName mergeVideos(Context context, String... videoPaths) throws Exception;
    }

    public static class VideoProcessingConfig {
        private int bufferSize = DEFAULT_BUFFER_SIZE;
        private int thumbnailQuality = DEFAULT_THUMBNAIL_QUALITY;
        private boolean createThumbnail = true;

        public VideoProcessingConfig setBufferSize(int size) {
            bufferSize = size; return this;
        }

        public VideoProcessingConfig setThumbnailQuality(int q) {
            thumbnailQuality = Math.max(0, Math.min(100, q)); return this;
        }

        public VideoProcessingConfig setCreateThumbnail(boolean c) {
            createThumbnail = c; return this;
        }

        public int  getBufferSize()       { return bufferSize; }
        public int  getThumbnailQuality() { return thumbnailQuality; }
        public boolean shouldCreateThumbnail() { return createThumbnail; }
    }

    private static final VideoProcessingConfig DEFAULT_CONFIG = new VideoProcessingConfig();

    public static class MediaCodecMergeStrategy implements VideoMergeStrategy {

        private static final String PREFIX_VIDEO = "video/";
        private static final String PREFIX_AUDIO = "audio/";

        private final VideoProcessingConfig cfg;

        public MediaCodecMergeStrategy()             { this(DEFAULT_CONFIG); }
        public MediaCodecMergeStrategy(VideoProcessingConfig c) { this.cfg = c; }

        @Override public O_FileName mergeVideos(Context ctx, String... paths) throws Exception {
            if (paths == null || paths.length == 0)
                throw new IllegalArgumentException("No video paths provided");

            O_FileName out = O_FileName.getVideoFileName();
            MediaMuxer muxer = new MediaMuxer(out.sFilePath,
                    MediaMuxer.OutputFormat.MUXER_OUTPUT_MPEG_4);

            TrackIndexMap map = addTracksFromFirstClip(paths[0], muxer);
            muxer.start();                 // must start AFTER addTrack()

            long offsetUs = 0;
            for (String p : paths) {
                if (StringUtils.isEmpty(p)) continue;
                offsetUs = appendSamples(p, muxer, map, offsetUs, cfg);
            }

            muxer.stop();
            muxer.release();
            return out;
        }

        /*───────────────── helpers ─────────────────*/

        private static class TrackIndexMap {
            int video = -1;
            int audio = -1;
        }

        private static TrackIndexMap addTracksFromFirstClip(String clip, MediaMuxer muxer)
                throws IOException {
            MediaExtractor ex = new MediaExtractor();
            ex.setDataSource(clip);

            TrackIndexMap m = new TrackIndexMap();
            for (int i = 0; i < ex.getTrackCount(); i++) {
                MediaFormat fmt = ex.getTrackFormat(i);
                String mime = fmt.getString(MediaFormat.KEY_MIME);

                if (mime.startsWith(PREFIX_VIDEO) && m.video < 0)
                    m.video = muxer.addTrack(fmt);
                else if (mime.startsWith(PREFIX_AUDIO) && m.audio < 0)
                    m.audio = muxer.addTrack(fmt);

                if (m.video >= 0 && m.audio >= 0) break;
            }
            ex.release();
            if (m.video < 0) throw new IllegalStateException("No video track in first clip");
            return m;
        }

        private static long appendSamples(String clip, MediaMuxer muxer, TrackIndexMap map,
                                          long baseUs, VideoProcessingConfig cfg) throws IOException {
            MediaExtractor ex = new MediaExtractor();
            ex.setDataSource(clip);

            Map<Integer,Integer> route = new HashMap<>();
            for (int i = 0; i < ex.getTrackCount(); i++) {
                String mime = ex.getTrackFormat(i).getString(MediaFormat.KEY_MIME);
                if (mime.startsWith(PREFIX_VIDEO))       route.put(i, map.video);
                else if (mime.startsWith(PREFIX_AUDIO))  route.put(i, map.audio);
            }

            ByteBuffer buf = ByteBuffer.allocate(cfg.getBufferSize());
            MediaCodec.BufferInfo info = new MediaCodec.BufferInfo();
            long clipEndUs = 0;

            for (Map.Entry<Integer,Integer> e : route.entrySet()) {
                int src = e.getKey(), dst = e.getValue();
                ex.selectTrack(src);

                while (true) {
                    int size = ex.readSampleData(buf, 0);
                    if (size < 0) { ex.unselectTrack(src); break; }

                    info.size = size;
                    info.offset = 0;
                    // Convert MediaExtractor flags to MediaCodec flags
                    int flags = 0;
                    if ((ex.getSampleFlags() & MediaExtractor.SAMPLE_FLAG_SYNC) != 0) {
                        flags |= MediaCodec.BUFFER_FLAG_KEY_FRAME;
                    }
                    if (size == 0) {
                        flags |= MediaCodec.BUFFER_FLAG_END_OF_STREAM;
                    }
                    info.flags = flags;
                    info.presentationTimeUs = ex.getSampleTime() + baseUs;

                    muxer.writeSampleData(dst, buf, info);
                    clipEndUs = Math.max(clipEndUs, info.presentationTimeUs);
                    ex.advance();
                }
            }
            ex.release();
            return clipEndUs + FRAME_GAP_US;   // +1 frame (≈33 ms) gap-free
        }
    }

    /*────────────────── Convenience wrappers ──────────────────*/

    public static O_FileName mergeVideosWithMediaCodec(Context ctx, String... paths) throws Exception {
        return new MediaCodecMergeStrategy().mergeVideos(ctx, paths);
    }

    public static void saveVideoThumbnail(O_FileName video) {
        saveVideoThumbnail(video, DEFAULT_CONFIG.getThumbnailQuality());
    }

    public static void saveVideoThumbnail(O_FileName video, int quality) {
        try {
            Bitmap bmp = ThumbnailUtils.createVideoThumbnail(video.sFilePath, MINI_KIND);
            if (bmp == null) { Log.e(TAG, "ThumbnailUtils returned null"); return; }

            File f = new File(video.sThumbNail);
            if (!f.exists()) f.createNewFile();

            try (FileOutputStream fos = new FileOutputStream(f);
                 BufferedOutputStream bos = new BufferedOutputStream(fos, DEFAULT_BUFFER_SIZE / 8)) {
                bmp.compress(Bitmap.CompressFormat.JPEG, quality, bos);
                bos.flush();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error saving thumbnail", e);
        }
    }
}