package com.snapinspect.snapinspect3.Helper;

import com.loopj.android.http.AsyncHttpClient;
import com.loopj.android.http.BinaryHttpResponseHandler;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.app.App;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.net.URL;

import static com.snapinspect.snapinspect3.Helper.Constants.Urls.BASE_URL;
import static com.snapinspect.snapinspect3.Helper.Constants.Urls.URL_SCHEME;

/**
 * Created by <PERSON><PERSON><PERSON> on 10/03/14.
 */
public class IF_RestClient {
    private static AsyncHttpClient client;
    public static void post(String url, RequestParams params, JsonHttpResponseHandler oResponse){
        //client.post("http://www.google.com", null, oResponse);


        try {
            if (client == null){

                //KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
                //trustStore.load(null, null);
                //MySSLSocketFactory sf = new MySSLSocketFactory(trustStore);
                //sf.setHostnameVerifier(MySSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
                //client = new AsyncHttpClient();
                //client.setSSLSocketFactory(sf);
               // new AsyncHttpClient()
                client = new AsyncHttpClient(true, 80, 443);
                client.setTimeout(120000);

            }
            client.post(getAbsoluteUrl(url), params, oResponse);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_RestClient.post", ex, null);
        }

        //SyncHttpClient oClient1 = new SyncHttpClient();
        //oClient1.p
    }

    public static void postWithUrl(String url, RequestParams params, JsonHttpResponseHandler oResponse){
        //client.post("http://www.google.com", null, oResponse);


        try {
            if (client == null){

                //KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
                //trustStore.load(null, null);
                //MySSLSocketFactory sf = new MySSLSocketFactory(trustStore);
                //sf.setHostnameVerifier(MySSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
                //client = new AsyncHttpClient();
                //client.setSSLSocketFactory(sf);
                // new AsyncHttpClient()
                client = new AsyncHttpClient(true, 80, 443);
                client.setTimeout(120000);

            }
            client.post(url, params, oResponse);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "IF_RestClient.post", ex, null);
        }

        //SyncHttpClient oClient1 = new SyncHttpClient();
        //oClient1.p
    }

    public static String getAbsoluteUrl(String relativeUrl) {
        String sUrl = App.getContext().getString(R.string.server_host);
        return URL_SCHEME + StringUtils.ifEmpty(sUrl, BASE_URL) + relativeUrl;
    }

    public static String getConnectUrl(String relativeUrl) {
        String sUrl = App.getContext().getString(R.string.connect_server_host);
        return URL_SCHEME + StringUtils.ifEmpty(sUrl, BASE_URL) + relativeUrl;
    }

   // String[] allowedTypes = new String[] { "text/xml" };
    public static void get(String url, String[] allowedTypes, final String sSavedPath){
        //final sSavedPath =
        if (client == null){
            client = new AsyncHttpClient(true, 80, 443);
            client.setTimeout(120000);
        }
        client.get(url, new BinaryHttpResponseHandler(allowedTypes){
            @Override
            public void onSuccess(byte[] imageData) {
                // Successfully got a response
                try{
                    File oFile = new File(sSavedPath);
                    if (oFile.exists()){
                        oFile.delete();
                    }
                    OutputStream f = new FileOutputStream(sSavedPath);
                    f.write(imageData);
                    f.close();
                }
                catch(Exception ex){

                    ai_BugHandler.ai_Handler_Exception("Exception", "IF_RestClient.get", ex, null);
                }
            }
        });
    }
}
