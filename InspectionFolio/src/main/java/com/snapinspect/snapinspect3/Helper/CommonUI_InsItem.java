package com.snapinspect.snapinspect3.Helper;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.*;
import android.graphics.drawable.Drawable;
import android.os.AsyncTask;
import android.os.Handler;
import android.util.DisplayMetrics;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.*;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.afollestad.materialdialogs.MaterialDialog;
import com.google.gson.Gson;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.activity.if_CameraX;
import com.snapinspect.snapinspect3.activity.if_CameraX.CameraSaveOption;
import com.snapinspect.snapinspect3.activity.if_EditComments;
import com.snapinspect.snapinspect3.activitynew.products.if_ProductCosts;
import com.snapinspect.snapinspect3.activity.if_selectrating;
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayAllPhotos;
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayPhoto;
import com.snapinspect.snapinspect3.activitynew.if_DateTimePicker;
import com.snapinspect.snapinspect3.util.NumberUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.ThrottledSearch;
import com.snapinspect.snapinspect3.views.RoundTextView;
import com.snapinspect.snapinspect3.views.RoundedImageView;
import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip;
import org.apache.http.Header;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import javax.annotation.Nullable;
import java.io.File;
import java.io.InputStream;
import java.lang.ref.WeakReference;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static android.text.InputType.*;
import static android.text.TextUtils.TruncateAt.MIDDLE;
import static android.view.Gravity.CENTER;
import static android.view.Gravity.CENTER_VERTICAL;
import static android.view.View.GONE;
import static android.widget.LinearLayout.HORIZONTAL;
import static android.widget.LinearLayout.VERTICAL;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_CONTROL_TYPE_SCHK;

public class CommonUI_InsItem {
    public static final int INSPECT_CELL_MARGIN_LEFT = 20; //(dp)
    public static final int INSPECT_CELL_MARGIN_RIGHT = 20;

    private static final String CONFIG_LIST_QUESTION_CHECK = "CB";
    private static final String CONFIG_LIST_QUESTION_TEXT = "TX";
    private static final String CONFIG_LIST_QUESTION_NUMBER = "NM";
    private static final String CONFIG_LIST_QUESTION_SINGLE = "SC";
    private static final String CONFIG_LIST_QUESTION_MULTI = "MC";

    public static final String sMessage_AddEditItem_DuplicateName = "The name is already in this Area, please enter a different Item Name.";

    //AreaOnlyInspection is the room and item inspection view, old approach     OneViewInspection is the all together view.

    public static void OneViewInspection_CommandButton(Context oContext,  ImageButton menuBtn, ai_InsItem oInsItem, boolean bReview){
        try {
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT
            );
            layoutParams.gravity = CENTER_VERTICAL;
            menuBtn.setLayoutParams(layoutParams);
            menuBtn.setImageResource(R.drawable.icon_menu);
            menuBtn.setBackgroundColor(Color.parseColor("#00ffffff"));
        } catch(Exception ex) {

        }
    }

    public static void OneViewInspection_FloorPlanButton(Context oContext, ImageButton dropPinButton) {
        try {
            LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT
            );
            int iMargin = (int) oContext.getResources().getDimension(R.dimen.margin_small);
            layoutParams.setMarginStart(iMargin);
            layoutParams.setMarginEnd(iMargin);
            layoutParams.gravity = CENTER_VERTICAL;
            dropPinButton.setScaleType(ImageView.ScaleType.FIT_START);
            dropPinButton.setLayoutParams(layoutParams);
            dropPinButton.setImageResource(R.drawable.icon_floor_plan_drop_pin);
            dropPinButton.setBackgroundColor(Color.TRANSPARENT);
        } catch(Exception ex) {

        }
    }

    public static void OneViewInspection_InstructionLabel(Context oContext, TextView oInstruction, String sInstruction){
        try{
            if (CommonConfig.bDisplayInstruction(oContext)){
                if (sInstruction !=  null && sInstruction.trim().length() > 0) {
                    LinearLayout.LayoutParams insLayout = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
                    insLayout.setMargins(CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT), 0, 0, 10);
                    oInstruction.setLayoutParams(insLayout);
                    oInstruction.setText(sInstruction);

                    oInstruction.setPadding(0, CommonHelper.pxFromDp(oContext, 5), CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 5));
                    oInstruction.setTextColor(Color.GRAY);
                    oInstruction.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
                    oInstruction.setGravity(Gravity.LEFT);
                } else {
                    oInstruction.setVisibility(GONE);
                }
            } else {
                oInstruction.setVisibility(GONE);
            }

        } catch(Exception ex) {

        }

    }
    public static void OneViewInspection_Label(Context oContext, ai_InsItem oInsItem,  boolean bFullInspection, LinearLayout headLayout){
        try {
            if (!CommonConfig.bDisplayInstruction(oContext)) {
                String sInstruction = CommonDB.GetInstruction(oInsItem, bFullInspection);
                if (sInstruction != null && !sInstruction.trim().isEmpty()){
                    LinearLayout infoLayout = new LinearLayout(oContext);
                    LinearLayout.LayoutParams infoParams = new LinearLayout.LayoutParams(
                            CommonHelper.pxFromDp(oContext, 30), CommonHelper.pxFromDp(oContext, 40));
                    infoParams.gravity = CENTER_VERTICAL;
                    infoLayout.setLayoutParams(infoParams);
                    infoLayout.setGravity(CENTER);

                    ImageView ivInfo = new ImageView(oContext);
                    ivInfo.setLayoutParams(new LinearLayout.LayoutParams(
                            CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 15)));
                    ivInfo.setImageResource(R.drawable.icon_info);
                    infoLayout.addView(ivInfo);

                    infoLayout.setOnClickListener(v -> new SimpleTooltip.Builder(oContext)
                            .anchorView(v)
                            .text(sInstruction)
                            .gravity(Gravity.BOTTOM)
                            .backgroundColor(oContext.getResources().getColor(R.color.colorPrimary))
                            .arrowColor(oContext.getResources().getColor(R.color.colorPrimary))
                            .animated(true)
                            .transparentOverlay(true)
                            .build()
                            .show()
                    );
                    headLayout.addView(infoLayout);
                }
            }
            final TextView oTextView = new TextView(oContext);
            oTextView.setLayoutParams(new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f));
            oTextView.setText(oInsItem.sName);
            oTextView.setPadding(0, 20, 20, 20);
            headLayout.addView(oTextView);

            if (oInsItem.sQType != null &&
                    (oInsItem.sQType.equalsIgnoreCase("C")
                    || oInsItem.sQType.equalsIgnoreCase("P")
                    || oInsItem.sQType.equalsIgnoreCase("G")
                    || oInsItem.sQType.equalsIgnoreCase("A"))) {
                String sNotice = CommonJson.GetJsonKeyValue("notice", oInsItem.sCustomOne);
                if (sNotice != null && sNotice.equals("1")) {
                    oTextView.setTextColor(Color.RED);
                } else {
                    oTextView.setTextColor(Color.BLACK);
                }
            }
            oTextView.setTypeface(oTextView.getTypeface(), Typeface.BOLD);
            oTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 14);
            oTextView.setGravity(Gravity.LEFT);

        } catch(Exception ex) {

        }
    }


    public static void AreaOnlyInspection_InstructionLabel(Context oContext, TextView oInstruction, String sInstruction){
        try {
            if (CommonConfig.bDisplayInstruction(oContext)) {
                if (sInstruction !=  null && sInstruction.trim().length() > 0) {
                    //TextView oInstruction = ((TextView)row.findViewById(R.id.txt_Video_Inst));
                    oInstruction.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT));
                    oInstruction.setText(sInstruction);
                    oInstruction.setPadding(20, 10, 20, 10);
                    oInstruction.setTextColor(Color.GRAY);
                    oInstruction.setTypeface(null, Typeface.ITALIC);
                    oInstruction.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
                    oInstruction.setGravity(CENTER);
                } else{
                    oInstruction.setVisibility(GONE);
                }
            } else {
                oInstruction.setVisibility(GONE);
            }

        }catch(Exception ex) {

        }

    }
    public static void AreaOnlyInspection_Label(Context oContext, TextView oTextView, ai_InsItem oInsItem, int iPosition){
        try {
            oTextView.setLayoutParams(new LinearLayout.LayoutParams(LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT, 1.0f));
            oTextView.setText(oInsItem.sName);
            oTextView.setPadding(20, 30, 20, 30);
            oTextView.setTextColor(Color.BLACK);
            //oTextView.setBackgroundColor(Color.GRAY);

            oTextView.setText(oInsItem.sName);

            oTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18);
            oTextView.setGravity(CENTER);
            oTextView.setTag(iPosition);
            if (oInsItem.sQType != null && (oInsItem.sQType.equalsIgnoreCase("C") || oInsItem.sQType.equalsIgnoreCase("P") || oInsItem.sQType.equalsIgnoreCase("G") || oInsItem.sQType.equalsIgnoreCase("A"))) {
                String sNotice = CommonJson.GetJsonKeyValue("notice", oInsItem.sCustomOne);
                if (sNotice != null && sNotice.equals("1")) {
                    oTextView.setTextColor(Color.RED);
                } else {
                    //oTextView.setTextAlignment(View.TEXT_ALIGNMENT_CENTER);
                    oTextView.setTextColor(Color.BLACK);
                }
            }
        } catch(Exception ex) {

        }
    }
    public static void AreaOnlyInspection_CommandButton(Context oContext,  ImageButton menuBtn, ai_InsItem oInsItem, boolean bReview){
        try {
            DisplayMetrics metrics = oContext.getResources().getDisplayMetrics();
           // boolean bReview = CommonValidate.bItemReviewEnabled(oInsItem);
            float dp = 35f;
            float fpixels = metrics.density * dp;
            int pixels = (int) (fpixels + 0.5f);

            menuBtn.setLayoutParams(new LinearLayout.LayoutParams(pixels, pixels));
            menuBtn.setPadding((int) (10 * metrics.density), 0, (int) (10 * metrics.density), 0);


            if (bReview) {
                menuBtn.setImageResource(R.drawable.menu_red);
            } else {
                menuBtn.setImageResource(R.drawable.icon_menu);
            }
            menuBtn.setBackgroundColor(Color.parseColor("#00ffffff"));
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }
    public static void InsItem_RenderCHK(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1, String sValue, int iPosition, int iButtonPerRow, int iDefaultButtonWidth, int iDefaultButtonHeight, FrameLayout oLayout) {
        //if (sFV1.startsWith(SI_S_CONFIG_KEY_CHK)) {
        //ai_Item oItem = new ai_Item();
        String[] sArray = CommonInsItem.getCHKLabelItems(sFV1), sArrayValue = sValue.split("\\|");
        if (sArray.length > sArrayValue.length) {
            for (int i = 0; i < sArray.length; i++) {
                if (i >= sArrayValue.length - 1) {
                    String sv = sValue.equals("") ? "A" : "|A";
                    sValue = sValue + sv;
                }
            }
            CommonHelper.SetValue(iPosition, oInsItem, sValue);
            CommonDB.saveInsItem(oInsItem);
        }

        sArrayValue = sValue.split("\\|");

        LinearLayout rootLayout = new LinearLayout(oContext);
        LinearLayout.LayoutParams oLayoutParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        rootLayout.setLayoutParams(oLayoutParams);
        rootLayout.setOrientation(VERTICAL);

        final String[] finalSArray = sArray;
        int iRows = (sArray.length % iButtonPerRow) == 0 ? sArray.length / iButtonPerRow : (sArray.length / iButtonPerRow + 1);
        for (int i = 0; i < iRows; i++) {
            final LinearLayout oTempLayout = new LinearLayout(oContext);
            LinearLayout.LayoutParams oParams2 = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);

            oTempLayout.setLayoutParams(oParams2);
            oTempLayout.setOrientation(HORIZONTAL);

            final LinearLayout olabLayout = new LinearLayout(oContext);
            olabLayout.setLayoutParams(oParams2);
            olabLayout.setOrientation(HORIZONTAL);

            for (int j = 0; j < iButtonPerRow; j++) {
                int iID = i * iButtonPerRow + j;
                if (iID < sArray.length) {
                    Button oButton = CommonHelper.CreateButton(oContext, iDefaultButtonHeight >> 1,
                            sArray, sArrayValue, null, iID);
                    oButton.setId(iID);
                    int heightBtn = CommonHelper.pxFromDp(oContext, iDefaultButtonHeight);
                    int widthBtn = heightBtn;
                    LinearLayout.LayoutParams oBtnParams = new LinearLayout.LayoutParams(widthBtn, heightBtn);
                    oBtnParams.setMargins(
                            CommonHelper.pxFromDp(oContext, (iDefaultButtonWidth - iDefaultButtonHeight) / 2) + 5, 0,
                            CommonHelper.pxFromDp(oContext, (iDefaultButtonWidth - iDefaultButtonHeight) / 2) + 5, 0);
                    oButton.setLayoutParams(oBtnParams);

                    oButton.setTag("" + oInsItem.getId() + "_" + iPosition + "_" + iID);
                    oButton.setOnClickListener(view -> {
                        String[] arrID = view.getTag().toString().split("_");
                        //   ai_InsItem oTempInsItem = ai_InsItem.findById(ai_InsItem.class, Long.parseLong(arrID[0]));
                        int iTempPosition = Integer.parseInt(arrID[1]);
                        int iID1 = Integer.parseInt(arrID[2]);
                        String sTempValue = CommonHelper.GetValue(iTempPosition, oInsItem);
                        String[] arrValue = sTempValue.split("\\|");
                        if (arrValue[iID1].equalsIgnoreCase("Y")) {
                            arrValue[iID1] = "N";
                        } else if (arrValue[iID1].equalsIgnoreCase("N")) {
                            arrValue[iID1] = "A";
                        } else if (arrValue[iID1].equalsIgnoreCase("A")) {
                            arrValue[iID1] = "Y";
                        }

                        CommonHelper.ChangeButton(oContext, oButton, iDefaultButtonHeight >> 1,
                                finalSArray[iID1], arrValue[iID1], null);
                        String sTextValue = CommonHelper.GetChkValue(arrValue);
                        CommonHelper.SetValue(iTempPosition, oInsItem, sTextValue);
                        CommonDB.saveInsItem(oInsItem);
                        broadcastInsItemSaved(oContext, oPInsItem.getId(), iPosition);
                    });

                    if (oPInsItem.canAssignAllRating()) {
                        oButton.setOnLongClickListener(view -> {
                            String[] arrID = view.getTag().toString().split("_");
                            int iTempPosition = Integer.parseInt(arrID[1]);
                            int iID12 = Integer.parseInt(arrID[2]);
                            List<ai_InsItem> lsInsItem = CommonDB.GetChildInsItem(oPInsItem.getId(), oPInsItem.iInsID);
                            BulkSetRating_V3(lsInsItem, iTempPosition, iID12, oInsItem, oPInsItem, oContext, iPosition, true);
                            return true;
                        });
                    }
                    oTempLayout.addView(oButton);

                    TextView oLabel = new TextView(oContext);
                    LinearLayout.LayoutParams olblParams = new LinearLayout.LayoutParams(
                            CommonHelper.pxFromDp(oContext, iDefaultButtonWidth), LinearLayout.LayoutParams.WRAP_CONTENT);
                    olblParams.setMargins(5, 5, 5, 5);
                    oLabel.setLayoutParams(olblParams);
                    oLabel.setGravity(Gravity.CENTER_HORIZONTAL);
                    oLabel.setLines(2);
                    oLabel.setEllipsize(MIDDLE);
                    oLabel.setText(sArray[iID]);

                    oLabel.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
                    olabLayout.addView(oLabel);
                }
            }
            rootLayout.addView(oTempLayout);
            rootLayout.addView(olabLayout);
        }

        oLayout.addView(rootLayout);
        ViewUtils.setViewMargins(rootLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);

        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, iDefaultButtonHeight);
    }

    public static void InsItem_RenderMCHK(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1, String sValue, int iPosition, int iButtonPerRow, int iDefaultButtonWidth, int iDefaultButtonHeight, FrameLayout oLayout ) {
        String[] sArray = CommonInsItem.getCHKLabelItems(sFV1), sColors = CommonInsItem.getCHKColorItems(sFV1);
        String[] sArrayValue = sValue.split("\\|");
        if (sArray.length > sArrayValue.length) {
            for (int i = 0; i < sArray.length; i++) {
                if (i >= sArrayValue.length - 1) {
                    String sv = sValue.equals("") ? "A" : "|A";
                    sValue = sValue + sv;
                }
            }
            CommonHelper.SetValue(iPosition, oInsItem, sValue);
            CommonDB.saveInsItem(oInsItem);
        }

        sArrayValue = sValue.split("\\|");

        LinearLayout rootLayout = new LinearLayout(oContext);
        LinearLayout.LayoutParams oLayoutParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        rootLayout.setLayoutParams(oLayoutParams);
        rootLayout.setOrientation(VERTICAL);

        final String[] finalSArray = sArray,  finalSColors = sColors;
        int iRows = (sArray.length % iButtonPerRow) == 0 ? sArray.length / iButtonPerRow : (sArray.length / iButtonPerRow + 1);
        for (int i = 0; i < iRows; i++) {
            final LinearLayout oTempLayout = new LinearLayout(oContext);
            LinearLayout.LayoutParams oParams2 = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            oTempLayout.setLayoutParams(oParams2);
            oTempLayout.setOrientation(HORIZONTAL);

            final LinearLayout olabLayout = new LinearLayout(oContext);
            olabLayout.setLayoutParams(oParams2);
            olabLayout.setOrientation(HORIZONTAL);

            for (int j = 0; j < iButtonPerRow; j++) {
                int iID = i * iButtonPerRow + j;
                if (iID < sArray.length) {
                    Button oButton = CommonHelper.CreateButton(oContext, iDefaultButtonHeight >> 1, sArray, sArrayValue, sColors, iID);
                    oButton.setId(iID);
                    int heightBtn = CommonHelper.pxFromDp(oContext, iDefaultButtonHeight);
                    LinearLayout.LayoutParams oBtnParams = new LinearLayout.LayoutParams(heightBtn, heightBtn);
                    oBtnParams.setMargins(
                            CommonHelper.pxFromDp(oContext, (iDefaultButtonWidth - iDefaultButtonHeight) / 2) + 5, 0,
                            CommonHelper.pxFromDp(oContext, (iDefaultButtonWidth - iDefaultButtonHeight) / 2) + 5, 0
                    );
                    oButton.setLayoutParams(oBtnParams);
                    oButton.setTag("" + oInsItem.getId() + "_" + iPosition + "_" + iID);

                    oButton.setOnClickListener(view -> {
                        String[] arrID = view.getTag().toString().split("_");
                        int iTempPosition = Integer.parseInt(arrID[1]);
                        int iID1 = Integer.parseInt(arrID[2]);

                        String sTempValue = CommonHelper.GetValue(iTempPosition, oInsItem);
                        String[] arrValue = sTempValue.split("\\|");

                        if (arrValue[iID1].equalsIgnoreCase("Y")) arrValue[iID1] = "A";
                        else arrValue[iID1] = "Y";

                        CommonHelper.ChangeButton(oContext, oButton, iDefaultButtonHeight >> 1,
                                finalSArray[iID1], arrValue[iID1], iID1 < finalSColors.length ? finalSColors[iID1]: null);
                        String sTextValue = CommonHelper.GetChkValue(arrValue);
                        //Log.v("Saved Value", sTextValue);
                        CommonHelper.SetValue(iTempPosition, oInsItem, sTextValue);
                        CommonDB.saveInsItem(oInsItem);
                        broadcastInsItemSaved(oContext, oPInsItem.getId(), iPosition);
                    });
                    if (oPInsItem.canAssignAllRating()) {
                        oButton.setOnLongClickListener(view -> {
                            String[] arrID = view.getTag().toString().split("_");
                            int iTempPosition = Integer.parseInt(arrID[1]);
                            int iID12 = Integer.parseInt(arrID[2]);
                            List<ai_InsItem> lsInsItem = CommonDB.GetChildInsItem(oPInsItem.getId(), oPInsItem.iInsID);
                            BulkSetRating_V3(lsInsItem, iTempPosition, iID12, oInsItem, oPInsItem, oContext, iPosition, false);
                            return true;
                        });
                    }
                    oTempLayout.addView(oButton);

                    TextView oLabel = new TextView(oContext);
                    LinearLayout.LayoutParams olblParams = new LinearLayout.LayoutParams(
                            CommonHelper.pxFromDp(oContext, iDefaultButtonWidth), LinearLayout.LayoutParams.WRAP_CONTENT);
                    olblParams.setMargins(5, 5, 5, 5);
                    oLabel.setLayoutParams(olblParams);
                    oLabel.setGravity(Gravity.CENTER_HORIZONTAL);
                    oLabel.setLines(2);
                    oLabel.setEllipsize(MIDDLE);
                    oLabel.setText(sArray[iID]);

                    oLabel.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
                    olabLayout.addView(oLabel);
                }

            }
            rootLayout.addView(oTempLayout);
            rootLayout.addView(olabLayout);
        }

        oLayout.addView(rootLayout);
        ViewUtils.setViewMargins(rootLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);
        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, iDefaultButtonHeight);
    }

    public static void InsItem_RenderSCHK(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1, String sValue, int iPosition, int iButtonPerRow, int iDefaultButtonWidth, int iDefaultButtonHeight, FrameLayout oLayout) {
        String[] sArray = CommonInsItem.getCHKLabelItems(sFV1), sColors = CommonInsItem.getCHKColorItems(sFV1);
        String[] sArrayValue = sValue.split("\\|");
        if (sArray.length > sArrayValue.length) {
            for (int i = 0; i < sArray.length; i++) {
                if (i >= sArrayValue.length - 1) {
                    String sv = sValue.equals("") ? "A" : "|A";
                    sValue = sValue + sv;
                }
            }
            CommonHelper.SetValue(iPosition, oInsItem, sValue);
            CommonDB.saveInsItem(oInsItem);
        }

        sArrayValue = sValue.split("\\|");

        final LinearLayout rootLayout = new LinearLayout(oContext);
        LinearLayout.LayoutParams oLayoutParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        rootLayout.setLayoutParams(oLayoutParams);
        rootLayout.setOrientation(VERTICAL);

        final String[] finalSArray = sArray, finalSColors = sColors;
        int iRows = (sArray.length % iButtonPerRow) == 0 ? sArray.length / iButtonPerRow : (sArray.length / iButtonPerRow + 1);
        for (int i = 0; i < iRows; i++) {
            final LinearLayout oTempLayout = new LinearLayout(oContext);
            LinearLayout.LayoutParams oParams2 = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.WRAP_CONTENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            oTempLayout.setLayoutParams(oParams2);
            oTempLayout.setOrientation(HORIZONTAL);
            oTempLayout.setTag("Button");
            final LinearLayout olabLayout = new LinearLayout(oContext);
            olabLayout.setLayoutParams(oParams2);
            olabLayout.setOrientation(HORIZONTAL);
            olabLayout.setTag("Label");
            for (int j = 0; j < iButtonPerRow; j++) {
                int iID = i * iButtonPerRow + j;
                if (iID < sArray.length) {
                    Button oButton = CommonHelper.CreateButton(oContext, iDefaultButtonHeight >> 1, sArray, sArrayValue, sColors, iID);
                    oButton.setId(iID);
                    int heightBtn = CommonHelper.pxFromDp(oContext, iDefaultButtonHeight);
                    int widthBtn = heightBtn;
                    LinearLayout.LayoutParams oBtnParams = new LinearLayout.LayoutParams(widthBtn, heightBtn);
                    oBtnParams.setMargins(
                            CommonHelper.pxFromDp(oContext, (iDefaultButtonWidth - iDefaultButtonHeight) / 2) + 5, 0,
                            CommonHelper.pxFromDp(oContext, (iDefaultButtonWidth - iDefaultButtonHeight) / 2) + 5, 0);
                    oButton.setLayoutParams(oBtnParams);

                    oButton.setTag("" + oInsItem.getId() + "_" + iPosition + "_" + iID);

                    oButton.setOnClickListener(view -> {
                        String[] arrID = view.getTag().toString().split("_");
                        //ai_InsItem oTempInsItem = ai_InsItem.findById(ai_InsItem.class, Long.parseLong(arrID[0]));
                        int iTempPosition = Integer.parseInt(arrID[1]);
                        int iID1 = Integer.parseInt(arrID[2]);
                        String sTempValue = CommonHelper.GetValue(iTempPosition, oInsItem);
                        String[] arrValue = sTempValue.split("\\|");
                        boolean bCheckAgain = !arrValue[iID1].equalsIgnoreCase("Y");
                        Arrays.fill(arrValue, "A");
                        for (int x = 0; x < rootLayout.getChildCount(); ++x) {
                            ViewGroup oTempViewGroup = (ViewGroup) rootLayout.getChildAt(x);
                            if (!oTempViewGroup.getTag().equals("Button")) {
                                continue;
                            }
                            for (int y = 0; y < oTempViewGroup.getChildCount(); y++) {
                                View child = oTempViewGroup.getChildAt(y);
                                if (child instanceof Button) {
                                    Button nextChild = (Button) child;
                                    nextChild.setBackgroundResource(R.drawable.btn_oval_normal);
                                }
                            }
                        }
                        if (bCheckAgain) {
                            //[view parent]
                            arrValue[iID1] = "Y";
                            CommonHelper.ChangeButton(oContext, oButton, iDefaultButtonHeight >> 1,
                                    finalSArray[iID1], arrValue[iID1], iID1 < finalSColors.length ? finalSColors[iID1]: null);
                        }
                        String sTextValue = CommonHelper.GetChkValue(arrValue);
                        CommonHelper.SetValue(iTempPosition, oInsItem, sTextValue);
                        CommonDB.saveInsItem(oInsItem);
                        broadcastInsItemSaved(oContext, oPInsItem.getId(), iPosition);
                    });

                    if (oPInsItem.canAssignAllRating()) {
                        oButton.setOnLongClickListener(view -> {
                            String[] arrID = view.getTag().toString().split("_");
                            int iTempPosition = Integer.parseInt(arrID[1]);
                            int iID12 = Integer.parseInt(arrID[2]);
                            List<ai_InsItem> lsInsItem = CommonDB.GetChildInsItem(oPInsItem.getId(), oPInsItem.iInsID);
                            BulkSetRating_V3(lsInsItem, iTempPosition, iID12, oInsItem, oPInsItem, oContext, iPosition, false);
                            return true;
                        });
                    }
                    oTempLayout.addView(oButton);

                    TextView oLabel = new TextView(oContext);
                    LinearLayout.LayoutParams olblParams = new LinearLayout.LayoutParams(
                            CommonHelper.pxFromDp(oContext, iDefaultButtonWidth), LinearLayout.LayoutParams.WRAP_CONTENT);
                    olblParams.setMargins(5, 5, 5, 5);
                    oLabel.setLayoutParams(olblParams);
                    oLabel.setGravity(Gravity.CENTER_HORIZONTAL);
                    oLabel.setLines(2);
                    oLabel.setEllipsize(MIDDLE);
                    oLabel.setText(sArray[iID]);

                    oLabel.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
                    olabLayout.addView(oLabel);
                }
            }
            rootLayout.addView(oTempLayout);
            rootLayout.addView(olabLayout);
        }
        oLayout.addView(rootLayout);
        ViewUtils.setViewMargins(rootLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);
        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, iDefaultButtonHeight);
    }

    public static void InsItem_RenderCMT(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1, String sValue, int iPosition, FrameLayout oLayout ){
        EditText oEditText = new EditText(oContext);
        LinearLayout.LayoutParams oLayoutParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        oEditText.setPadding(
            CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 10),
            CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 10));
        oLayoutParams.weight = 1;
        oEditText.setLayoutParams(oLayoutParams);
        oEditText.setTextColor(Color.BLACK);
        oEditText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        oEditText.setFocusable(true);
        oEditText.setHintTextColor(oContext.getResources().getColor(R.color.placeholder_color));
        oEditText.setTag("" + oInsItem.getId() + "_" + iPosition);

        oEditText.setFocusable(false);
        oEditText.setClickable(true);
        oEditText.setOnClickListener(view -> showsEditCommentsView(oContext, view, oPInsItem.iSLayoutID));

        oEditText.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                Intent intent = new Intent(Constants.Broadcasts.sEditTextFocused);
                intent.putExtra(Constants.Extras.iInsItemID, oInsItem.getId().intValue());
                intent.putExtra(Constants.Extras.iPosition, iPosition);
                intent.putExtra(Constants.Extras.iPLayoutID, oPInsItem.iSLayoutID);
                LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
            } else {
                CommonHelper.SetValue(iPosition, oInsItem, oEditText.getText().toString());
                CommonDB.saveInsItem(oInsItem);
            }
        });

        new ThrottledSearch((Activity) oContext, text -> {
            CommonHelper.SetValue(iPosition, oInsItem, text);
            CommonDB.saveInsItem(oInsItem);
        }).bindTo(oEditText);

        if (sFV1.length() > 4) {
            if (sFV1.startsWith("{")) {
                oEditText.setHint(CommonJson.GetJsonKeyValue("sLabel", sFV1, "Comments"));
            } else {
                oEditText.setHint(sFV1.substring(4, sFV1.length() - 1));
            }
        } else {
            oEditText.setHint("Comments");
        }
        oEditText.setBackgroundResource(R.drawable.md_transparent);
        oEditText.setText(sValue);

        LinearLayout pLayout = new LinearLayout(oContext);
        LinearLayout.LayoutParams oParams1 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        pLayout.setLayoutParams(oParams1);
        pLayout.addView(oEditText);
        pLayout.setBackgroundResource(R.drawable.bg_edittext);

        View rightView = rightTypeIcon(oContext, R.drawable.comment_box_text);
        rightView.setOnClickListener(view -> showsEditCommentsView(oContext, oEditText, oPInsItem.iSLayoutID));
        pLayout.addView(rightView);

        oLayout.addView(pLayout);
        ViewUtils.setViewMargins(pLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);

        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, -1);
    }

    public static void InsItem_RenderCOST(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1, String sValue, int iPosition, FrameLayout oLayout ){
        EditText oEditText = new EditText(oContext);
        LinearLayout.LayoutParams oLayoutParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        oEditText.setPadding(
                CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 10));
        oLayoutParams.weight = 1;
        oEditText.setLayoutParams(oLayoutParams);
        oEditText.setTextColor(Color.BLACK);
        oEditText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        oEditText.setFocusable(true);
        oEditText.setHintTextColor(oContext.getResources().getColor(R.color.placeholder_color));
        oEditText.setTag(oInsItem.getId() + "_" + iPosition);

        oEditText.setFocusable(false);
        oEditText.setClickable(true);
        oEditText.setOnClickListener(view -> showsProductCostsView(oContext, view, oPInsItem.iSLayoutID));

        if (sFV1.length() > 4) {
            if (sFV1.startsWith("{")) {
                oEditText.setHint(CommonJson.GetJsonKeyValue("sLabel", sFV1, "Cost"));
            } else {
                oEditText.setHint(sFV1.substring(4, sFV1.length() - 1));
            }
        } else {
            oEditText.setHint("Costs");
        }
        oEditText.setBackgroundResource(R.drawable.md_transparent);
        double totalCost = CommonHelper.getDouble(sValue);
        if (totalCost > 0) {
            oEditText.setText(NumberUtils.formatCurrencyNoSymbol(totalCost));
        } else {
            oEditText.setText("");
        }

        LinearLayout pLayout = new LinearLayout(oContext);
        LinearLayout.LayoutParams oParams1 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        pLayout.setLayoutParams(oParams1);
        pLayout.addView(oEditText);
        pLayout.setBackgroundResource(R.drawable.bg_edittext);

        View rightView = rightTypeIcon(oContext, R.drawable.comment_box_cost);
        rightView.setOnClickListener(view -> showsProductCostsView(oContext, oEditText, oPInsItem.iSLayoutID));
        pLayout.addView(rightView);

        oLayout.addView(pLayout);
        ViewUtils.setViewMargins(pLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);

        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, -1);
    }

    public static void InsItem_RenderDT(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1, String sValue, int iPosition, FrameLayout oLayout ) {
        String sPattern = CommonJson.GetJsonKeyValue("Content", sFV1);

        EditText oEditText = new EditText(oContext);
        LinearLayout.LayoutParams oLayoutParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        oEditText.setPadding(
                CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 10));
        oLayoutParams.weight = 1;
        oEditText.setLayoutParams(oLayoutParams);
        oEditText.setTextColor(Color.BLACK);
        oEditText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        oEditText.setFocusable(true);
        oEditText.setHintTextColor(oContext.getResources().getColor(R.color.placeholder_color));
        oEditText.setTag("" + oInsItem.getId() + "_" + iPosition);

        oEditText.setFocusable(false);
        oEditText.setClickable(true);
        oEditText.setOnClickListener(view -> showsDatePicker(oContext, view, sPattern));

        if (sFV1.length() > 4) {
            if (sFV1.startsWith("{")) {
                oEditText.setHint(CommonJson.GetJsonKeyValue("sLabel", sFV1, "Date"));
            } else {
                oEditText.setHint(sFV1.substring(4, sFV1.length() - 1));
            }
        } else {
            oEditText.setHint("Comments");
        }
        oEditText.setText(sValue);
        oEditText.setBackgroundResource(R.drawable.md_transparent);

        LinearLayout pLayout = new LinearLayout(oContext);
        LinearLayout.LayoutParams oParams1 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        pLayout.setLayoutParams(oParams1);
        pLayout.addView(oEditText);
        pLayout.setBackgroundResource(R.drawable.bg_edittext);

        View rightView = rightTypeIcon(oContext, R.drawable.comment_box_date);
        pLayout.addView(rightView);
        rightView.setOnClickListener(view -> showsDatePicker(oContext, oEditText, sPattern));

        oLayout.addView(pLayout);
        ViewUtils.setViewMargins(pLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);

        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, -1);
    }

    private static void showsDatePicker(Context oContext, View view, String datePattern) {
        String[] arrParams = view.getTag().toString().split("_");
        Intent oIntent = new Intent(oContext, if_DateTimePicker.class);
        oIntent.putExtra(Constants.Extras.iPosition, Integer.parseInt(arrParams[1]));
        oIntent.putExtra(Constants.Extras.iInsItemID, Integer.parseInt(arrParams[0]));
        oIntent.putExtra(Constants.Extras.sDatePattern, datePattern);
        oContext.startActivity(oIntent);
    }

    private static void showsEditCommentsView(Context oContext, View view, int iSPLayoutID) {
        String[] arrParams = view.getTag().toString().split("_");
        Intent oIntent = new Intent(oContext, if_EditComments.class);
        oIntent.putExtra(Constants.Extras.iPosition, Integer.parseInt(arrParams[1]));
        oIntent.putExtra(Constants.Extras.iInsItemID, Integer.parseInt(arrParams[0]));
        oIntent.putExtra(Constants.Extras.iPLayoutID, iSPLayoutID);
        oContext.startActivity(oIntent);
    }

    private static void showsProductCostsView(Context oContext, View view, int iSPLayoutID) {
        String[] arrParams = view.getTag().toString().split("_");

        int iInsItemID = Integer.parseInt(arrParams[0]);
        ai_InsItem oInsItem = db_InsItem.GetInsItem_ByID(iInsItemID);
        if (oInsItem == null || oInsItem.getServerInsItemID() > 0) {
            CommonUI.ShowAlert(oContext, "Message", "Cost can not be modified under Edit Inspection mode.");
            return;
        }

        Intent oIntent = new Intent(oContext, if_ProductCosts.class);
        oIntent.putExtra(Constants.Extras.iPosition, Integer.parseInt(arrParams[1]));
        oIntent.putExtra(Constants.Extras.iInsItemID, Integer.parseInt(arrParams[0]));
        oIntent.putExtra(Constants.Extras.iPLayoutID, iSPLayoutID);
        oContext.startActivity(oIntent);
    }

    private static void showSelectRatingView(Context oContext, View view) {
        String[] arrParams = view.getTag().toString().split("_");
        Intent oIntent = new Intent(oContext, if_selectrating.class);
        oIntent.putExtra(Constants.Extras.iPosition, Integer.parseInt(arrParams[1]));
        oIntent.putExtra(Constants.Extras.iInsItemID, Integer.parseInt(arrParams[0]));
        oContext.startActivity(oIntent);
    }

    private static void InsItem_RenderCompulsoryMark(
            Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem,
            String sFV1, String sValue, FrameLayout oLayout, int viewHeight) {

        if (oInsItem.bInspectionByPassCompulsory ||
                oPInsItem.bInspectionByPassCompulsory ||
                !oPInsItem.checkIfNeedValidatingCompulsoryItem(sFV1) ||
                    !oInsItem.isCompulsory(sFV1, sValue))
            return;

        // mark icon
        ImageButton button = new ImageButton(oContext);
        button.setBackgroundResource(R.color.transparent);
        button.setImageResource(R.drawable.icon_exclamation_sign);
        FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                viewHeight > 0 ? CommonHelper.pxFromDp(oContext, viewHeight) : FrameLayout.LayoutParams.MATCH_PARENT);
        params.setMargins(0, CommonHelper.pxFromDp(oContext, 10), 0, 0);
        button.setLayoutParams(params);
        oLayout.addView(button, 0);
    }

    public static void InsItem_RenderLST(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1, String sValue, int iPosition, FrameLayout oLayout) {
        JSONArray sContent = CommonJson.GetJSONArrayValue("Content", sFV1);
        if (sContent == null || sContent.length() == 0) return;

        ArrayList<String> arrContent = new ArrayList<>();
        for (int i = 0; i < sContent.length(); i++) {
            try {
                arrContent.add(sContent.get(i).toString());
            } catch (JSONException ignored) { }
        }

        if (arrContent.isEmpty()) return;

        LinearLayout pLayout = new LinearLayout(oContext);
        LinearLayout.LayoutParams pLayoutParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        pLayout.setLayoutParams(pLayoutParams);
        pLayout.setOrientation(VERTICAL);

        String sLabel = CommonJson.GetJsonKeyValue("sLabel", sFV1);
        if (!StringUtils.isEmpty(sLabel)) {
            TextView oTextView = new TextView(oContext);
            LinearLayout.LayoutParams oTextViewLayoutParams = new LinearLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
            oTextViewLayoutParams.setMargins(0, 0, 0, CommonUI.dpToPx(10, oContext));
            oTextView.setLayoutParams(oTextViewLayoutParams);
            oTextView.setTextColor(Color.BLACK);
            oTextView.setTypeface(oTextView.getTypeface(), Typeface.BOLD);
            oTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
            oTextView.setText(sLabel);
            pLayout.addView(oTextView);
        }

        String sQT = CommonJson.GetJsonKeyValue("sQT", sFV1);
        int lstItemLayoutId = getLstItemLayoutId(sQT);
        final Gson gson = new Gson();
        String[] arrValues = CommonJson.GetStringsFromJsonArray(sValue);
        for (int i = 0; i < arrContent.size(); i++) {
            LinearLayout itemView = (LinearLayout) LayoutInflater.from(oContext).inflate(lstItemLayoutId, null);
            TextView textTitle = itemView.findViewById(R.id.txt_Title);
            textTitle.setText(arrContent.get(i));
            if (lstItemLayoutId == R.layout.cell_insitem_lst_cb_item) {
                ImageView stateIcon = itemView.findViewById(R.id.icon_check_state);
                InsItem_RenderLSTItemStateTag(stateIcon, i < arrValues.length ? arrValues[i] : null);
                itemView.setOnClickListener(v -> {
                    ImageView icon = v.findViewById(R.id.icon_check_state);
                    if (CONFIG_LIST_QUESTION_CHECK.equalsIgnoreCase(sQT)) {
                        switch ((int) icon.getTag()) {
                            case 0:
                                InsItem_RenderLSTItemStateTag(icon, "" + 1);
                                break;
                            case 1:
                                InsItem_RenderLSTItemStateTag(icon, "" + 2);
                                break;
                            case 2:
                                InsItem_RenderLSTItemStateTag(icon, "" + 0);
                                break;
                            default:
                                break;
                        }
                    } else if (CONFIG_LIST_QUESTION_SINGLE.equalsIgnoreCase(sQT)) {
                        for (int j = 0; j < pLayout.getChildCount(); j++) {
                            if (pLayout.getChildAt(j) instanceof LinearLayout) {
                                View child = pLayout.getChildAt(j);
                                ImageView childIcon = child.findViewById(R.id.icon_check_state);
                                InsItem_RenderLSTItemStateTag(childIcon, "" + 0);
                            }
                        }
                        InsItem_RenderLSTItemStateTag(icon, "" + 1);
                    } else if (CONFIG_LIST_QUESTION_MULTI.equalsIgnoreCase(sQT)) {
                        switch ((int) icon.getTag()) {
                            case 0:
                                InsItem_RenderLSTItemStateTag(icon, "" + 1);
                                break;
                            case 1:
                                InsItem_RenderLSTItemStateTag(icon, "" + 0);
                                break;
                            default:
                                break;
                        }
                    }

                    ArrayList<String> values = new ArrayList<>();
                    for (int j = 0; j < pLayout.getChildCount(); j++) {
                        if (pLayout.getChildAt(j) instanceof LinearLayout) {
                            View child = pLayout.getChildAt(j);
                            values.add("" + (int) child.findViewById(R.id.icon_check_state).getTag());
                        }
                    }

                    CommonHelper.SetValue(iPosition, oInsItem, gson.toJson(values));
                    CommonDB.saveInsItem(oInsItem);
                    broadcastInsItemSaved(oContext, oPInsItem.getId(), iPosition);
                });
            } else if (lstItemLayoutId == R.layout.cell_insitem_lst_tx_item) {
                EditText txtValue = itemView.findViewById(R.id.txt_Value);
                txtValue.setText(i < arrValues.length ? arrValues[i] : null);
                txtValue.setInputType(getEditTextInputTypeForListQuestion(sQT));

                boolean isTablet = oContext.getResources().getBoolean(R.bool.md_is_tablet);
                double radioAsScreenWidth = isTablet ? 0.1 : 0.2;
                ViewGroup.LayoutParams params = txtValue.getLayoutParams();
                params.width = (int) (CommonUI.getScreenWidth() * radioAsScreenWidth);
                txtValue.setLayoutParams(params);

                new ThrottledSearch((Activity) oContext, text -> {
                    ArrayList<String> values = new ArrayList<>();
                    for (int j = 0; j < pLayout.getChildCount(); j++) {
                        if (pLayout.getChildAt(j) instanceof LinearLayout) {
                            View child = pLayout.getChildAt(j);
                            EditText editText = child.findViewById(R.id.txt_Value);
                            values.add(editText.getText().toString());
                        }
                    }
                    CommonHelper.SetValue(iPosition, oInsItem, gson.toJson(values));
                    CommonDB.saveInsItem(oInsItem);
                }).bindTo(txtValue);

                txtValue.setOnLongClickListener(v -> {
                    new SimpleTooltip.Builder(oContext)
                            .anchorView(v)
                            .text(txtValue.getText().toString())
                            .gravity(Gravity.START)
                            .maxWidth((float) (CommonUI.getScreenWidth() * (1 - 2 * radioAsScreenWidth)))
                            .backgroundColor(oContext.getResources().getColor(R.color.colorPrimary))
                            .arrowColor(oContext.getResources().getColor(R.color.colorPrimary))
                            .transparentOverlay(true)
                            .build()
                            .show();
                    return true;
                });
            }

            pLayout.addView(itemView);
        }

        oLayout.addView(pLayout);

        ViewUtils.setViewMargins(pLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);
        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, -1);
    }

    private static int getLstItemLayoutId(@Nullable String sQT) {
        if (sQT == null) return R.layout.cell_insitem_lst_label_item;
        switch (sQT) {
            case CONFIG_LIST_QUESTION_CHECK:
            case CONFIG_LIST_QUESTION_SINGLE:
            case CONFIG_LIST_QUESTION_MULTI:
                return R.layout.cell_insitem_lst_cb_item;
            case CONFIG_LIST_QUESTION_TEXT:
            case CONFIG_LIST_QUESTION_NUMBER:
                return R.layout.cell_insitem_lst_tx_item;
            default:
                return R.layout.cell_insitem_lst_label_item;
        }
    }

    private static int getEditTextInputTypeForListQuestion(String sQT) {
        return CONFIG_LIST_QUESTION_NUMBER.equalsIgnoreCase(sQT) ? TYPE_CLASS_NUMBER : TYPE_CLASS_TEXT;
    }

    private static void InsItem_RenderLSTItemStateTag(ImageView checkIcon, String sValue) {
        int tag = CommonHelper.getInt(sValue);
        switch (tag) {
            case 0:
                checkIcon.setImageResource(R.drawable.icon_option_unchecked);
                break;
            case 1:
                checkIcon.setImageResource(R.drawable.icon_option_checked);
                break;
            case 2:
                checkIcon.setImageResource(R.drawable.icon_option_checked_no);
                break;
        }
        checkIcon.setTag(tag);
    }

    public static FrameLayout rightTypeIcon(Context oContext, int iconRes) {
        FrameLayout rightLayout = new FrameLayout(oContext);
        FrameLayout.LayoutParams rightLayoutParams = new FrameLayout.LayoutParams(
                CommonHelper.pxFromDp(oContext, 40), CommonHelper.pxFromDp(oContext, 40));
        rightLayout.setLayoutParams(rightLayoutParams);
        ImageView rightImageView = new ImageView(oContext);
        FrameLayout.LayoutParams rightImageParams = new FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        rightImageParams.gravity = CENTER;
        rightImageView.setLayoutParams(rightImageParams);
        rightImageView.setImageResource(iconRes);
        rightLayout.addView(rightImageView);
        return rightLayout;
    }

    public static void InsItem_RenderNUM(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1, String sValue, int iPosition, FrameLayout oLayout ) {
        EditText oEditText = new EditText(oContext);
        LinearLayout.LayoutParams oLayoutParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        oLayoutParams.setMargins(0, 10, 0, 10);
        oEditText.setPadding(
                CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 10));
        oLayoutParams.weight = 1;
        oEditText.setLayoutParams(oLayoutParams);
        oEditText.setTextColor(Color.BLACK);
        oEditText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        oEditText.setInputType(TYPE_CLASS_NUMBER | TYPE_NUMBER_FLAG_DECIMAL | TYPE_NUMBER_FLAG_SIGNED);
        oEditText.setImeOptions(EditorInfo.IME_ACTION_DONE);
        oEditText.setHintTextColor(oContext.getResources().getColor(R.color.placeholder_color));
        oEditText.setSingleLine();
        oEditText.setTag("" + oInsItem.getId() + "_" + iPosition);

        oEditText.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                Intent intent = new Intent(Constants.Broadcasts.sEditTextFocused);
                intent.putExtra(Constants.Extras.iInsItemID, oInsItem.getId().intValue());
                intent.putExtra(Constants.Extras.iPosition, iPosition);
                intent.putExtra(Constants.Extras.iPLayoutID, oPInsItem.iSLayoutID);
                LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
            } else {
                CommonHelper.SetValue(iPosition, oInsItem, oEditText.getText().toString());
                CommonDB.saveInsItem(oInsItem);
            }
        });

        new ThrottledSearch((Activity) oContext, text -> {
            CommonHelper.SetValue(iPosition, oInsItem, text);
            CommonDB.saveInsItem(oInsItem);
        }).bindTo(oEditText);

        if (sFV1.length() > 4) {
            if (sFV1.startsWith("{")) {
                oEditText.setHint(CommonJson.GetJsonKeyValue("sLabel", sFV1, "Number"));
            } else {
                oEditText.setHint(sFV1.substring(4, sFV1.length() - 1));
            }
        } else {
            oEditText.setHint("Number");
        }
        oEditText.setText(sValue);
        oEditText.setBackgroundColor(oContext.getResources().getColor(R.color.transparent));

        LinearLayout pLayout = new LinearLayout(oContext);
        LinearLayout.LayoutParams oParams1 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        pLayout.setLayoutParams(oParams1);
        pLayout.addView(oEditText);

        pLayout.setBackgroundResource(R.drawable.bg_edittext);

        View rightView = rightTypeIcon(oContext, R.drawable.comment_box_number);
        rightView.setOnClickListener(v -> {
            oEditText.requestFocus();
            CommonHelper.showSoftKeyboard((Activity)oContext, oEditText);
        });
        pLayout.addView(rightView);
        oLayout.addView(pLayout);

        ViewUtils.setViewMargins(pLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);
        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, -1);
    }


    public static void BulkSetRating_V3(final List<ai_InsItem> lsInsItem, final int iControlNumber,
                                        final int iRatingNumber, final ai_InsItem oInsItem, final ai_InsItem oPInsItem,
                                        final Context oContext, int iPosition, boolean bShowNo){

        final String sConfig= CommonHelper.GetConfig(iControlNumber, oInsItem, oPInsItem);
        if (sConfig == null || sConfig.trim().equalsIgnoreCase("")){
            return;
        }
        String[] lsOption;
        if (bShowNo) {
            lsOption = new String[]{"Check", "Check No", "Uncheck"};
        }
        else {
            lsOption = new String[]{"Check",  "Uncheck"};
        }
        new MaterialDialog.Builder(oContext)
                .title("Bulk Assign Rating")
                .items(lsOption)
                .itemsCallbackSingleChoice(-1, (dialog, view, which, text) -> {
                    if (text.toString().equalsIgnoreCase("Check")){
                        AssignAllRating(lsInsItem, iControlNumber, iRatingNumber, oPInsItem,  sConfig, "Y");
                    }
                    else if (text.toString().equalsIgnoreCase("Uncheck")){
                        AssignAllRating(lsInsItem, iControlNumber, iRatingNumber, oPInsItem,  sConfig, "A");
                    }
                    else if (text.toString().equalsIgnoreCase("Check No")){
                        AssignAllRating(lsInsItem, iControlNumber, iRatingNumber, oPInsItem,  sConfig, "N");
                    }
                    Intent intent = new Intent(Constants.Broadcasts.sReloadInsItem);
                    // You can also include some extra data.
                    intent.putExtra(Constants.Extras.iPInsItemID, oPInsItem.getId());
                    intent.putExtra(Constants.Extras.iPosition, iPosition);
                    LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
                    return true;
                })
                .negativeText(R.string.md_cancel_label)
                .show();

    }
    private static void AssignAllRating(final List<ai_InsItem> lsInsItem, final int iControlNumber,
                                        final int iRatingNumber, final ai_InsItem oPInsItem,
                                         String sConfig, String sSampleRating){
        for (int i=0; i< lsInsItem.size(); i++){
            ai_InsItem oTempInsItem = lsInsItem.get(i);
            String sTempConfig = CommonHelper.GetConfig(iControlNumber, oTempInsItem, oPInsItem);
            if (sConfig.equalsIgnoreCase(sTempConfig)){
                String sValue = CommonHelper.GetValue(iControlNumber, oTempInsItem);
                String[] arrValue = sValue.split("\\|");
                int iControlType = CommonInsItem.getControlType(sConfig);
                if (iControlType == SI_CONTROL_TYPE_SCHK){
                    Arrays.fill(arrValue, "A");
                }
                arrValue[iRatingNumber] = sSampleRating;
                String sTextValue = CommonHelper.GetChkValue(arrValue);
                CommonHelper.SetValue(iControlNumber, oTempInsItem, sTextValue);
                CommonDB.saveInsItem(oTempInsItem);
            }
        }
    }

    public static void broadcastInsItemSaved(Context oContext, long iPInsItemId, int iPosition) {
        Intent intent = new Intent(Constants.Broadcasts.sInsItemChanged);
        intent.putExtra(Constants.Extras.iPInsItemID, iPInsItemId);
        intent.putExtra(Constants.Extras.iPosition, iPosition);
        LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
    }

    public static void InsItem_RenderPTO(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1, String sValue, int iPosition, FrameLayout oLayout) {
        long iInsItemID = oInsItem.getId();
        LinearLayout oRootView = (LinearLayout) LayoutInflater.from(oContext).inflate(R.layout.cell_insitem_photo_new, null);
        ImageButton oImageButton = oRootView.findViewById(R.id.btn_TakePhoto);
        oImageButton.setTag(iInsItemID);
        oImageButton.setOnClickListener(view -> {
            if (!CommonValidate.validateGPSLocationPermission(oContext)) return;
            Intent oIntent = if_CameraX.newIntent(oContext, CameraSaveOption.PHOTO,
                    Integer.parseInt(view.getTag().toString()), iPosition, false, Constants.Limits.iMaxPhotoCount);
            oContext.startActivity(oIntent);
        });

        LinearLayout ollPhotoList = oRootView.findViewById(R.id.ll_PhotoList);
        List<ai_Photo> lsPhoto = CommonDB_Inspection.GetInsItemPhotos(iInsItemID, sValue);

        List<String> sSplit = Arrays.asList(sValue.split(","));
        int width = CommonHelper.pxFromDp(oContext, 48), height = CommonHelper.pxFromDp(oContext, 48);
        int radius = CommonHelper.pxFromDp(oContext, 24);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(width, height);
        layoutParams.setMargins(
                CommonHelper.pxFromDp(oContext, 10), CommonHelper.pxFromDp(oContext, 5),
                CommonHelper.pxFromDp(oContext, 0), CommonHelper.pxFromDp(oContext, 5));

        if (lsPhoto.size() > 0) {
            Drawable drawable = oContext.getResources().getDrawable(R.drawable.bg_gray_indicator);
            drawable.setColorFilter(Color.RED, PorterDuff.Mode.SRC_ATOP);
            TextView tvIndicator = oRootView.findViewById(R.id.tv_photo_indicator);
            tvIndicator.setText("" + lsPhoto.size());
            oRootView.findViewById(R.id.red_indicator).setVisibility(View.VISIBLE);
            oRootView.findViewById(R.id.gray_indicator).setVisibility(GONE);
        }

        for (int i = 0; i < lsPhoto.size(); i++) {
            ai_Photo oPhoto = lsPhoto.get(i);
            if (i == 4) {
                RoundTextView tvMore = new RoundTextView(oContext);
                tvMore.setSolidColor(oContext.getResources().getColor(R.color.gray_border_color));
                tvMore.setText("Show\nAll");
                tvMore.setCornerRadius(radius);
                tvMore.setTextColor(oContext.getResources().getColor(R.color.placeholderColor));
                tvMore.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
                tvMore.setGravity(CENTER);
                tvMore.setLayoutParams(layoutParams);
                ollPhotoList.addView(tvMore);

                tvMore.setOnClickListener(view -> {
                    oContext.startActivity(if_DisplayAllPhotos.newIntent(oContext, iInsItemID, sValue, false));
                });

                break;
            }

            if (sSplit.contains("" + oPhoto.getId())) {
                FrameLayout frameLayout = new FrameLayout(oContext);
                FrameLayout.LayoutParams photoLayoutParams = new FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
                );
                RoundedImageView oImageView = new RoundedImageView(oContext);
                oImageView.setLayoutParams(photoLayoutParams);
                frameLayout.addView(oImageView);

                ProgressBar progressBar = new ProgressBar(oContext);
                progressBar.setIndeterminate(true);
                progressBar.setVisibility(GONE);

                int barSize = (int) (width * 0.4);
                FrameLayout.LayoutParams barLayoutParams = new FrameLayout.LayoutParams(barSize, barSize);
                barLayoutParams.gravity = CENTER;
                progressBar.setLayoutParams(barLayoutParams);
                frameLayout.addView(progressBar);
                ollPhotoList.addView(frameLayout);

                LinearLayout.LayoutParams frameLayoutParams = new LinearLayout.LayoutParams(width, height);
                frameLayoutParams.setMargins(
                        CommonHelper.pxFromDp(oContext, 10), CommonHelper.pxFromDp(oContext, 5),
                        CommonHelper.pxFromDp(oContext, 0), CommonHelper.pxFromDp(oContext, 5));
                frameLayout.setLayoutParams(frameLayoutParams);

                oImageView.setTag(oPhoto.getId());
                CommonUI_Photo.showPhotoThumbFile(oContext, oPhoto.getId(), oImageView, progressBar);

                File imgFile = new File(oPhoto.getThumb());
                if (imgFile.exists()) {
                    oImageView.setOnClickListener(view -> {
                        long lInsItemID = oInsItem.getId();
                        oContext.startActivity(if_DisplayPhoto.newIntent(oContext,
                                Long.parseLong(view.getTag().toString()), iPosition, lInsItemID, sValue,
                                false, false));
                    });

                } else if (oPhoto.iSPhotoID > 0 && oPhoto.bUploaded){
                    final ai_Photo fPhoto = oPhoto;
                    oImageView.setOnClickListener(view -> {
                        String[] options = {"Download Photo", "View Comments Only", "Download All Photos"};
                        new MaterialDialog.Builder(oContext)
                                .title("Photo In Cloud")
                                .items(options)
                                .itemsCallbackSingleChoice(-1, (dialog, view1, which, text) -> {
                                    if (which == 0) {
                                        downloadPhoto(oContext, fPhoto, null);
                                    } else if (which == 1) {
                                        long lInsItemID = oInsItem.getId();
                                        oContext.startActivity(if_DisplayPhoto.newIntent(oContext,
                                                fPhoto.getId(), iPosition, lInsItemID, sValue,
                                                false, false));
                                    } else if (which == 2) {
                                        Intent intent = new Intent(Constants.Broadcasts.sActionDownloadAllPhotos);
                                        LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
                                    }
                                    return true;
                                })
                                .negativeText(R.string.md_cancel_label)
                                .show();
                    });
                }
            }
        }

        LinearLayout pLayout = new LinearLayout(oContext);
        LinearLayout.LayoutParams oParams1 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        pLayout.setLayoutParams(oParams1);
        pLayout.addView(oRootView);
        oLayout.addView(pLayout);

        ViewUtils.setViewMargins(pLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);

        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, -1);
    }

    public static void InsItem_RenderSEL(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1, String sValue, int iPosition, FrameLayout oLayout) {
        LinearLayout selLayout = new LinearLayout(oContext);
        selLayout.setOrientation(HORIZONTAL);
        LinearLayout.LayoutParams oLayoutParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        selLayout.setGravity(CENTER_VERTICAL);
        selLayout.setBackgroundResource(R.drawable.bg_edittext);
        selLayout.setLayoutParams(oLayoutParams);

        final EditText oEditText = new EditText(oContext);
        LinearLayout.LayoutParams etParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        etParams.weight = 1;
        oEditText.setLayoutParams(etParams);
        oEditText.setPadding(
                CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, 15), CommonHelper.pxFromDp(oContext, 10));
        oEditText.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        oEditText.setHintTextColor(oContext.getResources().getColor(R.color.placeholder_color));
        oEditText.setSingleLine();
        oEditText.setTextColor(Color.BLACK);
        oEditText.setFocusable(false);
        oEditText.setClickable(true);
        oEditText.setTag("" + oInsItem.getId() + "_" + iPosition);
        oEditText.setOnClickListener(view -> showSelectRatingView(oContext, view));

        if (sValue == null || sValue.trim().equalsIgnoreCase("")) {
            oEditText.setHint((CommonJson.GetJsonKeyValue("sLabel", sFV1) == null || CommonJson.GetJsonKeyValue("sLabel", sFV1).equals("")) ? "Choose an answer" : CommonJson.GetJsonKeyValue("sLabel", sFV1));
            oEditText.setText("");
        } else {
            oEditText.setText(sValue);
        }
        oEditText.setBackgroundColor(oContext.getResources().getColor(R.color.transparent));
        selLayout.addView(oEditText);

        View rightView = CommonUI_InsItem.rightTypeIcon(oContext, R.drawable.comment_box_list);
        rightView.setOnClickListener(view -> showSelectRatingView(oContext, oEditText));
        selLayout.addView(rightView);
        oLayout.addView(selLayout);

        ViewUtils.setViewMargins(selLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);

        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, -1);
    }


    /**
     *
     * Download photo from server with the server photo id and load the image data into disk
     *
     * @param oContext the context
     * @param oPhoto the ai_Photo instance
     * @param listener if there is no listener, it will redirect to if_DisplayPhoto and display the photo,
     *                 otherwise it will call the listener and not redirect to if_DisplayPhoto
     */
    public static void downloadPhoto(Context oContext, ai_Photo oPhoto, LoadPhotoImage.LoadPhotoImageListener listener) {
        try {
            final MaterialDialog oDialog;
            oDialog = CommonUI.ShowMaterialProgressDialog(oContext, "Message", "Downloading, please wait ...");

            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(oContext, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(oContext, "sToken"));
            oParams.add("iPhotoID", String.valueOf(oPhoto.iSPhotoID));

            final String sURL = "/IOAPI/GetPhoto";

            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, Header[] headers, final JSONObject response) {
                    oDialog.dismiss();
                    if (statusCode == 200) {
                        try {
                            if (response.getBoolean("success")) {
                                try {
                                    new Handler().post(() -> {
                                        try {
                                            String sURL1 = response.getString("sURL");
                                            if (sURL1 != null) {
                                                new LoadPhotoImage(oContext, oPhoto, listener).execute(sURL1);
                                            }
                                        } catch (Exception e) {
                                            ai_BugHandler.ai_Handler_Exception("Per", "if_signup.IF_RestClient.Runnable", e);
                                        }
                                    });
                                } catch (Exception ex) {
                                    ai_BugHandler.ai_Handler_Exception("Exception", "Ins_C_Adapter.DownloadPhoto.Step1", ex,oContext);
                                }
                            } else if (!response.getBoolean("success")) {
                                new Handler().post(() -> {
                                    oDialog.dismiss();
                                    try {
                                        CommonUI.ShowAlert(oContext, "Error", response.getString("message"));
                                    } catch (Exception ex) {
                                        CommonUI.ShowAlert(oContext, "Error", "Asset Response Error. <NAME_EMAIL>.");
                                    }
                                });

                            } else {

                                new Handler().post(() -> {
                                    oDialog.dismiss();
                                    CommonUI.ShowAlert(oContext, "Error", "Please connect to internet to download photo.");
                                });

                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Exception", "Ins_C_Adapter.Top", ex, oContext);
                        }
                    }
                }

                @Override
                public void onFailure(int statusCode, Header[] headers, Throwable e, JSONObject errorResponse) {
                    new Handler().post(() -> {
                        oDialog.dismiss();
                        CommonUI.ShowAlert(oContext, "Error", "Please connect to internet to download photo.");
                    });
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "Ins_GA_Adapter.Upload.onReceiveResult", ex, oContext);
        }
    }

    private static int UpdatePhoto(ai_Photo aPhoto) {
        if (aPhoto != null && aPhoto.getId() > 0) {
            ai_Photo uPhoto = ai_Photo.findById(ai_Photo.class, aPhoto.getId());
            uPhoto.iSPhotoID = aPhoto.iSPhotoID;
            uPhoto.sThumb = aPhoto.sThumb;
            uPhoto.sFile = aPhoto.sFile;
            uPhoto.sComments = EscapeString(aPhoto.sComments);
            uPhoto.bUploaded = aPhoto.bUploaded;
            uPhoto.bDeleted = aPhoto.bDeleted;

            CommonDB.savePhoto(uPhoto);
            return uPhoto.getId().intValue();
        } else {
            ai_Photo sPhoto = new ai_Photo();

            sPhoto.iInsItemID = aPhoto.iInsItemID;
            sPhoto.iInsID = aPhoto.iInsID;
            sPhoto.sThumb = aPhoto.sThumb;
            sPhoto.iSPhotoID = aPhoto.iSPhotoID;
            sPhoto.sFile = aPhoto.sFile;
            sPhoto.iSize = CommonHelper.GetFileLength(aPhoto.sFile);
            Date oNow = new Date();
            sPhoto.dtDateTime = CommonHelper.sDateToString(oNow);

            CommonDB.savePhoto(sPhoto);
            return sPhoto.getId().intValue();
        }
    }

    private static String EscapeString(String sValue){
        return sValue.replaceAll("'","''");
    }

    public static void InsItem_RenderSCAN(Context oContext, ai_InsItem oInsItem, ai_InsItem oPInsItem, String sFV1,
                                          String sValue, int iPosition, FrameLayout oLayout) {
        long iInsItemID = oInsItem.getId();
        LinearLayout oRootView = (LinearLayout) LayoutInflater.from(oContext).inflate(R.layout.cell_insitem_scan_new, null);
        ImageButton oImageButton = oRootView.findViewById(R.id.btn_TakeScan);
        oImageButton.setTag(iInsItemID);
        oImageButton.setOnClickListener(view -> {
            Intent intent = new Intent(Constants.Broadcasts.ACTION_SCAN_DOCUMENT);
            intent.putExtra(Constants.Extras.iInsItemID, Integer.parseInt(view.getTag().toString()));
            intent.putExtra(Constants.Extras.iPosition, iPosition);
            LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
        });

        LinearLayout ollPhotoList = oRootView.findViewById(R.id.ll_ScanList);
        List<ai_Photo> lsPhoto = CommonDB_Inspection.GetInsItemPhotos(iInsItemID, sValue);

        List<String> sSplit = Arrays.asList(sValue.split(","));
        int width = CommonHelper.pxFromDp(oContext, 48);
        int height = CommonHelper.pxFromDp(oContext, 48);
        int radius = CommonHelper.pxFromDp(oContext, 24);
        LinearLayout.LayoutParams layoutParams = new LinearLayout.LayoutParams(width, height);
        layoutParams.setMargins(
                CommonHelper.pxFromDp(oContext, 10), CommonHelper.pxFromDp(oContext, 5),
                CommonHelper.pxFromDp(oContext, 0), CommonHelper.pxFromDp(oContext, 5));

        if (!lsPhoto.isEmpty()) {
            Drawable drawable = oContext.getResources().getDrawable(R.drawable.bg_gray_indicator);
            drawable.setColorFilter(Color.RED, PorterDuff.Mode.SRC_ATOP);
            TextView tvIndicator = oRootView.findViewById(R.id.tv_scan_indicator);
            tvIndicator.setText(String.format("%d", lsPhoto.size()));
            oRootView.findViewById(R.id.red_indicator).setVisibility(View.VISIBLE);
            oRootView.findViewById(R.id.gray_indicator).setVisibility(GONE);
        }

        for (int i = 0; i < lsPhoto.size(); i++) {
            ai_Photo oPhoto = lsPhoto.get(i);
            if (i == 4) {
                RoundTextView tvMore = new RoundTextView(oContext);
                tvMore.setSolidColor(oContext.getResources().getColor(R.color.gray_border_color));
                tvMore.setText("Show\nAll");
                tvMore.setCornerRadius(radius);
                tvMore.setTextColor(oContext.getResources().getColor(R.color.placeholderColor));
                tvMore.setTextSize(TypedValue.COMPLEX_UNIT_SP, 13);
                tvMore.setGravity(CENTER);
                tvMore.setLayoutParams(layoutParams);
                ollPhotoList.addView(tvMore);

                tvMore.setOnClickListener(view -> {
                    oContext.startActivity(if_DisplayAllPhotos.newIntent(oContext, iInsItemID, sValue, false));
                });

                break;
            }

            if (sSplit.contains("" + oPhoto.getId())) {
                FrameLayout frameLayout = new FrameLayout(oContext);
                FrameLayout.LayoutParams photoLayoutParams = new FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT
                );
                RoundedImageView oImageView = new RoundedImageView(oContext);
                oImageView.setLayoutParams(photoLayoutParams);
                frameLayout.addView(oImageView);

                ProgressBar progressBar = new ProgressBar(oContext);
                progressBar.setIndeterminate(true);
                progressBar.setVisibility(GONE);

                int barSize = (int) (width * 0.4);
                FrameLayout.LayoutParams barLayoutParams = new FrameLayout.LayoutParams(barSize, barSize);
                barLayoutParams.gravity = CENTER;
                progressBar.setLayoutParams(barLayoutParams);
                frameLayout.addView(progressBar);
                ollPhotoList.addView(frameLayout);

                LinearLayout.LayoutParams frameLayoutParams = new LinearLayout.LayoutParams(width, height);
                frameLayoutParams.setMargins(
                        CommonHelper.pxFromDp(oContext, 10), CommonHelper.pxFromDp(oContext, 5),
                        CommonHelper.pxFromDp(oContext, 0), CommonHelper.pxFromDp(oContext, 5));
                frameLayout.setLayoutParams(frameLayoutParams);

                oImageView.setTag(oPhoto.getId());
                CommonUI_Photo.showPhotoThumbFile(oContext, oPhoto.getId(), oImageView, progressBar);

                File imgFile = new File(oPhoto.getThumb());
                if (imgFile.exists()) {
                    oImageView.setOnClickListener(view -> {
                        long lInsItemID = oInsItem.getId();
                        oContext.startActivity(if_DisplayPhoto.newIntent(oContext,
                                Long.parseLong(view.getTag().toString()), iPosition, lInsItemID, sValue,
                                false, false));
                    });

                } else if (oPhoto.iSPhotoID > 0 && oPhoto.bUploaded) {
                    final ai_Photo fPhoto = oPhoto;
                    oImageView.setOnClickListener(view -> {
                        String[] options = {"Download Photo", "View Comments Only", "Download All Photos"};
                        new MaterialDialog.Builder(oContext)
                                .title("Photo In Cloud")
                                .items(options)
                                .itemsCallbackSingleChoice(-1, (dialog, view1, which, text) -> {
                                    if (which == 0) {
                                        downloadPhoto(oContext, fPhoto, null);
                                    } else if (which == 1) {
                                        long lInsItemID = oInsItem.getId();
                                        oContext.startActivity(if_DisplayPhoto.newIntent(oContext,
                                                fPhoto.getId(), iPosition, lInsItemID, sValue,
                                                false, false));
                                    } else if (which == 2) {
                                        Intent intent = new Intent(Constants.Broadcasts.sActionDownloadAllPhotos);
                                        LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
                                    }
                                    return true;
                                })
                                .negativeText(R.string.md_cancel_label)
                                .show();
                    });
                }
            }
        }

        LinearLayout pLayout = new LinearLayout(oContext);
        LinearLayout.LayoutParams oParams1 = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT);
        pLayout.setLayoutParams(oParams1);
        pLayout.addView(oRootView);
        oLayout.addView(pLayout);

        ViewUtils.setViewMargins(pLayout,
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_LEFT),
                CommonHelper.pxFromDp(oContext, 10),
                CommonHelper.pxFromDp(oContext, INSPECT_CELL_MARGIN_RIGHT), 0);

        InsItem_RenderCompulsoryMark(oContext, oInsItem, oPInsItem, sFV1, sValue, oLayout, -1);
    }

    public static class LoadPhotoImage extends AsyncTask<String, String, Bitmap> {

        public interface LoadPhotoImageListener {
            void onPhotoImageLoaded();
        }
        private MaterialDialog oDialog;
        private ai_Photo selectedPhoto;

        private final WeakReference<Context> oContext;
        private final LoadPhotoImageListener listener;
        LoadPhotoImage(Context ctx, ai_Photo photo, LoadPhotoImageListener listener) {
            oContext = new WeakReference<>(ctx);
            selectedPhoto = photo;
            this.listener = listener;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            oDialog = CommonUI.ShowMaterialProgressDialog(oContext.get(), "Message", "Downloading Data ...");
        }

        protected Bitmap doInBackground(String... args) {
            Bitmap bitmap = null;
            try {
                bitmap = BitmapFactory.decodeStream((InputStream)new URL(args[0]).getContent());
            } catch (Exception e) {
                e.printStackTrace();
            }
            return bitmap;
        }

        protected void onPostExecute(Bitmap bitmap) {
            oDialog.dismiss();

            if (bitmap != null) {
                O_FileName oFileName = O_FileName.getPhotoFileName();

                CommonHelper.SaveImage(oFileName.sFilePath, bitmap);
                CommonHelper.SaveThumb(oFileName.sThumbNail, bitmap);

                selectedPhoto.sFile = oFileName.sFilePath;
                selectedPhoto.sThumb = oFileName.sThumbNail;
                int iPhotoID = UpdatePhoto(selectedPhoto);

                bitmap.recycle();
                selectedPhoto = null;

                // Notify the loading is complete
                if (listener != null) listener.onPhotoImageLoaded();
                else {
                    Context ctx = oContext.get();
                    if (ctx != null) {
                        ctx.startActivity(if_DisplayPhoto.newIntent(ctx, iPhotoID));
                    }
                }
            }
        }
    }
}
