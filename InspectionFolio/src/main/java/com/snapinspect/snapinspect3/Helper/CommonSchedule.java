package com.snapinspect.snapinspect3.Helper;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.net.Uri;
import android.widget.Toast;
import com.afollestad.materialdialogs.MaterialDialog;
// Removed: import com.orm.query.Condition;
// Removed: import com.orm.query.Select;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activity.if_AssetDetails;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Ins_3rd;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Layout_3rd;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class CommonSchedule {
    private static final String option_ViewAsset = "View Asset";
    private static final String option_RouteToDestination = "Route To Destination";
    private static final String option_ViewNotes = "View Notes";
    private static final String option_EditIns = "Edit Inspection";
    private static final String option_StartIns = "Start Inspection";

    public static void viewSchedule(Activity activity, v_Schedule oSchedule) {
        String sText;
        if (oSchedule.isInsInProcess()) {
            sText = option_EditIns;
        } else if (oSchedule.isInsCompleted()) {
            sText = option_EditIns;
        } else {
            sText = option_StartIns;
        }

        List<String> options = new ArrayList<>();
        options.add(option_RouteToDestination);
        options.add(option_ViewNotes);

        if (oSchedule.hasREmail()) {
            String sEmail = CommonHelper.GetPreferenceString(activity, "sEmail");
            String rEmail = CommonJson.GetJsonKeyValue("REmail", oSchedule.sCustom1);
            if (StringUtils.isEmpty(sEmail) || !sEmail.equalsIgnoreCase(rEmail)) {
                if (!StringUtils.isEmpty(sText)) options.add(sText);
            }
        } else {
            if (!StringUtils.isEmpty(sText)) options.add(sText);
        }

        options.add(option_ViewAsset);
        showOptionsDialog(activity, oSchedule, options);
    }

    private static void showOptionsDialog(Activity activity, v_Schedule oSchedule, List<String> options) {
        new MaterialDialog.Builder(activity)
                .title(activity.getString(R.string.alert_title_action))
                .items(options)
                .itemsCallbackSingleChoice(-1,
                        (dialog, view, which, text) -> {
                            if (text.equals(option_RouteToDestination)) {
                                routeSchedule(activity, oSchedule);
                            } else if (text.equals(option_ViewNotes)) {
                                showNoteMessage(activity, oSchedule, "No notes for this schedule.");
                            } else if (text.equals(option_EditIns) || text.equals(option_StartIns)) {
                                startSchedule(activity, oSchedule);
                            } else if (text.equals(option_ViewAsset)) {
                                viewAsset(activity, oSchedule);
                            }
                            return true;
                        })
                //.autoDismiss(false)
                .negativeText(R.string.md_cancel_label)
                .onNegative((dialog, which) -> dialog.dismiss())
                .show();
    }

    private static void routeSchedule(Context context, v_Schedule oSchedule) {
        Geocoder coder = new Geocoder(context);
        List<Address> address;
        try {
            String strAddress = oSchedule.sAddress1 + "," + oSchedule.sAddress2;
            address = coder.getFromLocationName(strAddress, 5);
            if (address == null) {
                return;
            }
            Address location = address.get(0);
            location.getLatitude();
            location.getLongitude();

            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(
                    "http://maps.google.com/maps?" + "daddr=" + location.getLatitude() + "," + location.getLongitude())
            );
            context.startActivity(intent);
        } catch (Exception e) {
            //e.printStackTrace();
            Toast.makeText(context, "Invalid address. Route failed!", Toast.LENGTH_SHORT).show();
        }
    }

    private static void viewAsset(Context context, v_Schedule oSchedule) {
        context.startActivity(if_AssetDetails.newIntent(context, oSchedule.iSAssetID));
    }

    private static void showNoteMessage(Context context, v_Schedule oSchedule, String placeholder) {
        String sMessage = CommonJson.GetJsonKeyValue("sAddInfo", oSchedule.sCustom1);
        if (!StringUtils.isEmpty(sMessage)) sMessage = sMessage.trim();
        CommonHelper.ShowAlert("Notes", !StringUtils.isEmpty(sMessage) ? sMessage : placeholder, context);
    }

    public static void startSchedule(Context context, v_Schedule oSchedule) {
        if (context instanceof Activity && !CommonUI.bAppPermission((Activity) context)) return;

        ai_Inspection oInspection = CommonDB_Inspection.GetInspectionByScheduleID(oSchedule.iSScheduleID);
        if (oInspection != null && oInspection.getId() > 0){
            long lInspectionID = oInspection.getId();
            int iInspectionID = (int) lInspectionID;
            Intent oIntent = if_Ins_3rd.newIntent(context, iInspectionID, oInspection.sType, oInspection.sPTC);
            if (CommonHelper.isKioskMode(context) && (context instanceof Activity)) {
                ((Activity)context).startActivityForResult(oIntent, 2);
            } else {
                context.startActivity(oIntent);
            }
        }
        else {
            if (CommonValidate.bExternalInspection(oSchedule.sCustom1)) {
                try {
                    String sFileName = CommonHelper.sRequestInspection_FileName(oSchedule.sCustom1);
                    if (sFileName != null) {

                        boolean bPass = true;
                        ai_InsType oInsType = CommonRequestInspection.LoadInsType(sFileName);

                        if (oInsType == null) {
                            return;
                        }
                        List<ai_AssetLayout> lsAssetLayout = CommonRequestInspection.LoadAssetLayout(sFileName);
                        if (lsAssetLayout == null || lsAssetLayout.size() == 0) {
                            List<ai_Layout> lsLayoutItem = CommonRequestInspection.LoadLayout(0, sFileName);

                            for (int i = 0; i < lsLayoutItem.size(); i++) {
                                String sConfig = oSchedule.sType.equals("F") ? lsLayoutItem.get(i).sFConfig : lsLayoutItem.get(i).sSConfig;

                                if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null) {
                                    bPass = false;
                                    break;
                                }
                            }
                            if (bPass) {
                                IF_CreateInspection_ReqIns oCreateInspection = new IF_CreateInspection_ReqIns();
                                Date oNow = new Date();
                                int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(context, new ArrayList<>(), oInsType,
                                        oSchedule.iSAssetID, "", CommonHelper.sDateToString(oNow), "", oSchedule.sAddress1,
                                        oSchedule.sAddress2, oSchedule.iSScheduleID, oSchedule.sCustom1, sFileName, oSchedule.getScheduleDate());
                                CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                                goToInspection(context, iInspectionID);
                                //   CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                            } else {
                                context.startActivity(if_Layout_3rd.newIntent(context, oSchedule.iSAssetID,
                                        oInsType.iSInsTypeID, oSchedule.sAddress1, oSchedule.sAddress2,
                                        oSchedule.iSScheduleID, oSchedule.sCustom1, oSchedule.getScheduleDate()));
                            }
                        } else {
                            IF_CreateInspection_ReqIns oCreateInspection = new IF_CreateInspection_ReqIns();
                            int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(context, new ArrayList<>(lsAssetLayout), oInsType,
                                    oSchedule.iSAssetID, "", CommonHelper.sDateToString(null), "",
                                    oSchedule.sAddress1, oSchedule.sAddress2, oSchedule.iSScheduleID, oSchedule.sCustom1, sFileName, oSchedule.getScheduleDate());
                            CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                            goToInspection(context, iInspectionID);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                List<ai_InsType> lsInsType = ai_InsType.listAll(ai_InsType.class);
                ai_InsType oInsType = null;
                if (lsInsType != null && lsInsType.size() > 0) {
                    for (int i = 0; i < lsInsType.size(); i++) {
                        if (oSchedule.iSInsTypeID == lsInsType.get(i).iSInsTypeID) {
                            oInsType = lsInsType.get(i);
                            break;
                        }
                    }

                }
                if (oInsType == null || oInsType.iSInsTypeID == 0) {
                    CommonHelper.ShowAlert("Message", "The inspection type is not available, please sync the app and try again.", context);
                    return;
                }

                final ai_InsType finalInsType = oInsType;

                CommonInspection.fetchAssetLayoutsIfNeed(context, oSchedule.iSAssetID, oInsType.iSInsTypeID, assetLayouts -> {
                    if (assetLayouts == null || assetLayouts.isEmpty()) {
                        startNewInspection(context, oSchedule, finalInsType);
                    } else { // have layouts
                        new if_Layout_3rd.AsyncStartInspection(
                                context, oSchedule.iSAssetID, assetLayouts, finalInsType,
                                oSchedule.sCustom1, oSchedule.iSScheduleID,
                                oSchedule.sAddress1, oSchedule.sAddress2, 0,
                                oSchedule.getScheduleDate()
                        ).execute();
                    }
                });
            }
        }
    }

    private static void startNewInspection(Context context, v_Schedule oSchedule, ai_InsType oInsType) {
        boolean bPass = true;
        List<ai_AssetLayout> lsAssetLayout = CommonDB.GetAssetLayouts(oSchedule.iSAssetID, oSchedule.sPTC, context);
        if (lsAssetLayout == null || lsAssetLayout.size() == 0) {
            List<ai_Layout> lsLayoutItem = CommonDB.GetParentLayout(oSchedule.sPTC);

            for (int i = 0; i < lsLayoutItem.size(); i++) {
                String sConfig = oSchedule.sType.equals("F") ? lsLayoutItem.get(i).sFConfig : lsLayoutItem.get(i).sSConfig;

                if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null) {
                    bPass = false;
                    break;
                }
            }
            if (bPass) {
                IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                Date oNow = new Date();
                int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(context, new ArrayList<>(), oInsType,
                        oSchedule.iSAssetID, "", CommonHelper.sDateToString(oNow), "",
                        oSchedule.sAddress1, oSchedule.sAddress2, oSchedule.iSScheduleID, oSchedule.getScheduleDate());
                CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                goToInspection(context, iInspectionID);

                // CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
            } else {
                context.startActivity(if_Layout_3rd.newIntent(context, oSchedule.iSAssetID,
                        oInsType.iSInsTypeID, oSchedule.sAddress1, oSchedule.sAddress2,
                        oSchedule.iSScheduleID, "", oSchedule.getScheduleDate()));
            }
        } else {
            List<ai_CheckList> lsCheckList = ai_CheckList.find(ai_CheckList.class, "s_PTC = ?", oInsType.sPTC);
            ai_CheckList oChecklist = null;
            if (lsCheckList != null && lsCheckList.size() == 1) {
                oChecklist = lsCheckList.get(0);
            }
            boolean bSameVer = true;
            for (ai_AssetLayout oAL : lsAssetLayout) {
                if ((oAL.sFieldThree != null) && (!oAL.sFieldThree.equals("" + oChecklist.iLayoutVerID))) {
                    bSameVer = false;
                    break;
                }
            }

            if (bSameVer) {
                IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(context, new ArrayList<>(lsAssetLayout), oInsType,
                        oSchedule.iSAssetID, "", CommonHelper.sDateToString(null), "",
                        oSchedule.sAddress1, oSchedule.sAddress2, oSchedule.iSScheduleID, oSchedule.getScheduleDate());
                CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                goToInspection(context, iInspectionID);

            } else {
                for (ai_AssetLayout oAL : lsAssetLayout) oAL.delete();
                context.startActivity(if_Layout_3rd.newIntent(context, oSchedule.iSAssetID,
                        oInsType.iSInsTypeID, oSchedule.sAddress1, oSchedule.sAddress2,
                        oSchedule.iSScheduleID, "", oSchedule.getScheduleDate()));
            }
        }
    }

    public static void goToInspection(Context context, int iInspectionID) {
        Intent oIntent = if_Ins_3rd.newIntent(context, iInspectionID);
        if (CommonHelper.isKioskMode(context) && (context instanceof Activity)) {
            ((Activity)context).startActivityForResult(oIntent, 2);
        } else {
            context.startActivity(oIntent);
        }
        Activity parent = (Activity)context;
        if (parent instanceof if_Layout_3rd) parent.finish();
    }
}
