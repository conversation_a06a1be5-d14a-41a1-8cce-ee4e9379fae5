package com.snapinspect.snapinspect3.Helper;

import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.app.Application;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.drawable.GradientDrawable;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.BaseAdapter;
import android.widget.Button;
import android.widget.EditText;
import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.BuildConfig;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_JsonStatus;
import com.snapinspect.snapinspect3.IF_Object.ai_Schedule;
import com.snapinspect.snapinspect3.IF_Object.ai_User;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.app.App;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import io.intercom.android.sdk.Intercom;
import io.intercom.android.sdk.IntercomError;
import io.intercom.android.sdk.IntercomStatusCallback;
import io.intercom.android.sdk.UserAttributes;
import io.intercom.android.sdk.identity.Registration;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.*;

/**
 * Created by TerrySun on 7/03/14.
 */

public final class CommonHelper {
    // Private constructor to prevent instantiation
    private CommonHelper() {
        throw new UnsupportedOperationException("This class is not instantiable");
    }

    public static final String sFileRoot = FileUtils.getRootPath();
    public static int iAPIVersion = Build.VERSION.SDK_INT;
    public static void DebugLog(String tag, String sMessage){
        Log.v("DebugLog." + tag, sMessage);
    }

    public static int dpFromPx(final Context context, final int px) {
        return (int)(px / context.getResources().getDisplayMetrics().density);
    }

    public static int pxFromDp(final Context context, final int dp) {
        return (int)(dp * context.getResources().getDisplayMetrics().density);
    }

    public static int pxFromDp(final Context context, final float dp) {
        return (int)(dp * context.getResources().getDisplayMetrics().density);
    }

    public static Button CreateButton(Context oContext, int iCornerRadius, String[] arrTitle, String[] arrValue, String[] arrColor, int iPosition) {
        Button oButton = new Button(oContext);
        oButton.setTextSize(20);
        oButton.setPadding(0, 0, 0, 0);
        ChangeButton(oContext, oButton, iCornerRadius, arrTitle[iPosition],
                (arrValue != null && arrValue.length > iPosition) ? arrValue[iPosition] : "",
                (arrColor != null && arrColor.length > iPosition) ? arrColor[iPosition] : "");
        return oButton;
    }

    public static void ChangeButton(Context oContext, Button oButton, int iCornerRadius, String arrTitle, String arrValue, String sColor) {

        if (StringUtils.isEmpty(arrValue)) arrValue = "A";

        oButton.setTextColor(Color.WHITE);
        try {
            oButton.setText(arrTitle.trim().substring(0, 1).toUpperCase());
        } catch (Exception ignored) {
        }

        int iColor = 0;
        Resources resources = oContext.getResources();
        if (arrValue.equalsIgnoreCase("Y")) {
            try {
                if (!StringUtils.isEmpty(sColor)) {
                    iColor = Color.parseColor(sColor);
                } else {
                    iColor = resources.getColor(R.color.btn_y_background);
                }
            }catch(Exception exxx){
                iColor = resources.getColor(R.color.btn_y_background);
            }
        } else if (arrValue.equalsIgnoreCase("N")) {
            iColor = resources.getColor(R.color.btn_n_background);
        } else if (arrValue.equalsIgnoreCase("A")) {
            iColor = resources.getColor(R.color.btn_a_background);
        }

        GradientDrawable background = new GradientDrawable();
        background.setColor(iColor);
        background.setCornerRadius(CommonHelper.pxFromDp(oContext, iCornerRadius));
        oButton.setBackgroundDrawable(background);
    }

    public static void SaveThumb(String sFileName, Bitmap oBitmap){
        try {
            FileOutputStream stream1 = new FileOutputStream(sFileName);

            Matrix thm_matrix = new Matrix();
            // RESIZE THE BIT MAP
            float thum_scaleWidth = (float) 0.125;
            float thum_scaleHeight = (float) 0.125;
            thm_matrix.postScale(thum_scaleWidth, thum_scaleHeight);
            Bitmap.createBitmap(oBitmap, 0, 0, oBitmap.getWidth(), oBitmap.getHeight(), thm_matrix, false).compress(Bitmap.CompressFormat.JPEG, 80, stream1);
            stream1.flush();
            stream1.close();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }
    public static void SaveThumb_Height(String sFileName, Bitmap oBitmap, int iHeight){
        try {
            FileOutputStream stream1 = new FileOutputStream(sFileName);
           // int iHeight = oBitmap.getHeight();
            float fRatio = ((float)iHeight)/oBitmap.getHeight();
            Matrix thm_matrix = new Matrix();
            // RESIZE THE BIT MAP
            float thum_scaleWidth = fRatio;
            float thum_scaleHeight = fRatio;
            thm_matrix.postScale(thum_scaleWidth, thum_scaleHeight);
            Bitmap.createBitmap(oBitmap, 0, 0, oBitmap.getWidth(), oBitmap.getHeight(), thm_matrix, false).compress(Bitmap.CompressFormat.JPEG, 80, stream1);
            stream1.flush();
            stream1.close();
        }catch(Exception ex){

        }
    }
    public static String EscapeString(String sValue){
        return sValue.replaceAll("'","''");
    }

    public static void SaveImage(String sFileName, Bitmap oBitmap) {
        SaveImage(sFileName, oBitmap, 90);
    }

    public static void SaveImage(String sFileName, Bitmap oBitmap, int quality) {
        if (oBitmap == null) return;
        try {
            FileOutputStream outStream = new FileOutputStream(sFileName);
            oBitmap.compress(Bitmap.CompressFormat.JPEG, quality, outStream);
            outStream.flush();
            outStream.close();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }
    public static String GetStatusCode(String sStatus){
        try {
            if (sStatus.contains(" ")) {
                int iIndex = sStatus.indexOf(" ");
                String sIcon = sStatus.substring(iIndex + 1, iIndex + 2);
                return sStatus.substring(0, 1).toUpperCase() + sIcon.toUpperCase();
            } else if (!sStatus.contains(" ")) {
                return sStatus.substring(0, 1).toUpperCase() + sStatus.substring(1, 2).toLowerCase();
            } else {
                return sStatus.substring(0, 1).toUpperCase();
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return "";
    }

    public static void  ShowAlert(String sTitle, String sMessage, Context oContext){
        CommonUI.GetAlertBuilder(sTitle, sMessage, oContext, false, true).show();
    }

    public static void IntercomInit(Application application){
        try{
            //this.gedtApplication()

          //  Analytics analytics = new Analytics.Builder(oContext, "JCNjpqCRpq9xlaTfW294mKjaSurL9ptd")
          //          .trackApplicationLifecycleEvents() // Enable this to record certain application events automatically!
          //          .recordScreenViews() // Enable this to record screen views automatically!
           //         .build();

// Set the initialized instance as a globally accessible instance.
           // Analytics.setSingletonInstance(analytics);

            Intercom.initialize(application, "android_sdk-1582f3482758f4e493077ff0d8477aadbcb85773", "y508wqoz");
            if (CommonHelper.GetPreferenceString(application, "sHash") != null) {
                Intercom.client().setUserHash(CommonHelper.GetPreferenceString(application, "sHash"));
            }

        }catch(Exception ex){

        }
    }
    public static List<ai_JsonStatus> GetInspectionStatus(Context oContext) {
        return GetStatus(oContext, ai_JsonStatus.StatusType.INSPECTION);
    }

    private static List<ai_JsonStatus> GetStatus(Context oContext, ai_JsonStatus.StatusType oType) {
        List<ai_JsonStatus> lsStatus = new ArrayList<>();
        try {
            String sStatus = CommonHelper.GetPreferenceString(oContext, "jsonStatus");
            JSONArray oTempStatus = new JSONArray(sStatus);
            for (int i = 0; i < oTempStatus.length(); i++) {
                try {
                    JSONObject oObject = oTempStatus.getJSONObject(i);
                    ai_JsonStatus oStatus = new ai_JsonStatus(oObject);
                    if (oStatus.sType == oType && !oStatus.bDeleted) {
                        lsStatus.add(oStatus);
                    }
                } catch (Exception eeeee) {
                }
            }
        } catch (Exception ex) {

        }
        return lsStatus;
    }

    public static List<ai_JsonStatus> GetTaskStatus(Context oContext) {
        return GetStatus(oContext, ai_JsonStatus.StatusType.TASK);
    }

    public static List<ai_JsonStatus> GetAssetStatus(Context oContext) {
        return GetStatus(oContext, ai_JsonStatus.StatusType.ASSET);
    }

    public static void trackEvent(Context context, String eventName) {
        trackEvent(context, eventName, null);
    }

    public static void trackEvent(Context context, String sType, String key, String value) {
        HashMap<String, Object> params = new HashMap<>();
        params.put(key, value);
        trackEvent(context, sType, params);
    }

    public static void trackEvent(Context oContext, String sType, Map<String, Object> oParam) {
        if (BuildConfig.DEBUG) {
            Log.d("TrackEvent", sType + (oParam != null && !oParam.isEmpty() ? " " + oParam : ""));
            return;
        }
        try {
            //  Analytics analytics = new Analytics.Builder(oContext, "JCNjpqCRpq9xlaTfW294mKjaSurL9ptd")
            //        .trackApplicationLifecycleEvents()
            //         .build();

            //  Analytics.with(oContext).track("Product Viewed", new Properties().putValue("name", "Moto 360"));

            App app = (App) oContext.getApplicationContext();
            if (app == null) return;
            if (oParam == null) {
                // Intercom.client().logEvent(sType);
                app.dataDogLogger.i(sType);
            } else {
                // Intercom.client().logEvent(sType, oParam);
                app.dataDogLogger.i(sType, null, oParam);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.IntercomEvent", ex, null);
        }
    }
    public static void IntercomLogin(Context oContext){
        try {
            String sCustomerID = CommonHelper.GetPreferenceString(oContext, "iCustomerID");
            String sSavedEmail = CommonHelper.GetPreferenceString(oContext, "sEmail");
            if (sCustomerID != null && !sCustomerID.isEmpty()) {
                Registration registration = new Registration();
                registration.withUserId(sCustomerID);
                UserAttributes.Builder userAttributes = new UserAttributes.Builder();
                userAttributes.withUserId(sCustomerID);
                if (!StringUtils.isEmpty(sSavedEmail)) {
                    userAttributes.withEmail(sSavedEmail);
                }
                registration.withUserAttributes(userAttributes.build());
                Intercom.client().loginIdentifiedUser(registration, new IntercomStatusCallback() {
                    @Override
                    public void onSuccess() {
                        CommonHelper.trackEvent(oContext, "IntercomLogin - success");
                    }

                    @Override
                    public void onFailure(@NotNull IntercomError intercomError) {
                        CommonHelper.trackEvent(oContext, "IntercomLogin - failed");
                    }
                });
               /* Map<String, Object> userMap = new HashMap<String, Object>();
                userMap.put("name", "Bob");
                userMap.put("email", "<EMAIL>");
                Intercom.client().updateUser(userMap);*/
            }

            //Analytics.with(oContext).identify(sCustomerID, new Traits().putEmail(sSavedEmail), null);

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.IntercomLogin", ex, null);
        }
    }
    public static void IntercomLogout(){
        try {



            Intercom.client().reset();

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.IntercomLogout", ex, null);
        }
    }
    public static void IntercomLogout(Context context){
        try {

           // Analytics.with(context).reset();

            Intercom.client().reset();

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.IntercomLogout", ex, null);
        }
    }

    public static void ValidateRootFolderExist(){
        File oFile = new File(CommonHelper.sFileRoot);
        if ((!oFile.exists()) || (!oFile.isDirectory())) {
            oFile.mkdir();
        }
    }
    public static void ValidateTempFolderExist(){
        ValidateRootFolderExist();
        File oFileTemp = new File(CommonHelper.sFileRoot + Constants.Paths.tempFolder);
        if ((!oFileTemp.exists()) || (!oFileTemp.isDirectory())) {
            oFileTemp.mkdir();
        }
    }

    /**
     * This method is used to validate if the blueprint folder exists.
     * If the folder does not exist, it will create the folder.
     */
    public static void validateFloorPlanFolderExist() {
        ValidateRootFolderExist();
        File oFileTemp = new File(CommonHelper.sFileRoot + Constants.Paths.FLOOR_PLAN_FOLDER);
        if ((!oFileTemp.exists()) || (!oFileTemp.isDirectory())) {
            oFileTemp.mkdir();
        }
    }

    public static String GetFileSaveFileName(){
        String sRandomString = GetTimeString();
        return sFileRoot + "//" + "f_" + sRandomString + ".jpg";
    }
    public static String GenerateDebugFileName(String sFilePrefix){
        String sRandomString = GetTimeString();
        String sFilePath = sFileRoot + "//";
        //  if ((new File(sFilePath)).exists()) {
        //      (new File(sFilePath)).mkdir();
        //  }
       // O_FileName oFileName = new O_FileName(sFilePath + "image_" + sRandomString + "_t.jpg", sFilePath + "image_" + sRandomString + ".jpg");
        return sFilePath + ((sFilePrefix == null || sFilePrefix.equalsIgnoreCase("")) ? "debug_" : (sFilePrefix + "_")) + sRandomString + ".txt";
    }

    public static void showSoftKeyboard(Activity activity, EditText editText) {
        try {
            editText.requestFocus();
            InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
            imm.showSoftInput(editText, InputMethodManager.SHOW_IMPLICIT);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.showSoftKeyboard", ex, null);
        }
    }

    public static void hideSoftKeyboard(Activity activity) {
        try {
            View view = activity.findViewById(android.R.id.content);
            if (view != null) {
                InputMethodManager imm = (InputMethodManager) activity.getSystemService(Context.INPUT_METHOD_SERVICE);
                imm.hideSoftInputFromWindow(view.getWindowToken(), 0);
            }
        } catch(Exception ex) {
            //ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.hideSoftKeyboard", ex, activity);
        }
    }

    public static String GetTimeString() {
        Calendar currentCal = Calendar.getInstance();
        return new SimpleDateFormat("yyyyMMddhhmmssSSS")
                .format(currentCal.getTime());
    }

    public static int GetFileLength(String sPath) {
        try {
            File oFile = new File(sPath);
            return (int)(oFile.length()/1000);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.GetFileLength", ex, null);
            return 0;
        }
    }

    public static String GetPreferenceString_Advance(Context ctxt, String sKey, String sPref){
        //ai_BugHandler.ai_Handler_Exception(new Exception());
        //Log.v("pass new inspection", "test");
        try {
            //SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(ctxt);
           // Context CCX = ctxt.getApplicationContext();
            SharedPreferences preferences = ctxt.getSharedPreferences(sPref, Context.MODE_PRIVATE);
            return preferences.getString(sKey, null);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.GetPreferenceString_Advance", ex, ctxt);
        }
        return null;
    }
    public static void SavePreference_Advance(Context ctxt, String sKey, String sValue, String sPref){
        try {
            //Context CCX = ctxt.getApplicationContext();
            SharedPreferences preferences = ctxt.getSharedPreferences(sPref, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = preferences.edit();
            editor.putString(sKey, sValue);
            editor.apply();
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.SavePreference_Advance", ex, ctxt);
        }
    }
    public static boolean isKioskMode(Context ctx) {
        return CommonHelper.GetPreferenceBoolean(ctx, Constants.Settings.bKiosk);
    }

    public static boolean bRequestInspection(String sCustom1) {
        String sRequestInspection = CommonJson.GetJsonKeyValue("RCode", sCustom1);
        try {
            if (sRequestInspection != null && sRequestInspection.length() > 0) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    public static String sRequestInspection_FileName(String sCustom1) {
        String sFileName = CommonJson.GetJsonKeyValue("RIP", sCustom1);
        try {
            if (sFileName != null && !sFileName.equals("")) {
                if (CommonHelper.bFileExist(sFileName)) {
                    return sFileName;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean GetPreferenceBoolean(Context ctxt, String sKey){
        try {
            String sValue = GetPreferenceString(ctxt, sKey);
            return getBoolean(sValue);
        }catch(Exception ex){

        }
        return false;
    }

    public static void SavePreferenceBoolean(Context ctxt, String sKey, boolean bValue){
        SavePreference(ctxt, sKey, String.valueOf(bValue));
    }

    public static String GetPreferenceString(Context ctxt, String sKey, String defaultValue) {
        String value = GetPreferenceString(ctxt, sKey);
        return !StringUtils.isEmpty(value) ? value : defaultValue;
    }

    public static String GetPreferenceString(Context ctxt, String sKey){
        //ai_BugHandler.ai_Handler_Exception(new Exception());
        //Log.v("pass new inspection", "test");
        try {
            //SharedPreferences preferences = PreferenceManager.getDefaultSharedPreferences(ctxt);
            Context CCX = ctxt.getApplicationContext();
            SharedPreferences preferences = CCX.getSharedPreferences(Constants.Paths.prefs, Context.MODE_PRIVATE);
            return preferences.getString(sKey, null);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.GetPreferenceString - Key:" + sKey, ex, ctxt);
        }
        return null;
    }

    public static int GetPreferenceInt(Context context, String sKey) {
        try {
            String sValue = GetPreferenceString(context, sKey);
            return getInt(sValue);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.GetPreferenceInt", ex, context);
        }
        return 0;
    }

    public static void SavePreference(Context ctxt, String sKey, String sValue){
        try {
            Context CCX = ctxt.getApplicationContext();
            SharedPreferences preferences = CCX.getSharedPreferences(Constants.Paths.prefs, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = preferences.edit();
            editor.putString(sKey, sValue);
            editor.apply();
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.SavePreference", ex, ctxt);
        }
    }

    public static Map<String, ?> getAllPreferences(Context ctx) {
        return ctx.getApplicationContext().getSharedPreferences(Constants.Paths.prefs, Context.MODE_PRIVATE).getAll();
    }

    public static void DeleteFile(String imagePath) {
        try {
            File f = new File(imagePath);

            if (f.exists())
                f.delete();
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.DeleteFile",  e, null);
        }
    }
    public static boolean bFileExist(String imagePath) {
        try {
            File f = new File(imagePath);
            return f.exists();
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.bFileExist", e, null);
            return false;
        }
    }
    public static boolean IsEmail(String sEmail){
        if (sEmail == null) {
            return false;
        } else {
            return android.util.Patterns.EMAIL_ADDRESS.matcher(sEmail).matches();
        }
        /*Pattern p = Pattern.compile("^([a-zA-Z0-9_.-])+@([a-zA-Z0-9_.-])+\\.([a-zA-Z])+([a-zA-Z])+");
        Matcher m = p.matcher(sEmail);
        return m.matches();*/
    }
    public static String GetFilter(String sAddress1, String sAddress2){
        return sAddress1.toLowerCase().replaceAll(" ", "") + sAddress2.toLowerCase().replaceAll(" ", "");
    }
    public static long GetUnixTime(String sDate) {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy HH:mm", Locale.getDefault());
            Date date = sdf.parse(sDate);
            return date.getTime();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.GetUnixTime", ex, null);
            return 0;
        }
    }

    public static String GetDefaultChkValue(int iCount){
        try {
            String sValue = "";
            for (int i = 0; i < iCount; i++) {
                sValue = sValue + "A|";
            }
            if (sValue.endsWith("|")) {
                sValue = sValue.substring(0, sValue.length() - 1);
            }
            return sValue;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.GetDefaultChkValue",  ex, null);
        }
        return "";
    }

    public static String GetChkValue(String[] arrValue){
        try {
            String sValue = "";
            for (int i = 0; i < arrValue.length; i++) {
                sValue = sValue + arrValue[i] + "|";
            }
            if (sValue.endsWith("|")) {
                sValue = sValue.substring(0, sValue.length() - 1);
            }
            return sValue;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.GetChkValue",  ex, null);
        }
        return "";
    }
    public static String GetRatingValue_Text(String sValue){
        if (sValue.equalsIgnoreCase("Y")){
            return "Yes";
        }
        else         if (sValue.equalsIgnoreCase("N")){
            return "No";
        }
        else         if (sValue.equalsIgnoreCase("A")){
            return "NA";
        }
        else {
            return "";
        }
    }
    public static String sDateToString(Date oDate){
        try {
            if (oDate == null) {
                Calendar currentCal = Calendar.getInstance();
                return Constants.dateFormat.format(currentCal.getTime());
            } else {
                return Constants.dateFormat.format(oDate);
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.sDateToString",  ex, null);
        }
        return "";
    }

    public static String sDateToStringFormat(Date oDate){
        if (oDate == null){
            Calendar currentCal = Calendar.getInstance();
            return new SimpleDateFormat("MMM dd, yyyy HH:mm").format(currentCal.getTime());
        }
        else{
            return new SimpleDateFormat("MMM dd, yyyy HH:mm").format(oDate);
        }
    }

    public static String sDateToStringFormat(Date oDate, String format) {
        if (oDate == null) {
            Calendar currentCal = Calendar.getInstance();
            return new SimpleDateFormat(format, Locale.US).format(currentCal.getTime());
        } else {
            return new SimpleDateFormat(format, Locale.US).format(oDate);
        }
    }

    public static String sAttachPhoto(String sValue, String iPhotoID) {
        if (sValue == null || sValue.trim().length() == 0) {
            return iPhotoID;
        }
        return sValue.trim() + "," + iPhotoID;

    }
    public static String sRemovePhoto(String sValue, String iPhotoID) {
       // 1,2,3,4,5
       // 3
       // 3,4,5
       // 4,5,3
       // 4,3
        try {
            if (sValue == null || sValue.trim().length() == 0) {
                return "";
            }
            sValue = sValue.trim();
            if (sValue.contains("," + iPhotoID + ",")) {
                return sValue.replace("," + iPhotoID + ",", ",");
            } else if (sValue.startsWith(iPhotoID)) {
                if (sValue.startsWith(iPhotoID + ",")) {
                    return sValue.replace(iPhotoID + ",", "");
                }
                return sValue.replace(iPhotoID, "");
            } else if (sValue.endsWith(iPhotoID)) {
                return sValue.replace("," + iPhotoID, "");
            }
            return sValue;
        }
        catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.sRemovePhoto",  ex, null);
        }
        return sValue;
    }
    public static void SetValue(int iPosition, ai_InsItem oInsItem, String sValue){
        if (iPosition == 1){
            oInsItem.sValueOne = sValue;
        }
        else if (iPosition == 2){
            oInsItem.sValueTwo = sValue;
        }
        else if (iPosition == 3){
            oInsItem.sValueThree = sValue;
        }
        else if (iPosition == 4){
            oInsItem.sValueFour = sValue;
        }
        else if (iPosition == 5){
            oInsItem.sValueFive = sValue;
        }
        else if (iPosition == 6){
            oInsItem.sValueSix = sValue;
        }

    }
    public static String GetValue(int iPosition, ai_InsItem oInsItem){
        String sValue = "";
        if (iPosition == 1){
            sValue = oInsItem.sValueOne;
        }
        else if (iPosition == 2){
            sValue = oInsItem.sValueTwo;
        }
        else if (iPosition == 3){
            sValue = oInsItem.sValueThree;
        }
        else if (iPosition == 4){
            sValue = oInsItem.sValueFour;
        }
        else if (iPosition == 5){
            sValue = oInsItem.sValueFive;
        }
        else if (iPosition == 6){
            sValue = oInsItem.sValueSix;
        }
        return sValue;
    }
    public static String GetConfig(int iPosition, ai_InsItem oInsItem, ai_InsItem oPInsItem){
        String sConfig = "";
        if (iPosition == 1){
            sConfig = (oInsItem.sQType.equals("C")&& oPInsItem != null) ? oPInsItem.sConfigOne.trim() : oInsItem.sConfigOne.trim();
        }
        else if (iPosition == 2){
            sConfig = (oInsItem.sQType.equals("C")&& oPInsItem != null) ? oPInsItem.sConfigTwo.trim() : oInsItem.sConfigTwo.trim();
        }
        else if (iPosition == 3){
            sConfig = (oInsItem.sQType.equals("C")&& oPInsItem != null) ? oPInsItem.sConfigThree.trim() : oInsItem.sConfigThree.trim();
        }
        else if (iPosition == 4){
            sConfig = (oInsItem.sQType.equals("C") && oPInsItem != null)  ? oPInsItem.sConfigFour.trim() : oInsItem.sConfigFour.trim();
        }
        else if (iPosition == 5){
            sConfig = (oInsItem.sQType.equals("C")&& oPInsItem != null)  ? oPInsItem.sConfigFive.trim() : oInsItem.sConfigFive.trim();
        }
        else if (iPosition == 6){
            sConfig = (oInsItem.sQType.equals("C")&& oPInsItem != null) ? oPInsItem.sConfigSix.trim() : oInsItem.sConfigSix.trim();
        }
        return sConfig;
    }

    public static String GetDefaultCheckboxValue(String sConfig){
        int type = CommonInsItem.getControlType(sConfig);
        if (type == SI_CONTROL_TYPE_CHK || type == SI_CONTROL_TYPE_SCHK || type == SI_CONTROL_TYPE_MCHK) {
            String[] sArray = CommonInsItem.getCHKLabelItems(sConfig);
            try{

                String sChecks = CommonJson.GetJsonKeyValue("_sC", sConfig);
                if (!StringUtils.isEmpty(sChecks)) {
                    if (sChecks.split("\\|").length == sArray.length) return sChecks;
                }}
            catch(Exception eccc) {

            }

            return CommonHelper.GetDefaultChkValue(sArray.length);
        }

        if (type == SI_CONTROL_TYPE_LST) {
            String sContent = CommonJson.GetJsonKeyValue("Content", sConfig);
            if (StringUtils.isEmpty(sContent)) return "";
            String sQT = CommonJson.GetJsonKeyValue("sQT", sConfig);
            String[] sValues = CommonJson.GetStringsFromJsonArray(sContent);
            Arrays.fill(sValues, "TX".equals(sQT) ? "" : "0");
            JSONArray jsonArray = new JSONArray(Arrays.asList(sValues));
            return jsonArray.toString();
        }

        return "";
    }

    public static void DeleteScheduleInfo(int iSScheduleID) {
        if (iSScheduleID > 0){
            List<ai_Schedule> lsSchedule = ai_Schedule.find(ai_Schedule.class, "I_S_SCHEDULE_ID=?", "" + iSScheduleID);
            if (lsSchedule != null && lsSchedule.size() == 1){
                lsSchedule.get(0).delete();
            }
        }
    }

    public static void ScheduleAppendCustomOne(int iSScheduleID, String sKey, String sValue) {
        if (iSScheduleID > 0){
            List<ai_Schedule> lsSchedule = ai_Schedule.find(ai_Schedule.class, "I_S_SCHEDULE_ID=?", "" + iSScheduleID);
            if (lsSchedule != null && lsSchedule.size() == 1){
                ai_Schedule oSchedule = lsSchedule.get(0);
                oSchedule.sCustomOne = CommonJson.AddJsonKeyValue(oSchedule.sCustomOne, sKey, sValue);
                CommonDB.saveSchedule(oSchedule);
            }
        }
    }
    public static String GetDeviceInfo() {
        try {
            return "Model:" + Build.MODEL + " Version:" + Build.VERSION.SDK_INT + " (" + Build.VERSION.RELEASE + ")" + " (" + BuildConfig.VERSION_NAME +  ")";
        }catch(Exception ex) {
            return "Exception";
        }
    }
    public static void SaveDebugFile(String sFilePrefix,String sContent){
        String sFileName = CommonHelper.GenerateDebugFileName(sFilePrefix);
        try {
            File file = new File(sFileName);
            BufferedWriter out = new BufferedWriter(new FileWriter(file, true), 1024);
            out.write(sContent);
            out.flush();
            out.close();
        }catch (Exception ex){

        }
    }
    public static boolean bIns1DefaultAdd(String sConfig){
        try{
            if (sConfig != null && CommonJson.GetJsonKeyValue("ADD", sConfig) != null){
                return true;
            }
        }catch(Exception ex){

        }
        return false;

    }

    public static String getConfigById(int position, ai_InsItem oInsItem) {
        ai_InsItem oPInsItem = ai_InsItem.findById(ai_InsItem.class, oInsItem.iPInsItemID);
        return CommonHelper.GetConfig(position, oInsItem, oPInsItem);
    }

    public static String getValueById(int position, ai_InsItem oInsItem) {
        String value = "";
        switch (position) {
            case 1:
                value = oInsItem.sValueOne;
                break;
            case 2:
                value = oInsItem.sValueTwo;
                break;
            case 3:
                value = oInsItem.sValueThree;
                break;
            case 4:
                value = oInsItem.sValueFour;
                break;
            case 5:
                value = oInsItem.sValueFive;
                break;
            case 6:
                value = oInsItem.sValueSix;
                break;
        }

        return value;
    }
    public static void SaveStringToFilesDir(String sFileName, String sContent){
        try {
            String filesDir = App.getContext().getFilesDir().getAbsolutePath();
            File file = new File(filesDir, sFileName);
            BufferedWriter out = new BufferedWriter(new FileWriter(file), 1024);
            out.write(sContent);
            out.flush();
            out.close();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonHelper.saveStringToFilesDir", ex, null);
        }
    }

    public static String[] GetRatingArray(String sConfig){
        try{
            int iLength = sConfig.length();
            String sTemp = sConfig.substring(sConfig.indexOf("(") + 1, iLength - 1);
            return sTemp.split("\\[\\|\\]");
        }catch(Exception ex){

        }
        return new String[0];
    }
    public static String GetRatingName_WithPosition(String sConfig, int iPosition){
        try{
            String[] arrValue = GetRatingArray(sConfig);
            return arrValue[iPosition];
        }catch(Exception ex){

        }
        return "";
    }
    public static void BulkSetRating_V2(final List<ai_InsItem> lsInsItem, final int iControlNumber,
                                     final int iRatingNumber, final ai_InsItem oInsItem, final ai_InsItem oPInsItem,
                                     final Context oContext, final BaseAdapter oAdapter, boolean bShowNo){

        final String sConfig= CommonHelper.GetConfig(iControlNumber, oInsItem, oPInsItem);
        if (sConfig == null || sConfig.trim().equalsIgnoreCase("")){
            return;
        }
        //String sTempValue_Sample = CommonHelper.GetValue(iControlNumber, oInsItem);
        //String[] arrValue_Sample = sTempValue_Sample.split("\\|");
       // final String sSampleRating = arrValue_Sample[iRatingNumber];

     //   String sWording = GetRatingName_WithPosition(sConfig, iRatingNumber) + " to " + GetRatingValue_Text(sSampleRating);
        AlertDialog.Builder builder = CommonUI.GetAlertBuilder("Bulk Assign Rating", null,
                oContext, true, false);
        if (bShowNo){
            builder.setItems(new CharSequence[]
                            {"Checked", "Checked No", "Unchecked"},
                    new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int which) {
                            // The 'which' argument contains the index position
                            // of the selected item
                            switch (which) {
                                case 0:
                                    AssignAllRating(lsInsItem, iControlNumber, iRatingNumber, oPInsItem, oAdapter, sConfig, "Y");
                                    break;
                                case 1:
                                    AssignAllRating(lsInsItem, iControlNumber, iRatingNumber, oPInsItem, oAdapter, sConfig, "N");
                                    break;
                                case 2:
                                    AssignAllRating(lsInsItem, iControlNumber, iRatingNumber, oPInsItem, oAdapter, sConfig, "A");
                                    break;
                                default:
                                    break;
                            }
                        }
                    });
        }
        else{
            builder.setItems(new CharSequence[]
                            {"Checked", "Unchecked"},
                    new DialogInterface.OnClickListener() {
                        public void onClick(DialogInterface dialog, int which) {
                            // The 'which' argument contains the index position
                            // of the selected item
                            switch (which) {
                                case 0:
                                    AssignAllRating(lsInsItem, iControlNumber, iRatingNumber, oPInsItem, oAdapter, sConfig, "Y");
                                    break;
                                case 1:
                                    AssignAllRating(lsInsItem, iControlNumber, iRatingNumber, oPInsItem, oAdapter, sConfig, "A");
                                    break;
                                default:
                                    break;
                            }
                        }
                    });
        }





   //     builder.setPositiveButton(android.R.string.yes, new DialogInterface.OnClickListener() {
   //         public void onClick(DialogInterface dialog, int which) {

//            }
  //      });

        builder.create().show();
    }

    public static boolean isSamsungDevice() {
        String sManufacture = android.os.Build.MANUFACTURER;
        return !StringUtils.isEmpty(sManufacture) && sManufacture.toLowerCase().contains("samsung");
    }

    private static void AssignAllRating(final List<ai_InsItem> lsInsItem, final int iControlNumber,
                                        final int iRatingNumber, final ai_InsItem oPInsItem,
                                         final BaseAdapter oAdapter, String sConfig, String sSampleRating){
        for (int i=0; i< lsInsItem.size(); i++){
            ai_InsItem oTempInsItem = lsInsItem.get(i);
            String sTempConfig = CommonHelper.GetConfig(iControlNumber, oTempInsItem, oPInsItem);
            if (sConfig.equalsIgnoreCase(sTempConfig)){
                String sValue = CommonHelper.GetValue(iControlNumber, oTempInsItem);
                String[] arrValue = sValue.split("\\|");
                if (CommonInsItem.getControlType(sConfig) == SI_CONTROL_TYPE_SCHK) {
                    Arrays.fill(arrValue, "A");
                }
                arrValue[iRatingNumber] = sSampleRating;
                String sTextValue = CommonHelper.GetChkValue(arrValue);
                CommonHelper.SetValue(iControlNumber, oTempInsItem, sTextValue);
                CommonDB.saveInsItem(oTempInsItem);
            }
        }
        oAdapter.notifyDataSetChanged();
    }

    public static void BulkSetRating(final List<ai_InsItem> lsInsItem, final int iControlNumber,
                                     final int iRatingNumber, final ai_InsItem oInsItem, final ai_InsItem oPInsItem,
                                     final Context oContext, final BaseAdapter oAdapter) {

        final String sConfig = CommonHelper.GetConfig(iControlNumber, oInsItem, oPInsItem);
        if (sConfig == null || sConfig.trim().equalsIgnoreCase("")) return;

        String sTempValue_Sample = CommonHelper.GetValue(iControlNumber, oInsItem);
        String[] arrValue_Sample = sTempValue_Sample.split("\\|");
        final String sSampleRating = arrValue_Sample[iRatingNumber];

        String sWording = GetRatingName_WithPosition(sConfig, iRatingNumber) + " to " + GetRatingValue_Text(sSampleRating);
        AlertDialog.Builder builder = CommonUI.GetAlertBuilder(
                "Bulk Assign Rating", "Are you sure you want to bulk assign rating " + sWording + "?",
                oContext, true, false
        );

        builder.setPositiveButton(R.string.tv_ok, (dialog, which) -> {
            for (int i = 0; i < lsInsItem.size(); i++) {
                ai_InsItem oTempInsItem = lsInsItem.get(i);
                String sTempConfig = CommonHelper.GetConfig(iControlNumber, oTempInsItem, oPInsItem);
                if (sConfig.equalsIgnoreCase(sTempConfig)) {
                    String sValue = CommonHelper.GetValue(iControlNumber, oTempInsItem);
                    String[] arrValue = sValue.split("\\|");
                    if (CommonInsItem.getControlType(sConfig) == SI_CONTROL_TYPE_SCHK) {
                        Arrays.fill(arrValue, "A");
                    }
                    arrValue[iRatingNumber] = sSampleRating;
                    String sTextValue = CommonHelper.GetChkValue(arrValue);
                    CommonHelper.SetValue(iControlNumber, oTempInsItem, sTextValue);
                    CommonDB.saveInsItem(oTempInsItem);
                }
            }
            oAdapter.notifyDataSetChanged();
        });

        builder.show();
    }

    public static Date getDateFromString(String sDate, String formatStr) {
        SimpleDateFormat format = new SimpleDateFormat(formatStr, Locale.ENGLISH);
        Date date = null;

        try {
            date = format.parse(sDate);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return date;
    }

    public static String getDateTimeStrFromDate(Date date, String formatStr) {
        SimpleDateFormat dateFormat = new SimpleDateFormat(formatStr);
        dateFormat.setTimeZone(TimeZone.getDefault());
        String sTimeDate;
        sTimeDate = dateFormat.format(date);

        return sTimeDate;
    }

    public static String getUserColorString(int iCustomerId) {

        List<ai_User> users = CommonDB.GetAllUsersSugar();

        int count = 0;
        for (int i = 0; i < users.size(); i++) {
            if (users.get(i).iCustomerID == iCustomerId) {
                count = i;
                break;
            }
        }

        int idx = count % Constants.USER_COLORS.length;
        return Constants.USER_COLORS[idx];
    }

    public static int getInt(String s) {
        if (StringUtils.isEmpty(s)) return 0;
        try {
            return Integer.parseInt(s);
        } catch (Exception e) {
            return 0;
        }
    }

    public static float getFloat(String s) {
        if (StringUtils.isEmpty(s)) return 0;
        try {
            return Float.parseFloat(s);
        } catch (Exception e) {
            return 0;
        }
    }

    public static double getDouble(String s) {
        if (StringUtils.isEmpty(s)) return 0;
        try {
            return Double.parseDouble(s);
        } catch (Exception e) {
            return 0;
        }
    }

    public static long getLong(String s) {
        if (StringUtils.isEmpty(s)) return 0;
        try {
            return Long.parseLong(s);
        } catch (Exception e) {
            return 0;
        }
    }

    public static boolean getBoolean(String sValue) {
        if (StringUtils.isEmpty(sValue)) return false;
        return sValue.equalsIgnoreCase("true")
                || Boolean.parseBoolean(sValue)
                || sValue.equalsIgnoreCase("1");
    }

    public static long getVideoDuration(Context ctx, String sFile) {
        try {
            File videoFile = new File(sFile);
            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
            //use one of overloaded setDataSource() functions to set your data source
            retriever.setDataSource(ctx, Uri.fromFile(videoFile));
            String time = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            return Long.parseLong(time );
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
            return 0;
        }
    }

    public static void clearSavedPreferences(Context ctx) {
        Context CCX = ctx.getApplicationContext();
        SharedPreferences preferences = CCX.getSharedPreferences(Constants.Paths.prefs, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = preferences.edit();
        editor.clear();
        editor.apply();
    }

    public static boolean fixCameraIsDisabled(Context ctx) {
        String sCC = fixCameraOption(ctx);
        if (sCC == null){
            return true;
        }
        return Constants.FixCameraOption.disable.equals(sCC);
    }

    public static String fixCameraOption(Context ctx) {
        return CommonHelper.GetPreferenceString(ctx, Constants.Settings.bFixCamera);
    }

    public static void DownloadReportRequest(Context ctx, int iInspectionID, final String sURL, String sType) {
        MaterialDialog progressDialog = CommonUI.ShowMaterialProgressDialog(ctx, "Message", "Connecting to Server");
        RequestParams oParams = new RequestParams();
        oParams.add("iCustomerID", CommonHelper.GetPreferenceString(ctx, "iCustomerID"));
        oParams.add("sToken", CommonHelper.GetPreferenceString(ctx, "sToken"));
        oParams.add("iInsID", "" + iInspectionID);

        if (sType.equalsIgnoreCase("D")) oParams.add("sType", sType);
        IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
            @Override
            public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                CommonUI.DismissMaterialProgressDialog(progressDialog);
                if (statusCode == 200) {
                    //Push to next Activity
                    try {
                        if (response.getBoolean("success")) {
                            new Handler(Looper.getMainLooper()).post(() -> {
                                try {
                                    ctx.startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(response.getString("sURL"))));
                                } catch (Exception ex) {
                                    ai_BugHandler.ai_Handler_Exception("Exception", "DownloadReportRequest.ViewReport", ex, null);
                                }
                            });
                        } else {
                            new Handler().post(new Runnable() {
                                @Override
                                public void run() {
                                    ShowAlert("Failed", "Please try again.", ctx);
                                }
                            });
                        }
                    } catch (Exception ex) {
                        ai_BugHandler.ai_Handler_Exception("Exception", "GetReportRequest", ex, null);
                    }

                } else {
                    new Handler(Looper.getMainLooper()).post(new Runnable() {
                        @Override
                        public void run() {
                            ShowAlert("Error! Failed Connection", "Please try again later.", ctx);
                        }
                    });
                }
            }
        });
    }

    public static int getCurrentUserID(Context ctx) {
        return getInt(GetPreferenceString(ctx, Constants.Keys.iCustomerID));
    }

    public static String getUserToken(Context ctx) {
        return GetPreferenceString(ctx, Constants.Keys.sToken);
    }

    public static void openURL(Context context, String url) {
        Intent browserIntent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
        browserIntent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(browserIntent);
    }

    public static void resetSyncData(Context ctx) {
        SavePreference(ctx, "bStartSync", "true");
        SavePreference(ctx, "sSyncDate", "1980-1-1 0:0:0");
        SavePreference(ctx, Constants.Keys.sProjectsLastUpdated, "1980-1-1 0:0:0");
        SavePreference(ctx, Constants.Keys.DATE_SYNC_PRODUCTS, "1980-1-1 0:0:0");
        SavePreference(ctx, Constants.Keys.DATE_SYNC_PROPERTY_LAYOUTS, "1980-1-1 0:0:0");
		SavePreference(ctx, "SC_Sync", "1980-1-1 0:0:0");
        SavePreference(ctx, Constants.Keys.DATE_SYNC_CUSTOMER_TASKS, "1980-1-1 0:0:0");
    }

    public static void resetData(Context ctx) {
        resetSyncData(ctx);
        SavePreference(ctx, Constants.Keys.sCustomRole, "");
        SavePreference(ctx, Constants.Keys.kSelectedAssetViewID, "");
        CommonDB.ResetDB();
    }

    public static boolean isAppInBackground(Context context) {
        ActivityManager activityManager = (ActivityManager) context.getSystemService(Context.ACTIVITY_SERVICE);
        List<ActivityManager.RunningAppProcessInfo> appProcesses = activityManager.getRunningAppProcesses();
        if (appProcesses == null) {
            return true;
        }
        final String packageName = context.getPackageName();
        for (ActivityManager.RunningAppProcessInfo appProcess : appProcesses) {
            if (appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND
                    && appProcess.processName.equals(packageName)) {
                return false;
            }
        }
        return true;
    }

    public static boolean hasEnabledFloorPlan(Context context) {
        return !CommonHelper.isKioskMode(context) &&
            "1".equals(CommonJson.GetJsonKeyValue(Constants.Keys.kFloorPlan,
                CommonHelper.GetPreferenceString(context, Constants.Keys.sCompanyCustom1)));
    }

    public static boolean bUseNewVideo() {
        return Build.VERSION.SDK_INT > Build.VERSION_CODES.Q;
    }
}
