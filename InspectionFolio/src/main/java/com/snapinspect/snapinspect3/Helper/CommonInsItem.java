package com.snapinspect.snapinspect3.Helper;

import com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.*;

public class CommonInsItem {

    public static String GetConfig_Content(String sConfig){
        sConfig = sConfig == null ? "" : sConfig.trim();
        if (sConfig.startsWith("{")) {
            return CommonJson.GetJsonKeyValue("Content", sConfig, "");
        }
        return "";
    }
    public static String GetConfig_Label(String sConfig) {
        sConfig = sConfig == null ? "" : sConfig.trim();
        if (sConfig.startsWith("{")) {
            return CommonJson.GetJsonKeyValue("sLabel", sConfig, "");
        } else {
            int iControlType = getControlType(sConfig), iTextPos = 0;
            if (iControlType == SI_CONTROL_TYPE_CHK || iControlType == SI_CONTROL_TYPE_CMT) {
                iTextPos = 4;
            } else if (iControlType == SI_CONTROL_TYPE_SCHK) {
                iTextPos = 5;
            } else if (iControlType == SI_CONTROL_TYPE_MCHK) {
                iTextPos = 5;
            }

            try {
                return sConfig.substring(iTextPos, sConfig.length() - 1);
            } catch (Exception exX) {
                return "";
            }
        }
    }

    public static String[] GetConfig_StringArray(String sText) {
        try {
            String[] sArray = new String[]{};
            ArrayList<String> lsText = new ArrayList<>();

            try {
                if (sText.trim().startsWith("[")) {
                    sArray = CommonJson.GetStringsValue(sText, "T").toArray(new String[0]);
                    if (sArray.length == 0) {
                        sArray = new String[]{sText};
                    }
                } else if (sText.contains("[|]")) {
                    sArray = sText.split("\\[\\|\\]");
                } else {
                    sArray = sText.split(",");
                }

            } catch (Exception exx) {

            }
            for (int i = 0; i < sArray.length; i++) {
                if (sArray[i] != null && sArray[i].trim().length() > 0) {
                    lsText.add(sArray[i].trim());
                }
            }
            return lsText.toArray(new String[lsText.size()]);
        } catch (Exception ex) {

        }
        return new String[]{};
    }
    public static String[] GetConfig_ColorArray(String sText) {
        try {

            String[] sArray = new String[]{};
            ArrayList<String> lsText = new ArrayList<>();

            try {

                    sArray = CommonJson.GetStringsValue(sText, "C").toArray(new String[0]);
                    if (sArray.length == 0) {
                        sArray = new String[]{sText};
                    }


            } catch (Exception exx) {

            }
            for (int i = 0; i < sArray.length; i++) {
                if (sArray[i] != null && sArray[i].trim().length() > 0) {
                    lsText.add(sArray[i].trim());
                }
            }
            return lsText.toArray(new String[lsText.size()]);
        } catch (Exception ex) {

        }
        return new String[]{};
    }
    public static String[] getCHKColorItems(String sConfig){
        return GetConfig_ColorArray(GetConfig_Label(sConfig));
    }
    public static String[] getCHKLabelItems(String sConfig) {
        return GetConfig_StringArray(GetConfig_Label(sConfig));
    }

    public static String[] getAllSelectedCHKLabel(String sConfig, String sValue) {
        ArrayList<String> parcel = new ArrayList<>();
        try {
            String[] sArray = CommonInsItem.getCHKLabelItems(sConfig);
            String[] sArrayValue = !StringUtils.isEmpty(sValue) ? sValue.split("\\|") : new String[]{};
            if (sArray.length == sArrayValue.length && sArray.length > 0) {
                int controlType = getControlType(sConfig);
                if (controlType == SI_CONTROL_TYPE_CHK) {
                    for (int i = 0; i < sArray.length; i++) {
                        parcel.add(String.format("%s:%s", sArray[i], sArrayValue[i]));
                    }
                } else if (controlType == SI_CONTROL_TYPE_SCHK || controlType == SI_CONTROL_TYPE_MCHK) {
                    for (int i = 0; i < sArray.length; i++) {
                        if ("Y".equals(sArrayValue[i])) {
                            parcel.add(sArray[i]);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            // ex
        }
        return parcel.toArray(new String[0]);
    }

    public static String[] getCHKItemsWithAttributeName(String attribute, String sText) {
        try {
            String[] sArray = new String[]{};
            ArrayList<String> lsText = new ArrayList<>();
            try {
                if (sText.trim().startsWith("[")) {
                    sArray = CommonJson.GetStringsValue(sText, attribute).toArray(new String[0]);
                    if (sArray.length == 0) {
                        sArray = new String[]{sText};
                    }
                } else if (sText.contains("[|]")) {
                    sArray = sText.split("\\[\\|\\]");
                } else {
                    sArray = sText.split(",");
                }

            } catch (Exception exx) {
                //
            }

            for (String s : sArray) {
                if (s != null && s.trim().length() > 0) {
                    lsText.add(s.trim());
                }
            }
            return lsText.toArray(new String[lsText.size()]);
        } catch (Exception ex) {
            //
        }
        return new String[]{};
    }

    public static String[] getCHKColors(String sConfig) {

        String[] cc = GetConfig_StringArray(GetConfig_Label(sConfig));
        return cc;
    }

    public static boolean getConfig_bWeb(String sConfig) {
        if (sConfig.startsWith(SI_S_CONFIG_KEY_OTHER)) {
             return "1".equals(CommonJson.GetJsonKeyValue("_bWeb", sConfig));
        }
        return false;
    }

    public static int getControlType(String sConfig) {
        int iControlType = -1;
        if (StringUtils.isEmpty(sConfig)) return iControlType;

        if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_CHK)) {
            iControlType = SI_CONTROL_TYPE_CHK;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_SCHK)) {
            iControlType = ai_Enum_Config.SI_CONTROL_TYPE_SCHK;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_MCHK)) {
            iControlType = SI_CONTROL_TYPE_MCHK;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_CMT)) {
            iControlType = SI_CONTROL_TYPE_CMT;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_PTO)) {
            iControlType = ai_Enum_Config.SI_CONTROL_TYPE_PTO;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_SCAN)) {
            iControlType = ai_Enum_Config.SI_CONTROL_TYPE_SCAN;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_SEL)) {
            iControlType = ai_Enum_Config.SI_CONTROL_TYPE_SEL;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_MSEL)) {
            iControlType = ai_Enum_Config.SI_CONTROL_TYPE_MSEL;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_NUM)) {
            iControlType = ai_Enum_Config.SI_CONTROL_TYPE_NUM;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_LST)) {
            iControlType = ai_Enum_Config.SI_CONTROL_TYPE_LST;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_DT)) {
            iControlType = ai_Enum_Config.SI_CONTROL_TYPE_DT;
        } else if (sConfig.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_COST)) {
            iControlType = ai_Enum_Config.SI_CONTROL_TYPE_COST;
        } else if (sConfig.startsWith(SI_S_CONFIG_KEY_OTHER)) {
            String sType = CommonJson.GetJsonKeyValue("sType", sConfig);
            if (sType != null) {
                if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_SEL)) {
                    iControlType = ai_Enum_Config.SI_CONTROL_TYPE_SEL;
                } else if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_MSEL)) {
                    iControlType = ai_Enum_Config.SI_CONTROL_TYPE_MSEL;
                } else if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_NUM)) {
                    iControlType = ai_Enum_Config.SI_CONTROL_TYPE_NUM;
                } else if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_LST)) {
                    iControlType = ai_Enum_Config.SI_CONTROL_TYPE_LST;
                } else if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_CHK)) {
                    iControlType = SI_CONTROL_TYPE_CHK;
                } else if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_SCHK)) {
                    iControlType = ai_Enum_Config.SI_CONTROL_TYPE_SCHK;
                } else if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_MCHK)) {
                    iControlType = SI_CONTROL_TYPE_MCHK;
                } else if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_CMT)) {
                    iControlType = SI_CONTROL_TYPE_CMT;
                } else if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_PTO)) {
                    iControlType = ai_Enum_Config.SI_CONTROL_TYPE_PTO;
                } else if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_SCAN)) {
                    iControlType = ai_Enum_Config.SI_CONTROL_TYPE_SCAN;
                } else if (sType.equalsIgnoreCase(ai_Enum_Config.SI_S_CONFIG_KEY_DT)) {
                    iControlType = ai_Enum_Config.SI_CONTROL_TYPE_DT;
                } else if (sType.startsWith(ai_Enum_Config.SI_S_CONFIG_KEY_COST)) {
                    iControlType = ai_Enum_Config.SI_CONTROL_TYPE_COST;
                }
            }
        }
        return iControlType;
    }
}
