package com.snapinspect.snapinspect3.Helper;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;

import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.RequestParams;
import com.loopj.android.http.SyncHttpClient;
import com.loopj.android.http.TextHttpResponseHandler;
import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.SI_DB.db_Media;
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayPhoto;

import org.json.JSONObject;

import java.io.InputStream;
import java.lang.ref.WeakReference;
import java.net.URL;
import java.util.List;

/**
 * Created by terrysun on 3/02/18.
 */

public class ImageDownloader extends AsyncTask<Void, String, Void> {

    public interface DownloadingStateChange {
        void onComplete();
    }

    private MaterialDialog oDialog;
    private final boolean isPush;
    private long photoID;
    private final List<ai_Photo> lsPhoto;
    private final WeakReference<Context> oContext;
    private final DownloadingStateChange downloadingStateChange;
    // int iTotalCount = 0;

    public ImageDownloader(List<ai_Photo> _lsPhoto, boolean _bPush, Context _oContext, DownloadingStateChange stateChange) {
        lsPhoto = _lsPhoto;
        isPush = _bPush;
        oContext = new WeakReference<>(_oContext);
        downloadingStateChange = stateChange;
        //iTotalCount = lsPhoto.size();
    }

    @Override
    protected void onPreExecute() {
        super.onPreExecute();
        oDialog = CommonUI.ShowMaterialProgressDialog(
                oContext.get(), "Message", "Prepare download, please wait ...");
    }

    protected Void doInBackground(Void... params) {

        try {
            Context ctx = oContext.get();
            if (ctx == null) return null;

            int iCount = 1;
            final int iTotalCount = lsPhoto.size();
            for (final ai_Photo oTemp : lsPhoto) {
                final int iTempCount = iCount;
                if (oTemp.bUploaded && oTemp.iSPhotoID > 0 && (!CommonHelper.bFileExist(oTemp.getFile()))) {
                    final long iPhotoID = oTemp.getId();
                    RequestParams oParams = new RequestParams();
                    oParams.add("iCustomerID", CommonHelper.GetPreferenceString(ctx, "iCustomerID"));
                    oParams.add("sToken", CommonHelper.GetPreferenceString(ctx, "sToken"));
                    oParams.add("iPhotoID", String.valueOf(oTemp.iSPhotoID));

                    final String sURL = "/IOAPI/GetPhoto";
                    publishProgress("" + iCount + "/" + iTotalCount);
                    SyncHttpClient oClient = new SyncHttpClient(true, 80, 443);
                    oClient.post(IF_RestClient.getAbsoluteUrl(sURL), oParams, new TextHttpResponseHandler() {
                        @Override
                        public void onSuccess(String responseBody) {
                            try {
                                JSONObject response = new JSONObject(responseBody);
                                String sURL = response.getString("sURL");
                                if (sURL != null) {
                                    Bitmap bitmap = BitmapFactory.decodeStream((InputStream) new URL(sURL).getContent());
                                    if (bitmap != null) {
                                        O_FileName oFileName = O_FileName.getPhotoFileName();

                                        CommonHelper.SaveImage(oFileName.sFilePath, bitmap);
                                        CommonHelper.SaveThumb(oFileName.sThumbNail, bitmap);

                                        ai_Photo oPhoto = ai_Photo.findById(ai_Photo.class, iPhotoID);

                                        if (oPhoto != null) {
                                            oPhoto.sFile = oFileName.sFilePath;
                                            oPhoto.sThumb = oFileName.sThumbNail;
                                            photoID = db_Media.UpdatePhoto(oPhoto);
                                            bitmap.recycle();
                                        }
                                    }
                                }
                            } catch (Exception ex) {
                                ai_BugHandler.ai_Handler_Exception(ex);
                            }
                        }
                    });
                }

                iCount++;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    protected void onProgressUpdate(String... progress) {
        oDialog.setContent("Download Photo " + progress[0]);
    }

    protected void onPostExecute(Void param) {
        CommonUI.DismissMaterialProgressDialog(oDialog);
        Context ctx = oContext.get();
        if (ctx != null && isPush) {
            if (photoID > 0) {
                ctx.startActivity(if_DisplayPhoto.newIntent(ctx, photoID));
            }
        } else {
            downloadingStateChange.onComplete();
        }
    }
}