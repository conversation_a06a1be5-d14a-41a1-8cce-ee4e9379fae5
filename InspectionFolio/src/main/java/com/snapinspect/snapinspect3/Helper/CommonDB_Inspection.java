package com.snapinspect.snapinspect3.Helper;

import android.content.Context;
import android.database.Cursor;
import android.text.TextUtils;

import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.SI_DB.db_Inspection;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.database.SnapInspectDatabase;
import com.snapinspect.snapinspect3.database.views.VInspectionView;
import com.snapinspect.snapinspect3.app.App;
import org.json.JSONArray;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Created by TerryS on 2/08/17.
 */

public class CommonDB_Inspection {
    public static List<ai_InsType> GetInsType_CustomPermission(Context oContext){
        List<ai_InsType> lsInsType = new ArrayList<ai_InsType>();
        List<ai_InsType> lsTemp =      db_Inspection.GetAllInsType();
        for (int i=0; i< lsTemp.size(); i++){
            if (bDisplayInsIype(oContext, lsTemp.get(i))){
                lsInsType.add(lsTemp.get(i));
            }
        }
        return lsInsType;
    }
    public static ArrayList<Integer> GetInsType_CustomPermission_IntArray(Context oContext){
        ArrayList<Integer> arrResult = new ArrayList<Integer>();
        List<ai_InsType> lsTemp =      db_Inspection.GetAllInsType();
        for (int i=0; i< lsTemp.size(); i++){
            if (!bDisplayInsIype(oContext, lsTemp.get(i))){
                arrResult.add(lsTemp.get(i).iSInsTypeID);
            }
        }
        return arrResult;
    }

    private static boolean bDisplayInsIype(Context oContext, ai_InsType oInsType) {
        try {
            if (CommonHelper.GetPreferenceString(oContext, "sRole") == null
                    || !CommonJson.isCompanyAdmin(oContext)) {
                String sInsTypeCustom1 = CommonJson.GetJsonKeyValue("_iHide", oInsType.sFieldOne);
                if (!TextUtils.isEmpty(sInsTypeCustom1)) {
                    String[] arrArray = sInsTypeCustom1.split(",");
                    String iCustomerID = CommonHelper.GetPreferenceString(oContext, "iCustomerID");
                    List<String> list = Arrays.asList(arrArray);

                    if (list.contains(iCustomerID)) {
                        return false;
                    }
                }
            }

            // Check if the ins type is in the hidden list
            List<Integer> toBeHiddenInsTypeIds = CommonJson.getHiddenInsTypeIDs(oContext);
            if (toBeHiddenInsTypeIds.contains(oInsType.iSInsTypeID)) {
                return false;
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return true;
    }

    public static boolean bPassLayoutValidation(ai_Layout layout, ai_InsType insType) {
        try {
            if (layout == null || insType == null || insType.sFieldOne == null) {
                return false;
            }
            JSONArray byPassItems = CommonJson.GetJSONArrayFromJson(
                    CommonJson.GetJsonKeyValue("_iPass", insType.sFieldOne));
            for (int i = 0; i < byPassItems.length(); i++) {
                if (byPassItems.optInt(i) == layout.iSLayoutID) {
                    return true;
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return false;
    }

    public static List<ai_Layout> bypassLayouts(List<ai_Layout> layouts, ai_InsType insType) {
        if (layouts == null || insType == null || insType.sFieldOne == null) {
            return layouts;
        }
        ArrayList<ai_Layout> result = new ArrayList<>();
        try {
            for (ai_Layout layout : layouts) {
                if (!bPassLayoutValidation(layout, insType)) {
                    result.add(layout);
                }
            }
            return result;
        } catch (Exception exx) {
            return layouts;
        }
    }

    public static List<ai_Photo> GetInsItemPhotos(long iInsItemID, String sValue) {
        if (StringUtils.isEmpty(sValue)) return new ArrayList<>();

        String query = "B_DELETED = 0 AND ID IN (" + sValue + ")";
        if (iInsItemID > 0) {
            query += " AND I_INS_ITEM_ID = ?";
            return CommonDB.findPhotos(query, String.valueOf(iInsItemID));
        } else {
            return CommonDB.findPhotos(query);
        }
    }
    
    public static boolean bValidateDuplicateItemName(int iPInsItemID, String sName){
        try {
            List<ai_InsItem> lsTempInsItem = CommonDB.findInsItems("I_P_INS_ITEM_ID = ? AND S_NAME = ? AND B_DELETED = 0", 
                String.valueOf(iPInsItemID), sName);
            if (lsTempInsItem != null && lsTempInsItem.size() > 0) {
                return true;
            }
        }catch(Exception ex){

        }
        return false;
    }

    public static ai_InsItem DuplicateInsItem(ai_InsItem origInsItem, ai_InsItem oPInsItem, String sName){
        List<ai_InsItem> lsTempInsItem1 = CommonDB.GetChildInsItem(0, origInsItem.iInsID);
        try {
            // Get the current count for proper ordering
            if (lsTempInsItem1 == null) {
                lsTempInsItem1 = new ArrayList<>();
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        String sValueOne = ""; String sValueTwo = "";String sValueThree = "";String sValueFour = "";String sValueFive = "";String sValueSix = "";
        if (origInsItem.sQType.equalsIgnoreCase("P")){
            sValueOne = CommonHelper.GetDefaultCheckboxValue(origInsItem.sConfigOne);
            sValueTwo = CommonHelper.GetDefaultCheckboxValue(origInsItem.sConfigTwo);
            sValueThree = CommonHelper.GetDefaultCheckboxValue(origInsItem.sConfigThree);
            sValueFour = CommonHelper.GetDefaultCheckboxValue(origInsItem.sConfigFour);
            sValueFive = CommonHelper.GetDefaultCheckboxValue(origInsItem.sConfigFive);
            sValueSix = CommonHelper.GetDefaultCheckboxValue(origInsItem.sConfigSix);
        }
        else{
            sValueOne = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigOne);
            sValueTwo = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigTwo);
            sValueThree = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigThree);
            sValueFour = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigFour);
            sValueFive = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigFive);
            sValueSix = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigSix);
        }
        ai_InsItem oInsItem = new ai_InsItem(oPInsItem.getId().intValue(), origInsItem.iInsID, 0, sName,
                sValueOne, sValueTwo, sValueThree, sValueFour, sValueFive, sValueSix,
                origInsItem.sQType, origInsItem.sConfigOne, origInsItem.sConfigTwo, origInsItem.sConfigThree,
                origInsItem.sConfigFour, origInsItem.sConfigFive, origInsItem.sConfigSix, origInsItem.sConfig, false, lsTempInsItem1.size() + 1, 0, "c", "", "");

        CommonDB.saveInsItem(oInsItem);
        return oInsItem;
    }


    public static ai_Inspection GetInspectionByScheduleID(int iSScheduleID){
        try{
            //  AND iSInsID = 0
            List<ai_Inspection> lsInspection = CommonDB.findInspections(
                "I_S_SCHEDULE_ID=? and B_DELETED = 0 and I_S_INS_ID = 0", String.valueOf(iSScheduleID));

            if (lsInspection != null && lsInspection.size() > 0) {
                return lsInspection.get(0);
            }
        }catch(Exception ex){

        }
        return null;

    }
    public static ai_Inspection GetInspectionByAssetInsType(int iSAssetID, int iSInsTypeID){
        try{
            List<ai_Inspection> lsInspection = CommonDB.findInspections("B_SYNCED = 0 and (B_COMPLETE = 0 or B_COMPLETE = 1) and I_S_Asset_ID = ? and I_S_Ins_Type_ID=? and B_DELETED = 0", String.valueOf(iSAssetID), String.valueOf(iSInsTypeID));

            if (lsInspection != null && lsInspection.size() > 0) {
                return lsInspection.get(0);
            }
        }catch(Exception ex){

        }
        return null;

    }
    public static void CreateInspectionView(){
        try{
            String sPropertyCreate = "CREATE VIEW IF NOT EXISTS VASSET AS SELECT c.ID as iID, c.I_S_ASSET_ID as iAssetID, d.I_S_ASSET_ID as iPAssetID," +
                    "e.I_S_ASSET_ID as iPPAssetID, c.S_FIELD_THREE as sRef, " +
                    "((CASE WHEN d.I_SP_ASSET_ID > 0 THEN ' ' || e.S_ADDRESS_ONE || ' ' || e.S_ADDRESS_TWO || ' | ' ELSE '' END) || " +
                    "(CASE WHEN c.I_SP_ASSET_ID > 0 THEN ' ' || d.S_ADDRESS_ONE || ' ' || d.S_ADDRESS_TWO || ' | ' ELSE '' END ) || " +
                    "' ' || c.S_ADDRESS_ONE || ' ' || c.S_ADDRESS_TWO) as sSearchTerm, " +
                    "(CASE WHEN d.I_SP_Asset_ID > 0 THEN e.S_ADDRESS_ONE || ', ' || e.S_ADDRESS_TWO WHEN C.I_SP_ASSET_ID > 0 THEN d.S_ADDRESS_ONE || ', ' || d.S_ADDRESS_TWO " +
                    "ELSE c.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO END) as sBuildingAddress, " +
                    "(CASE WHEN d.I_SP_ASSET_ID > 0 then d.S_ADDRESS_ONE || ', ' || d.S_ADDRESS_TWO WHEN C.I_SP_ASSET_ID > 0 THEN c.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO " +
                    "ELSE '' END) as sUnitAddress, " +
                    "(CASE WHEN C.I_SP_ASSET_ID > 0 and d.I_SP_ASSET_ID > 0 THEN C.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO ELSE '' END) as sRoomAddress, " +
                    "c.I_CUSTOMER_ID as iCustomerID, c.I_GROUP_ID as iGroupID, c.B_PUSH as bApartment, c.S_FIELD_ONE as sCustom1, c.S_FIELD_TWO as sCustom2 FROM AIASSETS c LEFT JOIN AIASSETS d on c.I_SP_ASSET_ID = d.I_S_ASSET_ID LEFT JOIN " +
                    "AIASSETS e on d.I_SP_ASSET_ID = e.I_S_ASSET_ID WHERE c.B_DELETED = 0";

            // Views are now automatically created by Room from @DatabaseView annotations
            // VInspectionView and related views are defined in the database


           // String xx = "bb";

        }catch(Exception ex){
           // String cc = "bb";
        }
    }

    public static List<v_Inspection> SearchInspection(String sSearchTerm, boolean bCompleted, boolean bSynced){
        List<v_Inspection> lsInspection = new ArrayList<v_Inspection>();
        try{
            if (sSearchTerm == null || sSearchTerm.trim().length() == 0){
                sSearchTerm = "";
            }
            String[] arrTemp = sSearchTerm.split(",| |;");
            List<String> lsTerm = new ArrayList<>();

            List<String> arLikeTerm = new ArrayList<>();
            for (String tmp : arrTemp) {
                if (tmp.trim().isEmpty()) {
                    continue;
                }

                arLikeTerm.add("sSearchTerm like ?");
                lsTerm.add("%" + tmp.trim() + "%");

            }
            List<VInspectionView> inspections;
            
            if (arLikeTerm.isEmpty()) {
                // No search terms, just filter by completion and sync status
                inspections = getDatabase().vInspectionDao().searchInspections(bCompleted, bSynced);
            } else {
                // Search with terms and filter by completion and sync status
                inspections = new ArrayList<>();
                for (String term : lsTerm.subList(0, lsTerm.size() - 2)) { // Remove the bCompleted and bSynced values
                    List<VInspectionView> termResults = getDatabase().vInspectionDao()
                        .searchInspectionsByTerm(term, bCompleted, bSynced);
                    for (VInspectionView inspection : termResults) {
                        if (!inspections.contains(inspection)) {
                            inspections.add(inspection);
                        }
                    }
                }
            }
            
            for (VInspectionView inspection : inspections) {
                v_Inspection converted = convertToVInspection(inspection);
                if (converted != null) {
                    lsInspection.add(converted);
                }
            }
        }catch(Exception ex){
            String cc = "bb";
        }
        //CommonDB.

        return lsInspection;
    }

    public static ai_Inspection GetInspectionByID(int iInspectionID) {
        try {
            List<ai_Inspection> lsInspection = CommonDB.findInspections("ID=? and B_DELETED = 0", String.valueOf(iInspectionID));
            if (lsInspection != null && lsInspection.size() > 0) {
                return lsInspection.get(0);
            }
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return null;
    }

    public static ai_Inspection GetInspectionBySID(int iSInsID) {
        try {
            List<ai_Inspection> inspections = CommonDB.findInspections("I_S_INS_ID=? and B_DELETED = 0", String.valueOf(iSInsID));
            return inspections.get(0);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return null;
    }

    public static List<ai_Inspection> GetCompletedInspections(int iInsID) {
        List<ai_Inspection> lsInspection;
        if (iInsID > 0) {
            lsInspection = CommonDB.findInspections(
                    "B_COMPLETE = 1 and B_DELETED = 0 and B_SYNCED = 0 and  id=?", String.valueOf(iInsID));
        } else {
            lsInspection = CommonDB.findInspections(
                    "B_COMPLETE = 1 and B_DELETED = 0 and B_SYNCED = 0");
        }
        return lsInspection;
    }

    public static List<ai_Inspection> GetCompletedInspectionsNoCompulsoryItems(int iInsID) {
        List<ai_Inspection> inspections = GetCompletedInspections(iInsID);
        ArrayList<ai_Inspection> results = new ArrayList<>();
        for (ai_Inspection ins : inspections) {
            if (!ins.hasNotCompletedCompulsoryItems()) results.add(ins);
        }
        return results;
    }

    public static void markCompletedInspectionsNeedValidate(int iInsID) {
        List<ai_Inspection> inspections = GetCompletedInspections(iInsID);
        if (inspections == null || inspections.isEmpty()) return;
        for (ai_Inspection ins: inspections) {
            List<ai_InsItem> arrInsItem = CommonDB.GetChildInsItem(0, ins.getId());
            if (arrInsItem == null || arrInsItem.isEmpty()) continue;
            for (ai_InsItem insItem : arrInsItem)
                db_InsItem.markNeedValidateCompulsoryConfig(insItem);
        }
    }
    
    /**
     * Helper method to convert VInspectionView to v_Inspection
     */
    private static v_Inspection convertToVInspection(VInspectionView view) {
        if (view == null) return null;
        
        try {
            // Create a mock cursor using MockCursor utility
            MockCursor cursor = new MockCursor();
            
            // Add all the fields that v_Inspection expects
            cursor.addColumn("iInspectionID", view.iInspectionID);
            cursor.addColumn("iSInsID", view.iSInsID);
            cursor.addColumn("iSAssetID", view.iSAssetID);
            cursor.addColumn("sTitle", view.sTitle != null ? view.sTitle : "");
            cursor.addColumn("sInsTitle", view.sInsTitle != null ? view.sInsTitle : "");
            cursor.addColumn("dtStartDate", view.dtStartDate != null ? view.dtStartDate : "");
            cursor.addColumn("dtEndDate", view.dtEndDate != null ? view.dtEndDate : "");
            cursor.addColumn("bComplete", view.bComplete ? 1 : 0);
            cursor.addColumn("bSynced", view.bSynced ? 1 : 0);
            cursor.addColumn("sCustom1", view.sCustom1 != null ? view.sCustom1 : "");
            cursor.addColumn("sCustom2", view.sCustom2 != null ? view.sCustom2 : "");
            cursor.addColumn("iSScheduleID", view.iSScheduleID);
            cursor.addColumn("sRef", view.sRef != null ? view.sRef : "");
            cursor.addColumn("sBuildingAddress", view.sBuildingAddress != null ? view.sBuildingAddress : "");
            cursor.addColumn("sUnitAddress", view.sUnitAddress != null ? view.sUnitAddress : "");
            cursor.addColumn("sRoomAddress", view.sRoomAddress != null ? view.sRoomAddress : "");
            cursor.addColumn("bApartment", view.bApartment ? 1 : 0);
            
            cursor.moveToFirst();
            return new v_Inspection(cursor);
            
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception("Exception", "convertToVInspection", e);
            return null;
        }
    }
    
    /**
     * Get the database instance
     */
    private static SnapInspectDatabase getDatabase() {
        return SnapInspectDatabase.getInstance(App.getContext());
    }
}
