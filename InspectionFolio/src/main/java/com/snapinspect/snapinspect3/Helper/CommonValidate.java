package com.snapinspect.snapinspect3.Helper;

import android.Manifest;
import android.app.Activity;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Build;
import android.os.Environment;
import android.widget.Toast;

import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import fj.parser.Result;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by TerryS on 2/08/17.
 */

public class CommonValidate {
    public static boolean checkStoragePermissions(Context ctx) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return true;
        } else if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            return Environment.isExternalStorageManager();
        } else {
            return ActivityCompat.checkSelfPermission(ctx,
                    Manifest.permission.WRITE_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
        }
    }

    public static boolean bCompanyGroupEnabled(Context oContext){
        try{
            return CommonHelper.GetPreferenceBoolean(oContext, "bCompanyGroup");
            //  if (sRequestInspection != null && boolean. sRequestInspection){
            //     return true;
            //  }
        }catch(Exception ex){

        }

        return false;
    }
   /* public static boolean bCompanyReviewEnabled(Context oContext){
        try{
            return CommonHelper.GetPreferenceBoolean(oContext, "bCompanyReview");
          //  if (sRequestInspection != null && boolean. sRequestInspection){
           //     return true;
          //  }
        }catch(Exception ex){

        }

        return false;
    }*/
    public static boolean bInspectionReviewEnabled(ai_Inspection oIns){
        try{
            String sRequestInspection = CommonJson.GetJsonKeyValue("_bReview", oIns.sCustomTwo);
            if (sRequestInspection != null && sRequestInspection.equalsIgnoreCase("1")){
                return true;
            }
        }catch(Exception ex){

        }

        return false;
    }
    public static boolean bInspectionIsChild(ai_Inspection oIns){
        try{
            String sRequestInspection = CommonJson.GetJsonKeyValue("_bChild", oIns.sCustomTwo);
            if (sRequestInspection != null && sRequestInspection.equalsIgnoreCase("1")){
                return true;
            }
        }catch(Exception ex){

        }

        return false;
    }
    public static boolean bItemReviewEnabled(ai_InsItem oInsItem){
        try{
            String sRequestInspection = CommonJson.GetJsonKeyValue("_b_R", oInsItem.sConfig);
            if (sRequestInspection != null && sRequestInspection.equalsIgnoreCase("1")){
                return true;
            }
        }catch(Exception ex){

        }

        return false;
    }
    public static String sItemReviewNotes(ai_InsItem oInsItem){
        try{
            String sRequestInspection = CommonJson.GetJsonKeyValue("_R_Cmt", oInsItem.sConfig);
            if (sRequestInspection != null && sRequestInspection.length() > 0){
                return sRequestInspection;
            }
        }catch(Exception ex){

        }

        return "";
    }
    public static String sItemReviewDt(ai_InsItem oInsItem){
        try{
            String sRequestInspection = CommonJson.GetJsonKeyValue("_R_Dt", oInsItem.sConfig);
            if (sRequestInspection != null && sRequestInspection.length() > 0){
                return sRequestInspection;
            }
        }catch(Exception ex){

        }

        return "";
    }
    public static String sExternalTokenID(String sCustom1){
        try{
            return CommonJson.GetJsonKeyValue("iTokenID", sCustom1);

        }catch(Exception ex){

        }
        return "";
    }
    public static String sExternalToken(String sCustom1){
        try{
            return CommonJson.GetJsonKeyValue("sToken", sCustom1);

        }catch(Exception ex){

        }
        return "";
    }
    public static boolean bExternalInspection(String sCustom1){
        try{
            String sRequestInspection = CommonJson.GetJsonKeyValue("sToken", sCustom1);
            if (sRequestInspection != null && sRequestInspection.length() > 0){
                return true;
            }
        }catch(Exception ex){

        }

        return false;
    }
    public static void SetTenantToolMode(boolean bSet, Context oContext){
        try{
            if (bSet){
                CommonHelper.SavePreference(oContext, "bTToolMode", "1" );
            }
            else{
                CommonHelper.SavePreference(oContext, "bTToolMode", "0" );
            }
        }catch(Exception eee){

        }
    }

    public static boolean bTenantToolMode(Context oContext) {
        return CommonHelper.GetPreferenceBoolean(oContext, "bTToolMode");
    }

    public static void SetRequestInspectionMode(boolean bSet, Context oContext){
        try{
            if (bSet){
                CommonHelper.SavePreference(oContext, "bRInsMode", "1" );
            }
            else{
                CommonHelper.SavePreference(oContext, "bRInsMode", "0" );
            }
        }catch(Exception eee){

        }
    }

    public static boolean bRequestInspectionMode(Context oContext) {
        return CommonHelper.GetPreferenceBoolean(oContext, "bRInsMode");
    }

    public static boolean Permission_Validate(Activity oContext) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            String[] permissions = {
                android.Manifest.permission.CAMERA,
                android.Manifest.permission.RECORD_AUDIO,
            };
            if (!checkIfPermissionsGranted(oContext, permissions)) {
                Toast.makeText(oContext, "Please allow Camera, Audio permissions for SnapInspect.", Toast.LENGTH_LONG).show();
                ActivityCompat.requestPermissions(oContext, permissions, 0);
                return false;
            }
        }
        CommonHelper.ValidateTempFolderExist();
        return true;
    }

    public static void validateWriteExternalStoragePermission(Activity oContext, int requestCode) {
        if (!CommonValidate.checkStoragePermissions(oContext) && Build.VERSION.SDK_INT < Build.VERSION_CODES.R) {
            Toast.makeText(oContext, "Please allow Write External Storage permission for SnapInspect.", Toast.LENGTH_LONG).show();
            requestPermission(oContext, Manifest.permission.WRITE_EXTERNAL_STORAGE, requestCode);
        }
    }

    public static boolean checkIfPermissionsGranted(Context oContext, String[] permissions) {
        for (String permission : permissions) {
            if (ContextCompat.checkSelfPermission(oContext, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }

    public static void requestPermissions(Activity oContext, String[] permissions) {
        if (checkIfPermissionsGranted(oContext, permissions)) return;
        ActivityCompat.requestPermissions(oContext, permissions, 0);
    }

    public static void requestPermission(Activity oContext, String permission, int requestCode) {
        if (ContextCompat.checkSelfPermission(oContext, permission) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(oContext, new String[]{permission}, requestCode);
        }
    }

    public static boolean validateInspectionsCompulsoryItems(Context ctx, int iInsID) {
        List<ai_Inspection> completedInspects = CommonDB_Inspection.GetCompletedInspections(iInsID);
        CommonDB_Inspection.markCompletedInspectionsNeedValidate(iInsID);

        List<ai_Inspection> availableInspections = CommonDB_Inspection.GetCompletedInspectionsNoCompulsoryItems(iInsID);
        if (availableInspections.isEmpty()) {
            CommonUI.ShowAlert(
                    ctx,
                    ctx.getResources().getQuantityString(
                        R.plurals.uncompleted_inspections,
                        completedInspects.size(),
                        completedInspects.size()
                    ),
                    ctx.getResources().getQuantityString(
                        R.plurals.uncompleted_inspections_message,
                        completedInspects.size(),
                        completedInspects.size()
                    )
            );
            return false;
        }
        return true;
    }

    public static boolean bEditInspectionNotAllowDeleteVideo(Context oContext, long lInsItemID) {
        ai_InsItem oInsItem = db_InsItem.GetInsItem_ByID(lInsItemID);
        if (oInsItem != null && oInsItem.getServerInsItemID() > 0) {
            // Edit Inspection does not allow to Delete Video.
            CommonUI.ShowAlert(oContext,
                    oContext.getString(R.string.alert_title_message),
                    oContext.getString(R.string.edit_inspection_not_allow_delete_video));
            return false;
        }
        return true;
    }

    public static boolean bEditInspectionNotResumeRecordVideo(Context oContext, long lInsItemID) {
        ai_InsItem oInsItem = db_InsItem.GetInsItem_ByID(lInsItemID);
        if (oInsItem != null && oInsItem.getServerInsItemID() > 0) {
            // Edit Inspection does not allow Resume Record Video.
            CommonUI.ShowAlert(oContext,
                    oContext.getString(R.string.alert_title_message),
                    oContext.getString(R.string.edit_inspection_not_allow_resume_record_video));
            return false;
        }
        return true;
    }

    public static boolean bEditInspectionNotAllowAddVideo(Context oContext, long lInsItemID) {
        ai_InsItem oInsItem = db_InsItem.GetInsItem_ByID(lInsItemID);
        if (oInsItem != null && oInsItem.getServerInsItemID() > 0) {
            // Edit Inspection does not allow to Add Video.
            CommonUI.ShowAlert(oContext,
                    oContext.getString(R.string.alert_title_message),
                    oContext.getString(R.string.edit_inspection_not_allow_add_video));
            return false;
        }
        return true;
    }

    public static boolean bEditInspectionNotAllowDeleteItem(Context oContext, long lInsItemID) {
        ai_InsItem oInsItem = db_InsItem.GetInsItem_ByID(lInsItemID);
        if (oInsItem != null && oInsItem.getServerInsItemID() > 0) {
            // Edit Inspection does not allow to Delete Item.
            CommonUI.ShowAlert(oContext,
                    oContext.getString(R.string.alert_title_message),
                    oContext.getString(R.string.edit_inspection_not_allow_delete_item));
            return false;
        }
        return true;
    }

    public static boolean validateNewInspection(Context context) {
        Result<Boolean, CommonJson.ValidateCustomRoleError> result = CommonJson.validateNewInspection(context);
        CommonJson.ValidateCustomRoleError error = result.value();
        if (error != null) {
            if (error == CommonJson.ValidateCustomRoleError.LOCATION) {
                CommonUI.showLocationPermissionDeniedAlert(context, error.message());
            } else {
                CommonUI.ShowAlert(context, "Error", error.message());
            }
        }
        return result.rest();
    }

    public static boolean validateGPSLocationPermission(Context context) {
        if (CommonJson.shouldTurnOnLocation(context) || 
            CommonHelper.GetPreferenceBoolean(context, Constants.Settings.bShowGeoTag)) {
            return checkDeviceLocationPermission(context);
        }
        return true;
    }

    private static boolean checkDeviceLocationPermission(Context context) {
        if (!GeoLocationManager.getInstance(context).locationPermissionDenied()) {
            return true;
        }

        List<String> messages = new ArrayList<>();
        messages.add("Please allow Location permission to Geo Tag photo or video");
        if (!CommonJson.shouldTurnOnLocation(context)) {
            messages.add("The Geo Tag configuration can be turned off in the app settings");
        }
        String sMessage = String.join(", ", messages);
        CommonUI.showLocationPermissionDeniedAlert(context, sMessage);
        return false;
    }
}
