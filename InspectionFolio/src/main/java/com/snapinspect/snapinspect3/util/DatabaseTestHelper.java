package com.snapinspect.snapinspect3.util;

import android.content.Context;
import android.util.Log;
import com.snapinspect.snapinspect3.database.RoomDatabaseManager;
import com.snapinspect.snapinspect3.database.SnapInspectDatabase;
import com.snapinspect.snapinspect3.database.entities.Assets;
import com.snapinspect.snapinspect3.database.entities.User;
import com.snapinspect.snapinspect3.database.entities.Inspection;
import com.snapinspect.snapinspect3.database.entities.InsItem;
import com.snapinspect.snapinspect3.database.entities.Photo;
import com.snapinspect.snapinspect3.database.entities.Video;

import java.util.List;

/**
 * Helper class to test Room DAO integration at runtime
 */
public class DatabaseTestHelper {
    private static final String TAG = "DatabaseTestHelper";

    /**
     * Run comprehensive database tests to verify Room DAO integration works
     */
    public static boolean runDatabaseTests(Context context) {
        Log.i(TAG, "Starting database integration tests...");
        
        try {
            SnapInspectDatabase database = RoomDatabaseManager.getInstance(context).getDatabase();
            
            // Test User operations
            if (!testUserOperations(database)) {
                Log.e(TAG, "User operations test failed");
                return false;
            }
            
            // Test Asset operations  
            if (!testAssetOperations(database)) {
                Log.e(TAG, "Asset operations test failed");
                return false;
            }
            
            // Test Inspection operations
            if (!testInspectionOperations(database)) {
                Log.e(TAG, "Inspection operations test failed");
                return false;
            }
            
            // Test InspectionItem operations
            if (!testInsItemOperations(database)) {
                Log.e(TAG, "InspectionItem operations test failed");
                return false;
            }
            
            // Test Photo operations
            if (!testPhotoOperations(database)) {
                Log.e(TAG, "Photo operations test failed");
                return false;
            }
            
            // Test Video operations
            if (!testVideoOperations(database)) {
                Log.e(TAG, "Video operations test failed");
                return false;
            }
            
            Log.i(TAG, "All database tests passed!");
            return true;
            
        } catch (Exception ex) {
            Log.e(TAG, "Database test failed with exception: " + ex.getMessage(), ex);
            return false;
        }
    }
    
    private static boolean testUserOperations(SnapInspectDatabase database) {
        Log.i(TAG, "Testing User operations...");
        
        try {
            // Test creating a user
            User testUser = new User();
            testUser.iCustomerID = 12345;
            testUser.sName = "Test User";
            testUser.sEmail = "<EMAIL>";
            testUser.bDeleted = false;
            
            long savedId = database.userDao().insertUser(testUser);
            
            if (savedId <= 0) {
                Log.e(TAG, "User save returned invalid ID: " + savedId);
                return false;
            }
            
            Log.i(TAG, "User saved with ID: " + savedId);
            
            // Test finding the user
            User foundUser = database.userDao().getUserById(savedId);
            if (foundUser == null) {
                Log.e(TAG, "Could not find saved user");
                return false;
            }
            
            if (!"Test User".equals(foundUser.sName)) {
                Log.e(TAG, "Found user has wrong name: " + foundUser.sName);
                return false;
            }
            
            Log.i(TAG, "User found successfully: " + foundUser.sName);
            
            // Test listing all users
            List<User> allUsers = database.userDao().getAllUsers();
            Log.i(TAG, "Found " + allUsers.size() + " total users");
            
            // Test soft delete
            database.userDao().softDeleteById(savedId);
            
            Log.i(TAG, "User operations test passed");
            return true;
            
        } catch (Exception ex) {
            Log.e(TAG, "User operations test failed: " + ex.getMessage(), ex);
            return false;
        }
    }
    
    private static boolean testAssetOperations(SnapInspectDatabase database) {
        Log.i(TAG, "Testing Asset operations...");
        
        try {
            // Test creating an asset
            Assets testAsset = new Assets();
            testAsset.sAddressOne = "123 Test Street";
            testAsset.sAddressTwo = "Test City";
            testAsset.iCustomerID = 12345;
            testAsset.iSAssetID = 99999;
            testAsset.bDeleted = false;
            
            long savedId = database.assetsDao().insertAsset(testAsset);
            
            if (savedId <= 0) {
                Log.e(TAG, "Asset save returned invalid ID: " + savedId);
                return false;
            }
            
            Log.i(TAG, "Asset saved with ID: " + savedId);
            
            // Test finding the asset
            Assets foundAsset = database.assetsDao().getAssetById(savedId);
            if (foundAsset == null) {
                Log.e(TAG, "Could not find saved asset");
                return false;
            }
            
            if (!"123 Test Street".equals(foundAsset.sAddressOne)) {
                Log.e(TAG, "Found asset has wrong address: " + foundAsset.sAddressOne);
                return false;
            }
            
            Log.i(TAG, "Asset found successfully: " + foundAsset.sAddressOne);
            
            // Test listing all assets
            List<Assets> allAssets = database.assetsDao().getAllAssets();
            Log.i(TAG, "Found " + allAssets.size() + " total assets");
            
            // Test soft delete
            database.assetsDao().softDeleteById(savedId);
            
            Log.i(TAG, "Asset operations test passed");
            return true;
            
        } catch (Exception ex) {
            Log.e(TAG, "Asset operations test failed: " + ex.getMessage(), ex);
            return false;
        }
    }
    
    private static boolean testInspectionOperations(SnapInspectDatabase database) {
        Log.i(TAG, "Testing Inspection operations...");
        
        try {
            // Test creating an inspection
            Inspection testInspection = new Inspection();
            testInspection.sTitle = "Test Inspection";
            testInspection.sComments = "This is a test inspection";
            testInspection.iSAssetID = 12345;
            testInspection.iSInsID = 88888;
            testInspection.bDeleted = false;
            testInspection.bComplete = false;
            testInspection.bSynced = false;
            
            long savedId = database.inspectionDao().insertInspection(testInspection);
            
            if (savedId <= 0) {
                Log.e(TAG, "Inspection save returned invalid ID: " + savedId);
                return false;
            }
            
            Log.i(TAG, "Inspection saved with ID: " + savedId);
            
            // Test finding the inspection
            Inspection foundInspection = database.inspectionDao().getInspectionById(savedId);
            if (foundInspection == null) {
                Log.e(TAG, "Could not find saved inspection");
                return false;
            }
            
            if (!"Test Inspection".equals(foundInspection.sTitle)) {
                Log.e(TAG, "Found inspection has wrong title: " + foundInspection.sTitle);
                return false;
            }
            
            Log.i(TAG, "Inspection found successfully: " + foundInspection.sTitle);
            
            // Test listing all inspections
            List<Inspection> allInspections = database.inspectionDao().getAllInspections();
            Log.i(TAG, "Found " + allInspections.size() + " total inspections");
            
            // Test update
            database.inspectionDao().updateCompletionStatus(savedId, true);
            
            Log.i(TAG, "Inspection operations test passed");
            return true;
            
        } catch (Exception ex) {
            Log.e(TAG, "Inspection operations test failed: " + ex.getMessage(), ex);
            return false;
        }
    }
    
    private static boolean testInsItemOperations(SnapInspectDatabase database) {
        Log.i(TAG, "Testing InspectionItem operations...");
        
        try {
            // Test creating an inspection item
            InsItem testInsItem = new InsItem();
            testInsItem.sName = "Test Inspection Item";
            testInsItem.sQType = "C";
            testInsItem.iInsID = 12345;
            testInsItem.iSLayoutID = 67890;
            testInsItem.sValueOne = "Test Value";
            testInsItem.bCompleted = false;
            testInsItem.bDeleted = false;
            testInsItem.iSort = 1;
            
            long savedId = database.insItemDao().insertInsItem(testInsItem);
            
            if (savedId <= 0) {
                Log.e(TAG, "InspectionItem save returned invalid ID: " + savedId);
                return false;
            }
            
            Log.i(TAG, "InspectionItem saved with ID: " + savedId);
            
            // Test finding the inspection item
            InsItem foundInsItem = database.insItemDao().getInsItemById(savedId);
            if (foundInsItem == null) {
                Log.e(TAG, "Could not find saved inspection item");
                return false;
            }
            
            if (!"Test Inspection Item".equals(foundInsItem.sName)) {
                Log.e(TAG, "Found inspection item has wrong name: " + foundInsItem.sName);
                return false;
            }
            
            Log.i(TAG, "InspectionItem found successfully: " + foundInsItem.sName);
            
            // Test updating the inspection item
            foundInsItem.bCompleted = true;
            foundInsItem.sValueTwo = "Updated Value";
            database.insItemDao().updateInsItem(foundInsItem);
            
            // Test listing all inspection items
            List<InsItem> allInsItems = database.insItemDao().getAllInsItems();
            Log.i(TAG, "Found " + allInsItems.size() + " total inspection items");
            
            // Test soft delete
            database.insItemDao().softDeleteById(savedId);
            
            Log.i(TAG, "InspectionItem operations test passed");
            return true;
            
        } catch (Exception ex) {
            Log.e(TAG, "InspectionItem operations test failed: " + ex.getMessage(), ex);
            return false;
        }
    }
    
    private static boolean testPhotoOperations(SnapInspectDatabase database) {
        Log.i(TAG, "Testing Photo operations...");
        
        try {
            // Test creating a photo
            Photo testPhoto = new Photo();
            testPhoto.sFile = "/storage/photos/test_photo.jpg";
            testPhoto.sThumb = "/storage/thumbs/test_photo_thumb.jpg";
            testPhoto.sComments = "Test photo comment";
            testPhoto.iInsID = 12345;
            testPhoto.iInsItemID = 67890;
            testPhoto.sLat = "40.7128";
            testPhoto.sLong = "-74.0060";
            testPhoto.iSize = 1024000;
            testPhoto.bUploaded = false;
            testPhoto.bDeleted = false;
            testPhoto.dtDateTime = "2023-11-15 10:30:00";
            
            long savedId = database.photoDao().insertPhoto(testPhoto);
            
            if (savedId <= 0) {
                Log.e(TAG, "Photo save returned invalid ID: " + savedId);
                return false;
            }
            
            Log.i(TAG, "Photo saved with ID: " + savedId);
            
            // Test finding the photo
            Photo foundPhoto = database.photoDao().getPhotoById(savedId);
            if (foundPhoto == null) {
                Log.e(TAG, "Could not find saved photo");
                return false;
            }
            
            if (!"/storage/photos/test_photo.jpg".equals(foundPhoto.sFile)) {
                Log.e(TAG, "Found photo has wrong file path: " + foundPhoto.sFile);
                return false;
            }
            
            Log.i(TAG, "Photo found successfully: " + foundPhoto.sFile);
            
            // Test updating the photo
            foundPhoto.bUploaded = true;
            foundPhoto.sComments = "Updated photo comment";
            database.photoDao().updatePhoto(foundPhoto);
            
            // Test listing all photos
            List<Photo> allPhotos = database.photoDao().getAllPhotos();
            Log.i(TAG, "Found " + allPhotos.size() + " total photos");
            
            // Test soft delete
            database.photoDao().softDeleteById(savedId);
            
            Log.i(TAG, "Photo operations test passed");
            return true;
            
        } catch (Exception ex) {
            Log.e(TAG, "Photo operations test failed: " + ex.getMessage(), ex);
            return false;
        }
    }
    
    private static boolean testVideoOperations(SnapInspectDatabase database) {
        Log.i(TAG, "Testing Video operations...");
        
        try {
            // Test creating a video
            Video testVideo = new Video();
            testVideo.sFile = "/storage/videos/test_video.mp4";
            testVideo.sThumb = "/storage/thumbs/test_video_thumb.jpg";
            testVideo.iInsID = 12345;
            testVideo.iInsItemID = 67890;
            testVideo.iSVideoID = 99999;
            testVideo.iSize = 5120000;
            testVideo.bUploaded = false;
            testVideo.bProcessed = false;
            testVideo.bGetURL = false;
            testVideo.bDeleted = false;
            testVideo.dtDateTime = "2023-11-15 14:30:00";
            testVideo.sFieldOne = "test field one";
            
            long savedId = database.videoDao().insertVideo(testVideo);
            
            if (savedId <= 0) {
                Log.e(TAG, "Video save returned invalid ID: " + savedId);
                return false;
            }
            
            Log.i(TAG, "Video saved with ID: " + savedId);
            
            // Test finding the video
            Video foundVideo = database.videoDao().getVideoById(savedId);
            if (foundVideo == null) {
                Log.e(TAG, "Could not find saved video");
                return false;
            }
            
            if (!"/storage/videos/test_video.mp4".equals(foundVideo.sFile)) {
                Log.e(TAG, "Found video has wrong file path: " + foundVideo.sFile);
                return false;
            }
            
            Log.i(TAG, "Video found successfully: " + foundVideo.sFile);
            
            // Test updating the video
            foundVideo.bUploaded = true;
            foundVideo.bProcessed = true;
            foundVideo.sFieldTwo = "updated field two";
            database.videoDao().updateVideo(foundVideo);
            
            // Test listing all videos
            List<Video> allVideos = database.videoDao().getAllVideos();
            Log.i(TAG, "Found " + allVideos.size() + " total videos");
            
            // Test soft delete
            database.videoDao().softDeleteById(savedId);
            
            Log.i(TAG, "Video operations test passed");
            return true;
            
        } catch (Exception ex) {
            Log.e(TAG, "Video operations test failed: " + ex.getMessage(), ex);
            return false;
        }
    }
}