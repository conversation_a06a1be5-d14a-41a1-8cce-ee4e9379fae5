package com.snapinspect.snapinspect3.util;

import javax.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;

public final class ArrayUtils {

    private ArrayUtils() {
        throw new UnsupportedOperationException("This class cannot be instantiated");
    }

    public static <T> boolean isEmpty(@Nullable List<T> list) {
        return list == null || list.isEmpty();
    }

    public static <T> boolean isNotEmpty(@Nullable List<T> list) {
        return !isEmpty(list);
    }

    public static <T> List<List<T>> split(List<T> list, int size) {
        if (list == null || list.isEmpty()) return new ArrayList<>();
        List<List<T>> result = new ArrayList<>();
        int i = 0;
        while (i < list.size()) {
            int j = Math.min(i + size, list.size());
            result.add(list.subList(i, j));
            i = j;
        }
        return result;
    }
}
