package com.snapinspect.snapinspect3.util;

import java.io.IOException;
import java.util.List;

import android.content.Context;
import android.hardware.Camera;
import android.view.Display;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.WindowManager;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;

public class VideoPreview extends SurfaceView implements SurfaceHolder.Callback {
    private final SurfaceHolder mHolder;
    public Camera mCamera;
    private final Context oContext;
    public VideoPreview(Context context, Camera camera) {
        super(context);
        oContext = context;
        mCamera = camera;

        // Install a SurfaceHolder.Callback so we get notified when the
        // underlying surface is created and destroyed.
        mHolder = getHolder();
        mHolder.addCallback(this);
        // deprecated setting, but required on Android versions prior to 3.0
        mHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
    }

    public void surfaceCreated(SurfaceHolder holder) {
        // The Surface has been created, now tell the camera where to draw the preview.
        try {
            Camera.Parameters oParams= mCamera.getParameters();
            Display display = ((WindowManager)oContext.
                    getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay();
            int screenOrientation = display.getRotation();
            //Log.v("Scaaa", "" + screenOrientation);
            switch (screenOrientation)
            {
                default:
                case 0: // Portrait
                    mCamera.setDisplayOrientation(90);
                    oParams.setRotation(90);
                    //oParams.setPictureSize(iWidth, iHeight);
                    break;
                case 3: // Landscape right
                    mCamera.setDisplayOrientation(180);
                    oParams.setRotation(180);
                    //oParams.setPictureSize(iHeight, iWidth);
                    break;
                case 1: // Landscape left
                    mCamera.setDisplayOrientation(0);
                    oParams.setRotation(0);
                    //oParams.setPictureSize(iHeight, iWidth);
                    break;
            }
            List<String> focusModes = oParams.getSupportedFocusModes();
            if (focusModes.contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO)){
                oParams.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO);
            }
            else if (focusModes.contains(Camera.Parameters.FOCUS_MODE_AUTO)) {
            }
            oParams.setPreviewSize(640,480);
            mCamera.setParameters(oParams);
            mCamera.setPreviewDisplay(holder);
            mCamera.startPreview();
        } catch (IOException e) {

        }
    }

    public void surfaceDestroyed(SurfaceHolder holder) {
        try{
            if (mCamera != null){
                mCamera.stopPreview();
            }
        }
        catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        finally{
            mCamera.release();
            mCamera = null;
        }
    }

    public void surfaceChanged(SurfaceHolder holder, int format, int w, int h) {
        if (mHolder.getSurface() == null){
            return;
        }

        try {
            mCamera.stopPreview();
        } catch (Exception e){

        }

        try {
            mCamera.setPreviewDisplay(mHolder);
            mCamera.startPreview();

        } catch (Exception e){

        }
    }
}
