package com.snapinspect.snapinspect3.util;

import android.content.Context;
import androidx.annotation.StringRes;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;

public final class TextViewUtils {

    public static void updateText(Context ctx, TextView textView, @StringRes int resId) {
        if (textView == null) return;
        String text = ctx.getResources().getString(resId);
        textView.setText(text);
        textView.setVisibility(StringUtils.isEmpty(text) ? View.GONE : View.VISIBLE);
    }

    public static void updateText(TextView textView, String text) {
        if (textView == null) return;
        textView.setText(text);
        textView.setVisibility(StringUtils.isEmpty(text) ? View.GONE : View.VISIBLE);
    }

    public static void updateEditText(Context ctx, EditText editText, @StringRes int resId) {
        if (editText != null) editText.setText(ctx.getResources().getText(resId));
    }

    public static void updateEditText(EditText editText, CharSequence text) {
        if (editText != null) editText.setText(text);
    }
}
