package com.snapinspect.snapinspect3.util;

import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.widget.ImageView;

public class ImageViewUtils {
    public static RectF getImageBounds(ImageView imageView) {
        Drawable drawable = imageView.getDrawable();
        if (drawable == null) return null;

        RectF imageBounds = new RectF();
        // Original dimensions of the drawable
        float drawableWidth = drawable.getIntrinsicWidth();
        float drawableHeight = drawable.getIntrinsicHeight();

        // Actual dimensions of the ImageView
        float imageViewWidth = imageView.getWidth() - imageView.getPaddingLeft() - imageView.getPaddingRight();
        float imageViewHeight = imageView.getHeight() - imageView.getPaddingTop() - imageView.getPaddingBottom();

        // Calculate the scaling factor
        float scale;
        float dx = 0, dy = 0;

        switch (imageView.getScaleType()) {
            case FIT_CENTER:
            case CENTER_INSIDE:
                scale = Math.min(imageViewWidth / drawableWidth, imageViewHeight / drawableHeight);
                dx = (imageViewWidth - drawableWidth * scale) * 0.5f;
                dy = (imageViewHeight - drawableHeight * scale) * 0.5f;
                break;
            case CENTER_CROP:
                scale = Math.max(imageViewWidth / drawableWidth, imageViewHeight / drawableHeight);
                dx = (imageViewWidth - drawableWidth * scale) * 0.5f;
                dy = (imageViewHeight - drawableHeight * scale) * 0.5f;
                break;
            case FIT_XY:
                imageBounds.right = imageViewWidth;
                imageBounds.bottom = imageViewHeight;
                return imageBounds;
            default:
                imageBounds.right = imageViewWidth;
                imageBounds.bottom = imageViewHeight;
                return imageBounds;
        }

        // Applying the scale and translation to the image bounds
        imageBounds.left = imageView.getPaddingLeft() + dx;
        imageBounds.top = imageView.getPaddingTop() + dy;
        imageBounds.right = imageBounds.left + drawableWidth * scale;
        imageBounds.bottom = imageBounds.top + drawableHeight * scale;

        return imageBounds;
    }
}
