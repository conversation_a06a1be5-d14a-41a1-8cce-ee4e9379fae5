package com.snapinspect.snapinspect3.util;

import android.content.Context;
import android.graphics.*;
import android.graphics.drawable.Drawable;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.util.Base64;
import android.util.Size;
import androidx.annotation.NonNull;
import androidx.camera.core.ImageProxy;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Target;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;


/**
 * <AUTHOR> 10.11.15
 */
public class BitmapUtils {

    public enum IMAGE_FORMAT {
        JPEG, PNG
    }

    public interface OnBitmapLoadedListener {
        void onBitmapLoaded(Bitmap bitmap, Error error);
    }

    @NonNull
    public static Rect getBitmapBounds(@NonNull Context context, @NonNull Uri uri) {
        Validator.notNullOrThrow(context, uri);

        Rect bounds = new Rect();
        InputStream is = null;
        try {
            is = context.getContentResolver().openInputStream(uri);

            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeStream(is, null, options);

            bounds.right = options.outWidth;
            bounds.bottom = options.outHeight;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            SilentCloser.close(is);
        }

        return bounds;
    }
    /**
     * Decodes immutable bitmap and keeps original width and height.
     */
    public static Bitmap decodeBitmap(Context context, Uri uri) {
        InputStream is = null;
        Bitmap bitmap = null;
        try {
            is = context.getContentResolver().openInputStream(uri);
            bitmap = BitmapFactory.decodeStream(is);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            SilentCloser.close(is);
        }
        return bitmap;
    }

    /**
     * Decodes immutable bitmap that keeps aspect-ratio and spans most within the given rectangle.
     */
    public static Bitmap decodeBitmap(Context context, Uri uri, int width, int height) {
        // TODO: Take max pixels allowed into account for calculation to avoid possible OOM.
        Rect bounds = getBitmapBounds(context, uri);
        int sampleSize = Math.max(bounds.width() / width, bounds.height() / height);
        sampleSize = Math.min(sampleSize,
                              Math.max(bounds.width() / height, bounds.height() / width));

        InputStream is = null;
        Bitmap bitmap = null;
        try {
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inSampleSize = Math.max(sampleSize, 1);
            options.inPreferredConfig = Bitmap.Config.ARGB_8888;

            is = context.getContentResolver().openInputStream(uri);
            bitmap = BitmapFactory.decodeStream(is, null, options);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } finally {
            SilentCloser.close(is);
        }

        // Scale down the sampled bitmap if it's still larger than the desired dimension.
        if (bitmap != null) {
            float scale = Math.min((float) width / bitmap.getWidth(),
                                   (float) height / bitmap.getHeight());
            scale = Math.max(scale, Math.min((float) height / bitmap.getWidth(),
                                             (float) width / bitmap.getHeight()));
            if (scale < 1) {
                Matrix m = new Matrix();
                m.setScale(scale, scale);
                Bitmap transformed = createBitmap(bitmap, m);
                RecycleImage(bitmap);
                return transformed;
            }
        }

        return bitmap;
    }

    /**
     * Returns an immutable bitmap from subset of source bitmap transformed by the given matrix.
     */
    public static Bitmap createBitmap(Bitmap bitmap, Matrix m) {
        // TODO: Re-implement createBitmap to avoid ARGB -> RBG565 conversion on platforms
        // prior to honeycomb.
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), m, true);
    }

    public static Bitmap decodeBitmap(ImageProxy image) {
        ByteBuffer byteBuffer = image.getPlanes()[0].getBuffer();
        byteBuffer.rewind();
        byte[] bytes = new byte[byteBuffer.capacity()];
        byteBuffer.get(bytes);
        byte[] clonedBytes = bytes.clone();
        image.close();

        Bitmap decodedImage = BitmapFactory.decodeByteArray(clonedBytes, 0, clonedBytes.length);
        Bitmap croppedImage = cropBitmap(decodedImage, image.getCropRect());
        Bitmap bitmap1 = rotateBitmap(croppedImage, image.getImageInfo().getRotationDegrees());
        Bitmap bitmap = bitmap1.copy(bitmap1.getConfig(), true);
        RecycleImage(bitmap1);
        RecycleImage(croppedImage);
        RecycleImage(decodedImage);
        return bitmap;
    }
    private static void RecycleImage(Bitmap oImage){
        try {
            if (oImage != null && !oImage.isRecycled()) {
                oImage.recycle();
                oImage = null;
            }
        }catch(Exception ex){}
    }

    public static Bitmap fixCameraRotationIfNeed(Context ctx, Bitmap bitmap) {
        int rotation = CommonHelper.getInt(CommonHelper.GetPreferenceString(ctx, Constants.Settings.bFixCamRotation));
        if (rotation == 0) return bitmap;
        return rotateBitmap(bitmap, rotation);
    }

    public static Bitmap appendTextsIfNeed(Context ctx, Bitmap bitmap, String text) {
        Bitmap targetBitmap = bitmap.isMutable() ? bitmap : bitmap.copy(Bitmap.Config.ARGB_8888, true);
        Canvas canvas = new Canvas(targetBitmap); //bmp is the bitmap to dwaw into
        Paint paint = new Paint();
        paint.setColor(Color.RED);
        paint.setTextSize(30);
        paint.setTypeface(Typeface.create(Typeface.DEFAULT, Typeface.BOLD));
        paint.setTextAlign(Paint.Align.LEFT);

        List<String> texts = Arrays.asList(text.split("\n"));
        Collections.reverse(texts);
        int x = 10, y = targetBitmap.getHeight() - 20;
        for (String line: texts) {
            canvas.drawText(line, x, y, paint);
            y -= paint.descent() - paint.ascent();
        }
        return targetBitmap;
    }

    //c : overlap bitmap, s: background bitmap
    public static Bitmap combineImages(Bitmap c, Bitmap s, int iPhotoSize) {
        try {
            if (s == null) return c;

            Bitmap cs = Bitmap.createBitmap(iPhotoSize, iPhotoSize, Bitmap.Config.ARGB_8888);
            Matrix matrix = new Matrix();
            matrix.postScale((float) iPhotoSize / s.getWidth(), (float) iPhotoSize / s.getHeight());
            // create a new bitmap from the original using the matrix to transform the result
            Bitmap newS = Bitmap.createBitmap(s, 0, 0, s.getWidth(),
                    s.getHeight(), matrix, false);

            Canvas comboImage = new Canvas(cs);

            comboImage.drawBitmap(newS, 0f, 0f, null);
            try {
                if (c != null)
                    comboImage.drawBitmap(c,
                            (iPhotoSize - c.getWidth()) >> 1,
                            (iPhotoSize - c.getHeight()) >> 1,
                            null);
            } catch (Exception exception) {
                ai_BugHandler.logException(exception);
            }

            return cs;
        } catch (Exception e) {
            ai_BugHandler.logException(e);
        }

        return null;
    }

    private static Bitmap cropBitmap(Bitmap bitmap, Rect rect) {
        if (rect == null) return bitmap;
        
        // Ensure rect is within bitmap bounds
        int left = Math.max(0, rect.left);
        int top = Math.max(0, rect.top);
        int width = Math.min(rect.width(), bitmap.getWidth() - left);
        int height = Math.min(rect.height(), bitmap.getHeight() - top);
        
        // Check if resulting dimensions are valid
        if (width <= 0 || height <= 0) return bitmap;
        
        return Bitmap.createBitmap(bitmap, left, top, width, height);
    }

    public static Bitmap rotateBitmap(Bitmap bitmap, int rotationDegrees) {
        int width = bitmap.getWidth(), height = bitmap.getHeight();

        Matrix matrix = new Matrix();
        matrix.postRotate(rotationDegrees);

        return Bitmap.createBitmap(bitmap, 0, 0, width, height, matrix, true);
    }

    public static Bitmap scaleBitmap(Bitmap image, Size newSize) {
        int maxWidth = newSize.getWidth(), maxHeight = newSize.getHeight();
        if (maxWidth > 0 && maxHeight > 0) {
            int width = image.getWidth();
            int height = image.getHeight();
            float ratioBitmap = (float) width / (float) height;
            float ratioMax = (float) maxWidth / (float) maxHeight;

            int finalWidth = maxWidth, finalHeight = maxHeight;
            if (ratioMax > ratioBitmap) {
                finalWidth = (int) ((float)maxHeight * ratioBitmap);
            } else {
                finalHeight = (int) ((float)maxWidth / ratioBitmap);
            }
            image = Bitmap.createScaledBitmap(image, finalWidth, finalHeight, true);
        }
        return image;
    }

    public static int calculateInSampleSize(BitmapFactory.Options options, int reqWidth, int reqHeight) {
        // Raw height and width of image
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {

            final int halfHeight = height / 2;
            final int halfWidth = width / 2;

            // Calculate the largest inSampleSize value that is a power of 2 and keeps both
            // height and width larger than the requested height and width.
            while ((halfHeight / inSampleSize) >= reqHeight
                    && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }

        return inSampleSize;
    }

    public static void getBitmapFromFilePath(String imageFilePath, OnBitmapLoadedListener listener) {
        if (imageFilePath == null || imageFilePath.isEmpty()) {
            listener.onBitmapLoaded(null, new Error("Image file path cannot be null or empty"));
            return;
        }

        File file = new File(imageFilePath);
        if (!file.exists()) {
            listener.onBitmapLoaded(null, new Error("Image file doesn't exist in the provided path"));
            return;
        }

        new Handler(Looper.myLooper()).post(new Runnable() {
            @Override
            public void run() {
                BitmapFactory.Options bmOptions = new BitmapFactory.Options();
                Bitmap bitmap = BitmapFactory.decodeFile(file.getAbsolutePath(), bmOptions);
                listener.onBitmapLoaded(bitmap, null);
            }
        });
    }

    public static String encodeBase64(Bitmap bitmap, Bitmap.CompressFormat format) {
        if (bitmap == null) return null;
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            bitmap.compress(format, 100, outputStream);
            byte[] imageBytes = outputStream.toByteArray();
            // convert the base64 string with prefix
            String imageString = Base64.encodeToString(imageBytes, Base64.DEFAULT);
            switch (format) {
                case JPEG:
                    imageString = "data:image/jpeg;base64," + imageString;
                    break;
                case PNG:
                    imageString = "data:image/png;base64," + imageString;
                    break;
                default:
                    break;
            }
            return imageString;

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Bitmap decodeBase64(String imageBase64) {
        if (StringUtils.isEmpty(imageBase64)) return null;
        // remove the prefix
        if (imageBase64.startsWith("data:image/")) {
            imageBase64 = imageBase64.substring(imageBase64.indexOf(",") + 1);
        }
        Bitmap bitmap = null;
        try {
            // create bitmap from base64 string
            byte[] decodedString = Base64.decode(imageBase64, Base64.DEFAULT);
            bitmap = BitmapFactory.decodeByteArray(decodedString, 0, decodedString.length);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return bitmap;
    }
}
