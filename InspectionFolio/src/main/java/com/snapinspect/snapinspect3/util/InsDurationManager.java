package com.snapinspect.snapinspect3.util;

import android.app.Activity;
import android.os.Handler;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.app.App;

import java.util.Timer;
import java.util.TimerTask;

public class InsDurationManager {

    public interface Delegate {
        void updateInsDuration(String duration);
    }

    private int cost;

    private final int insId;
    private final Activity mActivity;
    private final Delegate mDelegate;

    private final Handler handler = new Handler();
    private Timer mTimer;

    public InsDurationManager(Activity activity, int oInsId, Delegate delegate) {
        this.mActivity = activity;
        this.insId = oInsId;
        this.mDelegate = delegate;
    }

    public void start() {
        stop();
        mTimer = new Timer();
        cost = getInsCost();
        mTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                handler.post(() -> {
                    if(!((CommonHelper.isAppInBackground(App.getContext())))) {
                        int hours = cost / 3600;
                        int minutes = (cost % 3600) / 60;
                        int seconds = cost % 60;

                        String timeString = String.format("%02d:%02d:%02d", hours, minutes, seconds);
                        mDelegate.updateInsDuration(timeString);

                        // save time on DB
                        // if (cost % 3 == 0) updateInsCost();

                        cost ++;
                    }
                });
            }
        }, 0, 1000);
    }

    public void stop() {
        if (mTimer != null) {
            mTimer.cancel();
            mTimer = null;
            updateInsCost();
        }
    }

    private void updateInsCost() {
        try {
            ai_Inspection oInspection = CommonDB.findInspectionById(insId);
            CommonHelper.trackEvent(mActivity, "UpdateInsCost - InsID:" + insId, null);
            if (oInspection != null) {

                oInspection.sCustomOne = CommonJson.AddJsonKeyValue(oInspection.sCustomOne, "InsTimer", "" + cost);
                CommonDB.saveInspection(oInspection);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        //Log.d("Saved Duration", cost + "");
    }

    private int getInsCost() {
        int cost = 0;
        try {
            ai_Inspection oInspection = CommonDB.findInspectionById(insId);
            if (oInspection != null && !StringUtils.isEmpty(oInspection.sCustomOne)) {
                String sCustomOne = oInspection.sCustomOne;
                String sTimer = CommonJson.GetJsonKeyValue("InsTimer", sCustomOne);
                cost = CommonHelper.getInt(sTimer);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return cost;
    }

    public void saveInsTime() {
        updateInsCost();
    }
}
