package com.snapinspect.snapinspect3.util;

import android.app.Activity;
import androidx.appcompat.widget.SearchView;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.os.Handler;
import android.os.Looper;
import java.lang.ref.WeakReference;

public class ThrottledSearch {

    public interface Delegate {
        void onThrottledSearch(String searchTerm);
    }

    private static final int DEFAULT_DELAY = 200;

    private final int mDelayMilliseconds;
    private final Delegate mDelegate;
    private String mSearchTerm;
    private final Handler mHandler;
    private final WeakReference<Activity> mActivityRef;
    private WeakReference<View> mBindViewRef;
    private TextWatcher mEditTextWatcher;
    private SearchView.OnQueryTextListener mQueryTextListener;
    private Runnable mSearchRunnable;

    public ThrottledSearch(Activity activity, Delegate delegate, int milliseconds) {
        mActivityRef = new WeakReference<>(activity);
        mDelegate = delegate;
        mDelayMilliseconds = milliseconds;
        mHandler = new Handler(Looper.getMainLooper());
        mSearchTerm = "";
    }

    public ThrottledSearch(Activity activity, Delegate delegate) {
        this(activity, delegate, DEFAULT_DELAY);
    }

    public void onTextChanged(CharSequence charSequence) {
        mHandler.removeCallbacks(mSearchRunnable);
        mSearchTerm = charSequence.toString();

        mSearchRunnable = () -> {
            Activity activity = mActivityRef.get();
            if (mDelegate != null && activity != null && !activity.isFinishing()) {
                mDelegate.onThrottledSearch(mSearchTerm);
            }
        };

        mHandler.postDelayed(mSearchRunnable, mDelayMilliseconds);
    }

    public void unbind() {
        mHandler.removeCallbacks(mSearchRunnable);
        if (mBindViewRef != null) {
            View bindView = mBindViewRef.get();
            if (bindView instanceof EditText && mEditTextWatcher != null) {
                ((EditText) bindView).removeTextChangedListener(mEditTextWatcher);
            } else if (bindView instanceof SearchView && mQueryTextListener != null) {
                ((SearchView) bindView).setOnQueryTextListener(null);
            }
        }
        mBindViewRef = null;
        mEditTextWatcher = null;
        mQueryTextListener = null;
    }

    public void bindTo(final EditText editText) {
        unbind();
        mBindViewRef = new WeakReference<>(editText);
        mEditTextWatcher = new SimpleTextWatcher() {
            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                ThrottledSearch.this.onTextChanged(charSequence);
            }
        };
        editText.addTextChangedListener(mEditTextWatcher);
    }

    public void bindTo(final SearchView searchView) {
        unbind();
        mBindViewRef = new WeakReference<>(searchView);
        mQueryTextListener = new SearchView.OnQueryTextListener() {
            @Override
            public boolean onQueryTextSubmit(String query) {
                return true;
            }

            @Override
            public boolean onQueryTextChange(String newText) {
                ThrottledSearch.this.onTextChanged(newText);
                return true;
            }
        };
        searchView.setOnQueryTextListener(mQueryTextListener);
    }
}