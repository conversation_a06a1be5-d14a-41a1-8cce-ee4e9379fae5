package com.snapinspect.snapinspect3.util;

import android.content.Context;
import android.os.Build;
import android.text.Html;
import android.text.Spanned;
import androidx.core.text.HtmlCompat;
import org.jetbrains.annotations.Nullable;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

public class StringUtils {
    public static String getFirstLetters(String sName) {
        if (StringUtils.isEmpty(sName)) return null;
        List<String> letters = new ArrayList<>();
        String[] names = sName.split(" ");
        if (names.length > 0) {
            String first = names[0];
            if (!first.isEmpty()) letters.add(first.substring(0, 1).toUpperCase());
            if (names.length > 1) {
                String last = names[names.length - 1];
                if (!last.isEmpty()) letters.add(last.substring(0, 1).toUpperCase());
            }
        }
        return String.join("", letters);
    }

    public static Spanned convertHtml(String html) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            return Html.fromHtml(html, HtmlCompat.FROM_HTML_MODE_LEGACY);
        } else {
            return Html.fromHtml(html);
        }
    }

    public static String getURLPath(String url) {
        if (StringUtils.isEmpty(url)) return null;
        try {
            return new URL(url).getPath();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getURLQueryParam(String url, String key) {
        return getURLQueryParam(url, key, null);
    }

    public static String getURLQueryParam(String url, String key, String defaultValue) {
        if (StringUtils.isEmpty(url)) return null;
        try {
            String query = new URL(url).getQuery();
            return getQueryParam(query, key, defaultValue);
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        return defaultValue;
    }

    public static String getQueryParam(String query, String key, String defaultValue) {
        if (StringUtils.isEmpty(query)) return null;
        String[] params = query.split("&");
        for (String param : params) {
            String[] pair = param.split("=");
            if (pair.length == 2 && pair[0].equals(key)) {
                return pair[1];
            }
        }
        return defaultValue;
    }

   public static String decodeBase64(String base64) {
       try {
           if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
               byte[] bytes = Base64.getDecoder().decode(base64);
               return new String(bytes);
           }
       } catch (Exception e) {
           e.printStackTrace();
       }
       return null;
   }

    public static String getFileContent(Context context, String filePath) {
        StringBuilder stringBuilder = new StringBuilder();
        try {
            InputStream inputStream = context.getAssets().open(filePath);
            BufferedReader bufferedReader = new BufferedReader(
                    new InputStreamReader(inputStream, StandardCharsets.UTF_8));

            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
                stringBuilder.append('\n');
            }

            bufferedReader.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return stringBuilder.toString();
    }

   /**
    * Checks if a given string is empty or null, it will also trim the string before checking.
    *
    * @param text The string to check.
    * @return {@code true} if the string is empty or null, {@code false} otherwise.
    */
   public static boolean isEmpty(String text) {
        return text == null || text.trim().isEmpty();
   }

    /**
     * Returns the first non-empty string out of two given strings.
     *
     * @param a The first string to check.
     * @param b The second string to check.
     * @return The first non-empty string. If both strings are empty or null, the method returns an empty string.
     */
    public static String ifEmpty(String a, String b) {
        return isEmpty(a) ? b : a;
    }

    /**
     * Returns the first non-empty string out of three given strings.
     *
     * @param a The first string to check
     * @param b The second string to check
     * @param c The third string to check
     * @return The first non-empty string. If all the strings are empty or null, the method returns an empty string.
     */
    public static String ifEmpty(String a, String b, String c) {
        return isEmpty(a) ? (isEmpty(b) ? c : b) : a;
    }

    /**
     * Replaces the last occurrence of the target string with the replacement string
     *
     * @param text        The text to search and replace in
     * @param target      The target string to be replaced
     * @param replacement The replacement string
     * @return The resulting string after replacing the last occurrence of the target string with the replacement string
     */
    public static String replaceLast(String text, String target, String replacement) {
        int index = text.lastIndexOf(target);
        if (index == -1) return text;
        return text.substring(0, index) + replacement + text.substring(index + target.length());
    }

    @Nullable
    public static String ensureHttpsScheme(@Nullable String sURL) {
        if (StringUtils.isEmpty(sURL)) return null;
        if (!sURL.startsWith("http://") && !sURL.startsWith("https://")) {
            sURL = "https://" + sURL;
        }
        return sURL.replaceFirst("^http://", "https://");
    }
}
