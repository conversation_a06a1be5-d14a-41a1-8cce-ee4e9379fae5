package com.snapinspect.snapinspect3.util;

import android.content.Context;
import android.content.pm.PackageManager;
import android.hardware.Camera;
import android.util.Log;
import android.view.Display;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.WindowManager;

import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;

import java.io.IOException;
import java.util.List;

public class CameraPreview extends SurfaceView implements SurfaceHolder.Callback {
    private final SurfaceHolder mHolder;
    public Camera mCamera;
    boolean bFlash = false;
    boolean bAutoFocus = false;
    private final int iPhotoSize = 900;
    private final Context oContext;
    private int iWidth = 0;
    private int iHeight = 0;
    public CameraPreview(Context context, Camera camera) {
        super(context);
        mCamera = camera;
        oContext = context;
        if (CommonHelper.GetPreferenceString(oContext, "iWidth") != null && (CommonHelper.GetPreferenceString(oContext, "iHeight")) != null){
       //     iWidth = Integer.parseInt(CommonHelper.GetPreferenceString(oContext, "iWidth"));
       //     iHeight = Integer.parseInt(CommonHelper.GetPreferenceString(oContext, "iHeight"));
        }
        // Install a SurfaceHolder.Callback so we get notified when the
        // underlying surface is created and destroyed.
        mHolder = getHolder();
        mHolder.addCallback(this);
        // deprecated setting, but required on Android versions prior to 3.0
        //mHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);
        bFlash = (context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA_FLASH));
        if (android.os.Build.MODEL.equalsIgnoreCase("Nexus 7")){
            bFlash = false;
        }
        bAutoFocus = (context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA_AUTOFOCUS));

    }
//Later on Click to Focus   remember to do that

    public void BackupCamera( SurfaceHolder holder) {
        try {
            Camera.Parameters oParams = mCamera.getParameters();
            Camera.Size oSize = oParams.getPreferredPreviewSizeForVideo();
            Display display = ((WindowManager) oContext.
                    getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay();
            int screenOrientation = display.getRotation();
            //Log.v("Scaaa", "" + screenOrientation);
            switch (screenOrientation) {
                default:
                case 0: // Portrait
                    mCamera.setDisplayOrientation(90);
                    oParams.setRotation(90);
                    //oParams.setPictureSize(iWidth, iHeight);
                    break;
                case 3: // Landscape right
                    mCamera.setDisplayOrientation(180);
                    oParams.setRotation(180);
                    //oParams.setPictureSize(iHeight, iWidth);
                    break;
                case 1: // Landscape left
                    mCamera.setDisplayOrientation(0);
                    oParams.setRotation(0);
                    //oParams.setPictureSize(iHeight, iWidth);
                    break;
            }
            oParams.setPictureSize(oSize.width, oSize.height);
            if (bFlash){
                oParams.setFlashMode(Camera.Parameters.FLASH_MODE_AUTO);
            }
            oParams.setJpegQuality(100);
            mCamera.setParameters(oParams);
            try {
                mCamera.setPreviewDisplay(holder);
            } catch (IOException e) {
                e.printStackTrace();
            }
            mCamera.startPreview();
        }catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Per", "CameraPreview.BackupCamera", ex);
        }
    }
    public void surfaceCreated(SurfaceHolder holder) {
        try{
            Camera.Parameters oParams= mCamera.getParameters();
        //   if (!android.os.Build.MODEL.equalsIgnoreCase("Nexus 7")){





                if (iWidth == 0 && iHeight == 0){
                    List<Camera.Size> sizes = oParams.getSupportedPictureSizes();
                    int iCurrentMin = 100000000;
                    Camera.Size oPerfectSize = oParams.getPreviewSize();
                    for (int i=0;i<sizes.size();i++){
                        Camera.Size oSize = sizes.get(i);
                        int iMax = oSize.width + oSize.height;
                        if (iMax > 1800 && oSize.width > iPhotoSize && oSize.height > iPhotoSize && iMax < iCurrentMin){
                            iCurrentMin = iMax;
                            oPerfectSize = oSize;
                        }
                    }
                    iWidth = oPerfectSize.width;
                    iHeight = oPerfectSize.height;
                    CommonHelper.SavePreference(oContext, "iWidth", "" + iWidth);
                    CommonHelper.SavePreference(oContext, "iHeight", "" + iHeight);

                }
                Display display = ((WindowManager)oContext.
                        getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay();
                int screenOrientation = display.getRotation();
                //Log.v("Scaaa", "" + screenOrientation);
                switch (screenOrientation)
                {
                    default:
                    case 0: // Portrait
                        mCamera.setDisplayOrientation(90);
                        oParams.setRotation(90);
                        //oParams.setPictureSize(iWidth, iHeight);
                        break;
                    case 3: // Landscape right
                        mCamera.setDisplayOrientation(180);
                        oParams.setRotation(180);
                        //oParams.setPictureSize(iHeight, iWidth);
                        break;
                    case 1: // Landscape left
                        mCamera.setDisplayOrientation(0);
                        oParams.setRotation(0);
                        //oParams.setPictureSize(iHeight, iWidth);
                        break;
                }
                oParams.setPictureSize(iWidth, iHeight);
          //  }
         //   else{
          //      oParams.setPictureSize(1920, 1080);
          //  }
            if (bFlash){
                oParams.setFlashMode(Camera.Parameters.FLASH_MODE_AUTO);
            }
            oParams.setJpegQuality(100);
            mCamera.setParameters(oParams);
            try {
                mCamera.setPreviewDisplay(holder);
            } catch (IOException e) {
                e.printStackTrace();
            }
            mCamera.startPreview();
        }catch (Exception ex){
            Log.v("IF_EX", "CameraPreview.Created", ex);
            mCamera.stopPreview();
            //BackupCamera(holder);
           // mCamera.startPreview();
        }
    }

    public void surfaceDestroyed(SurfaceHolder holder) {
        // Surface will be destroyed when we return, so stop the preview.

        if (mCamera != null) {
            // Call stopPreview() to stop updating the preview surface.
            stopPreviewAndFreeCamera();
        }
    }
    boolean previewing = false;
    public void surfaceChanged(SurfaceHolder holder, int format, int w, int h) {
        // Now that the size is known, set up the camera parameters and begin
        // the preview.
        try{
            if (previewing){
                mCamera.stopPreview();
                previewing = false;
            }
            if (mCamera != null) {

                Camera.Parameters oParams = mCamera.getParameters();
                requestLayout();

                mCamera.setParameters(oParams);
                mCamera.setPreviewDisplay(holder);
                // Important: Call startPreview() to start updating the preview surface.
                // Preview must be started before you can take a picture.
                mCamera.startPreview();
                previewing = true;
            }
        }catch (Exception ex){
            Log.v("IF_EX", "CameraPreview.surfaceChanged.1", ex);
            mCamera.stopPreview();
            mCamera.startPreview();
            Log.v("IF_EX", "CameraPreview.surfaceChanged.2", ex);
        }
    }
    /**
     * When this function returns, mCamera will be null.
     */
    private void stopPreviewAndFreeCamera() {

        if (mCamera != null) {
            // Call stopPreview() to stop updating the preview surface.
            mCamera.stopPreview();
            mCamera.setPreviewCallback(null);
            mHolder.removeCallback(this);
            // Important: Call release() to release the camera for use by other
            // applications. Applications should release the camera immediately
            // during onPause() and re-open() it during onResume()).
            mCamera.release();

            mCamera = null;
        }
    }
}
