package com.snapinspect.snapinspect3.util

import java.math.RoundingMode
import java.text.DecimalFormat
import java.text.NumberFormat
import java.util.*

object NumberUtils {
    private val DEFAULT_LOCALE = Locale.US
    private const val DEFAULT_CURRENCY_CODE = "USD"
    private const val DEFAULT_SYMBOL = "$"
    private const val DEFAULT_MAX_FRACTION_DIGITS = 2

    /**
     * Formats a number as currency with customizable parameters.
     */
    fun formatCurrency(
        value: Double,
        locale: Locale = DEFAULT_LOCALE,
        currencyCode: String = DEFAULT_CURRENCY_CODE,
        symbol: String? = DEFAULT_SYMBOL,
        maxFractionDigits: Int = DEFAULT_MAX_FRACTION_DIGITS,
        useGrouping: Boolean = true,
        roundingMode: RoundingMode = RoundingMode.HALF_UP
    ): String = NumberFormat.getCurrencyInstance(locale).apply {
        this.maximumFractionDigits = maxFractionDigits
        this.currency = Currency.getInstance(currencyCode)
        this.roundingMode = roundingMode
        (this as? DecimalFormat)?.apply {
            decimalFormatSymbols = decimalFormatSymbols.apply {
                currencySymbol = symbol.orEmpty()
            }
            isGroupingUsed = useGrouping
        }
    }.format(value)

    /**
     * Formats a number as currency without a thousand separators.
     */
    @JvmOverloads
    fun formatCurrencyWithoutGrouping(
        value: Double,
        locale: Locale = DEFAULT_LOCALE,
        currencyCode: String = DEFAULT_CURRENCY_CODE,
        symbol: String = DEFAULT_SYMBOL,
        maxFractionDigits: Int = DEFAULT_MAX_FRACTION_DIGITS
    ): String = formatCurrency(
        value = value,
        locale = locale,
        currencyCode = currencyCode,
        symbol = symbol,
        maxFractionDigits = maxFractionDigits,
        useGrouping = false
    )

    /**
     * Formats a number as currency without a symbol.
     */
    @JvmStatic
    fun formatCurrencyNoSymbol(value: Double): String = formatCurrencyWithoutGrouping(
        value = value,
        symbol = ""
    )
}