package com.snapinspect.snapinspect3.util;

import android.content.pm.PackageManager;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentActivity;

public class PermissionUtils {

    public interface PermissionGrantedCompletion {
        void onPermissionGranted(boolean granted);
    }

    private static PermissionGrantedCompletion permissionGrantedCompletion;
    private static ActivityResultLauncher<String> requestPermissionLauncher;
    private static FragmentActivity compatActivity;

    public static void registerRequestPermissionLauncher(FragmentActivity activity) {
        compatActivity = activity;
        requestPermissionLauncher = activity.registerForActivityResult(
                new ActivityResultContracts.RequestPermission(),
                result -> {
                    if (permissionGrantedCompletion != null) {
                        permissionGrantedCompletion.onPermissionGranted(result);
                    }
                }
        );
    }

    /**
     * Requests a permission from the user.
     * @param permission The permission to request.
     * @param completion Callback when the permission is granted or denied.
     */
    public static void requestPermission(String permission, PermissionGrantedCompletion completion) {
        assert requestPermissionLauncher != null ;

        permissionGrantedCompletion = completion;
        if (hasPermission(permission)) {
            if (completion != null) {
                completion.onPermissionGranted(true);
            }
        } else if (ActivityCompat.shouldShowRequestPermissionRationale(compatActivity, permission)) {
            // the last time to request permission and if user denied again, the permission will be blocked
            requestPermissionLauncher.launch(permission);
        } else {
            requestPermissionLauncher.launch(permission);
        }
    }

    /**
     * Checks if the app has a permission.
     * @param permission The permission to check.
     * @return True if the app has the permission, false otherwise.
     */
    private static boolean hasPermission(String permission) {
        return ContextCompat.checkSelfPermission(compatActivity, permission) == PackageManager.PERMISSION_GRANTED;
    }

}

