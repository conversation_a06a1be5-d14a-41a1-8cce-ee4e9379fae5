package com.snapinspect.snapinspect3.util;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.database.Cursor;
import android.location.Location;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.os.ParcelFileDescriptor;
import android.provider.MediaStore;
import android.webkit.MimeTypeMap;
import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.exifinterface.media.ExifInterface;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonValidate;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.app.App;

import java.io.*;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;

final public class FileUtils {

    // Checks if have to use Storage Access Framework (SAF)
    public static boolean isExternalStorageLegacySupported() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q && !Environment.isExternalStorageLegacy();
    }

    public static boolean shouldUseScopedStorage() {
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q;
    }

    public static String getRootPath() {
        if (shouldUseScopedStorage()) return getExternalStoragePrivateDirectory();
        else return getExternalStorageDirectory();
    }

    // the scoped storage root directory, which will be deleted once app was uninstalled
    public static String getExternalStoragePrivateDirectory() {
        return App.getContext().getExternalFilesDir(null) + "/.SnapInspect3";
    }

    // the legacy root directory
    public static String getExternalStorageDirectory() {
        return Environment.getExternalStorageDirectory() + "/.SnapInspect3";
    }

    public static String getMimeType(String url) {
        String type = null;
        String extension = MimeTypeMap.getFileExtensionFromUrl(url);
        if (extension != null) {
            type = MimeTypeMap.getSingleton().getMimeTypeFromExtension(extension);
        }
        return type;
    }

    public static String validFilePath(String sFile) {
        if (StringUtils.isEmpty(sFile)) return "";
        File oFile = new File(sFile);
        if (oFile.exists()){
            return sFile;
        }
        else{
            String sTempFile = FileUtils.getExternalStorageDirectory() + "/" + oFile.getName();
            File oFileTemp = new File(sTempFile);
            if (oFileTemp.exists()){
                return sTempFile;
            }
            else{
                String sTempFile1 = FileUtils.getExternalStoragePrivateDirectory()+ "/" + oFile.getName();
                File oFileTemp1 = new File(sTempFile1);
                if (oFileTemp1.exists()){
                    return sTempFile1;
                }
            }

        }
        return "";
    }

    public static boolean checkStoragePermissions(Context ctx) {
        return ActivityCompat.checkSelfPermission(ctx, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED
                && CommonValidate.checkStoragePermissions(ctx);
    }

    @RequiresApi(api = Build.VERSION_CODES.O)
    public static void migrateExistFilesIfNeed(File legacyRootDir, File appRootDir) {
        // move /storage/emulated/0/.SnapInspect3
        // to /storage/emulated/0/Android/data/com.snapinspect.snapinspect3/files.SnapInspect3
        try {
            File[] allFiles = legacyRootDir.listFiles();
            if (allFiles != null && allFiles.length > 0) {
                CommonHelper.ValidateTempFolderExist();
                //CommonHelper.trackEvent(App.getContext(), "Start moving files");
                for (File file : allFiles) {
                    Path source = file.toPath(), dst = appRootDir.toPath();
                    Files.move(source, dst.resolve(source.getFileName()), StandardCopyOption.REPLACE_EXISTING);
                }
                //CommonHelper.trackEvent(App.getContext(), "End moving files");
            }

            // check all the files moved
            File[] allFiles_Recheck = legacyRootDir.listFiles();
            if (allFiles_Recheck == null || allFiles_Recheck.length == 0) {
                deleteFile(legacyRootDir);
                //CommonHelper.trackEvent(App.getContext(), "Removed legacy directory");
                // update the file columns in table `AIVIDEO` and `AIPHOTO`
                CommonDB.migrationFilePaths();
                //CommonHelper.trackEvent(App.getContext(), "Updated file paths in database");
            }
        } catch (Exception exception) {
            ai_BugHandler.ai_Handler_Exception(exception);
        }

        // Note: requestLegacyExternalStorage is not working in Android 11 - API 30
        // https://stackoverflow.com/questions/63364476/requestlegacyexternalstorage-is-not-working-in-android-11-api-30
        // As stated in https://developer.android.com/preview/privacy/storage there are some changes regarding storage on Android 11:
        // Android 10 devices
        // requestLegacyExternalStorage will continue to work regardless of target sdk
        // Android 11 devices
        // new installation target sdk 29: requestLegacyExternalStorage value is respected
        // new installation target sdk 30: requestLegacyExternalStorage is always false
        // upgrade from target sdk 29 to 30: if preserveLegacyExternalStorage is set then requestLegacyExternalStorage is true (this is pure migration case and this state won't be preserved should user uninstall/reinstall the app)
        // You're pretty much forced to implement scoped storage at this point. Unless you're ready to migrate just keep target sdk 29 as there's no way to enforce legacy storage on Android 11 devices with target sdk 30.
    }

    public static void copyFile(File src, File dst, boolean targetIsDirectory) throws IOException {
        File newFile = targetIsDirectory ? new File(dst, src.getName()) : dst;
        FileChannel inChannel = new FileInputStream(src).getChannel();
        FileChannel outChannel = new FileOutputStream(newFile).getChannel();
        inChannel.transferTo(0, inChannel.size(), outChannel);
        inChannel.close();
        outChannel.close();
    }

    public static void copyFile(FileInputStream inputStream, File dst) throws IOException {
        FileChannel inChannel = inputStream.getChannel();
        FileChannel outChannel = new FileOutputStream(dst).getChannel();
        inChannel.transferTo(0, inChannel.size(), outChannel);
        inChannel.close();
        outChannel.close();
    }

    public static void deleteFile(File fileOrDirectory) {
        if (!fileOrDirectory.exists()) return;
        if (fileOrDirectory.isDirectory()) {
            File[] allFiles = fileOrDirectory.listFiles();
            if (allFiles != null) {
                for (File child : allFiles) deleteFile(child);

                allFiles = fileOrDirectory.listFiles();
                if (allFiles == null || allFiles.length < 1)
                    fileOrDirectory.delete();
            }
        } else {
            fileOrDirectory.delete();
        }
    }

    public static void updateGpsTag(String sFilePath, Location location) {
        try {
            if (sFilePath != null && location != null) {
                ExifInterface exif = new ExifInterface(sFilePath);
                exif.setGpsInfo(location);
                exif.saveAttributes();
            }
        } catch (IOException e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }
    }

    public static void writeToTextFile(String sFileName, String sContent) {
        try {
            File root = new File(CommonHelper.sFileRoot);
            File txtFile = new File(root, sFileName + ".txt");
            FileWriter writer = new FileWriter(txtFile);
            writer.append(sContent);
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void writeToFile(String sFilePath, String sContent) {
        try {
            File file = new File(sFilePath);
            FileWriter writer = new FileWriter(file);
            writer.append(sContent);
            writer.flush();
            writer.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /*
     * Get the extension of a file.
     */
    public static String getExtension(File f) {
        String ext = null;
        String s = f.getName();
        int i = s.lastIndexOf('.');

        if (i > 0 &&  i < s.length() - 1) {
            ext = s.substring(i+1).toLowerCase();
        }
        return ext;
    }

    // Copy file from MediaStore to app's temp folder and return the file path
    public static String getFilePathFromContentUri(Context context, Uri contentUri) {
        String filePath = null;
        ParcelFileDescriptor parcelFileDescriptor = null;
        try {
            parcelFileDescriptor = context.getContentResolver().openFileDescriptor(contentUri, "r");
            FileDescriptor fileDescriptor = parcelFileDescriptor.getFileDescriptor();
            String originalFileName = getFileName(context, contentUri);
            File outputFile = new File(FileUtils.getRootPath() + Constants.Paths.tempFolder, originalFileName);
            FileInputStream inputStream = new FileInputStream(fileDescriptor);
            FileUtils.copyFile(inputStream, outputFile);
            filePath = outputFile.getAbsolutePath();
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (parcelFileDescriptor != null) {
                try {
                    parcelFileDescriptor.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return filePath;
    }

    // Get file name from MediaStore
    public static String getFileName(Context context, Uri uri) {
        String fileName = null;
        String[] projection = {MediaStore.Files.FileColumns.DISPLAY_NAME};
        Cursor cursor = context.getContentResolver().query(uri, projection, null, null, null);
        if (cursor != null && cursor.moveToFirst()) {
            int displayNameIndex = cursor.getColumnIndex(MediaStore.Files.FileColumns.DISPLAY_NAME);
            if (displayNameIndex != -1) {
                fileName = cursor.getString(displayNameIndex);
            }
            cursor.close();
        }
        return fileName;
    }
}
