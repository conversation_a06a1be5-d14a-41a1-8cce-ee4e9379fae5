package com.snapinspect.snapinspect3.util;

import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewTreeObserver;
import android.widget.RelativeLayout;

import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

/**
 * @Created by <PERSON><PERSON><PERSON> on 3/9/18.
 */

public class SwipeRefreshHintLayout extends RelativeLayout {
    public SwipeRefreshHintLayout(Context context) {
        super(context);
    }

    public SwipeRefreshHintLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SwipeRefreshHintLayout(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
    }

    public void setSwipeLayoutTarget(final SwipeRefreshLayout swipeRefreshLayout) {

        final View swipeTarget = swipeRefreshLayout.getChildAt(0);
        if (swipeTarget == null) {
            return;
        }

        swipeTarget.getViewTreeObserver().addOnPreDrawListener(new ViewTreeObserver.OnPreDrawListener() {
            private final Rect oldBounds = new Rect();
            private final Rect newBounds = new Rect();

            @Override
            public boolean onPreDraw() {
                newBounds.set(swipeTarget.getLeft(), swipeRefreshLayout.getTop(), swipeTarget.getRight(), swipeTarget.getTop());

                if (!oldBounds.equals(newBounds)){
                    getLayoutParams().height = newBounds.height();
                    requestLayout();
                    oldBounds.set(newBounds);
                }
                return true;
            }
        });

    }
}
