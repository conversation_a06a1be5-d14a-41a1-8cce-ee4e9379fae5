package com.snapinspect.snapinspect3.util;

import android.app.Activity;
import android.content.Context;
import android.graphics.Rect;
import android.view.View;
import android.view.ViewTreeObserver;
import android.view.animation.AlphaAnimation;
import android.view.animation.Animation;

import static android.view.View.GONE;
import static android.view.View.VISIBLE;

/**
 * Created by <PERSON> on 07/02/20.
 */
public final class InputAccessoryHelper {

    public interface Listener {
        void keyboardVisible(boolean visible, int keyboardHeight);
    }

    private static final int SOFT_KEY_BOARD_MIN_HEIGHT = 200;
    private static final long FADE_ANIMATION_DURATION = 180; // ms

    private final Context mContext;
    private final Activity mActivity;
    private final View mAccessoryView;
    private final View mWindow;
    private ViewTreeObserver.OnGlobalLayoutListener mLayoutListener;
    private final Listener mListener;

    public InputAccessoryHelper(Context ctx, Activity activity, View accessoryView, Listener listener) {
        mContext = ctx;
        mAccessoryView = accessoryView;
        mActivity = activity;
        mWindow = mActivity.getWindow().getDecorView().findViewById(android.R.id.content);
        mListener = listener;
        registerLayoutListener();
    }

    private void registerLayoutListener() {
        mLayoutListener = () -> {
            Rect measureRect = new Rect();
            mWindow.getWindowVisibleDisplayFrame(measureRect);
            int keypadHeight = mWindow.getRootView().getHeight() - measureRect.bottom;
            boolean visible = keypadHeight > SOFT_KEY_BOARD_MIN_HEIGHT;
            updateAccessoryViewVisible(visible);
            if (mListener != null) {
                mListener.keyboardVisible(visible, keypadHeight);
            }
        };

        mWindow.getViewTreeObserver().addOnGlobalLayoutListener(mLayoutListener);
    }

    private void updateAccessoryViewVisible(boolean visible) {
        int visibility = mAccessoryView.getVisibility();
        if (visible && visibility == VISIBLE || !visible && visibility == GONE) return;

        float start = visible ? 0.f : 1.f, end = visible ? 1.f : 0.f;
        AlphaAnimation anim = new AlphaAnimation(start, end);
        anim.setDuration(FADE_ANIMATION_DURATION);
        anim.setAnimationListener(new Animation.AnimationListener() {
            @Override
            public void onAnimationStart(Animation animation) { }

            @Override
            public void onAnimationEnd(Animation animation) {
                mAccessoryView.setVisibility(visible ? VISIBLE : GONE);
            }

            @Override
            public void onAnimationRepeat(Animation animation) { }
        });
        mAccessoryView.startAnimation(anim);
    }
}
