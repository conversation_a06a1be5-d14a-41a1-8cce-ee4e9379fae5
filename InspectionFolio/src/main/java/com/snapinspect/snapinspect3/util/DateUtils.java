package com.snapinspect.snapinspect3.util;

import android.content.Context;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.app.App;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;

public final class DateUtils {

    private DateUtils() {
        // Private constructor to prevent instantiation
    }

    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String SCHEDULE_TIME_FORMAT = "yyyy-MM-dd HH:mm";
    public static final String DATE_RANGE_FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    public static final String DATE_TIME_READABLE_FORMAT = "MMM dd, yyyy HH:mm";

    public static final class DateFormat {
        public static final String ISO_DATE_TIME_WITH_ZONE = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
        public static final String ISO_DATE_TIME_WITH_MILLIS = "yyyy-MM-dd'T'HH:mm:ss.SSS";
        public static final String ISO_DATE_TIME = "yyyy-MM-dd'T'HH:mm:ss";
        public static final String DATE_TIME = "yyyy-MM-dd HH:mm:ss";
        public static final String DATE_TIME_SHORT = "yyyy-MM-dd HH:mm";
        public static final String LONG_DATE_TIME = "MMMM dd, yyyy HH:mm";
        
        private DateFormat() {
            // Private constructor to prevent instantiation
        }
    }

    public static final String[] possibleDateFormats = new String[] {
        DateFormat.ISO_DATE_TIME_WITH_ZONE,
        DateFormat.ISO_DATE_TIME_WITH_MILLIS,
        DateFormat.ISO_DATE_TIME,
        DateFormat.DATE_TIME,
        DateFormat.DATE_TIME_SHORT,
        DateFormat.LONG_DATE_TIME
    };

    public static Date startDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }

    public static Date endDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        return calendar.getTime();
    }

    public static Date startDateOfWeek(Date date, int firstDayOfWeek) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.clear(Calendar.MINUTE);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MILLISECOND);

        calendar.setFirstDayOfWeek(firstDayOfWeek);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek());
        return calendar.getTime();
    }

    public static Date endDateOfWeek(Date date, int firstDayOfWeek) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.setFirstDayOfWeek(firstDayOfWeek);
        calendar.set(Calendar.DAY_OF_WEEK, calendar.getFirstDayOfWeek() + 6);
        return calendar.getTime();
    }

    public static Date startDateOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.clear(Calendar.MINUTE);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MILLISECOND);

        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static Date endDateOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);

        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
        return calendar.getTime();
    }

    public static Date addMonths(Date date, int num) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, num);
        return cal.getTime();
    }

    public static boolean isSameDay(Date date1, Date date2) {
        SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
        return fmt.format(date1).equals(fmt.format(date2));
    }

    public static boolean isSameYear(Date date1, Date date2) {
        SimpleDateFormat fmt = new SimpleDateFormat("yyyy");
        return fmt.format(date1).equals(fmt.format(date2));
    }

    public static boolean isValidDate(Date date) {
        SimpleDateFormat fmt = new SimpleDateFormat("yyyy");
        return Integer.parseInt(fmt.format(date)) > 1980;
    }

    public static boolean isYesterday(Date date) {
        Calendar now = Calendar.getInstance();
        Calendar cDate = Calendar.getInstance();
        cDate.setTimeInMillis(date.getTime());
        now.add(Calendar.DATE,-1);
        return now.get(Calendar.YEAR) == cDate.get(Calendar.YEAR)
                && now.get(Calendar.MONTH) == cDate.get(Calendar.MONTH)
                && now.get(Calendar.DATE) == cDate.get(Calendar.DATE);
    }

    public static String format(Date date, String pattern) {
        return format(date, pattern, null);
    }

    public static String format(Date date, String pattern, TimeZone timeZone) {
        SimpleDateFormat format = new SimpleDateFormat(pattern, Locale.US);
        if (timeZone != null) format.setTimeZone(timeZone);
        return format.format(date);
    }

    public static Date parse(String dateStr, String pattern) {
        return parse(dateStr, pattern, null);
    }

    public static Date getUTCDate() {
        return Calendar.getInstance(Constants.UTC).getTime();
    }

    public static Date parse(String dateStr, String pattern, TimeZone timeZone) {
        if (dateStr == null || dateStr.isEmpty() ||
                pattern == null || pattern.isEmpty()) return null;
        try {
            SimpleDateFormat format = new SimpleDateFormat(pattern, Locale.US);
            if (timeZone != null) format.setTimeZone(timeZone);
            return format.parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static Date parse(String dateStr, String[] patterns) {
        return parse(dateStr, null, patterns);
    }

    public static Date parse(String dateStr, TimeZone timeZone, String[] patterns) {
        for (String pattern : patterns) {
            Date date = parse(dateStr, pattern, timeZone);
            if (date != null) return date;
        }
        return null;
    }

    public static String formatAsInboxNotificationDate(Date date) {
        Context context = App.getContext();
        if (isSameDay(date, new Date())) return context.getString(R.string.today);
        if (isYesterday(date)) return context.getString(R.string.yesterday);
        return formatAsScheduleTitle(date);
    }

    public static String formatAsTaskDueDate(Date date) {
        Context context = App.getContext();
        if (isSameDay(date, new Date())) return context.getString(R.string.today);
        return CommonHelper.getDateTimeStrFromDate(
                date, isSameYear(date, new Date()) ? "dd MMM" : "dd MMM, yyyy");
    }

    public static String formatAsScheduleTitle(Date date) {
        if (isSameDay(date, new Date())) return App.getContext().getString(R.string.today);

        String formatted = format(date, "E d MMM yyyy");
        if (StringUtils.isEmpty(formatted)) return null;

        try {
            List<String> items = new ArrayList<>(Arrays.asList(formatted.split(" ")));
            if (items.size() > 1) {
                items.set(1, ordinalDayOfMonth(date));
            }
            return String.join(" ", items);
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception(e);
            return null;
        }
    }

    /**
     * Converts a day of month to its ordinal representation (1st, 2nd, 3rd, etc)
     * 
     * @param date The date to extract the day from
     * @return The day with its ordinal suffix (e.g. "1st", "2nd", "3rd", "4th")
     */
    public static String ordinalDayOfMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int day = calendar.get(Calendar.DAY_OF_MONTH);

        String suffix;
        switch (day % 10) {
            case 1: suffix = (day == 11) ? "th" : "st"; break;
            case 2: suffix = (day == 12) ? "th" : "nd"; break;
            case 3: suffix = (day == 13) ? "th" : "rd"; break;
            default: suffix = "th"; break;
        }

        return day + suffix;
    }

    public static String formatAsCellDate(Context ctx, Date date) {
        String sTime = formatDateWithDefaultLocale(date, "HH:mm");
        if ("00:00".equals(sTime)) return ctx.getString(R.string.schedule_range_all_day);
        return sTime;
    }

    public static int daysPassed(Date d1, Date d2) {
        return (int) ((d1.getTime() - d2.getTime()) / (1000 * 60 * 60 * 24));
    }

    public static String cleanDateIfTimeIsZero(String sDate, String sFormat, String sOutputFormat) {
        Date date = parse(sDate, sFormat);
        if (date == null || !isValidDate(date)) return null;
        if (format(startDate(date), sFormat).equals(sDate)) {
            return format(date, sOutputFormat);
        }
        return sDate;
    }

    public static String reformatDate(String sDate, String sFormat, String sOutputFormat) {
        Date date = parse(sDate, sFormat);
        if (date == null) return sDate;
        return format(date, sOutputFormat);
    }

    public static String clockStringFromMilliseconds(long millis) {
        long hours;
        long minutes;
        long remainingSeconds;
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.O) {
            Duration duration = Duration.ofMillis(millis);
            hours = duration.toHours();
            minutes = duration.toMinutes() % 60;
            remainingSeconds = duration.getSeconds() % 60;
        } else {
            hours = TimeUnit.MILLISECONDS.toHours(millis);
            minutes = TimeUnit.MILLISECONDS.toMinutes(millis) % 60;
            remainingSeconds = TimeUnit.MILLISECONDS.toSeconds(millis) % 60;
        }

        return (hours > 0) ?
                String.format(Locale.US, "%02d:%02d:%02d", hours, minutes, remainingSeconds) :
                String.format(Locale.US, "%02d:%02d", minutes, remainingSeconds);
    }

    public static String formatDateWithDefaultLocale(Date date, String outputFormat) {
        SimpleDateFormat outputFormatter = new SimpleDateFormat(outputFormat, Locale.getDefault());
        return outputFormatter.format(date);
    }

    /**
     * Converts a date to a relative time string (e.g. "2 minutes ago", "3 hours ago")
     *
     * @param context Android context for accessing string resources
     * @param date The date to convert
     * @param now The current date to compare against
     * @return A string representing the relative time difference between the dates:
     *         - "X seconds ago" if less than a minute
     *         - "X minutes ago" if less than an hour
     *         - "X hours ago" if less than a day
     *         - "X days ago" if less than 2 weeks
     *         - Full date/time if 2 weeks or more
     */
    public static String toRelative(Context context, Date date, Date now) {
        long diffInMillis = now.getTime() - date.getTime();
        long diffInSeconds = TimeUnit.MILLISECONDS.toSeconds(diffInMillis);
        long diffInMinutes = TimeUnit.MILLISECONDS.toMinutes(diffInMillis);
        long diffInHours = TimeUnit.MILLISECONDS.toHours(diffInMillis);
        long diffInDays = TimeUnit.MILLISECONDS.toDays(diffInMillis);

        if (diffInSeconds < TimeUnit.MINUTES.toSeconds(1)) {
            return context.getResources().getQuantityString(R.plurals.secondsAgo, (int)diffInSeconds, (int)diffInSeconds);
        } else if (diffInMinutes < TimeUnit.HOURS.toMinutes(1)) {
            return context.getResources().getQuantityString(R.plurals.minutesAgo, (int)diffInMinutes, (int)diffInMinutes);
        } else if (diffInHours < TimeUnit.DAYS.toHours(1)) {
            return context.getResources().getQuantityString(R.plurals.hoursAgo, (int)diffInHours, (int)diffInHours);
        } else if (diffInDays < TimeUnit.DAYS.toDays(14)) {
            return context.getResources().getQuantityString(R.plurals.daysAgo, (int)diffInDays, (int)diffInDays);
        }

        return format(date, DATE_TIME_READABLE_FORMAT);
    }
}
