package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Schedule;

import java.util.List;

@Dao
public interface ScheduleDao {
    
    // Basic CRUD operations using only existing columns
    @Query("SELECT * FROM AISCHEDULE WHERE B_DELETED = 0")
    List<Schedule> getAllSchedules();

    @Query("SELECT * FROM AISCHEDULE WHERE ID = :id")
    Schedule getScheduleById(long id);

    @Query("SELECT * FROM AISCHEDULE WHERE I_S_SCHEDULE_ID = :serverScheduleId AND B_DELETED = 0")
    Schedule getScheduleByServerId(int serverScheduleId);

    @Query("SELECT * FROM AISCHEDULE WHERE I_S_ASSET_ID = :assetId AND B_DELETED = 0")
    List<Schedule> getSchedulesByAssetId(int assetId);

    @Query("SELECT * FROM AISCHEDULE WHERE I_S_INS_TYPE_ID = :insTypeId AND B_DELETED = 0")
    List<Schedule> getSchedulesByInsTypeId(int insTypeId);

    @Query("SELECT * FROM AISCHEDULE WHERE S_TYPE = :type AND B_DELETED = 0")
    List<Schedule> getSchedulesByType(String type);

    @Query("SELECT * FROM AISCHEDULE WHERE DT_DATE_TIME = :scheduleDate AND B_DELETED = 0")
    List<Schedule> getSchedulesByDate(String scheduleDate);

    @Query("SELECT * FROM AISCHEDULE WHERE DT_DATE_TIME BETWEEN :startDate AND :endDate AND B_DELETED = 0 ORDER BY DT_DATE_TIME")
    List<Schedule> getSchedulesBetweenDates(String startDate, String endDate);

    @Query("SELECT * FROM AISCHEDULE WHERE B_COMPLETED = 0 AND B_DELETED = 0")
    List<Schedule> getIncompleteSchedules();

    @Query("SELECT * FROM AISCHEDULE WHERE B_COMPLETED = 1 AND B_DELETED = 0")
    List<Schedule> getCompletedSchedules();

    @Insert
    long insertSchedule(Schedule schedule);

    @Insert
    List<Long> insertSchedules(List<Schedule> schedules);

    @Update
    void updateSchedule(Schedule schedule);

    @Update
    void updateSchedules(List<Schedule> schedules);

    @Delete
    void deleteSchedule(Schedule schedule);

    @Query("UPDATE AISCHEDULE SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AISCHEDULE SET B_DELETED = 1 WHERE I_S_SCHEDULE_ID = :serverScheduleId")
    void softDeleteByServerId(int serverScheduleId);

    @Query("UPDATE AISCHEDULE SET B_COMPLETED = :completed WHERE I_S_SCHEDULE_ID = :serverScheduleId")
    void updateScheduleCompletedStatus(int serverScheduleId, boolean completed);

    @Query("SELECT COUNT(*) FROM AISCHEDULE WHERE B_DELETED = 0")
    int getScheduleCount();

    @Query("SELECT COUNT(*) FROM AISCHEDULE WHERE DT_DATE_TIME = :date AND B_DELETED = 0")
    int getScheduleCountByDate(String date);

    @Query("SELECT COUNT(*) FROM AISCHEDULE WHERE I_S_ASSET_ID = :assetId AND B_DELETED = 0")
    int getScheduleCountByAsset(int assetId);

    // Batch operations
    @Query("DELETE FROM AISCHEDULE")
    void deleteAllSchedules();

    @Query("UPDATE AISCHEDULE SET B_DELETED = 1")
    void softDeleteAllSchedules();
}