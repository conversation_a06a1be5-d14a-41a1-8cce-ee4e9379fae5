package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;

@Entity(
    tableName = "AIINS_ITEM",
    indices = {
        @Index(value = "I_INS_ID"),
        @Index(value = "I_P_INS_ITEM_ID"),
        @Index(value = "I_S_LAYOUT_ID"),
        @Index(value = "B_DELETED"),
        @Index(value = "B_COMPLETED")
    },
    foreignKeys = {
        @ForeignKey(
            entity = Inspection.class,
            parentColumns = "ID",
            childColumns = "I_INS_ID"
        )
    }
)
public class InsItem {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_INS_ID")
    public Integer iInsID;

    @ColumnInfo(name = "I_P_INS_ITEM_ID")
    public Integer iPInsItemID;

    @ColumnInfo(name = "I_S_LAYOUT_ID")
    public Integer iSLayoutID;

    @ColumnInfo(name = "I_S_ASSET_LAYOUT_ID")
    public Integer iSAssetLayoutID;

    @ColumnInfo(name = "I_SORT")
    public Integer iSort;

    @ColumnInfo(name = "S_NAME")
    public String sName;

    @ColumnInfo(name = "S_NAME_CHANGED")
    public String sNameChanged;

    @ColumnInfo(name = "S_Q_TYPE")
    public String sQType;

    @ColumnInfo(name = "S_VALUE_ONE")
    public String sValueOne;

    @ColumnInfo(name = "S_VALUE_TWO")
    public String sValueTwo;

    @ColumnInfo(name = "S_VALUE_THREE")
    public String sValueThree;

    @ColumnInfo(name = "S_VALUE_FOUR")
    public String sValueFour;

    @ColumnInfo(name = "S_VALUE_FIVE")
    public String sValueFive;

    @ColumnInfo(name = "S_VALUE_SIX")
    public String sValueSix;

    @ColumnInfo(name = "S_CONFIG_ONE")
    public String sConfigOne;

    @ColumnInfo(name = "S_CONFIG_TWO")
    public String sConfigTwo;

    @ColumnInfo(name = "S_CONFIG_THREE")
    public String sConfigThree;

    @ColumnInfo(name = "S_CONFIG_FOUR")
    public String sConfigFour;

    @ColumnInfo(name = "S_CONFIG_FIVE")
    public String sConfigFive;

    @ColumnInfo(name = "S_CONFIG_SIX")
    public String sConfigSix;

    @ColumnInfo(name = "S_CONFIG")
    public String sConfig;

    @ColumnInfo(name = "S_CUSTOM_ONE")
    public String sCustomOne;

    @ColumnInfo(name = "S_CUSTOM_TWO")
    public String sCustomTwo;

    @ColumnInfo(name = "B_COMPLETED")
    public Boolean bCompleted = false;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    // Constructors
    public InsItem() {}

    // Copy constructor from Sugar ORM entity
    public InsItem(ai_InsItem sugarEntity) {
        this.id = sugarEntity.getId();
        this.iInsID = sugarEntity.iInsID;
        this.iPInsItemID = sugarEntity.iPInsItemID;
        this.iSLayoutID = sugarEntity.iSLayoutID;
        this.iSAssetLayoutID = sugarEntity.iSAssetLayoutID;
        this.iSort = sugarEntity.iSort;
        this.sName = sugarEntity.sName;
        this.sNameChanged = sugarEntity.sNameChanged;
        this.sQType = sugarEntity.sQType;
        this.sValueOne = sugarEntity.sValueOne;
        this.sValueTwo = sugarEntity.sValueTwo;
        this.sValueThree = sugarEntity.sValueThree;
        this.sValueFour = sugarEntity.sValueFour;
        this.sValueFive = sugarEntity.sValueFive;
        this.sValueSix = sugarEntity.sValueSix;
        this.sConfigOne = sugarEntity.sConfigOne;
        this.sConfigTwo = sugarEntity.sConfigTwo;
        this.sConfigThree = sugarEntity.sConfigThree;
        this.sConfigFour = sugarEntity.sConfigFour;
        this.sConfigFive = sugarEntity.sConfigFive;
        this.sConfigSix = sugarEntity.sConfigSix;
        this.sConfig = sugarEntity.sConfig;
        this.sCustomOne = sugarEntity.sCustomOne;
        this.sCustomTwo = sugarEntity.sCustomTwo;
        this.bCompleted = sugarEntity.bCompleted;
        this.bDeleted = sugarEntity.bDeleted;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_InsItem toSugarEntity() {
        ai_InsItem sugar = new ai_InsItem();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iInsID = this.iInsID;
        sugar.iPInsItemID = this.iPInsItemID;
        sugar.iSLayoutID = this.iSLayoutID;
        sugar.iSAssetLayoutID = this.iSAssetLayoutID;
        sugar.iSort = this.iSort;
        sugar.sName = this.sName;
        sugar.sNameChanged = this.sNameChanged;
        sugar.sQType = this.sQType;
        sugar.sValueOne = this.sValueOne;
        sugar.sValueTwo = this.sValueTwo;
        sugar.sValueThree = this.sValueThree;
        sugar.sValueFour = this.sValueFour;
        sugar.sValueFive = this.sValueFive;
        sugar.sValueSix = this.sValueSix;
        sugar.sConfigOne = this.sConfigOne;
        sugar.sConfigTwo = this.sConfigTwo;
        sugar.sConfigThree = this.sConfigThree;
        sugar.sConfigFour = this.sConfigFour;
        sugar.sConfigFive = this.sConfigFive;
        sugar.sConfigSix = this.sConfigSix;
        sugar.sConfig = this.sConfig;
        sugar.sCustomOne = this.sCustomOne;
        sugar.sCustomTwo = this.sCustomTwo;
        sugar.bCompleted = this.bCompleted;
        sugar.bDeleted = this.bDeleted;
        return sugar;
    }

    /**
     * Check if this item is hidden
     */
    public boolean isHidden() {
        return com.snapinspect.snapinspect3.Helper.CommonHelper.getInt(
            com.snapinspect.snapinspect3.Helper.CommonJson.GetJsonKeyValue("_bH", sConfig)) > 0;
    }
}