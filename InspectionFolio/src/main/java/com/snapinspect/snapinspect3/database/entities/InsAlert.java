package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_InsAlert;

/**
 * Room entity for ai_InsAlert (AIINS_ALERT table)
 */
@Entity(tableName = "AIINS_ALERT")
public class InsAlert {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_S_ASSET_ID")
    public Integer iSAssetID;
    
    @ColumnInfo(name = "I_S_LAYOUT_ID")
    public Integer iSLayoutID;
    
    @ColumnInfo(name = "S_PTC")
    public String sPTC;
    
    @ColumnInfo(name = "S_VALUE_ONE")
    public String sValueOne;
    
    @ColumnInfo(name = "S_VALUE_TWO")
    public String sValueTwo;
    
    @ColumnInfo(name = "S_VALUE_THREE")
    public String sValueThree;
    
    @ColumnInfo(name = "S_VALUE_FOUR")
    public String sValueFour;
    
    @ColumnInfo(name = "S_VALUE_FIVE")
    public String sValueFive;
    
    @ColumnInfo(name = "S_VALUE_SIX")
    public String sValueSix;
    
    public InsAlert() {}
    
    public InsAlert(ai_InsAlert source) {
        this.iSAssetID = source.iSAssetID;
        this.iSLayoutID = source.iSLayoutID;
        this.sPTC = source.sPTC;
        this.sValueOne = source.sValueOne;
        this.sValueTwo = source.sValueTwo;
        this.sValueThree = source.sValueThree;
        this.sValueFour = source.sValueFour;
        this.sValueFive = source.sValueFive;
        this.sValueSix = source.sValueSix;
    }
}