package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Product;

import java.util.List;

@Dao
public interface ProductDao {
    
    @Query("SELECT * FROM AIPRODUCT WHERE B_DELETED = 0")
    List<Product> getAllProducts();

    @Query("SELECT * FROM AIPRODUCT WHERE ID = :id")
    Product getProductById(long id);

    @Query("SELECT * FROM AIPRODUCT WHERE I_S_PRODUCT_ID = :serverProductId AND B_DELETED = 0")
    Product getProductByServerId(int serverProductId);

    @Query("SELECT * FROM AIPRODUCT WHERE S_NAME LIKE :namePattern AND B_DELETED = 0")
    List<Product> searchProductsByName(String namePattern);

    @Query("SELECT * FROM AIPRODUCT WHERE S_SKU = :sku AND B_DELETED = 0")
    Product getProductBySku(String sku);

    @Query("SELECT * FROM AIPRODUCT WHERE S_PRODUCT_CATEGORY = :category AND B_DELETED = 0")
    List<Product> getProductsByCategory(String category);

    @Query("SELECT * FROM AIPRODUCT WHERE B_ARCHIVE = 0 AND B_DELETED = 0")
    List<Product> getNonArchivedProducts();

    @Insert
    long insertProduct(Product product);

    @Insert
    List<Long> insertProducts(List<Product> products);

    @Update
    void updateProduct(Product product);

    @Update
    void updateProducts(List<Product> products);

    @Delete
    void deleteProduct(Product product);

    @Query("UPDATE AIPRODUCT SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIPRODUCT SET B_DELETED = 1 WHERE I_S_PRODUCT_ID = :serverProductId")
    void softDeleteByServerId(int serverProductId);

    @Query("UPDATE AIPRODUCT SET B_ARCHIVE = :archive WHERE I_S_PRODUCT_ID = :serverProductId")
    void updateArchiveStatus(int serverProductId, boolean archive);

    @Query("SELECT COUNT(*) FROM AIPRODUCT WHERE B_DELETED = 0")
    int getProductCount();

    @Query("SELECT COUNT(*) FROM AIPRODUCT WHERE S_PRODUCT_CATEGORY = :category AND B_ARCHIVE = 0 AND B_DELETED = 0")
    int getNonArchivedProductCountByCategory(String category);

    // Batch operations
    @Query("DELETE FROM AIPRODUCT")
    void deleteAllProducts();

    @Query("UPDATE AIPRODUCT SET B_DELETED = 1")
    void softDeleteAllProducts();
}