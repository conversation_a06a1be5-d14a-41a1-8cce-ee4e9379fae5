package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_Notification;

@Entity(
    tableName = "AINOTIFICATION",
    indices = {
        @Index(value = "I_S_NOTIFICATION_ID"),
        @Index(value = "I_INS_ITEM_ID"),
        @Index(value = "I_INS_ID"),
        @Index(value = "B_DELETED")
    }
)
public class Notification {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_S_NOTIFICATION_ID")
    public Integer iSNotificationID;

    @ColumnInfo(name = "I_INS_ITEM_ID")
    public Long iInsItemID;

    @ColumnInfo(name = "I_INS_ID")
    public Long iInsID;

    @ColumnInfo(name = "S_TITLE")
    public String sTitle;

    @ColumnInfo(name = "S_DESCRIPTION")
    public String sDescription;

    @ColumnInfo(name = "S_PHOTO_URL")
    public String sPhotoURL;

    @ColumnInfo(name = "S_VIDEO_ID")
    public String sVideoID;

    @ColumnInfo(name = "S_DUE_DATE")
    public String sDueDate;

    @ColumnInfo(name = "I_PRIORITY")
    public Integer iPriority;

    @ColumnInfo(name = "S_CATEGORY")
    public String sCategory;

    @ColumnInfo(name = "S_CUSTOM_ONE")
    public String sCustomOne;

    @ColumnInfo(name = "S_CUSTOM_TWO")
    public String sCustomTwo;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    @ColumnInfo(name = "DT_DATE_TIME")
    public String dtDateTime;

    // Constructors
    public Notification() {}

    // Copy constructor from Sugar ORM entity
    public Notification(ai_Notification sugarEntity) {
        this.id = sugarEntity.getId();
        this.iSNotificationID = sugarEntity.iSNotificationID;
        this.iInsItemID = sugarEntity.iInsItemID;
        this.iInsID = sugarEntity.iInsID;
        this.sTitle = sugarEntity.sTitle;
        this.sDescription = sugarEntity.sDescription;
        this.sPhotoURL = sugarEntity.sPhotoURL;
        this.sVideoID = sugarEntity.sVideoID;
        this.sDueDate = sugarEntity.sDueDate;
        this.iPriority = sugarEntity.iPriority;
        this.sCategory = sugarEntity.sCategory;
        this.sCustomOne = sugarEntity.sCustomOne;
        this.sCustomTwo = sugarEntity.sCustomTwo;
        this.bDeleted = sugarEntity.bDeleted;
        this.dtDateTime = sugarEntity.dtDateTime;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Notification toSugarEntity() {
        ai_Notification sugar = new ai_Notification();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSNotificationID = this.iSNotificationID != null ? this.iSNotificationID : 0;
        sugar.iInsItemID = this.iInsItemID != null ? this.iInsItemID : 0;
        sugar.iInsID = this.iInsID != null ? this.iInsID : 0;
        sugar.sTitle = this.sTitle;
        sugar.sDescription = this.sDescription;
        sugar.sPhotoURL = this.sPhotoURL;
        sugar.sVideoID = this.sVideoID;
        sugar.sDueDate = this.sDueDate;
        sugar.iPriority = this.iPriority != null ? this.iPriority : 0;
        sugar.sCategory = this.sCategory;
        sugar.sCustomOne = this.sCustomOne;
        sugar.sCustomTwo = this.sCustomTwo;
        sugar.bDeleted = this.bDeleted != null ? this.bDeleted : false;
        sugar.dtDateTime = this.dtDateTime;
        return sugar;
    }
}