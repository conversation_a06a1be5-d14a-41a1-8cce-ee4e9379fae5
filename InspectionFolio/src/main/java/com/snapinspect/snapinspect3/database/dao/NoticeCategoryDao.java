package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.NoticeCategory;

import java.util.List;

@Dao
public interface NoticeCategoryDao {
    
    @Query("SELECT * FROM ainotice_category WHERE b_deleted = 0")
    List<NoticeCategory> getAllNoticeCategories();

    @Query("SELECT * FROM ainotice_category WHERE id = :id")
    NoticeCategory getNoticeCategoryById(long id);

    @Query("SELECT * FROM ainotice_category WHERE i_s_notice_category_id = :serverNoticeCategoryId AND b_deleted = 0")
    NoticeCategory getNoticeCategoryByServerId(int serverNoticeCategoryId);

    @Query("SELECT * FROM ainotice_category WHERE s_name LIKE :namePattern AND b_deleted = 0")
    List<NoticeCategory> searchNoticeCategoriesByName(String namePattern);

    @Query("SELECT * FROM ainotice_category WHERE b_deleted = 0")
    List<NoticeCategory> getActiveNoticeCategories();

    @Query("SELECT * FROM ainotice_category WHERE b_deleted = 0 AND i_p_note_category_id = :parentId")
    List<NoticeCategory> getNoticeCategoriesByParentId(int parentId);

    // @Insert
    // long insertNoticeCategory(NoticeCategory noticeCategory);

    // @Insert
    // List<Long> insertNoticeCategories(List<NoticeCategory> noticeCategories);

    // @Update
    // void updateNoticeCategory(NoticeCategory noticeCategory);

    // @Update
    // void updateNoticeCategories(List<NoticeCategory> noticeCategories);

    // @Delete
    // void deleteNoticeCategory(NoticeCategory noticeCategory);

    // @Query("UPDATE AINOTICECATEGORY SET B_DELETED = 1 WHERE ID = :id")
    // void softDeleteById(long id);

    // @Query("UPDATE AINOTICECATEGORY SET B_DELETED = 1 WHERE I_S_NOTICE_CATEGORY_ID = :serverNoticeCategoryId")
    // void softDeleteByServerId(int serverNoticeCategoryId);

    // @Query("UPDATE AINOTICECATEGORY SET B_ACTIVE = :active WHERE I_S_NOTICE_CATEGORY_ID = :serverNoticeCategoryId")
    // void updateActiveStatus(int serverNoticeCategoryId, boolean active);

    // @Query("SELECT COUNT(*) FROM AINOTICECATEGORY WHERE B_DELETED = 0")
    // int getNoticeCategoryCount();

    // @Query("SELECT COUNT(*) FROM AINOTICECATEGORY WHERE B_ACTIVE = 1 AND B_DELETED = 0")
    // int getActiveNoticeCategoryCount();

    // Batch operations
    // @Query("DELETE FROM AINOTICECATEGORY")
    // void deleteAllNoticeCategories();

    // @Query("UPDATE AINOTICECATEGORY SET B_DELETED = 1")
    // void softDeleteAllNoticeCategories();
}