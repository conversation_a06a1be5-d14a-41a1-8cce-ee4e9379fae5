package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_Inbox;

import java.util.Date;

/**
 * Room entity for ai_Inbox
 */
@Entity(tableName = "AIINBOX")
public class Inbox {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_INBOX_ID")
    public int iInboxID;
    
    @ColumnInfo(name = "I_S_INBOX_ID")
    public int iSInboxID;
    
    @ColumnInfo(name = "I_USER_ID")
    public Integer iUserID;
    
    @ColumnInfo(name = "I_TYPE")
    public Integer iType;
    
    @ColumnInfo(name = "B_READ")
    public Boolean bRead;
    
    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted;
    
    @ColumnInfo(name = "I_FROM_CUSTOMER_ID")
    public int iFromCustomerID;
    
    @ColumnInfo(name = "I_TO_CUSTOMER_ID")
    public int iToCustomerID;
    
    @ColumnInfo(name = "I_COMPANY_ID")
    public int iCompanyID;
    
    @ColumnInfo(name = "I_ACTION_ID")
    public int iActionID;
    
    @ColumnInfo(name = "DATE_TIME")
    public Date dateTime;
    
    @ColumnInfo(name = "COLOR_STRING")
    public String colorString;
    
    @ColumnInfo(name = "ACTION_TYPE")
    public String actionType;
    
    @ColumnInfo(name = "S_COMMENTS")
    public String sComments;
    
    // Optional fields
    @ColumnInfo(name = "S_NAME")
    public String sName;
    
    @ColumnInfo(name = "S_DATE_TIME")
    public String sDateTime;
    
    @ColumnInfo(name = "S_DATE")
    public String sDate;
    
    @ColumnInfo(name = "S_TIME")
    public String sTime;
    
    public Inbox() {}
    
    public Inbox(ai_Inbox source) {
        this.iInboxID = source.iInboxID;
        this.iSInboxID = source.iSInboxID;
        this.iUserID = source.iUserID;
        this.iType = source.iType;
        this.bRead = source.bRead;
        this.bDeleted = source.bDeleted;
        this.iFromCustomerID = source.iFromCustomerID;
        this.iToCustomerID = source.iToCustomerID;
        this.iCompanyID = source.iCompanyID;
        this.iActionID = source.iActionID;
        this.dateTime = source.dateTime;
        this.colorString = source.colorString;
        this.actionType = source.actionType != null ? source.actionType.name() : null;
        this.sComments = source.sComments;
        this.sName = source.sName;
        this.sDateTime = source.sDateTime;
        this.sDate = source.sDate;
        this.sTime = source.sTime;
    }
    
    public Inbox(int iInboxID, int iSInboxID, int iFromCustomerID, int iToCustomerID,
                int iCompanyID, int iActionID, String sAction, String sComments) {
        this.iInboxID = iInboxID;
        this.iSInboxID = iSInboxID;
        this.iUserID = null;
        this.iType = null;
        this.bRead = false;
        this.bDeleted = false;
        this.iFromCustomerID = iFromCustomerID;
        this.iToCustomerID = iToCustomerID;
        this.iCompanyID = iCompanyID;
        this.iActionID = iActionID;
        this.actionType = sAction;
        this.sComments = sComments;
    }
}