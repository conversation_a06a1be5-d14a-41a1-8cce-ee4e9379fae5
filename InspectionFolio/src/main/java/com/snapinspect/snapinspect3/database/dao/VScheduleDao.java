package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Query;
import com.snapinspect.snapinspect3.database.views.VScheduleView;

import java.util.List;

@Dao
public interface VScheduleDao {
    
    @Query("SELECT * FROM VSCHEDULE WHERE bCompleted = 0 ORDER BY iUnixTime ASC LIMIT 1")
    VScheduleView getEarliestSchedule();
    
    @Query("SELECT * FROM VSCHEDULE WHERE iSScheduleID = :scheduleId ORDER BY iUnixTime")
    List<VScheduleView> searchSchedulesByServerId(int scheduleId);
    
    @Query("SELECT * FROM VSCHEDULE WHERE iAssetID = :assetId AND iSInsTypeID = :insTypeId ORDER BY iUnixTime")
    List<VScheduleView> searchSchedulesByAssetAndType(int assetId, int insTypeId);
    
    @Query("SELECT * FROM VSCHEDULE WHERE sSearchTerm LIKE :searchTerm ORDER BY iUnixTime")
    List<VScheduleView> searchSchedulesByTerm(String searchTerm);
    
    @Query("SELECT * FROM VSCHEDULE WHERE iUnixTime >= :startTime AND iUnixTime <= :endTime ORDER BY iUnixTime")
    List<VScheduleView> searchSchedulesByDateRange(long startTime, long endTime);
    
    @Query("SELECT * FROM VSCHEDULE WHERE (LENGTH(sRRule) > 0 OR LENGTH(sEXRule) > 0) ORDER BY iUnixTime")
    List<VScheduleView> getRecurringSchedules();
    
    @Query("SELECT * FROM VSCHEDULE WHERE (LENGTH(sRRule) > 0 OR LENGTH(sEXRule) > 0) AND sSearchTerm LIKE :searchTerm ORDER BY iUnixTime")
    List<VScheduleView> searchRecurringSchedulesByTerm(String searchTerm);
}