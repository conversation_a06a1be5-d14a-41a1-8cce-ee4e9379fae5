package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_QuickPhrase;

/**
 * Room entity for ai_QuickPhrase (AIQUICK_PHRASE table)
 */
@Entity(tableName = "AIQUICK_PHRASE")
public class QuickPhrase {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_QUICK_PHRASE_ID")
    public Integer iQuickPhraseID;
    
    @ColumnInfo(name = "I_S_LAYOUT_ID")
    public Integer iSLayoutID;
    
    @ColumnInfo(name = "S_COMMENTS")
    public String sComments;
    
    @ColumnInfo(name = "S_FIELD_ONE")
    public String sFieldOne;
    
    @ColumnInfo(name = "S_FIELD_TWO")
    public String sFieldTwo;
    
    @ColumnInfo(name = "S_FIELD_THREE")
    public String sFieldThree;
    
    public QuickPhrase() {}
    
    public QuickPhrase(ai_QuickPhrase source) {
        this.iQuickPhraseID = source.iQuickPhraseID;
        this.iSLayoutID = source.iSLayoutID;
        this.sComments = source.sComments;
        this.sFieldOne = source.sFieldOne;
        this.sFieldTwo = source.sFieldTwo;
        this.sFieldThree = source.sFieldThree;
    }
}