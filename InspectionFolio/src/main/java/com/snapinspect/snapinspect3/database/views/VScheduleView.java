package com.snapinspect.snapinspect3.database.views;

import androidx.room.DatabaseView;

@DatabaseView(
    viewName = "VSCHEDULE",
    value = "SELECT c.ID as iScheduleID, c.I_S_SCHEDULE_ID as iSScheduleID, c.I_S_INS_TYPE_ID as iSInsTypeID, " +
            "c.S_PTC as sPTC, c.S_INS_TITLE as sInsTitle, c.DT_Date_Time as sDateTime, c.I_UNIX_TIME as iUnixTime, c.B_COMPLETED as bCompleted, " +
            "c.S_CUSTOM_ONE as sCustom1, c.S_CUSTOM_TWO as sCustom2, c.S_TYPE as sType, c.S_R_RULE as sRRule, c.S_EX_RULE as sEXRule, " +
            "d.iPPAssetID, d.iPAssetID, d.iCustomerID, d.sRef, d.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, d.sUni<PERSON><PERSON><PERSON><PERSON>, d.s<PERSON><PERSON><PERSON><PERSON>, d.i<PERSON><PERSON><PERSON>, d.b<PERSON><PERSON><PERSON>, d.iAssetID, d.iID as iID, " +
            "(IFNULL(c.DT_DATE_TIME, '') || ' ' || IFNULL(d.sSearchTerm, (c.S_ADDRESS_ONE || ' ' || c.S_ADDRESS_TWO))) as sSearchTerm, " +
            "c.S_ADDRESS_ONE as sAddress1, c.S_ADDRESS_TWO as sAddress2 " +
            "FROM AISCHEDULE c LEFT JOIN VASSET d on c.I_S_ASSET_ID = d.iAssetID WHERE c.B_DELETED = 0"
)
public class VScheduleView {
    public int iScheduleID;
    public int iSScheduleID;
    public int iSInsTypeID;
    public String sPTC;
    public String sInsTitle;
    public String sDateTime;
    public long iUnixTime;
    public boolean bCompleted;
    public String sCustom1;
    public String sCustom2;
    public String sType;
    public String sRRule;
    public String sEXRule;
    public int iPPAssetID;
    public int iPAssetID;
    public int iCustomerID;
    public String sRef;
    public String sBuildingAddress;
    public String sUnitAddress;
    public String sRoomAddress;
    public int iGroupID;
    public boolean bApartment;
    public int iAssetID;
    public int iID;
    public String sSearchTerm;
    public String sAddress1;
    public String sAddress2;
}