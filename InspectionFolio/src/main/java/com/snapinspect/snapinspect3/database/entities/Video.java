package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;

@Entity(
    tableName = "AIVIDEO",
    indices = {
        @Index(value = "I_S_VIDEO_ID"),
        @Index(value = "I_INS_ID"),
        @Index(value = "I_INS_ITEM_ID"),
        @Index(value = "B_DELETED")
    }
)
public class Video {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_S_VIDEO_ID")
    public Integer iSVideoID;

    @ColumnInfo(name = "I_INS_ID")
    public Integer iInsID;

    @ColumnInfo(name = "I_INS_ITEM_ID")
    public Long iInsItemID;

    @ColumnInfo(name = "S_THUMB")
    public String sThumb;

    @ColumnInfo(name = "S_FILE")
    public String sFile;

    @ColumnInfo(name = "S_S_THUMB")
    public String sSThumb;

    @ColumnInfo(name = "S_S_FILE")
    public String sSFile;

    @ColumnInfo(name = "B_GET_URL")
    public Boolean bGetURL = false;

    @ColumnInfo(name = "B_UPLOADED")
    public Boolean bUploaded = false;

    @ColumnInfo(name = "B_PROCESSED")
    public Boolean bProcessed = false;

    @ColumnInfo(name = "S_FIELD_ONE")
    public String sFieldOne;

    @ColumnInfo(name = "S_FIELD_TWO")
    public String sFieldTwo;

    @ColumnInfo(name = "S_FIELD_THREE")
    public String sFieldThree;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    @ColumnInfo(name = "DT_DATE_TIME")
    public String dtDateTime;

    @ColumnInfo(name = "I_SIZE")
    public Integer iSize;

    // Constructors
    public Video() {}

    // Copy constructor from Sugar ORM entity
    public Video(ai_Video sugarEntity) {
        this.id = sugarEntity.getId();
        this.iSVideoID = sugarEntity.iSVideoID;
        this.iInsID = sugarEntity.iInsID;
        this.iInsItemID = sugarEntity.iInsItemID;
        this.sThumb = sugarEntity.sThumb;
        this.sFile = sugarEntity.sFile;
        this.sSThumb = sugarEntity.sSThumb;
        this.sSFile = sugarEntity.sSFile;
        this.bGetURL = sugarEntity.bGetURL;
        this.bUploaded = sugarEntity.bUploaded;
        this.bProcessed = sugarEntity.bProcessed;
        this.sFieldOne = sugarEntity.sFieldOne;
        this.sFieldTwo = sugarEntity.sFieldTwo;
        this.sFieldThree = sugarEntity.sFieldThree;
        this.bDeleted = sugarEntity.bDeleted;
        this.dtDateTime = sugarEntity.dtDateTime;
        this.iSize = sugarEntity.iSize;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Video toSugarEntity() {
        ai_Video sugar = new ai_Video();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSVideoID = this.iSVideoID != null ? this.iSVideoID : 0;
        sugar.iInsID = this.iInsID != null ? this.iInsID : 0;
        sugar.iInsItemID = this.iInsItemID != null ? this.iInsItemID : 0;
        sugar.sThumb = this.sThumb;
        sugar.sFile = this.sFile;
        sugar.sSThumb = this.sSThumb;
        sugar.sSFile = this.sSFile;
        sugar.bGetURL = this.bGetURL != null ? this.bGetURL : false;
        sugar.bUploaded = this.bUploaded != null ? this.bUploaded : false;
        sugar.bProcessed = this.bProcessed != null ? this.bProcessed : false;
        sugar.sFieldOne = this.sFieldOne;
        sugar.sFieldTwo = this.sFieldTwo;
        sugar.sFieldThree = this.sFieldThree;
        sugar.bDeleted = this.bDeleted != null ? this.bDeleted : false;
        sugar.dtDateTime = this.dtDateTime;
        sugar.iSize = this.iSize != null ? this.iSize : 0;
        return sugar;
    }
}