package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_Product;

/**
 * Room entity for ai_Product
 */
@Entity(tableName = "AIPRODUCT")
public class Product {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "i_s_product_id")
    public int iSProductID;
    
    @ColumnInfo(name = "s_check_list_id")
    public String sCheckListID;
    
    @ColumnInfo(name = "i_company_id")
    public int iCompanyID;
    
    @ColumnInfo(name = "s_sku")
    public String sSKU;
    
    @ColumnInfo(name = "s_name")
    public String sName;
    
    @ColumnInfo(name = "s_model")
    public String sModel;
    
    @ColumnInfo(name = "s_desp")
    public String sDesp;
    
    @ColumnInfo(name = "s_associate_area")
    public String sAssociateArea;
    
    @ColumnInfo(name = "s_associate_item")
    public String sAssociateItem;
    
    @ColumnInfo(name = "d_unit_cost")
    public double dUnitCost;
    
    @ColumnInfo(name = "b_allow_edit")
    public boolean bAllowEdit;
    
    @ColumnInfo(name = "b_one_off_cost")
    public boolean bOneOffCost;
    
    @ColumnInfo(name = "s_product_category")
    public String sProductCategory;
    
    @ColumnInfo(name = "s_unit_name")
    public String sUnitName;
    
    @ColumnInfo(name = "s_url")
    public String sURL;
    
    @ColumnInfo(name = "s_image")
    public String sImage;
    
    @ColumnInfo(name = "s_custom1")
    public String sCustom1;
    
    @ColumnInfo(name = "s_custom2")
    public String sCustom2;
    
    @ColumnInfo(name = "b_archive")
    public boolean bArchive;
    
    @ColumnInfo(name = "b_deleted")
    public boolean bDeleted;
    
    @ColumnInfo(name = "i_created_by")
    public int iCreatedBy;
    
    @ColumnInfo(name = "i_updated_by")
    public int iUpdatedBy;
    
    @ColumnInfo(name = "dt_update")
    public String dtUpdate;
    
    @ColumnInfo(name = "dt_date_time")
    public String dtDateTime;
    
    public Product() {}
    
    public Product(ai_Product source) {
        this.iSProductID = source.iSProductID;
        this.sCheckListID = source.sCheckListID;
        this.iCompanyID = source.iCompanyID;
        this.sSKU = source.sSKU;
        this.sName = source.sName;
        this.sModel = source.sModel;
        this.sDesp = source.sDesp;
        this.sAssociateArea = source.sAssociateArea;
        this.sAssociateItem = source.sAssociateItem;
        this.dUnitCost = source.dUnitCost;
        this.bAllowEdit = source.bAllowEdit;
        this.bOneOffCost = source.bOneOffCost;
        this.sProductCategory = source.sProductCategory;
        this.sUnitName = source.sUnitName;
        this.sURL = source.sURL;
        this.sImage = source.sImage;
        this.sCustom1 = source.sCustom1;
        this.sCustom2 = source.sCustom2;
        this.bArchive = source.bArchive;
        this.bDeleted = source.bDeleted;
        this.iCreatedBy = source.iCreatedBy;
        this.iUpdatedBy = source.iUpdatedBy;
        this.dtUpdate = source.dtUpdate;
        this.dtDateTime = source.dtDateTime;
    }
}