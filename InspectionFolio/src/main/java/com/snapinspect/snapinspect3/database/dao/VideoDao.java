package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Video;

import java.util.List;

@Dao
public interface VideoDao {
    
    // Basic CRUD operations using only existing columns
    @Query("SELECT * FROM AIVIDEO WHERE B_DELETED = 0")
    List<Video> getAllVideos();

    @Query("SELECT * FROM AIVIDEO WHERE ID = :id")
    Video getVideoById(long id);

    @Query("SELECT * FROM AIVIDEO WHERE I_S_VIDEO_ID = :serverVideoId AND B_DELETED = 0")
    Video getVideoByServerId(int serverVideoId);

    @Query("SELECT * FROM AIVIDEO WHERE I_INS_ID = :inspectionId AND B_DELETED = 0")
    List<Video> getVideosByInspectionId(int inspectionId);

    @Query("SELECT * FROM AIVIDEO WHERE I_INS_ITEM_ID = :insItemId AND B_DELETED = 0")
    List<Video> getVideosByInsItemId(long insItemId);

    @Query("SELECT * FROM AIVIDEO WHERE I_INS_ITEM_ID = :insItemId AND B_DELETED = 0")
    List<Video> getInsItemVideos(long insItemId);

    @Query("SELECT * FROM AIVIDEO WHERE S_FIELD_THREE = :noticeId AND I_INS_ITEM_ID = :insItemId AND B_DELETED = 0")
    List<Video> getNoticeVideos(long noticeId, long insItemId);

    @Query("SELECT * FROM AIVIDEO WHERE I_S_VIDEO_ID = :serverId AND B_DELETED = 0")
    List<Video> getVideosByServerId(int serverId);

    @Query("SELECT * FROM AIVIDEO WHERE B_UPLOADED = 0 AND B_DELETED = 0")
    List<Video> getUnuploadedVideos();

    @Query("SELECT * FROM AIVIDEO WHERE B_PROCESSED = 0 AND B_DELETED = 0")
    List<Video> getUnprocessedVideos();

    @Query("SELECT * FROM AIVIDEO WHERE B_GET_URL = 1 AND B_DELETED = 0")
    List<Video> getVideosWithUrl();

    @Insert
    long insertVideo(Video video);

    @Insert
    List<Long> insertVideos(List<Video> videos);

    @Update
    void updateVideo(Video video);

    @Update
    void updateVideos(List<Video> videos);

    @Delete
    void deleteVideo(Video video);

    @Query("UPDATE AIVIDEO SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIVIDEO SET B_DELETED = 1 WHERE I_S_VIDEO_ID = :serverVideoId")
    void softDeleteByServerId(int serverVideoId);

    @Query("UPDATE AIVIDEO SET B_UPLOADED = :uploaded WHERE I_S_VIDEO_ID = :serverVideoId")
    void updateUploadedFlag(int serverVideoId, boolean uploaded);

    @Query("UPDATE AIVIDEO SET B_PROCESSED = :processed WHERE I_S_VIDEO_ID = :serverVideoId")
    void updateProcessedFlag(int serverVideoId, boolean processed);

    @Query("UPDATE AIVIDEO SET B_GET_URL = :getUrl WHERE I_S_VIDEO_ID = :serverVideoId")
    void updateGetUrlFlag(int serverVideoId, boolean getUrl);

    @Query("SELECT COUNT(*) FROM AIVIDEO WHERE B_DELETED = 0")
    int getVideoCount();

    @Query("SELECT COUNT(*) FROM AIVIDEO WHERE I_INS_ID = :inspectionId AND B_DELETED = 0")
    int getVideoCountByInspection(int inspectionId);

    // Batch operations
    @Query("DELETE FROM AIVIDEO")
    void deleteAllVideos();

    @Query("UPDATE AIVIDEO SET B_DELETED = 1")
    void softDeleteAllVideos();
}