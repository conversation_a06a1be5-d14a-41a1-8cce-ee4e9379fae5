package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.File;

import java.util.List;

@Dao
public interface FileDao {
    
    // Basic CRUD operations using only existing columns
    @Query("SELECT * FROM AIFILE WHERE B_DELETED = 0")
    List<File> getAllFiles();

    @Query("SELECT * FROM AIFILE WHERE ID = :id")
    File getFileById(long id);

    @Query("SELECT * FROM AIFILE WHERE I_FILE_ID = :fileId AND B_DELETED = 0")
    File getFileByFileId(int fileId);

    @Query("SELECT * FROM AIFILE WHERE I_S_OBJECT_ID = :objectId AND B_DELETED = 0")
    List<File> getFilesByObjectId(int objectId);

    @Query("SELECT * FROM AIFILE WHERE S_FILE LIKE :filePattern AND B_DELETED = 0")
    List<File> searchFilesByFileName(String filePattern);

    @Query("SELECT * FROM AIFILE WHERE S_COMMENTS LIKE :commentPattern AND B_DELETED = 0")
    List<File> searchFilesByComments(String commentPattern);

    @Query("SELECT * FROM AIFILE WHERE B_UPLOADED = 0 AND B_DELETED = 0")
    List<File> getUnuploadedFiles();

    @Query("SELECT * FROM AIFILE WHERE B_UPLOADED = 1 AND B_DELETED = 0")
    List<File> getUploadedFiles();

    @Query("SELECT * FROM AIFILE WHERE S_LAT IS NOT NULL AND S_LONG IS NOT NULL AND B_DELETED = 0")
    List<File> getFilesWithLocation();

    @Insert
    long insertFile(File file);

    @Insert
    List<Long> insertFiles(List<File> files);

    @Update
    void updateFile(File file);

    @Update
    void updateFiles(List<File> files);

    @Delete
    void deleteFile(File file);

    @Query("UPDATE AIFILE SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIFILE SET B_DELETED = 1 WHERE I_FILE_ID = :fileId")
    void softDeleteByFileId(int fileId);

    @Query("UPDATE AIFILE SET B_UPLOADED = :uploaded WHERE I_FILE_ID = :fileId")
    void updateUploadedFlag(int fileId, boolean uploaded);

    @Query("SELECT COUNT(*) FROM AIFILE WHERE B_DELETED = 0")
    int getFileCount();

    @Query("SELECT COUNT(*) FROM AIFILE WHERE I_S_OBJECT_ID = :objectId AND B_DELETED = 0")
    int getFileCountByObject(int objectId);

    @Query("SELECT SUM(I_SIZE) FROM AIFILE WHERE B_DELETED = 0")
    long getTotalFileSize();

    // Methods referenced in CommonDB.java
    @Query("SELECT * FROM AIFILE WHERE B_UPLOADED = 0 AND B_DELETED = 0")
    List<File> getFilesToUpload();

    @Query("SELECT * FROM AIFILE WHERE B_UPLOADED = 0 AND B_DELETED = 0")
    List<File> getFilesToAttach();

    @Query("SELECT * FROM AIFILE WHERE I_FILE_ID = :serverId AND B_DELETED = 0")
    File getFileByServerId(int serverId);

    @Query("SELECT * FROM AIFILE WHERE I_S_OBJECT_ID = :assetId AND S_CUSTOM_ONE LIKE '%ASTPTO%' AND B_DELETED = 0")
    File getPropertyPhoto(int assetId);

    @Query("SELECT * FROM AIFILE WHERE I_S_OBJECT_ID = :assetId AND B_DELETED = 0")
    List<File> getFilesByAssetId(int assetId);

    // Batch operations
    @Query("DELETE FROM AIFILE")
    void deleteAllFiles();

    @Query("UPDATE AIFILE SET B_DELETED = 1")
    void softDeleteAllFiles();
}