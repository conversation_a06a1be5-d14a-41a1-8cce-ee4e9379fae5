package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_FloorPlan;

import java.util.Date;

/**
 * Room entity for ai_FloorPlan
 */
@Entity(tableName = "AIFLOORPLAN")
public class FloorPlan {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_FLOOR_PLAN_ID")
    public int iFloorPlanID;
    
    @ColumnInfo(name = "I_S_FLOOR_PLAN_ID")
    public int iSFloorPlanID;
    
    @ColumnInfo(name = "I_S_ASSET_ID")
    public int iSAssetID;
    
    @ColumnInfo(name = "I_CREATE_CUSTOMER_ID")
    public int iCreateCustomerID;
    
    @ColumnInfo(name = "S_MEMBER")
    public String sMember;
    
    @ColumnInfo(name = "S_TITLE")
    public String sTitle;
    
    @ColumnInfo(name = "S_DESP")
    public String sDesp;
    
    @ColumnInfo(name = "S_PLAN_PATH")
    public String sPlanPath;
    
    @ColumnInfo(name = "S_IMAGE_PATH")
    public String sImagePath;
    
    @ColumnInfo(name = "S_MARKS")
    public String sMarks;
    
    @ColumnInfo(name = "S_CUSTOM1")
    public String sCustom1;
    
    @ColumnInfo(name = "S_CUSTOM2")
    public String sCustom2;
    
    @ColumnInfo(name = "S_CUSTOM3")
    public String sCustom3;
    
    @ColumnInfo(name = "B_ARCHIVE")
    public boolean bArchive;
    
    @ColumnInfo(name = "B_DELETED")
    public boolean bDeleted;
    
    @ColumnInfo(name = "DT_UPDATE")
    public Date dtUpdate;
    
    @ColumnInfo(name = "DT_DATE_TIME")
    public Date dtDateTime;
    
    public FloorPlan() {}
    
    public FloorPlan(ai_FloorPlan source) {
        this.iFloorPlanID = source.iFloorPlanID;
        this.iSFloorPlanID = source.iSFloorPlanID;
        this.iSAssetID = source.iSAssetID;
        this.iCreateCustomerID = source.iCreateCustomerID;
        this.sMember = source.sMember;
        this.sTitle = source.sTitle;
        this.sDesp = source.sDesp;
        this.sPlanPath = source.sPlanPath;
        this.sImagePath = source.sImagePath;
        this.sMarks = source.sMarks;
        this.sCustom1 = source.sCustom1;
        this.sCustom2 = source.sCustom2;
        this.sCustom3 = source.sCustom3;
        this.bArchive = source.bArchive;
        this.bDeleted = source.bDeleted;
        this.dtUpdate = source.dtUpdate;
        this.dtDateTime = source.dtDateTime;
    }
    
    public FloorPlan(int iFloorPlanID, int iSFloorPlanID, int iSAssetID, int iCreateCustomerID,
                    String sMember, String sTitle, String sDesp, String sPlanPath, String sImagePath,
                    String sMarks, String sCustom1, String sCustom2, String sCustom3,
                    boolean bArchive, boolean bDeleted, Date dtUpdate, Date dtDateTime) {
        this.iFloorPlanID = iFloorPlanID;
        this.iSFloorPlanID = iSFloorPlanID;
        this.iSAssetID = iSAssetID;
        this.iCreateCustomerID = iCreateCustomerID;
        this.sMember = sMember;
        this.sTitle = sTitle;
        this.sDesp = sDesp;
        this.sPlanPath = sPlanPath;
        this.sImagePath = sImagePath;
        this.sMarks = sMarks;
        this.sCustom1 = sCustom1;
        this.sCustom2 = sCustom2;
        this.sCustom3 = sCustom3;
        this.bArchive = bArchive;
        this.bDeleted = bDeleted;
        this.dtUpdate = dtUpdate;
        this.dtDateTime = dtDateTime;
    }
}