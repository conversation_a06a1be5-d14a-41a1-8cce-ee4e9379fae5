package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_User;

@Entity(
    tableName = "AIUSER",
    indices = {
        @Index(value = "I_CUSTOMER_ID"),
        @Index(value = "S_EMAIL"),
        @Index(value = "B_DELETED")
    }
)
public class User {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_CUSTOMER_ID")
    public Integer iCustomerID;

    @ColumnInfo(name = "S_NAME")
    public String sName;

    @ColumnInfo(name = "S_EMAIL")
    public String sEmail;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    // Constructors
    public User() {}

    // Copy constructor from Sugar ORM entity
    public User(ai_User sugarEntity) {
        this.id = sugarEntity.getId();
        this.iCustomerID = sugarEntity.iCustomerID;
        this.sName = sugarEntity.sName;
        this.sEmail = sugarEntity.sEmail;
        this.bDeleted = sugarEntity.bDeleted;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_User toSugarEntity() {
        ai_User sugar = new ai_User();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iCustomerID = this.iCustomerID;
        sugar.sName = this.sName;
        sugar.sEmail = this.sEmail;
        sugar.bDeleted = this.bDeleted;
        return sugar;
    }

    // Helper methods for UI components
    public String getNameLetters() {
        return com.snapinspect.snapinspect3.util.StringUtils.getFirstLetters(sName);
    }

    public String getAvatarColor() {
        String[] colors = com.snapinspect.snapinspect3.Helper.Constants.USER_COLORS;
        if (com.snapinspect.snapinspect3.util.StringUtils.isEmpty(sEmail)) return colors[0];

        int hash = Math.abs(sEmail.hashCode());
        int colorIndex = hash % colors.length;
        return colors[colorIndex];
    }
}