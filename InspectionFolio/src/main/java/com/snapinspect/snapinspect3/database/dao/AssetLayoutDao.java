package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.AssetLayout;

import java.util.List;

@Dao
public interface AssetLayoutDao {
    
    @Query("SELECT * FROM AIASSET_LAYOUT WHERE B_DELETED = 0")
    List<AssetLayout> getAllAssetLayouts();

    @Query("SELECT * FROM AIASSET_LAYOUT WHERE ID = :id")
    AssetLayout getAssetLayoutById(long id);

    @Query("SELECT * FROM AIASSET_LAYOUT WHERE I_S_ASSET_LAYOUT_ID = :serverAssetLayoutId AND B_DELETED = 0")
    AssetLayout getAssetLayoutByServerId(int serverAssetLayoutId);

    @Query("SELECT * FROM AIASSET_LAYOUT WHERE I_ASSET_ID = :assetId AND B_DELETED = 0")
    List<AssetLayout> getAssetLayoutsByAssetId(int assetId);

    @Query("SELECT * FROM AIASSET_LAYOUT WHERE I_LAYOUT_ID = :layoutId AND B_DELETED = 0")
    List<AssetLayout> getAssetLayoutsByLayoutId(int layoutId);

    @Query("SELECT * FROM AIASSET_LAYOUT WHERE I_ASSET_ID = :assetId AND I_LAYOUT_ID = :layoutId AND B_DELETED = 0")
    AssetLayout getAssetLayoutByAssetAndLayout(int assetId, int layoutId);

    @Query("SELECT * FROM AIASSET_LAYOUT WHERE I_S_ASSET_ID = :assetId AND B_DELETED = 0")
    List<AssetLayout> getAssetLayoutsByServerAssetId(int assetId);

    @Insert
    long insertAssetLayout(AssetLayout assetLayout);

    @Insert
    List<Long> insertAssetLayouts(List<AssetLayout> assetLayouts);

    @Update
    void updateAssetLayout(AssetLayout assetLayout);

    @Update
    void updateAssetLayouts(List<AssetLayout> assetLayouts);

    @Delete
    void deleteAssetLayout(AssetLayout assetLayout);

    @Query("UPDATE AIASSET_LAYOUT SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIASSET_LAYOUT SET B_DELETED = 1 WHERE I_S_ASSET_LAYOUT_ID = :serverAssetLayoutId")
    void softDeleteByServerId(int serverAssetLayoutId);

    @Query("UPDATE AIASSET_LAYOUT SET B_DELETED = 1 WHERE I_ASSET_ID = :assetId")
    void softDeleteByAssetId(int assetId);

    @Query("SELECT COUNT(*) FROM AIASSET_LAYOUT WHERE B_DELETED = 0")
    int getAssetLayoutCount();

    @Query("SELECT COUNT(*) FROM AIASSET_LAYOUT WHERE I_ASSET_ID = :assetId AND B_DELETED = 0")
    int getAssetLayoutCountByAsset(int assetId);

    // Batch operations
    @Query("DELETE FROM AIASSET_LAYOUT")
    void deleteAllAssetLayouts();

    @Query("UPDATE AIASSET_LAYOUT SET B_DELETED = 1")
    void softDeleteAllAssetLayouts();
}