package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_Layout;

@Entity(
    tableName = "AILAYOUT",
    indices = {
        @Index(value = "I_S_LAYOUT_ID"),
        @Index(value = "I_SP_LAYOUT_ID"),
        @Index(value = "B_DELETED")
    }
)
public class Layout {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_S_LAYOUT_ID")
    public Integer iSLayoutID;

    @ColumnInfo(name = "I_SP_LAYOUT_ID")
    public Integer iSPLayoutID;

    @ColumnInfo(name = "S_PTC")
    public String sPTC;

    @ColumnInfo(name = "S_Q_TYPE")
    public String sQType;

    @ColumnInfo(name = "S_NAME")
    public String sName;

    @ColumnInfo(name = "S_FV_ONE_CONFIG")
    public String sFVOneConfig;

    @ColumnInfo(name = "S_FV_TWO_CONFIG")
    public String sFVTwoConfig;

    @ColumnInfo(name = "S_FV_THREE_CONFIG")
    public String sFVThreeConfig;

    @ColumnInfo(name = "S_FV_FOUR_CONFIG")
    public String sFVFourConfig;

    @ColumnInfo(name = "S_FV_FIVE_CONFIG")
    public String sFVFiveConfig;

    @ColumnInfo(name = "S_FV_SIX_CONFIG")
    public String sFVSixConfig;

    @ColumnInfo(name = "S_SV_ONE_CONFIG")
    public String sSVOneConfig;

    @ColumnInfo(name = "S_SV_TWO_CONFIG")
    public String sSVTwoConfig;

    @ColumnInfo(name = "S_SV_THREE_CONFIG")
    public String sSVThreeConfig;

    @ColumnInfo(name = "S_SV_FOUR_CONFIG")
    public String sSVFourConfig;

    @ColumnInfo(name = "S_SV_FIVE_CONFIG")
    public String sSVFiveConfig;

    @ColumnInfo(name = "S_SV_SIX_CONFIG")
    public String sSVSixConfig;

    @ColumnInfo(name = "S_F_CONFIG")
    public String sFConfig;

    @ColumnInfo(name = "S_S_CONFIG")
    public String sSConfig;

    @ColumnInfo(name = "S_FIELD_ONE")
    public String sFieldOne;

    @ColumnInfo(name = "S_FIELD_TWO")
    public String sFieldTwo;

    @ColumnInfo(name = "S_FIELD_THREE")
    public String sFieldThree;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    // Constructors
    public Layout() {}

    // Copy constructor from Sugar ORM entity
    public Layout(ai_Layout sugarEntity) {
        this.id = sugarEntity.getId();
        this.iSLayoutID = sugarEntity.iSLayoutID;
        this.iSPLayoutID = sugarEntity.iSPLayoutID;
        this.sPTC = sugarEntity.sPTC;
        this.sQType = sugarEntity.sQType;
        this.sName = sugarEntity.sName;
        this.sFVOneConfig = sugarEntity.sFVOneConfig;
        this.sFVTwoConfig = sugarEntity.sFVTwoConfig;
        this.sFVThreeConfig = sugarEntity.sFVThreeConfig;
        this.sFVFourConfig = sugarEntity.sFVFourConfig;
        this.sFVFiveConfig = sugarEntity.sFVFiveConfig;
        this.sFVSixConfig = sugarEntity.sFVSixConfig;
        this.sSVOneConfig = sugarEntity.sSVOneConfig;
        this.sSVTwoConfig = sugarEntity.sSVTwoConfig;
        this.sSVThreeConfig = sugarEntity.sSVThreeConfig;
        this.sSVFourConfig = sugarEntity.sSVFourConfig;
        this.sSVFiveConfig = sugarEntity.sSVFiveConfig;
        this.sSVSixConfig = sugarEntity.sSVSixConfig;
        this.sFConfig = sugarEntity.sFConfig;
        this.sSConfig = sugarEntity.sSConfig;
        this.sFieldOne = sugarEntity.sFieldOne;
        this.sFieldTwo = sugarEntity.sFieldTwo;
        this.sFieldThree = sugarEntity.sFieldThree;
        this.bDeleted = sugarEntity.bDeleted;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Layout toSugarEntity() {
        ai_Layout sugar = new ai_Layout();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSLayoutID = this.iSLayoutID != null ? this.iSLayoutID : 0;
        sugar.iSPLayoutID = this.iSPLayoutID != null ? this.iSPLayoutID : 0;
        sugar.sPTC = this.sPTC;
        sugar.sQType = this.sQType;
        sugar.sName = this.sName;
        sugar.sFVOneConfig = this.sFVOneConfig;
        sugar.sFVTwoConfig = this.sFVTwoConfig;
        sugar.sFVThreeConfig = this.sFVThreeConfig;
        sugar.sFVFourConfig = this.sFVFourConfig;
        sugar.sFVFiveConfig = this.sFVFiveConfig;
        sugar.sFVSixConfig = this.sFVSixConfig;
        sugar.sSVOneConfig = this.sSVOneConfig;
        sugar.sSVTwoConfig = this.sSVTwoConfig;
        sugar.sSVThreeConfig = this.sSVThreeConfig;
        sugar.sSVFourConfig = this.sSVFourConfig;
        sugar.sSVFiveConfig = this.sSVFiveConfig;
        sugar.sSVSixConfig = this.sSVSixConfig;
        sugar.sFConfig = this.sFConfig;
        sugar.sSConfig = this.sSConfig;
        sugar.sFieldOne = this.sFieldOne;
        sugar.sFieldTwo = this.sFieldTwo;
        sugar.sFieldThree = this.sFieldThree;
        sugar.bDeleted = this.bDeleted != null ? this.bDeleted : false;
        return sugar;
    }
}