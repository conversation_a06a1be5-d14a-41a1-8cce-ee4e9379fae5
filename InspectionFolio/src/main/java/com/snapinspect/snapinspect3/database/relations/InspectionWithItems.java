package com.snapinspect.snapinspect3.database.relations;

import androidx.room.Embedded;
import androidx.room.Relation;
import com.snapinspect.snapinspect3.database.entities.Inspection;
import com.snapinspect.snapinspect3.database.entities.InsItem;
import com.snapinspect.snapinspect3.database.entities.Photo;

import java.util.List;

public class InspectionWithItems {
    @Embedded
    public Inspection inspection;

    @Relation(
        parentColumn = "ID",
        entityColumn = "I_INS_ID"
    )
    public List<InsItem> items;

    @Relation(
        parentColumn = "ID",
        entityColumn = "I_INS_ID"
    )
    public List<Photo> photos;
}