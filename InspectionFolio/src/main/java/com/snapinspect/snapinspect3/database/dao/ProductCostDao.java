package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.ProductCost;

import java.util.List;

@Dao
public interface ProductCostDao {
    
    @Query("SELECT * FROM AIPRODUCT_COST WHERE B_DELETED = 0")
    List<ProductCost> getAllProductCosts();

    @Query("SELECT * FROM AIPRODUCT_COST WHERE ID = :id")
    ProductCost getProductCostById(long id);

    @Query("SELECT * FROM AIPRODUCT_COST WHERE I_S_COSTING_ID = :serverCostingId AND B_DELETED = 0")
    ProductCost getProductCostByServerId(int serverCostingId);

    @Query("SELECT * FROM AIPRODUCT_COST WHERE I_PRODUCT_ID = :productId AND B_DELETED = 0")
    List<ProductCost> getProductCostsByProductId(int productId);



    @Insert
    long insertProductCost(ProductCost productCost);

    @Insert
    List<Long> insertProductCosts(List<ProductCost> productCosts);

    @Update
    void updateProductCost(ProductCost productCost);

    @Update
    void updateProductCosts(List<ProductCost> productCosts);

    @Delete
    void deleteProductCost(ProductCost productCost);

    @Query("UPDATE AIPRODUCT_COST SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIPRODUCT_COST SET B_DELETED = 1 WHERE I_S_COSTING_ID = :serverCostingId")
    void softDeleteByServerId(int serverCostingId);

    @Query("UPDATE AIPRODUCT_COST SET B_DELETED = 1 WHERE I_PRODUCT_ID = :productId")
    void softDeleteByProductId(int productId);

    @Query("SELECT COUNT(*) FROM AIPRODUCT_COST WHERE B_DELETED = 0")
    int getProductCostCount();


    // Batch operations
    @Query("DELETE FROM AIPRODUCT_COST")
    void deleteAllProductCosts();

    @Query("UPDATE AIPRODUCT_COST SET B_DELETED = 1")
    void softDeleteAllProductCosts();
}