package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Photo;

import java.util.List;

@Dao
public interface PhotoDao {
    
    @Query("SELECT * FROM AIPHOTO WHERE B_DELETED = 0")
    List<Photo> getAllPhotos();

    @Query("SELECT * FROM AIPHOTO WHERE ID = :id")
    Photo getPhotoById(long id);

    @Query("SELECT * FROM AIPHOTO WHERE I_S_PHOTO_ID = :serverPhotoId AND B_DELETED = 0")
    Photo getPhotoByServerId(int serverPhotoId);

    @Query("SELECT * FROM AIPHOTO WHERE I_INS_ID = :inspectionId AND B_DELETED = 0 " +
           "ORDER BY DT_DATE_TIME DESC")
    List<Photo> getPhotosByInspectionId(int inspectionId);

    @Query("SELECT * FROM AIPHOTO WHERE I_INS_ITEM_ID = :insItemId AND B_DELETED = 0 " +
           "ORDER BY DT_DATE_TIME DESC")
    List<Photo> getPhotosByInsItemId(int insItemId);

    @Query("SELECT * FROM AIPHOTO WHERE B_UPLOADED = :uploaded AND B_DELETED = 0")
    List<Photo> getPhotosByUploadStatus(boolean uploaded);

    @Query("SELECT * FROM AIPHOTO WHERE B_UPLOADED = 0 AND B_DELETED = 0")
    List<Photo> getUnuploadedPhotos();

    @Query("SELECT * FROM AIPHOTO WHERE S_FILE = :filePath AND B_DELETED = 0")
    Photo getPhotoByFilePath(String filePath);

    @Insert
    long insertPhoto(Photo photo);

    @Insert
    List<Long> insertPhotos(List<Photo> photos);

    @Update
    void updatePhoto(Photo photo);

    @Update
    void updatePhotos(List<Photo> photos);

    @Delete
    void deletePhoto(Photo photo);

    @Query("UPDATE AIPHOTO SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIPHOTO SET B_DELETED = 1 WHERE I_S_PHOTO_ID = :serverPhotoId")
    void softDeleteByServerId(int serverPhotoId);

    @Query("UPDATE AIPHOTO SET B_DELETED = 1 WHERE I_INS_ID = :inspectionId")
    void softDeleteByInspectionId(int inspectionId);

    @Query("UPDATE AIPHOTO SET B_DELETED = 1 WHERE I_INS_ITEM_ID = :insItemId")
    void softDeleteByInsItemId(int insItemId);

    @Query("UPDATE AIPHOTO SET B_UPLOADED = :uploaded WHERE ID = :id")
    void updateUploadStatus(long id, boolean uploaded);

    @Query("UPDATE AIPHOTO SET I_S_PHOTO_ID = :serverPhotoId WHERE ID = :id")
    void updateServerPhotoId(long id, int serverPhotoId);

    @Query("SELECT COUNT(*) FROM AIPHOTO WHERE B_DELETED = 0")
    int getPhotoCount();

    @Query("SELECT COUNT(*) FROM AIPHOTO WHERE I_INS_ID = :inspectionId AND B_DELETED = 0")
    int getPhotoCountByInspectionId(int inspectionId);

    @Query("SELECT COUNT(*) FROM AIPHOTO WHERE I_INS_ITEM_ID = :insItemId AND B_DELETED = 0")
    int getPhotoCountByInsItemId(int insItemId);

    @Query("SELECT COUNT(*) FROM AIPHOTO WHERE ID IN (:photoIds) AND B_DELETED = 0")
    long getPhotoCountForIds(List<Long> photoIds);

    // Get photos with location data
    @Query("SELECT * FROM AIPHOTO WHERE S_LAT IS NOT NULL AND S_LAT != '' " +
           "AND S_LONG IS NOT NULL AND S_LONG != '' AND B_DELETED = 0")
    List<Photo> getPhotosWithLocation();

    // Get photos by size range
    @Query("SELECT * FROM AIPHOTO WHERE I_SIZE >= :minSize AND I_SIZE <= :maxSize AND B_DELETED = 0")
    List<Photo> getPhotosBySizeRange(int minSize, int maxSize);

    // Get photos by date range
    @Query("SELECT * FROM AIPHOTO WHERE DT_DATE_TIME >= :startDate AND DT_DATE_TIME <= :endDate " +
           "AND B_DELETED = 0 ORDER BY DT_DATE_TIME DESC")
    List<Photo> getPhotosByDateRange(String startDate, String endDate);

    // Search photos by comments
    @Query("SELECT * FROM AIPHOTO WHERE S_COMMENTS LIKE :searchTerm AND B_DELETED = 0 " +
           "ORDER BY DT_DATE_TIME DESC")
    List<Photo> searchPhotosByComments(String searchTerm);

    // Get photos by file extension
    @Query("SELECT * FROM AIPHOTO WHERE S_FILE LIKE :extension AND B_DELETED = 0")
    List<Photo> getPhotosByExtension(String extension);

    // Batch operations
    @Query("DELETE FROM AIPHOTO")
    void deleteAllPhotos();

    @Query("UPDATE AIPHOTO SET B_DELETED = 1")
    void softDeleteAllPhotos();

    @Query("UPDATE AIPHOTO SET B_UPLOADED = 1 WHERE ID IN (:photoIds)")
    void markPhotosAsUploaded(List<Long> photoIds);

    // File management
    @Query("SELECT S_FILE FROM AIPHOTO WHERE B_DELETED = 0")
    List<String> getAllPhotoFilePaths();

    @Query("SELECT S_THUMB FROM AIPHOTO WHERE B_DELETED = 0 AND S_THUMB IS NOT NULL")
    List<String> getAllThumbnailPaths();

    // Statistics
    @Query("SELECT SUM(I_SIZE) FROM AIPHOTO WHERE B_DELETED = 0")
    long getTotalPhotoSize();

    @Query("SELECT AVG(I_SIZE) FROM AIPHOTO WHERE B_DELETED = 0 AND I_SIZE > 0")
    double getAveragePhotoSize();
}