package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Transaction;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Assets;
import com.snapinspect.snapinspect3.database.relations.AssetWithInspections;

import java.util.List;

@Dao
public interface AssetsDao {
    
    @Query("SELECT * FROM AIASSETS WHERE B_DELETED = 0")
    List<Assets> getAllAssets();

    @Query("SELECT * FROM AIASSETS WHERE ID = :id")
    Assets getAssetById(long id);

    @Query("SELECT * FROM AIASSETS WHERE I_S_ASSET_ID = :serverAssetId AND B_DELETED = 0")
    Assets getAssetByServerId(int serverAssetId);

    @Query("SELECT * FROM AIASSETS WHERE I_S_ASSET_ID = :serverAssetId")
    Assets getAssetByServerIdIncludeDeleted(int serverAssetId);

    @Query("SELECT * FROM AIASSETS WHERE " +
           "(S_ADDRESS_ONE LIKE :filter OR S_ADDRESS_TWO LIKE :filter) " +
           "AND I_SP_ASSET_ID = :parentAssetId AND B_DELETED = 0 " +
           "ORDER BY S_ADDRESS_ONE COLLATE NOCASE LIMIT :limit OFFSET :offset")
    List<Assets> searchAssets(String filter, int parentAssetId, int limit, int offset);

    @Query("SELECT * FROM AIASSETS WHERE I_CUSTOMER_ID = :customerId AND B_DELETED = 0 " +
           "ORDER BY S_ADDRESS_ONE COLLATE NOCASE")
    List<Assets> getAssetsByCustomerId(int customerId);

    @Query("SELECT * FROM AIASSETS WHERE I_SP_ASSET_ID = :parentAssetId AND B_DELETED = 0 " +
           "ORDER BY S_ADDRESS_ONE COLLATE NOCASE")
    List<Assets> getChildAssets(int parentAssetId);

    @Query("SELECT * FROM AIASSETS WHERE I_SP_ASSET_ID = 0 AND B_DELETED = 0 " +
           "ORDER BY S_ADDRESS_ONE COLLATE NOCASE")
    List<Assets> getRootAssets();

    @Insert
    long insertAsset(Assets asset);

    @Insert
    List<Long> insertAssets(List<Assets> assets);

    @Update
    void updateAsset(Assets asset);

    @Update
    void updateAssets(List<Assets> assets);

    @Delete
    void deleteAsset(Assets asset);

    @Query("UPDATE AIASSETS SET B_DELETED = 1 WHERE I_S_ASSET_ID = :assetId")
    void softDeleteByServerId(int assetId);

    @Query("UPDATE AIASSETS SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("SELECT COUNT(*) FROM AIASSETS WHERE B_DELETED = 0")
    int getAssetCount();

    @Query("SELECT COUNT(*) FROM AIASSETS WHERE I_CUSTOMER_ID = :customerId AND B_DELETED = 0")
    int getAssetCountByCustomer(int customerId);

    @Transaction
    @Query("SELECT * FROM AIASSETS WHERE B_DELETED = 0")
    List<AssetWithInspections> getAssetsWithInspections();

    @Transaction
    @Query("SELECT * FROM AIASSETS WHERE I_S_ASSET_ID = :assetId AND B_DELETED = 0")
    AssetWithInspections getAssetWithInspections(int assetId);


    // Custom queries for specific business logic
    @Query("SELECT * FROM AIASSETS WHERE S_FILTER LIKE :filter AND B_DELETED = 0")
    List<Assets> getAssetsByFilter(String filter);

    @Query("UPDATE AIASSETS SET B_PUSH = :push WHERE I_S_ASSET_ID = :assetId")
    void updatePushFlag(int assetId, boolean push);

    @Query("SELECT * FROM AIASSETS WHERE B_PUSH = 1 AND B_DELETED = 0")
    List<Assets> getAssetsMarkedForPush();

    // Batch operations
    @Query("DELETE FROM AIASSETS")
    void deleteAllAssets();

    @Query("UPDATE AIASSETS SET B_DELETED = 1")
    void softDeleteAllAssets();
}