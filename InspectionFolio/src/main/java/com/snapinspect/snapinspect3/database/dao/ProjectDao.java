package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Project;

import java.util.List;

@Dao
public interface ProjectDao {
    
    // Basic CRUD operations using only existing columns
    @Query("SELECT * FROM AIPROJECT WHERE B_DELETED = 0")
    List<Project> getAllProjects();

    @Query("SELECT * FROM AIPROJECT WHERE ID = :id")
    Project getProjectById(long id);

    @Query("SELECT * FROM AIPROJECT WHERE I_S_PROJECT_ID = :serverProjectId AND B_DELETED = 0")
    Project getProjectByServerId(int serverProjectId);

    // Use existing columns only
    @Query("SELECT * FROM AIPROJECT WHERE I_CREATOR_ID = :creatorId AND B_DELETED = 0")
    List<Project> getProjectsByCreatorId(int creatorId);

    @Query("SELECT * FROM AIPROJECT WHERE I_COMPANY_ID = :companyId AND B_DELETED = 0")
    List<Project> getProjectsByCompanyId(int companyId);

    @Query("SELECT * FROM AIPROJECT WHERE S_NAME LIKE :namePattern AND B_DELETED = 0")
    List<Project> searchProjectsByName(String namePattern);

    @Query("SELECT * FROM AIPROJECT WHERE S_STATUS = :status AND B_DELETED = 0")
    List<Project> getProjectsByStatus(String status);

    @Query("SELECT * FROM AIPROJECT WHERE B_ACTIVE = 1 AND B_DELETED = 0")
    List<Project> getActiveProjects();

    @Insert
    long insertProject(Project project);

    @Insert
    List<Long> insertProjects(List<Project> projects);

    @Update
    void updateProject(Project project);

    @Update
    void updateProjects(List<Project> projects);

    @Delete
    void deleteProject(Project project);

    @Query("UPDATE AIPROJECT SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIPROJECT SET B_DELETED = 1 WHERE I_S_PROJECT_ID = :serverProjectId")
    void softDeleteByServerId(int serverProjectId);

    @Query("UPDATE AIPROJECT SET S_STATUS = :status WHERE I_S_PROJECT_ID = :serverProjectId")
    void updateProjectStatus(int serverProjectId, String status);

    @Query("SELECT COUNT(*) FROM AIPROJECT WHERE B_DELETED = 0")
    int getProjectCount();

    @Query("SELECT COUNT(*) FROM AIPROJECT WHERE I_COMPANY_ID = :companyId AND B_DELETED = 0")
    int getProjectCountByCompany(int companyId);

    // Batch operations
    @Query("DELETE FROM AIPROJECT")
    void deleteAllProjects();

    @Query("UPDATE AIPROJECT SET B_DELETED = 1")
    void softDeleteAllProjects();
}