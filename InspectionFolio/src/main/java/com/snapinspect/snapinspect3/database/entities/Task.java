package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;
import com.snapinspect.snapinspect3.IF_Object.ai_Task;
import java.util.Date;

@Entity(
    tableName = "AITASK",
    indices = {
        @Index(value = "I_S_NOTIFICATION_ID"),
        @Index(value = "I_PROPERTY_ID"),
        @Index(value = "I_INSPECTION_ID"),
        @Index(value = "I_INS_ITEM_ID"),
        @Index(value = "B_DELETED"),
        @Index(value = "B_CLOSED")
    }
)
@TypeConverters({com.snapinspect.snapinspect3.database.converters.TypeConverters.DateConverter.class})
public class Task {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_S_NOTIFICATION_ID")
    public Integer iSNotificationID;

    @ColumnInfo(name = "I_SUBMIT_CUSTOMER_ID")
    public Integer iSubmitCustomerID;

    @ColumnInfo(name = "I_FOLLOW_UP_CUSTOMER_ID")
    public Integer iFollowUpCustomerID;

    @ColumnInfo(name = "I_PROPERTY_ID")
    public Integer iPropertyID;

    @ColumnInfo(name = "I_COMPANY_ID")
    public Integer iCompanyID;

    @ColumnInfo(name = "I_INS_ITEM_ID")
    public Integer iInsItemID;

    @ColumnInfo(name = "I_INSPECTION_ID")
    public Integer iInspectionID;

    @ColumnInfo(name = "S_CATEGORY")
    public String sCategory;

    @ColumnInfo(name = "I_PRIORITY")
    public Integer iPriority;

    @ColumnInfo(name = "S_CODE")
    public String sCode;

    @ColumnInfo(name = "S_TITLE")
    public String sTitle;

    @ColumnInfo(name = "S_DESCRIPTION")
    public String sDescription;

    @ColumnInfo(name = "S_PHOTO_URL")
    public String sPhotoURL;

    @ColumnInfo(name = "S_VIDEO_URL")
    public String sVideoURL;

    @ColumnInfo(name = "DT_DATE_DUE")
    public Date dtDateDue;

    @ColumnInfo(name = "S_CUSTOM1")
    public String sCustom1;

    @ColumnInfo(name = "S_CUSTOM2")
    public String sCustom2;

    @ColumnInfo(name = "B_CLOSED")
    public Boolean bClosed = false;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    @ColumnInfo(name = "DT_DATE_TIME")
    public Date dtDateTime;

    @ColumnInfo(name = "DT_COMPLETE")
    public Date dtComplete;

    @ColumnInfo(name = "DT_UPDATE")
    public Date dtUpdate;

    @ColumnInfo(name = "I_P_TASK_ID")
    public Integer iPTaskID;

    @ColumnInfo(name = "ARR_MEMBER")
    public String arrMember;

    @ColumnInfo(name = "S_STATUS")
    public String sStatus;

    @ColumnInfo(name = "S_STATUS_CODE")
    public String sStatusCode;

    @ColumnInfo(name = "I_CATEGORY_ID")
    public Integer iCategoryID;

    @ColumnInfo(name = "DT_UPDATEMSG")
    public Date dtUpdateMsg;

    @ColumnInfo(name = "D_COST")
    public Double dCost;

    // Constructors
    public Task() {}

    // Copy constructor from Sugar ORM entity
    public Task(ai_Task sugarEntity) {
        this.id = sugarEntity.getId();
        this.iSNotificationID = sugarEntity.iSNotificationID;
        this.iSubmitCustomerID = sugarEntity.iSubmitCustomerID;
        this.iFollowUpCustomerID = sugarEntity.iFollowUpCustomerID;
        this.iPropertyID = sugarEntity.iPropertyID;
        this.iCompanyID = sugarEntity.iCompanyID;
        this.iInsItemID = sugarEntity.iInsItemID;
        this.iInspectionID = sugarEntity.iInspectionID;
        this.sCategory = sugarEntity.sCategory;
        this.iPriority = sugarEntity.iPriority;
        this.sCode = sugarEntity.sCode;
        this.sTitle = sugarEntity.sTitle;
        this.sDescription = sugarEntity.sDescription;
        this.sPhotoURL = sugarEntity.sPhotoURL;
        this.sVideoURL = sugarEntity.sVideoURL;
        this.dtDateDue = sugarEntity.dtDateDue;
        this.sCustom1 = sugarEntity.sCustom1;
        this.sCustom2 = sugarEntity.sCustom2;
        this.bClosed = sugarEntity.bClosed;
        this.bDeleted = sugarEntity.bDeleted;
        this.dtDateTime = sugarEntity.dtDateTime;
        this.dtComplete = sugarEntity.dtComplete;
        this.dtUpdate = sugarEntity.dtUpdate;
        this.iPTaskID = sugarEntity.iPTaskID;
        this.arrMember = sugarEntity.arrMember;
        this.sStatus = sugarEntity.sStatus;
        this.sStatusCode = sugarEntity.sStatusCode;
        this.iCategoryID = sugarEntity.iCategoryID;
        this.dtUpdateMsg = sugarEntity.dtUpdate_Msg;
        this.dCost = sugarEntity.dCost;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Task toSugarEntity() {
        ai_Task sugar = new ai_Task();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSNotificationID = this.iSNotificationID != null ? this.iSNotificationID : 0;
        sugar.iSubmitCustomerID = this.iSubmitCustomerID != null ? this.iSubmitCustomerID : 0;
        sugar.iFollowUpCustomerID = this.iFollowUpCustomerID != null ? this.iFollowUpCustomerID : 0;
        sugar.iPropertyID = this.iPropertyID != null ? this.iPropertyID : 0;
        sugar.iCompanyID = this.iCompanyID != null ? this.iCompanyID : 0;
        sugar.iInsItemID = this.iInsItemID != null ? this.iInsItemID : 0;
        sugar.iInspectionID = this.iInspectionID != null ? this.iInspectionID : 0;
        sugar.sCategory = this.sCategory;
        sugar.iPriority = this.iPriority != null ? this.iPriority : 1;
        sugar.sCode = this.sCode;
        sugar.sTitle = this.sTitle;
        sugar.sDescription = this.sDescription;
        sugar.sPhotoURL = this.sPhotoURL;
        sugar.sVideoURL = this.sVideoURL;
        sugar.dtDateDue = this.dtDateDue;
        sugar.sCustom1 = this.sCustom1;
        sugar.sCustom2 = this.sCustom2;
        sugar.bClosed = this.bClosed != null ? this.bClosed : false;
        sugar.bDeleted = this.bDeleted != null ? this.bDeleted : false;
        sugar.dtDateTime = this.dtDateTime;
        sugar.dtComplete = this.dtComplete;
        sugar.dtUpdate = this.dtUpdate;
        sugar.iPTaskID = this.iPTaskID != null ? this.iPTaskID : 0;
        sugar.arrMember = this.arrMember;
        sugar.sStatus = this.sStatus;
        sugar.sStatusCode = this.sStatusCode;
        sugar.iCategoryID = this.iCategoryID != null ? this.iCategoryID : 0;
        sugar.dtUpdate_Msg = this.dtUpdateMsg;
        sugar.dCost = this.dCost != null ? this.dCost : 0.0;
        return sugar;
    }
}