package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.CheckList;

import java.util.List;

@Dao
public interface CheckListDao {
    
    @Query("SELECT * FROM AICHECK_LIST")
    List<CheckList> getAllCheckLists();

    @Query("SELECT * FROM AICHECK_LIST WHERE ID = :id")
    CheckList getCheckListById(long id);

    @Query("SELECT * FROM AICHECK_LIST WHERE I_S_CHECK_LIST_ID = :serverCheckListId")
    CheckList getCheckListByServerId(int serverCheckListId);

    @Query("SELECT * FROM AICHECK_LIST WHERE S_TITLE LIKE :titlePattern")
    List<CheckList> searchCheckListsByTitle(String titlePattern);

    @Insert
    long insertCheckList(CheckList checkList);

    @Insert
    List<Long> insertCheckLists(List<CheckList> checkLists);

    @Update
    void updateCheckList(CheckList checkList);

    @Update
    void updateCheckLists(List<CheckList> checkLists);

    @Delete
    void deleteCheckList(CheckList checkList);

    @Query("DELETE FROM AICHECK_LIST WHERE ID = :id")
    void deleteById(long id);

    @Query("DELETE FROM AICHECK_LIST WHERE I_S_CHECK_LIST_ID = :serverCheckListId")
    void deleteByServerId(int serverCheckListId);

    @Query("SELECT COUNT(*) FROM AICHECK_LIST")
    int getCheckListCount();

    @Query("DELETE FROM AICHECK_LIST")
    void deleteAllCheckLists();
}