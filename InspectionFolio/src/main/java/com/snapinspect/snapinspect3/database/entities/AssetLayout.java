package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_AssetLayout;

@Entity(
    tableName = "AIASSET_LAYOUT",
    indices = {
        @Index(value = "I_S_LAYOUT_ID"),
        @Index(value = "I_S_ASSET_ID"),
        @Index(value = "I_S_ASSET_LAYOUT_ID"),
        @Index(value = "I_SORT")
    }
)
public class AssetLayout {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_S_LAYOUT_ID")
    public Integer iSLayoutID;

    @ColumnInfo(name = "I_S_ASSET_ID")
    public Integer iSAssetID;

    @ColumnInfo(name = "S_NAME")
    public String sName;

    @ColumnInfo(name = "S_CHILD_ID")
    public String sChildID;

    @ColumnInfo(name = "S_MORE_ITEMS")
    public String sMoreItems;

    @ColumnInfo(name = "S_FIELD_ONE")
    public String sFieldOne;

    @ColumnInfo(name = "S_FIELD_TWO")
    public String sFieldTwo;

    @ColumnInfo(name = "S_FIELD_THREE")
    public String sFieldThree;

    @ColumnInfo(name = "I_SORT")
    public Integer iSort;

    @ColumnInfo(name = "I_S_ASSET_LAYOUT_ID")
    public Integer iSAssetLayoutID;

    @ColumnInfo(name = "I_ASSET_ID") 
    public Integer iAssetID;

    @ColumnInfo(name = "I_LAYOUT_ID")
    public Integer iLayoutID;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    // Constructors
    public AssetLayout() {}

    // Copy constructor from Sugar ORM entity
    public AssetLayout(ai_AssetLayout sugarEntity) {
        this.id = sugarEntity.getId();
        this.iSLayoutID = sugarEntity.iSLayoutID;
        this.iSAssetID = sugarEntity.iSAssetID;
        this.sName = sugarEntity.sName;
        this.sChildID = sugarEntity.sChildID;
        this.sMoreItems = sugarEntity.sMoreItems;
        this.sFieldOne = sugarEntity.sFieldOne;
        this.sFieldTwo = sugarEntity.sFieldTwo;
        this.sFieldThree = sugarEntity.sFieldThree;
        this.iSort = sugarEntity.iSort;
        this.iSAssetLayoutID = sugarEntity.iSAssetLayoutID;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_AssetLayout toSugarEntity() {
        ai_AssetLayout sugar = new ai_AssetLayout();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSLayoutID = this.iSLayoutID != null ? this.iSLayoutID : 0;
        sugar.iSAssetID = this.iSAssetID != null ? this.iSAssetID : 0;
        sugar.sName = this.sName;
        sugar.sChildID = this.sChildID;
        sugar.sMoreItems = this.sMoreItems;
        sugar.sFieldOne = this.sFieldOne;
        sugar.sFieldTwo = this.sFieldTwo;
        sugar.sFieldThree = this.sFieldThree;
        sugar.iSort = this.iSort != null ? this.iSort : 0;
        sugar.iSAssetLayoutID = this.iSAssetLayoutID != null ? this.iSAssetLayoutID : 0;
        return sugar;
    }
}