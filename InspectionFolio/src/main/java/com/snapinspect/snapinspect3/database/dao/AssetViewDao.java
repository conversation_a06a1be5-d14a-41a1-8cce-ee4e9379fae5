package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.AssetView;

import java.util.List;

@Dao
public interface AssetViewDao {
    
    @Query("SELECT * FROM AIASSET_VIEW WHERE B_DELETED = 0")
    List<AssetView> getAllAssetViews();

    @Query("SELECT * FROM AIASSET_VIEW WHERE ID = :id")
    AssetView getAssetViewById(long id);

    @Query("SELECT * FROM AIASSET_VIEW WHERE I_S_ASSET_VIEW_ID = :serverAssetViewId AND B_DELETED = 0")
    AssetView getAssetViewByServerId(int serverAssetViewId);

    @Query("SELECT * FROM AIASSET_VIEW WHERE I_CUSTOMER_ID = :customerId AND B_DELETED = 0")
    List<AssetView> getAssetViewsByCustomerId(int customerId);

    @Query("SELECT * FROM AIASSET_VIEW WHERE I_GROUP_ID = :groupId AND B_DELETED = 0")
    List<AssetView> getAssetViewsByGroupId(int groupId);

    @Query("SELECT * FROM AIASSET_VIEW WHERE I_CUSTOMER_ID = :customerId AND I_GROUP_ID = :groupId AND B_DELETED = 0")
    AssetView getAssetViewByCustomerAndGroup(int customerId, int groupId);

    @Insert
    long insertAssetView(AssetView assetView);

    @Insert
    List<Long> insertAssetViews(List<AssetView> assetViews);

    @Update
    void updateAssetView(AssetView assetView);

    @Update
    void updateAssetViews(List<AssetView> assetViews);

    @Delete
    void deleteAssetView(AssetView assetView);

    @Query("UPDATE AIASSET_VIEW SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIASSET_VIEW SET B_DELETED = 1 WHERE I_S_ASSET_VIEW_ID = :serverAssetViewId")
    void softDeleteByServerId(int serverAssetViewId);

    @Query("SELECT COUNT(*) FROM AIASSET_VIEW WHERE B_DELETED = 0")
    int getAssetViewCount();

    @Query("SELECT COUNT(*) FROM AIASSET_VIEW WHERE I_CUSTOMER_ID = :customerId AND B_DELETED = 0")
    int getAssetViewCountByCustomer(int customerId);

    // Batch operations
    @Query("DELETE FROM AIASSET_VIEW")
    void deleteAllAssetViews();

    @Query("UPDATE AIASSET_VIEW SET B_DELETED = 1")
    void softDeleteAllAssetViews();
}