package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_File;

@Entity(
    tableName = "AIFILE",
    indices = {
        @Index(value = "I_FILE_ID"),
        @Index(value = "I_S_OBJECT_ID"),
        @Index(value = "B_DELETED"),
        @Index(value = "B_UPLOADED")
    }
)
public class File {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_FILE_ID")
    public Integer iFileID;

    @ColumnInfo(name = "I_S_OBJECT_ID")
    public Integer iSObjectID;

    @ColumnInfo(name = "S_FILE")
    public String sFile;

    @ColumnInfo(name = "S_COMMENTS")
    public String sComments;

    @ColumnInfo(name = "S_LAT")
    public String sLat;

    @ColumnInfo(name = "S_LONG")
    public String sLong;

    @ColumnInfo(name = "B_UPLOADED")
    public Boolean bUploaded = false;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    @ColumnInfo(name = "DT_DATE_TIME")
    public String dtDateTime;

    @ColumnInfo(name = "I_SIZE")
    public Integer iSize;

    @ColumnInfo(name = "S_CUSTOM_ONE")
    public String sCustomOne;

    @ColumnInfo(name = "S_CUSTOM_TWO")
    public String sCustomTwo;

    // Constructors
    public File() {}

    // Copy constructor from Sugar ORM entity
    public File(ai_File sugarEntity) {
        this.id = sugarEntity.getId();
        this.iFileID = sugarEntity.iFileID;
        this.iSObjectID = sugarEntity.iSObjectID;
        this.sFile = sugarEntity.sFile;
        this.sComments = sugarEntity.sComments;
        this.sLat = sugarEntity.sLat;
        this.sLong = sugarEntity.sLong;
        this.bUploaded = sugarEntity.bUploaded;
        this.bDeleted = sugarEntity.bDeleted;
        this.dtDateTime = sugarEntity.dtDateTime;
        this.iSize = sugarEntity.iSize;
        this.sCustomOne = sugarEntity.sCustomOne;
        this.sCustomTwo = sugarEntity.sCustomTwo;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_File toSugarEntity() {
        ai_File sugar = new ai_File();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iFileID = this.iFileID != null ? this.iFileID : 0;
        sugar.iSObjectID = this.iSObjectID != null ? this.iSObjectID : 0;
        sugar.sFile = this.sFile;
        sugar.sComments = this.sComments;
        sugar.sLat = this.sLat;
        sugar.sLong = this.sLong;
        sugar.bUploaded = this.bUploaded != null ? this.bUploaded : false;
        sugar.bDeleted = this.bDeleted != null ? this.bDeleted : false;
        sugar.dtDateTime = this.dtDateTime;
        sugar.iSize = this.iSize != null ? this.iSize : 0;
        sugar.sCustomOne = this.sCustomOne;
        sugar.sCustomTwo = this.sCustomTwo;
        return sugar;
    }
}