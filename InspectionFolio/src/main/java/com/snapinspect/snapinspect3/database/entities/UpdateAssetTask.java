package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_UpdateAssetTask;

import java.util.Date;

/**
 * Room entity for ai_UpdateAssetTask
 */
@Entity(tableName = "AIUPDATE_ASSET_TASK")
public class UpdateAssetTask {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_S_UPDATE_ASSET_TASK_ID")
    public Integer iSUpdateAssetTaskID;
    
    @ColumnInfo(name = "I_ASSET_ID")
    public int iAssetID;
    
    @ColumnInfo(name = "I_USER_ID")
    public Integer iUserID;
    
    @ColumnInfo(name = "I_STATUS")
    public Integer iStatus;
    
    @ColumnInfo(name = "B_PROCESSED")
    public Boolean bProcessed;
    
    @ColumnInfo(name = "DT_LAST_SYNC")
    public String dtLastSync;
    
    @ColumnInfo(name = "S_CUSTOM1")
    public String sCustom1;
    
    @ColumnInfo(name = "S_CUSTOM2")
    public String sCustom2;
    
    @ColumnInfo(name = "B_DELETED")
    public boolean bDeleted;
    
    @ColumnInfo(name = "DT_UPDATE")
    public Date dtUpdate;
    
    @ColumnInfo(name = "DT_DATE_TIME")
    public Date dtDateTime;
    
    public UpdateAssetTask() {}
    
    public UpdateAssetTask(ai_UpdateAssetTask source) {
        this.iSUpdateAssetTaskID = source.iSUpdateAssetTaskID;
        this.iAssetID = source.iAssetID;
        this.iUserID = source.iUserID;
        this.iStatus = source.iStatus;
        this.bProcessed = source.bProcessed;
        this.dtLastSync = source.dtLastSync;
        this.sCustom1 = source.sCustom1;
        this.sCustom2 = source.sCustom2;
        this.bDeleted = source.bDeleted;
        this.dtUpdate = source.dtUpdate;
        this.dtDateTime = source.dtDateTime;
    }
    
    public UpdateAssetTask(int iAssetID, String dtLastSync) {
        this.iAssetID = iAssetID;
        this.dtLastSync = dtLastSync;
        this.iUserID = null;
        this.iStatus = null;
        this.bProcessed = false;
        this.sCustom1 = "";
        this.sCustom2 = "";
        this.bDeleted = false;
        this.dtUpdate = new Date();
        this.dtDateTime = new Date();
    }
    
    public UpdateAssetTask(int iAssetID, String dtLastSync, String sCustom1, String sCustom2,
                          boolean bDeleted, Date dtUpdate, Date dtDateTime) {
        this.iAssetID = iAssetID;
        this.dtLastSync = dtLastSync;
        this.iUserID = null;
        this.iStatus = null;
        this.bProcessed = false;
        this.sCustom1 = sCustom1;
        this.sCustom2 = sCustom2;
        this.bDeleted = bDeleted;
        this.dtUpdate = dtUpdate;
        this.dtDateTime = dtDateTime;
    }
}