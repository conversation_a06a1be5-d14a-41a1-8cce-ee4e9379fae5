package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_PropertyLayout;

import java.util.Date;

/**
 * Room entity for ai_PropertyLayout
 */
@Entity(tableName = "AIPROPERTY_LAYOUT")
public class PropertyLayout {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_S_PROPERTY_LAYOUT_ID")
    public int iSPropertyLayoutID;
    
    @ColumnInfo(name = "I_COMPANY_ID")
    public int iCompanyID;
    
    @ColumnInfo(name = "I_PROPERTY_ID")
    public int iPropertyID;
    
    @ColumnInfo(name = "S_PTC")
    public String sPTC;
    
    @ColumnInfo(name = "ARR_LAYOUT")
    public String arrLayout;
    
    @ColumnInfo(name = "S_CUSTOM1")
    public String sCustom1;
    
    @ColumnInfo(name = "S_CUSTOM2")
    public String sCustom2;
    
    @ColumnInfo(name = "DT_UPDATE")
    public Date dtUpdate;
    
    @ColumnInfo(name = "B_DELETED")
    public boolean bDeleted;
    
    @ColumnInfo(name = "DT_DATE_TIME")
    public Date dtDateTime;
    
    public PropertyLayout() {}
    
    public PropertyLayout(ai_PropertyLayout source) {
        this.iSPropertyLayoutID = source.iSPropertyLayoutID;
        this.iCompanyID = source.iCompanyID;
        this.iPropertyID = source.iPropertyID;
        this.sPTC = source.sPTC;
        this.arrLayout = source.getArrLayout();
        this.sCustom1 = source.sCustom1;
        this.sCustom2 = source.sCustom2;
        this.dtUpdate = source.dtUpdate;
        this.bDeleted = source.bDeleted;
        this.dtDateTime = source.dtDateTime;
    }
}