package com.snapinspect.snapinspect3.database.views;

import androidx.room.DatabaseView;

@DatabaseView(
    viewName = "VREQUEST_INSPECTION",
    value = "SELECT c.iInspectionID, c.sInsTitle as sInspectionTitle, c.sTitle, c.bComplete, c.bSynced, c.iSInsID, " +
            "(c.sInsTitle || ' ' || c.sTitle) as sInsSearchTerm, d.* " +
            "FROM VINSPECTION c LEFT JOIN VSCHEDULE d ON c.iSScheduleID = d.iSScheduleID WHERE c.iSScheduleID > 0 " +
            "UNION ALL " +
            "SELECT c.iInspectionID, c.sInsTitle as sInspectionTitle, c.sTitle, c.bComplete, c.bSynced, c.iSInsID, " +
            "(d.sAddress1 || ' ' || d.sAddress2) as sInsSearchTerm, d.* " +
            "FROM VSCHEDULE d LEFT JOIN VINSPECTION c ON c.iSScheduleID = d.iSScheduleID WHERE c.iSScheduleID IS NULL " +
            "ORDER BY iUnixTime"
)
public class VRequestInspectionView {
    public int iInspectionID;
    public String sInspectionTitle; 
    public String sTitle;
    public boolean bComplete;
    public boolean bSynced;
    public int iSInsID;
    public String sInsSearchTerm;
    
    // From VSCHEDULE
    public int iScheduleID;
    public int iSScheduleID;
    public int iSInsTypeID;
    public String sPTC;
    public String sInsTitle;
    public String sDateTime;
    public long iUnixTime;
    public boolean bCompleted;
    public String sCustom1;
    public String sCustom2;
    public String sType;
    public String sRRule;
    public String sEXRule;
    public int iPPAssetID;
    public int iPAssetID;
    public int iCustomerID;
    public String sRef;
    public String sBuildingAddress;
    public String sUnitAddress;
    public String sRoomAddress;
    public int iGroupID;
    public boolean bApartment;
    public int iAssetID;
    public int iID;
    public String sSearchTerm;
    public String sAddress1;
    public String sAddress2;
}