package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;

@Entity(
    tableName = "AIPHOTO",
    indices = {
        @Index(value = "I_INS_ID"),
        @Index(value = "I_INS_ITEM_ID"),
        @Index(value = "I_S_PHOTO_ID"),
        @Index(value = "B_DELETED"),
        @Index(value = "B_UPLOADED")
    },
    foreignKeys = {
        @ForeignKey(
            entity = Inspection.class,
            parentColumns = "ID",
            childColumns = "I_INS_ID"
        ),
        @ForeignKey(
            entity = InsItem.class,
            parentColumns = "ID",
            childColumns = "I_INS_ITEM_ID"
        )
    }
)
public class Photo {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_INS_ID")
    public Integer iInsID;

    @ColumnInfo(name = "I_INS_ITEM_ID")
    public Integer iInsItemID;

    @ColumnInfo(name = "I_S_PHOTO_ID")
    public Integer iSPhotoID;

    @ColumnInfo(name = "I_SIZE")
    public Integer iSize;

    @ColumnInfo(name = "S_FILE")
    public String sFile;

    @ColumnInfo(name = "S_THUMB")
    public String sThumb;

    @ColumnInfo(name = "S_COMMENTS")
    public String sComments;

    @ColumnInfo(name = "S_LAT")
    public String sLat;

    @ColumnInfo(name = "S_LONG")
    public String sLong;

    @ColumnInfo(name = "S_FIELD_ONE")
    public String sFieldOne;

    @ColumnInfo(name = "DT_DATE_TIME")
    public String dtDateTime;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    @ColumnInfo(name = "B_UPLOADED")
    public Boolean bUploaded = false;

    // Constructors
    public Photo() {}

    // Copy constructor from Sugar ORM entity
    public Photo(ai_Photo sugarEntity) {
        this.id = sugarEntity.getId();
        this.iInsID = sugarEntity.iInsID;
        this.iInsItemID = sugarEntity.iInsItemID;
        this.iSPhotoID = sugarEntity.iSPhotoID;
        this.iSize = sugarEntity.iSize;
        this.sFile = sugarEntity.sFile;
        this.sThumb = sugarEntity.sThumb;
        this.sComments = sugarEntity.sComments;
        this.sLat = sugarEntity.sLat;
        this.sLong = sugarEntity.sLong;
        this.sFieldOne = sugarEntity.sFieldOne;
        this.dtDateTime = sugarEntity.dtDateTime;
        this.bDeleted = sugarEntity.bDeleted;
        this.bUploaded = sugarEntity.bUploaded;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Photo toSugarEntity() {
        ai_Photo sugar = new ai_Photo();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iInsID = this.iInsID;
        sugar.iInsItemID = this.iInsItemID;
        sugar.iSPhotoID = this.iSPhotoID;
        sugar.iSize = this.iSize;
        sugar.sFile = this.sFile;
        sugar.sThumb = this.sThumb;
        sugar.sComments = this.sComments;
        sugar.sLat = this.sLat;
        sugar.sLong = this.sLong;
        sugar.sFieldOne = this.sFieldOne;
        sugar.dtDateTime = this.dtDateTime;
        sugar.bDeleted = this.bDeleted;
        sugar.bUploaded = this.bUploaded;
        return sugar;
    }
}