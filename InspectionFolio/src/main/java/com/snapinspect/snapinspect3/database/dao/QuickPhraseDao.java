package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.QuickPhrase;

import java.util.List;

@Dao
public interface QuickPhraseDao {
    
    @Query("SELECT * FROM AIQUICK_PHRASE")
    List<QuickPhrase> getAllQuickPhrases();

    @Query("SELECT * FROM AIQUICK_PHRASE WHERE ID = :id")
    QuickPhrase getQuickPhraseById(long id);

    @Query("SELECT * FROM AIQUICK_PHRASE WHERE I_QUICK_PHRASE_ID = :serverQuickPhraseId")
    QuickPhrase getQuickPhraseByServerId(int serverQuickPhraseId);

    @Query("SELECT * FROM AIQUICK_PHRASE WHERE S_COMMENTS LIKE :commentPattern")
    List<QuickPhrase> searchPhrasesByComments(String commentPattern);

    @Query("SELECT * FROM AIQUICK_PHRASE WHERE I_S_LAYOUT_ID = :layoutId")
    List<QuickPhrase> getQuickPhrasesByLayoutId(int layoutId);

    // @Insert
    // long insertQuickPhrase(QuickPhrase quickPhrase);

    // @Insert
    // List<Long> insertQuickPhrases(List<QuickPhrase> quickPhrases);

    // @Update
    // void updateQuickPhrase(QuickPhrase quickPhrase);

    // @Update
    // void updateQuickPhrases(List<QuickPhrase> quickPhrases);

    // @Delete
    // void deleteQuickPhrase(QuickPhrase quickPhrase);

    // @Query("UPDATE AIQUICKPHRASE SET B_DELETED = 1 WHERE ID = :id")
    // void softDeleteById(long id);

    // @Query("UPDATE AIQUICKPHRASE SET B_DELETED = 1 WHERE I_S_QUICK_PHRASE_ID = :serverQuickPhraseId")
    // void softDeleteByServerId(int serverQuickPhraseId);

    // @Query("SELECT COUNT(*) FROM AIQUICKPHRASE WHERE B_DELETED = 0")
    // int getQuickPhraseCount();

    // @Query("SELECT COUNT(*) FROM AIQUICKPHRASE WHERE I_S_LAYOUT_ID = :layoutId AND B_DELETED = 0")
    // int getQuickPhraseCountByLayout(int layoutId);

    // Batch operations
    // @Query("DELETE FROM AIQUICKPHRASE")
    // void deleteAllQuickPhrases();

    // @Query("UPDATE AIQUICKPHRASE SET B_DELETED = 1")
    // void softDeleteAllQuickPhrases();
}