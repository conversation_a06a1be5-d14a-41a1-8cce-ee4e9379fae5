package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_Log;

/**
 * Room entity for ai_Log
 */
@Entity(tableName = "AILOG")
public class Log {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "S_TYPE")
    public String sType;
    
    @ColumnInfo(name = "S_MESSAGE")
    public String sMessage;
    
    @ColumnInfo(name = "DT_DATE_TIME")
    public String dtDateTime;
    
    @ColumnInfo(name = "B_UPLOADED")
    public boolean bUploaded;
    
    @ColumnInfo(name = "B_DELETED")
    public boolean bDeleted;
    
    public Log() {}
    
    public Log(ai_Log source) {
        this.sType = source.sType;
        this.sMessage = source.sMessage;
        this.dtDateTime = source.dtDateTime;
        this.bUploaded = source.bUploaded;
        this.bDeleted = source.bDeleted;
    }
    
    public Log(String sType, String sMessage, String dtDateTime, boolean bUploaded) {
        this.sType = sType;
        this.sMessage = sMessage;
        this.dtDateTime = dtDateTime;
        this.bUploaded = bUploaded;
        this.bDeleted = false;
    }
}