package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_AssetView;


/**
 * Room entity for ai_AssetView
 */
@Entity(tableName = "AIASSET_VIEW")
public class AssetView {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_S_ASSET_VIEW_ID")
    public int iSAssetViewID;
    
    @ColumnInfo(name = "I_CUSTOMER_ID")
    public int iCustomerID;
    
    @ColumnInfo(name = "I_COMPANY_ID")
    public int iCompanyID;
    
    @ColumnInfo(name = "I_GROUP_ID")
    public int iGroupID;
    
    @ColumnInfo(name = "S_NAME")
    public String sName;
    
    @ColumnInfo(name = "S_DESCRIPTION")
    public String sDescription;
    
    @ColumnInfo(name = "S_CUSTOM1")
    public String sCustom1;
    
    @ColumnInfo(name = "S_CUSTOM2")
    public String sCustom2;
    
    @ColumnInfo(name = "B_ARCHIVED")
    public boolean bArchived;
    
    @ColumnInfo(name = "B_DELETED")
    public boolean bDeleted;
    
    @ColumnInfo(name = "DT_UPDATE")
    public Long dtUpdate;
    
    @ColumnInfo(name = "ARR_MEM")
    public String arrMem;
    
    public AssetView() {}
    
    public AssetView(ai_AssetView source) {
        this.iSAssetViewID = source.iSAssetViewID;
        this.iCustomerID = source.iCustomerID;
        this.iCompanyID = source.iCompanyID;
        this.iGroupID = source.iGroupID;
        this.sName = source.sName;
        this.sDescription = source.sDescription;
        this.sCustom1 = source.sCustom1;
        this.sCustom2 = source.sCustom2;
        this.bArchived = source.bArchived;
        this.bDeleted = source.bDeleted;
        this.dtUpdate = source.dtUpdate != null ? source.dtUpdate.getTime() : null;
        this.arrMem = source.arrMem;
    }
}