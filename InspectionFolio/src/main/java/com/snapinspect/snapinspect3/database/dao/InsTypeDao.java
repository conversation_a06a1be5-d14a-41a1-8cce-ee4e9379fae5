package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.InsType;

import java.util.List;

@Dao
public interface InsTypeDao {
    
    @Query("SELECT * FROM AIINS_TYPE WHERE B_DELETED = 0")
    List<InsType> getAllInsTypes();

    @Query("SELECT * FROM AIINS_TYPE WHERE ID = :id")
    InsType getInsTypeById(long id);

    @Query("SELECT * FROM AIINS_TYPE WHERE I_S_INS_TYPE_ID = :serverInsTypeId AND B_DELETED = 0")
    InsType getInsTypeByServerId(int serverInsTypeId);

    @Query("SELECT * FROM AIINS_TYPE WHERE S_INS_TITLE LIKE :titlePattern AND B_DELETED = 0")
    List<InsType> searchInsTypesByTitle(String titlePattern);

    @Query("SELECT * FROM AIINS_TYPE WHERE S_TYPE = :type AND B_DELETED = 0")
    List<InsType> getInsTypesByType(String type);

    @Query("SELECT * FROM AIINS_TYPE WHERE B_REM_LAYOUT = 0 AND B_DELETED = 0")
    List<InsType> getInsTypesWithLayout();

    @Insert
    long insertInsType(InsType insType);

    @Insert
    List<Long> insertInsTypes(List<InsType> insTypes);

    @Update
    void updateInsType(InsType insType);

    @Update
    void updateInsTypes(List<InsType> insTypes);

    @Delete
    void deleteInsType(InsType insType);

    @Query("UPDATE AIINS_TYPE SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIINS_TYPE SET B_DELETED = 1 WHERE I_S_INS_TYPE_ID = :serverInsTypeId")
    void softDeleteByServerId(int serverInsTypeId);

    @Query("UPDATE AIINS_TYPE SET B_REM_LAYOUT = :remLayout WHERE I_S_INS_TYPE_ID = :serverInsTypeId")
    void updateRemLayoutStatus(int serverInsTypeId, boolean remLayout);

    @Query("SELECT COUNT(*) FROM AIINS_TYPE WHERE B_DELETED = 0")
    int getInsTypeCount();

    @Query("SELECT COUNT(*) FROM AIINS_TYPE WHERE B_REM_LAYOUT = 0 AND B_DELETED = 0")
    int getInsTypeWithLayoutCount();

    // Batch operations
    @Query("DELETE FROM AIINS_TYPE")
    void deleteAllInsTypes();

    @Query("UPDATE AIINS_TYPE SET B_DELETED = 1")
    void softDeleteAllInsTypes();
}