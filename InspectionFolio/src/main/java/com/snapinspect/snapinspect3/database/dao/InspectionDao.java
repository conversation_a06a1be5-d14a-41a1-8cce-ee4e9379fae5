package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Transaction;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Inspection;
import com.snapinspect.snapinspect3.database.relations.InspectionWithItems;

import java.util.List;

@Dao
public interface InspectionDao {
    
    @Query("SELECT * FROM AIINSPECTION WHERE B_DELETED = 0")
    List<Inspection> getAllInspections();

    @Query("SELECT * FROM AIINSPECTION WHERE ID = :id")
    Inspection getInspectionById(long id);

    @Query("SELECT * FROM AIINSPECTION WHERE I_S_INS_ID = :serverInsId AND B_DELETED = 0")
    Inspection getInspectionByServerId(int serverInsId);

    @Query("SELECT * FROM AIINSPECTION WHERE I_S_ASSET_ID = :assetId AND B_DELETED = 0 " +
           "ORDER BY I_S_INS_ID DESC")
    List<Inspection> getInspectionsByAssetId(int assetId);

    @Query("SELECT * FROM AIINSPECTION WHERE I_S_ASSET_ID = :assetId " +
           "AND B_COMPLETE = :completed AND B_SYNCED = :synced AND B_DELETED = 0 " +
           "ORDER BY I_S_INS_ID DESC")
    List<Inspection> getInspectionsByAssetId(int assetId, boolean completed, boolean synced);

    @Query("SELECT * FROM AIINSPECTION WHERE I_S_INS_TYPE_ID = :insTypeId AND B_DELETED = 0")
    List<Inspection> getInspectionsByTypeId(int insTypeId);

    @Query("SELECT * FROM AIINSPECTION WHERE I_S_SCHEDULE_ID = :scheduleId AND B_DELETED = 0")
    List<Inspection> getInspectionsByScheduleId(int scheduleId);

    @Query("SELECT * FROM AIINSPECTION WHERE B_COMPLETE = :completed AND B_DELETED = 0 " +
           "ORDER BY DT_START_DATE DESC")
    List<Inspection> getInspectionsByCompletion(boolean completed);

    @Query("SELECT * FROM AIINSPECTION WHERE B_SYNCED = :synced AND B_DELETED = 0 " +
           "ORDER BY DT_START_DATE DESC")
    List<Inspection> getInspectionsBySyncStatus(boolean synced);

    @Query("SELECT * FROM AIINSPECTION WHERE B_COMPLETE = 1 AND B_SYNCED = 0 AND B_DELETED = 0")
    List<Inspection> getCompletedUnsyncedInspections();

    @Insert
    long insertInspection(Inspection inspection);

    @Insert
    List<Long> insertInspections(List<Inspection> inspections);

    @Update
    void updateInspection(Inspection inspection);

    @Update
    void updateInspections(List<Inspection> inspections);

    @Delete
    void deleteInspection(Inspection inspection);

    @Query("UPDATE AIINSPECTION SET B_DELETED = 1 WHERE I_S_INS_ID = :serverInsId")
    void softDeleteByServerId(int serverInsId);

    @Query("UPDATE AIINSPECTION SET B_DELETED = 1 WHERE I_S_ASSET_ID = :assetId AND B_SYNCED = 1")
    void softDeleteSyncedInspectionsByAssetId(int assetId);

    @Query("UPDATE AIINSPECTION SET B_COMPLETE = :complete WHERE ID = :id")
    void updateCompletionStatus(long id, boolean complete);

    @Query("UPDATE AIINSPECTION SET B_SYNCED = :synced WHERE ID = :id")
    void updateSyncStatus(long id, boolean synced);

    @Query("UPDATE AIINSPECTION SET B_LOCK = :lock WHERE ID = :id")
    void updateLockStatus(long id, boolean lock);

    @Query("SELECT COUNT(*) FROM AIINSPECTION WHERE B_DELETED = 0")
    int getInspectionCount();

    @Query("SELECT COUNT(*) FROM AIINSPECTION WHERE I_S_ASSET_ID = :assetId AND B_DELETED = 0")
    int getInspectionCountByAssetId(int assetId);

    @Query("SELECT COUNT(*) FROM AIINSPECTION WHERE B_COMPLETE = 1 AND B_DELETED = 0")
    int getCompletedInspectionCount();

    @Transaction
    @Query("SELECT * FROM AIINSPECTION WHERE B_DELETED = 0")
    List<InspectionWithItems> getInspectionsWithItems();

    @Transaction
    @Query("SELECT * FROM AIINSPECTION WHERE ID = :id AND B_DELETED = 0")
    InspectionWithItems getInspectionWithItems(long id);

    @Transaction
    @Query("SELECT * FROM AIINSPECTION WHERE I_S_ASSET_ID = :assetId AND B_DELETED = 0")
    List<InspectionWithItems> getInspectionsWithItemsByAssetId(int assetId);


    // Search functionality
    @Query("SELECT * FROM AIINSPECTION WHERE " +
           "(S_TITLE LIKE :searchTerm OR S_INS_TITLE LIKE :searchTerm OR S_COMMENTS LIKE :searchTerm) " +
           "AND B_DELETED = 0 ORDER BY DT_START_DATE DESC")
    List<Inspection> searchInspections(String searchTerm);

    // Date range queries
    @Query("SELECT * FROM AIINSPECTION WHERE DT_START_DATE >= :startDate AND DT_START_DATE <= :endDate " +
           "AND B_DELETED = 0 ORDER BY DT_START_DATE DESC")
    List<Inspection> getInspectionsByDateRange(String startDate, String endDate);

    // Batch operations
    @Query("DELETE FROM AIINSPECTION")
    void deleteAllInspections();

    @Query("UPDATE AIINSPECTION SET B_DELETED = 1")
    void softDeleteAllInspections();
}