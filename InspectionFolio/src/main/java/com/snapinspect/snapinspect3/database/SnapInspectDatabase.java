package com.snapinspect.snapinspect3.database;

import android.content.Context;
import androidx.room.Database;
import androidx.room.Room;
import androidx.room.RoomDatabase;
import androidx.room.TypeConverters;
import androidx.room.migration.Migration;
import androidx.sqlite.db.SupportSQLiteDatabase;

import com.snapinspect.snapinspect3.database.converters.TypeConverters.BooleanConverter;
import com.snapinspect.snapinspect3.database.converters.TypeConverters.DateConverter;
import com.snapinspect.snapinspect3.database.converters.TypeConverters.JsonConverter;
import com.snapinspect.snapinspect3.database.converters.TypeConverters.StringArrayConverter;

import com.snapinspect.snapinspect3.database.entities.Assets;
import com.snapinspect.snapinspect3.database.entities.Inspection;
import com.snapinspect.snapinspect3.database.entities.InsItem;
import com.snapinspect.snapinspect3.database.entities.Photo;
import com.snapinspect.snapinspect3.database.entities.User;
import com.snapinspect.snapinspect3.database.entities.Video;
import com.snapinspect.snapinspect3.database.entities.Task;
import com.snapinspect.snapinspect3.database.entities.Schedule;
import com.snapinspect.snapinspect3.database.entities.Project;
import com.snapinspect.snapinspect3.database.entities.Contact;
import com.snapinspect.snapinspect3.database.entities.Layout;
import com.snapinspect.snapinspect3.database.entities.File;
import com.snapinspect.snapinspect3.database.entities.Notification;
import com.snapinspect.snapinspect3.database.entities.Comment;
import com.snapinspect.snapinspect3.database.entities.AssetLayout;
import com.snapinspect.snapinspect3.database.entities.CheckList;
import com.snapinspect.snapinspect3.database.entities.InsAlert;
import com.snapinspect.snapinspect3.database.entities.InsType;
import com.snapinspect.snapinspect3.database.entities.QuickPhrase;
import com.snapinspect.snapinspect3.database.entities.AssetView;
import com.snapinspect.snapinspect3.database.entities.ProjectInspection;
import com.snapinspect.snapinspect3.database.entities.FloorPlan;
import com.snapinspect.snapinspect3.database.entities.Product;
import com.snapinspect.snapinspect3.database.entities.ProductCost;
import com.snapinspect.snapinspect3.database.entities.NoticeCategory;
import com.snapinspect.snapinspect3.database.entities.PropertyLayout;
import com.snapinspect.snapinspect3.database.entities.UpdateAssetTask;
import com.snapinspect.snapinspect3.database.entities.Inbox;
import com.snapinspect.snapinspect3.database.entities.TaskList;
import com.snapinspect.snapinspect3.database.entities.Log;

import com.snapinspect.snapinspect3.database.views.VAssetView;
import com.snapinspect.snapinspect3.database.views.VScheduleView;
import com.snapinspect.snapinspect3.database.views.VInspectionView;
import com.snapinspect.snapinspect3.database.views.VRequestInspectionView;

import com.snapinspect.snapinspect3.database.dao.AssetsDao;
import com.snapinspect.snapinspect3.database.dao.InspectionDao;
import com.snapinspect.snapinspect3.database.dao.PhotoDao;
import com.snapinspect.snapinspect3.database.dao.InsItemDao;
import com.snapinspect.snapinspect3.database.dao.UserDao;
import com.snapinspect.snapinspect3.database.dao.VideoDao;
import com.snapinspect.snapinspect3.database.dao.TaskDao;
import com.snapinspect.snapinspect3.database.dao.ScheduleDao;
import com.snapinspect.snapinspect3.database.dao.ProjectDao;
import com.snapinspect.snapinspect3.database.dao.ContactDao;
import com.snapinspect.snapinspect3.database.dao.LayoutDao;
import com.snapinspect.snapinspect3.database.dao.FileDao;
import com.snapinspect.snapinspect3.database.dao.NotificationDao;
import com.snapinspect.snapinspect3.database.dao.CommentDao;
import com.snapinspect.snapinspect3.database.dao.AssetLayoutDao;
import com.snapinspect.snapinspect3.database.dao.CheckListDao;
import com.snapinspect.snapinspect3.database.dao.InsAlertDao;
import com.snapinspect.snapinspect3.database.dao.InsTypeDao;
import com.snapinspect.snapinspect3.database.dao.QuickPhraseDao;
import com.snapinspect.snapinspect3.database.dao.AssetViewDao;
import com.snapinspect.snapinspect3.database.dao.ProjectInspectionDao;
import com.snapinspect.snapinspect3.database.dao.FloorPlanDao;
import com.snapinspect.snapinspect3.database.dao.ProductDao;
import com.snapinspect.snapinspect3.database.dao.ProductCostDao;
import com.snapinspect.snapinspect3.database.dao.NoticeCategoryDao;
import com.snapinspect.snapinspect3.database.dao.PropertyLayoutDao;
import com.snapinspect.snapinspect3.database.dao.UpdateAssetTaskDao;
import com.snapinspect.snapinspect3.database.dao.InboxDao;
import com.snapinspect.snapinspect3.database.dao.TaskListDao;
import com.snapinspect.snapinspect3.database.dao.LogDao;
import com.snapinspect.snapinspect3.database.dao.VScheduleDao;
import com.snapinspect.snapinspect3.database.dao.VInspectionDao;
import com.snapinspect.snapinspect3.database.dao.VRequestInspectionDao;

@Database(
    entities = {
        Assets.class,
        Inspection.class,
        InsItem.class,
        Photo.class,
        User.class,
        Video.class,
        Task.class,
        Schedule.class,
        Project.class,
        Contact.class,
        Layout.class,
        File.class,
        Notification.class,
        Comment.class,
        AssetLayout.class,
        CheckList.class,
        InsAlert.class,
        InsType.class,
        QuickPhrase.class,
        AssetView.class,
        ProjectInspection.class,
        FloorPlan.class,
        Product.class,
        ProductCost.class,
        NoticeCategory.class,
        PropertyLayout.class,
        UpdateAssetTask.class,
        Inbox.class,
        TaskList.class,
        Log.class,
    },
    views = {
        VAssetView.class,
        VScheduleView.class,
        VInspectionView.class,
        VRequestInspectionView.class
    },
    version = 1,
    exportSchema = true
)
@TypeConverters({
    BooleanConverter.class,
    DateConverter.class,
    JsonConverter.class,
    StringArrayConverter.class
})
public abstract class SnapInspectDatabase extends RoomDatabase {
    
    private static volatile SnapInspectDatabase INSTANCE;
    private static final String DATABASE_NAME = "if_data.db";
    
    // Abstract DAO methods
    public abstract AssetsDao assetsDao();
    public abstract InspectionDao inspectionDao();
    public abstract PhotoDao photoDao();
    public abstract InsItemDao insItemDao();
    public abstract UserDao userDao();
    public abstract VideoDao videoDao();
    public abstract TaskDao taskDao();
    public abstract ScheduleDao scheduleDao();
    public abstract ProjectDao projectDao();
    public abstract ContactDao contactDao();
    public abstract LayoutDao layoutDao();
    public abstract FileDao fileDao();
    public abstract NotificationDao notificationDao();
    public abstract CommentDao commentDao();
    public abstract AssetLayoutDao assetLayoutDao();
    public abstract CheckListDao checkListDao();
    public abstract InsAlertDao insAlertDao();
    public abstract InsTypeDao insTypeDao();
    public abstract QuickPhraseDao quickPhraseDao();
    public abstract AssetViewDao assetViewDao();
    public abstract ProjectInspectionDao projectInspectionDao();
    public abstract FloorPlanDao floorPlanDao();
    public abstract ProductDao productDao();
    public abstract ProductCostDao productCostDao();
    public abstract NoticeCategoryDao noticeCategoryDao();
    public abstract PropertyLayoutDao propertyLayoutDao();
    public abstract UpdateAssetTaskDao updateAssetTaskDao();
    public abstract InboxDao inboxDao();
    public abstract TaskListDao taskListDao();
    public abstract LogDao logDao();

    // View DAOs
    public abstract VScheduleDao vScheduleDao();
    public abstract VInspectionDao vInspectionDao();
    public abstract VRequestInspectionDao vRequestInspectionDao();
    
    public static SnapInspectDatabase getInstance(Context context) {
        if (INSTANCE == null) {
            synchronized (SnapInspectDatabase.class) {
                if (INSTANCE == null) {
                    INSTANCE = Room.databaseBuilder(
                        context.getApplicationContext(),
                        SnapInspectDatabase.class,
                        DATABASE_NAME
                    )
                    .addMigrations(MIGRATION_SUGAR_TO_ROOM, MIGRATION_11_12)
                    .build();
                }
            }
        }
        return INSTANCE;
    }
    
    /**
     * Migration from Sugar ORM to Room
     * Since we're keeping the same database file and table structure,
     * no actual schema changes are needed - just switching ORMs
     */
    static final Migration MIGRATION_SUGAR_TO_ROOM = new Migration(11, 11) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // No schema changes needed - just switching from Sugar ORM to Room
            // Room will validate that existing schema matches entity definitions
            
            // Log migration completion
            android.util.Log.i("SnapInspectDB", "Sugar ORM to Room migration completed");
        }
    };
    
    /**
     * Migration to add database views
     */
    static final Migration MIGRATION_11_12 = new Migration(11, 12) {
        @Override
        public void migrate(SupportSQLiteDatabase database) {
            // Views are automatically created by Room when database version changes
            // No manual SQL needed as Room handles view creation from @DatabaseView annotations
            
            android.util.Log.i("SnapInspectDB", "Database views migration completed");
        }
    };
    
    /**
     * Close database instance
     */
    public static void closeDatabase() {
        if (INSTANCE != null) {
            INSTANCE.close();
            INSTANCE = null;
        }
    }
    
    /**
     * For testing purposes - force recreate instance
     */
    public static void destroyInstance() {
        INSTANCE = null;
    }
}