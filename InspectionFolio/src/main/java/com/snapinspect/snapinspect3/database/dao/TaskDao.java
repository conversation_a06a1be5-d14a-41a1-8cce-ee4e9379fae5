package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Task;

import java.util.List;

@Dao
public interface TaskDao {
    
    @Query("SELECT * FROM AITASK WHERE B_DELETED = 0")
    List<Task> getAllTasks();

    @Query("SELECT * FROM AITASK WHERE ID = :id")
    Task getTaskById(long id);

    @Query("SELECT * FROM AITASK WHERE I_S_NOTIFICATION_ID = :serverTaskId AND B_DELETED = 0")
    Task getTaskByServerId(int serverTaskId);

    @Query("SELECT * FROM AITASK WHERE I_PROPERTY_ID = :assetId AND B_DELETED = 0")
    List<Task> getTasksByAssetId(int assetId);

    @Query("SELECT * FROM AITASK WHERE I_P_TASK_ID = :projectId AND B_DELETED = 0")
    List<Task> getTasksByProjectId(int projectId);

    @Query("SELECT * FROM AITASK WHERE I_SUBMIT_CUSTOMER_ID = :userId AND B_DELETED = 0")
    List<Task> getTasksByAssignedUserId(int userId);

    @Query("SELECT * FROM AITASK WHERE I_FOLLOW_UP_CUSTOMER_ID = :userId AND B_DELETED = 0")
    List<Task> getTasksByCreatedUserId(int userId);

    @Query("SELECT * FROM AITASK WHERE S_STATUS = :status AND B_DELETED = 0")
    List<Task> getTasksByStatus(String status);

    @Query("SELECT * FROM AITASK WHERE DT_DATE_DUE <= :dueDate AND B_DELETED = 0 ORDER BY DT_DATE_DUE")
    List<Task> getTasksDueBefore(String dueDate);

    @Insert
    long insertTask(Task task);

    @Insert
    List<Long> insertTasks(List<Task> tasks);

    @Update
    void updateTask(Task task);

    @Update
    void updateTasks(List<Task> tasks);

    @Delete
    void deleteTask(Task task);

    @Query("UPDATE AITASK SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AITASK SET B_DELETED = 1 WHERE I_S_NOTIFICATION_ID = :serverTaskId")
    void softDeleteByServerId(int serverTaskId);

    @Query("UPDATE AITASK SET S_STATUS = :status WHERE I_S_NOTIFICATION_ID = :serverTaskId")
    void updateTaskStatus(int serverTaskId, String status);

    @Query("SELECT COUNT(*) FROM AITASK WHERE B_DELETED = 0")
    int getTaskCount();

    @Query("SELECT COUNT(*) FROM AITASK WHERE I_SUBMIT_CUSTOMER_ID = :userId AND S_STATUS = :status AND B_DELETED = 0")
    int getTaskCountByUserAndStatus(int userId, String status);

    // Batch operations
    @Query("DELETE FROM AITASK")
    void deleteAllTasks();

    @Query("UPDATE AITASK SET B_DELETED = 1")
    void softDeleteAllTasks();
}