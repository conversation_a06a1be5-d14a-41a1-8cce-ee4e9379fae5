package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.ProjectInspection;

import java.util.List;

@Dao
public interface ProjectInspectionDao {
    
    @Query("SELECT * FROM AIPROJECT_INSPECTION WHERE B_DELETED = 0")
    List<ProjectInspection> getAllProjectInspections();

    @Query("SELECT * FROM AIPROJECT_INSPECTION WHERE ID = :id")
    ProjectInspection getProjectInspectionById(long id);

    @Query("SELECT * FROM AIPROJECT_INSPECTION WHERE I_S_INSPECTION_ID = :serverInspectionId AND B_DELETED = 0")
    ProjectInspection getProjectInspectionByServerInspectionId(int serverInspectionId);

    @Query("SELECT * FROM AIPROJECT_INSPECTION WHERE I_PROJECT_ID = :projectId AND B_DELETED = 0")
    List<ProjectInspection> getProjectInspectionsByProjectId(int projectId);

    @Query("SELECT * FROM AIPROJECT_INSPECTION WHERE I_INSPECTION_ID = :inspectionId AND B_DELETED = 0")
    List<ProjectInspection> getProjectInspectionsByInspectionId(int inspectionId);

    @Query("SELECT * FROM AIPROJECT_INSPECTION WHERE I_PROJECT_ID = :projectId AND I_INSPECTION_ID = :inspectionId AND B_DELETED = 0")
    ProjectInspection getProjectInspectionByProjectAndInspection(int projectId, int inspectionId);

    @Insert
    long insertProjectInspection(ProjectInspection projectInspection);

    @Insert
    List<Long> insertProjectInspections(List<ProjectInspection> projectInspections);

    @Update
    void updateProjectInspection(ProjectInspection projectInspection);

    @Update
    void updateProjectInspections(List<ProjectInspection> projectInspections);

    @Delete
    void deleteProjectInspection(ProjectInspection projectInspection);

    @Query("UPDATE AIPROJECT_INSPECTION SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIPROJECT_INSPECTION SET B_DELETED = 1 WHERE I_S_INSPECTION_ID = :serverInspectionId")
    void softDeleteByServerInspectionId(int serverInspectionId);

    @Query("UPDATE AIPROJECT_INSPECTION SET B_DELETED = 1 WHERE I_PROJECT_ID = :projectId")
    void softDeleteByProjectId(int projectId);

    @Query("SELECT COUNT(*) FROM AIPROJECT_INSPECTION WHERE B_DELETED = 0")
    int getProjectInspectionCount();

    @Query("SELECT COUNT(*) FROM AIPROJECT_INSPECTION WHERE I_PROJECT_ID = :projectId AND B_DELETED = 0")
    int getProjectInspectionCountByProject(int projectId);

    // Batch operations
    @Query("DELETE FROM AIPROJECT_INSPECTION")
    void deleteAllProjectInspections();

    @Query("UPDATE AIPROJECT_INSPECTION SET B_DELETED = 1")
    void softDeleteAllProjectInspections();
}