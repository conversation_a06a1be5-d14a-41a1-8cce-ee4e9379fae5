package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_Assets;

@Entity(
    tableName = "AIASSETS",
    indices = {
        @Index(value = "I_S_ASSET_ID", unique = true),
        @Index(value = "I_CUSTOMER_ID"),
        @Index(value = "I_SP_ASSET_ID"),
        @Index(value = "B_DELETED")
    }
)
public class Assets {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_S_ASSET_ID")
    public Integer iSAssetID;

    @ColumnInfo(name = "I_SP_ASSET_ID")
    public Integer iSPAssetID;

    @ColumnInfo(name = "S_ADDRESS_ONE")
    public String sAddressOne;

    @ColumnInfo(name = "S_ADDRESS_TWO")
    public String sAddressTwo;

    @ColumnInfo(name = "S_FILTER")
    public String sFilter;

    @ColumnInfo(name = "I_CUSTOMER_ID")
    public Integer iCustomerID;

    @ColumnInfo(name = "I_GROUP_ID")
    public Integer iGroupID;

    @ColumnInfo(name = "I_PL_VER_ID")
    public Integer iPLVerID;

    @ColumnInfo(name = "DT_INS_DUE")
    public String dtInsDue;

    @ColumnInfo(name = "S_NOTES")
    public String sNotes;

    @ColumnInfo(name = "S_KEY")
    public String sKey;

    @ColumnInfo(name = "S_ALARM")
    public String sAlarm;

    @ColumnInfo(name = "S_FIELD_ONE")
    public String sFieldOne;

    @ColumnInfo(name = "S_FIELD_TWO")
    public String sFieldTwo;

    @ColumnInfo(name = "S_FIELD_THREE")
    public String sFieldThree;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    @ColumnInfo(name = "B_PUSH")
    public Boolean bPush = false;

    // Constructors
    public Assets() {}

    // Copy constructor from Sugar ORM entity
    public Assets(ai_Assets sugarEntity) {
        this.id = sugarEntity.getId();
        this.iSAssetID = sugarEntity.iSAssetID;
        this.iSPAssetID = sugarEntity.iSPAssetID;
        this.sAddressOne = sugarEntity.sAddressOne;
        this.sAddressTwo = sugarEntity.sAddressTwo;
        this.sFilter = sugarEntity.sFilter;
        this.iCustomerID = sugarEntity.iCustomerID;
        this.iGroupID = sugarEntity.iGroupID;
        this.iPLVerID = sugarEntity.iPLVerID;
        this.dtInsDue = sugarEntity.dtInsDue;
        this.sNotes = sugarEntity.sNotes;
        this.sKey = sugarEntity.sKey;
        this.sAlarm = sugarEntity.sAlarm;
        this.sFieldOne = sugarEntity.sFieldOne;
        this.sFieldTwo = sugarEntity.sFieldTwo;
        this.sFieldThree = sugarEntity.sFieldThree;
        this.bDeleted = sugarEntity.bDeleted;
        this.bPush = sugarEntity.bPush;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Assets toSugarEntity() {
        ai_Assets sugar = new ai_Assets();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSAssetID = this.iSAssetID;
        sugar.iSPAssetID = this.iSPAssetID;
        sugar.sAddressOne = this.sAddressOne;
        sugar.sAddressTwo = this.sAddressTwo;
        sugar.sFilter = this.sFilter;
        sugar.iCustomerID = this.iCustomerID;
        sugar.iGroupID = this.iGroupID;
        sugar.iPLVerID = this.iPLVerID;
        sugar.dtInsDue = this.dtInsDue;
        sugar.sNotes = this.sNotes;
        sugar.sKey = this.sKey;
        sugar.sAlarm = this.sAlarm;
        sugar.sFieldOne = this.sFieldOne;
        sugar.sFieldTwo = this.sFieldTwo;
        sugar.sFieldThree = this.sFieldThree;
        sugar.bDeleted = this.bDeleted;
        sugar.bPush = this.bPush;
        return sugar;
    }
}