package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_Schedule;

@Entity(
    tableName = "AISCHEDULE",
    indices = {
        @Index(value = "I_S_SCHEDULE_ID"),
        @Index(value = "I_S_ASSET_ID"),
        @Index(value = "I_S_INS_TYPE_ID"),
        @Index(value = "B_DELETED"),
        @Index(value = "B_COMPLETED")
    }
)
public class Schedule {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_S_SCHEDULE_ID")
    public Integer iSScheduleID;

    @ColumnInfo(name = "I_S_ASSET_ID")
    public Integer iSAssetID;

    @ColumnInfo(name = "I_S_INS_TYPE_ID")
    public Integer iSInsTypeID;

    @ColumnInfo(name = "S_TYPE")
    public String sType;

    @ColumnInfo(name = "S_PTC")
    public String sPTC;

    @ColumnInfo(name = "S_INS_TITLE")
    public String sInsTitle;

    @ColumnInfo(name = "S_ADDRESS_ONE")
    public String sAddressOne;

    @ColumnInfo(name = "S_ADDRESS_TWO")
    public String sAddressTwo;

    @ColumnInfo(name = "DT_DATE_TIME")
    public String dtDateTime;

    @ColumnInfo(name = "I_UNIX_TIME")
    public Long iUnixTime;

    @ColumnInfo(name = "B_COMPLETED")
    public Boolean bCompleted = false;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    @ColumnInfo(name = "S_CUSTOM_ONE")
    public String sCustomOne;

    @ColumnInfo(name = "S_CUSTOM_TWO")
    public String sCustomTwo;

    @ColumnInfo(name = "S_R_RULE")
    public String sRRule;

    @ColumnInfo(name = "S_EX_RULE")
    public String sEXRule;

    // Constructors
    public Schedule() {}

    // Copy constructor from Sugar ORM entity
    public Schedule(ai_Schedule sugarEntity) {
        this.id = sugarEntity.getId();
        this.iSScheduleID = sugarEntity.iSScheduleID;
        this.iSAssetID = sugarEntity.iSAssetID;
        this.iSInsTypeID = sugarEntity.iSInsTypeID;
        this.sType = sugarEntity.sType;
        this.sPTC = sugarEntity.sPTC;
        this.sInsTitle = sugarEntity.sInsTitle;
        this.sAddressOne = sugarEntity.sAddressOne;
        this.sAddressTwo = sugarEntity.sAddressTwo;
        this.dtDateTime = sugarEntity.dtDateTime;
        this.iUnixTime = sugarEntity.iUnixTime;
        this.bCompleted = sugarEntity.bCompleted;
        this.bDeleted = sugarEntity.bDeleted;
        this.sCustomOne = sugarEntity.sCustomOne;
        this.sCustomTwo = sugarEntity.sCustomTwo;
        this.sRRule = sugarEntity.sRRule;
        this.sEXRule = sugarEntity.sEXRule;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Schedule toSugarEntity() {
        ai_Schedule sugar = new ai_Schedule();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSScheduleID = this.iSScheduleID != null ? this.iSScheduleID : 0;
        sugar.iSAssetID = this.iSAssetID != null ? this.iSAssetID : 0;
        sugar.iSInsTypeID = this.iSInsTypeID != null ? this.iSInsTypeID : 0;
        sugar.sType = this.sType;
        sugar.sPTC = this.sPTC;
        sugar.sInsTitle = this.sInsTitle;
        sugar.sAddressOne = this.sAddressOne;
        sugar.sAddressTwo = this.sAddressTwo;
        sugar.dtDateTime = this.dtDateTime;
        sugar.iUnixTime = this.iUnixTime != null ? this.iUnixTime : 0;
        sugar.bCompleted = this.bCompleted != null ? this.bCompleted : false;
        sugar.bDeleted = this.bDeleted != null ? this.bDeleted : false;
        sugar.sCustomOne = this.sCustomOne;
        sugar.sCustomTwo = this.sCustomTwo;
        sugar.sRRule = this.sRRule;
        sugar.sEXRule = this.sEXRule;
        return sugar;
    }
}