package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Layout;

import java.util.List;

@Dao
public interface LayoutDao {
    
    // Basic CRUD operations using only existing columns
    @Query("SELECT * FROM AILAYOUT WHERE B_DELETED = 0")
    List<Layout> getAllLayouts();

    @Query("SELECT * FROM AILAYOUT WHERE ID = :id")
    Layout getLayoutById(long id);

    @Query("SELECT * FROM AILAYOUT WHERE I_S_LAYOUT_ID = :serverLayoutId AND B_DELETED = 0")
    Layout getLayoutByServerId(int serverLayoutId);

    @Query("SELECT * FROM AILAYOUT WHERE I_SP_LAYOUT_ID = :spLayoutId AND B_DELETED = 0")
    Layout getLayoutBySpLayoutId(int spLayoutId);

    @Query("SELECT * FROM AILAYOUT WHERE S_NAME LIKE :namePattern AND B_DELETED = 0")
    List<Layout> searchLayoutsByName(String namePattern);

    @Query("SELECT * FROM AILAYOUT WHERE S_PTC = :ptc AND I_SP_LAYOUT_ID > 0 AND B_DELETED = 0 GROUP BY S_NAME")
    List<Layout> searchChildLayoutsByPTC(String ptc);

    @Query("SELECT * FROM AILAYOUT WHERE S_PTC = :ptc AND B_DELETED = 0")
    List<Layout> getLayoutsByPtc(String ptc);

    @Query("SELECT * FROM AILAYOUT WHERE S_PTC = :ptc AND I_SP_LAYOUT_ID = 0 AND B_DELETED = 0")
    List<Layout> getParentLayouts(String ptc);

    @Query("SELECT * FROM AILAYOUT WHERE I_SP_LAYOUT_ID = :parentLayoutId AND B_DELETED = 0")
    List<Layout> getChildLayouts(int parentLayoutId);

    @Query("SELECT * FROM AILAYOUT WHERE S_Q_TYPE = :qType AND B_DELETED = 0")
    List<Layout> getLayoutsByQType(String qType);

    @Insert
    long insertLayout(Layout layout);

    @Insert
    List<Long> insertLayouts(List<Layout> layouts);

    @Update
    void updateLayout(Layout layout);

    @Update
    void updateLayouts(List<Layout> layouts);

    @Delete
    void deleteLayout(Layout layout);

    @Query("UPDATE AILAYOUT SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AILAYOUT SET B_DELETED = 1 WHERE I_S_LAYOUT_ID = :serverLayoutId")
    void softDeleteByServerId(int serverLayoutId);

    @Query("SELECT COUNT(*) FROM AILAYOUT WHERE B_DELETED = 0")
    int getLayoutCount();

    @Query("SELECT COUNT(*) FROM AILAYOUT WHERE S_Q_TYPE = :qType AND B_DELETED = 0")
    int getLayoutCountByQType(String qType);

    // Batch operations
    @Query("DELETE FROM AILAYOUT")
    void deleteAllLayouts();

    @Query("UPDATE AILAYOUT SET B_DELETED = 1")
    void softDeleteAllLayouts();
}