package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Contact;

import java.util.List;

@Dao
public interface ContactDao {
    
    // Basic CRUD operations using only existing columns
    @Query("SELECT * FROM AICONTACT WHERE B_DELETED = 0")
    List<Contact> getAllContacts();

    @Query("SELECT * FROM AICONTACT WHERE ID = :id")
    Contact getContactById(long id);

    @Query("SELECT * FROM AICONTACT WHERE I_S_CONTACT_ID = :serverContactId AND B_DELETED = 0")
    Contact getContactByServerId(int serverContactId);

    // Use existing columns only
    @Query("SELECT * FROM AICONTACT WHERE I_ASSET_ID = :assetId AND B_DELETED = 0")
    List<Contact> getContactsByAssetId(int assetId);

    @Query("SELECT * FROM AICONTACT WHERE I_S_ASSET_ID = :serverAssetId AND B_DELETED = 0")
    List<Contact> getContactsByServerAssetId(int serverAssetId);

    @Query("SELECT * FROM AICONTACT WHERE S_EMAIL = :email AND B_DELETED = 0")
    Contact getContactByEmail(String email);

    @Query("SELECT * FROM AICONTACT WHERE S_PHONE = :phone AND B_DELETED = 0")
    Contact getContactByPhone(String phone);

    @Query("SELECT * FROM AICONTACT WHERE (S_FIRST_NAME LIKE :namePattern OR S_LAST_NAME LIKE :namePattern) AND B_DELETED = 0")
    List<Contact> searchContactsByName(String namePattern);

    @Query("SELECT * FROM AICONTACT WHERE S_TAG = :tag AND B_DELETED = 0")
    List<Contact> getContactsByTag(String tag);

    @Insert
    long insertContact(Contact contact);

    @Insert
    List<Long> insertContacts(List<Contact> contacts);

    @Update
    void updateContact(Contact contact);

    @Update
    void updateContacts(List<Contact> contacts);

    @Delete
    void deleteContact(Contact contact);

    @Query("UPDATE AICONTACT SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AICONTACT SET B_DELETED = 1 WHERE I_S_CONTACT_ID = :serverContactId")
    void softDeleteByServerId(int serverContactId);

    @Query("SELECT COUNT(*) FROM AICONTACT WHERE B_DELETED = 0")
    int getContactCount();

    @Query("SELECT COUNT(*) FROM AICONTACT WHERE I_ASSET_ID = :assetId AND B_DELETED = 0")
    int getContactCountByAsset(int assetId);

    // Batch operations
    @Query("DELETE FROM AICONTACT")
    void deleteAllContacts();

    @Query("UPDATE AICONTACT SET B_DELETED = 1")
    void softDeleteAllContacts();
}