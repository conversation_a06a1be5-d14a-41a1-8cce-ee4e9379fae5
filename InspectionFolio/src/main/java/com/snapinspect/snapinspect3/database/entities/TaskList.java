package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_TaskList;

/**
 * Room entity for ai_TaskList (AITASK_LIST table)
 */
@Entity(tableName = "AITASK_LIST")
public class TaskList {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_S_TASK_LIST_ID")
    public Integer iSTaskListID;
    
    @ColumnInfo(name = "I_PROJECT_ID")
    public Integer iProjectID;
    
    @ColumnInfo(name = "I_CREATED_USER_ID")
    public Integer iCreatedUserID;
    
    @ColumnInfo(name = "S_NAME")
    public String sName;
    
    @ColumnInfo(name = "I_TASK_ID")
    public int iTaskID;
    
    @ColumnInfo(name = "S_TYPE")
    public String sType;
    
    @ColumnInfo(name = "S_MESSAGE")
    public String sMessage;
    
    @ColumnInfo(name = "S_CUSTOM_ONE")
    public String sCustomOne;
    
    @ColumnInfo(name = "S_CUSTOM_TWO")
    public String sCustomTwo;
    
    @ColumnInfo(name = "B_UPLOADED")
    public boolean bUploaded;
    
    @ColumnInfo(name = "B_DELETED")
    public boolean bDeleted;
    
    @ColumnInfo(name = "DT_DATE_TIME")
    public String dtDateTime;
    
    public TaskList() {}
    
    public TaskList(ai_TaskList source) {
        this.iSTaskListID = source.iSTaskListID;
        this.iProjectID = source.iProjectID;
        this.iCreatedUserID = source.iCreatedUserID;
        this.sName = source.sName;
        this.iTaskID = source.iTaskID;
        this.sType = source.sType;
        this.sMessage = source.sMessage;
        this.sCustomOne = source.sCustomOne;
        this.sCustomTwo = source.sCustomTwo;
        this.bUploaded = source.bUploaded;
        this.bDeleted = source.bDeleted;
        this.dtDateTime = source.dtDateTime;
    }
    
    public TaskList(int iTaskID, String sType, String sMessage, String sCustomOne, String sCustomTwo,
                   boolean bUploaded, boolean bDeleted, String dtDateTime) {
        this.iSTaskListID = null;
        this.iProjectID = null;
        this.iCreatedUserID = null;
        this.sName = null;
        this.iTaskID = iTaskID;
        this.sType = sType;
        this.sMessage = sMessage;
        this.sCustomOne = sCustomOne;
        this.sCustomTwo = sCustomTwo;
        this.bUploaded = bUploaded;
        this.bDeleted = bDeleted;
        this.dtDateTime = dtDateTime;
    }
}