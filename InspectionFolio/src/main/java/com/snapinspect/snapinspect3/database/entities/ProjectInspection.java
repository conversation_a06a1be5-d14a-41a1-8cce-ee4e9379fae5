package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_ProjectInspection;

import java.util.Date;

/**
 * Room entity for ai_ProjectInspection
 */
@Entity(tableName = "AIPROJECT_INSPECTION")
public class ProjectInspection {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_S_PROJECT_ASSET_INS_TYPE_ID")
    public int iSProjectAssetInsTypeID;
    
    @ColumnInfo(name = "I_PROJECT_ID")
    public int iProjectID;
    
    @ColumnInfo(name = "I_COMPANY_ID")
    public int iCompanyID;
    
    @ColumnInfo(name = "I_ASSET_ID")
    public int iAssetID;
    
    @ColumnInfo(name = "I_INS_TYPE_ID")
    public int iInsTypeID;
    
    @ColumnInfo(name = "I_INSPECTION_ID")
    public int iInspectionID;
    
    @ColumnInfo(name = "I_S_INSPECTION_ID")
    public int iSInspectionID;
    
    @ColumnInfo(name = "I_INSPECTOR_ID")
    public int iInspectorID;
    
    @ColumnInfo(name = "DT_START")
    public Date dtStart;
    
    @ColumnInfo(name = "DT_END")
    public Date dtEnd;
    
    @ColumnInfo(name = "S_CUSTOM")
    public String sCustom;
    
    @ColumnInfo(name = "B_DELETED")
    public boolean bDeleted;
    
    @ColumnInfo(name = "DT_UPDATE")
    public Date dtUpdate;
    
    @ColumnInfo(name = "DT_DATE_TIME")
    public Date dtDateTime;
    
    public ProjectInspection() {}
    
    public ProjectInspection(ai_ProjectInspection source) {
        this.iSProjectAssetInsTypeID = source.iSProjectAssetInsTypeID;
        this.iProjectID = source.iProjectID;
        this.iCompanyID = source.iCompanyID;
        this.iAssetID = source.iAssetID;
        this.iInsTypeID = source.iInsTypeID;
        this.iInspectionID = source.iInspectionID;
        this.iSInspectionID = source.iSInspectionID;
        this.iInspectorID = source.iInspectorID;
        this.dtStart = source.dtStart;
        this.dtEnd = source.dtEnd;
        this.sCustom = source.sCustom;
        this.bDeleted = source.bDeleted;
        this.dtUpdate = source.dtUpdate;
        this.dtDateTime = source.dtDateTime;
    }
    
    public ProjectInspection(int iSProjectAssetInsTypeID, int iProjectID, int iCompanyID, int iAssetID,
                           int iInsTypeID, int iInspectionID, int iSInspectionID, int iInspectorID,
                           Date dtStart, Date dtEnd, String sCustom, boolean bDeleted, 
                           Date dtUpdate, Date dtDateTime) {
        this.iSProjectAssetInsTypeID = iSProjectAssetInsTypeID;
        this.iProjectID = iProjectID;
        this.iCompanyID = iCompanyID;
        this.iAssetID = iAssetID;
        this.iInsTypeID = iInsTypeID;
        this.iInspectionID = iInspectionID;
        this.iSInspectionID = iSInspectionID;
        this.iInspectorID = iInspectorID;
        this.dtStart = dtStart;
        this.dtEnd = dtEnd;
        this.sCustom = sCustom;
        this.bDeleted = bDeleted;
        this.dtUpdate = dtUpdate;
        this.dtDateTime = dtDateTime;
    }
    
    public ai_ProjectInspection toSugarEntity() {
        ai_ProjectInspection sugar = new ai_ProjectInspection();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSProjectAssetInsTypeID = this.iSProjectAssetInsTypeID;
        sugar.iProjectID = this.iProjectID;
        sugar.iCompanyID = this.iCompanyID;
        sugar.iAssetID = this.iAssetID;
        sugar.iInsTypeID = this.iInsTypeID;
        sugar.iInspectionID = this.iInspectionID;
        sugar.iSInspectionID = this.iSInspectionID;
        sugar.iInspectorID = this.iInspectorID;
        sugar.dtStart = this.dtStart;
        sugar.dtEnd = this.dtEnd;
        sugar.sCustom = this.sCustom;
        sugar.bDeleted = this.bDeleted;
        sugar.dtUpdate = this.dtUpdate;
        sugar.dtDateTime = this.dtDateTime;
        return sugar;
    }
}