package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_NoticeCategory;

/**
 * Room entity for ai_NoticeCategory (AINOTICE_CATEGORY table)
 */
@Entity(tableName = "AINOTICE_CATEGORY")
public class NoticeCategory {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "i_s_notice_category_id")
    public int iSNoticeCategoryID;
    
    @ColumnInfo(name = "s_name")
    public String sName;
    
    @ColumnInfo(name = "s_description")
    public String sDescription;
    
    @ColumnInfo(name = "b_deleted")
    public boolean bDeleted;
    
    @ColumnInfo(name = "dt_update")
    public String dtUpdate;
    
    @ColumnInfo(name = "dt_date_time")
    public String dtDateTime;
    
    @ColumnInfo(name = "i_p_note_category_id")
    public int iPNoteCategoryID;
    
    @ColumnInfo(name = "s_custom1")
    public String sCustom1;
    
    @ColumnInfo(name = "s_custom2")
    public String sCustom2;
    
    public NoticeCategory() {}
    
    public NoticeCategory(ai_NoticeCategory source) {
        this.iSNoticeCategoryID = source.iSNoticeCategoryID;
        this.sName = source.sName;
        this.sDescription = source.sDescription;
        this.bDeleted = source.bDeleted;
        this.dtUpdate = source.dtUpdate;
        this.dtDateTime = source.dtDateTime;
        this.iPNoteCategoryID = source.iPNoteCategoryID;
        this.sCustom1 = source.sCustom1;
        this.sCustom2 = source.sCustom2;
    }
}