package com.snapinspect.snapinspect3.database.views;

import androidx.room.DatabaseView;

@DatabaseView(
    viewName = "VINSPECTION",
    value = "SELECT c.ID as iInspectionID, c.I_S_INS_ID as iSInsID, c.I_S_ASSET_ID as iSAssetID, " +
            "c.S_TITLE as sTitle, c.S_INS_TITLE as sInsTitle, c.DT_START_DATE as dtStartDate, c.DT_END_DATE as dtEndDate, " +
            "c.B_COMPLETE as bComplete, c.B_SYNCED as bSynced, c.S_CUSTOM_ONE as sCustom1, c.S_CUSTOM_TWO as sCustom2, " +
            "c.I_S_SCHEDULE_ID as iSScheduleID, " +
            "d.sRef, d.sBuilding<PERSON>ddress, d.sU<PERSON>t<PERSON><PERSON><PERSON>, d.s<PERSON>oom<PERSON>dd<PERSON>, d.bApartment, " +
            "(IFNULL(c.S_TITLE, '') || ' ' || IFNULL(c.S_INS_TITLE, '') || ' ' || IFNULL(d.sSearchTerm, '')) as sSearchTerm " +
            "FROM AIINSPECTION c LEFT JOIN VASSET d on c.I_S_ASSET_ID = d.iAssetID WHERE c.B_DELETED = 0"
)
public class VInspectionView {
    public int iInspectionID;
    public int iSInsID;
    public int iSAssetID;
    public String sTitle;
    public String sInsTitle;
    public String dtStartDate;
    public String dtEndDate;
    public boolean bComplete;
    public boolean bSynced;
    public String sCustom1;
    public String sCustom2;
    public int iSScheduleID;
    public String sRef;
    public String sBuildingAddress;
    public String sUnitAddress;
    public String sRoomAddress;
    public boolean bApartment;
    public String sSearchTerm;
}