package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.PropertyLayout;

import java.util.List;

@Dao
public interface PropertyLayoutDao {
    
    @Query("SELECT * FROM AIPROPERTY_LAYOUT WHERE B_DELETED = 0")
    List<PropertyLayout> getAllPropertyLayouts();

    @Query("SELECT * FROM AIPROPERTY_LAYOUT WHERE ID = :id")
    PropertyLayout getPropertyLayoutById(long id);

    @Query("SELECT * FROM AIPROPERTY_LAYOUT WHERE I_S_PROPERTY_LAYOUT_ID = :serverPropertyLayoutId AND B_DELETED = 0")
    PropertyLayout getPropertyLayoutByServerId(int serverPropertyLayoutId);

    @Query("SELECT * FROM AIPROPERTY_LAYOUT WHERE I_PROPERTY_ID = :propertyId AND B_DELETED = 0")
    List<PropertyLayout> getPropertyLayoutsByPropertyId(int propertyId);

    @Query("SELECT * FROM AIPROPERTY_LAYOUT WHERE S_PTC LIKE :ptcPattern AND B_DELETED = 0")
    List<PropertyLayout> searchPropertyLayoutsByPTC(String ptcPattern);

    @Insert
    long insertPropertyLayout(PropertyLayout propertyLayout);

    @Insert
    List<Long> insertPropertyLayouts(List<PropertyLayout> propertyLayouts);

    @Update
    void updatePropertyLayout(PropertyLayout propertyLayout);

    @Update
    void updatePropertyLayouts(List<PropertyLayout> propertyLayouts);

    @Delete
    void deletePropertyLayout(PropertyLayout propertyLayout);

    @Query("UPDATE AIPROPERTY_LAYOUT SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIPROPERTY_LAYOUT SET B_DELETED = 1 WHERE I_S_PROPERTY_LAYOUT_ID = :serverPropertyLayoutId")
    void softDeleteByServerId(int serverPropertyLayoutId);

    @Query("UPDATE AIPROPERTY_LAYOUT SET B_DELETED = 1 WHERE I_PROPERTY_ID = :propertyId")
    void softDeleteByPropertyId(int propertyId);

    @Query("SELECT COUNT(*) FROM AIPROPERTY_LAYOUT WHERE B_DELETED = 0")
    int getPropertyLayoutCount();

    @Query("SELECT COUNT(*) FROM AIPROPERTY_LAYOUT WHERE I_PROPERTY_ID = :propertyId AND B_DELETED = 0")
    int getPropertyLayoutCountByProperty(int propertyId);

    // Batch operations
    @Query("DELETE FROM AIPROPERTY_LAYOUT")
    void deleteAllPropertyLayouts();

    @Query("UPDATE AIPROPERTY_LAYOUT SET B_DELETED = 1")
    void softDeleteAllPropertyLayouts();
}