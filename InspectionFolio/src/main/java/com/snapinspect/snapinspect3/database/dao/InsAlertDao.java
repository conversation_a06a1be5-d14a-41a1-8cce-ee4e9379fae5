package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.InsAlert;

import java.util.List;

@Dao
public interface InsAlertDao {
    
    @Query("SELECT * FROM AIINS_ALERT")
    List<InsAlert> getAllInsAlerts();

    @Query("SELECT * FROM AIINS_ALERT WHERE ID = :id")
    InsAlert getInsAlertById(long id);

    @Query("SELECT * FROM AIINS_ALERT WHERE I_S_ASSET_ID = :assetId")
    List<InsAlert> getInsAlertsByAssetId(int assetId);

    @Query("SELECT * FROM AIINS_ALERT WHERE I_S_LAYOUT_ID = :layoutId")
    List<InsAlert> getInsAlertsByLayoutId(int layoutId);

    @Query("SELECT * FROM AIINS_ALERT WHERE S_PTC = :ptc")
    List<InsAlert> getInsAlertsByPTC(String ptc);

    @Insert
    long insertInsAlert(InsAlert insAlert);

    @Insert
    List<Long> insertInsAlerts(List<InsAlert> insAlerts);

    @Update
    void updateInsAlert(InsAlert insAlert);

    @Update
    void updateInsAlerts(List<InsAlert> insAlerts);

    @Delete
    void deleteInsAlert(InsAlert insAlert);

    @Query("DELETE FROM AIINS_ALERT WHERE ID = :id")
    void deleteById(long id);

    @Query("SELECT COUNT(*) FROM AIINS_ALERT")
    int getInsAlertCount();

    @Query("DELETE FROM AIINS_ALERT")
    void deleteAllInsAlerts();
}