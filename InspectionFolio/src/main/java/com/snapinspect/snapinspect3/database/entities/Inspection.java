package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;

@Entity(
    tableName = "AIINSPECTION",
    indices = {
        @Index(value = "I_S_ASSET_ID"),
        @Index(value = "I_S_INS_ID"),
        @Index(value = "I_S_INS_TYPE_ID"),
        @Index(value = "B_DELETED"),
        @Index(value = "B_COMPLETE"),
        @Index(value = "B_SYNCED")
    },
    foreignKeys = {
        @ForeignKey(
            entity = Assets.class,
            parentColumns = "I_S_ASSET_ID",
            childColumns = "I_S_ASSET_ID"
        )
    }
)
public class Inspection {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_S_INS_ID")
    public Integer iSInsID;

    @ColumnInfo(name = "I_S_ASSET_ID")
    public Integer iSAssetID;

    @ColumnInfo(name = "I_S_INS_TYPE_ID")
    public Integer iSInsTypeID;

    @ColumnInfo(name = "I_S_SCHEDULE_ID")
    public Integer iSScheduleID;

    @ColumnInfo(name = "S_TITLE")
    public String sTitle;

    @ColumnInfo(name = "S_INS_TITLE")
    public String sInsTitle;

    @ColumnInfo(name = "S_COMMENTS")
    public String sComments;

    @ColumnInfo(name = "S_PTC")
    public String sPTC;

    @ColumnInfo(name = "S_TYPE")
    public String sType;

    @ColumnInfo(name = "DT_START_DATE")
    public String dtStartDate;

    @ColumnInfo(name = "DT_END_DATE")
    public String dtEndDate;

    @ColumnInfo(name = "S_LAT")
    public String sLat;

    @ColumnInfo(name = "S_LONG")
    public String sLong;

    @ColumnInfo(name = "S_ADDRESS_ONE")
    public String sAddressOne;

    @ColumnInfo(name = "S_ADDRESS_TWO")
    public String sAddressTwo;

    @ColumnInfo(name = "S_CUSTOM_ONE")
    public String sCustomOne;

    @ColumnInfo(name = "S_CUSTOM_TWO")
    public String sCustomTwo;

    @ColumnInfo(name = "B_COMPLETE")
    public Boolean bComplete = false;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    @ColumnInfo(name = "B_LOCK")
    public Boolean bLock = false;

    @ColumnInfo(name = "B_SYNCED")
    public Boolean bSynced = false;

    // Constructors
    public Inspection() {}

    // Copy constructor from Sugar ORM entity
    public Inspection(ai_Inspection sugarEntity) {
        this.id = sugarEntity.getId();
        this.iSInsID = sugarEntity.iSInsID;
        this.iSAssetID = sugarEntity.iSAssetID;
        this.iSInsTypeID = sugarEntity.iSInsTypeID;
        this.iSScheduleID = sugarEntity.iSScheduleID;
        this.sTitle = sugarEntity.sTitle;
        this.sInsTitle = sugarEntity.sInsTitle;
        this.sComments = sugarEntity.sComments;
        this.sPTC = sugarEntity.sPTC;
        this.sType = sugarEntity.sType;
        this.dtStartDate = sugarEntity.dtStartDate;
        this.dtEndDate = sugarEntity.dtEndDate;
        this.sLat = sugarEntity.sLat;
        this.sLong = sugarEntity.sLong;
        this.sAddressOne = sugarEntity.sAddressOne;
        this.sAddressTwo = sugarEntity.sAddressTwo;
        this.sCustomOne = sugarEntity.sCustomOne;
        this.sCustomTwo = sugarEntity.sCustomTwo;
        this.bComplete = sugarEntity.bComplete;
        this.bDeleted = sugarEntity.bDeleted;
        this.bLock = sugarEntity.bLock;
        this.bSynced = sugarEntity.bSynced;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Inspection toSugarEntity() {
        ai_Inspection sugar = new ai_Inspection();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSInsID = this.iSInsID;
        sugar.iSAssetID = this.iSAssetID;
        sugar.iSInsTypeID = this.iSInsTypeID;
        sugar.iSScheduleID = this.iSScheduleID;
        sugar.sTitle = this.sTitle;
        sugar.sInsTitle = this.sInsTitle;
        sugar.sComments = this.sComments;
        sugar.sPTC = this.sPTC;
        sugar.sType = this.sType;
        sugar.dtStartDate = this.dtStartDate;
        sugar.dtEndDate = this.dtEndDate;
        sugar.sLat = this.sLat;
        sugar.sLong = this.sLong;
        sugar.sAddressOne = this.sAddressOne;
        sugar.sAddressTwo = this.sAddressTwo;
        sugar.sCustomOne = this.sCustomOne;
        sugar.sCustomTwo = this.sCustomTwo;
        sugar.bComplete = this.bComplete;
        sugar.bDeleted = this.bDeleted;
        sugar.bLock = this.bLock;
        sugar.bSynced = this.bSynced;
        return sugar;
    }
}