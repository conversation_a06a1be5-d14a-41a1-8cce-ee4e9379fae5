package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Log;

import java.util.List;

@Dao
public interface LogDao {
    
    @Query("SELECT * FROM AILOG WHERE B_DELETED = 0")
    List<Log> getAllLogs();

    @Query("SELECT * FROM AILOG WHERE ID = :id")
    Log getLogById(long id);

    @Query("SELECT * FROM AILOG WHERE S_TYPE = :type AND B_DELETED = 0 ORDER BY DT_DATE_TIME DESC")
    List<Log> getLogsByType(String type);

    @Query("SELECT * FROM AILOG WHERE DT_DATE_TIME BETWEEN :startDate AND :endDate AND B_DELETED = 0 ORDER BY DT_DATE_TIME DESC")
    List<Log> getLogsBetweenDates(String startDate, String endDate);

    @Query("SELECT * FROM AILOG WHERE S_MESSAGE LIKE :messagePattern AND B_DELETED = 0 ORDER BY DT_DATE_TIME DESC")
    List<Log> searchLogsByMessage(String messagePattern);

    @Insert
    long insertLog(Log log);

    @Insert
    List<Long> insertLogs(List<Log> logs);

    @Update
    void updateLog(Log log);

    @Update
    void updateLogs(List<Log> logs);

    @Delete
    void deleteLog(Log log);

    @Query("UPDATE AILOG SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("SELECT COUNT(*) FROM AILOG WHERE B_DELETED = 0")
    int getLogCount();

    @Query("SELECT COUNT(*) FROM AILOG WHERE S_TYPE = :type AND B_DELETED = 0")
    int getLogCountByType(String type);

    @Query("DELETE FROM AILOG WHERE DT_DATE_TIME < :cutoffDate")
    void deleteLogsOlderThan(String cutoffDate);

    @Query("DELETE FROM AILOG")
    void deleteAllLogs();

    @Query("UPDATE AILOG SET B_DELETED = 1")
    void softDeleteAllLogs();
}