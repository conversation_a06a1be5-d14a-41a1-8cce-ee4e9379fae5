package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.FloorPlan;

import java.util.List;

@Dao
public interface FloorPlanDao {
    
    @Query("SELECT * FROM AIFLOORPLAN WHERE B_DELETED = 0")
    List<FloorPlan> getAllFloorPlans();

    @Query("SELECT * FROM AIFLOORPLAN WHERE ID = :id")
    FloorPlan getFloorPlanById(long id);

    @Query("SELECT * FROM AIFLOORPLAN WHERE I_S_FLOOR_PLAN_ID = :serverFloorPlanId AND B_DELETED = 0")
    FloorPlan getFloorPlanByServerId(int serverFloorPlanId);

    @Query("SELECT * FROM AIFLOORPLAN WHERE I_S_ASSET_ID = :assetId AND B_DELETED = 0")
    List<FloorPlan> getFloorPlansByAssetId(int assetId);

    @Query("SELECT * FROM AIFLOORPLAN WHERE S_TITLE LIKE :namePattern AND B_DELETED = 0")
    List<FloorPlan> searchFloorPlansByTitle(String namePattern);


    @Insert
    long insertFloorPlan(FloorPlan floorPlan);

    @Insert
    List<Long> insertFloorPlans(List<FloorPlan> floorPlans);

    @Update
    void updateFloorPlan(FloorPlan floorPlan);

    @Update
    void updateFloorPlans(List<FloorPlan> floorPlans);

    @Delete
    void deleteFloorPlan(FloorPlan floorPlan);

    @Query("UPDATE AIFLOORPLAN SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIFLOORPLAN SET B_DELETED = 1 WHERE I_S_FLOOR_PLAN_ID = :serverFloorPlanId")
    void softDeleteByServerId(int serverFloorPlanId);

    @Query("UPDATE AIFLOORPLAN SET B_DELETED = 1 WHERE I_S_ASSET_ID = :assetId")
    void softDeleteByAssetId(int assetId);

    @Query("SELECT COUNT(*) FROM AIFLOORPLAN WHERE B_DELETED = 0")
    int getFloorPlanCount();

    @Query("SELECT COUNT(*) FROM AIFLOORPLAN WHERE I_S_ASSET_ID = :assetId AND B_DELETED = 0")
    int getFloorPlanCountByAsset(int assetId);

    // Batch operations
    @Query("DELETE FROM AIFLOORPLAN")
    void deleteAllFloorPlans();

    @Query("UPDATE AIFLOORPLAN SET B_DELETED = 1")
    void softDeleteAllFloorPlans();
}