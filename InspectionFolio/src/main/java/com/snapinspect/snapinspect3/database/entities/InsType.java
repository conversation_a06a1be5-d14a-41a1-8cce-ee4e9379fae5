package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_InsType;

/**
 * Room entity for ai_InsType (AIINS_TYPE table)
 */
@Entity(tableName = "AIINS_TYPE")
public class InsType {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_S_INS_TYPE_ID")
    public Integer iSInsTypeID;
    
    @ColumnInfo(name = "S_INS_TITLE")
    public String sInsTitle;
    
    @ColumnInfo(name = "S_TYPE")
    public String sType;
    
    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    @ColumnInfo(name = "B_REM_LAYOUT")
    public Boolean bRemLayout = false;
    
    @ColumnInfo(name = "S_PTC")
    public String sPTC;
    
    @ColumnInfo(name = "S_FIELD_ONE")
    public String sFieldOne;
    
    @ColumnInfo(name = "S_FIELD_TWO")
    public String sFieldTwo;
    
    @ColumnInfo(name = "S_FIELD_THREE")
    public String sFieldThree;
    
    public InsType() {}
    
    public InsType(ai_InsType source) {
        if (source.getId() != null) {
            this.id = source.getId();
        }
        this.iSInsTypeID = source.iSInsTypeID;
        this.sInsTitle = source.sInsTitle;
        this.sType = source.sType;
        this.sPTC = source.sPTC;
        this.bDeleted = source.bDeleted;
        this.bRemLayout = source.bRemLayout;
        this.sFieldOne = source.sFieldOne;
        this.sFieldTwo = source.sFieldTwo;
        this.sFieldThree = source.sFieldThree;
    }
}