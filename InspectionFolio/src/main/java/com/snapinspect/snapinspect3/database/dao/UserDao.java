package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.User;

import java.util.List;

@Dao
public interface UserDao {
    
    @Query("SELECT * FROM AIUSER WHERE B_DELETED = 0")
    List<User> getAllUsers();

    @Query("SELECT * FROM AIUSER WHERE ID = :id")
    User getUserById(long id);

    @Query("SELECT * FROM AIUSER WHERE I_CUSTOMER_ID = :customerId AND B_DELETED = 0")
    User getUserByCustomerId(int customerId);

    @Query("SELECT * FROM AIUSER WHERE S_EMAIL = :email AND B_DELETED = 0")
    User getUserByEmail(String email);

    @Insert
    long insertUser(User user);

    @Insert
    List<Long> insertUsers(List<User> users);

    @Update
    void updateUser(User user);

    @Update
    void updateUsers(List<User> users);

    @Delete
    void deleteUser(User user);

    @Query("UPDATE AIUSER SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIUSER SET B_DELETED = 1 WHERE I_CUSTOMER_ID = :customerId")
    void softDeleteByCustomerId(int customerId);

    @Query("SELECT COUNT(*) FROM AIUSER WHERE B_DELETED = 0")
    int getUserCount();

    // Batch operations
    @Query("DELETE FROM AIUSER")
    void deleteAllUsers();

    @Query("UPDATE AIUSER SET B_DELETED = 1")
    void softDeleteAllUsers();
}