package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Query;
import com.snapinspect.snapinspect3.database.views.VRequestInspectionView;

import java.util.List;

@Dao
public interface VRequestInspectionDao {
    
    @Query("SELECT * FROM VREQUEST_INSPECTION WHERE sInsSearchTerm LIKE :searchTerm ORDER BY iUnixTime")
    List<VRequestInspectionView> getRequestInspectionsWithText(String searchTerm);
    
    @Query("SELECT * FROM VREQUEST_INSPECTION ORDER BY iUnixTime")
    List<VRequestInspectionView> getAllRequestInspections();
}