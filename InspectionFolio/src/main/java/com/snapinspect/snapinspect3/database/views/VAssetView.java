package com.snapinspect.snapinspect3.database.views;

import androidx.room.DatabaseView;

@DatabaseView(
    viewName = "VASSET",
    value = "SELECT c.ID as iID, c.I_S_ASSET_ID as iAssetID, d.I_S_ASSET_ID as iPAssetID, " +
            "e.I_S_ASSET_ID as iPPAssetID, c.S_FIELD_THREE as sRef, " +
            "((CASE WHEN d.I_SP_ASSET_ID > 0 THEN ' ' || e.S_ADDRESS_ONE || ' ' || e.S_ADDRESS_TWO || ' | ' ELSE '' END) || " +
            "(CASE WHEN c.I_SP_ASSET_ID > 0 THEN ' ' || d.S_ADDRESS_ONE || ' ' || d.S_ADDRESS_TWO || ' | ' ELSE '' END ) || " +
            "' ' || c.S_ADDRESS_ONE || ' ' || c.S_ADDRESS_TWO) as sSearchTerm, " +
            "(CASE WHEN d.I_SP_Asset_ID > 0 THEN e.S_ADDRESS_ONE || ', ' || e.S_ADDRESS_TWO WHEN C.I_SP_ASSET_ID > 0 THEN d.S_ADDRESS_ONE || ', ' || d.S_ADDRESS_TWO " +
            "ELSE c.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO END) as sBuildingAddress, " +
            "(CASE WHEN d.I_SP_ASSET_ID > 0 then d.S_ADDRESS_ONE || ', ' || d.S_ADDRESS_TWO WHEN C.I_SP_ASSET_ID > 0 THEN c.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO " +
            "ELSE '' END) as sUnitAddress, " +
            "(CASE WHEN C.I_SP_ASSET_ID > 0 and d.I_SP_ASSET_ID > 0 THEN C.S_ADDRESS_ONE || ', ' || c.S_ADDRESS_TWO ELSE '' END) as sRoomAddress, " +
            "c.I_CUSTOMER_ID as iCustomerID, c.I_GROUP_ID as iGroupID, c.B_PUSH as bApartment, c.S_FIELD_ONE as sCustom1, c.S_FIELD_TWO as sCustom2 " +
            "FROM AIASSETS c LEFT JOIN AIASSETS d on c.I_SP_ASSET_ID = d.I_S_ASSET_ID LEFT JOIN " +
            "AIASSETS e on d.I_SP_ASSET_ID = e.I_S_ASSET_ID WHERE c.B_DELETED = 0"
)
public class VAssetView {
    public int iID;
    public int iAssetID;
    public int iPAssetID;
    public int iPPAssetID;
    public String sRef;
    public String sSearchTerm;
    public String sBuildingAddress;
    public String sUnitAddress;
    public String sRoomAddress;
    public int iCustomerID;
    public int iGroupID;
    public boolean bApartment;
    public String sCustom1;
    public String sCustom2;
}