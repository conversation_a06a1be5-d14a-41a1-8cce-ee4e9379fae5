package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;
import com.snapinspect.snapinspect3.IF_Object.ai_Comment;
import java.util.Date;

@Entity(
    tableName = "AICOMMENT",
    indices = {
        @Index(value = "I_COMMENT_ID"),
        @Index(value = "I_COMPANY_ID"),
        @Index(value = "I_CUSTOMER_ID"),
        @Index(value = "I_INSPECTION_ID"),
        @Index(value = "B_DELETED")
    }
)
@TypeConverters({com.snapinspect.snapinspect3.database.converters.TypeConverters.DateConverter.class})
public class Comment {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_COMMENT_ID")
    public Integer iCommentID;

    @ColumnInfo(name = "I_COMPANY_ID")
    public Integer iCompanyID;

    @ColumnInfo(name = "I_CUSTOMER_ID")
    public Integer iCustomerID;

    @ColumnInfo(name = "I_INSPECTION_ID")
    public Long iInspectionID;

    @ColumnInfo(name = "S_DATE")
    public String sDate;

    @ColumnInfo(name = "S_TIME")
    public String sTime;

    @ColumnInfo(name = "DATE")
    public Date date;

    @ColumnInfo(name = "S_NAME")
    public String sName;

    @ColumnInfo(name = "S_DESCRIPTION")
    public String sDescription;

    @ColumnInfo(name = "S_TYPE")
    public String sType;

    @ColumnInfo(name = "S_CUSTOM1")
    public String sCustom1;

    @ColumnInfo(name = "I_TASK_ID")
    public String iTaskID;

    @ColumnInfo(name = "S_PHOTO_URL")
    public String sPhotoUrl;

    @ColumnInfo(name = "COLOR_RES_ID")
    public Integer colorResId;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    // Constructors
    public Comment() {}

    // Copy constructor from Sugar ORM entity
    public Comment(ai_Comment sugarEntity) {
        this.id = sugarEntity.getId();
        this.iCommentID = sugarEntity.iCommentID;
        this.iCompanyID = sugarEntity.iCompanyID;
        this.iCustomerID = sugarEntity.iCustomerID;
        this.iInspectionID = sugarEntity.iInspectionID;
        this.sDate = sugarEntity.sDate;
        this.sTime = sugarEntity.sTime;
        this.date = sugarEntity.date;
        this.sName = sugarEntity.sName;
        this.sDescription = sugarEntity.sDescription;
        this.sType = sugarEntity.sType;
        this.sCustom1 = sugarEntity.sCustom1;
        this.iTaskID = sugarEntity.iTaskID;
        this.sPhotoUrl = sugarEntity.sPhotoUrl;
        this.colorResId = sugarEntity.colorResId;
        this.bDeleted = sugarEntity.bDeleted;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Comment toSugarEntity() {
        ai_Comment sugar = new ai_Comment();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iCommentID = this.iCommentID != null ? this.iCommentID : 0;
        sugar.iCompanyID = this.iCompanyID != null ? this.iCompanyID : 0;
        sugar.iCustomerID = this.iCustomerID != null ? this.iCustomerID : 0;
        sugar.iInspectionID = this.iInspectionID != null ? this.iInspectionID : 0;
        sugar.sDate = this.sDate;
        sugar.sTime = this.sTime;
        sugar.date = this.date;
        sugar.sName = this.sName;
        sugar.sDescription = this.sDescription;
        sugar.sType = this.sType;
        sugar.sCustom1 = this.sCustom1;
        sugar.iTaskID = this.iTaskID;
        sugar.sPhotoUrl = this.sPhotoUrl;
        sugar.colorResId = this.colorResId != null ? this.colorResId : 0;
        sugar.bDeleted = this.bDeleted != null ? this.bDeleted : false;
        return sugar;
    }
}