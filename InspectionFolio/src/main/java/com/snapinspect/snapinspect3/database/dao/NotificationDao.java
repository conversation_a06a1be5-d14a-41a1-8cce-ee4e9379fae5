package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Notification;

import java.util.List;

@Dao
public interface NotificationDao {
    
    // Basic CRUD operations using only existing columns
    @Query("SELECT * FROM AINOTIFICATION WHERE B_DELETED = 0")
    List<Notification> getAllNotifications();

    @Query("SELECT * FROM AINOTIFICATION WHERE ID = :id")
    Notification getNotificationById(long id);

    @Query("SELECT * FROM AINOTIFICATION WHERE I_S_NOTIFICATION_ID = :serverNotificationId AND B_DELETED = 0")
    Notification getNotificationByServerId(int serverNotificationId);

    @Query("SELECT * FROM AINOTIFICATION WHERE I_INS_ID = :inspectionId AND B_DELETED = 0 ORDER BY DT_DATE_TIME DESC")
    List<Notification> getNotificationsByInspectionId(long inspectionId);

    @Query("SELECT * FROM AINOTIFICATION WHERE I_INS_ITEM_ID = :insItemId AND B_DELETED = 0")
    List<Notification> getNotificationsByInsItemId(long insItemId);

    @Query("SELECT * FROM AINOTIFICATION WHERE S_CATEGORY = :category AND B_DELETED = 0")
    List<Notification> getNotificationsByCategory(String category);

    @Query("SELECT * FROM AINOTIFICATION WHERE I_PRIORITY = :priority AND B_DELETED = 0")
    List<Notification> getNotificationsByPriority(int priority);

    @Query("SELECT * FROM AINOTIFICATION WHERE S_TITLE LIKE :titlePattern AND B_DELETED = 0")
    List<Notification> searchNotificationsByTitle(String titlePattern);

    @Insert
    long insertNotification(Notification notification);

    @Insert
    List<Long> insertNotifications(List<Notification> notifications);

    @Update
    void updateNotification(Notification notification);

    @Update
    void updateNotifications(List<Notification> notifications);

    @Delete
    void deleteNotification(Notification notification);

    @Query("UPDATE AINOTIFICATION SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AINOTIFICATION SET B_DELETED = 1 WHERE I_S_NOTIFICATION_ID = :serverNotificationId")
    void softDeleteByServerId(int serverNotificationId);

    @Query("UPDATE AINOTIFICATION SET I_PRIORITY = :priority WHERE I_S_NOTIFICATION_ID = :serverNotificationId")
    void updateNotificationPriority(int serverNotificationId, int priority);

    @Query("SELECT COUNT(*) FROM AINOTIFICATION WHERE B_DELETED = 0")
    int getNotificationCount();

    @Query("SELECT COUNT(*) FROM AINOTIFICATION WHERE I_INS_ID = :inspectionId AND B_DELETED = 0")
    int getNotificationCountByInspection(long inspectionId);

    @Query("SELECT COUNT(*) FROM AINOTIFICATION WHERE S_CATEGORY = :category AND B_DELETED = 0")
    int getNotificationCountByCategory(String category);

    // Batch operations
    @Query("DELETE FROM AINOTIFICATION")
    void deleteAllNotifications();

    @Query("UPDATE AINOTIFICATION SET B_DELETED = 1")
    void softDeleteAllNotifications();
}