package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import com.snapinspect.snapinspect3.IF_Object.ai_Contact;

@Entity(
    tableName = "AICONTACT",
    indices = {
        @Index(value = "I_S_CONTACT_ID"),
        @Index(value = "I_ASSET_ID"),
        @Index(value = "I_S_ASSET_ID"),
        @Index(value = "B_DELETED")
    }
)
public class Contact {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_S_CONTACT_ID")
    public Integer iSContactID;

    @ColumnInfo(name = "I_ASSET_ID")
    public Integer iAssetID;

    @ColumnInfo(name = "I_S_ASSET_ID")
    public Integer iSAssetID;

    @ColumnInfo(name = "S_FIRST_NAME")
    public String sFirstName;

    @ColumnInfo(name = "S_LAST_NAME")
    public String sLastName;

    @ColumnInfo(name = "S_PHONE")
    public String sPhone;

    @ColumnInfo(name = "S_MOBILE")
    public String sMobile;

    @ColumnInfo(name = "S_EMAIL")
    public String sEmail;

    @ColumnInfo(name = "S_TAG")
    public String sTag;

    @ColumnInfo(name = "S_FIELD_ONE")
    public String sFieldOne;

    @ColumnInfo(name = "S_FIELD_TWO")
    public String sFieldTwo;

    @ColumnInfo(name = "S_FIELD_THREE")
    public String sFieldThree;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    // Constructors
    public Contact() {}

    // Copy constructor from Sugar ORM entity
    public Contact(ai_Contact sugarEntity) {
        this.id = sugarEntity.getId();
        this.iSContactID = sugarEntity.iSContactID;
        this.iAssetID = sugarEntity.iAssetID;
        this.iSAssetID = sugarEntity.iSAssetID;
        this.sFirstName = sugarEntity.sFirstName;
        this.sLastName = sugarEntity.sLastName;
        this.sPhone = sugarEntity.sPhone;
        this.sMobile = sugarEntity.sMobile;
        this.sEmail = sugarEntity.sEmail;
        this.sTag = sugarEntity.sTag;
        this.sFieldOne = sugarEntity.sFieldOne;
        this.sFieldTwo = sugarEntity.sFieldTwo;
        this.sFieldThree = sugarEntity.sFieldThree;
        this.bDeleted = sugarEntity.bDeleted;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Contact toSugarEntity() {
        ai_Contact sugar = new ai_Contact();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSContactID = this.iSContactID != null ? this.iSContactID : 0;
        sugar.iAssetID = this.iAssetID != null ? this.iAssetID : 0;
        sugar.iSAssetID = this.iSAssetID != null ? this.iSAssetID : 0;
        sugar.sFirstName = this.sFirstName;
        sugar.sLastName = this.sLastName;
        sugar.sPhone = this.sPhone;
        sugar.sMobile = this.sMobile;
        sugar.sEmail = this.sEmail;
        sugar.sTag = this.sTag;
        sugar.sFieldOne = this.sFieldOne;
        sugar.sFieldTwo = this.sFieldTwo;
        sugar.sFieldThree = this.sFieldThree;
        sugar.bDeleted = this.bDeleted != null ? this.bDeleted : false;
        return sugar;
    }
}