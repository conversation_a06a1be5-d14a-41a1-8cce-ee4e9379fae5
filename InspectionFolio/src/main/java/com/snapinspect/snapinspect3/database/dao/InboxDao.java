package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Inbox;

import java.util.List;

@Dao
public interface InboxDao {
    
    @Query("SELECT * FROM AIINBOX WHERE B_DELETED = 0")
    List<Inbox> getAllInboxes();

    @Query("SELECT * FROM AIINBOX WHERE ID = :id")
    Inbox getInboxById(long id);

    @Query("SELECT * FROM AIINBOX WHERE I_S_INBOX_ID = :serverInboxId AND B_DELETED = 0")
    Inbox getInboxByServerId(int serverInboxId);

    @Query("SELECT * FROM AIINBOX WHERE I_USER_ID = :userId AND B_DELETED = 0 ORDER BY DATE_TIME DESC")
    List<Inbox> getInboxesByUserId(int userId);

    @Query("SELECT * FROM AIINBOX WHERE I_TYPE = :type AND B_DELETED = 0")
    List<Inbox> getInboxesByType(int type);

    @Query("SELECT * FROM AIINBOX WHERE B_READ = :isRead AND B_DELETED = 0")
    List<Inbox> getInboxesByReadStatus(boolean isRead);

    @Query("SELECT * FROM AIINBOX WHERE I_USER_ID = :userId AND B_READ = 0 AND B_DELETED = 0 ORDER BY DATE_TIME DESC")
    List<Inbox> getUnreadInboxesByUserId(int userId);

    @Insert
    long insertInbox(Inbox inbox);

    @Insert
    List<Long> insertInboxes(List<Inbox> inboxes);

    @Update
    void updateInbox(Inbox inbox);

    @Update
    void updateInboxes(List<Inbox> inboxes);

    @Delete
    void deleteInbox(Inbox inbox);

    @Query("UPDATE AIINBOX SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIINBOX SET B_DELETED = 1 WHERE I_S_INBOX_ID = :serverInboxId")
    void softDeleteByServerId(int serverInboxId);

    @Query("UPDATE AIINBOX SET B_READ = 1 WHERE ID = :id")
    void markAsRead(long id);

    @Query("UPDATE AIINBOX SET B_READ = 1 WHERE I_USER_ID = :userId")
    void markAllAsReadForUser(int userId);

    @Query("SELECT COUNT(*) FROM AIINBOX WHERE B_DELETED = 0")
    int getInboxCount();

    @Query("SELECT COUNT(*) FROM AIINBOX WHERE I_USER_ID = :userId AND B_READ = 0 AND B_DELETED = 0")
    int getUnreadInboxCount(int userId);

    // Batch operations
    @Query("DELETE FROM AIINBOX")
    void deleteAllInboxes();

    @Query("UPDATE AIINBOX SET B_DELETED = 1")
    void softDeleteAllInboxes();
}