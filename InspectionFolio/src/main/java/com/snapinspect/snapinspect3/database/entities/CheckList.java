package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_CheckList;

/**
 * Room entity for ai_CheckList (AICHECK_LIST table)
 */
@Entity(tableName = "AICHECK_LIST")
public class CheckList {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_S_CHECK_LIST_ID")
    public Integer iSCheckListID;
    
    @ColumnInfo(name = "S_TITLE")
    public String sTitle;
    
    @ColumnInfo(name = "S_PTC")
    public String sPTC;
    
    @ColumnInfo(name = "I_LAYOUT_VER_ID")
    public Integer iLayoutVerID;
    
    public CheckList() {}
    
    public CheckList(ai_CheckList source) {
        this.iSCheckListID = source.iSCheckListID;
        this.sTitle = source.sTitle;
        this.sPTC = source.sPTC;
        this.iLayoutVerID = source.iLayoutVerID;
    }
}