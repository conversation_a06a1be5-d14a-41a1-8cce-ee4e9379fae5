package com.snapinspect.snapinspect3.database;

import android.content.Context;
import android.util.Log;
import com.snapinspect.snapinspect3.database.entities.Assets;
import com.snapinspect.snapinspect3.database.entities.Inspection;
import com.snapinspect.snapinspect3.database.entities.Photo;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;

import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * Manager class for Room database operations
 * Provides a bridge between Sugar ORM and Room during migration
 */
public class RoomDatabaseManager {
    private static final String TAG = "RoomDatabaseManager";
    private static RoomDatabaseManager instance;
    private final SnapInspectDatabase database;
    private final Executor executor;
    
    private RoomDatabaseManager(Context context) {
        database = SnapInspectDatabase.getInstance(context);
        executor = Executors.newFixedThreadPool(4);
    }
    
    public static synchronized RoomDatabaseManager getInstance(Context context) {
        if (instance == null) {
            instance = new RoomDatabaseManager(context.getApplicationContext());
        }
        return instance;
    }
    
    public SnapInspectDatabase getDatabase() {
        return database;
    }
    
    /**
     * Test basic Room functionality with existing database
     */
    public void testRoomFunctionality() {
        executor.execute(() -> {
            try {
                Log.i(TAG, "Testing Room database functionality...");
                
                // Test Assets
                List<Assets> assets = database.assetsDao().getAllAssets();
                Log.i(TAG, "Found " + assets.size() + " assets in Room");
                
                // Test Inspections  
                List<Inspection> inspections = database.inspectionDao().getAllInspections();
                Log.i(TAG, "Found " + inspections.size() + " inspections in Room");
                
                // Test Photos
                List<Photo> photos = database.photoDao().getAllPhotos();
                Log.i(TAG, "Found " + photos.size() + " photos in Room");
                
                // Test specific queries
                if (!assets.isEmpty()) {
                    Assets firstAsset = assets.get(0);
                    List<Inspection> assetInspections = database.inspectionDao()
                        .getInspectionsByAssetId(firstAsset.iSAssetID);
                    Log.i(TAG, "Asset " + firstAsset.iSAssetID + " has " + 
                          assetInspections.size() + " inspections");
                }
                
                Log.i(TAG, "Room database test completed successfully!");
                
            } catch (Exception e) {
                Log.e(TAG, "Error testing Room functionality", e);
                ai_BugHandler.ai_Handler_Exception("RoomDatabaseManager", "testRoomFunctionality", e, null);
            }
        });
    }
    
    /**
     * Get asset count comparison between Sugar ORM and Room
     */
    public void compareAssetCounts() {
        executor.execute(() -> {
            try {
                // Room count
                int roomCount = database.assetsDao().getAssetCount();
                Log.i(TAG, "Room Assets count: " + roomCount);
                
                // This will help verify data consistency during migration
                
            } catch (Exception e) {
                Log.e(TAG, "Error comparing asset counts", e);
            }
        });
    }
    
    /**
     * Async callback interface
     */
    public interface DatabaseCallback<T> {
        void onSuccess(T result);
        void onError(Exception error);
    }
    
    /**
     * Get assets asynchronously
     */
    public void getAssetsAsync(DatabaseCallback<List<Assets>> callback) {
        executor.execute(() -> {
            try {
                List<Assets> assets = database.assetsDao().getAllAssets();
                callback.onSuccess(assets);
            } catch (Exception e) {
                callback.onError(e);
            }
        });
    }
    
    /**
     * Get inspections for asset asynchronously
     */
    public void getInspectionsForAssetAsync(int assetId, DatabaseCallback<List<Inspection>> callback) {
        executor.execute(() -> {
            try {
                List<Inspection> inspections = database.inspectionDao().getInspectionsByAssetId(assetId);
                callback.onSuccess(inspections);
            } catch (Exception e) {
                callback.onError(e);
            }
        });
    }
}