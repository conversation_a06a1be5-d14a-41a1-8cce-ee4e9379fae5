package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.UpdateAssetTask;

import java.util.List;

@Dao
public interface UpdateAssetTaskDao {

    @Query("SELECT * FROM AIUPDATE_ASSET_TASK WHERE B_DELETED = 0")
    List<UpdateAssetTask> getAllUpdateAssetTasks();

    @Query("SELECT * FROM AIUPDATE_ASSET_TASK WHERE ID = :id")
    UpdateAssetTask getUpdateAssetTaskById(long id);

    @Query("SELECT * FROM AIUPDATE_ASSET_TASK WHERE I_S_UPDATE_ASSET_TASK_ID = :serverUpdateAssetTaskId AND B_DELETED = 0")
    UpdateAssetTask getUpdateAssetTaskByServerId(int serverUpdateAssetTaskId);

    @Query("SELECT * FROM AIUPDATE_ASSET_TASK WHERE I_ASSET_ID = :assetId AND B_DELETED = 0")
    List<UpdateAssetTask> getUpdateAssetTasksByAssetId(int assetId);

    @Query("SELECT * FROM AIUPDATE_ASSET_TASK WHERE I_USER_ID = :userId AND B_DELETED = 0")
    List<UpdateAssetTask> getUpdateAssetTasksByUserId(int userId);

    @Query("SELECT * FROM AIUPDATE_ASSET_TASK WHERE I_STATUS = :status AND B_DELETED = 0")
    List<UpdateAssetTask> getUpdateAssetTasksByStatus(int status);

    @Query("SELECT * FROM AIUPDATE_ASSET_TASK WHERE B_PROCESSED = 0 AND B_DELETED = 0")
    List<UpdateAssetTask> getUnprocessedUpdateAssetTasks();

    @Insert
    long insertUpdateAssetTask(UpdateAssetTask updateAssetTask);

    @Insert
    List<Long> insertUpdateAssetTasks(List<UpdateAssetTask> updateAssetTasks);

    @Update
    void updateUpdateAssetTask(UpdateAssetTask updateAssetTask);

    @Update
    void updateUpdateAssetTasks(List<UpdateAssetTask> updateAssetTasks);

    @Delete
    void deleteUpdateAssetTask(UpdateAssetTask updateAssetTask);

    @Query("UPDATE AIUPDATE_ASSET_TASK SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AIUPDATE_ASSET_TASK SET B_DELETED = 1 WHERE I_S_UPDATE_ASSET_TASK_ID = :serverUpdateAssetTaskId")
    void softDeleteByServerId(int serverUpdateAssetTaskId);

    @Query("UPDATE AIUPDATE_ASSET_TASK SET B_PROCESSED = 1 WHERE ID = :id")
    void markAsProcessed(long id);

    @Query("UPDATE AIUPDATE_ASSET_TASK SET I_STATUS = :status WHERE ID = :id")
    void updateStatus(long id, int status);

    @Query("SELECT COUNT(*) FROM AIUPDATE_ASSET_TASK WHERE B_DELETED = 0")
    int getUpdateAssetTaskCount();

    @Query("SELECT COUNT(*) FROM AIUPDATE_ASSET_TASK WHERE I_ASSET_ID = :assetId AND B_PROCESSED = 0 AND B_DELETED = 0")
    int getUnprocessedUpdateAssetTaskCountByAsset(int assetId);

    // Batch operations
    @Query("DELETE FROM AIUPDATE_ASSET_TASK")
    void deleteAllUpdateAssetTasks();

    @Query("UPDATE AIUPDATE_ASSET_TASK SET B_DELETED = 1")
    void softDeleteAllUpdateAssetTasks();
}