package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;
import com.snapinspect.snapinspect3.IF_Object.ai_Project;
import java.util.Date;

@Entity(
    tableName = "AIPROJECT",
    indices = {
        @Index(value = "I_S_PROJECT_ID"),
        @Index(value = "I_CREATOR_ID"),
        @Index(value = "I_COMPANY_ID"),
        @Index(value = "I_GROUP_ID"),
        @Index(value = "B_DELETED"),
        @Index(value = "B_ACTIVE")
    }
)
@TypeConverters({com.snapinspect.snapinspect3.database.converters.TypeConverters.DateConverter.class})
public class Project {
    @PrimaryKey(autoGenerate = true)
    @ColumnInfo(name = "ID")
    public Long id;

    @ColumnInfo(name = "I_S_PROJECT_ID")
    public Integer iSProjectID;

    @ColumnInfo(name = "I_CREATOR_ID")
    public Integer iCreatorID;

    @ColumnInfo(name = "I_COMPANY_ID")
    public Integer iCompanyID;

    @ColumnInfo(name = "S_REFERENCE")
    public String sReference;

    @ColumnInfo(name = "S_NAME")
    public String sName;

    @ColumnInfo(name = "S_DESCRIPTION")
    public String sDescription;

    @ColumnInfo(name = "S_CUSTOM_FIELD")
    public String sCustomField;

    @ColumnInfo(name = "I_GROUP_ID")
    public Integer iGroupID;

    @ColumnInfo(name = "I_TOTAL_INS")
    public Integer iTotalIns;

    @ColumnInfo(name = "I_COMPLETED_INS")
    public Integer iCompletedIns;

    @ColumnInfo(name = "S_STATUS")
    public String sStatus;

    @ColumnInfo(name = "S_STATUS_CODE")
    public String sStatusCode;

    @ColumnInfo(name = "S_CUSTOM_ONE")
    public String sCustom1;

    @ColumnInfo(name = "S_CUSTOM_TWO")
    public String sCustom2;

    @ColumnInfo(name = "B_ACTIVE")
    public Boolean bActive = false;

    @ColumnInfo(name = "B_DELETED")
    public Boolean bDeleted = false;

    @ColumnInfo(name = "DT_UPDATE")
    public Date dtUpdate;

    @ColumnInfo(name = "DT_DATE_TIME")
    public Date dtDateTime;

    @ColumnInfo(name = "ARR_INSPECTOR")
    public String arrInspector;

    @ColumnInfo(name = "I_MANAGER_ID")
    public Integer iManagerID;

    // Constructors
    public Project() {}

    // Copy constructor from Sugar ORM entity
    public Project(ai_Project sugarEntity) {
        this.id = sugarEntity.getId();
        this.iSProjectID = sugarEntity.iSProjectID;
        this.iCreatorID = sugarEntity.iCreatorID;
        this.iCompanyID = sugarEntity.iCompanyID;
        this.sReference = sugarEntity.sReference;
        this.sName = sugarEntity.sName;
        this.sDescription = sugarEntity.sDescription;
        this.sCustomField = sugarEntity.sCustomField;
        this.iGroupID = sugarEntity.iGroupID;
        this.iTotalIns = sugarEntity.iTotalIns;
        this.iCompletedIns = sugarEntity.iCompletedIns;
        this.sStatus = sugarEntity.sStatus;
        this.sStatusCode = sugarEntity.sStatusCode;
        this.sCustom1 = sugarEntity.sCustom1;
        this.sCustom2 = sugarEntity.sCustom2;
        this.bActive = sugarEntity.bActive;
        this.bDeleted = sugarEntity.bDeleted;
        this.dtUpdate = sugarEntity.dtUpdate;
        this.dtDateTime = sugarEntity.dtDateTime;
        this.arrInspector = sugarEntity.arrInspector;
        this.iManagerID = sugarEntity.iManagerID;
    }

    // Convert to Sugar ORM entity for backward compatibility
    public ai_Project toSugarEntity() {
        ai_Project sugar = new ai_Project();
        if (this.id != null) {
            sugar.setId(this.id);
        }
        sugar.iSProjectID = this.iSProjectID != null ? this.iSProjectID : 0;
        sugar.iCreatorID = this.iCreatorID != null ? this.iCreatorID : 0;
        sugar.iCompanyID = this.iCompanyID != null ? this.iCompanyID : 0;
        sugar.sReference = this.sReference;
        sugar.sName = this.sName;
        sugar.sDescription = this.sDescription;
        sugar.sCustomField = this.sCustomField;
        sugar.iGroupID = this.iGroupID != null ? this.iGroupID : 0;
        sugar.iTotalIns = this.iTotalIns != null ? this.iTotalIns : 0;
        sugar.iCompletedIns = this.iCompletedIns != null ? this.iCompletedIns : 0;
        sugar.sStatus = this.sStatus;
        sugar.sStatusCode = this.sStatusCode;
        sugar.sCustom1 = this.sCustom1;
        sugar.sCustom2 = this.sCustom2;
        sugar.bActive = this.bActive != null ? this.bActive : false;
        sugar.bDeleted = this.bDeleted != null ? this.bDeleted : false;
        sugar.dtUpdate = this.dtUpdate;
        sugar.dtDateTime = this.dtDateTime;
        sugar.arrInspector = this.arrInspector;
        sugar.iManagerID = this.iManagerID != null ? this.iManagerID : 0;
        return sugar;
    }
}