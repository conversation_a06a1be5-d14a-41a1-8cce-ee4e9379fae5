package com.snapinspect.snapinspect3.database.entities;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.PrimaryKey;

import com.snapinspect.snapinspect3.IF_Object.ai_ProductCost;

/**
 * Room entity for ai_ProductCost
 */
@Entity(tableName = "AIPRODUCT_COST")
public class ProductCost {
    
    @PrimaryKey(autoGenerate = true)
    public Long id;
    
    @ColumnInfo(name = "I_S_COSTING_ID")
    public int iSCostingID;
    
    @ColumnInfo(name = "I_ASSET_ID")
    public int iAssetID;
    
    @ColumnInfo(name = "I_INSPECTION_ID")
    public int iInspectionID;
    
    @ColumnInfo(name = "I_INS_ITEM_ID")
    public int iInsItemID;
    
    @ColumnInfo(name = "I_P_INS_ITEM_ID")
    public int iPInsItemID;
    
    @ColumnInfo(name = "I_TASK_ID")
    public int iTaskID;
    
    @ColumnInfo(name = "I_PRODUCT_ID")
    public int iProductID;
    
    @ColumnInfo(name = "D_UNIT")
    public double dUnit;
    
    @ColumnInfo(name = "D_UNIT_COST")
    public double dUnitCost;
    
    @ColumnInfo(name = "D_TOTAL_COST")
    public double dTotalCost;
    
    @ColumnInfo(name = "S_NOTES")
    public String sNotes;
    
    @ColumnInfo(name = "S_CUSTOM1")
    public String sCustom1;
    
    @ColumnInfo(name = "S_CUSTOM2")
    public String sCustom2;
    
    @ColumnInfo(name = "S_CUSTOM3")
    public String sCustom3;
    
    @ColumnInfo(name = "B_DELETED")
    public boolean bDeleted;
    
    @ColumnInfo(name = "I_CREATED_BY")
    public int iCreatedBy;
    
    @ColumnInfo(name = "I_UPDATED_BY")
    public int iUpdatedBy;
    
    @ColumnInfo(name = "DT_UPDATE")
    public String dtUpdate;
    
    @ColumnInfo(name = "DT_DATE_TIME")
    public String dtDateTime;
    
    public ProductCost() {}
    
    public ProductCost(ai_ProductCost source) {
        this.iSCostingID = source.iSCostingID;
        this.iAssetID = source.iAssetID;
        this.iInspectionID = source.iInspectionID;
        this.iInsItemID = source.iInsItemID;
        this.iPInsItemID = source.iPInsItemID;
        this.iTaskID = source.iTaskID;
        this.iProductID = source.iProductID;
        this.dUnit = source.dUnit;
        this.dUnitCost = source.dUnitCost;
        this.dTotalCost = source.dTotalCost;
        this.sNotes = source.sNotes;
        this.sCustom1 = source.sCustom1;
        this.sCustom2 = source.sCustom2;
        this.sCustom3 = source.sCustom3;
        this.bDeleted = source.bDeleted;
        this.iCreatedBy = source.iCreatedBy;
        this.iUpdatedBy = source.iUpdatedBy;
        this.dtUpdate = source.dtUpdate;
        this.dtDateTime = source.dtDateTime;
    }
}