package com.snapinspect.snapinspect3.database.relations;

import androidx.room.Embedded;
import androidx.room.Relation;
import com.snapinspect.snapinspect3.database.entities.Assets;
import com.snapinspect.snapinspect3.database.entities.Inspection;

import java.util.List;

public class AssetWithInspections {
    @Embedded
    public Assets asset;

    @Relation(
        parentColumn = "I_S_ASSET_ID",
        entityColumn = "I_S_ASSET_ID"
    )
    public List<Inspection> inspections;
}