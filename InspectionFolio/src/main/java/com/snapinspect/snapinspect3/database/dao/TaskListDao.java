package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.TaskList;

import java.util.List;

@Dao
public interface TaskListDao {
    
    @Query("SELECT * FROM AITASK_LIST WHERE B_DELETED = 0")
    List<TaskList> getAllTaskLists();

    @Query("SELECT * FROM AITASK_LIST WHERE ID = :id")
    TaskList getTaskListById(long id);

    @Query("SELECT * FROM AITASK_LIST WHERE I_S_TASK_LIST_ID = :serverTaskListId AND B_DELETED = 0")
    TaskList getTaskListByServerId(int serverTaskListId);

    @Query("SELECT * FROM AITASK_LIST WHERE I_PROJECT_ID = :projectId AND B_DELETED = 0")
    List<TaskList> getTaskListsByProjectId(int projectId);

    @Query("SELECT * FROM AITASK_LIST WHERE I_CREATED_USER_ID = :userId AND B_DELETED = 0")
    List<TaskList> getTaskListsByCreatedUserId(int userId);

    @Query("SELECT * FROM AITASK_LIST WHERE S_NAME LIKE :namePattern AND B_DELETED = 0")
    List<TaskList> searchTaskListsByName(String namePattern);

    @Insert
    long insertTaskList(TaskList taskList);

    @Insert
    List<Long> insertTaskLists(List<TaskList> taskLists);

    @Update
    void updateTaskList(TaskList taskList);

    @Update
    void updateTaskLists(List<TaskList> taskLists);

    @Delete
    void deleteTaskList(TaskList taskList);

    @Query("UPDATE AITASK_LIST SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AITASK_LIST SET B_DELETED = 1 WHERE I_S_TASK_LIST_ID = :serverTaskListId")
    void softDeleteByServerId(int serverTaskListId);

    @Query("UPDATE AITASK_LIST SET B_DELETED = 1 WHERE I_PROJECT_ID = :projectId")
    void softDeleteByProjectId(int projectId);

    @Query("SELECT COUNT(*) FROM AITASK_LIST WHERE B_DELETED = 0")
    int getTaskListCount();

    @Query("SELECT COUNT(*) FROM AITASK_LIST WHERE I_PROJECT_ID = :projectId AND B_DELETED = 0")
    int getTaskListCountByProject(int projectId);

    // Batch operations
    @Query("DELETE FROM AITASK_LIST")
    void deleteAllTaskLists();

    @Query("UPDATE AITASK_LIST SET B_DELETED = 1")
    void softDeleteAllTaskLists();
}