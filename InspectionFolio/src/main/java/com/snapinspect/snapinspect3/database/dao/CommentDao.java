package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;
import com.snapinspect.snapinspect3.database.entities.Comment;

import java.util.List;

@Dao
public interface CommentDao {
    
    @Query("SELECT * FROM AICOMMENT WHERE B_DELETED = 0")
    List<Comment> getAllComments();

    @Query("SELECT * FROM AICOMMENT WHERE ID = :id")
    Comment getCommentById(long id);

    @Query("SELECT * FROM AICOMMENT WHERE I_COMMENT_ID = :commentId AND B_DELETED = 0")
    Comment getCommentByCommentId(int commentId);

    @Query("SELECT * FROM AICOMMENT WHERE I_INSPECTION_ID = :inspectionId AND B_DELETED = 0 ORDER BY DATE")
    List<Comment> getCommentsByInspectionId(long inspectionId);

    @Query("SELECT * FROM AICOMMENT WHERE I_COMPANY_ID = :companyId AND B_DELETED = 0 ORDER BY DATE DESC")
    List<Comment> getCommentsByCompanyId(int companyId);

    @Query("SELECT * FROM AICOMMENT WHERE I_CUSTOMER_ID = :customerId AND B_DELETED = 0 ORDER BY DATE DESC")
    List<Comment> getCommentsByCustomerId(int customerId);

    @Query("SELECT * FROM AICOMMENT WHERE S_TYPE = :type AND B_DELETED = 0")
    List<Comment> getCommentsByType(String type);

    @Insert
    long insertComment(Comment comment);

    @Insert
    List<Long> insertComments(List<Comment> comments);

    @Update
    void updateComment(Comment comment);

    @Update
    void updateComments(List<Comment> comments);

    @Delete
    void deleteComment(Comment comment);

    @Query("UPDATE AICOMMENT SET B_DELETED = 1 WHERE ID = :id")
    void softDeleteById(long id);

    @Query("UPDATE AICOMMENT SET B_DELETED = 1 WHERE I_COMMENT_ID = :commentId")
    void softDeleteByCommentId(int commentId);

    @Query("SELECT COUNT(*) FROM AICOMMENT WHERE B_DELETED = 0")
    int getCommentCount();

    @Query("SELECT COUNT(*) FROM AICOMMENT WHERE I_INSPECTION_ID = :inspectionId AND B_DELETED = 0")
    int getCommentCountByInspection(long inspectionId);

    @Query("SELECT COUNT(*) FROM AICOMMENT WHERE I_COMPANY_ID = :companyId AND B_DELETED = 0")
    int getCommentCountByCompany(int companyId);

    @Query("DELETE FROM AICOMMENT")
    void deleteAllComments();

    @Query("UPDATE AICOMMENT SET B_DELETED = 1")
    void softDeleteAllComments();
}