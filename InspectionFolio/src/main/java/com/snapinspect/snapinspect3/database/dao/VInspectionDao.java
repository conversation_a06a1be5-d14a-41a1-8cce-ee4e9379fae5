package com.snapinspect.snapinspect3.database.dao;

import androidx.room.Dao;
import androidx.room.Query;
import com.snapinspect.snapinspect3.database.views.VInspectionView;

import java.util.List;

@Dao
public interface VInspectionDao {
    
    @Query("SELECT * FROM VINSPECTION WHERE bComplete = :completed AND bSynced = :synced ORDER BY iInspectionID DESC")
    List<VInspectionView> searchInspections(boolean completed, boolean synced);
    
    @Query("SELECT * FROM VINSPECTION WHERE sSearchTerm LIKE :searchTerm AND bComplete = :completed AND bSynced = :synced ORDER BY iInspectionID DESC")
    List<VInspectionView> searchInspectionsByTerm(String searchTerm, boolean completed, boolean synced);
    
    @Query("SELECT * FROM VINSPECTION")
    List<VInspectionView> getAllInspections();
}