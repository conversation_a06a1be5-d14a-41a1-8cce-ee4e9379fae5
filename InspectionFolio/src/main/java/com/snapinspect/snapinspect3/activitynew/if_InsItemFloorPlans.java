package com.snapinspect.snapinspect3.activitynew;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Adapter.InsItemFloorPlanAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_Assets;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_FloorPlan;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.views.EmptyDataView;
import com.snapinspect.snapinspect3.views.InspectionItemTitleView;
import com.snapinspect.snapinspect3.views.StickyHeaderListView.StickyHeaderListView;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class if_InsItemFloorPlans extends Activity implements InsItemFloorPlanAdapter.Listener {

    public static class FloorPlanAnnotation {
        public final ai_FloorPlan floorPlan;
        public final List<Map<String, Object>> sMarks;
        public final int iPhotoID;
        public final int iSPhotoID;

        public FloorPlanAnnotation(ai_FloorPlan floorPlan, List<Map<String, Object>> sMarks, int iPhotoID, int iSPhotoID) {
            this.floorPlan = floorPlan;
            this.sMarks = sMarks;
            this.iPhotoID = iPhotoID;
            this.iSPhotoID = iSPhotoID;
        }
    }
    private SwipeRefreshLayout mListSwipeRefreshLayout;
    private SwipeRefreshLayout mEmptySwipeRefreshLayout;
    private StickyHeaderListView mListView;

    private ai_InsItem mInsItem;
    private ai_Assets mAsset;

    // Blueprints (Available)
    private List<ai_FloorPlan> lsFloorPlan;

    public static Intent newIntent(Context oContext, long iInsItemID) {
        Intent intent = new Intent(oContext, if_InsItemFloorPlans.class);
        intent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        return intent;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        try {
            setTheme(R.style.RemoveShadowActionBar);
            super.onCreate(savedInstanceState);
            setContentView(R.layout.activity_item_floor_plan);

            getActionBar().show();
            getActionBar().setDisplayHomeAsUpEnabled(true);
            initializeData();
            initializeViews();
            loadAssetFloorPlansIfNecessary();
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception("if_InsItemFloorPlans.onCreate", "Exception:" + e.getMessage(), e);
        }
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        onBackPressed();
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onResume() {
        super.onResume();
        initializeData();
        updateFloorPlans();
    }

    /**
     * Initializes the views of the activity.
     */
    private void initializeViews() {
        EmptyDataView mEmptyView;
        mListSwipeRefreshLayout = findViewById(R.id.list_refresh_layout);
        mListSwipeRefreshLayout.setOnRefreshListener(() -> {
            loadAssetFloorPlans(mListSwipeRefreshLayout);
        });
        mEmptySwipeRefreshLayout = findViewById(R.id.empty_data_refresh_layout);
        mEmptySwipeRefreshLayout.setOnRefreshListener(() -> {
            loadAssetFloorPlans(mEmptySwipeRefreshLayout);
        });

        mListView = findViewById(R.id.listview_floor_plan);
        mListView.getListView().setDividerHeight(0);
        mEmptyView = findViewById(R.id.view_empty_data);
        mEmptyView.setListener(() -> {
            CommonHelper.openURL(this, getString(R.string.projects_support_url));
        });

        InspectionItemTitleView titleView = findViewById(R.id.inspection_item_title_view);
        titleView.setItemTitles(mInsItem.getParentItemName(), mInsItem.sName);
        if (mAsset != null) {
            getActionBar().setTitle(mAsset.sAddressOne + " " + mAsset.sAddressTwo);
        } else {
            getActionBar().setTitle(R.string.title_activity_item_floor_plan);
        }
    }

    private void initializeData() {
        long iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, -1);
        if (iInsItemID <= 0) {
            CommonUI.ShowAlert(this, "Error", "Invalid Inspection Item ID");
            return;
        }
        mInsItem = db_InsItem.GetInsItem_ByID(iInsItemID);
        mAsset = db_InsItem.getAssetByInsItemID(iInsItemID);
    }

    private void loadAssetFloorPlansIfNecessary() {
        if (mAsset != null && mAsset.iSAssetID > 0) {
            List<ai_FloorPlan> floorPlans = CommonDB_FloorPlan.getFloorPlans(mAsset.iSAssetID);
            if (floorPlans.isEmpty()) {
                loadAssetFloorPlans(mListSwipeRefreshLayout);
            }
        }
    }

    /**
     * Updates the blueprints of the activity.
     * It retrieves the blueprints from the local database using the asset ID.
     * It also gets the drop pin data from the inspection item and converts it to annotated blueprints.
     * Finally, it updates the list view with the new blueprints using a custom adapter.
     */
    private void updateFloorPlans() {
        if (mAsset == null || mAsset.iSAssetID <= 0) return;
        // local database
        lsFloorPlan = CommonDB_FloorPlan.getFloorPlans(mAsset.iSAssetID);
        List<Map<String, Object>> dropPins = db_InsItem.getDropPinData(mInsItem);
        // Blueprints (Annotated)
        List<FloorPlanAnnotation> lsAnnotatedFloorPlan = dropPinsToFloorPlans(dropPins);

        List<ai_FloorPlan> lsAvailableFloorPlan = new ArrayList<>();
        for (ai_FloorPlan floorPlan : lsFloorPlan) {
            boolean isAvailable = true;
            for (FloorPlanAnnotation annotatedFloorPlan : lsAnnotatedFloorPlan) {
                if (Objects.equals(floorPlan.getId(), annotatedFloorPlan.floorPlan.getId())) {
                    isAvailable = false;
                    break;
                }
            }
            if (isAvailable) lsAvailableFloorPlan.add(floorPlan);
        }

        if (lsFloorPlan.isEmpty()) {
            mEmptySwipeRefreshLayout.setVisibility(View.VISIBLE);
            mListSwipeRefreshLayout.setVisibility(View.GONE);
        } else {
            mEmptySwipeRefreshLayout.setVisibility(View.GONE);
            mListSwipeRefreshLayout.setVisibility(View.VISIBLE);
            // update the list view
            InsItemFloorPlanAdapter adapter =
                    new InsItemFloorPlanAdapter(this, lsAnnotatedFloorPlan, lsAvailableFloorPlan, this);
            mListView.setAdapter(adapter);
            adapter.notifyDataSetChanged();
        }
    }

    private List<FloorPlanAnnotation> dropPinsToFloorPlans(List<Map<String, Object>> dropPins) {
        List<FloorPlanAnnotation> floorPlans = new ArrayList<>();
        for (Map<String, Object> dropPin : dropPins) {
            try {
                Object iFloorPlanIdOjb = dropPin.get(Constants.Keys.iFloorPlanID);
                Object sMarksObj = dropPin.get(Constants.Keys.objects);
                if (!(iFloorPlanIdOjb instanceof Double) || !(sMarksObj instanceof List)) continue;

                double iFloorPlanID = (double) iFloorPlanIdOjb;
                List<Map<String, Object>> sMarks = (List<Map<String, Object>>) sMarksObj;
                ai_FloorPlan floorPlan = CommonDB_FloorPlan.getFloorPlan((int) iFloorPlanID);
                if (floorPlan != null) {
                    int iPhotoID = getValueFromMap(dropPin, Constants.Keys.iPhotoID, 0.0).intValue();
                    int iSPhotoID = getValueFromMap(dropPin, Constants.Keys.iSPhotoID, 0.0).intValue();
                    floorPlans.add(new FloorPlanAnnotation(
                            floorPlan, sMarks, iPhotoID, iSPhotoID));
                }
            } catch (Exception e) {
                ai_BugHandler.ai_Handler_Exception("if_InsItemFloorPlans.dropPinsToFloorPlans", "Exception:" + e.getMessage(), e);
            }
        }
        return floorPlans;
    }

    private <T> T getValueFromMap(Map<String, Object> map, String key, T defaultValue) {
        if (map.containsKey(key)) {
            return (T) map.get(key);
        }
        return defaultValue;
    }

    private void loadAssetFloorPlans(SwipeRefreshLayout refreshLayout) {
        if (!NetworkUtils.isNetworkAvailable(this)) {
            refreshLayout.setRefreshing(false);
            CommonUI.ShowAlert(this, getString(R.string.title_alert_error), getString(R.string.failed_connection));
            return;
        }

        MaterialDialog materialDialog = null;
        if (lsFloorPlan != null && !lsFloorPlan.isEmpty()) {
            refreshLayout.setRefreshing(true);
        } else {
            materialDialog = CommonUI.ShowMaterialProgressDialog(
                this, "Connecting to server...", "Please wait...");
        }
        MaterialDialog finalMaterialDialog = materialDialog;
        CommonFloorPlan.loadFloorPlans(this, mAsset.iSAssetID,
                new CommonFloorPlan.RequestCompletion<List<ai_FloorPlan>>() {
                    @Override
                    public void onSuccess(List<ai_FloorPlan> floorPlans) {
                        refreshLayout.setRefreshing(false);
                        if (finalMaterialDialog != null) {
                            finalMaterialDialog.dismiss();
                        }
                        lsFloorPlan = floorPlans;
                        updateFloorPlans();
                    }

                    @Override
                    public void onFailure(Error error) {
                        refreshLayout.setRefreshing(false);
                        if (finalMaterialDialog != null) {
                            finalMaterialDialog.dismiss();
                        }
                    }
                },
                (progress, total) -> {
                    if (finalMaterialDialog != null) {
                        finalMaterialDialog.setContent(getString(R.string.message_progress_download_floor_plan, progress, total));
                    }
                }
        );
    }

    @Override
    public void onClickOnFloorPlan(ai_FloorPlan floorPlan, InsItemFloorPlanAdapter.FloorPlanType floorPlanType) {
        startActivity(if_DisplayFloorPlan.newIntent(
            this, floorPlan.getId(), mInsItem.getId(), if_DisplayFloorPlan.FloorPlanLayerType.ITEM, floorPlanType));
    }
}
