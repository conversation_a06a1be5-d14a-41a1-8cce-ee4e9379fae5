package com.snapinspect.snapinspect3.activitynew.Edit;

import android.app.Activity;
import android.os.Bundle;
import androidx.annotation.Nullable;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.Adapter.FormItemsAdapter;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.Helper.IF_RestClient;
import com.snapinspect.snapinspect3.Helper.NetworkUtils;
import com.snapinspect.snapinspect3.IF_Object.ai_Assets;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.if_FormItem;
import com.snapinspect.snapinspect3.IF_Object.v_Asset;
import com.snapinspect.snapinspect3.IF_Object.v_Asset.AssetType;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_Asset;

import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.addressLine1;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.addressLine2;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.alarms;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.keys;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.multiFamily;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.reference;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.ViewType.checkBox;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.ViewType.textInput;
import static com.snapinspect.snapinspect3.IF_Object.v_Asset.AssetType.*;

public class if_UpdateAsset_2nd extends Activity {

    private List<if_FormItem> mDataSource;
    private ListView listView;
    private int iSAssetID, iSPAssetID;
    private ai_Assets oAsset;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_update_asset_2nd);

        try {
            iSAssetID = getIntent().getIntExtra(Constants.Extras.iSAssetID, 0);
            iSPAssetID = getIntent().getIntExtra(Constants.Extras.iSPAssetID, 0);

            getActionBar().setTitle(getNavTitle());
            getActionBar().setDisplayHomeAsUpEnabled(true);

            View bottomContainer = findViewById(R.id.btn_bottom_container);
            if (!NetworkUtils.isNetworkAvailable(this)) {
                bottomContainer.setVisibility(View.GONE);
                CommonUI.ShowAlert(this, "Error",
                        "To update asset information, please make sure you are connected to Internet.",
                        R.string.action_cancel, this::finish);
                return;
            }

            oAsset = db_Asset.GetAssetBySAssetID(iSAssetID);
            if (oAsset == null) oAsset = new ai_Assets();
            else iSPAssetID = oAsset.iSPAssetID;

            listView = findViewById(R.id.lv_formItems);
            listView.setAdapter(new FormItemsAdapter(this, getDataSource()));

            TextView txtAddress = findViewById(R.id.txt_AssetAddress);
            String topText = getTopText();
            txtAddress.setText(topText);
            txtAddress.setVisibility(StringUtils.isEmpty(topText) ? View.GONE : View.VISIBLE);

            bottomContainer.setVisibility(View.VISIBLE);
            Button btnBottom = findViewById(R.id.btn_bottom);
            btnBottom.setText(getBottomTitle());
            btnBottom.setOnClickListener(v -> requestSubmitFormData());

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        onBackPressed();
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        CommonHelper.hideSoftKeyboard(this);
        super.onBackPressed();
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    private String getNavTitle() {
        int titleRes = 0;
        if (iSAssetID > 0) {
            titleRes = R.string.update_asset;
        } else {
            switch (getAssetType()) {
                case ASSET:
                    titleRes = R.string.new_asset;
                    break;
                case UNIT:
                    titleRes = R.string.new_unit;
                    break;
                case ROOM:
                    titleRes = R.string.new_room;
                    break;
            }
        }
        return titleRes != 0 ? getResources().getString(titleRes) : "";
    }

    private String getTopText() {
        if (getAssetType() == ROOM || getAssetType() == UNIT) {
            if (iSAssetID > 0) {
                v_Asset vAsset = db_Asset.GetVAssetBySAssetID(iSAssetID);
                return vAsset.sBuildingAddress;
            }
        }
        return null;
    }

    private String getBottomTitle() {
        int titleRes = 0;
        if (iSAssetID > 0) {
            switch (getAssetType()) {
                case ASSET:
                    titleRes = R.string.save_asset;
                    break;
                case UNIT:
                    titleRes = R.string.save_unit;
                    break;
                case ROOM:
                    titleRes = R.string.save_room;
                    break;
            }
        } else {
            switch (getAssetType()) {
                case ASSET:
                    titleRes = R.string.create_asset;
                    break;
                case UNIT:
                    titleRes = R.string.create_unit;
                    break;
                case ROOM:
                    titleRes = R.string.create_room;
                    break;
            }
        }
        return titleRes != 0 ? getResources().getString(titleRes) : "";
    }

    private AssetType getAssetType() {
        v_Asset vAsset = db_Asset.GetVAssetBySAssetID(iSAssetID);
        if (vAsset == null) {
            vAsset = new v_Asset();
            vAsset.iPSAssetID = iSPAssetID;
        }
        return vAsset.getAssetType();
    }

    private List<if_FormItem> getDataSource() {
        if (mDataSource == null) {
            ArrayList<if_FormItem> items = new ArrayList<>();

            if_FormItem line1 = new if_FormItem(addressLine1, textInput);
            line1.title = "Address Line 1";
            line1.requiredTitle = "Line 1 required";
            line1.value = oAsset.sAddressOne;
            items.add(line1);

            if_FormItem line2 = new if_FormItem(addressLine2, textInput);
            line2.title = "Address Line 2";
            line2.value = oAsset.sAddressTwo;
            items.add(line2);

            if_FormItem ref = new if_FormItem(reference, textInput);
            ref.title = "References";
            items.add(ref);

            if_FormItem key = new if_FormItem(keys, textInput);
            key.title = "Keys";
            key.value = oAsset.sKey;
            items.add(key);

            if_FormItem alarm = new if_FormItem(alarms, textInput);
            alarm.title = "Alarms";
            alarm.value = oAsset.sAlarm;
            items.add(alarm);

            if (showMultiFamilyOption()) {
                if_FormItem multi = new if_FormItem(multiFamily, checkBox);
                multi.value = String.valueOf(false);
                if (getAssetType() == ASSET) {
                    multi.title = getResources().getString(R.string.mark_asset_multi_family);
                } else if (getAssetType() == UNIT) {
                    multi.title = getResources().getString(R.string.allow_additional_rooms);
                }
                items.add(multi);
            }

            updateFormDataSource(items);
            mDataSource = items;
        }
        return mDataSource;
    }

    private boolean showMultiFamilyOption() {
        if (iSAssetID > 0 || getAssetType() == ROOM) return false;
        return getAssetType() == ASSET && CommonHelper.GetPreferenceBoolean(this, Constants.Settings.bMultiFamily) ||
                getAssetType() == UNIT && CommonHelper.GetPreferenceBoolean(this, Constants.Settings.bRoom);
    }

    private void updateFormDataSource(List<if_FormItem> dataSource) {
        if (iSAssetID <= 0) return;
        v_Asset vAsset = db_Asset.GetVAssetBySAssetID(iSAssetID);
        for (if_FormItem item : dataSource) {
            if (item.identifier.equalsIgnoreCase(addressLine1)) {
                item.value = oAsset.sAddressOne;
            } else if (item.identifier.equalsIgnoreCase(addressLine2)) {
                item.value = oAsset.sAddressTwo;
            } else if (item.identifier.equalsIgnoreCase(reference)) {
                if (vAsset != null) item.value = vAsset.sRef;
            } else if (item.identifier.equalsIgnoreCase(keys)) {
                item.value = oAsset.sKey;
            } else if (item.identifier.equalsIgnoreCase(alarms)) {
                item.value = oAsset.sAlarm;
            } else if (item.identifier.equalsIgnoreCase(multiFamily)) {
                if (vAsset != null) item.value = String.valueOf(vAsset.bApartment);
            }
        }

        FormItemsAdapter itemsAdapter = (FormItemsAdapter) listView.getAdapter();
        if (itemsAdapter != null) {
            itemsAdapter.setFormItems(getDataSource());
        }
    }

    private String validateFormItems() {
        String sAddress1;
        for (if_FormItem formItem : getDataSource()) {
            formItem.validateResult = if_FormItem.ValidateResult.none;
            if (formItem.identifier.equalsIgnoreCase(addressLine1)) {
                sAddress1 = !StringUtils.isEmpty(formItem.value) ? formItem.value.trim() : "";
                if (sAddress1.length() == 0) {
                    formItem.validateResult = if_FormItem.ValidateResult.failure;
                    return "Address Line 1 can not be empty.";
                }
            }
        }
        return null;
    }

    private void requestSubmitFormData() {
        if (NetworkUtils.isNetworkAvailable(this)) {
            String sMessage = validateFormItems();
            if (!StringUtils.isEmpty(sMessage)) {
                CommonUI.ShowAlert(this, "Error", sMessage);
            } else {
                MaterialDialog progressDialog = CommonUI.ShowMaterialProgressDialog(
                        this, "Message", "Processing. Please wait ...");
                CommonHelper.hideSoftKeyboard(this);
                List<if_FormItem> data = getDataSource();
                RequestParams oParams = new RequestParams();
                for (if_FormItem item : data) {
                    oParams.add(item.identifier, !StringUtils.isEmpty(item.value) ? item.value.trim() : "");
                }

                if (!showMultiFamilyOption() && iSAssetID > 0)
                    oParams.add("bApartment", String.valueOf(oAsset.bPush));
                oParams.add("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                oParams.add("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
                oParams.add("iSPropertyID", "" + iSPAssetID);
                oParams.add("iPropertyID", "" + iSAssetID);
                oParams.add("sInsDue", StringUtils.isEmpty(oAsset.dtInsDue) ? "" : oAsset.dtInsDue);
                oParams.add("sNotes", StringUtils.isEmpty(oAsset.sNotes) ? "" : oAsset.sNotes);

                String sURL = "/IOAPI/App_UpdateProperty_V2";
                IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                    @Override
                    public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                        runOnUiThread(() -> progressDialog.dismiss());
                        try {
                            if (statusCode == 200 && response.getBoolean("success")) {
                                JSONObject oObject = response.getJSONObject("oProperty");
                                int iServerAssetID = oObject.getInt("iPropertyID");
                                if (iServerAssetID > 0) {
                                    oAsset.sAddressOne = oObject.getString("sAddress1");
                                    oAsset.sAddressTwo = oObject.getString("sAddress2");
                                    oAsset.sKey = oObject.getString("sKey");
                                    oAsset.sAlarm = oObject.getString("sAlarm");
                                    oAsset.dtInsDue = ((oObject.getString("dtDue") == null || oObject.getString("dtDue").length() < 11)
                                            ? "" : oObject.getString("dtDue").substring(0, 12));
                                    oAsset.sFilter = CommonHelper.GetFilter(oAsset.sAddressOne, oAsset.sAddressTwo);
                                    oAsset.sFieldThree = oObject.getString("sRef");
                                    oAsset.bPush = oObject.getBoolean("bApartment");
                                    if (oAsset.iSAssetID == 0) {
                                        oAsset.iSAssetID = iServerAssetID;
                                        oAsset.iSPAssetID = (oObject.isNull("iSPropertyID") ? 0 : oObject.getInt("iSPropertyID"));
                                    }
                                    oAsset.save();
                                } else {
                                    Toast.makeText(if_UpdateAsset_2nd.this, "Failed to create new Asset, Please go to 'Settings' and 'Ask Support' for help.", Toast.LENGTH_LONG).show();
                                }
                                finish();
                            } else {
                                String message = response.getString("message");
                                if (StringUtils.isEmpty(message)) {
                                    message = "To create or update asset information, please make sure you are connected to Internet.";
                                }
                                CommonUI.ShowAlert(if_UpdateAsset_2nd.this, "Error", message);
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Exception", "if_UpdateAsset_2nd.UpdateAsset", ex, if_UpdateAsset_2nd.this);
                        }
                    }

                    @Override
                    public void onFailure(int statusCode, org.apache.http.Header[] headers, java.lang.Throwable e, org.json.JSONObject errorResponse) {
                        runOnUiThread(() -> progressDialog.dismiss());
                        CommonUI.ShowAlert(if_UpdateAsset_2nd.this, "Error", "To update asset information, please make sure you are connected to Internet.");
                    }
                });
            }
        } else {
            CommonUI.ShowAlert(this, "Error", "To update asset or contact information, please make sure the device is connected to Internet. Please note, inspecting assets DO NOT require internet but uploading inspection data requires either 3G or WiFi. If you want to inspect a NEW asset without internet, please go to Inspection Tab, and add a new inspection from there so you can carry on inspection on a new asset without Internet.");
        }
    }
}
