package com.snapinspect.snapinspect3.activitynew.Photo;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.os.Handler;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Adapter.DisplayPhotoAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.QRScan.QRScannerActivity;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activity.if_CommentsLibrary;
import com.snapinspect.snapinspect3.activity.if_ImageAnnotation;
import com.snapinspect.snapinspect3.util.*;

import java.io.File;
import java.lang.reflect.Field;
import java.util.List;

import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.viewpager.widget.ViewPager;
import com.snapinspect.snapinspect3.views.InputAccessoryToolbar;
import com.squareup.picasso.Picasso;
import kotlin.Result;
import kotlin.Unit;
import kotlin.jvm.functions.Function1;
import kotlinx.coroutines.Dispatchers;

import static com.snapinspect.snapinspect3.Helper.Constants.Extras.bFromAllPhotos;
import static com.snapinspect.snapinspect3.Helper.Constants.Extras.sComments;

public class if_DisplayPhoto extends Activity implements View.OnClickListener, KeyboardHeightProvider.KeyboardHeightObserver {

    private static final int ALL_PHOTOS_REQUEST = 1;
    private static final int COMMENTS_LIBRARY_REQUEST_CODE = 2;
    private long iPhotoID, iInsItemID;
    private int iPosition;
    private ViewPager viewPager;
    private int viewPagerHeight;
    private EditText mPhotoCommentsText;

    private KeyboardHeightProvider keyboardHeightProvider;
    private String sPhotoIds;
    private boolean bNoticePhoto;

    public static Intent newIntent(Context context, long iPhotoID, int iPosition,
                                   long lInsItemID, String sPhotoIds, boolean bNoticePhoto,
                                   boolean bFromAllPhotos) {
        Intent intent = new Intent(context, if_DisplayPhoto.class);
        intent.putExtra(Constants.Extras.iPhotoID, iPhotoID);
        intent.putExtra(Constants.Extras.iPosition, iPosition);
        intent.putExtra(Constants.Extras.iInsItemID, lInsItemID);
        intent.putExtra(Constants.Extras.sPhotoIds, sPhotoIds);
        intent.putExtra(Constants.Extras.bNoticePhotos, bNoticePhoto);
        intent.putExtra(Constants.Extras.bFromAllPhotos, bFromAllPhotos);
        return intent;
    }

    private static int getPhotoWidth() {
        if ((CommonUI.getScreenHeight() / (double)CommonUI.getScreenWidth()) > 1.2) {
            return (int) (CommonUI.getScreenWidth() * 0.9);
        } else {
            return (int) (CommonUI.getScreenWidth() * 0.66);
        }
    }

    public static Intent newIntent(Context context, long iPhotoID, int iPosition,
                                   long lInsItemID, String sPhotoIds, boolean bNoticePhoto) {
        return newIntent(context, iPhotoID, iPosition, lInsItemID, sPhotoIds, bNoticePhoto, false);
    }

    public static Intent newIntent(Context context, long iPhotoID, String sPhotoIds, boolean bNoticePhoto) {
        return newIntent(context, iPhotoID, 0, 0, sPhotoIds, bNoticePhoto, false);
    }

    public static Intent newIntent(Context ctx, long iPhotoID) {
        Intent intent = new Intent(ctx, if_DisplayPhoto.class);
        intent.putExtra(Constants.Extras.iPhotoID, iPhotoID);
        return intent;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            setContentView(R.layout.activity_display_photo);
            getActionBar().setDisplayHomeAsUpEnabled(true);

            iPhotoID = getIntent().getLongExtra(Constants.Extras.iPhotoID, 0);
            iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, 0);
            iPosition = getIntent().getIntExtra(Constants.Extras.iPosition, 0);
            sPhotoIds = getIntent().getStringExtra(Constants.Extras.sPhotoIds);
            bNoticePhoto = getIntent().getBooleanExtra(Constants.Extras.bNoticePhotos, false);

            findViewById(R.id.btn_Delete_Photo).setOnClickListener(this);
            findViewById(R.id.btn_action_all_photos).setOnClickListener(this);

            int width = (int) getPhotoWidth();
            DisplayPhotoAdapter adapter = new DisplayPhotoAdapter(this, width, width);
            adapter.setCallback(sComments -> saveCurrentComments());

            viewPager = findViewById(R.id.view_pager);
            viewPager.setAdapter(adapter);
            viewPager.addOnPageChangeListener(new ViewPager.OnPageChangeListener() {
                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                }

                @Override
                public void onPageSelected(int position) {
                    iPhotoID = getLsPhoto().get(position).getId();
                    updateBottomInfo(position);
                    mPhotoCommentsText = null;
                    new Handler().postDelayed(() -> {
                        hidesKeyboard();
                        reloadPhotos();
                    }, 200);
                }

                @Override
                public void onPageScrollStateChanged(int state) {
                }
            });

            InputAccessoryToolbar inputAccessoryToolbar = findViewById(R.id.input_accessory_view);
            inputAccessoryToolbar.setListener(new InputAccessoryToolbar.Listener() {
                @Override
                public void onCommentLibraryClick() {
                    showsCommentsLibrary();
                }

                @Override
                public void onScanCodeClick() {
                    Intent i = new Intent(if_DisplayPhoto.this, QRScannerActivity.class);
                    startActivityForResult(i, Constants.RequestCodes.QR_SCANNER_REQUEST);
                }
            });
            new InputAccessoryHelper(this, this, inputAccessoryToolbar,
                (visible, height) -> {
                    if (!visible && height == 0)
                        viewPagerHeight = viewPager.getHeight();
                });

            keyboardHeightProvider = new KeyboardHeightProvider(this);
            viewPager.post(() -> keyboardHeightProvider.start());

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    @Override
    public void onKeyboardHeightChanged(int height, int orientation) {
        if (height > 0) {
            int bottomOffset = viewPagerHeight - getPhotoWidth() - 16 - 20;
            EditText editText = getPhotoCommentsText();
            if (editText != null) {
                int commentsHeight = (int) (editText.getHeight() * 1.5);
                bottomOffset -= Math.max(commentsHeight, 405);
            }
            int marginTop = -height + bottomOffset;
            ViewUtils.setViewTopMargin(viewPager, marginTop);
        } else {
            ViewUtils.setViewTopMargin(viewPager, 16);
            viewPagerHeight = viewPager.getHeight();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        keyboardHeightProvider.setKeyboardHeightObserver(this);
        reloadPhotos();
        scrollToPhoto(iPhotoID);
    }

    @Override
    protected void onPause() {
        super.onPause();
        keyboardHeightProvider.setKeyboardHeightObserver(null);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        keyboardHeightProvider.close();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        ai_Photo photo = getPhoto();
        if (photo.getFile() != null && CommonHelper.bFileExist(photo.getFile()) && !photo.bUploaded) {
            getMenuInflater().inflate(R.menu.menu_annotation_2nd, menu);
        } else if (photo.iSPhotoID > 0 && (photo.isTaskPhoto() || photo.isUploadedAndUsingWhenEditInspection())) {
            // if the photo is a task photo or has been uploaded when editing inspection
            // then should be able to save the photo comments
            getMenuInflater().inflate(R.menu.menu_save, menu);
        }
        return true;
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        switch (item.getItemId()) {
            case R.id.action_menu_rotation:
                startRotation();
                break;
            case R.id.action_menu_annotation:
                startAnnotation();
                break;
            case R.id.action_menu_save:
                saveCurrentCommentsToServer();
                break;
            default:
                onBackPressed();
                break;
        }
        return true;
    }

    @Override
    public void onBackPressed() {
        keyboardHeightProvider.setKeyboardHeightObserver(null);
        hidesKeyboard();
        super.onBackPressed();
    }

    private void hidesKeyboard() {
        CommonHelper.hideSoftKeyboard(this);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        handleActivityResults(requestCode, resultCode, data);
    }

    private void handleActivityResults(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case ALL_PHOTOS_REQUEST:
                handleDisplayAllPhotosActivities(resultCode, data);
                break;
            case COMMENTS_LIBRARY_REQUEST_CODE:
                handleCommentsLibraryActivities(resultCode, data);
                break;
            case Constants.RequestCodes.QR_SCANNER_REQUEST:
                handleQRScannerActivities(resultCode, data);
                break;
            default: break;
        }
    }

    private void handleDisplayAllPhotosActivities(int resultCode, Intent data) {
        if (resultCode == if_DisplayAllPhotos.SELECT_PHOTO_RESPONSE) {
            handleSelectPhotoResponse(data);
        } else if (resultCode == if_DisplayAllPhotos.DELETE_PHOTO_RESPONSE) {
            onBackPressed();
        }
    }

    private void handleSelectPhotoResponse(Intent data) {
        iPhotoID = (int) data.getLongExtra(Constants.Extras.iPhotoID, 0);
        reloadPhotos();
        scrollToPhoto(iPhotoID);
    }

    private void handleCommentsLibraryActivities(int resultCode, Intent data) {
        if (resultCode == if_CommentsLibrary.COMMENTS_LIBRARY_RESULT_CODE) {
            if (data == null || !data.hasExtra(Constants.Extras.sComments)) return;
            String sComments = data.getStringExtra(Constants.Extras.sComments);
            if (!StringUtils.isEmpty(sComments)) {
                updatePhotoComments(sComments);
            }
        }
    }

    private void updatePhotoComments(String sComments) {
        EditText oEditText = getPhotoCommentsText();
        if (oEditText != null) {
            CommonUI.insertText(oEditText, sComments);
            saveCurrentComments();
            reloadPhotos();
        }
    }

    private void handleQRScannerActivities(int resultCode, Intent data) {
        if (resultCode == Activity.RESULT_OK) {
            String sQRCode = data.getStringExtra(Constants.Extras.sResult);
            if (!StringUtils.isEmpty(sQRCode)) {
                updatePhotoComments(sQRCode);
            }
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            case R.id.btn_Delete_Photo:
                deletePhoto();
                break;
            case R.id.btn_action_all_photos:
                startAllPhotos();
                break;
            default: break;
        }
    }

    private ai_Photo getPhoto() {
        return ai_Photo.findById(ai_Photo.class, iPhotoID);
    }

    private List<ai_Photo> getLsPhoto() {
        if (getPhoto() != null || !StringUtils.isEmpty(sPhotoIds)) {
            return CommonDB_Inspection.GetInsItemPhotos(iInsItemID, sPhotoIds);
        }
        return null;
    }

    private EditText getPhotoCommentsText() {
        if (mPhotoCommentsText == null) {
            View currentPhotoView = getCurrentView(viewPager);
            if (currentPhotoView != null) {
                mPhotoCommentsText = currentPhotoView.findViewById(R.id.edit_PhotoComments);
            }
        }
        return mPhotoCommentsText;
    }

    private View getCurrentView(ViewPager viewPager) {
        try {
            final int currentItem = viewPager.getCurrentItem();
            for (int i = 0; i < viewPager.getChildCount(); i++) {
                final View child = viewPager.getChildAt(i);
                final ViewPager.LayoutParams layoutParams = (ViewPager.LayoutParams) child.getLayoutParams();

                Field f = layoutParams.getClass().getDeclaredField("position"); //NoSuchFieldException
                f.setAccessible(true);
                int position = (Integer) f.get(layoutParams); //IllegalAccessException

                if (!layoutParams.isDecor && currentItem == position) {
                    return child;
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return null;
    }

    private void reloadPhotos() {
        List<ai_Photo> photos = getLsPhoto();
        if (photos == null) return;
        mPhotoCommentsText = null;
        DisplayPhotoAdapter adapter = (DisplayPhotoAdapter) viewPager.getAdapter();
        adapter.setPhotos(photos);
    }

    private void scrollToPhoto(long iPhotoID) {
        List<ai_Photo> lsPhoto = getLsPhoto();
        if (lsPhoto == null) return;
        for (int i= 0; i < lsPhoto.size(); i++) {
            ai_Photo pto = lsPhoto.get(i);
            if (pto.getId() == iPhotoID) {
                viewPager.setCurrentItem(i);
                updateBottomInfo(i);
                break;
            }
        }
    }

    private void updateBottomInfo(int position) {
        List<ai_Photo> photos = getLsPhoto();
        if (photos == null) return;

        TextView textView = findViewById(R.id.tv_bottom_info);
        TextViewUtils.updateText(textView, (position + 1) + " / " + photos.size());
    }

    private void startAnnotation() {
        final ai_Photo photo = getPhoto();
        if (photo == null) return;

        if (photo.iSPhotoID > 0 && photo.bUploaded) {
            Toast.makeText(this, "Can not annotate the photo on the cloud", Toast.LENGTH_LONG).show();
        } else {
            Intent oIntent = new Intent(this, if_ImageAnnotation.class);
            oIntent.putExtra(Constants.Extras.iPhotoID, photo.getId());
            startActivity(oIntent);
        }
    }

    private void startRotation() {
        final ai_Photo photo = getPhoto();
        if (photo == null) return;

        if (photo.iSPhotoID > 0 && photo.bUploaded) {
            Toast.makeText(this, "Can not rotate the photo on the cloud", Toast.LENGTH_LONG).show();
        } else {
            Bitmap photoBitmap = BitmapFactory.decodeFile(photo.getFile());
            if (photoBitmap != null) {
                Bitmap rotated = BitmapUtils.rotateBitmap(photoBitmap, 90);
                CommonHelper.SaveImage(photo.getFile(), rotated);
                CommonHelper.SaveThumb(photo.getThumb(), rotated);
                rotated.recycle();
                photoBitmap.recycle();
            }

            Picasso.get().invalidate(new File(photo.getFile()));
            Picasso.get().invalidate(new File(photo.getThumb()));

            reloadPhotos();
        }
    }

    private void deletePhoto() {
        final ai_Photo photo = getPhoto();
        if (photo == null) return;
        new MaterialDialog.Builder(this)
                .title(R.string.alert_title_attention)
                .content(R.string.delete_photo_confirmation)
                .positiveText(R.string.tv_ok)
                .onPositive((dialog, which) -> {
                    if (bNoticePhoto) { // notice photos
                        Intent intent = new Intent(Constants.Broadcasts.sDeleteNoticePhoto);
                        intent.putExtra(Constants.Extras.iPhotoID, photo.getId());
                        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
                        finish();
                    } else { // ins item photos
                        CommonDB.DeletePhotoByPhotoID(photo.getId().intValue(), photo.iInsItemID, iPosition, this);
                        List<ai_Photo> lsPhoto = getLsPhoto();
                        if (!ArrayUtils.isEmpty(lsPhoto)) {
                            reloadPhotos();
                            updateBottomInfo(viewPager.getCurrentItem());
                        }
                        else finish();
                    }
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    private void startAllPhotos() {
        if (getIntent().getBooleanExtra(bFromAllPhotos, false)) {
            finish();
        } else {
            final ai_Photo photo = getPhoto();
            if (photo == null && getIntent() != null) return;
            Intent intent = if_DisplayAllPhotos.newIntent(this);
            intent.putExtras(getIntent());
            startActivityForResult(intent, ALL_PHOTOS_REQUEST);
        }
    }

    private void showsCommentsLibrary() {
        final ai_Photo photo = getPhoto();
        if (photo == null) return;
        Intent oIntent = new Intent(this, if_CommentsLibrary.class);
        oIntent.putExtra(Constants.Extras.iInsItemID, photo.iInsItemID);
        startActivityForResult(oIntent, COMMENTS_LIBRARY_REQUEST_CODE);
    }

    private void saveCurrentComments() {
        final ai_Photo photo = getPhoto();
        final EditText editText = getPhotoCommentsText();
        if (photo == null || editText == null || photo.sComments.equals(editText.getText().toString())) return;
        photo.sComments = editText.getText().toString();
        if (photo.isUploadedAndUsingWhenEditInspection()) {
            photo.sFieldOne = CommonJson.AddJsonKeyValue(photo.sFieldOne, Constants.Keys.bUpdateComment, "1");
        } else {
            photo.sFieldOne = CommonJson.RemoveJsonKey(photo.sFieldOne, Constants.Keys.bUpdateComment);
        }
        photo.save();
    }

    private void saveCurrentCommentsToServer() {
        // upload to server
        final ai_Photo photo = getPhoto();
        final EditText editText = getPhotoCommentsText();

        if (photo == null || photo.iSPhotoID <= 0 || editText == null) return;

        MaterialDialog dialog = CommonUI.ShowMaterialProgressDialog(this, "Message", "Saving photo comments...");
        CommonTaskKt.savePhotoComment(
            this,
            Dispatchers.getIO(),
            photo.iSPhotoID,
            editText.getText().toString(),
            result -> {
                CommonUI.DismissMaterialProgressDialog(if_DisplayPhoto.this, dialog);
                // save to local
                saveCurrentComments();
                return null;
            }
        );
    }
}
