package com.snapinspect.snapinspect3.activitynew;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import androidx.core.util.Pair;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Adapter.ItemAdapter;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonRequestInspection;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.CommonValidate;
//import com.snapinspect.snapinspect3.Helper.IF_InsRoomHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.IF_Object.ai_Layout;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.app.App;
import com.woxthebox.draglistview.DragItem;
import com.woxthebox.draglistview.DragListView;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Observable;
import java.util.Observer;

/*
 * @Created by osama on 16/06/17.
 */

public class if_OrderInspection extends Activity implements Observer {
    private int iInspectionID;
    private DragListView oListView;
    private List<ai_InsItem> arInsItem;
    private ai_Inspection oInspection;
    ItemAdapter listAdapter;
    private TextView txtTimer;
    App baseApplication;
    private String sType = "";
    private String sPTC = "";
    private List<ai_Layout> lsLayout;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            iInspectionID = getIntent().getIntExtra(Constants.Extras.iInspectionID, 0);
            oInspection = ai_Inspection.findById(ai_Inspection.class, (long) iInspectionID);
            if (oInspection.bLock) {
                Toast.makeText(getBaseContext(), "The Inspection is locked.", Toast.LENGTH_LONG).show();
                finish();
            }
            sPTC = oInspection.sPTC;
            sType = oInspection.sType;
          //  sType = getIntent().getStringExtra(Constants.Extras.sType);
          //  sPTC = getIntent().getStringExtra(Constants.Extras.sPTC);
            getActionBar().setDisplayHomeAsUpEnabled(true);

            getActionBar().setTitle(oInspection.sAddressOne);


            setContentView(R.layout.activity_if_order_inspection);
            getOverflowMenu();

            //tiemr part
            txtTimer = findViewById(R.id.txt_timer);
            baseApplication = (App) getApplication();
            baseApplication.getObserver().addObserver(this);


            oListView = findViewById(R.id.order_inspection_listview);
          /*  oListView.setDragListListener(new DragListView.DragListListener() {
                @Override
                public void onItemDragStarted(int position) {

                }

                @Override
                public void onItemDragging(int itemPosition, float x, float y) {

                }

                @Override
                public void onItemDragEnded(int fromPosition, int toPosition) {
                    if (fromPosition != toPosition) {
                        ArrayList<ai_InsItem> lsInsitem = new ArrayList<ai_InsItem>(arInsItem);

                        if( CommonDB.swapElements(lsInsitem, fromPosition, toPosition) ) {
//                            arInsItem = CommonDB.GetChildInsItem(0, iInspectionID);
//                            ArrayList<Pair<Long, ai_InsItem>> mItemArray = new ArrayList<>();
//                            for (int i = 0; i < arInsItem.size(); i++) {
//                                mItemArray.add(new Pair<>((long) i, arInsItem.get(i)));
//                            }
//                            listAdapter.setItemArray(mItemArray);
                        }

                    }
                }
            });*/

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_inspection.onCreate", ex, this);
        }
    }

    private void getOverflowMenu() {

        try {
            ViewConfiguration config = ViewConfiguration.get(this);
            Field menuKeyField = ViewConfiguration.class.getDeclaredField("sHasPermanentMenuKey");
            if(menuKeyField != null) {
                menuKeyField.setAccessible(true);
                menuKeyField.setBoolean(config, false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_if_orderinspection, menu);

        return true;
    }
    @Override
    public boolean onOptionsItemSelected( MenuItem item )
    {
        try {
            if (item.getItemId() == R.id.action_OrderInspection_AddNewRoom) {
                ShowRoomPopup();
            } else if (item.getItemId() == R.id.action_OrderInspection_Save) {
                onBackPressed();

            }
            else  if (item.getItemId() == android.R.id.home) {
                onBackPressed();
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "ir_OrderInspection.onOptionItemsSelected", ex, this);
        }
        return true;
    }
    private void ShowRoomPopup(){
        //SIDB oDB = new SIDB(this);
        try {
            if (lsLayout == null || lsLayout.size() == 0) {
                if (CommonHelper.bRequestInspection(oInspection.sCustomTwo)){
                    lsLayout = CommonRequestInspection.GetDisplayedLayout(sType, CommonHelper.sRequestInspection_FileName(oInspection.sCustomTwo) );
                }
                else if (CommonValidate.bExternalInspection(oInspection.sCustomTwo)){
                    lsLayout = CommonRequestInspection.GetDisplayedLayout(sType, CommonHelper.sRequestInspection_FileName(oInspection.sCustomTwo) );


                }
                else {
                    lsLayout = CommonDB.GetDisplayedLayout(sType, sPTC, this);
                }
            }
            if (lsLayout != null && lsLayout.size() > 0) {
                String[] options;
                options = new String[lsLayout.size()];
                //options = new String[insItem.size()];
                for (int i = 0; i < lsLayout.size(); i++) {
                    options[i] = lsLayout.get(i).sName;
                }
                new MaterialDialog.Builder(if_OrderInspection.this)
                        .title(R.string.alert_title_action)
                        .items(options)
                        .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                            @Override
                            public boolean onSelection(MaterialDialog dialog, View view, int which, CharSequence text) {
                                SelectRoom(which);
                                return true;
                            }
                        })
                        .negativeText(R.string.md_cancel_label)
                        .show();
            }
            else{
                Toast.makeText(getBaseContext(), "No additional Area can be added to this Inspection.", Toast.LENGTH_LONG).show();
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_inspection.ShowRoomPopup", ex, this);
        }

    }
    private void SelectRoom(int which){
        try {
            ai_Layout oParentLayout = lsLayout.get(which);

            int iCount = 1;
            for (ai_InsItem oTemp : arInsItem){
                if (oTemp.iSLayoutID == oParentLayout.iSLayoutID){
                    iCount++;
                }
            }

            if (sType.equalsIgnoreCase("S")){
                String sDefaultValue1 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVOneConfig);
                String sDefaultValue2 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVTwoConfig);
                String sDefaultValue3 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVThreeConfig);
                String sDefaultValue4 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVFourConfig);
                String sDefaultValue5 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVFiveConfig);
                String sDefaultValue6 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVSixConfig);

                ai_InsItem oInsItem = new ai_InsItem( 0, iInspectionID, oParentLayout.iSLayoutID, oParentLayout.sName + (iCount > 1 ? " " + iCount : ""),
                        sDefaultValue1, sDefaultValue2, sDefaultValue3, sDefaultValue4, sDefaultValue5, sDefaultValue6,
                        oParentLayout.sQType, oParentLayout.sSVOneConfig,
                        oParentLayout.sSVTwoConfig, oParentLayout.sSVThreeConfig, oParentLayout.sSVFourConfig,
                        oParentLayout.sSVFiveConfig, oParentLayout.sSVSixConfig, oParentLayout.sSConfig, false, arInsItem.size()+1, 0, "", "", "");
                oInsItem.save();
                if (oInsItem.sQType.equalsIgnoreCase("S") || oInsItem.sQType.equalsIgnoreCase("V") ){
                    long lPInsItemID = oInsItem.getId();
                    int iPInsItemID = (int)lPInsItemID;
                    List<ai_Layout> lsChildLayout = new ArrayList<ai_Layout>();
                    if (CommonHelper.bRequestInspection(oInspection.sCustomTwo)){
                        lsChildLayout = CommonRequestInspection.LoadLayout(oParentLayout.iSLayoutID, CommonHelper.sRequestInspection_FileName(oInspection.sCustomTwo));
                    }
                    else if (CommonValidate.bExternalInspection(oInspection.sCustomTwo)){
                        lsChildLayout = CommonRequestInspection.LoadLayout(oParentLayout.iSLayoutID, CommonHelper.sRequestInspection_FileName(oInspection.sCustomTwo) );


                    }
                    else{
                        lsChildLayout = CommonDB.GetChildLayout(oParentLayout.iSLayoutID, this);
                    }

                    for (int j=0; j< lsChildLayout.size(); j++){
                        ai_Layout oChildLayout = lsChildLayout.get(j);
                        String sValue1 = oChildLayout.sFVOneConfig == null ||
                                oChildLayout.sFVOneConfig.equalsIgnoreCase("") ?
                                sDefaultValue1 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVOneConfig);
                        String sValue2 = oChildLayout.sFVTwoConfig == null ||
                                oChildLayout.sFVTwoConfig.equalsIgnoreCase("") ?
                                sDefaultValue2 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVTwoConfig);
                        String sValue3 = oChildLayout.sFVThreeConfig == null ||
                                oChildLayout.sFVThreeConfig.equalsIgnoreCase("") ?
                                sDefaultValue3 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVThreeConfig);
                        String sValue4 = oChildLayout.sFVFourConfig == null ||
                                oChildLayout.sFVFourConfig.equalsIgnoreCase("") ?
                                sDefaultValue4 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVFourConfig);
                        String sValue5 = oChildLayout.sFVFiveConfig == null ||
                                oChildLayout.sFVFiveConfig.equalsIgnoreCase("") ?
                                sDefaultValue5 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVFiveConfig);
                        String sValue6 = oChildLayout.sFVSixConfig == null ||
                                oChildLayout.sFVSixConfig.equalsIgnoreCase("") ?
                                sDefaultValue6 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVSixConfig);
                        ai_InsItem oChildInsItem = new ai_InsItem(iPInsItemID, iInspectionID, oChildLayout.iSLayoutID, oChildLayout.sName,
                                sValue1, sValue2, sValue3, sValue4, sValue5, sValue6,
                                oChildLayout.sQType, oChildLayout.sFVOneConfig,
                                oChildLayout.sFVTwoConfig, oChildLayout.sFVThreeConfig, oChildLayout.sFVFourConfig,
                                oChildLayout.sFVFiveConfig, oChildLayout.sFVSixConfig, oChildLayout.sFConfig, false, j + 1, 0, "", "", "");
                        oChildInsItem.save();

                    }
                }
            }
            else{
                String sDefaultValue1 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVOneConfig);
                String sDefaultValue2 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVTwoConfig);
                String sDefaultValue3 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVThreeConfig);
                String sDefaultValue4 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVFourConfig);
                String sDefaultValue5 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVFiveConfig);
                String sDefaultValue6 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVSixConfig);
                ai_InsItem oInsItem = new ai_InsItem( 0, iInspectionID, oParentLayout.iSLayoutID, oParentLayout.sName + (iCount > 1 ? " " + iCount : ""),
                        sDefaultValue1, sDefaultValue2, sDefaultValue3, sDefaultValue4, sDefaultValue5, sDefaultValue6,
                        oParentLayout.sQType, oParentLayout.sFVOneConfig,
                        oParentLayout.sFVTwoConfig, oParentLayout.sFVThreeConfig, oParentLayout.sFVFourConfig,
                        oParentLayout.sFVFiveConfig, oParentLayout.sFVSixConfig, oParentLayout.sFConfig, false, arInsItem.size()+1, 0, "", "", "");
                oInsItem.save();
                // long lPInsItemID = oInsItem.getId();
                // int iPInsItemID = (int)lPInsItemID;
                List<ai_Layout> lsChildLayout = new ArrayList<ai_Layout>();
                if (CommonHelper.bRequestInspection(oInspection.sCustomTwo)){
                    lsChildLayout = CommonRequestInspection.LoadLayout(oParentLayout.iSLayoutID, CommonHelper.sRequestInspection_FileName(oInspection.sCustomTwo));
                }
                else if (CommonValidate.bExternalInspection(oInspection.sCustomTwo)){
                    lsChildLayout = CommonRequestInspection.LoadLayout(oParentLayout.iSLayoutID, CommonHelper.sRequestInspection_FileName(oInspection.sCustomTwo) );


                }
                else{
                    lsChildLayout = CommonDB.GetChildLayout(oParentLayout.iSLayoutID, this);
                }


                CommonDB.AddChildLayout(oInsItem, if_OrderInspection.this, oInsItem.iInsID, lsChildLayout);
                /*for (int j=0; j< lsChildLayout.size(); j++){
                    ai_Layout oChildLayout = lsChildLayout.get(j);
                    String sValue1 = oChildLayout.sFVOneConfig == null ||
                            oChildLayout.sFVOneConfig.equalsIgnoreCase("") ?
                            sDefaultValue1 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVOneConfig);
                    String sValue2 = oChildLayout.sFVTwoConfig == null ||
                            oChildLayout.sFVTwoConfig.equalsIgnoreCase("") ?
                            sDefaultValue2 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVTwoConfig);
                    String sValue3 = oChildLayout.sFVThreeConfig == null ||
                            oChildLayout.sFVThreeConfig.equalsIgnoreCase("") ?
                            sDefaultValue3 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVThreeConfig);
                    String sValue4 = oChildLayout.sFVFourConfig == null ||
                            oChildLayout.sFVFourConfig.equalsIgnoreCase("") ?
                            sDefaultValue4 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVFourConfig);
                    String sValue5 = oChildLayout.sFVFiveConfig == null ||
                            oChildLayout.sFVFiveConfig.equalsIgnoreCase("") ?
                            sDefaultValue5 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVFiveConfig);
                    String sValue6 = oChildLayout.sFVSixConfig == null ||
                            oChildLayout.sFVSixConfig.equalsIgnoreCase("") ?
                            sDefaultValue6 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVSixConfig);
                    ai_InsItem oChildInsItem = new ai_InsItem(iPInsItemID, iInspectionID, oChildLayout.iSLayoutID, oChildLayout.sName,
                            sValue1, sValue2, sValue3, sValue4, sValue5, sValue6,
                            oChildLayout.sQType, oChildLayout.sFVOneConfig,
                            oChildLayout.sFVTwoConfig, oChildLayout.sFVThreeConfig, oChildLayout.sFVFourConfig,
                            oChildLayout.sFVFiveConfig, oChildLayout.sFVSixConfig, oChildLayout.sFConfig, false, j + 1, 0, "", "", "");
                    oChildInsItem.save();

                }*/
            }
            EditInsItem();
        }
        catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_inspection.SelectRoom", ex, this);
        }
    }
    @Override
    public void onBackPressed()
    {
        // code here to show dialog
        super.onBackPressed();  // optional depending on your needs
        List<Pair<Long, ai_InsItem>> mItemArray = listAdapter.getItemList();
        int iSort = 1;
        for (Pair<Long, ai_InsItem> oTemp : mItemArray){
           // long ii = oTemp.first;
            ai_InsItem insItem  =oTemp.second;
            insItem.iSort = iSort;
            iSort++;

            insItem.save();
        }
        InputMethodManager inputManager =
                (InputMethodManager) if_OrderInspection.this.
                        getSystemService(Context.INPUT_METHOD_SERVICE);

        inputManager.hideSoftInputFromWindow(
                (oListView).getApplicationWindowToken(),
                InputMethodManager.HIDE_NOT_ALWAYS);

    }
    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);

    }
    @Override
    public void onResume(){
        super.onResume();
        CommonDB.InsertLog(if_OrderInspection.this, "Event", "Inspection View " + oInspection.sAddressOne + " - " + oInspection.sInsTitle + " - " + oInspection.dtStartDate);
        EditInsItem();

    }
    public void EditInsItem(){
        try {
            arInsItem = CommonDB.GetChildInsItem(0, iInspectionID);
            ArrayList<Pair<Long, ai_InsItem>> mItemArray = new ArrayList<>();
            for (int i = 0; i < arInsItem.size(); i++) {
                mItemArray.add(new Pair<>((long) i+1, arInsItem.get(i)));
            }
            oListView.setLayoutManager(new LinearLayoutManager(this));
            if (CommonUI.bBelowLollipop()) {
                listAdapter = new ItemAdapter(mItemArray, R.layout.cell_inspection_edit_low, R.id.order_handler, false, this);
            } else {
                listAdapter = new ItemAdapter(mItemArray, R.layout.cell_inspection_edit, R.id.order_handler, false, this);
            }

            oListView.setAdapter(listAdapter, true);
            oListView.setCanDragHorizontally(false);
            oListView.setCustomDragItem(new MyDragItem(this, R.layout.cell_inspection_edit));
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_inspection.EditInsItem", ex, this);
        }
    }

    public void updateInsItem() {
        arInsItem = CommonDB.GetChildInsItem(0, iInspectionID);
        ArrayList<Pair<Long, ai_InsItem>> mItemArray = new ArrayList<>();
        for (int i = 0; i < arInsItem.size(); i++) {
            mItemArray.add(new Pair<>((long) i, arInsItem.get(i)));
        }
        listAdapter.setItemArray(mItemArray);
    }
    /*@Override
    public boolean onOptionsItemSelected( MenuItem item )
    {
        try {

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_inspection.onOptionItemsSelected", ex, this);
        }
        return true;
    }*/

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void update(Observable observable, Object data) {
        txtTimer.setText(baseApplication.getObserver().getValue());
    }

    private static class MyDragItem extends DragItem {

        MyDragItem(Context context, int layoutId) {
            super(context, layoutId);
        }

        @Override
        public void onBindDragView(View clickedView, View dragView) {
            CharSequence text = ((EditText) clickedView.findViewById(R.id.txt_RoomName)).getText();
            ((EditText) dragView.findViewById(R.id.txt_RoomName)).setText(text);
        }
    }


}
