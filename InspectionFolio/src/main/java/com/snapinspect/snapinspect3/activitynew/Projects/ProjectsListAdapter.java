package com.snapinspect.snapinspect3.activitynew.Projects;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.IF_Object.ai_Project;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.TextViewUtils;
import com.softw4re.views.InfiniteListAdapter;

import java.util.ArrayList;

public class ProjectsListAdapter extends InfiniteListAdapter<ai_Project> {

    public interface Listener {
        void onRefresh();
        void didSelectProject(ai_Project project);
    }

    private static class ViewHolder {
        TextView tvTitle;
        TextView tvRef;
        TextView tvManager;
        TextView tvProgress;
        ViewHolder(View view) {
            tvTitle = view.findViewById(R.id.tv_title);
            tvRef = view.findViewById(R.id.tv_ref);
            tvManager = view.findViewById(R.id.tv_manager);
            tvProgress = view.findViewById(R.id.tv_progress);
        }
    }

    private Listener mListener;
    private final LayoutInflater mInflater;

    public ProjectsListAdapter(
            Activity activity, int itemLayoutRes, ArrayList<ai_Project> itemList) {
        super(activity, itemLayoutRes, itemList);
        mInflater = LayoutInflater.from(activity);
    }

    public void setListener(Listener listener) {
        mListener = listener;
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        ViewHolder mViewHolder;
        if (convertView == null){
            convertView = mInflater.inflate(R.layout.cell_project_list, null);
            mViewHolder = new ViewHolder(convertView);
            convertView.setTag(mViewHolder);
        } else {
            mViewHolder = (ViewHolder) convertView.getTag();
        }

        TextViewUtils.updateText(mViewHolder.tvTitle, getItem(position).sName);
        TextViewUtils.updateText(mViewHolder.tvRef,
                getContext().getString(R.string.title_project_ref, getItem(position).sReference));
        TextViewUtils.updateText(mViewHolder.tvManager,
                CommonJson.getPropertyInspector(getItem(position).iManagerID));

        int iCompleted = getItem(position).iCompletedIns, iTotal = getItem(position).iTotalIns;
        TextViewUtils.updateText(mViewHolder.tvProgress,
                getContext().getString(R.string.title_project_progress, iCompleted, iTotal));
        int backgroundResId = CommonUI.getProjectProgressBackgroundRes(iCompleted, iTotal);
        mViewHolder.tvProgress.setBackground(
                ContextCompat.getDrawable(getContext(), backgroundResId));
        return convertView;
    }

    @Override
    public void onNewLoadRequired() {

    }

    @Override
    public void onRefresh() {
        if (mListener != null) mListener.onRefresh();
    }

    @Override
    public void onItemClick(int i) {
        if (mListener != null) mListener.didSelectProject(getItem(i));
    }

    @Override
    public void onItemLongClick(int i) {

    }
}
