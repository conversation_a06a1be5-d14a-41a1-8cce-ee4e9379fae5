package com.snapinspect.snapinspect3.activitynew.Photo;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.GridView;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Adapter.AllPhotosAdapter;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonDB_Inspection;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class if_DisplayAllPhotos extends Activity {
    public static final int SELECT_PHOTO_RESPONSE = 1;
    public static final int DELETE_PHOTO_RESPONSE = 2;

    private static final int columnCount = 3;
    private static final int columnSpacing = 10;

    private Menu mOptionsMenu;
    private boolean mIsSelectingPhoto = false;
    private TextView tvBottom;
    private ImageButton btnDelPhotos;
    private GridView gridView;

    private List<ai_Photo> mAllPhotos = new ArrayList<>();
    private final ArrayList<ai_Photo> mSelectedPhotos = new ArrayList<>();

    public static Intent newIntent(Context context, long iInsItemID, String sPhotoURL, boolean bNoticePhotos) {
        Intent intent = new Intent(context, if_DisplayAllPhotos.class);
        intent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        intent.putExtra(Constants.Extras.sPhotoIds, sPhotoURL);
        intent.putExtra(Constants.Extras.bNoticePhotos, bNoticePhotos);
        return intent;
    }

    public static Intent newIntent(Context context) {
        return new Intent(context, if_DisplayAllPhotos.class);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            setContentView(R.layout.activity_display_all_photos);
            getActionBar().setDisplayHomeAsUpEnabled(true);

            gridView = findViewById(R.id.grid_view);
            gridView.setNumColumns(columnCount);
            int width = (CommonUI.getScreenWidth() - CommonUI.dpToPx(columnSpacing * (1 + columnCount ), this)) / 3;
            AllPhotosAdapter photosAdapter = new AllPhotosAdapter(mAllPhotos, this);
            photosAdapter.updateColumnSize(width, width);
            gridView.setAdapter(photosAdapter);
            gridView.setOnItemClickListener((parent, view, position, id) -> {
                ai_Photo selected = (ai_Photo) parent.getItemAtPosition(position);
                if (selected == null) return;
                if (mIsSelectingPhoto) {
                    if (mSelectedPhotos.contains(selected)) mSelectedPhotos.remove(selected);
                    else mSelectedPhotos.add(selected);
                    reloadSelectedPhotos();
                    updateBottomInfo();
                } else {
                    if (getCallingActivity() != null) {
                        Intent intent = new Intent();
                        intent.putExtra(Constants.Extras.iPhotoID, selected.getId());
                        setResult(SELECT_PHOTO_RESPONSE, intent);
                        finish();
                    } else {
                        Intent oIntent = if_DisplayPhoto.newIntent(if_DisplayAllPhotos.this, selected.getId());
                        oIntent.putExtras(if_DisplayAllPhotos.this.getIntent());
                        oIntent.putExtra(Constants.Extras.bFromAllPhotos, true);
                        if_DisplayAllPhotos.this.startActivity(oIntent);
                    }
                }
            });

            tvBottom = findViewById(R.id.tv_bottom_info);
            btnDelPhotos = findViewById(R.id.btn_Delete_Photo);
            btnDelPhotos.setOnClickListener(v -> deletePhotoFiles());
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        reloadPhotos();
        updateBottomInfo();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_select_or_cancel, menu);
        mOptionsMenu = menu;
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (mOptionsMenu == null) return false;
        int[] itemResIds = {R.id.action_menu_cancel, R.id.action_menu_select};
        for (int itemId : itemResIds) {
            mOptionsMenu.findItem(itemId).setVisible(false);
        }
        if (item.getItemId() == R.id.action_menu_select) {
            mOptionsMenu.findItem(R.id.action_menu_cancel).setVisible(true);
            setIsSelectingPhoto(true);
        } else if (item.getItemId() == R.id.action_menu_cancel) {
            mOptionsMenu.findItem(R.id.action_menu_select).setVisible(true);
            setIsSelectingPhoto(false);
        } else {
            onBackPressed();
        }
        return super.onOptionsItemSelected(item);
    }

    private void setIsSelectingPhoto(boolean select) {
        mIsSelectingPhoto = select;
        mSelectedPhotos.clear();
        reloadSelectedPhotos();
        updateBottomInfo();
    }

    private void reloadSelectedPhotos() {
        AllPhotosAdapter adapter = (AllPhotosAdapter) gridView.getAdapter();
        adapter.updateSelectedPhotos(mSelectedPhotos);
    }

    private void reloadPhotos() {
        long iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, 0);
        String sPhotoIds = getIntent().getStringExtra(Constants.Extras.sPhotoIds);

        if (StringUtils.isEmpty(sPhotoIds)) return;

        mAllPhotos = CommonDB_Inspection.GetInsItemPhotos(iInsItemID, sPhotoIds);
        AllPhotosAdapter adapter = (AllPhotosAdapter) gridView.getAdapter();
        adapter.updateData(mAllPhotos);
    }

    @SuppressLint("DefaultLocale")
    private void updateBottomInfo() {
        btnDelPhotos.setVisibility(mIsSelectingPhoto ? View.VISIBLE : View.GONE);
        String text;
        if (mIsSelectingPhoto) {
            int size = mSelectedPhotos.size();
            if (size > 0) {
                btnDelPhotos.setEnabled(true);
                btnDelPhotos.setImageResource(R.drawable.icon_trash_can_red);
                text = getResources().getQuantityString(R.plurals.selected_photos, size, size);
            } else {
                btnDelPhotos.setEnabled(false);
                btnDelPhotos.setImageResource(R.drawable.icon_trash_can_disabled);
                text = "Select photos";
            }
        } else {
            int size = mAllPhotos.size();
            text = getResources().getQuantityString(R.plurals.total_photos, size, size);
        }

        tvBottom.setText(text);
    }

    private void deletePhotoFiles() {
        int size = mSelectedPhotos.size();
        String sMessage = getResources().getQuantityString(R.plurals.these_photos, size, size);
        new MaterialDialog.Builder(this)
                .title(R.string.alert_title_attention)
                .content("Are you sure to delete " + sMessage + "?")
                .positiveText(R.string.tv_ok)
                .onPositive((dialog, which) -> {
                    int iPosition = getIntent().getIntExtra(Constants.Extras.iPosition, 0);
                    boolean bNoticePhotos = getIntent().getBooleanExtra(Constants.Extras.bNoticePhotos, false);
                    for (ai_Photo photo: mSelectedPhotos) {
                        int iPhotoID = photo.getId().intValue();
                        if (bNoticePhotos) {
                            Intent intent = new Intent(Constants.Broadcasts.sDeleteNoticePhoto);
                            intent.putExtra(Constants.Extras.iPhotoID, iPhotoID);
                            LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
                        } else {
                            CommonDB.DeletePhotoByPhotoID(iPhotoID, photo.iInsID, iPosition, this);
                        }
                    }
                    setResult(DELETE_PHOTO_RESPONSE, new Intent());
                    finish();
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }
}
