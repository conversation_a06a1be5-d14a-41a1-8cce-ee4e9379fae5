package com.snapinspect.snapinspect3.activitynew.Projects;

import android.app.Activity;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.*;
import androidx.annotation.ColorRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.IF_Object.ai_Project;
import com.snapinspect.snapinspect3.IF_Object.v_ProjectInspection;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.TextViewUtils;
import com.snapinspect.snapinspect3.views.CircularTextView;
import com.softw4re.views.InfiniteListAdapter;

import java.util.ArrayList;

public class ProjectInspectionsListAdapter extends InfiniteListAdapter<Object> {

    private static class ProjectInspectionViewHolder {
        TextView tvInspector;
        TextView tvTitle;
        TextView tvDetails;
        ImageView ivIndicator;
        LinearLayout viewIndicator;
        LinearLayout mainLayer;
        TextView tvNoInspection;
        RelativeLayout viewUpload;
        RelativeLayout viewExpand;
        CircularTextView statusText;

        ProjectInspectionViewHolder(View view) {
            tvTitle = view.findViewById(R.id.list_exist_asset_child_layout_title);
            tvDetails = view.findViewById(R.id.list_exist_asset_child_layout_details);
            tvInspector = view.findViewById(R.id.list_exist_asset_child_layout_inspector);
            ivIndicator = view.findViewById(R.id.list_exist_asset_child_layout_expand_iv);
            ivIndicator.setImageResource(R.drawable.collpase);
            viewIndicator = view.findViewById(R.id.view_inspection_status);
            mainLayer = view.findViewById(R.id.layer_asset);
            tvNoInspection = view.findViewById(R.id.tv_no_inspection);

            viewExpand = view.findViewById(R.id.list_exist_asset_child_layout_btn_expand);
            viewUpload = view.findViewById(R.id.list_exist_asset_child_layout_btn_expand_upload);
            statusText = view.findViewById(R.id.list_exist_asset_child_layout_expand_iv_textview);
        }
    }

    private static class ProjectViewHolder {
        TextView tvTitle;
        TextView tvRef;
        TextView tvManager;
        TextView tvProgress;

        ProjectViewHolder(View view) {
            tvTitle = view.findViewById(R.id.tv_title);
            tvRef = view.findViewById(R.id.tv_ref);
            tvManager = view.findViewById(R.id.tv_manager);
            tvProgress = view.findViewById(R.id.tv_progress);
        }
    }

    interface Listener {
        void didSelectProjectInspection(v_ProjectInspection projectInspection);
        void onRefresh();
        void uploadProjectInspection(v_ProjectInspection projectInspection);
    }

    private Listener mListener;
    private final LayoutInflater mInflater;

    public ProjectInspectionsListAdapter(Activity activity, int itemLayoutRes, ArrayList<Object> itemList) {
        super(activity, itemLayoutRes, itemList);
        mInflater = LayoutInflater.from(activity);
    }

    @NonNull
    @Override
    public View getView(int position, @Nullable View convertView, @NonNull ViewGroup parent) {
        Object item = getItem(position);
        if (item instanceof ai_Project) {
            ProjectViewHolder mViewHolder;
            if (convertView == null || !(convertView.getTag() instanceof ProjectViewHolder)) {
                convertView = mInflater.inflate(R.layout.cell_project_inspection_overview, parent, false);
                mViewHolder = new ProjectViewHolder(convertView);
                convertView.setTag(mViewHolder);
            } else {
                mViewHolder = (ProjectViewHolder) convertView.getTag();
            }
            ai_Project project = (ai_Project)item;
            TextViewUtils.updateText(mViewHolder.tvTitle, project.sName);
            TextViewUtils.updateText(mViewHolder.tvRef,
                    getContext().getString(R.string.title_project_ref, project.sReference));
            TextViewUtils.updateText(mViewHolder.tvManager,
                    CommonJson.getPropertyInspector(project.iManagerID));

            int iCompleted = project.iCompletedIns, iTotal = project.iTotalIns;
            TextViewUtils.updateText(mViewHolder.tvProgress,
                    getContext().getString(R.string.title_project_progress, iCompleted, iTotal));
            int backgroundResId = CommonUI.getProjectProgressBackgroundRes(iCompleted, iTotal);
            mViewHolder.tvProgress.setBackground(
                    ContextCompat.getDrawable(getContext(), backgroundResId));
        } else if (item instanceof v_ProjectInspection) {
            v_ProjectInspection projectInspection = (v_ProjectInspection) item;
            String sInsStart = projectInspection.getInsStartDate() == null ? "" :
                    DateUtils.format(projectInspection.getInsStartDate(), "MMM dd, yyyy HH:mm");
            String sInsEnd = projectInspection.getInsEndDate() == null ? "" :
                    DateUtils.format(projectInspection.getInsEndDate(), "MMM dd, yyyy HH:mm");

            ProjectInspectionViewHolder mViewHolder;
            if (convertView == null || !(convertView.getTag() instanceof ProjectInspectionViewHolder)) {
                convertView = mInflater.inflate(R.layout.list_exist_asset_child_layout, parent, false);
                mViewHolder = new ProjectInspectionViewHolder(convertView);
                convertView.setTag(mViewHolder);
            } else {
                mViewHolder = (ProjectInspectionViewHolder) convertView.getTag();
            }

            @ColorRes int dotColorRes;
            if (projectInspection.isCompletedNotUploaded() || projectInspection.isCompletedAndContinue()) {
                dotColorRes = R.color.blue_color;

                mViewHolder.mainLayer.setVisibility(View.VISIBLE);
                mViewHolder.tvNoInspection.setVisibility(View.GONE);

                mViewHolder.viewExpand.setVisibility(View.GONE);
                mViewHolder.viewUpload.setVisibility(View.VISIBLE);
                mViewHolder.viewUpload.setOnClickListener(view -> {
                    if (mListener != null) mListener.uploadProjectInspection(projectInspection);
                });

                TextViewUtils.updateText(mViewHolder.tvDetails,
                        (StringUtils.isEmpty(sInsStart) && StringUtils.isEmpty(sInsEnd) ?
                                projectInspection.sInsTypeTitle :
                                (projectInspection.sInsTitle + " " +
                                        StringUtils.ifEmpty(sInsStart, "?") + " - " +
                                        StringUtils.ifEmpty(sInsEnd, "?"))));

            } else if (projectInspection.isUploaded()) { // completed in server
                dotColorRes = R.color.green_color;

                mViewHolder.mainLayer.setVisibility(View.VISIBLE);
                mViewHolder.tvNoInspection.setVisibility(View.GONE);
                mViewHolder.ivIndicator.setVisibility(View.GONE);
                String sStatus = projectInspection.getInspectionStatus();
                if (!StringUtils.isEmpty(sStatus) && !sStatus.trim().isEmpty()) {
                    mViewHolder.ivIndicator.setVisibility(View.INVISIBLE);
                    sStatus = sStatus.trim();
                    String sStatusCode = projectInspection.getInspectionStatusCode();
                    String sColor = StringUtils.isEmpty(sStatusCode) || sStatusCode.trim().isEmpty() ? "#CCCCCC" : sStatusCode;
                    mViewHolder.statusText.setVisibility(View.VISIBLE);
                    mViewHolder.statusText.setText(CommonHelper.GetStatusCode(sStatus));
                    mViewHolder.statusText.setSolidColor(sColor.trim());
                    mViewHolder.statusText.setTextColor(Color.WHITE);
                }

                TextViewUtils.updateText(mViewHolder.tvDetails,
                        StringUtils.ifEmpty(projectInspection.sInsTitle, projectInspection.sInsTypeTitle));
                TextViewUtils.updateText(mViewHolder.tvInspector,
                        projectInspection.getInspector() == null ? "" :
                                "Inspector - " + projectInspection.getInspector().sName);
            } else {
                TextViewUtils.updateText(mViewHolder.tvInspector,
                        projectInspection.getAssignedInspector() == null ? "" :
                                "Inspector - " + projectInspection.getAssignedInspector().sName);

                dotColorRes = R.color.red_color;
                mViewHolder.mainLayer.setVisibility(View.VISIBLE);
                mViewHolder.tvNoInspection.setVisibility(View.GONE);
                if (projectInspection.iInspectionID > 0) {
                    mViewHolder.ivIndicator.setImageResource(R.drawable.icon_pencil);
                    TextViewUtils.updateText(mViewHolder.tvDetails,
                            (StringUtils.isEmpty(sInsStart) && StringUtils.isEmpty(sInsEnd) ?
                                    projectInspection.sInsTypeTitle :
                                    (projectInspection.sInsTitle + " " +
                                            StringUtils.ifEmpty(sInsStart, "?") + " - " +
                                            StringUtils.ifEmpty(sInsEnd, "?"))));
                } else {
                    mViewHolder.ivIndicator.setImageResource(R.drawable.collpase);
                    TextViewUtils.updateText(mViewHolder.tvDetails,
                            StringUtils.ifEmpty(projectInspection.sInsTitle, projectInspection.sInsTypeTitle));

                }
            }

            GradientDrawable dot = new GradientDrawable();
            dot.setColor(ContextCompat.getColor(getContext(), dotColorRes));
            dot.setCornerRadius(CommonHelper.pxFromDp(getContext(), 3));
            mViewHolder.viewIndicator.setBackground(dot);

            TextViewUtils.updateText(mViewHolder.tvTitle, projectInspection.getInspectionTitle());
        }

        return convertView;
    }

    @Override
    public void onNewLoadRequired() {

    }

    @Override
    public void onRefresh() {
        if (mListener == null) return;
        mListener.onRefresh();
    }

    @Override
    public void onItemClick(int i) {
        if (mListener != null && getItem(i) instanceof v_ProjectInspection) {
            mListener.didSelectProjectInspection((v_ProjectInspection) getItem(i));
        }
    }

    @Override
    public void onItemLongClick(int i) {

    }

    public void setListener(Listener listener) {
        mListener = listener;
    }
}
