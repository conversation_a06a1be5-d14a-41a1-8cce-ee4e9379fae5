package com.snapinspect.snapinspect3.activitynew.inspection3rd;

import android.content.Context;
import android.content.Intent;
import android.location.Location;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.MenuItem;
import android.widget.Button;
import android.widget.ListView;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Adapter.InsLayoutAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_AssetLayout;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsType;
import com.snapinspect.snapinspect3.IF_Object.ai_Layout;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.List;

public class if_Layout_3rd extends FragmentActivity {

    /// Extra keys :
    /// (Constants.Extras.iSAssetID, oSchedule.iSAssetID);
    /// (Constants.Extras.iSInsTypeID, oInsType.iSInsTypeID);
    /// (Constants.Extras.sAddress1, oSchedule.sAddress1);
    /// (Constants.Extras.sAddress2, oSchedule.sAddress2);
    /// (Constants.Extras.iSScheduleID, oSchedule.iSScheduleID);
    /// (Constants.Extras.sScheduleCustom1, oSchedule.sCustom1);

    private int iSAssetID;
    private int iSInsTypeID;
    private int iSScheduleID;
    private String sAddress1;
    private String sAddress2;
    private String sScheduleCustom1;
    private String sDtSchedule;
    private ai_InsType oInsType;
    private List<ai_Layout> lsLayout;

    public static Intent newIntent(
            Context context, int iSAssetID, int iSInsTypeID,
            String sAddress1, String sAddress2, int iSScheduleID,
            String sScheduleCustom1, String sDtSchedule) {
        Intent intent = new Intent(context, if_Layout_3rd.class);
        intent.putExtra(Constants.Extras.iSAssetID, iSAssetID);
        intent.putExtra(Constants.Extras.iSInsTypeID, iSInsTypeID);
        intent.putExtra(Constants.Extras.sAddress1, sAddress1);
        intent.putExtra(Constants.Extras.sAddress2, sAddress2);
        intent.putExtra(Constants.Extras.iSScheduleID, iSScheduleID);
        intent.putExtra(Constants.Extras.sScheduleCustom1, sScheduleCustom1);
        intent.putExtra(Constants.Extras.S_DT_SCHEDULE, sDtSchedule);
        return intent;
    }

    public static Intent newIntent(
            Context context, int iSAssetID, int iSInsTypeID,
            String sAddress1, String sAddress2) {
        return newIntent(context, iSAssetID, iSInsTypeID, sAddress1, sAddress2, 0, "", "");
    }

    public static Intent newIntent(
            Context context, int iSAssetID, int iSInsTypeID,
            String sAddress1, String sAddress2, int iSProjectInspectionID) {
        Intent intent = new Intent(context, if_Layout_3rd.class);
        intent.putExtra(Constants.Extras.iSAssetID, iSAssetID);
        intent.putExtra(Constants.Extras.iSInsTypeID, iSInsTypeID);
        intent.putExtra(Constants.Extras.sAddress1, sAddress1);
        intent.putExtra(Constants.Extras.sAddress2, sAddress2);
        intent.putExtra(Constants.Extras.iSProjectInspectionID, iSProjectInspectionID);
        return intent;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_layout_3rd);

        try {
          //  CommonHelper.trackEvent(this, "Android Layout Setup", null);
            getActionBar().setDisplayHomeAsUpEnabled(true);

            iSAssetID = getIntent().getIntExtra(Constants.Extras.iSAssetID, 0);
            iSInsTypeID = getIntent().getIntExtra(Constants.Extras.iSInsTypeID, 0);
            sAddress1 = getIntent().getStringExtra(Constants.Extras.sAddress1);
            sAddress2 = getIntent().getStringExtra(Constants.Extras.sAddress2);
            iSScheduleID = getIntent().getIntExtra(Constants.Extras.iSScheduleID, 0);
            sScheduleCustom1 = getIntent().getStringExtra(Constants.Extras.sScheduleCustom1);
            sDtSchedule = getIntent().getStringExtra(Constants.Extras.S_DT_SCHEDULE);

            List<ai_Layout> allLayouts = new ArrayList<>();
            if (!StringUtils.isEmpty(sScheduleCustom1) && CommonValidate.bExternalInspection(sScheduleCustom1)) {
                String sFilePath = CommonHelper.sRequestInspection_FileName(sScheduleCustom1);
                oInsType = CommonRequestInspection.LoadInsType(sFilePath);
                allLayouts = CommonRequestInspection.GetDisplayedLayout(oInsType.sType, sFilePath);
            } else {
                List<ai_InsType> lsInsType = ai_InsType.find(ai_InsType.class, "i_S_Ins_Type_ID = ?", "" + iSInsTypeID);
                if (null != lsInsType && lsInsType.size() > 0) {
                    oInsType = lsInsType.get(0);
                    allLayouts = CommonDB.GetDisplayedLayout(oInsType.sType, oInsType.sPTC, this);
                }
            }

            // bypass layout
            if (allLayouts != null && allLayouts.size() > 0) {
                lsLayout = CommonDB_Inspection.bypassLayouts(allLayouts, oInsType);
            }

            if (lsLayout != null && lsLayout.size() > 0) {
                for (ai_Layout oLayout : lsLayout) {
                    oLayout.sFieldOne = String.valueOf(oLayout.getDefaultNumOfAreas(this, iSAssetID));
                }

                InsLayoutAdapter layoutAdapter = new InsLayoutAdapter(lsLayout, this);
                ListView lvLayouts = findViewById(R.id.lv_Layout);
                lvLayouts.setAdapter(layoutAdapter);
            }

            Button btnStartIns = findViewById(R.id.btn_start_inspection);
            btnStartIns.setOnClickListener(v -> startInspection());

            // request location permission
            CommonValidate.requestPermissions(this, Constants.LOCATION_PERMISSIONS);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Layout_3rd.onCreate", ex, this);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        GeoLocationManager.getInstance(this).startUpdateLocation();
        CommonDB.InsertLog(this, "Event", "Layout Setup View - InsTypeID: " + oInsType.iSInsTypeID + " - " + sAddress1);
    }

    @Override
    protected void onPause() {
        super.onPause();
        GeoLocationManager.getInstance(this).stopUpdateLocation();
    }

    @Override
    public boolean onMenuItemSelected(int featureId, MenuItem item) {
        onBackPressed();
        return super.onMenuItemSelected(featureId, item);
    }

    private void startInspection() {
        if (!CommonUI.bAppPermission(this)) return;

        if (!canStartInspection()) {
            CommonUI.ShowAlert(this, getString(R.string.title_alert_error), getString(R.string.message_pls_choose_one), R.string.btn_title_got_it);
            return;
        }

        int iSProjectAssetInsTypeID = getIntent().getIntExtra(Constants.Extras.iSProjectInspectionID, 0);
        new AsyncStartInspection(
                this, iSAssetID, lsLayout, oInsType, sScheduleCustom1,
                iSScheduleID, sAddress1, sAddress2, iSProjectAssetInsTypeID, sDtSchedule
            ).execute();
    }

    private boolean canStartInspection() {
        for (ai_Layout oLayout : lsLayout) {
            if (CommonHelper.getInt(oLayout.sFieldOne) > 0) return true;
        }
        return false;
    }

    public static final class AsyncStartInspection extends AsyncTask<Void, Void, Integer> {
        private final WeakReference<Context> mContextReference;
        private final int iSAssetID;
        private final ai_InsType oInsType;
        private final int iSScheduleID;
        private final String sAddress1;
        private final String sAddress2;
        private MaterialDialog progressDialog;
        private final List<ai_Layout> lsLayout;
        private final String sScheduleCustom1;
        private final int iSProjectAssetInsTypeID;
        private final String sDtSchedule;

        public AsyncStartInspection(
                Context context,
                int iSAssetID,
                List<ai_Layout> lsLayout,
                ai_InsType oInsType,
                String sScheduleCustom1,
                int iSScheduleID,
                String sAddress1,
                String sAddress2,
                int iSProjectAssetInsTypeID,
                String sDtSchedule
        ) {
            mContextReference = new WeakReference<>(context);
            this.lsLayout = lsLayout;
            this.sScheduleCustom1 = sScheduleCustom1;
            this.iSAssetID = iSAssetID;
            this.oInsType = oInsType;
            this.iSScheduleID = iSScheduleID;
            this.sAddress1 = sAddress1;
            this.sAddress2 = sAddress2;
            this.iSProjectAssetInsTypeID = iSProjectAssetInsTypeID;
            this.sDtSchedule = sDtSchedule;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            progressDialog = CommonUI.ShowMaterialProgressDialog(mContextReference.get(), "", "Processing...");
        }

        @Override
        protected Integer doInBackground(Void... voids) {
            int iInspectionID = 0;
            try {
                ArrayList<ai_AssetLayout> lsAssetLayout = new ArrayList<>();
                for (int i = 0; i < lsLayout.size(); i++) {
                    String sNumber = lsLayout.get(i).sFieldOne;
                    int iNumber = CommonHelper.getInt(sNumber);
                    ai_Layout oLayout = lsLayout.get(i);

                    List<ai_Layout> lsChildLayout;
                    if (sScheduleCustom1 != null && CommonValidate.bExternalInspection(sScheduleCustom1))
                        lsChildLayout = CommonRequestInspection.LoadLayout(oLayout.iSLayoutID, CommonHelper.sRequestInspection_FileName(sScheduleCustom1));
                    else
                        lsChildLayout = ai_Layout.find(ai_Layout.class, "I_SP_LAYOUT_ID = ? ORDER BY ID ASC", "" + oLayout.iSLayoutID);

                    JSONArray arChild = new JSONArray();
                    for (int j = 0; j < lsChildLayout.size(); j++) {
                        JSONObject oTemp = new JSONObject();
                        oTemp.put("i", lsChildLayout.get(j).iSLayoutID);
                        oTemp.put("n", lsChildLayout.get(j).sName);
                        arChild.put(oTemp);
                    }
                    for (int j = 0; j < iNumber; j++) {
                        ai_AssetLayout oAssetLayout = new ai_AssetLayout();
                        oAssetLayout.iSAssetID = iSAssetID;
                        oAssetLayout.iSLayoutID = oLayout.iSLayoutID;
                        oAssetLayout.sMoreItems = "";
                        oAssetLayout.sChildID = arChild.toString();
                        oAssetLayout.sName = j == 0 ? oLayout.sName : (oLayout.sName + " " + (j + 1));
                        //oAssetLayout.iSort = lsLayout.get(i).
                        //oAssetLayout.sPTC = oInsType.sPTC;
                        lsAssetLayout.add(oAssetLayout);
                    }
                }

                if (sScheduleCustom1 != null && CommonValidate.bExternalInspection(sScheduleCustom1)) {
                    IF_CreateInspection_ReqIns oCreateInspection = new IF_CreateInspection_ReqIns();
                    iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                            mContextReference.get(), lsAssetLayout, oInsType, iSAssetID, "",
                            CommonHelper.sDateToString(null), "",
                            sAddress1, sAddress2, iSScheduleID, sScheduleCustom1,
                            CommonHelper.sRequestInspection_FileName(sScheduleCustom1), sDtSchedule
                    );
                    CommonHelper.ScheduleAppendCustomOne(iSScheduleID, "iInsID", "" + iInspectionID);
                    oCreateInspection.LoadInspectionDefaultOrder(iInspectionID,CommonHelper.sRequestInspection_FileName(sScheduleCustom1) );
                } else {
                    IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                    iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                            mContextReference.get(), lsAssetLayout, oInsType, iSAssetID, "",
                            CommonHelper.sDateToString(null), "",
                            sAddress1, sAddress2, iSScheduleID, sDtSchedule
                    );
                    // Assign inspection to project inspection
                    if (iSProjectAssetInsTypeID > 0)  {
                        CommonProjectInspection.startInspection(mContextReference.get(), iSProjectAssetInsTypeID, iInspectionID);
                    }
                    CommonHelper.ScheduleAppendCustomOne(iSScheduleID, "iInsID", "" + iInspectionID);
                    // Load default order only when there is no layout
                    if (lsAssetLayout.isEmpty()) {
                        oCreateInspection.LoadInspectionDefaultOrder(iInspectionID);
                    }
                }
            } catch(Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "if_Layout_3rd.doInBackground", ex, mContextReference.get());
            }

            return iInspectionID;
        }

        @Override
        protected void onPostExecute(Integer iInspectionID) {
            super.onPostExecute(iInspectionID);
            CommonUI.DismissMaterialProgressDialog(progressDialog);
            if (iInspectionID > 0)
                CommonSchedule.goToInspection(mContextReference.get(), iInspectionID);
        }
    }
}
