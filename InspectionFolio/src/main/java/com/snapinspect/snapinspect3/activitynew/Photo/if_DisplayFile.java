package com.snapinspect.snapinspect3.activitynew.Photo;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.annotation.Nullable;

import android.view.MenuItem;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;

import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.Helper.ViewUtils;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_File;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.if_existasset;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.squareup.picasso.Callback;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.RequestCreator;

import java.io.File;

public class if_DisplayFile extends Activity {

    private ImageView photoView;
    private ProgressBar progressBar;
    private ai_File oFile = null;
    private String sFileURL = null;

    public static Intent newIntent(Context mContext, String url) {
        Intent intent = new Intent(mContext, if_DisplayFile.class);
        intent.putExtra(Constants.Extras.sURL, url);
        return intent;
    }

    public static Intent newIntent(if_existasset mContext, long iFileID) {
        Intent intent = new Intent(mContext, if_DisplayFile.class);
        intent.putExtra(Constants.Extras.iFileID, iFileID);
        return intent;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            setContentView(R.layout.activity_display_image_file);
            getActionBar().setDisplayHomeAsUpEnabled(true);

            photoView = findViewById(R.id.img_photo_view);
            progressBar = findViewById(R.id.progress_bar);

            int photoWidth = (int) (CommonUI.getScreenWidth() * 0.9);
            ViewUtils.setViewSize(photoView, photoWidth, photoWidth);

            if (getIntent().hasExtra(Constants.Extras.iFileID)) {
                long iFileID = getIntent().getLongExtra(Constants.Extras.iFileID, 0);
                oFile = ai_File.findById(ai_File.class, iFileID);
            } else if (getIntent().hasExtra(Constants.Extras.sURL)) {
                sFileURL = getIntent().getStringExtra(Constants.Extras.sURL);
            }

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadPhotoFile();
    }

    @Override
    public boolean onMenuItemSelected(int featureId, MenuItem item) {
        onBackPressed();
        return super.onMenuItemSelected(featureId, item);
    }

    private void loadPhotoFile() {
        try {
            RequestCreator request = null;
            if (oFile != null)
                request = Picasso.get().load(new File(oFile.sFile));
            else if (!StringUtils.isEmpty(sFileURL))
                request = Picasso.get().load(sFileURL);

            if (request == null) return;
            progressBar.setVisibility(View.VISIBLE);
            request.into(photoView, new Callback() {
                @Override
                public void onSuccess() {
                    progressBar.setVisibility(View.GONE);
                }

                @Override
                public void onError(Exception e) {
                    progressBar.setVisibility(View.GONE);
                    Drawable placeholder = if_DisplayFile.this.getResources().getDrawable(R.drawable.image_placeholder_full);
                    photoView.setImageDrawable(placeholder);
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

}
