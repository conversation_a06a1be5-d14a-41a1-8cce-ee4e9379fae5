package com.snapinspect.snapinspect3.activitynew.products

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.net.Uri
import android.os.Bundle
import android.util.Size
import android.view.MenuItem
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import com.snapinspect.snapinspect3.Helper.CommonHelper
import com.snapinspect.snapinspect3.Helper.Constants
import com.snapinspect.snapinspect3.Helper.IF_RestClient
import com.snapinspect.snapinspect3.IF_Object.ai_Product
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.SI_DB.db_Product
import com.snapinspect.snapinspect3.views.composables.ImagePreviewView
import com.snapinspect.snapinspect3.views.composables.RemoteImage
import com.snapinspect.snapinspect3.views.composables.getActionBarSize
import com.snapinspect.snapinspect3.util.NumberUtils
import com.snapinspect.snapinspect3.util.StringUtils
import com.snapinspect.snapinspect3.views.composables.SnapTheme
import java.io.File

private const val IMAGE_URL_PATH = "/IOAPI/GetProductPhoto"

fun ai_Product.getImageFileUri(): Uri? {
    val fileName = sImage.takeIf { it.isNotEmpty() }?.let { Uri.parse(it).lastPathSegment }
    return fileName?.let {
        val sFile = "${Constants.Paths.PRODUCT_PHOTOS}/$it"
        CommonHelper.sFileRoot?.let { rootDir -> File(rootDir, sFile) }?.let { file -> Uri.fromFile(file) }
    }
}

fun ai_Product.getImageUri(context: Context): Uri? {
    if (sImage == null || sImage.isBlank()) return null
    getImageFileUri()?.takeIf { it.path != null && File(it.path!!).exists() }?.let { return it }
    ensureProductPhotoFolderExists()
    return Uri.parse(IF_RestClient.getAbsoluteUrl(IMAGE_URL_PATH)).buildUpon().apply {
        appendQueryParameter("iProductID", iSProductID.toString())
        appendQueryParameter("iCustomerID", CommonHelper.getCurrentUserID(context).toString())
        appendQueryParameter("sToken", CommonHelper.getUserToken(context))
    }.build()
}

private fun ensureProductPhotoFolderExists() {
    CommonHelper.sFileRoot?.let {
        val productPhotoDir = File(it, Constants.Paths.PRODUCT_PHOTOS)
        if (!productPhotoDir.exists()) {
            productPhotoDir.mkdirs()
        }
    }
}

class if_ProductDetails : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setTheme(R.style.RemoveShadowActionBar)
        actionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            show()
        }
        val productId = intent.getLongExtra(Constants.Extras.I_PRODUCT_ID, 0)
        val product = db_Product.getProductByID(productId)
        actionBar?.title = product.sName

        setContent { 
            SnapTheme { ProductDetailsView(product) }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }
}

@Composable
private fun ProductDetailsView(product: ai_Product) {
    var showPreview by remember { mutableStateOf(false) }
    val context = LocalContext.current
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(colorResource(id = R.color.colorPrimary))
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = getActionBarSize())
                .background(Color.White)
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(5.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.Top
            ) {
                Column {
                    DetailItem("Name:", product.sName)
                    DetailItem("SKU:", product.sSKU)
                    DetailItem("Model:", product.sModel)
                    DetailItem(
                        "Cost:",
                        "${NumberUtils.formatCurrencyWithoutGrouping(product.dUnitCost)} / " +
                                product.sUnitName.ifBlank { Constants.Values.kUnitName }
                    )
                }

                RemoteImage(
                    imageUrl = product.getImageUri(context)?.toString(),
                    size = Size(100, 100),
                    localFilePath = product.getImageFileUri(),
                    modifier = Modifier
                        .clickable { showPreview = true }
                )
            }

            // Associated Items
            if (product.lsAssociatedItems().isNotEmpty()) {
                AssociatedItemTagsView(product.lsAssociatedItems())
            }
            
            // Product Details
            DetailItem("Editable:", if (product.bAllowEdit) "Yes" else "No", 74.dp)
            DetailItem("One-Time:", if (product.bOneOffCost) "Yes" else "No", 74.dp)

            // Description
            product.sDesp?.takeIf { it.isNotBlank() }?.let {
                Text(
                    text = it,
                    fontSize = dimensionResource(id = R.dimen.text_normal).value.sp,
                    color = colorResource(id = R.color.light_gray)
                )
            }
            // URL
            StringUtils.ensureHttpsScheme(product.sURL)?.let { url ->
                Text(
                    text = product.sURL,
                    color = colorResource(id = R.color.colorPrimary),
                    fontSize = dimensionResource(id = R.dimen.text_h5).value.sp,
                    modifier = Modifier.fillMaxWidth().clickable {
                        ContextCompat.startActivity(context, Intent(Intent.ACTION_VIEW, Uri.parse(url)), null)
                    }
                )
            }
        }
        if (showPreview) {
            ImagePreviewView(product.getImageUri(context)?.toString()) {
                showPreview = false
            }
        }
    }
}

@Composable
fun DetailItem(label: String, value: String, widthDp: Dp = 56.dp) {
    Row(modifier = Modifier.padding(vertical = 4.dp)) {
        Text(
            text = label,
            modifier = Modifier.width(widthDp),
            fontWeight = FontWeight.Normal,
            fontSize = 16.sp,
            color = colorResource(id = R.color.light_gray)
        )
        Spacer(modifier = Modifier.width(8.dp))
        Text(
            text = value.ifBlank { Constants.Values.kNoValue },
            fontWeight = FontWeight.Normal,
            fontSize = 16.sp,
            color = colorResource(id = R.color.dark_night)
        )
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AssociatedItemTagsView(tags: List<String>) {
    FlowRow(
        modifier = Modifier
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Surface(
            modifier = Modifier.height(32.dp).width(56.dp),
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = "Items: ",
                    fontWeight = FontWeight.Normal,
                    fontSize = 16.sp,
                    color = colorResource(id = R.color.light_gray)
                )
            }
        }

        tags.forEach { ItemTagChip(it) }
    }
}

@Composable
private fun ItemTagChip(text: String) {
    Surface(
        modifier = Modifier.height(32.dp),
        shape = RoundedCornerShape(8.dp),
        color = colorResource(id = R.color.gray_color)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Text(
                text = text,
                color = colorResource(id = R.color.dark_gray),
                style = MaterialTheme.typography.body2,
                modifier = Modifier.padding(end = 4.dp),
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
            )
        }
    }
}

@Preview(name = "Light Mode",
    uiMode = Configuration.UI_MODE_NIGHT_NO,
    showBackground = true)
@Composable
fun PreviewProductDetailsViewWithLightMode() {
    SnapTheme {
        ProductDetailsView(
            product = ai_Product().apply {
                iSProductID = 1
                sName = "n0801007"
                sSKU = "SUD8362929"
                sModel = "m0801007"
                dUnitCost = 60.0
                sUnitName = "Unit"
                sURL = "www.google.com"
                sImage = "dummy.jpg"
                sAssociateItem = "[\"Item 1\", \"Item 2\", \"Item 3\", \"Item 4\", \"Item 5\"," +
                        " \"Item 6\", \"Item 7\", \"Item 8\", \"Item 9\", \"Item 10\"," +
                        " \"Long long long long long long Long long long Long long long Long long long  \"]"
            }
        )
    }
}