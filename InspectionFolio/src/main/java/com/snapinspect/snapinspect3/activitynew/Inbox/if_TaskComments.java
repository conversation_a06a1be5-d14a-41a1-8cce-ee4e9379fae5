package com.snapinspect.snapinspect3.activitynew.Inbox;

import android.app.Activity;
import android.os.Bundle;
import android.view.MenuItem;
import androidx.annotation.Nullable;
import com.snapinspect.snapinspect3.R;

public class if_TaskComments extends Activity {
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_task_comments);
        getActionBar().setDisplayHomeAsUpEnabled(true);
        getActionBar().setTitle(R.string.title_activity_if_task_comments);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        onBackPressed();
        return super.onOptionsItemSelected(item);
    }
}
