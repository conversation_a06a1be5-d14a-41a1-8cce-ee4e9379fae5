package com.snapinspect.snapinspect3.activitynew.inspection3rd;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.*;
import androidx.annotation.Nullable;
import com.afollestad.materialdialogs.MaterialDialog;
import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Adapter.InsAreaAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.activity.if_NoticeList;
import com.snapinspect.snapinspect3.util.InsDurationManager;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.snapinspect.snapinspect3.activity.frag_inspections.INSPECTION_COMPLETED;

public class if_Ins_3rd extends Activity implements AdapterView.OnItemClickListener, InsDurationManager.Delegate {

    public static final int FRAG_INSPECTION_REQUEST = 1;

    private int insId = 0;
    private String sType, sPTC;
    private ai_Inspection oInspection;

    private ListView lvAreas;
    private TextView tvInsDuration;
    private InsDurationManager mDurationManager;
    private boolean needRestartTimer = false;

    public static Intent newIntent(Context context, int insId, String sType, String sPTC) {
        Intent intent = new Intent(context, if_Ins_3rd.class);
        intent.putExtra(Constants.Extras.iInspectionID, insId);
        intent.putExtra(Constants.Extras.sType, sType);
        intent.putExtra(Constants.Extras.sPTC, sPTC);
        return intent;
    }

    public static Intent newIntent(Context context, int iInspectionID) {
        return newIntent(context, iInspectionID, "", "");
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_ins_3rd);

        if (!CommonValidate.Permission_Validate(if_Ins_3rd.this)){
            onBackPressed();
        }
        try {
            insId = getIntent().getIntExtra(Constants.Extras.iInspectionID, 0);
            oInspection = ai_Inspection.findById(ai_Inspection.class, insId);
            if (oInspection.bLock) onBackPressed();
            else {
                sType = getIntent().getStringExtra(Constants.Extras.sType);
                sPTC = getIntent().getStringExtra(Constants.Extras.sPTC);
                if (StringUtils.isEmpty(sType) || StringUtils.isEmpty(sPTC)) {
                    sType = oInspection.sType;
                    sPTC = oInspection.sPTC;
                }

                getActionBar().setDisplayHomeAsUpEnabled(true);
                getActionBar().setTitle(oInspection.sAddressOne);

                tvInsDuration = findViewById(R.id.tv_inspection_time);
                ImageButton btnAction = findViewById(R.id.btn_Inspection_Action);
                btnAction.setOnClickListener(v -> onBtnActionPressed());

                lvAreas = findViewById(R.id.lv_Inspections_areas);
                lvAreas.setAdapter(new InsAreaAdapter(new ArrayList<>(), if_Ins_3rd.this));
                lvAreas.setOnItemClickListener(this);

                getDurationManager().start();

                if (CommonHelper.isKioskMode(this)) {
                    Toast.makeText(this, "Please remember to press 'Complete' button on the top right corner once the inspection has been completed.", Toast.LENGTH_LONG).show();
                }
            }
        } catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Ins_3rd.onCreate", ex, this);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        CommonDB.InsertLog(if_Ins_3rd.this, "Event", "Inspection View " + oInspection.sAddressOne + " - " + oInspection.sInsTitle + " - " + oInspection.dtStartDate);
        displayInspectionAreas();
        displayCompleteItems();

        if (needRestartTimer) {
            getDurationManager().start();
            needRestartTimer = false;
        }
    }

    @Override
    protected void onPause() {
        getDurationManager().saveInsTime();
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        getDurationManager().stop();
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        getDurationManager().stop();
        super.onBackPressed();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (!oInspection.bComplete) {
            getMenuInflater().inflate(R.menu.menu_ins_complete, menu);
        }
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onMenuItemSelected(int featureId, MenuItem item) {
        if (item.getItemId() == R.id.action_complete_ins) { // Complete
            completeInspection();
        } else { // Back
            onBackPressed();
        }
        return super.onMenuItemSelected(featureId, item);
    }

    @Override
    public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
        ai_InsItem oInsItem = (ai_InsItem) lvAreas.getAdapter().getItem(position);
        int iInsItemId = oInsItem.getId().intValue();

        startActivity(if_Ins_items_3rd.newIntent(if_Ins_3rd.this, iInsItemId, oInspection.sAddressOne,
                oInsItem.iInsID, CommonValidate.bExternalInspection(oInspection.sCustomTwo)));

        getDurationManager().stop();
        needRestartTimer = true;
    }

    @Override
    public void updateInsDuration(String duration) {
        if (CommonHelper.GetPreferenceBoolean(this, Constants.Settings.bDisplayInspectionDuration)) {
            tvInsDuration.setText(duration);
        }
    }

    private void displayInspectionAreas() {
        List<ai_InsItem> arrInsItem = CommonDB.GetChildInsItem(0, insId);
        if (arrInsItem != null && !arrInsItem.isEmpty()) {
            boolean shouldBypassCompulsory = oInspection.shouldBypassCompulsory();
            for (ai_InsItem insItem: arrInsItem) {
                insItem.bInspectionByPassCompulsory = shouldBypassCompulsory;
            }
            InsAreaAdapter adaptor = (InsAreaAdapter) lvAreas.getAdapter();
            adaptor.setInsItems(arrInsItem);
        }
    }

    private void displayCompleteItems() {
        List<ai_InsItem> arrInsItem = CommonDB.GetChildInsItem(0, insId);
        if (arrInsItem == null || arrInsItem.isEmpty()) return;

        int count = 0;
        for (ai_InsItem item: arrInsItem) {
            if (item.bCompleted) count ++;
        }
        String[] strings = new String[] {
            count + "", getString(R.string.items_out_of) , arrInsItem.size() + ""
        };

        TextView textView = findViewById(R.id.tv_bottom_info);
        textView.setText(String.join(" ", strings));
    }

    private boolean isKioskMode() {
        return CommonHelper.isKioskMode(if_Ins_3rd.this);
    }

    private void onBtnActionPressed() {
        View actionView = LayoutInflater.from(this).inflate(R.layout.list_inspection_actions, null);
        final MaterialDialog dialog = new MaterialDialog.Builder(this)
                .customView(actionView, false)
                .show();

        // Close
        actionView.findViewById(R.id.btn_action_close).setOnClickListener(v -> dialog.dismiss());
        // View Tasks
        actionView.findViewById(R.id.btn_action_view_tasks).setOnClickListener(v -> {
            if_Ins_3rd.this.startActivity(if_NoticeList.newIntent(if_Ins_3rd.this, insId, 0));
            dialog.dismiss();
        });
        // Edit Areas
        actionView.findViewById(R.id.btn_action_edit_ins).setOnClickListener(v -> {
            editInspection();
            dialog.dismiss();
        });
        // Delete Inspection
        actionView.findViewById(R.id.btn_action_delete_ins).setOnClickListener(v -> {
            cancelInspection();
            dialog.dismiss();
        });
    }

    private void completeInspection() {
        try {
            if (oInspection.hasNotCompletedCompulsoryItems()) {
                CommonUI.ShowAlert(this, "Incompleted Inspection", "The inspection can not be uploaded until all compulsory questions are answered. Please follow ! mark to complete inspection.");
                List<ai_InsItem> arrInsItem = CommonDB.GetChildInsItem(0, insId);
                if (arrInsItem == null || arrInsItem.isEmpty()) return;
                for (ai_InsItem insItem: arrInsItem) db_InsItem.markNeedValidateCompulsoryConfig(insItem);
                displayInspectionAreas();
            } else {
                Date oDate = new Date();
                oInspection.dtEndDate = CommonHelper.sDateToString(oDate);
                oInspection.bComplete = true;
                oInspection.save();
                if (oInspection.iSScheduleID > 0) {
                    CommonHelper.ScheduleAppendCustomOne(oInspection.iSScheduleID, "iInsIDCom", "" + oInspection.getId());
                }
                CommonDB.InsertLog(if_Ins_3rd.this, "Ins Action", "Completed Inspection - " + oInspection.sAddressOne + ", " + oInspection.iSInsTypeID + ", " + oInspection.dtEndDate);

                if (isKioskMode()) {
                    new MaterialDialog.Builder(this)
                            .title("Inspection Completed")
                            .content("To upload this inspection, Please click 'Upload' button under 'Inspection' tab")
                            .positiveText(R.string.tv_ok)
                            .onPositive((dialog, which) -> markAsCompletedAndBack())
                            .show();
                } else {
                    markAsCompletedAndBack();
                }
            }
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Ins_3rd.completeInspection", ex, this);
        }
    }

    private void markAsCompletedAndBack() {
        setResult(INSPECTION_COMPLETED, new Intent());
        onBackPressed();
    }

    private void editInspection() {
        try {
            Intent intent = new Intent(this, if_Ins_Edit_3rd.class);
            intent.putExtra(Constants.Extras.iInspectionID, insId);
            //  intent.putExtra(Constants.Extras.bRequestInspection, CommonValidate.bExternalInspection(oInspection.sCustomTwo));
            startActivity(intent);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Ins_3rd.editInspection", ex, this);
        }
    }

    private void cancelInspection() {
        if (CommonValidate.bExternalInspection(oInspection.sCustomTwo)) {
            Toast.makeText(if_Ins_3rd.this, "The request inspection can not be deleted.", Toast.LENGTH_LONG).show();
        } else {
            new MaterialDialog.Builder(this)
                    .title("Message")
                    .content(R.string.lbl_DeleteInspectionPrompt)
                    .positiveText("Yes")
                    .negativeText("Cancel")
                    .onPositive((dialog, which) -> {
                        CommonInspection.DeleteInspection(oInspection, dialog.getContext());
                        onBackPressed();
                    })
                    .onNegative((dialog, which) -> dialog.dismiss())
                    .show();
        }
    }

    private InsDurationManager getDurationManager() {
        if (mDurationManager == null)
            mDurationManager = new InsDurationManager(this, insId, this);
        return mDurationManager;
    }
}

