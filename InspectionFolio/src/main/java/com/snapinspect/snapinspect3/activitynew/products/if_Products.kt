package com.snapinspect.snapinspect3.activitynew.products

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.util.Size
import android.view.MenuItem
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Icon
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Close
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import com.snapinspect.snapinspect3.Helper.CommonDB
import com.snapinspect.snapinspect3.Helper.CommonHelper
import com.snapinspect.snapinspect3.Helper.Constants
import com.snapinspect.snapinspect3.Helper.SharedConfig
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem
import com.snapinspect.snapinspect3.IF_Object.ai_Layout
import com.snapinspect.snapinspect3.IF_Object.ai_Product
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.SI_DB.db_InsItem
import com.snapinspect.snapinspect3.SI_DB.db_Product
import com.snapinspect.snapinspect3.activitynew.fragments.ItemSelectionDialog
import com.snapinspect.snapinspect3.activitynew.fragments.LayoutSelection
import com.snapinspect.snapinspect3.util.NumberUtils
import com.snapinspect.snapinspect3.views.composables.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update

class if_Products : ComponentActivity() {

    private val viewModel: ProductsViewModel by viewModels {
        ProductsViewModel.Factory(
            productDataProvider = db_Product::getProductsByInsItemTitles,
        )
    }

    private val aiInsItem: ai_InsItem?
        get() {
            val iInsItemID = intent.getIntExtra(Constants.Extras.iInsItemID, 0)
            return db_InsItem.GetInsItem_ByID(iInsItemID.toLong())
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setTheme(R.style.RemoveShadowActionBar)
        actionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            show()
            setTitle(R.string.title_products)
        }

        // Add the layout of the ins item where this activity was opened from
        if (CommonHelper.GetPreferenceBoolean(this, Constants.Keys.PRODUCT_AUTO_LOAD_ITEM)) {
            aiInsItem?.let {
                CommonDB.getLayoutBySLayoutID(it.iSLayoutID)?.let { layout ->
                    // Create a simple ai_Layout for compatibility
                    val aiLayout = ai_Layout(layout.iSLayoutID ?: 0, layout.sName ?: "")
                    viewModel.addSelectedLayout(aiLayout)
                }
            }
        }

        setContent {
            SnapTheme {
                ProductsView(
                    viewModel,
                    onProductSelected = ::didSelectProduct,
                    onAddTag = ::didAddTag
                )
            }
        }
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        return when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
    }

    private fun didSelectProduct(selectedProduct: ai_Product, layouts: List<ai_Layout>) {
        CommonHelper.SavePreferenceBoolean(this, Constants.Keys.PRODUCT_AUTO_LOAD_ITEM, layouts.isNotEmpty())

        val intent = Intent().apply {
            putExtra(Constants.Extras.I_PRODUCT_ID, selectedProduct.id)
        }
        setResult(Constants.ResultCodes.ADD_PRODUCT_RESULT, intent)
        finish()
    }

    private fun didAddTag() {
        val dialog = ItemSelectionDialog(
            context = this,
            getString(R.string.title_choose_layout_item),
            items = CommonDB.searchChildLayouts(aiInsItem?.iInsID ?: 0)
                .map { ai_Layout(it.iSLayoutID ?: 0, it.sName ?: "") }
                .map(LayoutSelection.Companion::fromLayout),
            recentIds = SharedConfig.getInstance(this)::getRecentLayoutIds,
            onItemSelected = { item ->
                CommonDB.getLayoutBySLayoutID(item.id)?.let {
                    val aiLayout = ai_Layout(it.iSLayoutID ?: 0, it.sName ?: "")
                    viewModel.addSelectedLayout(aiLayout)
                    SharedConfig.getInstance(this).saveLayoutID(item.id)
                }
            }
        )
        dialog.show()
    }
}

data class ProductsUiState(
    val products: List<ai_Product> = emptyList(),
    val selectedLayouts: List<ai_Layout> = emptyList(),
    val searchQuery: String = "",
    val groupedProducts: Map<Char, List<ai_Product>> = emptyMap(),
    val selectedProduct: ai_Product? = null,
)

class ProductsViewModel(
    private val productDataProvider: (List<String>, String) -> List<ai_Product>,
) : ViewModel() {
    private val _uiState = MutableStateFlow(ProductsUiState())
    val products: Flow<List<ai_Product>> = _uiState.map { it.products }
    val selectedLayouts: Flow<List<ai_Layout>> = _uiState.map { it.selectedLayouts }
    val searchQuery: Flow<String> = _uiState.map { it.searchQuery }
    val groupedProducts: Flow<Map<Char, List<ai_Product>>> = _uiState.map { it.groupedProducts }
    val selectedProduct: Flow<ai_Product?> = _uiState.map { it.selectedProduct }

    init {
        loadProducts()
    }

    private fun loadProducts() {
        _uiState.value = _uiState.value.copy(
            products = productDataProvider(
                _uiState.value.selectedLayouts.map { it.sName },
                _uiState.value.searchQuery
            )
        )
        updateGroupedProducts()
    }

    fun updateSearchQuery(query: String) {
        _uiState.value = _uiState.value.copy(searchQuery = query)
        loadProducts()
    }

    fun addSelectedLayout(layout: ai_Layout) {
        _uiState.update {
            val updatedLayouts = it.selectedLayouts.toMutableList().apply {
                if (!contains(layout)) add(layout)
            }
            it.copy(selectedLayouts = updatedLayouts)
        }
        loadProducts()
    }

    fun removeSelectedLayout(layout: ai_Layout) {
        _uiState.update {
            val updatedLayouts = it.selectedLayouts.toMutableList().apply {
                remove(layout)
            }
            it.copy(selectedLayouts = updatedLayouts)
        }
        loadProducts()
    }

    fun selectProduct(product: ai_Product) {
        _uiState.value = _uiState.value.copy(selectedProduct = product)
    }

    private fun updateGroupedProducts() {
        val _products = _uiState.value.products
        _uiState.value = _uiState.value.copy(
            groupedProducts = _products.groupBy { it.sName.first().uppercaseChar() }
                .toSortedMap()
        )
    }

    class Factory(
        private val productDataProvider: (List<String>, String) -> List<ai_Product>
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(ProductsViewModel::class.java)) {
                @Suppress("UNCHECKED_CAST")
                return ProductsViewModel(productDataProvider) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }
}

@Composable
fun ProductsView(viewModel: ProductsViewModel, onProductSelected: (ai_Product, List<ai_Layout>) -> Unit, onAddTag: () -> Unit) {
    val groupedProducts by viewModel.groupedProducts.collectAsState(emptyMap())
    val searchQuery by viewModel.searchQuery.collectAsState("")
    val selectedProduct by viewModel.selectedProduct.collectAsState(null)
    val selectedLayouts by viewModel.selectedLayouts.collectAsState(emptyList())

    val focusManager = LocalFocusManager.current

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(colorResource(id = R.color.colorPrimary))
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = getActionBarSize())
                .background(Color.White)
        ) {
            SearchBar(
                value = searchQuery,
                onValueChange = { viewModel.updateSearchQuery(it) }
            )

            TagsView(
                tags = selectedLayouts.map { it.sName },
                onAddTag = onAddTag,
                onTagRemoved = { viewModel.removeSelectedLayout(selectedLayouts[it]) }
            )

            if (groupedProducts.isEmpty()) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "No products found",
                        textAlign = TextAlign.Center,
                        color = colorResource(R.color.dark_input_text_color),
                        fontSize = dimensionResource(R.dimen.text_h5).value.sp
                    )
                }
            } else {
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(Color.White)
                        .clickableWithoutRipple { focusManager.clearFocus() }
                ) {
                    groupedProducts.forEach { (initial, productsForInitial) ->
                        item {
                            SectionHeader(initial.toString())
                        }
                        items(productsForInitial) { product ->
                            ProductItem(
                                product = product,
                                isSelected = product == selectedProduct,
                                onProductSelected = {
                                    viewModel.selectProduct(it)
                                    onProductSelected(it, selectedLayouts)
                                }
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun SectionHeader(initial: String) {
    Text(
        text = initial,
        modifier = Modifier
            .fillMaxWidth()
            .background(colorResource(R.color.gray_color))
            .padding(vertical = 8.dp, horizontal = 16.dp),
        fontWeight = FontWeight.Bold
    )
}

@Composable
fun ProductItem(
    product: ai_Product,
    isSelected: Boolean,
    onProductSelected: (ai_Product) -> Unit
) {
    val context = LocalContext.current
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(if (isSelected) Color.LightGray else Color.White)
            .clickable { onProductSelected(product) }
            .padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        RemoteImage(
            imageUrl = product.getImageUri(context)?.toString(),
            size = Size(60, 60),
            localFilePath = product.getImageFileUri()
        )

        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(6.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    product.sName,
                    color = colorResource(id = R.color.dark_input_text_color),
                    fontSize = dimensionResource(id = R.dimen.text_h5).value.sp,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    product.sSKU,
                    color = colorResource(id = R.color.light_gray),
                    fontWeight = FontWeight.Normal
                )
            }
            Text(
                text = "${NumberUtils.formatCurrencyWithoutGrouping(product.dUnitCost)} / " +
                        "${product.sUnitName.let { Constants.Values.kUnitName }} ",
                color = colorResource(id = R.color.dark_input_text_color),
            )
            product.sModel?.let {
                if (it.isNotBlank()) {
                    Text(
                        text = "Model: $it",
                        color = colorResource(id = R.color.dark_input_text_color)
                    )
                }
            }

            // description
            product.sDesp?.let {
                if (it.isNotBlank()) {
                    Text(
                        text = it,
                        fontSize = dimensionResource(id = R.dimen.text_normal).value.sp,
                        color = colorResource(id = R.color.light_gray),
                        maxLines = 2
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun TagsView(
    tags: List<String>,
    onAddTag: () -> Unit = {},
    onTagRemoved: (Int) -> Unit
) {
    FlowRow(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 8.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        tags.forEach { tag ->
            TagChip(
                text = tag,
                onRemove = { onTagRemoved(tags.indexOf(tag)) }
            )
        }

        addTagButton(onAddTag)
    }
}

@Composable
fun addTagButton(didTap: () -> Unit = {}) {
    Surface(
        modifier = Modifier.height(32.dp)
            .clickable(onClick = didTap),
        shape = RoundedCornerShape(8.dp),
        border = BorderStroke(1.dp, colorResource(id = R.color.colorPrimary)),
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = Icons.Filled.Add,
                contentDescription = "Add",
                tint = colorResource(id = R.color.colorPrimary),
                modifier = Modifier.size(16.dp)
            )
            Text(
                text = stringResource(id = R.string.action_new),
                color = colorResource(id = R.color.colorPrimary),
                style = MaterialTheme.typography.body2,
                modifier = Modifier.padding(end = 4.dp)
            )
        }
    }
}

@Composable
fun TagChip(
    text: String,
    onRemove: () -> Unit
) {
    Surface(
        modifier = Modifier.height(32.dp),
        shape = RoundedCornerShape(8.dp),
        color = colorResource(id = R.color.gray_color)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            modifier = Modifier.padding(horizontal = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(4.dp)
        ) {
            Icon(
                imageVector = Icons.Filled.Close,
                contentDescription = "Remove tag",
                tint = colorResource(id = R.color.dark_gray),
                modifier = Modifier
                    .size(16.dp)
                    .clickable(onClick = onRemove)
            )
            Text(
                text = text,
                color = colorResource(id = R.color.dark_gray),
                style = MaterialTheme.typography.body2,
                modifier = Modifier.padding(end = 4.dp)
            )
        }
    }
}

@Preview(name = "Light Mode", uiMode = Configuration.UI_MODE_NIGHT_NO, showBackground = true)
@Composable
fun PreviewProductsViewWithLightMode() {
    val mockDataProvider: (List<String>, String) -> List<ai_Product> = { _, _ ->
        listOf(
            ai_Product().apply {
                iSProductID = 1
                sName = "fggad"
                sSKU = "gggad"
                dUnitCost = 1.0
                sModel = "m0801007"
                sURL = "www.google.com"
                sDesp = "This is a description"
            },
            ai_Product().apply {
                iSProductID = 2
                sName = "n0801007"
                sSKU = "SUD8362929"
                sModel = "m0801007"
                dUnitCost = 60.0
                sURL = "www.google.com"
            },
        )
    }

    val previewViewModel = ProductsViewModel(mockDataProvider)
    SnapTheme {
        ProductsView(previewViewModel, onProductSelected = { _, _ -> }, onAddTag = {})
    }
}