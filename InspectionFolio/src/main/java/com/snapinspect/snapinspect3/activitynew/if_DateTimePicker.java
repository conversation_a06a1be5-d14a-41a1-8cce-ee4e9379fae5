package com.snapinspect.snapinspect3.activitynew;

import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.views.DateTimePickerDialog;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Locale;
import java.util.Objects;

public class if_DateTimePicker extends AppCompatActivity
        implements DateTimePickerDialog.OnDateTimeSetListener {

    private static final String DATE_PATTERN = "yyyy-MM-dd";

    private ai_InsItem oInsItem;
    private int iPosition;
    private SimpleDateFormat dateFormat;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.layout_date_time_picker);

        iPosition = getIntent().getIntExtra(Constants.Extras.iPosition, 0);
        int iInsItemID = getIntent().getIntExtra(Constants.Extras.iInsItemID, 0);
        oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);

        String pattern = getIntent().getStringExtra(Constants.Extras.sDatePattern);
        if (StringUtils.isEmpty(pattern)) pattern = DATE_PATTERN;
        try {
            dateFormat = new SimpleDateFormat(pattern, Locale.US);
        } catch (Exception ex) {
            dateFormat = new SimpleDateFormat(DATE_PATTERN, Locale.US);
            ai_BugHandler.ai_Handler_Exception(ex);
        }

        Calendar c = Calendar.getInstance();
        try {
            String sText = CommonHelper.GetValue(iPosition, oInsItem);
            if (!StringUtils.isEmpty(sText)) {
                c.setTime(Objects.requireNonNull(dateFormat.parse(sText)));
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }

        String sPattern = pattern.toLowerCase();
        DateTimePickerDialog.DateTimePickerMode mode;
        if (sPattern.contains("yy") && sPattern.contains("hh")) {
            mode = DateTimePickerDialog.DateTimePickerMode.DATE_TIME;
        } else if (sPattern.contains("hh")) {
            mode = DateTimePickerDialog.DateTimePickerMode.TIME;
        } else {
            mode = DateTimePickerDialog.DateTimePickerMode.DATE;
        }

        DateTimePickerDialog dialog = new DateTimePickerDialog(c, mode);
        dialog.setDateTimeSetListener(this);
        dialog.show(getSupportFragmentManager(), "DATE_TIME");
    }

    @Override
    public void onDateTimeChanged(Calendar calendar) {
        CommonHelper.SetValue(iPosition, oInsItem, dateFormat.format(calendar.getTime()));
        oInsItem.save();
    }

    @Override
    public void onDateTimeCleaned() {
        CommonHelper.SetValue(iPosition, oInsItem, "");
        oInsItem.save();
    }

    @Override
    public void dismiss() {
        finish();
    }
}
