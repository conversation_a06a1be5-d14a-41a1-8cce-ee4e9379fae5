package com.snapinspect.snapinspect3.activitynew.Comments;

import android.annotation.SuppressLint;
import android.annotation.TargetApi;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.media.ExifInterface;
import android.net.Uri;
import android.os.Bundle;
import android.app.Activity;
import android.os.Handler;
import android.provider.MediaStore;
import androidx.annotation.Nullable;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import android.text.Editable;
import android.text.TextUtils;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.afollestad.materialdialogs.MaterialDialog;
//import com.amazonaws.org.apache.http.NameValuePair;

import com.snapinspect.snapinspect3.Helper.*;
import org.apache.http.NameValuePair;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.Adapter.InsCommentsAdapter;
import com.snapinspect.snapinspect3.BuildConfig;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Comment;
import com.snapinspect.snapinspect3.IF_Object.ai_File;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.IF_Object.ai_User;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.async.PhotoUploader;
import com.snapinspect.snapinspect3.util.SimpleTextWatcher;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static android.text.InputType.TYPE_CLASS_TEXT;
import static android.text.InputType.TYPE_TEXT_FLAG_AUTO_CORRECT;
import static android.text.InputType.TYPE_TEXT_FLAG_CAP_SENTENCES;
import static android.text.InputType.TYPE_TEXT_FLAG_MULTI_LINE;

public class if_InspectionComments extends Activity
        implements View.OnClickListener, SwipeRefreshLayout.OnRefreshListener, PhotoUploader.Callback {

    private TextView tvTitle;
    private RecyclerView lvComments;
    private EditText etComments;
    private Button btnSend;
    private Button btnControl;

    private int iSInsID;

    private ai_Inspection oInspection;
    private MaterialDialog oDialog;
    private List<ai_Comment> lsComments = new ArrayList<>();
    private final List<String> lsHeaders = new ArrayList<>();
    private InsCommentsAdapter commentsAdapter;
    private SwipeRefreshLayout swipeRefreshLayout;
    private Uri takenPhotoPath;
    private String mCurrentPhotoPath;
    private boolean photosPicked;
    private Uri[] pickedPhotos;
    private PhotoUploader uploader;
    private LinearLayout userView;

    private static final int PICK_PHOTOS_REQUEST_CODE = 1;
    private static final int CAPTURE_IMAGES_FROM_CAMERA = 2;
    private static final int MY_PERMISSIONS_REQUEST_CAMERA = 4;
    private static final int MY_PERMISSIONS_REQUEST_STORAGE = 3;

    @SuppressLint("ClickableViewAccessibility")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        getActionBar().setDisplayHomeAsUpEnabled(true);
        getActionBar().show();

        setContentView(R.layout.activity_inspection_comments);

        userView = findViewById(R.id.user_view);
        tvTitle = findViewById(R.id.comments_tv_title);
        lvComments = findViewById(R.id.comments_list_view);

        lvComments.setLayoutManager(new GridLayoutManager(this, 1));

        commentsAdapter = new InsCommentsAdapter(this);
        lvComments.setAdapter(commentsAdapter);
        swipeRefreshLayout =
                findViewById(R.id.comments_swipe_refresh);
        swipeRefreshLayout.setOnRefreshListener(this);

        etComments = findViewById(R.id.comments_et_note);
        btnSend = findViewById(R.id.comments_btn_send);
        btnControl = findViewById(R.id.comments_btn_control);
        btnSend.setOnClickListener(this);
        btnControl.setOnClickListener(this);
        etComments.setInputType(TYPE_CLASS_TEXT | TYPE_TEXT_FLAG_CAP_SENTENCES
                | TYPE_TEXT_FLAG_AUTO_CORRECT | TYPE_TEXT_FLAG_MULTI_LINE);


        iSInsID = getIntent().getIntExtra(Constants.Extras.iSInsID, 0);

        List<ai_Inspection> lsTemp = ai_Inspection.find(ai_Inspection.class, "I_S_INS_ID=?", "" + iSInsID);
        ai_Inspection oTemp = new ai_Inspection();

        if (lsTemp != null && lsTemp.size() > 0) {
            oTemp = lsTemp.get(0);
        }

        oInspection = oTemp;
        if (oInspection != null && oInspection.iSAssetID > 0) {
            getActionBar().setTitle(oInspection.sTitle);
            tvTitle.setText(oInspection.sInsTitle);
        } else {
            getActionBar().setTitle("Comment");
            tvTitle.setText("Comment");
        }

        etComments.addTextChangedListener(new SimpleTextWatcher() {
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                resetEnableButton();
            }

            @Override
            public void afterTextChanged(Editable editable) {
                findUserByString(editable.toString());
            }
        });

        userView.setOnTouchListener((view, motionEvent) -> true);

        lvComments.setOnTouchListener((view, motionEvent) -> {
            hideSoftKeyboard();
            return false;
        });
        CommonValidate.Permission_Validate(if_InspectionComments.this);
        //checkPermissions();
    }

    private void findUserByString(String comment) {
        String[] ss = comment.split("@");

        resetUserView();

        if (ss.length > 1) {
            final String searchStr = ss[ss.length - 1];
            if (searchStr.contains(" ") || searchStr.isEmpty()) {
                return;
            }

            final List<ai_User> filtered = new ArrayList<>();
            List<ai_User> users = CommonDB.GetAllUsers();
            for (ai_User user : users) {
                if (user.sName.toLowerCase().contains(searchStr.toLowerCase())) {
                    //Log.e("osama", "sdd " + user.sName);
                    filtered.add(user);
                }
            }

            new Handler().post(new Runnable() {
                @Override
                public void run() {
                    for (ai_User user : filtered) {
                        TextView tvUser = new TextView(if_InspectionComments.this);
                        tvUser.setText(user.sName);
                        tvUser.setTag(user.sName);
                        tvUser.setTextColor(android.graphics.Color.parseColor("#000000"));
                        tvUser.setLines(1);
                        tvUser.setEllipsize(TextUtils.TruncateAt.END);

                        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(LinearLayout.LayoutParams.MATCH_PARENT, CommonHelper.pxFromDp(if_InspectionComments.this, 30));
                        int margin = CommonHelper.pxFromDp(if_InspectionComments.this, 4);
                        params.setMargins(margin, margin, margin, 0);
                        tvUser.setLayoutParams(params);
                        userView.addView(tvUser);

                        tvUser.setOnClickListener(view -> {
                            Log.e("osama", view.getTag().toString());
                            String name = view.getTag().toString().replace(" ", "");
                            String comment1 = etComments.getText().toString();

                            String updated = "";
                            if (comment1 != null && comment1.length() > 1) {
                                updated = comment1.substring(0, comment1.length() - searchStr.length());
                            }

                            etComments.setText("");
                            etComments.append(updated + name + " ");

                            resetUserView();
                        });
                    }
                }
            });

        }
    }


    private void resetUserView() {
        if (userView != null) {
            userView.removeAllViews();
        }
    }

    private void resetEnableButton() {
        if (etComments.getText().toString().isEmpty()) {
            btnSend.setEnabled(false);
            btnSend.setTextColor(getResources().getColor(R.color.gray_color));
        } else {
            btnSend.setTextColor(getResources().getColor(R.color.blue_color));
            btnSend.setEnabled(true);
        }
    }

    @Override
    public void onResume() {
        super.onResume();

        if (photosPicked) {
            uploadPhotoComment();
        } else {
            getAllInspectionComments(true);
        }

        resetEnableButton();
    }

    @Override
    public void onPause() {
        super.onPause();
        hideRefreshIndicator();
        hideSoftKeyboard();

        if (null != uploader) {
            uploader.setCallback(null);
            uploader.cancel(false);
            uploader = null;
        }

        dismissDialog();
    }

    public void hideSoftKeyboard() {
        InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
        if (getCurrentFocus() != null)
            inputMethodManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.comments_btn_send:
                sendTextComment();
                break;
            case R.id.comments_btn_control:
                showControls();
                break;
            default:
                break;
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();

        switch (id) {
            case android.R.id.home:
                finish();
                break;
            default:
                break;
        }

        return super.onOptionsItemSelected(item);
    }

    private void showControls() {
//        AlertDialog.Builder builder = CommonUI.GetAlertBuilder("Action", null, this, true, false);
        String[] options = new String[]{"Take a Photo", "Choose from Library"};

//        builder.setTitle("What would you like to do?");
//        builder.setItems(options, new DialogInterface.OnClickListener() {
//
//            @Override
//            public void onClick(DialogInterface dialog, int which) {
//
//                dialog.dismiss();
//                if (which == 0) {
//                    actionCamearPic();
//                } else if (which == 1) {
//                    actionGalleryPic();
//                }
//            }
//
//        });
//
//        builder.show();

        new MaterialDialog.Builder(this)
                .title("What would you like to do?")
                .items(options)
                .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                    @Override
                    public boolean onSelection(MaterialDialog dialog, View view, int which, CharSequence text) {
                        if (which == 0) {
                            actionCamearPic();
                        } else if (which == 1) {
                            actionGalleryPic();
                        }
                        return true;
                    }
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    private void actionCamearPic() {
        try {
            dispatchTakePictureIntent();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void actionGalleryPic() {
        Intent intent = new Intent("android.intent.action.GET_CONTENT");
        intent.putExtra("android.intent.extra.ALLOW_MULTIPLE", false);
        intent.putExtra("android.intent.extra.LOCAL_ONLY", true);
        intent.putExtra(Intent.ACTION_EDIT, true);
        intent.setType("image/*");
        startActivityForResult(intent, PICK_PHOTOS_REQUEST_CODE);
    }

    private void dispatchTakePictureIntent() throws IOException {
        Intent takePictureIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);
        // Ensure that there's a camera activity to handle the intent
        if (takePictureIntent.resolveActivity(getPackageManager()) != null) {
            // Create the File where the photo should go
            File photoFile = null;
            try {
                photoFile = createImageFile();
            } catch (IOException ex) {
                // Error occurred while creating the File
                return;
            }
            // Continue only if the File was successfully created
            if (photoFile != null) {
                Uri photoURI = FileProvider.getUriForFile(if_InspectionComments.this,
                        BuildConfig.APPLICATION_ID + ".provider",
                        createImageFile());
                takenPhotoPath = photoURI;
                takePictureIntent.putExtra(MediaStore.EXTRA_OUTPUT, photoURI);
                startActivityForResult(takePictureIntent, CAPTURE_IMAGES_FROM_CAMERA);
            }
        }
    }

    private File createImageFile() throws IOException {
        // Create an image file name
        String timeStamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        String imageFileName = "JPEG_" + timeStamp + "_";
        File storageDir = new File(CommonHelper.sFileRoot);
        File image = File.createTempFile(
                imageFileName,  /* prefix */
                ".jpg",   /* suffix */
                storageDir      /* directory */
        );

        // Save a file: path for use with ACTION_VIEW intents
        mCurrentPhotoPath = image.getAbsolutePath();
        return image;
    }

    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (PICK_PHOTOS_REQUEST_CODE == requestCode) {
            photosPicked = Activity.RESULT_OK == resultCode;
            if (photosPicked) {

                pickedPhotos = getPickedPhotos(data);
                Uri[] pickedPtos = getPickedPhotos(data);
                if (pickedPtos != null && pickedPtos.length > 0) {
                    File file = new File(pickedPtos[0].getPath());

                    if (file.exists()) {
                        Log.e("osama", "Existed");
                    }
                }
            }
        } else if (CAPTURE_IMAGES_FROM_CAMERA == requestCode) {
            if (Activity.RESULT_OK == resultCode && rotatePhoto()) {

                Uri[] uris = new Uri[1];
                File file = new File(mCurrentPhotoPath);

                if (file.exists()) {
                    Log.e("osama", "file existed");
                }
                uris[0] = Uri.fromFile(file);

                pickedPhotos = uris;
                photosPicked = true;
            } else {
                photosPicked = false;
            }
        } else {
            super.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Nullable
    @TargetApi(16)
    private static Uri[] getPickedPhotos(@Nullable Intent data) {
        if (data == null) {
            return null;
        }
        if (data.getData() == null || data.getClipData() != null) {
            Uri[] uris = new Uri[data.getClipData().getItemCount()];
            for (int i = 0; i < uris.length; i++) {
                uris[i] = data.getClipData().getItemAt(i).getUri();
            }
            return uris;
        }
        return new Uri[]{data.getData()};
    }

    private boolean rotatePhoto() {

        if (mCurrentPhotoPath == null) return false;

        String photopath = mCurrentPhotoPath;
        final BitmapFactory.Options options = new BitmapFactory.Options();
        options.inJustDecodeBounds = true;
        options.inSampleSize = 2;
        options.inJustDecodeBounds = false;
        options.inTempStorage = new byte[16 * 1024];

        Bitmap bmp = BitmapFactory.decodeFile(photopath, options);
        Bitmap resizedBitmap = Bitmap.createScaledBitmap(bmp, bmp.getWidth(), bmp.getHeight(), false);

        try {
            ExifInterface exif = new ExifInterface(photopath);
            String orientString = exif.getAttribute(ExifInterface.TAG_ORIENTATION);
            int orientation = orientString != null ? Integer.parseInt(orientString) : ExifInterface.ORIENTATION_NORMAL;
            int rotationAngle;
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    rotationAngle = 90;
                    break;

                case ExifInterface.ORIENTATION_ROTATE_180:
                    rotationAngle = 180;
                    break;

                case ExifInterface.ORIENTATION_ROTATE_270:
                    rotationAngle = 270;
                    break;

                case ExifInterface.ORIENTATION_NORMAL:
                    rotationAngle = 0;
                    break;
                default:
                    rotationAngle = 90;
                    break;
            }

            Matrix matrix = new Matrix();
            matrix.postRotate(rotationAngle);
            Bitmap newBmp = Bitmap.createBitmap(resizedBitmap, 0, 0, resizedBitmap.getWidth(), resizedBitmap.getHeight(), matrix, true);
            resizedBitmap.recycle();
            bmp.recycle();
            FileOutputStream fOut;
            try {
                fOut = new FileOutputStream(photopath);
                newBmp.compress(Bitmap.CompressFormat.JPEG, 100, fOut);
                fOut.flush();
                fOut.close();

                return true;
            } catch (IOException e) {
                e.printStackTrace();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }


        return false;
    }

    private void uploadPhotoComment() {
       /* if (oInspection == null) {
            Toast.makeText(this, "Error! Photo can not be saved. Please try again!", Toast.LENGTH_LONG).show();
            return;
        }*/

        oDialog = CommonUI.ShowMaterialProgressDialog(this, "Running", "Connecting to Server");
//        oDialog = CommonUI.GetProgressDialog(this, "Running", "Connecting to Server", true);

        uploader = new PhotoUploader(this, oInspection.iSAssetID);
        uploader.setCallback(this);
        uploader.execute(pickedPhotos);
    }

    private void sendTextComment() {
        String sComment = etComments.getText().toString();
        sendInspectionComment(sComment, "c");

        etComments.setText("");
    }

    private void reloadComments() {
        lsHeaders.clear();
        final Map<String, List<ai_Comment>> map = new HashMap<>();

        for (ai_Comment com : lsComments) {
            List<ai_Comment> comments = map.get(com.sDate);
            if (comments == null) {
                comments = new ArrayList<>();
                lsHeaders.add(com.sDate);
            }

            comments.add(com);
            map.put(com.sDate, comments);
        }

        Collections.sort(lsHeaders, new Comparator<String>() {
            @Override
            public int compare(String h1, String h2) {
                ai_Comment c1 = map.get(h1).get(0);
                ai_Comment c2 = map.get(h2).get(0);

                return c1.date.compareTo(c2.date);
            }
        });

        commentsAdapter.setItems(map, lsHeaders);

        lvComments.scrollToPosition(commentsAdapter.getItemCount() - 1);
    }

    private void getAllComments(JSONObject response) {
        List<ai_Comment> commentList = new ArrayList<>();
        try {
            if (oInspection == null || oInspection.iSInsID == 0) {
                JSONObject oObject = response.getJSONObject("oInspection");
                if (oObject != null) {
                    oInspection = new ai_Inspection();
                    oInspection.sTitle = oObject.getString("sTitle");
                    oInspection.sInsTitle = oObject.getString("sInsTitle");
                    oInspection.iSAssetID = oObject.getInt("iPropertyID");
                    oInspection.iSInsID = oObject.getInt("iInspectionID");
                    getActionBar().setTitle(oInspection.sTitle);
                    tvTitle.setText(oInspection.sInsTitle);
                }
            }
        } catch (Exception ex) {

        }

        try {
            JSONArray jsonArray = response.getJSONArray("lsComments");

            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject obj = jsonArray.getJSONObject(i);
                ai_Comment oComment = CommonJson.oJsonToComments(obj);
                commentList.add(oComment);
            }

        } catch (JSONException ex) {
            ex.printStackTrace();
        }

        lsComments = commentList;
        reloadComments();
    }

    private void addCommentResponse(JSONObject response) {
        try {
            JSONObject commentJson = response.getJSONObject("oComment");

            ai_Comment oComment = CommonJson.oJsonToComments(commentJson);
            lsComments.add(oComment);
            reloadComments();
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

   /* private ai_Comment parseJsontoComment(JSONObject json) {

    }

    private void getDownloadableUrl(final ai_Comment oComment, String sFileId) {
        String sURL = "/IOAPI/GetAssetFile";

        ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
        RequestParams oParams = new RequestParams();
        oParams.add("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
        oParams.add("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
        oParams.add("iFileID", "" + sFileId);

        IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
            @Override
            public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                if (statusCode == 200) {
                    try {
                        if (response.getBoolean("success")) {
                           // Log.e("osama","" + response);
                            String url = response.getString("sDownloadURL");
                            oComment.sPhotoUrl = url;
                        }
                    } catch (Exception ex) {
                        ex.printStackTrace();
                    }

                }
            }
        });

    } */

    private void getAllInspectionComments(boolean isShowing) {
        try {
            if (isShowing)
                oDialog = CommonUI.ShowMaterialProgressDialog(this, "Running", "Connecting to Server");
//                oDialog = CommonUI.GetProgressDialog(this, "Running", "Connecting to Server", true);
            String sURL = "/ioapi/getinspectioncomments";

            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
            oParams.add("iInspectionID", "" + iSInsID);

            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    CommonUI.DismissMaterialProgressDialog(if_InspectionComments.this, oDialog);
                    hideRefreshIndicator();
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            if (response.getBoolean("success")) {
                                // Log.e("osama","" + response);
                                getAllComments(response);
                                return;
                            } else {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        ShowAlert("Failed", "Please try again.");
                                    }
                                });

                                return;
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest", ex);
                        }

                    }
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            ShowAlert("Error! Failed Connection", "Please try again later.");
                        }
                    });
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_InspectionComments", ex, this);
        }
    }

    private void sendInspectionComment(String comment, String sType) {
        try {
            oDialog = CommonUI.ShowMaterialProgressDialog(this, "Running", "Connecting to Server");
//            oDialog = CommonUI.GetProgressDialog(this, "Running", "Connecting to Server", true);
            String sURL = "/ioapi/addinspectioncomments";

            ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
            oParams.add("iInspectionID", "" + iSInsID);
            oParams.add("sComments", "" + comment);
            oParams.add("sType", "" + sType);
            oParams.add("sSource", "a");

            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    oDialog.dismiss();
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            if (response.getBoolean("success")) {
                                Log.e("osama", "" + response);
                                addCommentResponse(response);

                                return;
                            } else {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        ShowAlert("Failed", "Please try again.");
                                    }
                                });

                                return;
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Per", "if_InspectionComments.sendInspectionComment", ex);
                        }

                    }
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            ShowAlert("Error! Failed Connection", "Please try again later.");
                        }
                    });
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_InspectionComments.sendInspectionComment", ex, this);
        }
    }

    private void ShowAlert(String sTitle, String sMessage) {
        CommonUI.ShowAlert(this, sTitle, sMessage);

//        AlertDialog.Builder builder = CommonUI.GetAlertBuilder(sTitle, sMessage, this, false, true);
//
//        builder.show();
    }

    @Override
    public void onRefresh() {
        swipeRefreshLayout.setRefreshing(true);
        getAllInspectionComments(false);
    }

    private void hideRefreshIndicator() {

        new Handler().post(new Runnable() {
            @Override
            public void run() {
                if (null != swipeRefreshLayout) {
                    swipeRefreshLayout.setRefreshing(false);
                }
            }
        });
    }

    @Override
    public void onProgress(PhotoUploader.Progress progress) {

    }

    @Override
    public void onSuccess(List<ai_File> aFileList) {
        photosPicked = false;
        dismissDialog();

        for (ai_File oFile : aFileList) {
            sendInspectionComment(String.valueOf(oFile.iFileID), "f");
        }
    }

    @Override
    public void onFailure(String message) {
        photosPicked = false;
        dismissDialog();

        ShowAlert("Error", message);
    }

    private void dismissDialog() {
        if (null != oDialog) {
            oDialog.dismiss();
            oDialog = null;
        }
    }

    /*private boolean isTextComment(ai_Comment oComment) {
         return oComment.sType.toLowerCase().equals("c");
    }*/

    private boolean isPhotoComment(ai_Comment oComment) {
        return oComment.sType.equalsIgnoreCase("f");
    }

   /* private boolean isUserComment(ai_Comment oComment) {
        return oComment.sType.toLowerCase().equals("a");
    }*/

}