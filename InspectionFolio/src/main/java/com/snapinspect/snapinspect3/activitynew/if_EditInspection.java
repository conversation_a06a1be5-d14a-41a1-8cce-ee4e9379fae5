
package com.snapinspect.snapinspect3.activitynew;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import android.text.InputType;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ExpandableListView;
import android.widget.TextView;
import android.widget.Toast;

import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Adapter.Ins_Edit_Adapter;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.app.App;

import org.json.JSONObject;

import java.lang.reflect.Field;
import java.util.Observable;
import java.util.Observer;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_CHK;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_CMT;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_MCHK;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_MSEL;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_PTO;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_SCAN;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_SCHK;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_SEL;


/*
 * @Created by osama 6/23/17
 */
public class if_EditInspection extends Activity implements Observer, Ins_Edit_Adapter.Listener {
    private int iInspectionID;
    private ai_Inspection oInspection;
    ExpandableListView listview;
    private ai_InsItem oInsItem;
    private ai_InsItem oPInsItem;
    private Ins_Edit_Adapter editAdapter;
    private TextView txtTimer, tvTitle;
    long iPInsItemID, iInsItemID;
    int times = 0;
    App baseApplication;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        iInspectionID = getIntent().getIntExtra(Constants.Extras.iInspectionID, 0);
        iPInsItemID = getIntent().getLongExtra(Constants.Extras.iPInsItemID, 0);
        iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, 0);

        oPInsItem = ai_InsItem.findById(ai_InsItem.class, iPInsItemID);
        oInsItem = ai_InsItem.findById(ai_InsItem.class, iInsItemID);

        getActionBar().setDisplayHomeAsUpEnabled(true);

        getActionBar().show();
        setContentView(R.layout.activity_if_edit_inspection);
        getOverflowMenu();

        oInspection = ai_Inspection.findById(ai_Inspection.class, (long) iInspectionID);
        getActionBar().setTitle("Edit Item");

        if (oInspection.bLock) {
            finish();
        }

        listview = findViewById(R.id.activity_if_edit_inspection_listview);
        editAdapter = new Ins_Edit_Adapter(if_EditInspection.this, this);
        listview.setAdapter(editAdapter);

        LayoutInflater myinflater = getLayoutInflater();
        ViewGroup myHeader = (ViewGroup)myinflater.inflate(R.layout.header_ins_edit, listview, false);
        listview.addHeaderView(myHeader, null, false);
        tvTitle = myHeader.findViewById(R.id.header_ins_edit_tv_title);
        tvTitle.setText(oInsItem.sName);
        myHeader.findViewById(R.id.header_ins_edit_btn_menu).setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                editGroupItem();
            }
        });
        listview.setOnGroupClickListener(new ExpandableListView.OnGroupClickListener() {
            @Override
            public boolean onGroupClick(ExpandableListView parent, View v,
                                        int groupPosition, long id) {
                return true; // This way the expander cannot be collapsed
            }
        });
        //Osama Add, Prompt
//        promptView = (LinearLayout)findViewById(R.id.prompt_layout);
//        TextView tvMessage = (TextView)promptView.findViewById(R.id.view_prompt_tv_message);
//        tvMessage.setText(PROMPT_MESSAGE);
//        Button btnClose = (Button)promptView.findViewById(R.id.view_prompt_btn_close);
//        if (btnClose != null) {
//            btnClose.setOnClickListener(new View.OnClickListener() {
//                @Override
//                public void onClick(View view) {
//                    showPopUp(false);
//                }
//            });
//        }

//        swipeRefreshLayout = (SwipeRefreshLayout)findViewById(R.id.if_exist_asset_refresh);

        txtTimer = findViewById(R.id.txt_timer);
        baseApplication = (App) getApplication();
        baseApplication.getObserver().addObserver(this);


        try {
            String sCustomOne = oInspection.sCustomOne;
            if (sCustomOne != null && !sCustomOne.equals("")) {
                String sTimer = CommonJson.GetJsonKeyValue("InsTimer", sCustomOne );
                times = Integer.parseInt(sTimer);

            }
        }catch(Exception ex){
            times = 0;
        }


    }

    private void getOverflowMenu() {

        try {
            ViewConfiguration config = ViewConfiguration.get(this);
            Field menuKeyField = ViewConfiguration.class.getDeclaredField("sHasPermanentMenuKey");
            if(menuKeyField != null) {
                menuKeyField.setAccessible(true);
                menuKeyField.setBoolean(config, false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public void onResume(){
        try {
            super.onResume();

            oPInsItem = ai_InsItem.findById(ai_InsItem.class, iPInsItemID);
            oInsItem = ai_InsItem.findById(ai_InsItem.class, iInsItemID);
            displayItems();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void displayItems() {
        editAdapter.setInsItem(oInsItem, oPInsItem);
    }
    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_edit_ins, menu);

        return true;
    }

    @Override
    public boolean onOptionsItemSelected( MenuItem item )
    {
        try {
            if (item.getItemId() == R.id.action_save) {
                oPInsItem.save();
                oInsItem.save();

                onBackPressed();
            } else if (item.getItemId() == R.id.action_edit) {
                ai_InsItem insItem;
                if (oInsItem.sQType.equals("C")&& oPInsItem != null) {
                    insItem = oPInsItem;
                } else {
                    insItem = oInsItem;
                }
                Intent intent = new Intent(if_EditInspection.this, if_OrderInsItem.class);
                intent.putExtra(Constants.Extras.iInsItemID, insItem.getId());
                startActivity(intent);
            } else {
                onBackPressed();

            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_inspection.onOptionItemsSelected", ex, this);
        }
        return true;
    }
    @Override
    public void update(Observable observable, Object o) {
        txtTimer.setText(baseApplication.getObserver().getValue());
    }

    private void editGroupItem() {

        try {
            final EditText input = new EditText(this);
            input.setText(oInsItem.sName);
            String sTitleText = String.format("Re-Name %s", oInsItem.sName);


//            AlertDialog.Builder alert = CommonUI.GetAlertBuilder(sTitleText, "", if_EditInspection.this, true, false);
//                    alert.setView(input);
//                    input.setTextColor(Color.DKGRAY);
//            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
//            if (inputMethodManager != null) {
//                inputMethodManager.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0);
//            }
//            alert.setPositiveButton("OK", new DialogInterface.OnClickListener() {
//                public void onClick(DialogInterface dialog, int whichButton) {
//                    String sValue = input.getText().toString();
//                    input.requestFocus();
//                    InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
//                    if (inputMethodManager != null) {
//                        inputMethodManager.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0);
//                    }
//                    if (sValue.isEmpty()) {
//                        Toast.makeText(getBaseContext(), "This field can not be empty.", Toast.LENGTH_LONG).show();
//
//                    } else {
//                        oInsItem.sName = sValue;
//                        tvTitle.setText(sValue);
//                    }
//
//                }
//            });
//
//
//            alert.show();

            new MaterialDialog.Builder(this)
                    .title(sTitleText)
                    .content(null)
                    .inputType(InputType.TYPE_CLASS_TEXT)
                    .input("Enter name", oInsItem.sName, new MaterialDialog.InputCallback() {
                        @Override
                        public void onInput(MaterialDialog dialog, CharSequence input) {
                            String sValue = input.toString();

                            if (sValue.isEmpty()) {
                                Toast.makeText(getBaseContext(), "This field can not be empty.", Toast.LENGTH_LONG).show();

                            } else {
                                oInsItem.sName = sValue;
                                tvTitle.setText(sValue);
                            }
                        }
                    }).show();
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_ins_full.ShowAddAlert", ex, this);
        }

    }

    @Override
    public void onMenuClicked(final int position, final String sConfig) {
        if (sConfig == null) return;

        final boolean isCMT = sConfig.startsWith(SI_S_CONFIG_KEY_CMT);

//        AlertDialog.Builder builder = CommonUI.GetAlertBuilder("Action", "", if_EditInspection.this, true, false);

        String[] options = {"Edit Prompt Text", "Delete"};
        String[] options1 = {"Delete"};


//        builder.setItems(isCMT ? options : options1, new DialogInterface.OnClickListener() {
//
//            @Override
//            public void onClick(DialogInterface dialog, int which) {
//
//                dialog.dismiss();
//                if (which == 0) {
//                    if (isCMT) {
//                        editConfigCommit(position, sConfig);
//                    } else {
//                        confirmDelete(position);
//                    }
//
//                }else if (which == 1) {
//                    confirmDelete(position);
//                }
//            }
//
//        });
//
//        builder.show();

        new MaterialDialog.Builder(this)
                .title(R.string.alert_title_action)
                .items(isCMT ? options : options1)
                .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                    @Override
                    public boolean onSelection(MaterialDialog dialog, View view, int which, CharSequence text) {
                        if (which == 0) {
                            if (isCMT) {
                                editConfigCommit(position, sConfig);
                            } else {
                                confirmDelete(position);
                            }

                        }else if (which == 1) {
                            confirmDelete(position);
                        }
                        return true;
                    }
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    private void confirmDelete(final int position) {
//        AlertDialog.Builder builder = CommonUI.GetAlertBuilder("Message", "Are you sure to Delete this item?", if_EditInspection.this, true, false);
//
//        builder.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
//            public void onClick(DialogInterface dialog, int which) {
//
//                deleteConfig(position);
//            }
//        });
//
//        builder.show();

        new MaterialDialog.Builder(this)
                .title("Message")
                .content("Are you sure to Delete this item?")
                .positiveText(R.string.tv_ok)
                .onPositive(new MaterialDialog.SingleButtonCallback() {
                    @Override
                    public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                        deleteConfig(position);
                        finish();
                    }
                })
                .show();
    }
    private void deleteConfig(int position) {
        if (position < 1 || position > 6) {
            return;
        }

        ai_InsItem insItem;
        if (oInsItem.sQType.equals("C")&& oPInsItem != null) {
            insItem = oPInsItem;
        } else {
            insItem = oInsItem;
        }

        switch (position) {
            case 1:
                insItem.sConfigOne = "";
                insItem.sValueOne = "";

                break;
            case 2:
                insItem.sConfigTwo = "";
                insItem.sValueTwo = "";
                break;
            case 3:
                insItem.sConfigThree = "";
                insItem.sValueThree = "";
                break;
            case 4:
                insItem.sConfigFour = "";
                insItem.sValueFour = "";
                break;
            case 5:
                insItem.sConfigFive = "";
                insItem.sValueFive = "";
                break;
            case 6:
                insItem.sConfigSix = "";
                insItem.sValueSix = "";
                break;
            default:
                break;
        }

        if (oInsItem.sQType.equals("C")&& oPInsItem != null) {
            oPInsItem = insItem;
        } else {
            oInsItem = insItem;
        }

        displayItems();
    }

    @Override
    public void updateConfig(int iPosition, String sConfig) {
        ai_InsItem insItem;
        if (oInsItem.sQType.equals("C")&& oPInsItem != null) {
            insItem = oPInsItem;
        } else {
            insItem = oInsItem;
        }

        if (iPosition == 1){
            insItem.sConfigOne = sConfig;
        }
        else if (iPosition == 2){
            insItem.sConfigTwo = sConfig;
        }
        else if (iPosition == 3){
            insItem.sConfigThree = sConfig;
        }
        else if (iPosition == 4){
            insItem.sConfigFour = sConfig;
        }
        else if (iPosition == 5){
            insItem.sConfigFive = sConfig;
        }
        else if (iPosition == 6){
            insItem.sConfigSix = sConfig;
        }

        if (oInsItem.sQType.equals("C")&& oPInsItem != null) {
            oPInsItem = insItem;
        } else {
            oInsItem = insItem;
        }

        displayItems();
    }

    @Override
    public void addConfig(final int position) {
//        AlertDialog.Builder builder = CommonUI.GetAlertBuilder("Select Rating Type", "", if_EditInspection.this, true, false);

        String[] options = {"CHK", "SCHK", "MCHK", "CMT", "PTO", "SCAN", "SEL", "MSEL"};

//        builder.setItems(options, new DialogInterface.OnClickListener() {
//
//            @Override
//            public void onClick(DialogInterface dialog, int which) {
//
//                dialog.dismiss();
//                switch (which) {
//                    case 0:  //chk
//                        addConfig(position, SI_S_CONFIG_KEY_CHK);
//                        break;
//                    case 1: //schk
//                        addConfig(position, SI_S_CONFIG_KEY_SCHK);
//                        break;
//                    case 2: //mchk
//                        addConfig(position, SI_S_CONFIG_KEY_MCHK);
//                        break;
//                    case 3: //cmt
//                        addConfig(position, SI_S_CONFIG_KEY_CMT);
//                        break;
//                    case 4: //pto
//                        addConfig(position, SI_S_CONFIG_KEY_PTO);
//                        break;
//                    case 5: //scan
//                        addConfig(position, SI_S_CONFIG_KEY_SCAN);
//                        break;
//                    case 6: //sel
//                        addConfig(position, SI_S_CONFIG_KEY_SEL);
//                        break;
//                    case 7:
//                        addConfig(position, SI_S_CONFIG_KEY_MSEL);
//                        break;
//                    default:
//                        break;
//                }
//            }
//
//        });
//        builder.show();

        new MaterialDialog.Builder(this)
                .title("Select Rating Type")
                .items(options)
                .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                    @Override
                    public boolean onSelection(MaterialDialog dialog, View view, int which, CharSequence text) {
                        switch (which) {
                            case 0:  //chk
                                addConfig(position, SI_S_CONFIG_KEY_CHK);
                                break;
                            case 1: //schk
                                addConfig(position, SI_S_CONFIG_KEY_SCHK);
                                break;
                            case 2: //mchk
                                addConfig(position, SI_S_CONFIG_KEY_MCHK);
                                break;
                            case 3: //cmt
                                addConfig(position, SI_S_CONFIG_KEY_CMT);
                                break;
                            case 4: //pto
                                addConfig(position, SI_S_CONFIG_KEY_PTO);
                                break;
                            case 5: //scan
                                addConfig(position, SI_S_CONFIG_KEY_SCAN);
                                break;
                            case 6: //sel
                                addConfig(position, SI_S_CONFIG_KEY_SEL);
                                break;
                            case 7:
                                addConfig(position, SI_S_CONFIG_KEY_MSEL);
                                break;
                            default:
                                break;
                        }
                        return true;
                    }
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    @Override
    public void editCommit(int position, String sConfig) {
        editConfigCommit(position, sConfig);
    }

    private void editConfigCommit(final int position, String sConfig) {
        try {
            final EditText input = new EditText(this);
            String commit = sConfig != null && sConfig.length() > 5 ? sConfig.substring(4, sConfig.length() - 1) : "";
            input.setText(commit);
            String sTitleText = String.format("Edit Prompt Text");


//            AlertDialog.Builder alert = CommonUI.GetAlertBuilder(sTitleText, "", if_EditInspection.this, true, false);
//                    alert.setView(input);
//                    input.setTextColor(Color.DKGRAY);
//            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
//            if (inputMethodManager != null) {
//                inputMethodManager.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0);
//            }
//            alert.setPositiveButton("OK", new DialogInterface.OnClickListener() {
//                public void onClick(DialogInterface dialog, int whichButton) {
//                    String sValue = input.getText().toString();
//                    input.requestFocus();
//                    InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
//                    if (inputMethodManager != null) {
//                        inputMethodManager.toggleSoftInput(InputMethodManager.SHOW_FORCED, 0);
//                    }
//                    if (sValue.isEmpty()) {
//                        Toast.makeText(getBaseContext(), "This field can not be empty.", Toast.LENGTH_LONG).show();
//
//                    } else {
//                        String update = "CMT(" + sValue + ")";
//                        updateConfig(position, update);
//                    }
//
//                }
//            });
//
//
//            alert.show();

            new MaterialDialog.Builder(this)
                    .title(sTitleText)
                    .content(null)
                    .inputType(InputType.TYPE_CLASS_TEXT)
                    .input("", commit, (dialog, input1) -> {
                        String sValue = input1.toString();
                        if (sValue.isEmpty()) {
                            Toast.makeText(getBaseContext(), "This field can not be empty.", Toast.LENGTH_LONG).show();
                        } else {
                            String update = "CMT(" + sValue + ")";
                            updateConfig(position, update);
                        }
                    }).show();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_ins_full.ShowAddAlert", ex, this);
        }
    }
    private void addConfig(int iPosition, String prefix) {
        ai_InsItem insItem;
        if (oInsItem.sQType.equals("C")&& oPInsItem != null) {
            insItem = oPInsItem;
        } else {
            insItem = oInsItem;
        }

        String sConfig = "";
        if (prefix.equals(SI_S_CONFIG_KEY_CHK) || prefix.equals(SI_S_CONFIG_KEY_SCHK) || prefix.equals(SI_S_CONFIG_KEY_MCHK)) {
            sConfig = prefix + "()";
        }
        else if (prefix.equals(SI_S_CONFIG_KEY_CMT)) {
            sConfig = prefix + "()";
        }
        else if (prefix.equals(SI_S_CONFIG_KEY_PTO) || prefix.equals(SI_S_CONFIG_KEY_SCAN)) {
            sConfig = prefix;
        }
        else if (prefix.equals(SI_S_CONFIG_KEY_MSEL) || prefix.equals(SI_S_CONFIG_KEY_SEL)) {
            try {
                JSONObject selJson = new JSONObject();
                selJson.put("sType", prefix);
                selJson.put("sLabel", "");
                selJson.put("Content", "");
                selJson.put("bAdd", true);

                sConfig = selJson.toString();
            }catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (iPosition == 1){
            insItem.sConfigOne = sConfig;
            insItem.sValueOne = "";
        }
        else if (iPosition == 2){
            insItem.sConfigTwo = sConfig;
            insItem.sValueTwo = "";
        }
        else if (iPosition == 3){
            insItem.sConfigThree = sConfig;
            insItem.sValueThree = "";
        }
        else if (iPosition == 4){
            insItem.sConfigFour = sConfig;
            insItem.sValueFour = "";
        }
        else if (iPosition == 5){
            insItem.sConfigFive = sConfig;
            insItem.sValueFive = "";
        }
        else if (iPosition == 6){
            insItem.sConfigSix = sConfig;
            insItem.sValueSix = "";
        }

        if (oInsItem.sQType.equals("C")&& oPInsItem != null) {
            oPInsItem = insItem;
        } else {
            oInsItem = insItem;
        }

        displayItems();
    }
}
