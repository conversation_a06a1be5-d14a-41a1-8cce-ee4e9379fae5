package com.snapinspect.snapinspect3.activitynew.inspection3rd;

import android.app.Activity;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.ViewConfiguration;
import android.widget.ImageButton;
import android.widget.Toast;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Adapter.InsRoomEditAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.woxthebox.draglistview.DragListView;

import java.lang.reflect.Field;
import java.util.List;

public class if_Ins_Edit_3rd extends Activity implements DragListView.DragListListener, InsRoomEditAdapter.SharedListener {

    private int insId;
    private String sType, sPTC;
    private ai_Inspection oInspection;
    private DragListView dragListView;
    private List<ai_Layout> lsLayout;
    private boolean bRequestIns = false;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_ins_edit_3rd);
        try {
            insId = getIntent().getIntExtra(Constants.Extras.iInspectionID, 0);
            oInspection = ai_Inspection.findById(ai_Inspection.class, insId);
            bRequestIns = CommonValidate.bExternalInspection(oInspection.sCustomTwo);


            if (oInspection.bLock) {
                Toast.makeText(this, "The Inspection is locked.", Toast.LENGTH_LONG).show();
                finish();
            } else {
                getActionBar().setDisplayHomeAsUpEnabled(true);
                getActionBar().setTitle(oInspection.sAddressOne);
                getOverflowMenu();

                ImageButton btnAdd = findViewById(R.id.btn_inspection_add);
                btnAdd.setOnClickListener(v -> addNewInsRoom());

                dragListView = findViewById(R.id.lv_Inspections_areas);
                dragListView.setLayoutManager(new LinearLayoutManager(this));
                dragListView.setCanDragHorizontally(false);
                dragListView.setDragListListener(this);

                InsRoomEditAdapter adapter = new InsRoomEditAdapter(this);
                adapter.setSharedListener(this);
                dragListView.setAdapter(adapter, true);
                dragListView.setCustomDragItem(new InsRoomEditAdapter.InsRoomDragItem(this, bRequestIns));

                sPTC = oInspection.sPTC;
                sType = oInspection.sType;
            }
        } catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Ins_Edit_3rd.onCreate", ex, this);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        CommonDB.InsertLog(this, "Event", "Inspection View " + oInspection.sAddressOne + " - " + oInspection.sInsTitle + " - " + oInspection.dtStartDate);
        displayInsItems();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_save, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onMenuItemSelected(int featureId, MenuItem item) {
        onBackPressed();
        return super.onMenuItemSelected(featureId, item);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();

        InsRoomEditAdapter adapter = (InsRoomEditAdapter)dragListView.getAdapter();
        List<ai_InsItem> mItemArray = adapter.getDataSource();
        for (int i = 0; i < mItemArray.size(); i++) {
            ai_InsItem insItem = mItemArray.get(i);
            insItem.iSort = i + 1;
            insItem.save();
        }

        CommonHelper.hideSoftKeyboard(this);
    }

    @Override
    public void onItemDragStarted(int position) { }

    @Override
    public void onItemDragging(int itemPosition, float x, float y) { }

    @Override
    public void onItemDragEnded(int fromPosition, int toPosition) {

    }

    @Override
    public void deleteInsItem(ai_InsItem insItem) {
        new MaterialDialog.Builder(this)
                .title("Message")
                .content(getString(R.string.lbl_DeleteItemPrompt, insItem.sName))
                .positiveText(R.string.tv_ok)
                .onPositive((dialog, which) -> {
                    if (CommonHelper.bIns1DefaultAdd(insItem.sConfig)) {
                        Toast.makeText(if_Ins_Edit_3rd.this, "Error! Compulsory section can not be deleted!", Toast.LENGTH_LONG).show();
                        return;
                    }
                    insItem.bDeleted = true;
                    insItem.save();

                    // Delete all photos of this insItem
                    CommonDB.DeletePhotoByInsItemID(insItem.getId().intValue(), if_Ins_Edit_3rd.this);

                    // Delete all child items of this insItem if needed
                    List<ai_InsItem> childItems = CommonDB.GetChildInsItem(insItem.getId().intValue(), insId);
                    for (ai_InsItem childItem : childItems) {
                        childItem.bDeleted = true;
                        childItem.save();
                        // Delete all photos of this childItem
                        CommonDB.DeletePhotoByInsItemID(childItem.getId().intValue(), if_Ins_Edit_3rd.this);
                    }

                    // reload the list
                    displayInsItems();
                })
                .show();
    }

    @Override
    public void updateInsRoomName(String sName, ai_InsItem insItem) {
        insItem.sName = sName;
        insItem.save();
    }

    private void getOverflowMenu() {
        try {
            ViewConfiguration config = ViewConfiguration.get(this);
            Field menuKeyField = ViewConfiguration.class.getDeclaredField("sHasPermanentMenuKey");
            if(menuKeyField != null) {
                menuKeyField.setAccessible(true);
                menuKeyField.setBoolean(config, false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void displayInsItems() {
        List<ai_InsItem> arrInsItem = CommonDB.GetChildInsItem(0, insId);
        if (arrInsItem != null && !arrInsItem.isEmpty()) {
            InsRoomEditAdapter adapter = (InsRoomEditAdapter)dragListView.getAdapter();
            adapter.SetRequestIns(bRequestIns);
            adapter.setDataSource(arrInsItem);
        }
    }

    private List<ai_Layout> getLsLayout() {
        if (lsLayout == null || lsLayout.isEmpty()) {
            if (CommonHelper.bRequestInspection(oInspection.sCustomTwo) || CommonValidate.bExternalInspection(oInspection.sCustomTwo)) {
                lsLayout = CommonRequestInspection.GetDisplayedLayout(sType, CommonHelper.sRequestInspection_FileName(oInspection.sCustomTwo));
            } else {
                lsLayout = CommonDB.GetDisplayedLayout(sType, sPTC, this);
            }
        }
        return lsLayout;
    }

    private void addNewInsRoom() {
        try {
            if (getLsLayout() != null && getLsLayout().isEmpty()) {
                Toast.makeText(getBaseContext(), "No additional Area can be added to this Inspection.", Toast.LENGTH_LONG).show();
                return;
            }

            String[] options;
            options = new String[getLsLayout().size()];
            //options = new String[insItem.size()];
            for (int i = 0, size = getLsLayout().size(); i < size; i++) {
                options[i] = getLsLayout().get(i).sName;
            }

            new MaterialDialog.Builder(this)
                    .title(R.string.alert_title_action)
                    .items(options)
                    .itemsCallbackSingleChoice(-1, (dialog, view, which, text) -> {
                        SelectRoom(which);
                        return true;
                    })
                    .negativeText(R.string.md_cancel_label)
                    .show();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Ins_Edit_3rd.addNewInsRoom", ex, this);
        }
    }

    private void SelectRoom(int which) {
        try {
            ai_Layout oParentLayout = getLsLayout().get(which);
            List<ai_InsItem> arrInsItem = CommonDB.GetChildInsItem(0, insId);

            if (null == arrInsItem || arrInsItem.isEmpty()) return;

            int iCount = 1;
            for (ai_InsItem oTemp : arrInsItem) {
                if (oTemp.iSLayoutID == oParentLayout.iSLayoutID) iCount ++;
            }

            List<ai_Layout> lsChildLayout;
            if (CommonHelper.bRequestInspection(oInspection.sCustomTwo)) {
                lsChildLayout = CommonRequestInspection.LoadLayout(oParentLayout.iSLayoutID, CommonHelper.sRequestInspection_FileName(oInspection.sCustomTwo));
            } else if (CommonValidate.bExternalInspection(oInspection.sCustomTwo)) {
                lsChildLayout = CommonRequestInspection.LoadLayout(oParentLayout.iSLayoutID, CommonHelper.sRequestInspection_FileName(oInspection.sCustomTwo) );
            } else {
                lsChildLayout = CommonDB.GetChildLayout(oParentLayout.iSLayoutID, this);
            }

            if (oInspection.getEnumInsType() == ai_Enum_Config.SI_Inspection_Type.Simple) {
                String sDefaultValue1 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVOneConfig);
                String sDefaultValue2 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVTwoConfig);
                String sDefaultValue3 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVThreeConfig);
                String sDefaultValue4 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVFourConfig);
                String sDefaultValue5 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVFiveConfig);
                String sDefaultValue6 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sSVSixConfig);

                ai_InsItem oInsItem = new ai_InsItem(
                        0, insId, oParentLayout.iSLayoutID, oParentLayout.sName + (iCount > 1 ? " " + iCount : ""),
                        sDefaultValue1, sDefaultValue2, sDefaultValue3, sDefaultValue4, sDefaultValue5, sDefaultValue6,
                        oParentLayout.sQType, oParentLayout.sSVOneConfig,
                        oParentLayout.sSVTwoConfig, oParentLayout.sSVThreeConfig, oParentLayout.sSVFourConfig,
                        oParentLayout.sSVFiveConfig, oParentLayout.sSVSixConfig, oParentLayout.sSConfig,
                        false, arrInsItem.size() + 1, 0, "", "", "");
                oInsItem.save();

                if (oInsItem.sQType.equalsIgnoreCase(ai_Enum_Config.SI_QUESTION_TYPE_S)
                        || oInsItem.sQType.equalsIgnoreCase(ai_Enum_Config.SI_QUESTION_TYPE_V)) {
                    long lPInsItemID = oInsItem.getId();
                    int iPInsItemID = (int)lPInsItemID;

                    for (int j = 0; j < lsChildLayout.size(); j ++){
                        ai_Layout oChildLayout = lsChildLayout.get(j);
                        String sValue1 = StringUtils.isEmpty(oChildLayout.sFVOneConfig) ?
                                sDefaultValue1 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVOneConfig);
                        String sValue2 = StringUtils.isEmpty(oChildLayout.sFVTwoConfig) ?
                                sDefaultValue2 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVTwoConfig);
                        String sValue3 = StringUtils.isEmpty(oChildLayout.sFVThreeConfig) ?
                                sDefaultValue3 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVThreeConfig);
                        String sValue4 = StringUtils.isEmpty(oChildLayout.sFVFourConfig) ?
                                sDefaultValue4 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVFourConfig);
                        String sValue5 = StringUtils.isEmpty(oChildLayout.sFVFiveConfig) ?
                                sDefaultValue5 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVFiveConfig);
                        String sValue6 = StringUtils.isEmpty(oChildLayout.sFVSixConfig) ?
                                sDefaultValue6 : CommonHelper.GetDefaultCheckboxValue(oChildLayout.sFVSixConfig);
                        ai_InsItem oChildInsItem = new ai_InsItem(iPInsItemID, insId, oChildLayout.iSLayoutID, oChildLayout.sName,
                                sValue1, sValue2, sValue3, sValue4, sValue5, sValue6,
                                oChildLayout.sQType, oChildLayout.sFVOneConfig,
                                oChildLayout.sFVTwoConfig, oChildLayout.sFVThreeConfig, oChildLayout.sFVFourConfig,
                                oChildLayout.sFVFiveConfig, oChildLayout.sFVSixConfig, oChildLayout.sFConfig,
                                false, j + 1, 0, "", "", "");
                        oChildInsItem.save();
                    }
                }
            } else {
                String sDefaultValue1 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVOneConfig);
                String sDefaultValue2 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVTwoConfig);
                String sDefaultValue3 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVThreeConfig);
                String sDefaultValue4 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVFourConfig);
                String sDefaultValue5 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVFiveConfig);
                String sDefaultValue6 = CommonHelper.GetDefaultCheckboxValue(oParentLayout.sFVSixConfig);
                ai_InsItem oInsItem = new ai_InsItem(
                        0, insId, oParentLayout.iSLayoutID, oParentLayout.sName + (iCount > 1 ? " " + iCount : ""),
                        sDefaultValue1, sDefaultValue2, sDefaultValue3, sDefaultValue4, sDefaultValue5, sDefaultValue6,
                        oParentLayout.sQType, oParentLayout.sFVOneConfig,
                        oParentLayout.sFVTwoConfig, oParentLayout.sFVThreeConfig, oParentLayout.sFVFourConfig,
                        oParentLayout.sFVFiveConfig, oParentLayout.sFVSixConfig, oParentLayout.sFConfig,
                        false, arrInsItem.size() + 1, 0, "", "", ""
                );
                oInsItem.save();
                CommonDB.AddChildLayout(oInsItem, this, oInsItem.iInsID, lsChildLayout);
            }

            displayInsItems();

        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_inspection.SelectRoom", ex, this);
        }
    }
}
