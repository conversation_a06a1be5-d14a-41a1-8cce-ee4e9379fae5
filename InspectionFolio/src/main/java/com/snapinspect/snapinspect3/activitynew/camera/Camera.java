package com.snapinspect.snapinspect3.activitynew.camera;

import android.annotation.SuppressLint;
import android.graphics.ImageFormat;
import android.graphics.Point;
import android.graphics.Rect;
import android.graphics.SurfaceTexture;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCaptureSession;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraDevice;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.CameraMetadata;
import android.hardware.camera2.CaptureFailure;
import android.hardware.camera2.CaptureRequest;
import android.hardware.camera2.CaptureResult;
import android.hardware.camera2.TotalCaptureResult;
import android.hardware.camera2.params.MeteringRectangle;
import android.media.Image;
import android.media.ImageReader;
import android.os.Build;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Message;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import android.util.Range;
import android.util.Size;
import android.view.Surface;

import org.jetbrains.annotations.NotNull;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

import timber.log.Timber;

@RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
public class Camera {

    private static final Object LOCK = new Object();
    private static final int MANUAL_POINT_AREA_SIZE = 200;
    private static volatile Camera instance;
    private CaptureRequest.Builder previewRequestBuilder;
    private Integer aeState;
    private Integer afState;
    private Camera.State lastCameraState;
    private final boolean takePhotoImmediately = false;
    private Rect manualMeteringRect;
    private Size textureSize;
    private Rect zoom;

    private Rect mCropRegion;

    private MeteringRectangle[] mAFRegions = AutoFocusHelper.getZeroWeightRegion();

    private MeteringRectangle[] mAERegions = AutoFocusHelper.getZeroWeightRegion();
    /**
     * Whether the camera is manual focusing now
     */
    private boolean mIsManualFocusing;

    /**
     * The current camera auto focus mode
     */
    private final boolean mAutoFocus = true;

    /**
     * Whether the current camera device supports auto focus or not.
     */
    private boolean mAutoFocusSupported = true;

    private static final int MSG_CAPTURE_PICTURE_WHEN_FOCUS_TIMEOUT = 100;

    private final int mBrightness = 50;

    private enum State {
        PREVIEW,
        WAITING_LOCK,
        WAITING_PRECAPTURE,
        WAITING_NON_PRECAPTURE,
        TAKEN
    }

    public enum CameraType {
        Front,
        Back
    }

    public enum FlashMode {
        Auto,
        FlashOn,
        FlashOff
    }

    public enum Rotation {
        ROTATION_0(Surface.ROTATION_0, 0),
        ROTATION_90(Surface.ROTATION_90, 90),
        ROTATION_180(Surface.ROTATION_180, 180),
        ROTATION_270(Surface.ROTATION_270, 270);
        public final int index;
        public final int degree;

        Rotation(int index, int degree) {
            this.index = index;
            this.degree = degree;
        }
    }

    public enum WhiteBalance {
        AUTO("auto"),
        SUNNY("sunny"),
        CLOUDY("cloudy");

        private final String label;

        WhiteBalance(String label) {
            this.label = label;
        }

        public String getLabel() {
            return this.label;
        }

        public static List<String> getLabels() {
            List<String> labels = new ArrayList<>();
            for (WhiteBalance whiteBalance : WhiteBalance.values()) {
                labels.add(whiteBalance.getLabel());
            }
            return labels;
        }
    }

    public enum Exposure {
        PLUS_2(2, "+2 EV"),
        PLUS_1(1, "+1 EV"),
        ZERO(0, "0 EV"),
        MINUS_1(-1, "-1 EV"),
        MINUS_2(-2, "-2 EV");

        public final int value;
        public final String label;

        Exposure(int value, String label) {
            this.value = value;
            this.label = label;
        }

        public static List<String> getLabels() {
            List<String> labels = new ArrayList<>();
            for (Exposure exposure : Exposure.values()) {
                labels.add(exposure.label);
            }
            return labels;
        }
    }

    public enum Focus {
        AUTO("auto"),
        INFINITY("infinity");

        private final String label;

        Focus(String label) {
            this.label = label;
        }

        public String getLabel() {
            return this.label;
        }

        public static List<String> getLabels() {
            List<String> labels = new ArrayList<>();
            for (Focus focus : Focus.values()) {
                labels.add(focus.getLabel());
            }
            return labels;
        }
    }

    public enum Metering {
        MATRIX("matrix"),
        CENTER("center"),
        SPOT("spot"),
        MANUAL("manual");

        private final String label;

        Metering(String label) {
            this.label = label;
        }

        public String getLabel() {
            return this.label;
        }

        public static List<String> getLabels() {
            List<String> labels = new ArrayList<>();
            for (Metering metering : Metering.values()) {
                labels.add(metering.getLabel());
            }
            return labels;
        }
    }

    public interface CapturePhotoListener {

        void onImageAvailable(byte[] data);

        void onError(String message);
    }

    private CaptureRequest.Builder captureBuilder;
    private final Semaphore openLock = new Semaphore(1);
    private CapturePhotoListener capturingListener;
    private HandlerThread backgroundThread;
    private final CameraManager cameraManager;
    private Handler backgroundHandler;
    private String cameraId;

    private CameraCharacteristics cameraCharacteristics;
    private CameraCaptureSession captureSession;
    private SurfaceTexture surfaceTexture;
    private CameraDevice cameraDevice;
    private ImageReader imageReader;
    private Surface surface;

    private int aeMode = CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH;
    private int curCameraId = CameraMetadata.LENS_FACING_BACK;
    private int rotation = Rotation.ROTATION_90.degree;
    private Exposure exposure = Exposure.ZERO;
    private WhiteBalance wbMode = WhiteBalance.AUTO;
    private Metering metering = Metering.MATRIX;
    private State state = State.PREVIEW;
    private Focus focus = Focus.AUTO;
    private FlashMode curFlashMode = FlashMode.FlashOff;

    private final CameraDevice.StateCallback cameraStateCallback = new CameraDevice.StateCallback() {
        @Override
        public void onOpened(@NonNull CameraDevice camera) {
            openLock.release();
            cameraDevice = camera;
            Timber.d("Camera " + cameraDevice.getId() + " opened");
            start(surface);
        }


        @Override
        public void onDisconnected(@NonNull CameraDevice camera) {
            openLock.release();
            camera.close();
            cameraDevice = null;
            Timber.d(" camera " + camera.getId() + " disconnected");
        }

        @Override
        public void onClosed(@NonNull CameraDevice camera) {
            Timber.d("Camera " + camera.getId() + " closed.");
        }

        @SuppressLint("DefaultLocale")
        @Override
        public void onError(@NonNull CameraDevice camera, int error) {
            onDisconnected(cameraDevice);
            Timber.d("Camera error code %s", error);
            capturingListener.onError(String.format("Camera state error. Camera id: %s, error code: %d", camera.getId(), error));
        }
    };

    private final CameraCaptureSession.StateCallback captureSessionStateCallback = new CameraCaptureSession.StateCallback() {
        @Override
        public void onConfigured(@NonNull CameraCaptureSession session) {
            if (cameraDevice == null) {
                Timber.e("camera Device is null");
                return;
            }
            captureSession = session;
            startPreview();
        }

        @Override
        public void onConfigureFailed(@NonNull CameraCaptureSession session) {
            Timber.d("Configure Failed: %s", cameraDevice.getId());
            capturingListener.onError(String.format("Configure Failed:. Camera id: %s", cameraDevice.getId()));
        }
    };

    public static Camera getInstance(CameraManager manager) {
        if (instance == null) {
            synchronized (LOCK) {
                if (instance == null) {
                    instance = new Camera(manager);
                }
            }
        }
        return instance;
    }

    private Camera(CameraManager manager) {
        cameraManager = manager;
        cameraId = getCameraId(cameraManager);
        try {
            cameraCharacteristics = cameraManager.getCameraCharacteristics(cameraId);
        } catch (CameraAccessException e) {
            Timber.e(e);
            capturingListener.onError(e.getMessage());
        }
    }


    public String getCameraId(CameraType type) {
        if (cameraManager == null) {
            return null;
        }

        if (type == CameraType.Front) {
            curCameraId = CameraMetadata.LENS_FACING_FRONT;
        } else if (type == CameraType.Back) {
            curCameraId = CameraMetadata.LENS_FACING_BACK;
        }

        if (getCameraId(cameraManager) != null) {
            return getCameraId(cameraManager);
        }

        return null;
    }

    public void switchCamera(String cameraId) {
        this.cameraId = cameraId;
        try {
            cameraCharacteristics = cameraManager.getCameraCharacteristics(cameraId);
        } catch (CameraAccessException e) {
            Timber.e(e);
            capturingListener.onError(e.getMessage());
        }
    }

    private CaptureRequest.Builder createPreviewRequestBuilder() {
        try {
            previewRequestBuilder = cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_PREVIEW);
            previewRequestBuilder.set(CaptureRequest.CONTROL_VIDEO_STABILIZATION_MODE,
                    CameraMetadata.CONTROL_VIDEO_STABILIZATION_MODE_ON);
            previewRequestBuilder.set(CaptureRequest.LENS_OPTICAL_STABILIZATION_MODE,
                    CameraMetadata.LENS_OPTICAL_STABILIZATION_MODE_ON);
            previewRequestBuilder.addTarget(surface);
            setupCaptureRequest(previewRequestBuilder);
        } catch (CameraAccessException e) {
            Timber.e(e);
            capturingListener.onError(e.getMessage());
        }
        return previewRequestBuilder;
    }

    private void setupCaptureRequest(@NonNull CaptureRequest.Builder builder) {
        captureBuilder = builder;

//        setupFocus(builder); // done
        updateAutoFocus();
        setupExposure(builder); //done
        setupWhiteBalance(builder); // done
        setBrightness(mBrightness);

        //Zoom
        if (zoom != null) {
            captureBuilder.set(CaptureRequest.SCALER_CROP_REGION, zoom);
        }
    }


    public int getJpegOrientation(int deviceOrientation) {
        try {
            if (deviceOrientation == android.view.OrientationEventListener.ORIENTATION_UNKNOWN)
                return 0;
            int sensorOrientation = cameraCharacteristics.get(CameraCharacteristics.SENSOR_ORIENTATION);

            // Round device orientation to a multiple of 90
            deviceOrientation = (deviceOrientation + 45) / 90 * 90;

            // Reverse device orientation for front-facing cameras
            boolean facingFront = cameraCharacteristics.get(CameraCharacteristics.LENS_FACING) == CameraCharacteristics.LENS_FACING_FRONT;
            if (facingFront) deviceOrientation = -deviceOrientation;

            // Calculate desired JPEG orientation relative to camera orientation to make
            // the image upright relative to the device orientation
            int jpegOrientation = (sensorOrientation + deviceOrientation + 360) % 360;

            return jpegOrientation;
        } catch (Exception ex) {
            ex.printStackTrace();
        }

        return 0;
    }

    /**
     * @param builder CaptureRequest.Builder
     */
    private void setupExposure(@NonNull CaptureRequest.Builder builder) {
        Boolean available = cameraCharacteristics.get(CameraCharacteristics.FLASH_INFO_AVAILABLE);
        boolean mFlashSupported = available != null && available;

        if (mFlashSupported) {
            switch (curFlashMode) {
                default:
                case Auto:
                    builder.set(CaptureRequest.CONTROL_AE_MODE,
                            CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH);
                    builder.set(CaptureRequest.FLASH_MODE,
                            CaptureRequest.FLASH_MODE_OFF);
                    break;
                case FlashOn:
                    builder.set(CaptureRequest.CONTROL_AE_MODE,
                            CameraMetadata.CONTROL_AE_MODE_ON);
                    builder.set(CaptureRequest.FLASH_MODE,
                            CameraMetadata.FLASH_MODE_TORCH);
                    break;
                case FlashOff:
                    builder.set(CaptureRequest.CONTROL_AE_MODE,
                            CaptureRequest.CONTROL_AE_MODE_ON);
                    builder.set(CaptureRequest.FLASH_MODE,
                            CaptureRequest.FLASH_MODE_OFF);
                    break;

            }
        }

//        if (mFlashSupported) {
//            builder.set(CaptureRequest.CONTROL_AE_MODE, aeMode);
//        } else {
//            Timber.w("Camera does'nt support auto exposure.");
//        }
    }

    private boolean setupExposure(int mode) {
        Timber.e("updatePreview");
        aeMode = mode;

        return true;
    }

    public void setBrightness(int value) {
        try {
            Range<Integer> compensationRange =
                    cameraCharacteristics.get(CameraCharacteristics.CONTROL_AE_COMPENSATION_RANGE);
            if (compensationRange != null) {
                int minCompensationRange = compensationRange.getLower();
                int maxCompensationRange = compensationRange.getUpper();

                int brightness = (int) (minCompensationRange + (maxCompensationRange - minCompensationRange) * (value / 100f));
                previewRequestBuilder.set(CaptureRequest.CONTROL_AE_EXPOSURE_COMPENSATION, brightness);
                applySettings();
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    public int getCurrentBrightness() {
        return mBrightness;
    }

    private void applySettings() {
        try {
            captureSession.setRepeatingRequest(previewRequestBuilder.build(), null, backgroundHandler);
        } catch (CameraAccessException e) {
            e.printStackTrace();
        }
    }

    private void updateFlash() {
        if (previewRequestBuilder != null) {
            setupExposure(previewRequestBuilder);
            if (captureSession != null) {
                try {
                    captureSession.setRepeatingRequest(previewRequestBuilder.build(),
                            captureCallback, backgroundHandler);
                } catch (CameraAccessException e) {

                }
            }
        }
    }

    public boolean setFlashAuto() {
        curFlashMode = FlashMode.Auto;

        updateFlash();
        return true;
//        return setupExposure(CaptureRequest.CONTROL_AE_MODE_ON_AUTO_FLASH);
    }

    public boolean setFlashAlways() {
        curFlashMode = FlashMode.FlashOn;

        updateFlash();
        return true;
//        return setupExposure(CaptureRequest.CONTROL_AE_MODE_ON_ALWAYS_FLASH);
    }

    public boolean setFlashOff() {
//            return setupExposure(CaptureRequest.CONTROL_AE_MODE_ON);
        curFlashMode = FlashMode.FlashOff;

        updateFlash();
        return true;
    }

    private void setupWhiteBalance(@NonNull CaptureRequest.Builder builder) {
        Timber.d("White balance: %s", wbMode);
        switch (wbMode) {
            case AUTO:
                if (CameraUtils.isAutoWhiteBalanceSupported(cameraCharacteristics)) {
                    builder.set(CaptureRequest.CONTROL_AWB_MODE, CaptureRequest.CONTROL_AWB_MODE_AUTO);
                } else {
                    Timber.d("Camera does'nt support auto white balance.");
                }
                break;
            case SUNNY:
                if (CameraUtils.isSupported(cameraCharacteristics, CameraCharacteristics.CONTROL_AWB_AVAILABLE_MODES, CameraCharacteristics.CONTROL_AWB_MODE_DAYLIGHT)) {
                    builder.set(CaptureRequest.CONTROL_AWB_MODE, CaptureRequest.CONTROL_AWB_MODE_DAYLIGHT);
                } else {
                    Timber.d("Camera does'nt support Sunny(CONTROL_AWB_MODE_DAYLIGHT).");
                }

                break;
            case CLOUDY:
                if (CameraUtils.isSupported(cameraCharacteristics, CameraCharacteristics.CONTROL_AWB_AVAILABLE_MODES, CameraCharacteristics.CONTROL_AWB_MODE_CLOUDY_DAYLIGHT)) {
                    builder.set(CaptureRequest.CONTROL_AWB_MODE, CaptureRequest.CONTROL_AWB_MODE_CLOUDY_DAYLIGHT);
                } else {
                    Timber.d("Camera does'nt support Cloudy(CONTROL_AWB_MODE_CLOUDY_DAYLIGHT).");
                }
                break;
        }
    }

    private void setupFocus(@NonNull CaptureRequest.Builder builder) {
        if (CameraUtils.isContinuousAutoFocusSupported(cameraCharacteristics)) {
            builder.set(CaptureRequest.CONTROL_AF_MODE,
                    CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
        } else {
            builder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_AUTO);
        }
    }

    private void startPreview() {
        try {
            Timber.e("startPreview");
            state = State.PREVIEW;
            previewRequestBuilder = createPreviewRequestBuilder();
            captureSession.setRepeatingRequest(
                    previewRequestBuilder.build(), captureCallback, backgroundHandler);
        } catch (CameraAccessException e2) {
            Timber.e("CameraAccessException" + e2.getLocalizedMessage());
            capturingListener.onError(e2.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void takePhoto() {
        try {
            if (cameraDevice == null) {
                throw new IllegalStateException("Camera device not ready");
            }
        } catch (IllegalStateException e) {
            capturingListener.onError(e.getMessage());
        }

        if (!mIsManualFocusing && mAutoFocus && mAutoFocusSupported) {
            Timber.d("takePicture lockFocus");

            capturePictureWhenFocusTimeout(); //Sometimes, camera do not focus in some devices.
            lockFocus();
        } else {
            Timber.d("takePicture captureStill");
            captureStillPicture();
        }
    }

    /**
     * Capture picture when auto focus timeout
     */
    private void capturePictureWhenFocusTimeout() {
        if (backgroundHandler != null) {
            backgroundHandler.sendEmptyMessageDelayed(MSG_CAPTURE_PICTURE_WHEN_FOCUS_TIMEOUT,
                    CameraConstants.AUTO_FOCUS_TIMEOUT_MS);
        }
    }

    /**
     * Remove capture message, because auto focus work correctly.
     */
    private void removeCaptureMessage() {
        if (backgroundHandler != null) {
            backgroundHandler.removeMessages(MSG_CAPTURE_PICTURE_WHEN_FOCUS_TIMEOUT);
        }
    }

    public void setSurface(Surface surface) {
        this.surface = surface;
    }

    @SuppressLint({"MissingPermission"})
    public void open() {
        try {
            if (!openLock.tryAcquire(2500, TimeUnit.MILLISECONDS)) {
                throw new RuntimeException("Time out waiting to lock camera opening.");
            }

            //   Timber.d("Opening camera: %s", cameraId);
            //   startBackgroundThread();
            cameraManager.openCamera(cameraId, cameraStateCallback, backgroundHandler);
        } catch (CameraAccessException | InterruptedException e) {
            Timber.e(e);
            capturingListener.onError(e.getMessage());
        }
    }

    private String getCameraId(@NotNull CameraManager manager) {
        try {
            for (String cameraId : manager.getCameraIdList()) {
                CameraCharacteristics cameraCharacteristics = manager.getCameraCharacteristics(cameraId);
                Integer facing = cameraCharacteristics.get(CameraCharacteristics.LENS_FACING);
                if (facing != null && facing == curCameraId) {
                    return cameraId;
                }
            }
        } catch (CameraAccessException e) {
            e.printStackTrace();
            Timber.e(e);
            capturingListener.onError(e.getMessage());
        }
        throw new IllegalStateException("Camera hasn't found.");
    }

    public void start(Surface surface) {
        this.surface = surface;
        try {
            Size size = null;
            zoom = null;

            Size largest = getPreviewSize(textureSize.getWidth() / (float) textureSize.getHeight());
            if (textureSize != null) {
                size = CameraUtils.chooseOptimalSize(
                        cameraCharacteristics, textureSize.getHeight(),
                        textureSize.getWidth(), largest);
//                size = CameraUtils.getCaptureSize(cameraCharacteristics, new CompareSizesByArea(), largest.getHeight(), largest.getWidth());
            } else {
                size = CameraUtils.getCaptureSize(cameraCharacteristics, new CompareSizesByArea());
            }


            imageReader = ImageReader.newInstance(size.getWidth(), size.getHeight(), ImageFormat.JPEG, 2);
            final List<Surface> outputSurfaces = new ArrayList<>();
            outputSurfaces.add(surface);
            outputSurfaces.add(imageReader.getSurface());

            if (imageReader != null) {
                imageReader.setOnImageAvailableListener(onImageAvailableListener, backgroundHandler);
            }

            // Check if the flash is supported.

            if (cameraDevice != null) {
                Timber.d("Creating session for camera %s", cameraDevice.getId());
                cameraDevice.createCaptureSession(outputSurfaces, captureSessionStateCallback, backgroundHandler);
            } else {
                Timber.d("start CameraDevice is null");
            }

            checkAutoFocusSupported();

            mCropRegion = AutoFocusHelper.cropRegionForZoom(cameraCharacteristics,
                    CameraConstants.ZOOM_REGION_DEFAULT);

        } catch (CameraAccessException e) {
            e.printStackTrace();
            Timber.e(e);
            capturingListener.onError(e.getMessage());
        }
    }

    private final CameraCaptureSession.CaptureCallback captureCallback = new CameraCaptureSession.CaptureCallback() {

        @Override
        public void onCaptureBufferLost(@NonNull CameraCaptureSession session, @NonNull CaptureRequest request, @NonNull Surface target, long frameNumber) {
            super.onCaptureBufferLost(session, request, target, frameNumber);
            Timber.e("onCaptureBufferLost");
        }

        @Override
        public void onCaptureFailed(@NonNull CameraCaptureSession session, @NonNull CaptureRequest request, @NonNull CaptureFailure failure) {
            super.onCaptureFailed(session, request, failure);
            Timber.e("onCaptureFailed");
        }

        @Override
        public void onCaptureSequenceAborted(@NonNull CameraCaptureSession session, int sequenceId) {
            super.onCaptureSequenceAborted(session, sequenceId);
            Timber.e("onCaptureSequenceAborted");
        }

        @Override
        public void onCaptureSequenceCompleted(@NonNull CameraCaptureSession session, int sequenceId, long frameNumber) {
            super.onCaptureSequenceCompleted(session, sequenceId, frameNumber);
            Timber.e("onCaptureSequenceCompleted");
        }

        @Override
        public void onCaptureStarted(@NonNull CameraCaptureSession session, @NonNull CaptureRequest request, long timestamp, long frameNumber) {
            super.onCaptureStarted(session, request, timestamp, frameNumber);
            Timber.e("onCaptureStarted");
        }

        private void process(CaptureResult result) {
            Timber.e("CameraCaptureSession captureCallback " + state);
            debugCameraState(result);
            switch (state) {
                case PREVIEW: {
//                    if (takePhotoImmediately) {
//                        takePhoto();
//                        takePhotoImmediately = false;
//                    }
                    break;
                }
                case WAITING_LOCK: {
                    Integer afState = result.get(CaptureResult.CONTROL_AF_STATE);
                    // Auto Focus state is not ready in the first place

                    if (afState == null) {
                        runPreCaptureSequence();
                    } else if (/*CaptureResult.CONTROL_AF_STATE_INACTIVE == afState || */
                            CaptureResult.CONTROL_AF_STATE_FOCUSED_LOCKED == afState ||
                                    CaptureResult.CONTROL_AF_STATE_NOT_FOCUSED_LOCKED == afState) {
                        // CONTROL_AE_STATE can be null on some devices
                        Integer aeState = result.get(CaptureResult.CONTROL_AE_STATE);
                        if (aeState == null || aeState == CaptureResult.CONTROL_AE_STATE_CONVERGED) {
                            captureStillPicture();
                        } else {
                            runPreCaptureSequence();
                        }
                    } else {
                        captureStillPicture();
                    }

                    break;
                }
                case WAITING_PRECAPTURE: {
                    Integer aeState = result.get(CaptureResult.CONTROL_AE_STATE);
                    if (aeState == null
                            || aeState == CaptureRequest.CONTROL_AE_STATE_PRECAPTURE
                            || aeState == CaptureRequest.CONTROL_AE_STATE_FLASH_REQUIRED
                            || aeState == CaptureRequest.CONTROL_AE_STATE_CONVERGED) {
                        state = State.WAITING_NON_PRECAPTURE;
                    }
                    break;
                }
                case WAITING_NON_PRECAPTURE: {
                    Integer aeState = result.get(CaptureResult.CONTROL_AE_STATE);
                    if (aeState == null || aeState != CaptureRequest.CONTROL_AE_STATE_PRECAPTURE) {
                        state = State.TAKEN;
                        captureStillPicture();
                    }
                    break;
                }
            }
        }

        @Override
        public void onCaptureProgressed(@NonNull CameraCaptureSession session,
                                        @NonNull CaptureRequest request,
                                        @NonNull CaptureResult partialResult) {
            Timber.e("onCaptureProgressed");
            process(partialResult);
        }

        @Override
        public void onCaptureCompleted(@NonNull CameraCaptureSession session, @NonNull CaptureRequest request, @NonNull TotalCaptureResult result) {
            Timber.e("onCaptureCompleted");
            process(result);
        }
    };

    private void debugCameraState(CaptureResult result) {
        Integer aeState = result.get(CaptureResult.CONTROL_AE_STATE);
        Integer afState = result.get(CaptureResult.CONTROL_AF_STATE);

        if (!Objects.equals(lastCameraState, state)) {
            Timber.d("Camera state: %s", state);
        }
        if (!Objects.equals(Camera.this.aeState, aeState)) {
            Timber.d("AE state: %s", aeState);
        }
        if (!Objects.equals(Camera.this.afState, afState)) {
            Timber.d("AF state: %s", afState);
        }

        lastCameraState = state;
        this.aeState = aeState;
        this.afState = afState;
    }

    private final ImageReader.OnImageAvailableListener onImageAvailableListener = new ImageReader.OnImageAvailableListener() {
        @Override
        public void onImageAvailable(ImageReader imReader) {
            Timber.d("Image is available");
            //final Image image = imReader.acquireLatestImage();
            final Image image = imReader.acquireNextImage();
            final ByteBuffer buffer = image.getPlanes()[0].getBuffer();
            final byte[] bytes = new byte[buffer.capacity()];
            buffer.get(bytes);
            image.close();
            capturingListener.onImageAvailable(bytes);
        }
    };

    private void runPreCaptureSequence() {
        try {
            state = State.WAITING_PRECAPTURE;
            previewRequestBuilder.set(CaptureRequest.CONTROL_AE_PRECAPTURE_TRIGGER,
                    CaptureRequest.CONTROL_AE_PRECAPTURE_TRIGGER_START);
            captureSession.capture(previewRequestBuilder.build(), captureCallback, backgroundHandler);
        } catch (Exception e) {
            e.printStackTrace();
            Timber.e(e);
            capturingListener.onError(e.getMessage());
        }
    }

    private void captureStillPicture() {
        state = State.TAKEN;
        try {
            removeCaptureMessage();

            if (cameraDevice == null) return;
            // This is the CaptureRequest.Builder that we use to take a picture.
            final CaptureRequest.Builder captureBuilder =
                    cameraDevice.createCaptureRequest(CameraDevice.TEMPLATE_STILL_CAPTURE);

            captureBuilder.addTarget(imageReader.getSurface());
            captureBuilder.addTarget(surface);

            setupExposure(captureBuilder); //done

            //Zoom
            if (zoom != null) {
                captureBuilder.set(CaptureRequest.SCALER_CROP_REGION, zoom);
            }

            setupCaptureRequest(captureBuilder);

            captureBuilder.set(CaptureRequest.JPEG_ORIENTATION, rotation);
            captureSession.stopRepeating();
            captureSession.capture(captureBuilder.build(), new CameraCaptureSession.CaptureCallback() {
                @Override
                public void onCaptureCompleted(
                        @NonNull CameraCaptureSession session,
                        @NonNull CaptureRequest request,
                        @NonNull TotalCaptureResult result) {
                    Timber.d("Done taking picture from camera %s", session.getDevice().getId());
                    unlockFocus();
                }

                @Override
                public void onCaptureFailed(@NonNull CameraCaptureSession session,
                                            @NonNull CaptureRequest request, @NonNull CaptureFailure failure) {
                    Timber.d("Done taking picture from camera %s", session.getDevice().getId());
                    unlockFocus();
                }
            }, backgroundHandler);
        } catch (CameraAccessException e) {
            e.printStackTrace();
            Timber.e(e);
            capturingListener.onError(e.getMessage());
        }
    }

    private void lockFocus() {
        try {
            state = State.WAITING_LOCK;
            previewRequestBuilder.set(CaptureRequest.CONTROL_AF_TRIGGER,
                    CaptureRequest.CONTROL_AF_TRIGGER_START);
            if (captureSession == null) return;
            captureSession.capture(previewRequestBuilder.build(), captureCallback, backgroundHandler);
        } catch (CameraAccessException e) {
            Timber.e(e);
            capturingListener.onError(e.getMessage());
        }
    }

    private void unlockFocus() {
        try {
            // Reset the auto-focus trigger
            previewRequestBuilder.set(CaptureRequest.CONTROL_AF_TRIGGER,
                    CameraMetadata.CONTROL_AF_TRIGGER_CANCEL);
            captureSession.capture(previewRequestBuilder.build(), captureCallback,
                    backgroundHandler);

            updateAutoFocus();
            setupExposure(previewRequestBuilder);
            // After this, the camera will go back to the normal state of preview.
            state = State.PREVIEW;
            previewRequestBuilder.set(CaptureRequest.CONTROL_AF_TRIGGER,
                    CaptureRequest.CONTROL_AF_TRIGGER_IDLE);
            captureSession.setRepeatingRequest(previewRequestBuilder.build(), captureCallback,
                    backgroundHandler);
        } catch (CameraAccessException e) {
            Timber.e(e);
            capturingListener.onError(e.getMessage());
        }
    }

    public void zoomLevel(Rect zoom) {
        this.zoom = zoom;

        try {
            setupCaptureRequest(previewRequestBuilder);
            captureSession
                    .setRepeatingRequest(previewRequestBuilder.build(), captureCallback, backgroundHandler);
        } catch (CameraAccessException e) {
            e.printStackTrace();
        } catch (NullPointerException ex) {
            ex.printStackTrace();
        }
    }

    public CameraCharacteristics getCameraCharacteristics() {
        return cameraCharacteristics;
    }

    public Rect texturePointToRect(Point point) {
        Rect rect = cameraCharacteristics.get(CameraCharacteristics.SENSOR_INFO_ACTIVE_ARRAY_SIZE);
        if (rect == null || textureSize == null || textureSize.getWidth() == 0 || textureSize.getHeight() == 0) {
            return null;
        }

        int right = rect.right;
        int bottom = rect.bottom;
        int ll = (point.x * right - MANUAL_POINT_AREA_SIZE) / textureSize.getWidth();
        int rr = (point.y * bottom - MANUAL_POINT_AREA_SIZE) / textureSize.getHeight();

        int focusLeft = clamp(ll, 0, right);
        int focusBottom = clamp(rr, 0, bottom);
        return new Rect(focusLeft, focusBottom, focusLeft + MANUAL_POINT_AREA_SIZE, focusBottom + MANUAL_POINT_AREA_SIZE);
    }

    public Point rectToTexturePoint(Rect rect) {
        Rect sensorRect = cameraCharacteristics.get(CameraCharacteristics.SENSOR_INFO_ACTIVE_ARRAY_SIZE);
        if (sensorRect == null || rect == null || textureSize == null)
            return null;

        int x = (rect.left * textureSize.getWidth() + MANUAL_POINT_AREA_SIZE) / sensorRect.right;
        int y = (rect.top * textureSize.getHeight() + MANUAL_POINT_AREA_SIZE) / sensorRect.bottom;

        int pointX = clamp(x, 0, textureSize.getWidth());
        int pointY = clamp(y, 0, textureSize.getHeight());
        return new Point(pointX, pointY);
    }

    public int clamp(int value, int min, int max) {
        if (value < min) {
            return min;
        } else if (value > max) {
            return max;
        }
        return value;
    }

    public Size getPreviewSize(float aspectRatio) {
        return CameraUtils.getPreviewSize(cameraCharacteristics, aspectRatio);
    }

    public void setListener(CapturePhotoListener listener) {
        this.capturingListener = listener;
    }

    public void stop() {
        closeCamera();
        stopBackgroundThread();
    }

    private void closeCamera() {
        try {
            openLock.acquire();
            if (captureSession != null) {
                captureSession.close();
                captureSession = null;
            }

            if (cameraDevice != null) {
                cameraDevice.close();
                cameraDevice = null;
            }

            if (surface != null) {
                surface.release();
                surface = null;
            }

            if (surfaceTexture != null) {
                surfaceTexture.release();
                surfaceTexture = null;
            }

            if (imageReader != null) {
                imageReader.close();
                imageReader = null;
            }
        } catch (InterruptedException e) {
            e.printStackTrace();
            Timber.e(e);
        } finally {
            openLock.release();
        }
    }

    private void startBackgroundThread() {
        if (backgroundThread != null) return;
        backgroundThread = new HandlerThread("CameraBackground");
        backgroundThread.start();
        backgroundHandler = new Handler(backgroundThread.getLooper()) {
            @Override
            public void handleMessage(Message msg) {
                super.handleMessage(msg);
                switch (msg.what) {
                    case MSG_CAPTURE_PICTURE_WHEN_FOCUS_TIMEOUT:
                        state = State.TAKEN;
                        captureStillPicture();
                        break;
                    default:
                        break;
                }

            }
        };
    }

    private void stopBackgroundThread() {
        if (backgroundThread != null) {
            backgroundThread.quitSafely();
            //backgroundThread.join();
            backgroundThread = null;
        }
        if (backgroundHandler != null) {
            backgroundHandler = null;
        }
    }

    public void setManualFocus(float x, float y) {
        if (previewRequestBuilder != null) {
            mIsManualFocusing = true;
            updateManualFocus(x, y);
            if (captureSession != null) {
                try {
                    previewRequestBuilder.set(CaptureRequest.CONTROL_AF_TRIGGER,
                            CaptureRequest.CONTROL_AF_TRIGGER_START);
                    captureSession.capture(previewRequestBuilder.build(), null, backgroundHandler);
                    previewRequestBuilder.set(CaptureRequest.CONTROL_AF_TRIGGER,
                            CaptureRequest.CONTROL_AF_TRIGGER_IDLE);
                    captureSession.setRepeatingRequest(previewRequestBuilder.build(),
                            null, backgroundHandler);
                } catch (CameraAccessException | IllegalStateException e) {
                    Timber.e("Failed to set manual focus.");
                }
            }
            resumeAutoFocusAfterManualFocus();
        }
    }

    /**
     * Updates the internal state of manual focus.
     */
    void updateManualFocus(float x, float y) {
        @SuppressWarnings("ConstantConditions")
        int sensorOrientation = cameraCharacteristics.get(CameraCharacteristics.SENSOR_ORIENTATION);
        mAFRegions = AutoFocusHelper.afRegionsForNormalizedCoord(x, y, mCropRegion, sensorOrientation);
        mAERegions = AutoFocusHelper.aeRegionsForNormalizedCoord(x, y, mCropRegion, sensorOrientation);
        previewRequestBuilder.set(CaptureRequest.CONTROL_AF_REGIONS, mAFRegions);
        previewRequestBuilder.set(CaptureRequest.CONTROL_AE_REGIONS, mAERegions);
        previewRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE, CaptureRequest.CONTROL_AF_MODE_AUTO);
    }

    private void resumeAutoFocusAfterManualFocus() {
        if (backgroundHandler == null) return;
        backgroundHandler.removeCallbacks(mAutoFocusRunnable);
        backgroundHandler.postDelayed(mAutoFocusRunnable, CameraConstants.FOCUS_HOLD_MILLIS);
    }

    private final Runnable mAutoFocusRunnable = new Runnable() {
        @Override
        public void run() {
            if (previewRequestBuilder != null) {
                mIsManualFocusing = false;
                updateAutoFocus();
                if (captureSession != null) {
                    try {
                        captureSession.setRepeatingRequest(previewRequestBuilder.build(),
                                captureCallback, backgroundHandler);
                    } catch (CameraAccessException e) {
                        Timber.e("Failed to set manual focus." + e);
                    }
                }
            }
        }
    };

    /**
     * Updates the internal state of auto-focus to {@link #mAutoFocus}.
     */
    void updateAutoFocus() {
        if (mAutoFocus) {
            if (!mAutoFocusSupported) {
                previewRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE,
                        CaptureRequest.CONTROL_AF_MODE_OFF);
            } else {
                previewRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE,
                        CaptureRequest.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
            }
        } else {
            previewRequestBuilder.set(CaptureRequest.CONTROL_AF_MODE,
                    CaptureRequest.CONTROL_AF_MODE_OFF);
        }
        mAFRegions = AutoFocusHelper.getZeroWeightRegion();
        mAERegions = AutoFocusHelper.getZeroWeightRegion();
        previewRequestBuilder.set(CaptureRequest.CONTROL_AF_REGIONS, mAFRegions);
        previewRequestBuilder.set(CaptureRequest.CONTROL_AE_REGIONS, mAERegions);
    }

    /**
     * Check if the auto focus is supported.
     */
    private void checkAutoFocusSupported() {
        int[] modes = cameraCharacteristics.get(CameraCharacteristics.CONTROL_AF_AVAILABLE_MODES);
        mAutoFocusSupported = !(modes == null || modes.length == 0 ||
                (modes.length == 1 && modes[0] == CameraCharacteristics.CONTROL_AF_MODE_OFF));
    }

    public void setRotation(int rotation) {
        this.rotation = rotation;
    }

    public void setZoom(Rect zoom) {
        this.zoom = zoom;
    }

    public void setFocus(Focus focus) {
        this.focus = focus;
    }

    public void setMetering(Metering metering) {
        this.metering = metering;
    }

    public void setManualMeteringRect(Rect rect) {
        manualMeteringRect = rect;
    }

    public void setExposure(Exposure exposure) {
        this.exposure = exposure;
    }

    public void setTextureSize(Size size) {
        textureSize = size;
    }

    public void setWhiteBalance(WhiteBalance whiteBalance) {
        wbMode = whiteBalance;
    }

    public CaptureRequest.Builder getPreviewRequestBuilder() {
        return previewRequestBuilder;
    }

    public CameraCaptureSession getCaptureSession() {
        return captureSession;
    }

    public Size getCaptureSize() {
        return CameraUtils.getCaptureSize(cameraCharacteristics, new CompareSizesByArea());
    }

    public Integer getSensorOrientation() {
        return cameraCharacteristics.get(CameraCharacteristics.SENSOR_ORIENTATION);
    }

    public Boolean getFlashSupported() {
        Boolean flash = cameraCharacteristics.get(CameraCharacteristics.FLASH_INFO_AVAILABLE);
        return (flash != null && flash);
    }

    public Size chooseOptimalSize(
            int textureViewWidth, int textureViewHeight,
            int maxWidth, int maxHeight, Size aspectRatio) {
        return CameraUtils.chooseOptimalSize(
                cameraCharacteristics, textureViewWidth,
                textureViewHeight, aspectRatio);
    }
}