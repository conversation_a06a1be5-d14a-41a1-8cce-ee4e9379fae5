package com.snapinspect.snapinspect3.activitynew.Home;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.*;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.afollestad.materialdialogs.MaterialDialog;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.QRScan.QRScannerActivity;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_Inspection;
import com.snapinspect.snapinspect3.activity.*;
import com.snapinspect.snapinspect3.activitynew.Edit.if_UpdateAsset_2nd;
import com.snapinspect.snapinspect3.activitynew.Inbox.if_Inbox;
import com.snapinspect.snapinspect3.activitynew.Projects.frag_Projects;
import com.snapinspect.snapinspect3.activitynew.fragments.ItemSelectionDialog;
import com.snapinspect.snapinspect3.activitynew.fragments.InsTypeSelection;
import com.snapinspect.snapinspect3.activitynew.tasks.TasksFragment;
import com.snapinspect.snapinspect3.app.App;
import com.snapinspect.snapinspect3.util.*;

import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.PermissionUtils;
import com.snapinspect.snapinspect3.util.StringUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.*;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static com.snapinspect.snapinspect3.activity.frag_inspections.Segment.Uploaded;

public class if_HomeTab extends FragmentActivity implements View.OnClickListener {

    public enum FRAGMENT_TAB {
        ASSETS,
        INSPECTIONS,
        SCHEDULES,
        REQUEST_INSPECTION,
        PROJECTS
    }

    private static final String KEY_SELECTED_FRAGMENT_INDEX = "fragment_index";

    private Button btnSetting;
    private Button btnMenu;
    private Button btnSync;
    private Button btnAdd;
    private TextView tvTitle;
    private MaterialDialog mProgressDialog;
    private boolean isSyncing = false;
    private int selectedMenuItemId = 0;
    private Animation animRotate;

    private BottomNavigationView navigation;

    private frag_assets mFragAssets;
    private frag_inspections mFragInspections;
    private frag_NewSchedules mFragNewSchedules;
    private frag_schedules mFragSchedules;
    private frag_RequestIns mFragRequestIns;
    private frag_Projects mFragProjects;
    private TasksFragment mFragTasks;

    private List<ai_InsType> lsInsType;
    private boolean bInitialSync = false;

    private frag_assets getFragAssets() {
        if (mFragAssets == null) {
            mFragAssets = new frag_assets();
            mFragAssets.setListener(title -> tvTitle.setText(title));
        }
        return mFragAssets;
    }

    private frag_inspections getFragInspections() {
        if (mFragInspections == null) mFragInspections = new frag_inspections();
        return mFragInspections;
    }

    private frag_NewSchedules getFragNewSchedules() {
        if (mFragNewSchedules == null) mFragNewSchedules = new frag_NewSchedules();
        return mFragNewSchedules;
    }

    private frag_schedules getFragSchedules() {
        if (mFragSchedules == null) mFragSchedules = new frag_schedules();
        return mFragSchedules;
    }

    private frag_RequestIns getFragRequestIns() {
        if (mFragRequestIns == null) mFragRequestIns = new frag_RequestIns();
        return mFragRequestIns;
    }

    private TasksFragment getFragTasks() {
        if (mFragTasks == null) mFragTasks = new TasksFragment();
        return mFragTasks;
	}

    private frag_Projects getFragProjects() {
        if (mFragProjects == null) mFragProjects = new frag_Projects();
        return mFragProjects;
    }

    private final BottomNavigationView.OnNavigationItemSelectedListener mOnNavigationItemSelectedListener = item -> {
        selectedMenuItemId = item.getItemId();
        selectFragment(item, true);
        return false;
    };

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_home_tab);

        tvTitle = findViewById(R.id.home_tab_tv_title);
        btnSetting = findViewById(R.id.home_tab_btn_setting);
        btnMenu = findViewById(R.id.home_tab_btn_menu);
        btnSync = findViewById(R.id.home_tab_btn_sync);
        btnAdd = findViewById(R.id.home_tab_btn_add);

        btnSetting.setOnClickListener(this);
        btnMenu.setOnClickListener(this);
        btnSync.setOnClickListener(this);
        btnAdd.setOnClickListener(this);

        navigation = findViewById(R.id.navigationView);
        navigation.setOnNavigationItemSelectedListener(mOnNavigationItemSelectedListener);

        // Register the request permission launcher
        PermissionUtils.registerRequestPermissionLauncher(this);

        ViewUtils.disableShiftMode(navigation);
        createDatabaseViews();
        migrateExistFilesIfNeed();
        setupNavigationMenu();
        updateNavigationMenu();

        Menu menu = navigation.getMenu();
        if (null == savedInstanceState) {
            MenuItem firstMenuItem = menu.getItem(0);
            selectedMenuItemId = firstMenuItem.getItemId();
            selectFragment(firstMenuItem, true);
        } else {
            selectedMenuItemId = savedInstanceState.getInt(KEY_SELECTED_FRAGMENT_INDEX);
            MenuItem menuItem = menu.findItem(selectedMenuItemId);
            if (menuItem == null) menuItem = menu.getItem(0);
            selectFragment(menuItem, false);
        }

        animRotate = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.rotation);

        try {
            String sSyncDate = CommonHelper.GetPreferenceString(this, "sSyncDate");
            if (sSyncDate == null || sSyncDate.equals("") || sSyncDate.startsWith("1980-1-1")){
                bInitialSync = true;
            }
        } catch (Exception ec2) {

        }
        if (!bTenantTool()) {

            String bStartSync = CommonHelper.GetPreferenceString(this, "bStartSync");
            String bServerSync = CommonHelper.GetPreferenceString(this, Constants.Keys.bServerSync);
            if (bStartSync != null && bStartSync.equals("true")) {
                syncService();
                CommonValidate.Permission_Validate(if_HomeTab.this);
                CommonHelper.SavePreference(this, "bStartSync", "false");
            } else if (bServerSync != null && bServerSync.equals("1")) {
                syncService();
                CommonValidate.Permission_Validate(if_HomeTab.this);
                CommonHelper.SavePreference(this, Constants.Keys.bServerSync, "false");
            } else {
                try {
                    String dtStart = CommonHelper.GetPreferenceString(this, "sSyncDate");
                    Date dtLastSync = DateUtils.parse(dtStart, "yyyy-MM-dd'T'HH:mm:ss'Z'");
                    long lStart = dtLastSync != null ? dtLastSync.getTime() : 0;
                    long lEnd = (new Date()).getTime();
                    long lDiff = lEnd - lStart;
                    long iDifferent = lDiff / (1000 * 60 * 60 * 24);
                    if (iDifferent > 1) {
                        syncService();
                        CommonValidate.Permission_Validate(if_HomeTab.this);
                    }
                    //System.out.println(date);
                } catch (Exception e) {
                    //String cc = "bb";
                }
            }
        }

        // pause to parse the external link from intent if user login at first time and the data is not synced yet
        // resume it after the sync is done
        if (!bInitialSync || CommonHelper.isKioskMode(this)) {
            parseExternalLinks();
        }

        // Register the broadcast receiver
        LocalBroadcastManager.getInstance(this).registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (intent.getAction().equals(Constants.Broadcasts.sHomeTabSwitched)) {
                    int index = intent.getIntExtra(Constants.Extras.iTabIndex, -1);
                    if (index >= 0 && index < FRAGMENT_TAB.values().length) {
                        FRAGMENT_TAB tab = FRAGMENT_TAB.values()[index];
                        selectFragmentTab(tab);
                    }
                }
            }
        }, new IntentFilter(Constants.Broadcasts.sHomeTabSwitched));
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onResume() {
        super.onResume();
        RefreshInspection2ndTab();
        updateNavigationMenu();
    }

    @Override
    protected void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(KEY_SELECTED_FRAGMENT_INDEX, selectedMenuItemId);
    }

    @Override
    protected void onRestoreInstanceState(Bundle savedInstanceState) {
        super.onRestoreInstanceState(savedInstanceState);
        if (null != savedInstanceState) {
            selectedMenuItemId = savedInstanceState.getInt(KEY_SELECTED_FRAGMENT_INDEX);
        }
    }

    @SuppressLint("SetTextI18n")
    protected void selectFragment(MenuItem item, boolean pushFragment) {
        item.setChecked(true);

        CharSequence title = item.getTitle();
        tvTitle.setOnLongClickListener(v -> false);
        if (getResources().getString(R.string.assets).contentEquals(title)) {
            tvTitle.setText(R.string.assets);
            tvTitle.setOnClickListener(v -> getFragAssets().showAvailableAssetViews());
            btnSetting.setVisibility(View.VISIBLE);
            btnMenu.setVisibility(View.VISIBLE);
            btnSync.setVisibility(View.VISIBLE);
            boolean bDisableAdd = CommonJson.disableEditAsset(this);
            btnAdd.setVisibility(bDisableAdd ? View.INVISIBLE : View.VISIBLE);

            if (pushFragment) pushFragment(getFragAssets());
        } else if (getResources().getString(R.string.inspections).contentEquals(title)) {
            tvTitle.setText(R.string.inspections);
            btnSetting.setVisibility(View.VISIBLE);
            btnMenu.setVisibility(View.INVISIBLE);
            btnSync.setVisibility(View.INVISIBLE);
            // Permission Edit/Add Asset to prevent start new inspection icon on top right on the inspection tab.
            boolean bDisableAdd = CommonJson.disableNewInspection(this)
                    || CommonJson.disableEditAsset(this);
            btnAdd.setVisibility(bDisableAdd ? View.INVISIBLE : View.VISIBLE);
            if (pushFragment) pushFragment(getFragInspections());
        } else if (getResources().getString(R.string.schedules).contentEquals(title)) {
            tvTitle.setText(R.string.schedules);
            if (CommonHelper.isKioskMode(this)) {
                btnSetting.setVisibility(View.INVISIBLE);
                if (pushFragment) pushFragment(getFragSchedules());
            } else {
                btnSetting.setVisibility(View.VISIBLE);
                if (pushFragment) pushFragment(getFragNewSchedules());
            }
            btnMenu.setVisibility(View.INVISIBLE);
            btnSync.setVisibility(View.VISIBLE);
            btnAdd.setVisibility(View.INVISIBLE);
        } else if (getResources().getString(R.string.request_inspections).contentEquals(title)) {
            tvTitle.setText(R.string.inspections);
            tvTitle.setOnLongClickListener(v -> {
                showForceUploadDataPrompt();
                return true;
            });
            btnSetting.setVisibility(View.INVISIBLE);
            btnMenu.setVisibility(View.INVISIBLE);
            btnSync.setVisibility(View.INVISIBLE);
            btnAdd.setVisibility(View.VISIBLE);
            if (pushFragment) pushFragment(getFragRequestIns());
        } else if (getResources().getString(R.string.tasks).contentEquals(title)) {
            tvTitle.setText(R.string.my_tasks);
            tvTitle.setOnClickListener(v -> {
                if (getFragTasks() != null) getFragTasks().showFilterTasksOptions();
            });
            btnSetting.setVisibility(View.VISIBLE);
            btnMenu.setVisibility(View.INVISIBLE);
            btnSync.setVisibility(View.INVISIBLE);
            btnAdd.setVisibility(View.INVISIBLE);
            if (pushFragment) pushFragment(getFragTasks());
        } else if (getResources().getString(R.string.projects).contentEquals(title)) {
            tvTitle.setText(R.string.projects);
            btnSetting.setVisibility(View.VISIBLE);
            btnMenu.setVisibility(View.INVISIBLE);
            btnSync.setVisibility(View.INVISIBLE);
            btnAdd.setVisibility(View.INVISIBLE);
            if (pushFragment) pushFragment(getFragProjects());
        }
    }

    private void showForceUploadDataPrompt() {
        new MaterialDialog.Builder(this)
                .title(R.string.alert_title_message)
                .content(R.string.lbl_forceUploadInspectionPrompt)
                .positiveText("Yes")
                .negativeText("Cancel")
                .onPositive((dialog, which) -> getFragRequestIns().forceSubmitRequestInsData())
                .onNegative((dialog, which) -> dialog.dismiss())
                .show();
    }

    private void setupNavigationMenu() {
        if (CommonHelper.isKioskMode(this)) {
            navigation.inflateMenu(R.menu.navigation_request_ins);
        } else {
            navigation.inflateMenu(R.menu.navigation);
        }
    }

    private void updateNavigationMenu() {
        boolean appHideAssetTab = CommonJson.isUserConfigEnabled(this, Constants.Keys.bAppHideAssetTab);
        boolean appEnableProjectTab = CommonJson.isProjectModuleEnable(this);
        boolean appEnableTasksTab = CommonJson.bEnableTasks(this);
        Menu menu = navigation.getMenu();
        if (CommonHelper.isKioskMode(this)) {
            navigation.setVisibility(View.GONE);
        } else {
            menu.findItem(R.id.navigation_assets).setVisible(!appHideAssetTab);
            menu.findItem(R.id.navigation_projects).setVisible(appEnableProjectTab);
            menu.findItem(R.id.navigation_tasks).setVisible(appEnableTasksTab);
            navigation.setVisibility(View.VISIBLE);
        }

        // If the current menu item becomes invisible, then set the first visible item as the current item
        MenuItem selectedMenuItem = getSelectedMenuItem();
        if (selectedMenuItem != null) {
            if (!selectedMenuItem.isVisible())
                selectFirstVisibleMenuItem();
            else
                selectFragment(selectedMenuItem, true);
        }
    }

    private void updateActionButtons() {
        if (getFragAssets() == getCurrentFragment()) {
            boolean bDisableAdd = CommonJson.disableEditAsset(this);
            btnAdd.setVisibility(bDisableAdd ? View.INVISIBLE : View.VISIBLE);
        } else if (getFragInspections() == getCurrentFragment()) {
            boolean bDisableAdd = CommonJson.disableNewInspection(this) || CommonJson.disableEditAsset(this);
            btnAdd.setVisibility(bDisableAdd ? View.INVISIBLE : View.VISIBLE);
        } else if (getFragNewSchedules() == getCurrentFragment()) {
            btnAdd.setVisibility(View.INVISIBLE);
        } else if (getFragRequestIns() == getCurrentFragment()) {
            btnAdd.setVisibility(View.VISIBLE);
        } else if (getFragProjects() == getCurrentFragment()) {
            btnAdd.setVisibility(View.INVISIBLE);
        } else if (getFragTasks() == getCurrentFragment()) {
            btnAdd.setVisibility(View.INVISIBLE);
        }
    }

    private MenuItem getSelectedMenuItem() {
        Menu menu = navigation.getMenu();
        for (int i = 0; i < menu.size(); i++) {
            MenuItem menuItem = menu.getItem(i);
            if (menuItem.isChecked()) return menuItem;
        }
        return null;
    }

    private void selectFirstVisibleMenuItem() {
        Menu menu = navigation.getMenu();
        for (int i = 0; i < menu.size(); i++) {
            MenuItem menuItem = menu.getItem(i);
            if (menuItem.isVisible()) {
                selectFragment(menuItem, true);
                break;
            }
        }
    }

    public void RefreshInspection2ndTab(){
        getFragInspections().ReloadData();
    }

    protected void pushFragment(Fragment fragment) {
        if (fragment == null)
            return;

        getSupportFragmentManager()
                .beginTransaction()
                .replace(R.id.container, fragment)
                .commit();
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.home_tab_btn_setting:
                goToSetting();
                break;
            case  R.id.home_tab_btn_menu:
                tappedMenuBtn();
                break;
            case R.id.home_tab_btn_sync:
                setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
                syncService();
                break;
            case R.id.home_tab_btn_add:
                tappedAddBtn();
                break;
        }
    }

    public void syncService() {
        if (isSyncing) {
            return;
        }

        if (getFragAssets() != null) {
            getFragAssets().showRefreshIndicator();
        }

        if (bInitialSync){
            mProgressDialog = CommonUI.ShowMaterialProgressDialog(this, "Message", "Running. Please wait ...");
        }

        btnSync.startAnimation(animRotate);

        Intent oIntent = new Intent(this, SyncService.class);
        oIntent.putExtra("receiver", new SyncReceiver(new Handler()));
        startService(oIntent);
    }

    private final ActivityResultLauncher<Intent> settingActivityResultLauncher = registerForActivityResult(
            new ActivityResultContracts.StartActivityForResult(),
            result -> {
                if (result.getResultCode() == Constants.ResultCodes.FORCE_SYNC) {
                    bInitialSync = true;
                    syncService();
                }
            });

    private void goToSetting() {
        Intent oIntent = new Intent(this, if_settings.class);
        settingActivityResultLauncher.launch(oIntent);
    }

    public void tappedAddBtn() {
        int itemId = navigation.getSelectedItemId();
        if (itemId == R.id.navigation_assets) {
            Intent oIntent = new Intent(this, if_UpdateAsset_2nd.class);
            oIntent.putExtra(Constants.Extras.iSAssetID, 0);
            oIntent.putExtra(Constants.Extras.iSPAssetID, 0);
            startActivity(oIntent);
        } else if (itemId == R.id.navigation_inspection) {
            selectInsType();
        } else if (itemId == R.id.navigation_request_ins) {
            if (CommonHelper.isKioskMode(this)) {
                Intent i = new Intent(this, QRScannerActivity.class);
                startActivityForResult(i, Constants.RequestCodes.QR_SCANNER_REQUEST);
            }
        }
    }

    public String copyDBToSDCard() {
        try {
            InputStream myInput = new FileInputStream(this.getDatabasePath("if_data.db"));
            String sDBPath = CommonHelper.sFileRoot + "/if_data.db";
            File file = new File(sDBPath);
            if (!file.exists()){
                try {
                    file.createNewFile();
                } catch (IOException e) {
                    Log.i("FO","File creation failed for " + file);
                }
            }

            OutputStream myOutput = new FileOutputStream(sDBPath);

            byte[] buffer = new byte[1024];
            int length;
            while ((length = myInput.read(buffer))>0){
                myOutput.write(buffer, 0, length);
            }

            //Close the streams
            myOutput.flush();
            myOutput.close();
            myInput.close();
            Log.i("FO","copied");
            return sDBPath;

        } catch (Exception e) {
            Log.i("FO","exception="+e);
            return null;
        }
    }

    private void tappedMenuBtn() {
        String[] lsOption;
        if (CommonHelper.isKioskMode(this)){
            lsOption  = new String[3];
            lsOption[2] = "QRCode";
        }
        else{
            lsOption = new String[2];
        }


        lsOption[0] = "Sort Assets";
        lsOption[1] = "Inbox";

        new MaterialDialog.Builder(if_HomeTab.this)
                .title(R.string.alert_title_action)
                .items(lsOption)
                .itemsCallbackSingleChoice(-1, (dialog, view, which, text) -> {
                    switch (which) {
                        case 0:  //sort Assets
                            sortAssets();
                            break;
                        //case 1:  //Upload Inspections
                        //    uploadInspections();
                         //   break;
                        case 1:  //Inbox
                            Intent oIntent = new Intent(if_HomeTab.this, if_Inbox.class);
                            startActivity(oIntent);
                            break;
                        case 2:  //QRCode
                            Intent intent = new Intent(if_HomeTab.this, QRScannerActivity.class);
                            startActivityForResult(intent, Constants.RequestCodes.QR_SCANNER_REQUEST);
                            break;
                    }
                    return true;
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        switch (requestCode) {
            case 0: {
                // If request is cancelled, the result arrays are empty.
                if (grantResults.length > 0) {
                    if (grantResults[0] != PackageManager.PERMISSION_GRANTED) {
                        Toast.makeText(this, R.string.permission_take_photos_denied, Toast.LENGTH_LONG).show();
                    }

                    if (grantResults[1] != PackageManager.PERMISSION_GRANTED) {
                        Toast.makeText(this, R.string.permission_record_video_denied, Toast.LENGTH_LONG).show();
                    }
                    // permission was granted, yay! Do the
                    // contacts-related task you need to do.

                } else {
                    Toast.makeText(this, R.string.permission_not_granted, Toast.LENGTH_LONG).show();
                }
            }

        }
    }

    private void selectInsType(){
        if (!CommonUI.bAppPermission(this)) return;
        if (!CommonValidate.validateNewInspection(this)) return;
        
        try {
            lsInsType = CommonDB_Inspection.GetInsType_CustomPermission(this);
            if (ArrayUtils.isNotEmpty(lsInsType)) {
                List<InsTypeSelection> lsInsTypeSelection = new ArrayList<>();
                for (ai_InsType oInsType : lsInsType) {
                    lsInsTypeSelection.add(InsTypeSelection.fromInsType(oInsType));
                }

                new ItemSelectionDialog<>(this, getString(R.string.title_choose_inspection_type), lsInsTypeSelection, () -> {
                    return SharedConfig.getInstance(this).getRecentInsTypes();
                }, selection -> {
                    ai_InsType aiInsType = db_Inspection.GetInsTypeBySInsTypeID(selection.getId());
                    if (aiInsType == null) {
                        CommonUI.showToast(this, "Inspection type not found");
                        return null;
                    }
                    CommonDB.InsertLog(if_HomeTab.this, "Prepare Inspection",
                            "Start New (" + aiInsType.sInsTitle + ") Inspection For New Asset");

                    Intent oIntent = new Intent(if_HomeTab.this, if_NewAsset.class);
                    oIntent.putExtra(Constants.Extras.iSInsTypeID, aiInsType.iSInsTypeID);
                    startActivity(oIntent);
                    SharedConfig.getInstance(this).saveInsType(selection.getId());
                    return null;
                }).show();
            }
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Home.SelectInsType", ex, this);
        }
    }

    public void uploadInspections() {
        CommonValidate.Permission_Validate(this);
        if (!CommonValidate.validateInspectionsCompulsoryItems(this, 0)) return;

        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);

        // Not allowed starting service Intent while app is in background
        if(!CommonHelper.isAppInBackground(this)) {
            mProgressDialog = CommonUI.ShowMaterialProgressDialog(this, "Message", "Running. Please wait ...");
            Intent oIntent = new Intent(this, UploadService.class);
            oIntent.putExtra("receiver", new UploadReceiver(new Handler()));
            oIntent.putExtra(Constants.Extras.iInsID, 0);
            startService(oIntent);
        }
    }

    private void sortAssets(){
        try {
            String[] lsOption = new String[6];
            lsOption[0] = "By Address (Asc)";
            lsOption[1] = "By Address (Desc)";
            lsOption[2] = "By Number (Asc)";
            lsOption[3] = "By Number (Desc)";
            lsOption[4] = "By Reference (Asc)";
            lsOption[5] = "By Reference (Desc)";

            new MaterialDialog.Builder(if_HomeTab.this)
                    .title("Sort Assets")
                    .items(lsOption)
                    .itemsCallbackSingleChoice(-1, (dialog, view, i, text) -> {
                        if (i == 0) {
                            getFragAssets().setSortString("order by ltrim(S_ADDRESS_ONE, '1234567890 ')  COLLATE NOCASE asc");
                        } else if (i == 1) {
                            getFragAssets().setSortString("order by ltrim(S_ADDRESS_ONE, '1234567890 ')  COLLATE NOCASE desc");
                        } else if (i == 2) {
                            getFragAssets().setSortString("order by S_ADDRESS_ONE  COLLATE NOCASE asc");
                        } else if (i == 3) {
                            getFragAssets().setSortString("order by S_ADDRESS_ONE  COLLATE NOCASE desc");
                        } else if (i == 4) {
                            getFragAssets().setSortString("order by S_FIELD_THREE  COLLATE NOCASE asc");
                        } else if (i == 5) {
                            getFragAssets().setSortString("order by S_FIELD_THREE  COLLATE NOCASE desc");
                        } else {
                            getFragAssets().setSortString("COLLATE NOCASE");
                        }
                        getFragAssets().ReloadData();

                        return true;
                    })
                    .negativeText(R.string.md_cancel_label)
                    .show();
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Home.SelectInsType", ex, this);
        }
    }

    private void startSync() {
        String bStartSync = CommonHelper.GetPreferenceString(this, "bStartSync");

        if (bStartSync != null && bStartSync.equals("true")) {
            CommonHelper.SavePreference(this, "bStartSync", "false");
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);

            try {
                ai_Contact.deleteAll(ai_Contact.class);
                ai_InsAlert.deleteAll(ai_InsAlert.class);
                ai_AssetLayout.deleteAll(ai_AssetLayout.class);
                ai_Assets.deleteAll(ai_Assets.class);
                ai_CheckList.deleteAll(ai_CheckList.class);
                ai_InsType.deleteAll(ai_InsType.class);
                ai_Layout.deleteAll(ai_Layout.class);
                ai_File.deleteAll(ai_File.class);

                ai_QuickPhrase.deleteAll(ai_QuickPhrase.class);
            } catch (Exception ex) {
                ex.printStackTrace();
            }

            mProgressDialog = CommonUI.ShowMaterialProgressDialog(this, "Message", "Running. Please wait ...");
            Intent oIntent = new Intent(this, SyncService.class);
            oIntent.putExtra("receiver", new SyncReceiver(new Handler()));
            startService(oIntent);
            CommonValidate.Permission_Validate(if_HomeTab.this);
        }

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constants.RequestCodes.QR_SCANNER_REQUEST) {
            if (resultCode == Activity.RESULT_OK) {
                String result = data.getStringExtra("result");
                if (result.startsWith("SI_ExternalIns:")) {
                    String sToken = result.replace("SI_ExternalIns://", "");
                    if (sToken.contains("_")) {
                        String[] arrToken = sToken.split("_");
                        String downloadableUri = IF_RestClient.getAbsoluteUrl(String.format(
                                "/SyncExternal/GetRequestExternalAccess?iRequestID=%s&sRequestCode=%s", arrToken[0], arrToken[1]));
                        final String sEmail = CommonHelper.GetPreferenceString(if_HomeTab.this, "sEmail");
                        if (StringUtils.isEmpty(sEmail)) {
                            CommonHelper.SavePreference(if_HomeTab.this, Constants.Settings.bKiosk, "1");
                        }
                        new RequestInspectionTask(this).execute(downloadableUri);
                        return;
                    }
                }
            }
            ShowAlert("Message", "Sorry, we can not recognize your QR Code. Please try again. ");
        }
    }

    private Fragment getCurrentFragment() {
        return getSupportFragmentManager().findFragmentById(R.id.container);
    }

    public void UploadIndividualInspection(int iInsID){
        CommonValidate.Permission_Validate(if_HomeTab.this);

        if (!CommonValidate.validateInspectionsCompulsoryItems(this, iInsID)) return;

        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);

        mProgressDialog = CommonUI.ShowMaterialProgressDialog(this, "Message", "Running. Please wait ...");
        Intent oIntent = new Intent(this, UploadService.class);
        oIntent.putExtra("receiver", new UploadReceiver(new Handler()));
        oIntent.putExtra(Constants.Extras.iInsID, iInsID);
        startService(oIntent);
    }

    @SuppressLint("ParcelCreator")
    private class SyncReceiver extends ResultReceiver {
        SyncReceiver(Handler handler) {
            super(handler);
        }

        @Override
        protected void onReceiveResult(int resultCode, Bundle resultData) {
            try {
                super.onReceiveResult(resultCode, resultData);

                String sTitle = resultData.getString(Constants.Keys.sTitle),
                        sMessage = resultData.getString(Constants.Keys.sMessage);
                if (resultCode == SyncService.SYNC_PROGRESS || resultCode == SyncService.SUBMIT_PROCESS) {
                    isSyncing = true;
                    try {
                        if (bInitialSync) {
                            if (!StringUtils.isEmpty(sTitle)) mProgressDialog.setTitle(sTitle);
                            mProgressDialog.setContent(sMessage);
                        }
                    } catch (Exception ee) {

                    }
                } else if (resultCode == SyncService.SYNC_SUCCESS) {

                    isSyncing = false;
                    btnSync.clearAnimation();


                    if (getFragNewSchedules() != null) {
                        getFragNewSchedules().ReloadSchedules();
                    }

                    if (mProgressDialog != null) {
                        mProgressDialog.dismiss();
                    }

                    runOnUiThread(if_HomeTab.this::updateNavigationMenu);
                    runOnUiThread(if_HomeTab.this::updateActionButtons);
                    if (getFragAssets() != null) {
                        getFragAssets().updateSelectedAssetViewIDs();
                        getFragAssets().hideRefreshIndicator();
                        getFragAssets().ReloadData();
                    }
                    if (bInitialSync) {
                        bInitialSync = false;
                        // parsing the external link after the initial sync is done
                        parseExternalLinks();
                    }

                    // Ask for permission to post notification
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                        runOnUiThread(() -> {
                            PermissionUtils.requestPermission(Manifest.permission.POST_NOTIFICATIONS, null);
                        });
                    }

                } else if (resultCode == SyncService.SYNC_FAIL) {
                    isSyncing = false;
                    btnSync.clearAnimation();
                    if (getFragAssets() != null) {
                        getFragAssets().hideRefreshIndicator();
                    }

                    if (mProgressDialog != null) {
                        mProgressDialog.dismiss();
                    }

                    // Shows the error message prompt
                    CommonUI.longToast(if_HomeTab.this, sMessage);
                }
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "if_home.Sync.onReceiveResult", ex, getApplicationContext());
            }
        }
    }

    @SuppressLint("ParcelCreator")
    private class UploadReceiver extends ResultReceiver {
        UploadReceiver(Handler handler) {
            super(handler);
        }

        @Override
        protected void onReceiveResult(int resultCode, Bundle resultData) {
            try {
                super.onReceiveResult(resultCode, resultData);
                if (resultCode == UploadService.UPDATE_PROGRESS) {
                    if (resultData.getString("Title").equalsIgnoreCase("")) {
                        //TODO
                    } else {
                        mProgressDialog.setTitle("Running. Please wait... - " + resultData.getString("Title") );
                    }
                    mProgressDialog.setContent(resultData.getString("Message"));
                } else if (resultCode == UploadService.UPDATE_SUCCESS) {
                    if (mProgressDialog != null) {
                        mProgressDialog.dismiss();
                        new MaterialDialog.Builder(if_HomeTab.this)
                                .title(resultData.getString("Title"))
                                .content(resultData.getString("Message"))
                                .positiveText(R.string.tv_ok)
                                .onPositive((dialog, which) -> {
                                    if (CommonHelper.isKioskMode(if_HomeTab.this)) {
                                        getFragRequestIns().reloadInspections();
                                    } else {
                                        getFragInspections().switchToSegment(Uploaded);
                                    }
                                })
                                .show();
                    }
                } else if (resultCode == UploadService.UPDATE_SUCCESSNODATA) {
                    if (mProgressDialog != null) {
                        mProgressDialog.dismiss();
                        new MaterialDialog.Builder(if_HomeTab.this)
                                .title(resultData.getString("Title"))
                                .content(resultData.getString("Message"))
                                .positiveText(R.string.tv_ok)
                                .onPositive((dialog, which) -> getFragInspections().switchToSegment(Uploaded))
                                .show();
                    }
                } else if (resultCode == UploadService.UPDATE_FAIL) {
                    if (mProgressDialog != null) {
                        mProgressDialog.dismiss();
                        new MaterialDialog.Builder(if_HomeTab.this)
                                .title(resultData.getString("Title"))
                                .content(resultData.getString("Message"))
                                .positiveText(R.string.tv_ok)
                                .onPositive((dialog, which) -> dialog.dismiss())
                                .show();
                    }
                }
            } catch(Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "if_home.Upload.onReceiveResult", ex, getApplicationContext());
            }
        }
    }

    private void ShowAlert(String sTitle, String sMessage){
        CommonHelper.ShowAlert(sTitle, sMessage, this);
    }

    private void createDatabaseViews() {
        new Handler().post(() -> {
            CommonDB_Inspection.CreateInspectionView();
            CommonDB_Schedule.CreateScheduleView();
            if (CommonHelper.isKioskMode(if_HomeTab.this)) {
                CommonDB_Schedule.CreateRequestInspectionView();
            }
        });
    }

    private void migrateExistFilesIfNeed() {
        if (!FileUtils.shouldUseScopedStorage() || !FileUtils.checkStoragePermissions(App.getContext())) return;
        try {
            File legacyRootDir = new File(FileUtils.getExternalStorageDirectory());
            File appRootDir = new File(FileUtils.getExternalStoragePrivateDirectory());
            // Check if have merged the files
            if (legacyRootDir.exists()) {
                //CommonHelper.trackEvent(this, "Start storage migration");
                MaterialDialog progressDialog = CommonUI.ShowMaterialProgressDialog(
                        this, "Migration...", "Please keep SnapInspect in foreground until finish.");
                // migrate the directory `/.SnapInspect3`
                FileUtils.migrateExistFilesIfNeed(legacyRootDir, appRootDir);
                progressDialog.dismiss();
            }
        } catch (Exception ex) {
            //CommonHelper.trackEvent(this, "Storage migration failed.");
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    private void parseExternalLinks() {
        String sKioskUri = getIntent().getStringExtra(Constants.Extras.sKioskUri);
        if (StringUtils.isEmpty(sKioskUri)) return;

        String path = StringUtils.getURLPath(sKioskUri);
        // if the path is empty, ignore it
        if (StringUtils.isEmpty(path)) return;

        if (CommonHelper.isKioskMode(this)) {
            new RequestInspectionTask(this).execute(sKioskUri);
        } else if (path.toLowerCase().contains(Constants.ExternalLinks.externalSchedule)) {
            loadExternalScheduleIfNeeded(sKioskUri);
        }
    }

    private void loadExternalScheduleIfNeeded(String sKioskUri) {
        if (StringUtils.isEmpty(sKioskUri)) return;

        // parse the external link and url query
        final String sCode = StringUtils.getURLQueryParam(sKioskUri, "code");
        final int iSScheduleID = CommonHelper.getInt(StringUtils.getQueryParam(
                StringUtils.decodeBase64(sCode), "iScheduleID", null));
        if (iSScheduleID <= 0) return;

        // selected the schedule tab
        selectFragment(navigation.getMenu().findItem(R.id.navigation_new_schedule), true);

        // if local have this schedule, start this schedule straightaway.
        ai_Inspection oInspection = CommonDB_Inspection.GetInspectionByScheduleID(iSScheduleID);
        if (oInspection != null && oInspection.getId() > 0) {
            getFragNewSchedules().displayExternalSchedule(iSScheduleID);
        } else {
            // otherwise, get the schedule from server and save it to local.
            // load the schedule
            String userID = CommonHelper.GetPreferenceString(if_HomeTab.this, "iCustomerID");
            final MaterialDialog progressDialog = CommonUI.ShowMaterialProgressDialog(
                    this, "Message", "Running. Please wait ...");
            CommonRequest.requestURL(this, "/IOAPI/GetSchedule", () -> {
                RequestParams oParams = new RequestParams();
                oParams.add("iCustomerID", userID);
                oParams.add("sToken", CommonHelper.GetPreferenceString(if_HomeTab.this, "sToken"));
                oParams.add("iScheduleID", "" + iSScheduleID);
                return oParams;
            }, (response, error) -> {
                if (response != null) {
                    try {
                        JSONObject oSchedule = response.getJSONObject("oSchedule");
                        final int iCustomerID = oSchedule.getInt("iCustomerID");
                        if (CommonHelper.getInt(CommonJson.GetJsonKeyValue("_RequestID", oSchedule.getString("sCustom1"))) > 0) {
                            CommonUI.ShowAlert(if_HomeTab.this, "Message", "The inspection can not start due to request inspection.");
                        } else if (iCustomerID == CommonHelper.getInt(userID)) {
                            v_Schedule vSchedule = CommonDB_Schedule.GetVScheduleBySScheduleID(iSScheduleID);
                            if (vSchedule == null || vSchedule.iScheduleID == 0) {
                                new ai_Schedule(oSchedule).save();
                            }
                            getFragNewSchedules().displayExternalSchedule(iSScheduleID);
                        } else {
                            CommonUI.ShowAlert(if_HomeTab.this, "Message", "The schedule can not be started as it is assigned to other team members..");
                        }
                    } catch (JSONException e) {
                        CommonUI.ShowAlert(if_HomeTab.this, "Message", "Response is not valid, please try again.");
                        ai_BugHandler.ai_Handler_Exception(e);
                    }
                } else if (error != null) {
                    CommonUI.ShowAlert(this, "Error", error.getMessage());
                }
                CommonUI.DismissMaterialProgressDialog(if_HomeTab.this, progressDialog);
            });
        }
    }

    private static class RequestInspectionTask extends AsyncTask<String, String, String> {
        private MaterialDialog oDialog;
        //ai_Schedule oSchedule = null;

        private final WeakReference<if_HomeTab> weakReference;
        RequestInspectionTask(if_HomeTab homeTab) {
            weakReference = new WeakReference<>(homeTab);
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            oDialog = CommonUI.ShowMaterialProgressDialog(
                    weakReference.get(), "Message", "Running. Please wait ...");
        }

        @Override
        protected String doInBackground(String... params) {
            //String server_response = null;
            if_HomeTab activity = weakReference.get();
            if (activity == null) return null;

            try {
                String sTempURL = params[0];
                if (sTempURL.toLowerCase().contains("/external/request")) {
                    Uri oData = Uri.parse(sTempURL);
                    HashMap<String, String> lsParams = new HashMap<>();
                    lsParams.put("iRequestID", oData.getQueryParameter("iRequestID"));
                    lsParams.put("sRequestCode", oData.getQueryParameter("sRequestCode"));

                    JSONObject oJson = IF_SyncClient.PostRequest("/SyncExternal/TokenExchange", lsParams);
                    if (oJson != null && oJson.getBoolean("success") && oJson.getInt("iRequestID") > 0) {
                        sTempURL = IF_RestClient.getAbsoluteUrl(String.format(
                                "/SyncExternal/GetRequestExternalAccess?iRequestID=%s&sRequestCode=%s",
                                "" + oJson.getInt("iRequestID"), oJson.getString("sRequestCode")));
                    }
                }

                // Uri uri = Uri.parse(sTempURL);
                String sContent = IF_SyncClient.DownloadData(sTempURL);
                if (sContent != null && sContent.length() > 0) {
                    JSONObject objResponse = new JSONObject(sContent);
                    boolean success = objResponse.getBoolean("success");
                    if (success) {
                        JSONObject oToken = objResponse.getJSONObject("oToken");
                        String sToken = oToken.getString("sExternalToken");
                        int iTokenID = oToken.getInt("iExternalTokenID");
                        String sExternalRequestType = oToken.getString("sType");
                        String sRequesterEmail = oToken.getString("sEmail");
                        String sRequesterName = oToken.getString("sName");
                        if (sExternalRequestType.equalsIgnoreCase("Property")) {
                            JSONArray arrProperties = objResponse.getJSONArray("lsProperty");
                            if (arrProperties != null && arrProperties.length() > 0) {
                                String sPath = activity.getFilesDir().getAbsolutePath() + "/";
                                String sFileName = iTokenID + "_" + CommonHelper.GetTimeString() + ".json";
                                CommonHelper.SaveStringToFilesDir(sFileName, sContent);

                                for (int i = 0; i < arrProperties.length(); i++) {
                                    try {
                                        JSONObject oObj = arrProperties.getJSONObject(i);
                                        int iSAssetID = oObj.getInt("iPropertyID");
                                        ai_Assets oAsset = CommonDB_Assets.GetAssetBy_iSAssetID(iSAssetID);
                                        if (oAsset == null) {
                                            oAsset = new ai_Assets();
                                        } else if (CommonValidate.bExternalInspection(oAsset.sFieldOne)) {
                                            break;
                                        }
                                        oAsset.iSAssetID = iSAssetID;
                                        try {
                                            oAsset.iSPAssetID = oObj.getInt("iSPropertyID");
                                        } catch (Exception ex111) {
                                            oAsset.iSPAssetID = 0;
                                        }
                                        oAsset.sAddressOne = oObj.getString("sAddress1");
                                        oAsset.sAddressTwo = oObj.getString("sAddress2");
                                        oAsset.iCustomerID = oObj.getInt("iCustomerID");
                                        oAsset.sKey = oObj.getString("sKey");
                                        oAsset.sAlarm = oObj.getString("sAlarm");
                                        oAsset.dtInsDue = oObj.getString("dtDue");
                                        oAsset.sFilter = CommonHelper.GetFilter(oAsset.sAddressOne, oAsset.sAddressTwo);
                                        oAsset.iPLVerID = oObj.getInt("iPLVerID");
                                        String sCustom2 = "";
                                        sCustom2 = CommonJson.AddJsonKeyValue(sCustom2, "iTokenID", "" + iTokenID);
                                        sCustom2 = CommonJson.AddJsonKeyValue(sCustom2, "RIP", sPath + sFileName);
                                        sCustom2 = CommonJson.AddJsonKeyValue(sCustom2, "sToken", sToken);
                                        oAsset.sFieldTwo = sCustom2;
                                        oAsset.save();
                                    } catch (Exception eee) {
                                        ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB_Assets.GetAssetBy_iSAssetID", eee);
                                    }
                                }
                                CommonValidate.SetTenantToolMode(true, activity);
                                JSONObject oObject = new JSONObject();
                                oObject.put("bSuccess", true);
                                return oObject.toString();


                            } else {
                                JSONObject oObject = new JSONObject();
                                oObject.put("bSuccess", false);
                                oObject.put("sMessage", "Invalid assset share. Please contact the request sender for more details.");
                                return oObject.toString();
                            }


                        } else if (sExternalRequestType.equalsIgnoreCase("Schedule")) {
                            JSONObject oDic = objResponse.getJSONObject("oSchedule");
                            int iSScheduleID = oDic.getInt("iScheduleID");
                            ai_Schedule oTemp = CommonDB_Schedule.GetScheduleBySScheduleID(iSScheduleID);
                            if (oTemp == null) {
                                String sPath = activity.getFilesDir().getAbsolutePath() + "/";
                                String sFileName = iTokenID + "_" + CommonHelper.GetTimeString() + ".json";
                                CommonHelper.SaveStringToFilesDir(sFileName, sContent);
                                ai_Schedule oSchedule = new ai_Schedule();
                                oSchedule.iSScheduleID = iSScheduleID;
                                oSchedule.iSAssetID = oDic.getInt("iSAssetID");
                                oSchedule.iSInsTypeID = oDic.getInt("iInsTypeID");
                                oSchedule.sAddressOne = oDic.getString("sAddress1");
                                oSchedule.sAddressTwo = oDic.getString("sAddress2");
                                oSchedule.sInsTitle = oDic.getString("sInsTitle");
                                oSchedule.sType = oDic.getString("sType");
                                oSchedule.sPTC = oDic.getString("sPTC");
                                oSchedule.dtDateTime = oDic.getString("dtSchedule");
                                JSONObject oCustom1 = new JSONObject();
                                oCustom1.put("iTokenID", "" + iTokenID);
                                oCustom1.put("RIP", sPath + sFileName);
                                oCustom1.put("REmail", sRequesterEmail);
                                oCustom1.put("RName", sRequesterName);
                                oCustom1.put("sAddInfo", oDic.getString("sAdditionalInfo"));
                                oCustom1.put("sToken", sToken);
                                oSchedule.sCustomOne = oCustom1.toString();
                                oSchedule.save();
                                CommonValidate.SetRequestInspectionMode(true, activity);
                                JSONObject oObject = new JSONObject();
                                oObject.put("bSuccess", true);
                                return oObject.toString();
                            } else {
                                JSONObject oObject = new JSONObject();
                                oObject.put("bSuccess", false);
                                return oObject.toString();
                            }
                        }
                    } else {
                        JSONObject oObject = new JSONObject();
                        oObject.put("bSuccess", false);
                        oObject.put("sMessage", objResponse.getString("message"));
                        return oObject.toString();
                    }
                } else {
                    JSONObject oObject = new JSONObject();
                    oObject.put("bSuccess", false);
                    oObject.put("sMessage", "Invalid Data 1.");
                    return oObject.toString();
                }

            } catch (Exception e) {
                // Log.e("osama", "exception erro " + e);
                //e.printStackTrace();
                ai_BugHandler.ai_Handler_Exception(
                        "Exception", "if_home.Sync.RequestInspectionTask.doInBackground", e, activity.getApplicationContext());
            }
            try {
                JSONObject oObject = new JSONObject();
                oObject.put("bSuccess", false);
                oObject.put("sMessage", "Invalid Data 1.");
                return oObject.toString();
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            return "";
        }

        @Override
        protected void onPostExecute(String response) {
            super.onPostExecute(response);
            if_HomeTab activity = weakReference.get();
            CommonUI.DismissMaterialProgressDialog(activity, oDialog);
            if (activity == null) return;

            String sTitle = null, sMessage = null;
            if (response == null || response.equalsIgnoreCase("")) {
                sTitle = "Download Failed.";
                sMessage = "Exception! Please contact your admin for details.";
            } else {
                try {
                    JSONObject oObject = new JSONObject(response);
                    if (CommonJson.GetJsonKeyValue_Bool("bSuccess", oObject)) {
                        //  sTitle = "Message";
                        //  sMessage = oObject.getString("sMessage");
                        new Handler().post(() -> {
                            if (activity.navigation.getSelectedItemId() == R.id.navigation_request_ins)
                                activity.getFragRequestIns().reloadInspections();
                        });
                    } else {
                       sTitle = "Message";
                       sMessage = oObject.getString("sMessage");
                    }
                } catch (Exception exxxx) {
                    sTitle = "Error";
                    sMessage = "Exception.";
                }
            }

            if (sTitle != null) {
                activity.ShowAlert(sTitle, sMessage);
            }
        }
    }

    private boolean bTenantTool() {
        try {
            return CommonHelper.isKioskMode(this) ||
                    CommonValidate.bRequestInspectionMode(this) ||
                    CommonValidate.bTenantToolMode(this);
        }catch(Exception ex){

        }
        return false;
    }

    private void selectFragmentTab(FRAGMENT_TAB tab) {
        MenuItem item = null;
        switch (tab) {
            case REQUEST_INSPECTION:
                item = navigation.getMenu().findItem(R.id.navigation_request_ins);
                break;
            case SCHEDULES:
                item = navigation.getMenu().findItem(R.id.navigation_new_schedule);
                break;
            case ASSETS:
                item = navigation.getMenu().findItem(R.id.navigation_assets);
                break;
            case INSPECTIONS:
                item = navigation.getMenu().findItem(R.id.navigation_inspection);
                break;
            case PROJECTS:
                item = navigation.getMenu().findItem(R.id.navigation_projects);
                break;
        }
        item.setChecked(true);
    }

}
