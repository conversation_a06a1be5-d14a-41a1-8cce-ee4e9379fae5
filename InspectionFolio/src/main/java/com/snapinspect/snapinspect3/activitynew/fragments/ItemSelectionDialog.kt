package com.snapinspect.snapinspect3.activitynew.fragments

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.*
import androidx.core.content.res.ResourcesCompat
import com.snapinspect.snapinspect3.Helper.CommonDB
import com.snapinspect.snapinspect3.IF_Object.ai_InsType
import com.snapinspect.snapinspect3.IF_Object.ai_Layout
import com.snapinspect.snapinspect3.IF_Object.ai_NoticeCategory
import com.snapinspect.snapinspect3.R

interface SelectableItem {
    val id: Int
    val title: String
    val titleAlignment: Int // View.TextAlignment

    fun fetchSubItems(): List<SelectableItem>
}

class ItemSelectionDialog<T : SelectableItem>(
    context: Context,
    private val title: String,
    private val items: List<T>,
    private val recentIds: () -> List<Int>,
    private val onItemSelected: (T) -> Unit,
) : Dialog(context) {

    private lateinit var listView: ListView
    private lateinit var editTextSearch: EditText
    private lateinit var cancelButton: Button
    private lateinit var backButton: ImageButton
    private lateinit var adapter: SelectableItemAdapter
    private val itemStack = mutableListOf<List<T>>()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val view = LayoutInflater.from(context).inflate(R.layout.dialog_inspection_type, null)
        requestWindowFeature(Window.FEATURE_NO_TITLE)
        setContentView(view)

        val titleView = view.findViewById<TextView>(R.id.dialogTitle)
        titleView.text = title

        listView = view.findViewById(R.id.listViewInspectionTypes)
        editTextSearch = view.findViewById(R.id.editTextSearch)
        cancelButton = view.findViewById(R.id.buttonCancel)
        backButton = view.findViewById(R.id.buttonBack)

        adapter = SelectableItemAdapter(context, sortItems(items))
        listView.adapter = adapter

        backButton.visibility = View.GONE

        editTextSearch.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                adapter.getFilter().filter(s.toString())
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
        })

        listView.setOnItemClickListener { _, _, position, _ ->
            val selectedItem = adapter.getItem(position)
            if (selectedItem != null) {
                onItemSelected(selectedItem)
                dismiss()
            }
        }

        cancelButton.setOnClickListener {
            dismiss()
        }

        backButton.setOnClickListener {
            if (itemStack.isNotEmpty()) {
                val previousItems = itemStack.removeAt(itemStack.size - 1)
                adapter = SelectableItemAdapter(context, previousItems)
                listView.adapter = adapter
                updateBackButtonVisibility()
            }
        }
    }

    override fun onStart() {
        super.onStart()
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        val displayMetrics = context.resources.displayMetrics
        val width = (displayMetrics.widthPixels * 0.8).toInt() // 80% of screen width
        val height = (displayMetrics.heightPixels * 0.6).toInt() // 60% of screen height

        window?.apply {
            setLayout(width, height)
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            // Apply the custom background drawable
            decorView.background = ResourcesCompat.getDrawable(
                context.resources, R.drawable.rounded_dialog_background, null)
            // Set margins
            val params = attributes
            params.x = 0 // horizontal margin
            params.y = 0 // vertical margin
            attributes = params
        }
    }

    private fun sortItems(items: List<T>): List<T> {
        val recentIds = recentIds()
        val recentItems = items.filter { it.id in recentIds }
        val otherItems = items.filter { it.id !in recentIds }
        return recentItems + otherItems
    }

    private inner class SelectableItemAdapter(
        context: Context,
        private val allItems: List<T>
    ) : BaseAdapter() {

        private var filteredItems: List<T> = allItems

        override fun getCount(): Int = filteredItems.size
        override fun getItem(position: Int): T? = filteredItems.getOrNull(position)
        override fun getItemId(position: Int): Long = position.toLong()

        fun getFilter(): Filter {
            return object : Filter() {
                override fun performFiltering(constraint: CharSequence?): FilterResults {
                    val filterResults = FilterResults()
                    if (constraint.isNullOrEmpty()) {
                        filterResults.values = allItems
                        filterResults.count = allItems.size
                    } else {
                        val filteredList = allItems.filter {
                            it.title.contains(constraint, ignoreCase = true)
                        }
                        filterResults.values = filteredList
                        filterResults.count = filteredList.size
                    }
                    return filterResults
                }

                override fun publishResults(constraint: CharSequence?, results: FilterResults?) {
                    filteredItems = results?.values as? List<T> ?: allItems
                    notifyDataSetChanged()
                }
            }
        }

        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
            val view = convertView ?: LayoutInflater.from(context)
                .inflate(R.layout.list_item_inspection_type, parent, false)
            
            val textView = view.findViewById<TextView>(R.id.title_inspection_type)
            val currentItem = getItem(position)
            textView.text = currentItem?.title
            textView.textAlignment = currentItem?.titleAlignment ?: View.TEXT_ALIGNMENT_CENTER

            val disclosureButton = view.findViewById<ImageButton>(R.id.disclosureButton)
            disclosureButton.visibility = View.GONE

            currentItem
                ?.fetchSubItems()
                ?.mapNotNull { it as? T }
                ?.takeIf { it.isNotEmpty() }
                ?.let { items ->
                    disclosureButton.visibility = View.VISIBLE
                    disclosureButton.setOnClickListener {
                        itemStack.add(filteredItems)
                        pushToSubItems(items)
                        updateBackButtonVisibility()
                    }
                    disclosureButton.isFocusable = false
                    disclosureButton.isFocusableInTouchMode = false
                }
            return view
        }

        private fun pushToSubItems(subItems: List<T>) {
            adapter = SelectableItemAdapter(context, subItems)
            listView.adapter = adapter
        }
    }

    private fun updateBackButtonVisibility() {
        if (itemStack.isEmpty()) {
            backButton.visibility = View.GONE
        } else {
            backButton.visibility = View.VISIBLE
            backButton.alpha = 0f
            backButton.animate()
                .alpha(1f)
                .setDuration(200)
                .start()
        }
    }
}

data class InsTypeSelection(
    override val id: Int,
    override val title: String,
    override val titleAlignment: Int = View.TEXT_ALIGNMENT_CENTER,
) : SelectableItem {
    companion object {
        @JvmStatic
        fun fromInsType(insType: ai_InsType): InsTypeSelection {
            return InsTypeSelection(insType.iSInsTypeID, insType.sInsTitle)
        }
    }

    override fun fetchSubItems(): List<InsTypeSelection> = emptyList()
}

data class LayoutSelection(
    override val id: Int,
    override val title: String,
    override val titleAlignment: Int = View.TEXT_ALIGNMENT_CENTER,
) : SelectableItem {
    companion object {
        fun fromLayout(layout: ai_Layout): LayoutSelection {
            return LayoutSelection(layout.iSLayoutID, layout.sName)
        }
    }

    override fun fetchSubItems(): List<LayoutSelection> = emptyList()
}

data class TaskCategorySelection(
    override val id: Int,
    override val title: String,
    override val titleAlignment: Int = View.TEXT_ALIGNMENT_TEXT_START,
) : SelectableItem {
    companion object {
        fun fromTaskCategory(taskCategory: ai_NoticeCategory): TaskCategorySelection {
            return TaskCategorySelection(
                taskCategory.iSNoticeCategoryID,
                taskCategory.sName,
            )
        }
    }

    override fun fetchSubItems() : List<TaskCategorySelection> {
        return CommonDB.getNoticeCategories(id)
            .map { 
                val ai = ai_NoticeCategory()
                ai.iSNoticeCategoryID = it.iSNoticeCategoryID
                ai.sName = it.sName ?: ""
                ai
            }
            .map(Companion::fromTaskCategory)
    }
}