package com.snapinspect.snapinspect3.activitynew.Edit;

import android.content.Intent;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ListView;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.PickVisualMediaRequest;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.Adapter.FormItemsAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_AssetAttribute;
import com.snapinspect.snapinspect3.IF_Object.ai_Assets;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.if_FormItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.*;

import static android.text.InputType.TYPE_CLASS_TEXT;
import static android.text.InputType.TYPE_TEXT_FLAG_MULTI_LINE;
import static com.snapinspect.snapinspect3.IF_Object.O_MapItem.CustomInfoType.textArea;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.ViewType.attachment;

public class if_EditCustomInfo extends AppCompatActivity implements FormItemsAdapter.Delegate {

    private List<if_FormItem> mDataSource;
    private ListView mListView;
    private int iSAssetID, selectItemPosition = -1;
    private String mSelectedFilePath;
    private ActivityResultLauncher<PickVisualMediaRequest> mPickMedia;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edit_custom_info);
        try {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);

            View bottomContainer = findViewById(R.id.btn_bottom_container);
            if (!NetworkUtils.isNetworkAvailable(this)) {
                bottomContainer.setVisibility(View.GONE);
                CommonUI.ShowAlert(if_EditCustomInfo.this, "Error",
                        "To update custom information, please make sure you are connected to Internet.",
                        R.string.action_cancel, this::finish);
                return;
            }

            iSAssetID = getIntent().getIntExtra(Constants.Extras.iSAssetID, 0);

            mListView = findViewById(R.id.lv_formItems);
            mListView.setAdapter(new FormItemsAdapter(this, getDataSource(), this));

            bottomContainer.setVisibility(View.VISIBLE);
            Button btnBottom = findViewById(R.id.btn_bottom);
            btnBottom.setText(R.string.save_changes);
            btnBottom.setOnClickListener(v -> requestSubmitFormData(true));

            registerImagePicker();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        onBackPressed();
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        deleteSelectedFile();
        CommonHelper.hideSoftKeyboard(this);
        super.onBackPressed();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        deleteSelectedFile();
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data == null) return;
        if (RESULT_OK == resultCode) {
            String filePath = null;
            Uri uri = data.getData();
            if (uri != null) {
                // delete previous selected file if exist
                deleteSelectedFile();
                if (requestCode == Constants.ResultCodes.REQUEST_OPEN_DOCUMENT) {
                    // copy file to temp folder and get file path from uri
                    filePath = FileUtils.getFilePathFromContentUri(this, uri);
                    mSelectedFilePath = filePath;
                } else if (requestCode == Constants.RequestCodes.REQUEST_TAKE_PHOTO) {
                    filePath = uri.getPath();
                    mSelectedFilePath = filePath;
                }
            }

            if (!CommonHelper.bFileExist(filePath)) return;
            new UploadFileTask(this).execute(filePath);
        }
    }

    private List<ai_AssetAttribute> getCustomInfos() {
        List<ai_AssetAttribute> customInfos = new ArrayList<>();

        JSONObject assetFieldOne = null;
        ai_Assets oAsset = CommonDB_Assets.GetAssetBy_iSAssetID(iSAssetID);
        if (oAsset != null) {
            try {
                assetFieldOne = new JSONObject(oAsset.sFieldOne);
            } catch (JSONException e) {
                //
            }
        }
        if (assetFieldOne == null) return customInfos;

        try {
            String sCustomInfo = CommonHelper.GetPreferenceString(this, Constants.Keys.sAssetAttributes);
            JSONArray arrCustomInfo = new JSONArray(sCustomInfo);
            for (int i = 0; i < arrCustomInfo.length(); i++) {
                try {
                    JSONObject oObject = arrCustomInfo.getJSONObject(i);
                    ai_AssetAttribute oAssetAttribute = new ai_AssetAttribute(oObject);
                    String sTempValue = "";
                    String sAssetAttributeID =
                            Constants.Values.kAssetPropertyPrefix + oAssetAttribute.iAssetAttributeID;
                    if (assetFieldOne.has(sAssetAttributeID)) {
                        sTempValue = assetFieldOne.getString(sAssetAttributeID);
                    }
                    oAssetAttribute.sValue = sTempValue;
                    customInfos.add(oAssetAttribute);
                } catch (Exception ex) {
                    //
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return customInfos;
    }

    private List<if_FormItem> getDataSource() {
        if (mDataSource == null) {
            List<ai_AssetAttribute> customInfos = getCustomInfos();
            ArrayList<if_FormItem> items = new ArrayList<>();
            for (ai_AssetAttribute info : customInfos) {
                String iAssetAttributeID = Constants.Values.kAssetPropertyPrefix + info.iAssetAttributeID;
                if_FormItem item = new if_FormItem(iAssetAttributeID, info.getViewType());
                item.title = info.sLabel;
                item.inputType = info.sType == textArea
                        ? TYPE_TEXT_FLAG_MULTI_LINE | TYPE_CLASS_TEXT : TYPE_CLASS_TEXT;
                try {
                    JSONObject object = new JSONObject(info.sValue);
                    item.sFileID = object.getInt(Constants.Extras.iFileID);
                    item.sFileName = object.getString("sName");
                } catch (Exception ex) {
                    item.sFileID = CommonHelper.getInt(info.sValue);
                }
                item.value = info.getValue();
                items.add(item);
            }
            mDataSource = items;
        }
        return mDataSource;
    }

    private void requestSubmitFormData(boolean shouldFinish) {
        if (NetworkUtils.isNetworkAvailable(this)) {
            final MaterialDialog progressDialog = CommonUI.ShowMaterialProgressDialog(
                    this, "Message", "Processing. Please wait ...");
            CommonHelper.hideSoftKeyboard(this);
            List<if_FormItem> data = getDataSource();
            RequestParams oParams = new RequestParams();
            HashMap<String, String> customMap = new HashMap<>();
            for (if_FormItem item : data) {
                String sValue;
                if (item.viewType == attachment) {
                    if (StringUtils.isEmpty(item.sFileName) || item.sFileID == 0) continue;
                    HashMap<String, String> map = new HashMap<>();
                    map.put("sName", item.sFileName);
                    map.put("iFileID", item.sFileID + "");
                    sValue = new JSONObject(map).toString();
                } else {
                    sValue = !StringUtils.isEmpty(item.value) ? item.value.trim() : "";
                }
                customMap.put(item.identifier, sValue);
            }
            oParams.add("sCustom1", new JSONObject(customMap).toString());
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
            oParams.add("iAssetID", "" + iSAssetID);

            String sURL = "/IOAPI/UpdateAssetCustomInfo";
            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    runOnUiThread(progressDialog::dismiss);
                    try {
                        if (statusCode == 200 && response.getBoolean("success")) {
                            JSONObject oObject = response.getJSONObject("oProperty");
                            int id = CommonHelper.getInt(oObject.getString("iPropertyID"));
                            ai_Assets oAsset = CommonDB_Assets.GetAssetBy_iSAssetID(id);
                            if (oAsset != null) {
                                oAsset.sFieldOne = oObject.getString("sCustom1");
                                oAsset.save();
                            }
                            if (shouldFinish) finish();
                        } else {
                            CommonUI.ShowAlert(if_EditCustomInfo.this, "Error", "To create or update custom information, please make sure you are connected to Internet.");
                        }
                    } catch (Exception ex) {
                        ai_BugHandler.ai_Handler_Exception("Exception", "if_EditCustomInfo.requestSubmitFormData", ex, if_EditCustomInfo.this);
                    }
                }

                @Override
                public void onFailure(int statusCode, org.apache.http.Header[] headers, java.lang.Throwable e, org.json.JSONObject errorResponse) {
                    runOnUiThread(progressDialog::dismiss);
                    CommonUI.ShowAlert(if_EditCustomInfo.this, "Error", "To update custom information, please make sure you are connected to Internet.");
                }
            });
        } else {
            CommonUI.ShowAlert(this, "Error", "To update custom information, please make sure the device is connected to Internet. Please note, inspecting assets DO NOT require internet but uploading inspection data requires either 3G or WiFi. If you want to inspect a NEW asset without internet, please go to Inspection Tab, and add a new inspection from there so you can carry on inspection on a new asset without Internet.");
        }
    }

    @Override
    public void itemSelect(if_FormItem item, final int position) {
        if (!item.selectable) return;
        CommonHelper.hideSoftKeyboard(this);
        String[] options = {"Owner", "Tenant"};
        new MaterialDialog.Builder(this)
                .title("Contact Type")
                .items(options)
                .itemsCallbackSingleChoice(-1, (dialog, view1, which, text) -> {
                    item.value = (String) text;
                    CommonUI.reloadListViewRow(mListView, position);
                    return true;
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    @Override
    public void attachmentSelect(if_FormItem item, int position) {
        if (!item.selectable) return;
        CommonHelper.hideSoftKeyboard(this);
        String[] options = {
                getResources().getString(R.string.upload_file),
                getResources().getString(R.string.upload_photo),
                getResources().getString(R.string.take_photo)
        };
        new MaterialDialog.Builder(this)
                .title(R.string.please_select)
                .items(options)
                .itemsCallbackSingleChoice(-1, (dialog, view1, which, text) -> {
                    selectItemPosition = position;
                    switch (which) {
                        case 0:
                            showsFileDialog();
                            break;
                        case 1:
                            mPickMedia.launch(
                                new PickVisualMediaRequest.Builder()
                                    .setMediaType(ActivityResultContracts.PickVisualMedia.ImageOnly.INSTANCE)
                                    .build()
                            );
                            break;
                        case 2:
                            CommonUI_Photo.takePhoto(this, Constants.RequestCodes.REQUEST_TAKE_PHOTO);
                            break;
                    }
                    return true;
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    @Override
    public void dateRangeSelect(if_FormItem item, Constants.DateRange dateRange, String value, int position) {

    }

    private void showsFileDialog() {
        Intent intent = new Intent(Intent.ACTION_OPEN_DOCUMENT);
        intent.addCategory(Intent.CATEGORY_OPENABLE);
        intent.setType("application/pdf");
        startActivityForResult(intent, Constants.ResultCodes.REQUEST_OPEN_DOCUMENT);
    }

    private static class UploadFileTask  extends AsyncTask<String, Void, Void> {

        private MaterialDialog progressDialog;
        private final WeakReference<if_EditCustomInfo> activityReference;

        // only retain a weak reference to the activity
        UploadFileTask(if_EditCustomInfo context) {
            activityReference = new WeakReference<>(context);
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            progressDialog = CommonUI.ShowMaterialProgressDialog(
                    activityReference.get(), "Message", "Processing. Please wait ...");
        }

        @Override
        protected Void doInBackground(String... strings) {
            if_EditCustomInfo activity = activityReference.get();
            if (strings.length > 0 && activity != null) activity.uploadFile(strings[0]);
            return null;
        }

        @Override
        protected void onPostExecute(Void aVoid) {
            super.onPostExecute(aVoid);
            if (progressDialog != null) progressDialog.dismiss();
            if_EditCustomInfo activity = activityReference.get();
            if (activity != null) {
                activity.reloadListView();
                activity.requestSubmitFormData(false);
                activity.deleteSelectedFile();
            }
        }
    }

    private void deleteSelectedFile() {
        if (StringUtils.isEmpty(mSelectedFilePath)) return;
        try {
            FileUtils.deleteFile(new File(mSelectedFilePath));
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditCustomInfo.deleteSelectedFile", ex, this);
        }
    }

    private void reloadListView() {
        mListView.setAdapter(new FormItemsAdapter(this, getDataSource(), this));
        CommonUI.reloadListViewRow(mListView, selectItemPosition);
        selectItemPosition = -1;
    }

    private void uploadFile(String sFilePath) {
        if (StringUtils.isEmpty(sFilePath)) return;
        String fileName = sFilePath.substring(sFilePath.lastIndexOf("/") + 1);
        HashMap<String, String> lsParams = new HashMap<>();
        lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
        lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
        lsParams.put("iPropertyID", "" + iSAssetID);
        lsParams.put("dtDateTaken", Constants.dateFormat.format(new Date()));
        lsParams.put("iSize", "" +  CommonHelper.GetFileLength(sFilePath));
        lsParams.put("sClientFileName", fileName);

        JSONObject oJson = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFile", lsParams);
        try {
            if (oJson != null && oJson.getBoolean("success")) {
                if (IF_SyncClient.UploadFile(oJson.getString("sURL"), sFilePath) != 200) {
                    if (IF_SyncClient.UploadFile(oJson.getString("sURL"), sFilePath) != 200) {
                        return;
                    }
                }
            } else {
                oJson = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFile", lsParams);
                if (oJson != null && oJson.getBoolean("success")) {
                    if (IF_SyncClient.UploadFile(oJson.getString("sURL"), sFilePath) != 200) {
                        if (IF_SyncClient.UploadFile(oJson.getString("sURL"), sFilePath) != 200) {
                            return;
                        }
                    }
                } else {
                    return;
                }
            }
            int iSFileID = oJson.getJSONObject("oFile").getInt("iFileID");
            if (iSFileID > 0 && selectItemPosition > -1) {
                lsParams.clear();
                lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
                lsParams.put("iSFileID", "" + iSFileID);
                JSONObject oReturn = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFileSuccess", lsParams);
                if (oReturn == null || !oReturn.getBoolean("success")) {
                    oReturn = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFileSuccess", lsParams);
                }

                if (oReturn != null && oReturn.getBoolean("success")) {
                    if_FormItem item = getDataSource().get(selectItemPosition);
                    if (item != null) {
                        item.sFileID = iSFileID;
                        item.sFileName = fileName;
                    }
                }
            }
        } catch (JSONException e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }
    }

    private void registerImagePicker() {
        mPickMedia = registerForActivityResult(new ActivityResultContracts.PickVisualMedia(), uri -> {
            if (uri != null) {
                String filePath =  FileUtils.getFilePathFromContentUri(if_EditCustomInfo.this, uri);
                if (!CommonHelper.bFileExist(filePath)) return;
                new UploadFileTask(if_EditCustomInfo.this).execute(filePath);
            }
        });
    }
}
