package com.snapinspect.snapinspect3.activitynew.tasks

import android.app.ComponentCaller
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.PickVisualMediaRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.afollestad.materialdialogs.MaterialDialog
import com.snapinspect.snapinspect3.Helper.*
import com.snapinspect.snapinspect3.IF_Object.*
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.SI_DB.db_Media
import com.snapinspect.snapinspect3.SI_DB.db_Tasks
import com.snapinspect.snapinspect3.activity.if_CameraX
import com.snapinspect.snapinspect3.activity.if_video
import com.snapinspect.snapinspect3.activity.if_video_old
import com.snapinspect.snapinspect3.activity.if_videoshow
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayPhoto
import com.snapinspect.snapinspect3.activitynew.if_existasset
import com.snapinspect.snapinspect3.util.FileUtils
import com.snapinspect.snapinspect3.views.composables.*
import com.snapinspect.snapinspect3.views.tasks.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.lang.ref.WeakReference
import java.util.*


class if_EditTask : ComponentActivity() {
    private lateinit var viewModel: EditTaskViewModel

    private lateinit var customInfoFileSelectionLauncher: ActivityResultLauncher<Intent>
    private lateinit var customInfoVisualMediaLauncher: ActivityResultLauncher<PickVisualMediaRequest>
    private lateinit var commentFileSelectionLauncher: ActivityResultLauncher<Intent>
    private lateinit var commentVisualMediaLauncher: ActivityResultLauncher<PickVisualMediaRequest>
    private var selectedCustomInfo: ai_CustomInfo? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setTheme(R.style.RemoveShadowActionBar)
        actionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            show()
            title = ""
        }

        val iAssetID = intent.getIntExtra(Constants.Extras.iSAssetID, 0)
        val iTaskID = intent.getIntExtra(Constants.Extras.iSTaskID, 0)
        val iPTaskID = intent.getIntExtra(Constants.Extras.iPSTaskID, 0)
        val shouldScrollToBottom = intent.getBooleanExtra(Constants.Extras.SCROLL_TO_BOTTOM, false)

        setContent {
            SnapTheme {
                viewModel = viewModel(
                    factory = EditTaskViewModel.Factory(
                        this@if_EditTask, iAssetID, iTaskID, iPTaskID, shouldScrollToBottom
                    )
                )
                EditTaskView(
                    viewModel = viewModel,
                    handleNavigation = ::handleNavigation,
                    onUiStateChange = ::invalidateOptionsMenu
                )
            }
        }

        addLocalBroadcastReceiver()
        createActivityLaunchers()
    }

    override fun onDestroy() {
        super.onDestroy()
        removeLocalBroadcastReceiver()
        customInfoFileSelectionLauncher.unregister()
        customInfoVisualMediaLauncher.unregister()
        commentFileSelectionLauncher.unregister()
        commentVisualMediaLauncher.unregister()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?, caller: ComponentCaller) {
        super.onActivityResult(requestCode, resultCode, data, caller)
        if (resultCode != RESULT_OK) return
        data?.data?.let { uri ->
            uri.path
                ?.takeIf { CommonHelper.bFileExist(it) }
                ?.let { path ->
                    when (requestCode) {
                        Constants.RequestCodes.CUSTOM_INFO_TAKE_PHOTO -> {
                            viewModel.uploadFileAndSaveTaskCustomInfo(selectedCustomInfo, path)
                        }

                        Constants.RequestCodes.TASK_COMMENT_TAKE_PHOTO -> {
                            viewModel.addTaskCommentFile(path)
                        }
                    }
                }
        }
    }

    private fun addLocalBroadcastReceiver() {
        LocalBroadcastManager.getInstance(this).apply {
            registerReceiver(actionReceiver, IntentFilter(Constants.Broadcasts.sDeleteNoticePhoto))
            registerReceiver(actionReceiver, IntentFilter(Constants.Broadcasts.sInsertNoticePhoto))
            registerReceiver(actionReceiver, IntentFilter(Constants.Broadcasts.sInsertNoticeVideo))
            registerReceiver(actionReceiver, IntentFilter(Constants.Broadcasts.sReloadTask))
        }
    }

    private fun removeLocalBroadcastReceiver() {
        LocalBroadcastManager.getInstance(this).unregisterReceiver(actionReceiver)
    }

    private val actionReceiver = object : BroadcastReceiver() {
        override fun onReceive(context: Context, intent: Intent) {
            intent.action?.let { action ->
                when (action) {
                    Constants.Broadcasts.sDeleteNoticePhoto -> {
                        intent.getLongExtra(Constants.Extras.iPhotoID, 0)
                            .takeIf { it > 0 }
                            ?.let { photoId ->
                                ai_Photo.findById(ai_Photo::class.java, photoId)?.let { photo ->
                                    viewModel.saveTaskPhoto(FileId.Remote(photo.iSPhotoID), true)
                                }
                            }
                    }

                    Constants.Broadcasts.sInsertNoticeVideo -> {
                        intent.getLongExtra(Constants.Extras.iVideoID, 0)
                            .takeIf { it > 0 }
                            ?.let { videoId -> viewModel.uploadAndSaveTaskVideo(videoId) }
                    }

                    Constants.Broadcasts.sInsertNoticePhoto -> {
                        intent.getLongExtra(Constants.Extras.iPhotoID, 0)
                            .takeIf { it > 0 }
                            ?.let { photoId -> viewModel.uploadAndSaveTaskPhoto(photoId) }
                    }

                    Constants.Broadcasts.sReloadTask -> viewModel.refreshTaskDetails { }

                    else -> {}
                }
            }
        }
    }

    private fun createActivityLaunchers() {
        customInfoFileSelectionLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            result.takeIf { it.resultCode == RESULT_OK }
                ?.data?.data
                ?.let { uri -> FileUtils.getFilePathFromContentUri(this, uri) }
                ?.let { filePath ->
                    viewModel.uploadFileAndSaveTaskCustomInfo(selectedCustomInfo, filePath)
                }
        }

        val visualMedia = ActivityResultContracts.PickVisualMedia()
        customInfoVisualMediaLauncher = registerForActivityResult(visualMedia) { uri ->
            uri
                ?.let { FileUtils.getFilePathFromContentUri(this, it) }
                ?.let { filePath ->
                    viewModel.uploadFileAndSaveTaskCustomInfo(selectedCustomInfo, filePath)
                }
        }

        commentFileSelectionLauncher = registerForActivityResult(
            ActivityResultContracts.StartActivityForResult()
        ) { result ->
            result.takeIf { it.resultCode == RESULT_OK }
                ?.data?.data
                ?.let { uri -> FileUtils.getFilePathFromContentUri(this, uri) }
                ?.let { filePath ->
                    viewModel.addTaskCommentFile(filePath)
                }
        }

        commentVisualMediaLauncher = registerForActivityResult(visualMedia) { uri ->
            uri
                ?.let { FileUtils.getFilePathFromContentUri(this, it) }
                ?.let { filePath ->
                    viewModel.addTaskCommentFile(filePath)
                }
        }
    }

    private fun handleNavigation(navigationAction: EditTaskAction) = when (navigationAction) {
        is EditTaskAction.ViewTaskDetails -> Intent(this, if_EditTask::class.java)
            .putExtra(Constants.Extras.iSTaskID, navigationAction.taskID)
            .also { startActivity(it) }
        is EditTaskAction.BackToAssetDetails -> if_existasset.newIntent(this, navigationAction.assetID)
            .apply { flags = Intent.FLAG_ACTIVITY_CLEAR_TOP }
            .also { startActivity(it) }
        is EditTaskAction.ViewSubTasks -> Intent(this, if_AssetTasks::class.java)
            .putExtra(Constants.Extras.iSTaskID, navigationAction.taskID)
            .putExtra(Constants.Extras.iSAssetID, navigationAction.assetID)
            .also { startActivity(it) }
        is EditTaskAction.AddAttachment -> when (navigationAction.type) {
            AttachmentType.PHOTO -> if_CameraX.newIntent(
                this, 0, 0, true, 0,
                if_CameraX.CameraSaveOption.PHOTO,
                Constants.Limits.iSinglePhotoLimit
            ).also { startActivity(it) }
            AttachmentType.VIDEO -> {
                val intent = if (CommonHelper.bUseNewVideo()) {
                    if_video.newIntent(
                        this, viewModel.iTaskID.toLong(), 0,
                        Constants.Limits.iMaxVideoDuration,
                        Constants.Values.DEFAULT_VIDEO_QUALITY
                    )
                } else {
                    if_video_old.newIntent(
                        this, viewModel.iTaskID.toLong(), 0,
                        Constants.Limits.iMaxVideoDuration
                    )
                }
                startActivity(intent)
            }
        }
        is EditTaskAction.PhotoAction -> with(navigationAction.params) {
            val photoId = db_Media.getPhotoOrCreateByServerID(fileId.id).id
            val sPhotoURL = photoIds.map { db_Media.getPhotoOrCreateByServerID(it.id).id }
                .joinToString(",") { it.toString() }
            when (action) {
                PhotoAction.VIEW -> if_DisplayPhoto.newIntent(this@if_EditTask, photoId, sPhotoURL, true)
                    .also { startActivity(it) }
                PhotoAction.DOWNLOAD -> { }
                PhotoAction.DOWNLOAD_ALL -> { }
            }
        }
        is EditTaskAction.VideoAction -> with(navigationAction.params) {
            fileId?.let { id ->
                when (action) {
                    VideoAction.PLAY -> {
                        if_videoshow.newIntent(this@if_EditTask, getVideoFileURL(id))
                            .also { startActivity(it) }
                    }
                    VideoAction.DELETE -> viewModel.saveTaskVideo(id, true)
                }
            }
        }
        is EditTaskAction.CustomInfoFileAction -> handleCustomInfoFileAction(navigationAction)
        is EditTaskAction.AddSubTask -> Intent(this, if_EditTask::class.java)
            .putExtra(Constants.Extras.iSAssetID, viewModel.iAssetID)
            .putExtra(Constants.Extras.iPSTaskID, viewModel.iTaskID)
            .also { startActivity(it) }
        is EditTaskAction.CommentFileAction -> handleCommentFileAction(navigationAction)
    }

    private fun handleCustomInfoFileAction(navigationAction: EditTaskAction.CustomInfoFileAction) {
        val (customInfo, action, fileInfo) = navigationAction
        selectedCustomInfo = customInfo

        when (action) {
            AttachmentAction.VIEW_FILE -> fileInfo?.takeIf { it.fileId > 0 }?.let {
                CommonUI.viewRemoteFile(this, it.fileId)
            }
            AttachmentAction.DELETE_FILE -> {
                val updatedCustomInfo = customInfo.copy().apply { sValue = "" }
                viewModel.updateTaskCustomInfo(updatedCustomInfo)
            }
            AttachmentAction.UPLOAD_FILE -> customInfoFileSelectionLauncher.launch(
                Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                    addCategory(Intent.CATEGORY_OPENABLE)
                    type = "application/pdf"
                }
            )
            AttachmentAction.UPLOAD_PHOTO -> customInfoVisualMediaLauncher.launch(
                PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly)
            )
            AttachmentAction.TAKE_PHOTO -> CommonUI_Photo.takePhoto(
                this, Constants.RequestCodes.CUSTOM_INFO_TAKE_PHOTO
            )
        }
    }

    private fun handleCommentFileAction(navigationAction:  EditTaskAction.CommentFileAction) {
        val (action, fileInfo) = navigationAction
        when (action) {
            AttachmentAction.UPLOAD_FILE -> commentFileSelectionLauncher.launch(
                Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                    addCategory(Intent.CATEGORY_OPENABLE)
                    type = "application/pdf"
                }
            )
            AttachmentAction.UPLOAD_PHOTO -> commentVisualMediaLauncher.launch(
                PickVisualMediaRequest(ActivityResultContracts.PickVisualMedia.ImageOnly)
            )
            AttachmentAction.TAKE_PHOTO -> CommonUI_Photo.takePhoto(
                this, Constants.RequestCodes.TASK_COMMENT_TAKE_PHOTO
            )
            AttachmentAction.VIEW_FILE ->  fileInfo?.takeIf { it.fileId > 0 }?.let {
                CommonUI.viewRemoteFile(this, it.fileId)
            }
            AttachmentAction.DELETE_FILE -> {}
        }
    }
    
    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menu?.let {
            val menuRes = when {
                ::viewModel.isInitialized && 
                viewModel.uiState.value.run { isEditMode && !isTaskCompleted } -> R.menu.menu_edit_task
                else -> R.menu.menu_save
            }
            menuInflater.inflate(menuRes, it)
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean = when (item.itemId) {
        android.R.id.home -> {
            checkForUnsavedChanges()
            true
        }
        R.id.action_complete_task -> {
            handleTaskCompletion()
            true
        }
        R.id.action_menu_save -> {
            handleTaskSave()
            true
        }
        else -> super.onOptionsItemSelected(item)
    }

    private fun checkForUnsavedChanges() {
        val currentState = viewModel.uiState.value
        val currentTask = currentState.task
        val originalTask = when {
            currentTask.iSNotificationID > 0 -> db_Tasks.getTaskBySTaskID(currentTask.iSNotificationID)
            else -> ai_Task().apply {
                iPropertyID = viewModel.iAssetID
                iPTaskID = viewModel.iPTaskID
            }
        }

        if (currentState.taskSettings != currentTask.settings
            || currentState.taskAttachments != currentTask.attachments
            || !CommonTask.compareTaskCustomInfos(currentTask.taskCustomInfos(this), currentState.taskCustomInfos)
            || currentTask.dump() != (originalTask?.dump() ?: "")) {
            showUnsavedChangesDialog()
        } else {
            finish()
        }
    }

    private fun showUnsavedChangesDialog() {
        MaterialDialog.Builder(this)
            .title(R.string.alert_title_message)
            .content(R.string.msg_unsaved_task_changes)
            .positiveText(R.string.btn_action_discard)
            .onPositive { _, _ -> finish() }
            .negativeText(R.string.btn_save_and_back)
            .onNegative { _, _ -> handleTaskSave() }
            .show()
    }

    private fun handleTaskCompletion() {
        viewModel.completeTask {
            notifyTaskUpdated()
            finish()
        }
    }

    private fun handleTaskSave() {
        viewModel.saveTask {
            notifyTaskUpdated()
            finish()
        }
    }

    private fun notifyTaskUpdated() {
        sendBroadcast(Constants.Broadcasts.sReloadTask)
    }
}

sealed interface EditTaskAction {
    data class ViewTaskDetails(val taskID: Int) : EditTaskAction
    data class BackToAssetDetails(val assetID: Int) : EditTaskAction
    data class AddAttachment(val type: AttachmentType) : EditTaskAction
    data class PhotoAction(val params: TaskPhotoParams) : EditTaskAction
    data class VideoAction(val params: TaskVideoParams) : EditTaskAction
    data class CustomInfoFileAction(
        val customInfo: ai_CustomInfo,
        val action: AttachmentAction,
        val fileInfo: TaskCustomInfoFile?,
    ) : EditTaskAction

    data class ViewSubTasks(val taskID: Int, val assetID: Int) : EditTaskAction
    data object AddSubTask : EditTaskAction
    data class CommentFileAction(
        val action: AttachmentAction,
        val fileInfo: TaskCommentFile?
    ) : EditTaskAction
}

data class TaskUiState(
    val task: ai_Task = ai_Task(),
    val taskSettings: TaskSettings = TaskSettings(),
    val taskAttachments: TaskAttachments = TaskAttachments(),
    val loadingState: LoadingState = LoadingState.None,
    val isEditMode: Boolean = false,
    val isTaskCompleted: Boolean = false,
    val taskCustomInfos: List<ai_CustomInfo> = emptyList(),
    val taskComments: List<ai_TaskComment> = emptyList(),
    val currentPageOfComments: Int = 0,
    val hasMoreComments: Boolean = false,
    val shouldScrollToBottom: Boolean = false,
)

val TaskUiState.shouldShowSubTasks: Boolean
    get() = !task.isSubTask && isEditMode

class EditTaskViewModel(
    context: Context,
    val iAssetID: Int,
    val iTaskID: Int,
    val iPTaskID: Int,
    private val shouldScrollToComment: Boolean = false,
) : ViewModel() {

    class Factory(
        private val context: Context,
        private val iAssetID: Int,
        private val iTaskID: Int,
        private val iPTaskID: Int,
        private val shouldScrollToComment: Boolean = false,
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(EditTaskViewModel::class.java)) {
                @Suppress("UNCHECKED_CAST")
                return EditTaskViewModel(
                    context,
                    iAssetID,
                    iTaskID,
                    iPTaskID,
                    shouldScrollToComment
                ) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }

    private val contextRef: WeakReference<Context> = WeakReference(context)
    private val _uiState = MutableStateFlow(TaskUiState(isEditMode = iTaskID > 0))
    val uiState: StateFlow<TaskUiState> = _uiState

    init {
        loadTaskDetailsFromDB()
        updateUiState {
            copy(shouldScrollToBottom = shouldScrollToComment)
        }
        uiState.takeIf { it.value.isEditMode }?.let {
            viewModelScope.launch {
                loadTaskDetailsAndComments()
            }
        }
    }

    private fun loadTaskDetailsFromDB() {
        val context = contextRef.get() ?: return
        viewModelScope.launch {
            val loadedTask = db_Tasks.getTaskBySTaskID(iTaskID) ?: ai_Task()
            if (loadedTask.iSNotificationID == 0) {
                loadedTask.apply {
                    iPropertyID = iAssetID
                    iPTaskID = <EMAIL>
                }
            }
            updateUiState {
                copy(
                    task = loadedTask,
                    taskSettings = loadedTask.settings,
                    taskAttachments = loadedTask.attachments,
                    isTaskCompleted = loadedTask.bClosed,
                    taskCustomInfos = loadedTask.taskCustomInfos(context)
                )
            }
        }
    }

    private fun updateUiState(update: TaskUiState.() -> TaskUiState) {
        _uiState.update { it.update() }
    }

    private suspend fun loadTaskDetailsAndComments() {
        loadTaskDetails()
        loadTaskComments()
    }

    private suspend fun loadTaskDetails() {
        if (!uiState.value.isEditMode) return
        val context = contextRef.get() ?: return
        withContext(Dispatchers.IO) {
            CommonTask.loadTaskDetails(context, iTaskID)
        }.onSuccess {
            loadTaskDetailsFromDB()
        }
    }

    private suspend fun loadTaskComments(currentPage: Int = 0) {
        val context = contextRef.get() ?: return
        val pageSize = Constants.Limits.PAGE_COMMENTS_DISPLAY
        withContext(Dispatchers.IO) {
            CommonTask.loadTaskComments(
                context = context,
                taskID = iTaskID,
                iStart = currentPage * pageSize,
                iLength = pageSize,
            )
        }.onSuccess { newComments ->
            updateUiState {
                val updated = (if (currentPage == 0) newComments else taskComments + newComments)
                copy(
                    taskComments = updated.sortedBy(ai_TaskComment::dtDateTime),
                    currentPageOfComments = currentPage,
                    hasMoreComments = newComments.size >= pageSize
                )
            }
        }
    }

    fun loadMoreTaskComments() {
        viewModelScope.launch {
            loadTaskComments(uiState.value.currentPageOfComments + 1)
        }
    }

    fun refreshTaskDetails(stateChange: (Boolean) -> Unit) {
        viewModelScope.launch {
            stateChange(true)
            loadTaskDetailsAndComments()
            stateChange(false)
        }
    }

    fun updateTitle(newTitle: String) = updateUiState {
        copy(task = task.apply { sTitle = newTitle })
    }

    fun updateDescription(newDescription: String) = updateUiState {
        copy(task = task.apply { sDescription = newDescription })
    }

    fun updateTaskSettings(newSettings: TaskSettings) = updateUiState {
        copy(taskSettings = newSettings)
    }

    fun updateTaskCustomInfo(newCustomInfo: ai_CustomInfo) = updateUiState {
        copy(taskCustomInfos = taskCustomInfos.map {
            if (it.iCustomInfoID == newCustomInfo.iCustomInfoID) newCustomInfo else it
        })
    }

    fun resetLoadingState() = updateUiState {
        copy(loadingState = LoadingState.None)
    }

    fun completeTask(completion: () -> Unit) {
        val task = uiState.value.task
        if (task.bClosed) return
        completeTask(task, completion)
    }

    fun completeTask(task: ai_Task, completion: () -> Unit) {
        val context = contextRef.get() ?: return
        viewModelScope.launch {
            showLoadingState("Completing task...")

            withContext(Dispatchers.IO) {
                CommonTask.completeTask(context, task.iSNotificationID)
            }.fold(
                onSuccess = {
                    loadTaskDetailsFromDB()
                    hideLoadingState()
                    completion()
                },
                onFailure = { error ->
                    updateUiState {
                        copy(
                            loadingState = LoadingState.Error(
                                error.message ?: "Failed to complete task"
                            )
                        )
                    }
                }
            )
        }
    }

    fun saveTask(success: (ai_Task) -> Unit) {
        val context = contextRef.get() ?: return
        viewModelScope.launch {
            showLoadingState("Saving task...")
            val currentState = uiState.value

            withContext(Dispatchers.IO) {
                if (currentState.isEditMode) {
                    CommonTask.updateTask(
                        context = context,
                        task = currentState.task,
                        settings = currentState.taskSettings,
                        customInfos = currentState.taskCustomInfos,
                    )
                } else {
                    CommonTask.createTask(
                        context = context,
                        iPropertyID = iAssetID,
                        sTitle = currentState.task.sTitle ?: "",
                        sDescription = currentState.task.sDescription ?: "",
                        iParentTaskID = iPTaskID,
                        settings = currentState.taskSettings
                    )
                }
            }.fold(
                onSuccess = { task ->
                    loadTaskDetailsFromDB()
                    hideLoadingState()
                    success(task)
                },
                onFailure = { error ->
                    updateUiState {
                        copy(
                            loadingState = LoadingState.Error(
                                error.message ?: "Failed to save task"
                            )
                        )
                    }
                }
            )
        }
    }

    fun uploadAndSaveTaskVideo(videoId: Long) {
        val video = ai_Video.findById(ai_Video::class.java, videoId) ?: return
        val context = contextRef.get() ?: return
        viewModelScope.launch {
            showLoadingState("Uploading video...")

            withContext(Dispatchers.IO) {
                CommonTask.uploadVideo(context, video)
            }.fold(
                onSuccess = {
                    saveTaskVideo(FileId.Remote(it.iSVideoID))
                },
                onFailure = { error ->
                    updateUiState {
                        copy(
                            loadingState = LoadingState.Error(
                                error.message ?: "Failed to upload video"
                            )
                        )
                    }
                }
            )
        }
    }

    fun saveTaskVideo(fileId: FileId, bDeleted: Boolean = false) {
        val context = contextRef.get() ?: return
        viewModelScope.launch {
            val iSVideoID = fileId.id
            showLoadingState(
                if (bDeleted) "Deleting video..." else "Saving video..."
            )

            withContext(Dispatchers.IO) {
                CommonTask.saveTaskVideo(context, iSVideoID, iTaskID, bDeleted)
            }.fold(
                onSuccess = {
                    db_Tasks.saveTask(it)
                    loadTaskDetailsFromDB()
                    if (bDeleted) CommonDB.DeleteVideoByServerID(iSVideoID)
                    hideLoadingState()
                },
                onFailure = { error ->
                    updateUiState {
                        copy(
                            loadingState = LoadingState.Error(
                                error.message ?: "Failed to ${if (bDeleted) "delete" else "save"} video"
                            )
                        )
                    }
                }
            )
        }
    }

    fun uploadAndSaveTaskPhoto(photoId: Long) {
        val photo = ai_Photo.findById(ai_Photo::class.java, photoId) ?: return
        val context = contextRef.get() ?: return
        viewModelScope.launch {
            showLoadingState("Uploading photo...")

            withContext(Dispatchers.IO) {
                CommonTask.uploadPhoto(context, photo)
            }.fold(
                onSuccess = { photo ->
                    saveTaskPhoto(FileId.Remote(photo.iSPhotoID))
                },
                onFailure = { error ->
                    updateUiState {
                        copy(
                            loadingState = LoadingState.Error(
                                error.message ?: "Failed to upload photo"
                            )
                        )
                    }
                }
            )
        }
    }

    fun saveTaskPhoto(photoId: FileId.Remote, bDeleted: Boolean = false) {
        val context = contextRef.get() ?: return
        viewModelScope.launch {
            showLoadingState(
                if (bDeleted) "Deleting photo..." else "Saving photo..."
            )
            withContext(Dispatchers.IO) {
                CommonTask.saveTaskPhoto(context, photoId.id, iTaskID, bDeleted)
            }.fold(
                onSuccess = {
                    db_Tasks.saveTask(it)
                    loadTaskDetailsFromDB()
                    if (bDeleted) CommonDB.DeletePhotoByServerID(photoId.id)
                    hideLoadingState()
                },
                onFailure = { error ->
                    updateUiState {
                        copy(
                            loadingState = LoadingState.Error(
                                error.message ?: "Failed to ${if (bDeleted) "delete" else "save"} photo"
                            )
                        )
                    }
                }
            )
        }
    }

    private fun showLoadingState(message: String) = updateUiState {
        copy(loadingState = LoadingState.Loading(message))
    }

    private fun hideLoadingState() = updateUiState {
        copy(loadingState = LoadingState.None)
    }

    fun uploadFileAndSaveTaskCustomInfo(customInfo: ai_CustomInfo?, path: String) {
        val customInfoItem = customInfo ?: return
        uploadFile(path) { fileId, fileName ->
            val updatedCustomInfo = customInfoItem.copy().apply { 
                val file = TaskCustomInfoFile(fileId, fileName)
                sValue = file.toJson() 
            }
            updateTaskCustomInfo(updatedCustomInfo)
            saveTask { }
        }
    }

    private fun uploadFile(path: String, onSuccess: (fileId: Int, fileName: String) -> Unit) {
        val context = contextRef.get() ?: return
        val file = ai_File().apply {
            iSObjectID = iAssetID
            sFile = path
            dtDateTime = Constants.dateFormat.format(Date())
            iSize = CommonHelper.GetFileLength(path)
        }
        viewModelScope.launch {
            showLoadingState("Uploading ...")

            withContext(Dispatchers.IO) {
                CommonRequest.uploadFile(context, file) { response, error ->
                    error?.let {
                        updateUiState {
                            copy(loadingState = LoadingState.Error(it.message ?: "Failed to upload file"))
                        }
                        return@uploadFile
                    }

                    response?.let { jsonResponse ->
                        val fileObj = jsonResponse.optJSONObject("oFile")
                        fileObj?.let {
                            val fileId = it.optInt("iFileID", 0)
                            val fileName = it.optString("sFileName", "")

                            if (fileId > 0) {
                                onSuccess(fileId, fileName)
                                // Clean up the file after saving the custom info
                                try {
                                    FileUtils.deleteFile(File(path))
                                } catch (e: Exception) {
                                    // Ignore
                                }
                            }
                        }
                    }
                    hideLoadingState()
                }
            }
        }
    }

    fun addTaskComment(sComments: String) {
        postTaskComment(sComments, ai_TaskComment.COMMENT_TYPE_TEXT)
    }

    private fun postTaskComment(sComments: String, sType: String) {
        val context = contextRef.get() ?: return
        viewModelScope.launch {
            showLoadingState(
                "Saving comment..."
            )

            withContext(Dispatchers.IO) {
                CommonTask.saveTaskComment(context, iTaskID, sComments, sType)
            }.fold(
                onSuccess = {
                    updateUiState {
                        copy(
                            taskComments = taskComments + it,
                            loadingState = LoadingState.None,
                            shouldScrollToBottom = true
                        )
                    }
                },
                onFailure = { error ->
                    updateUiState {
                        copy(
                            loadingState = LoadingState.Error(
                                error.message ?: "Failed to save comment"
                            )
                        )
                    }
                }
            )
        }
    }

    fun  addTaskCommentFile(filePath: String) {
        uploadFile(filePath) { fileId, fileName ->
            val file = TaskCommentFile(fileId, fileName)
            postTaskComment(file.toJson(), ai_TaskComment.COMMENT_TYPE_FILE)
        }
    }

    fun resetScrollToBottom() = updateUiState {
        copy(shouldScrollToBottom = false)
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun EditTaskView(
    viewModel: EditTaskViewModel,
    handleNavigation: (EditTaskAction) -> Unit,
    onUiStateChange: () -> Unit,
) {
    val uiState by viewModel.uiState.collectAsState()
    var isRefreshing by remember { mutableStateOf(false) }
    
    LaunchedEffect(uiState) {
        onUiStateChange()
    }

    val pullRefreshState = rememberPullRefreshState(
        refreshing = isRefreshing,
        onRefresh = {
            viewModel.refreshTaskDetails {
                isRefreshing = it
            }
        }
    )

    val listState = rememberLazyListState()

    LaunchedEffect(uiState.taskComments.size, uiState.shouldScrollToBottom) {
        uiState.taskComments.takeIf { it.isNotEmpty() }?.let {
            if (uiState.isEditMode && uiState.shouldScrollToBottom) {
                listState.scrollToBottom()
                viewModel.resetScrollToBottom()
            }
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(colorResource(id = R.color.colorPrimary))
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = getActionBarSize())
                .background(colors.colorF7F7F7)
        ) {
            // Asset address view only shows if the task is associated with an available asset
            uiState.task.sAssetAddress.takeIf(String::isNotEmpty)?.let {
                AssetAddressView(
                    assetAddress = uiState.task.sAssetAddress,
                    sInsItemTitle = uiState.task.sInsItemTitle,
                    backgroundColor = colors.colorD8D8D8,
                    contentColor = colors.color4A4A4A,
                    onTap = {
                        handleNavigation(EditTaskAction.BackToAssetDetails(uiState.task.iPropertyID))
                    }
                )
            }

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .pullRefresh(pullRefreshState)
            ) {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    state = listState,
                    verticalArrangement = Arrangement.spacedBy(dimens.zero)
                ) {
                    item {
                        AssetInfoSection(
                            isEditMode = uiState.isEditMode,
                            title = uiState.task.sTitle ?: "",
                            description = uiState.task.sDescription ?: "",
                            associatedVAsset = uiState.task.associatedVAsset,
                            taskSettings = uiState.taskSettings,
                            taskAttachments = uiState.taskAttachments,
                            onTitleChange = viewModel::updateTitle,
                            onDescriptionChange = viewModel::updateDescription,
                            onTaskSettingsChange = viewModel::updateTaskSettings,
                            onAddAttachment = { handleNavigation(EditTaskAction.AddAttachment(it)) },
                            onPhotoAction = { handleNavigation(EditTaskAction.PhotoAction(it)) },
                            onVideoAction = { handleNavigation(EditTaskAction.VideoAction(it)) },
                        )
                    }

                    if (uiState.isEditMode) {
                        if (uiState.shouldShowSubTasks) {
                            item {
                                TaskSubTasksView(
                                    task = uiState.task,
                                    onViewAll = {
                                        handleNavigation(
                                            EditTaskAction.ViewSubTasks(
                                                taskID = viewModel.iTaskID,
                                                assetID = viewModel.iAssetID,
                                            )
                                        )
                                    },
                                    onAddSubTask = { handleNavigation(EditTaskAction.AddSubTask) },
                                    onViewSubTask = { handleNavigation(EditTaskAction.ViewTaskDetails(it.iSNotificationID)) },
                                    onCompleteSubTask = { viewModel.completeTask(it) { } }
                                )
                            }
                        }

                        uiState.taskCustomInfos.takeIf { it.isNotEmpty() }?.let {
                            item {
                                TaskCustomInfosView(
                                    groupedCustomInfos = it.groupByGroup(),
                                    onCustomInfoChanged = viewModel::updateTaskCustomInfo,
                                    onFileAction = { customInfo, action, fileInfo ->
                                        handleNavigation(
                                            EditTaskAction.CustomInfoFileAction(customInfo, action, fileInfo)
                                        )
                                    }
                                )
                            }
                        }

                        uiState.taskComments.takeIf { it.isNotEmpty() }?.let {
                            item {
                                TaskCommentsView(
                                    comments = it,
                                    hasMoreComments = uiState.hasMoreComments,
                                    onLoadMore = viewModel::loadMoreTaskComments,
                                    onClickFile = { file ->
                                        handleNavigation(EditTaskAction.CommentFileAction(AttachmentAction.VIEW_FILE, file))
                                    }
                                )
                            }
                        }

                        // extra padding for the last item
                        item {
                            Spacer(modifier = Modifier.height(dimens.xlarge))
                        }
                    }
                }

                if (uiState.isEditMode) {
                    PullRefreshIndicator(
                        refreshing = isRefreshing,
                        state = pullRefreshState,
                        modifier = Modifier.align(Alignment.TopCenter),
                    )

                    TaskCommentComposeView(
                        modifier = Modifier.align(Alignment.BottomStart),
                        onSubmit = viewModel::addTaskComment,
                        onFileAction = { handleNavigation(EditTaskAction.CommentFileAction(it, null)) }
                    )
                }
            }
        }

        TitleView(
            title = stringResource(
                id = if (uiState.isEditMode) R.string.title_asset_edit_task
                     else R.string.title_asset_add_task
            ),
            modifier = Modifier.align(Alignment.TopCenter)
        )

        if (uiState.isTaskCompleted) {
            TaskCompletedView(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(end = dimens.extraLarge)
            )
        }

        LoadingView(
            state = uiState.loadingState,
            onDismissError = {
                viewModel.resetLoadingState()
                onUiStateChange()
            }
        )
    }
}

@Composable
private fun TaskCompletedView(modifier: Modifier) {
    Box(
        modifier = modifier.height(getActionBarSize()),
        contentAlignment = Alignment.Center
    ) {
        Row(
            modifier = Modifier
                .background(
                    color = Color(0xFF54B445).copy(alpha = 0.6f),
                    shape = RoundedCornerShape(dimens.extraMedium)
                )
                .border(
                    width = dimensionResource(R.dimen.margin_divider_normal),
                    color = Color(0xFF54B445),
                    shape = RoundedCornerShape(dimens.extraMedium)
                )
                .padding(start = dimens.smaller, end = dimens.semiMedium)
                .padding(vertical = dimens.smaller),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(dimens.smallest)
        ) {
            Icon(
                imageVector = Icons.Default.Check,
                contentDescription = null,
                tint = Color.White,
                modifier = Modifier.size(dimens.medium)
            )
            Text(
                text = "Completed",
                color = Color.White,
                fontSize = 12.sp,
                fontFamily = FontFamily.Default
            )
        }
    }
}

@Composable
private fun AssetInfoSection(
    isEditMode: Boolean = false,
    title: String,
    description: String,
    associatedVAsset: v_Asset? = null,
    taskSettings: TaskSettings,
    taskAttachments: TaskAttachments,
    onTitleChange: (String) -> Unit,
    onDescriptionChange: (String) -> Unit,
    onTaskSettingsChange: (TaskSettings) -> Unit,
    onAddAttachment: (AttachmentType) -> Unit,
    onPhotoAction: (TaskPhotoParams) -> Unit,
    onVideoAction: (TaskVideoParams) -> Unit,
) {
    ExpandableColumn {
        // title
        TaskTitleView(
            text = remember { mutableStateOf(title) },
            placeholder = "Enter title",
            textDidChange = onTitleChange
        )

        associatedVAsset?.let {
            TaskAssetAddressView(
                title = "Property Address",
                asset = it,
            )
        }

        // due date, assignee, status, priority, category, member
        TaskSettingsView(
            settings = taskSettings,
            onUpdate = {
                onTaskSettingsChange(it.settings)
            }
        )
        // task description
        TaskDescriptionView(
            text = remember { mutableStateOf(description) },
            placeholder = "Enter description",
            textDidChange = onDescriptionChange
        )
        // task attachments
        if (isEditMode) TaskAttachmentsView(
            attachments = taskAttachments,
            onAddAttachment = onAddAttachment,
            onPhotoAction = onPhotoAction,
            onVideoAction = onVideoAction,
        )
    }
}

@Composable
@Preview
private fun TaskCompletedViewPreview() {
    SnapTheme {
        val isTaskCompleted = true
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(colorResource(id = R.color.colorPrimary))
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = getActionBarSize())
                    .background(Color.White)
            ) {
                AssetAddressView(
                    assetAddress = "123, Main Street, New York",
                    backgroundColor = colors.colorD8D8D8,
                    contentColor = colors.color4A4A4A,
                    onTap = { }
                )
            }

            TitleView(
                title = stringResource(id = R.string.title_asset_edit_task),
                modifier = Modifier.align(Alignment.TopCenter)
            )

            if (isTaskCompleted) {
                TaskCompletedView(
                    modifier = Modifier.align(Alignment.TopEnd)
                )
            }

            TaskCommentComposeView(
                modifier = Modifier.align(Alignment.BottomStart),
                onSubmit = { }
            )
        }
    }
}