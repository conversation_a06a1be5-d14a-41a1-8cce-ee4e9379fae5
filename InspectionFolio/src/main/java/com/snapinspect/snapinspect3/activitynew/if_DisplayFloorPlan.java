package com.snapinspect.snapinspect3.activitynew;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.AsyncTask;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.webkit.JavascriptInterface;
import android.webkit.WebSettings;
import android.webkit.WebView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.afollestad.materialdialogs.MaterialDialog;
import com.google.gson.Gson;
import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Adapter.InsItemFloorPlanAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.async.InitFloorPlanTask;
import com.snapinspect.snapinspect3.util.BitmapUtils;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.views.InspectionItemTitleView;
import com.squareup.picasso.Picasso;
import org.json.JSONObject;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class if_DisplayFloorPlan extends Activity {

    private MaterialDialog materialDialog;
    private int mLastPhotoID = -1;
    private boolean mIsInitFloorPlan = false;

    public enum FloorPlanLayerType {
        ASSET, ITEM;

        public int layerLevel() {
            return this == FloorPlanLayerType.ITEM ? LAYER_LEVEL_ITEM : LAYER_LEVEL_ASSET;
        }
    }
    private static final String NORMAL_DELIMITER= "%@";
    private static final String LAYER_LEVEL_PLACEHOLDER = "`###LAYER###`";
    private static final String CANVAS_DATA_PLACEHOLDER = "`###CANVAS_DATA###`";
    private static final int LAYER_LEVEL_ASSET = 1;
    private static final int LAYER_LEVEL_ITEM = 2;

    private WebView mWebView;
    private ai_FloorPlan mFloorPlan;
    private ai_InsItem mInsItem;
    private String mFloorPlanImageBase64;
    private InsItemFloorPlanAdapter.FloorPlanType mFloorPlanType;
    private FloorPlanLayerType mFloorPlanLayerType;

    public static Intent newIntent(
            Context context, long iFloorPlanID, long iInsItemID,
            FloorPlanLayerType layerType, InsItemFloorPlanAdapter.FloorPlanType floorPlanType) {
        Intent intent = new Intent(context, if_DisplayFloorPlan.class);
        intent.putExtra(Constants.Extras.iFloorPlanID, iFloorPlanID);
        intent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        intent.putExtra(Constants.Extras.floorPlanLayerType, layerType.ordinal());
        intent.putExtra(Constants.Extras.floorPlanType, floorPlanType.ordinal());
        return intent;
    }

    public static Intent newIntent(Context context, long iFloorPlanID, FloorPlanLayerType layerType) {
        Intent intent = new Intent(context, if_DisplayFloorPlan.class);
        intent.putExtra(Constants.Extras.iFloorPlanID, iFloorPlanID);
        intent.putExtra(Constants.Extras.floorPlanLayerType, layerType.ordinal());
        return intent;
    }

    /**
     * Creates a new Intent for launching the if_DisplayFloorPlan activity for initializing a new blueprint
     */
    public static Intent newIntent(Context context, int iSAssetID, long iPhotoID) {
        Intent intent = new Intent(context, if_DisplayFloorPlan.class);
        intent.putExtra(Constants.Extras.iSAssetID, iSAssetID);
        intent.putExtra(Constants.Extras.iPhotoID, iPhotoID);
        intent.putExtra(Constants.Extras.floorPlanLayerType, FloorPlanLayerType.ASSET.ordinal());
        intent.putExtra(Constants.Extras.IS_INITIAL_FLOOR_PLAN, true);
        return intent;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        try {
            if (getIntent().getLongExtra(Constants.Extras.iFloorPlanID, -1) > 0) {
                setTheme(R.style.RemoveShadowActionBar);
            }
            super.onCreate(savedInstanceState);
            setContentView(R.layout.activity_floor_plan);
            getActionBar().show();
            getActionBar().setDisplayHomeAsUpEnabled(true);
            initializeData();
            initializeViews();
            getFloorPlanImageBase64(this::loadFloorPlanHtml);
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception("if_DisplayFloorPlan.onCreate", "Exception:" + e.getMessage(), e);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mWebView != null) {
            mWebView.destroy();
            mWebView = null;
        }
    }

    private void initializeData() {
        mIsInitFloorPlan = getIntent().getBooleanExtra(Constants.Extras.IS_INITIAL_FLOOR_PLAN, false);
        mFloorPlanLayerType = getFloorPlanLayerTypeFromIntent();
        mFloorPlanType = getFloorPlanTypeFromIntent();
        mInsItem = getInsItemFromIntent();
        mFloorPlan = getFloorPlanFromIntent();
    }

    private InsItemFloorPlanAdapter.FloorPlanType getFloorPlanTypeFromIntent() {
        int floorPlanTypeIndex = getIntent().getIntExtra(Constants.Extras.floorPlanType, 0);
        return InsItemFloorPlanAdapter.FloorPlanType.values()[floorPlanTypeIndex];
    }

    private FloorPlanLayerType getFloorPlanLayerTypeFromIntent() {
        int floorPlanTypeIndex = getIntent().getIntExtra(Constants.Extras.floorPlanLayerType, 0);
        return FloorPlanLayerType.values()[floorPlanTypeIndex];
    }

    private ai_Photo getPhotoFromIntent() {
        long iPhotoID = getIntent().getLongExtra(Constants.Extras.iPhotoID, -1);
        return iPhotoID > 0 ? ai_Photo.findById(ai_Photo.class, (long) iPhotoID) : null;
    }

    private ai_FloorPlan getFloorPlanFromIntent() {
        if (mIsInitFloorPlan) {
            ai_Photo aiPhoto = getPhotoFromIntent();
            if (aiPhoto != null && aiPhoto.getId() > 0) {
                try {
                    ai_FloorPlan oFloorPlan = new ai_FloorPlan();
                    oFloorPlan.iSAssetID = getIntent().getIntExtra(Constants.Extras.iSAssetID, -1);
                    oFloorPlan.setId(0L);
                    // Copy the file to the floor plan folder
                    FileUtils.copyFile(new File(aiPhoto.getFile()), new File(oFloorPlan.savedPlanFilePath()), false);
                    oFloorPlan.sPlanPath = oFloorPlan.getPlanFileName();
                    return oFloorPlan;
                } catch (Exception e) {
                    ai_BugHandler.ai_Handler_Exception("if_DisplayFloorPlan.getFloorPlanFromIntent", "Exception:" + e.getMessage(), e);
                }
            }
            return null;
        } else {
            long iFloorPlanID = getIntent().getLongExtra(Constants.Extras.iFloorPlanID, -1);
            return iFloorPlanID > 0 ? CommonDB_FloorPlan.getFloorPlanWithID(iFloorPlanID) : null;
        }
    }

    private ai_InsItem getInsItemFromIntent() {
        long iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, -1);
        return iInsItemID > 0 ? db_InsItem.GetInsItem_ByID(iInsItemID) : null;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (shouldDisplaySaveButton()) {
            getMenuInflater().inflate(R.menu.menu_save, menu);
        }
        return super.onCreateOptionsMenu(menu);
    }

    private boolean shouldDisplaySaveButton() {
        switch (mFloorPlanLayerType) {
            case ASSET:
                return !CommonJson.disableEditAsset(this);
            case ITEM:
                return true;
            default:
                return false;
        }
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        boolean handled;
        switch (item.getItemId()) {
            case R.id.action_menu_save:
                saveFloorPlan();
                handled = true;
                break;
            default:
                super.onBackPressed();
                handled = super.onOptionsItemSelected(item);
                break;
        }
        return handled;
    }

    @SuppressLint("SetJavaScriptEnabled")
    private void initializeViews() {
        mWebView = findViewById(R.id.webview_floor_plan);
        if (mWebView == null) return;
        WebSettings settings = mWebView.getSettings();
        settings.setJavaScriptEnabled(true);
        settings.setAllowFileAccess(true);
        settings.setAllowContentAccess(true);
        settings.setLoadWithOverviewMode(true);
        settings.setUseWideViewPort(true);
        settings.setDomStorageEnabled(true);

        // Add the javascript interface for saving the image editor data
        mWebView.addJavascriptInterface(new JSInterface(this), "jsInterface");

        // Update the title of the action bar
        if (mFloorPlan != null) getActionBar().setTitle(mFloorPlan.sTitle);

        // if the blueprint is annotated, show the titles of the inspection item
        InspectionItemTitleView titleView = findViewById(R.id.inspection_item_title_view);
        if (FloorPlanLayerType.ITEM == mFloorPlanLayerType) {
            titleView.setVisibility(View.VISIBLE);
            titleView.setItemTitles(mInsItem.getParentItemName(), mInsItem.sName);
        } else {
            titleView.setVisibility(View.GONE);
        }
    }

    private void loadFloorPlanHtml() {
        String floorPlanHtmlPath = String.format("%s%s/%s", CommonHelper.sFileRoot,
                Constants.Paths.FLOOR_PLAN_FOLDER, Constants.FloorPlanFiles.FLOOR_PLAN_HTML_FILE_NAME);
        FileUtils.writeToFile(floorPlanHtmlPath, getFloorPlanHtml());
        mWebView.loadUrl(floorPlanHtmlPath);
    }

    private String getFloorPlanHtml() {
        if (mFloorPlan == null) return "";

        String floorPlanHtmlPath = String.format("%s/%s",
                Constants.FloorPlanFiles.FLOOR_PLAN_HTML_FOLDER, Constants.FloorPlanFiles.FLOOR_PLAN_HTML_FILE_NAME);
        String html = StringUtils.getFileContent(this, floorPlanHtmlPath);
        String[] htmlSplit = html.split(NORMAL_DELIMITER);
        String[] jsFileNames = {
            Constants.FloorPlanFiles.FLOOR_PLAN_UMD_REACT_DEVELOPMENT_JS,
            Constants.FloorPlanFiles.FLOOR_PLAN_UMD_REACT_DOM_DEVELOPMENT_JS,
            Constants.FloorPlanFiles.FLOOR_PLAN_STYLED_COMPONENTS_JS,
            Constants.FloorPlanFiles.FLOOR_PLAN_FABRIC_JS,
            Constants.FloorPlanFiles.FLOOR_PLAN_DIST_IIFF_INDEX_JS,
        };

        ArrayList<String> htmlStrings = new ArrayList<>();
        // insert the js file content into the html
        // the split length should be more than the js file names length + 2, because there is an extra `%@`
        // at the end of the html for `bgImage`
        if (htmlSplit.length >= jsFileNames.length + 2) {
            for (int i = 0; i < jsFileNames.length; i++) {
                htmlStrings.add(htmlSplit[i]);
                htmlStrings.add(StringUtils.getFileContent(
                    this, Constants.FloorPlanFiles.FLOOR_PLAN_HTML_FOLDER + "/" + jsFileNames[i]));
            }

            // `bgImage` is the base64 string of the blueprint image
            htmlStrings.add(htmlSplit[jsFileNames.length]);
            String planImageBase64;
            if (StringUtils.isEmpty(mFloorPlan.sMarks))
                planImageBase64 = StringUtils.ifEmpty(mFloorPlanImageBase64, "");
            else
                planImageBase64 = "";
            htmlStrings.add(planImageBase64);
            htmlStrings.add(htmlSplit[jsFileNames.length + 1]);
        }
        String result = StringUtils.replaceLast(
            String.join("", htmlStrings), CANVAS_DATA_PLACEHOLDER, getCanvasData());
        return StringUtils.replaceLast(
                result, LAYER_LEVEL_PLACEHOLDER, String.valueOf(mFloorPlanLayerType.layerLevel()));
    }
    private String getCanvasData() {
        if (mIsInitFloorPlan && StringUtils.isEmpty(mFloorPlan.sMarks)) { return "''"; }
        return "JSON.stringify({version: '5.2.1', objects: " + getImageEditorData() + ", background: 'white'})";
    }

    private void getFloorPlanImageBase64(Runnable callback) {
        if (mFloorPlan == null) return;
        MaterialDialog loadingDialog = CommonUI.ShowMaterialProgressDialog(this,
                getString(R.string.loading_blueprints), getString(R.string.yubikit_prompt_wait));
        BitmapUtils.getBitmapFromFilePath(mFloorPlan.getPlanFilePath(), (bitmap, error) -> {
            mFloorPlanImageBase64 = BitmapUtils.encodeBase64(bitmap, Bitmap.CompressFormat.JPEG);
            runOnUiThread(() -> {
                if (error != null) {
                    CommonUI.DismissMaterialProgressDialog(if_DisplayFloorPlan.this, loadingDialog);
                    CommonUI.ShowAlert(if_DisplayFloorPlan.this,
                            getString(R.string.title_alert_error), error.getMessage());
                } else if (mFloorPlanImageBase64 == null) {
                    CommonUI.DismissMaterialProgressDialog(if_DisplayFloorPlan.this, loadingDialog);
                    CommonUI.ShowAlert(if_DisplayFloorPlan.this,
                            getString(R.string.title_alert_error),
                            getString(R.string.failed_get_floor_plan_image));
                } else {
                    // repair the blueprint image base64 string
                    mFloorPlanImageBase64 = mFloorPlanImageBase64.replace("\n", "");
                    callback.run();
                    CommonUI.DismissMaterialProgressDialog(if_DisplayFloorPlan.this, loadingDialog);
                }
            });
        });
    }

    private String getImageEditorData() {
        if (mFloorPlan == null || mFloorPlanImageBase64 == null || StringUtils.isEmpty(mFloorPlan.sMarks)) return "";
        String marksJsonString = mFloorPlan.sMarks;
        // replace the attribute "src" for the first element of the attribute "objects"
        // with the blueprint image base64 string

        String editorData = null;
        try {
            Gson gson = new Gson();
            List<Map<String, Object>> markObjects = (List<Map<String, Object>>) gson.fromJson(marksJsonString, List.class);
            if (markObjects != null && !markObjects.isEmpty()) {
                for (Map<String, Object> markObject : markObjects) {
                    if (markObject != null && "image".equals(markObject.get("type"))) {
                        markObject.put("src", mFloorPlanImageBase64);
                        break;
                    }
                }

                // Add the extra marks for the inspection item
                if (FloorPlanLayerType.ITEM == mFloorPlanLayerType
                        && InsItemFloorPlanAdapter.FloorPlanType.ANNOTATED == mFloorPlanType) {
                    Map<String, Object> annotation = db_InsItem.getDropPinData(mInsItem, mFloorPlan.iSFloorPlanID);
                    Object extraMarksObj = annotation.get(Constants.Keys.objects);
                    if (!annotation.isEmpty() && extraMarksObj instanceof List) {
                        List<Map<String, Object>> extraMarks = (List<Map<String, Object>>) extraMarksObj;
                        if (!extraMarks.isEmpty()) {
                            markObjects.addAll(extraMarks);
                        }
                    }
                }
            }

            editorData = gson.toJson(markObjects);
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception("if_DisplayFloorPlan.getImageEditorData", "Exception:" + e.getMessage(), e);
        }
        return StringUtils.isEmpty(editorData) ? "" : editorData;
    }

    private void saveFloorPlan() {
        if (mFloorPlan == null) return;
        // Save the image editor data
        mWebView.evaluateJavascript("saveImageEditorData();", null);

        // Show the progress dialog
        CommonUI.DismissMaterialProgressDialog(this, materialDialog);
        materialDialog = CommonUI.ShowMaterialProgressDialog(this, getString(R.string.saving_blueprints), getString(R.string.yubikit_prompt_wait));
    }

    private void saveFloorPlanThumbnail(String imageBase64) {
        if (StringUtils.isEmpty(imageBase64)) return;

        Bitmap bitmap = BitmapUtils.decodeBase64(imageBase64);
        if (FloorPlanLayerType.ASSET == mFloorPlanLayerType) {
            // Save the image editor data to the server
            CommonHelper.SaveImage(mFloorPlan.savedImageFilePath(), bitmap);
            Picasso.get().invalidate(new File(mFloorPlan.getImageFilePath()));
        } else if (FloorPlanLayerType.ITEM == mFloorPlanLayerType) {
            // Save the image editor data to the local and insert a new ai_Photo record
            O_FileName oFileName = O_FileName.getPhotoFileName();
            CommonHelper.SaveImage(oFileName.sFilePath, bitmap);
            /*Create thumbnail*/
            CommonHelper.SaveThumb(oFileName.sThumbNail, bitmap);

            if (oFileName.sThumbNail != null && oFileName.sFilePath != null && mInsItem != null) {
                long photoID = CommonDB.InsertInsItemPhoto(this, mInsItem.getId().intValue(),
                        oFileName.sThumbNail, oFileName.sFilePath, 0, null);
                ai_Photo oPhoto = ai_Photo.findById(ai_Photo.class, (long) photoID);
                if (oPhoto != null) {
                    oPhoto.sComments = CommonJson.AddJsonKeyValue(oPhoto.sComments, Constants.Keys.kFloorPlan, "1");
                    oPhoto.save();
                }
                mLastPhotoID = (int) photoID;
            }
        }
    }

    private void saveFloorPlanAnnotation(Map<String, Object> data) {
        try {
            Object layer2MarksObj = data.get(Constants.Keys.LAYER_2_DATA);
            if (layer2MarksObj instanceof List) {
                List<Map<String, Object>> extraMarks = (List<Map<String, Object>>) layer2MarksObj;
                db_InsItem.addDropPinData(mInsItem, mFloorPlan.iSFloorPlanID, extraMarks, mLastPhotoID);
            }
            CommonUI.DismissMaterialProgressDialog(this, materialDialog);
            finish();
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception("if_DisplayFloorPlan.saveFloorPlanAnnotation", "Exception:" + e.getMessage(), e);
        }
    }

    private void submitFloorPlan(Map<String, Object> data) {
        CommonUI.DismissMaterialProgressDialog(this, materialDialog);
        if (!NetworkUtils.isNetworkAvailable(this)) {
            CommonUI.ShowAlert(this, getString(R.string.title_alert_error), getString(R.string.failed_connection));
            return;
        }

        try {
            Object sMarksObj = data.get(Constants.Keys.LAYER_1_DATA);
            if (!(sMarksObj instanceof List)) {
                CommonUI.ShowAlert(this, getString(R.string.title_alert_error), getString(R.string.failed_to_save_blueprint));
                return;
            }

            // Save the image editor data to the local database
            String sMarks = new Gson().toJson(sMarksObj);
            ai_FloorPlan floorPlan = CommonDB_FloorPlan.getFloorPlan(mFloorPlan.iSFloorPlanID);
            if (floorPlan != null) {
                floorPlan.sMarks = sMarks;
                floorPlan.save();
            }

            // Upload "sMarks" to the server and the new blueprint image
            UploadFloorPlanTask.UploadParams uploadParams = new UploadFloorPlanTask.UploadParams(
                    mFloorPlan.iSFloorPlanID, sMarks,
                    CommonHelper.GetPreferenceString(this, Constants.Keys.iCustomerID),
                    CommonHelper.GetPreferenceString(this, Constants.Keys.sToken),
                    mFloorPlan.getImageFilePath());
            new UploadFloorPlanTask(this, uploadParams).execute();
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception("if_DisplayFloorPlan.submitFloorPlan", "Exception:" + e.getMessage(), e);
        }
    }

    /**
     * This class represents an asynchronous task for uploading the blueprint image and the marks
     */
    private static class UploadFloorPlanTask extends AsyncTask<Void, Void, JSONObject> {
        private final UploadParams uploadParams;
        private final WeakReference<if_DisplayFloorPlan> activityReference;
        private final MaterialDialog progressDialog;

        public UploadFloorPlanTask(if_DisplayFloorPlan activity, UploadParams params) {
            uploadParams = params;
            activityReference = new WeakReference<>(activity);
            progressDialog = CommonUI.ShowMaterialProgressDialog(
                    activity, activity.getString(R.string.uploading_blueprint), activity.getString(R.string.yubikit_prompt_wait));
        }

        @Override
        protected JSONObject doInBackground(Void... voids) {
            if_DisplayFloorPlan activity = activityReference.get();
            if (activity == null) return null;

            HashMap<String, String> params = new HashMap<>();
            params.put(Constants.Extras.iFloorPlanID, String.valueOf(uploadParams.iSFloorPlanID));
            params.put(Constants.Keys.sMarks, uploadParams.imageEditorData);
            params.put(Constants.Keys.iCustomerID, uploadParams.customerID);
            params.put(Constants.Keys.sToken, uploadParams.token);

            return IF_SyncClient.UploadData(
                    "/IOAPI/UploadFloorPlan_Marks", params, uploadParams.filePath, "fileData");
        }

        @Override
        protected void onPostExecute(JSONObject oReturn) {
            if_DisplayFloorPlan activity = activityReference.get();
            CommonUI.DismissMaterialProgressDialog(activity, progressDialog);
            if (activity == null) return;

            if (oReturn != null) {
                handleResult(oReturn, activity);
            } else {
                CommonUI.ShowAlert(activity,
                    activity.getString(R.string.title_alert_error), activity.getString(R.string.failed_to_save_blueprint));
            }
        }

        private void handleResult(JSONObject result, if_DisplayFloorPlan activity) {
            if (result.optBoolean(activity.getString(R.string.response_success))) {
                activity.finish();
            } else {
                CommonUI.ShowAlert(activity, activity.getString(R.string.title_alert_error), result.optString("Message"));
            }
        }

        public static class UploadParams {
            final int iSFloorPlanID;
            final String imageEditorData;
            final String customerID;
            final String token;
            final String filePath;

            public UploadParams(int iSFloorPlanID, String imageEditorData,
                                String customerID, String token, String filePath) {
                this.iSFloorPlanID = iSFloorPlanID;
                this.imageEditorData = imageEditorData;
                this.customerID = customerID;
                this.token = token;
                this.filePath = filePath;
            }
        }
    }

    private static class JSInterface {
        private final WeakReference<if_DisplayFloorPlan> activityReference;
        JSInterface(if_DisplayFloorPlan activity) {
            activityReference = new WeakReference<>(activity);
        }

        @JavascriptInterface
        public void saveImageEditorData(String data) {
            if_DisplayFloorPlan activity = activityReference.get();
            if (activity == null) return;
            try {
                Gson gson = new Gson();
                Map<String, Object> dataJson = gson.fromJson(data, Map.class);
                Object imageBase64Obj = dataJson.get(Constants.Keys.canvasImageData);
                if (imageBase64Obj instanceof String) {
                    String imageBase64 = (String) imageBase64Obj;
                    activity.saveFloorPlanThumbnail(imageBase64);
                }

                // Save the image editor data
                if (FloorPlanLayerType.ITEM == activity.mFloorPlanLayerType) {
                    activity.saveFloorPlanAnnotation(dataJson);
                } else if (FloorPlanLayerType.ASSET == activity.mFloorPlanLayerType) {
                    // Save the image editor data to the server
                    activity.submitFloorPlan(dataJson);
                }
            } catch (Exception e) {
                ai_BugHandler.ai_Handler_Exception("if_DisplayFloorPlan.JSInterface.saveImageEditorData", "Exception:" + e.getMessage(), e);
            }
        }

        @JavascriptInterface
        public void initializedCallback() {
            if_DisplayFloorPlan activity = activityReference.get();
            if (activity == null || !activity.mIsInitFloorPlan) return;
            // Initialize the blueprint
            new InitFloorPlanTask(
                activity,
                activity.getPhotoFromIntent(),
                activity.mFloorPlan.iSAssetID,
                iSFloorPlanID -> {
                    // Delete the original file
                    FileUtils.deleteFile(new File(activity.mFloorPlan.savedPlanFilePath()));
                    // Update the blueprint object
                    activity.mFloorPlan = CommonDB_FloorPlan.getFloorPlan(iSFloorPlanID);
                    // save the canvas image data automatically
                    activity.runOnUiThread(activity::saveFloorPlan);

                }
            ).execute();
        }
    }
}
