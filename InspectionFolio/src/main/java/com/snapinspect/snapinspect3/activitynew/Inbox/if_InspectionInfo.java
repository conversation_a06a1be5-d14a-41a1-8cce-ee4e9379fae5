package com.snapinspect.snapinspect3.activitynew.Inbox;

import android.graphics.Color;
import android.os.Bundle;
import android.view.MenuItem;
import android.view.View;
import androidx.annotation.Nullable;
import androidx.appcompat.app.ActionBar;
import androidx.appcompat.app.AppCompatActivity;
import com.afollestad.materialdialogs.MaterialDialog;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.SupportMapFragment;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.MarkerOptions;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.IF_Object.ai_JsonStatus;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.TextViewUtils;
import com.snapinspect.snapinspect3.views.RoundTextView;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class if_InspectionInfo extends AppCompatActivity {
    private int iSInsID;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_inspection_info);
        ActionBar actionBar = getSupportActionBar();
        if (actionBar != null) {
            actionBar.setLogo(R.drawable.ic_launcher);
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setTitle(R.string.title_activity_if_inspection_info);
        }
        findViewById(R.id.inspection_status).setVisibility(View.GONE);
        findViewById(R.id.inspection_summary).setVisibility(View.GONE);
        findViewById(R.id.inspection_details).setVisibility(View.GONE);

        iSInsID = getIntent().getIntExtra(Constants.Extras.iSInsID, 0);
        requestIns(true);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        onBackPressed();
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    private void requestIns(boolean showingProgressDialog) {
        if (iSInsID > 0) {
            MaterialDialog oDialog = null;
            if (showingProgressDialog)
                oDialog = CommonUI.ShowMaterialProgressDialog(this, "Running", "Connecting to Server");
            MaterialDialog finalODialog = oDialog;
            CommonRequest.requestURL(this, "/IOAPI/GetInspection", () -> {
                RequestParams oParams = new RequestParams();
                oParams.add("iCustomerID", CommonHelper.GetPreferenceString(if_InspectionInfo.this, "iCustomerID"));
                oParams.add("sToken", CommonHelper.GetPreferenceString(if_InspectionInfo.this, "sToken"));
                oParams.add("iInspectionID", "" + iSInsID);
                return oParams;
            }, (response, error) -> {
                if (response != null) {
                    try {
                        JSONObject oInspection = response.getJSONObject("oInspection");
                        ai_Inspection oIns = new ai_Inspection(oInspection);
                        oIns.save();
                        String inspector = CommonJson.getPropertyInspector(oInspection.getInt("iCustomerID"));
                        updateViewWithIns(oIns, inspector);
                    } catch (JSONException e) {
                        ai_BugHandler.ai_Handler_Exception(e);
                    }
                } else if (error != null) {
                    CommonUI.ShowAlert(this, "Error", error.getMessage());
                }
                CommonUI.DismissMaterialProgressDialog(finalODialog);
            });
        }
    }

    private void updateViewWithIns(ai_Inspection oIns, String inspector) {
        findViewById(R.id.inspection_details).setVisibility(View.VISIBLE);
        /// Inspection Details
        TextViewUtils.updateText(findViewById(R.id.txt_inspection_title), oIns.sTitle);
        TextViewUtils.updateText(findViewById(R.id.txt_inspection_type), oIns.sInsTitle);
        TextViewUtils.updateText(findViewById(R.id.txt_inspection_start), oIns.dtStartDate);
        TextViewUtils.updateText(findViewById(R.id.txt_inspection_end), oIns.dtEndDate);
        TextViewUtils.updateText(findViewById(R.id.txt_inspection_inspector), inspector);

        /// Inspection Summary
        String lng = CommonJson.GetJsonKeyValue("Long", oIns.sCustomOne);
        String lat = CommonJson.GetJsonKeyValue("Lat", oIns.sCustomOne);
        View summaryView = findViewById(R.id.inspection_summary);
        if (!StringUtils.isEmpty(lng) && !StringUtils.isEmpty(lat)) {
            summaryView.setVisibility(View.VISIBLE);
            TextViewUtils.updateText(findViewById(R.id.txt_inspection_location), String.format("%s, %s", lat, lng));
            SupportMapFragment mapFragment =
                    (SupportMapFragment) getSupportFragmentManager().findFragmentById(R.id.map_inspection_location);
            if (mapFragment != null) {
                mapFragment.getMapAsync(googleMap -> {
                    LatLng dst = new LatLng(Double.parseDouble(lat), Double.parseDouble(lng));
                    googleMap.addMarker(new MarkerOptions().position(dst));
                    googleMap.moveCamera(CameraUpdateFactory.newLatLngZoom(dst, 16));
                });
            }
        } else {
            summaryView.setVisibility(View.GONE);
        }

        /// Inspection Status
        RoundTextView statusTextView = findViewById(R.id.txt_inspection_status);
        statusTextView.setCornerRadius(10);
        String status = CommonJson.GetJsonKeyValue("Status", oIns.sCustomOne);
        String statusColorCode = CommonJson.GetJsonKeyValue("S_Code", oIns.sCustomOne);
        View statusLayout = findViewById(R.id.inspection_status);
        if (!StringUtils.isEmpty(status) && !StringUtils.isEmpty(statusColorCode)) {
            statusLayout.setVisibility(View.VISIBLE);
            statusTextView.setText(status);
            statusTextView.setSolidColor(Color.parseColor(statusColorCode));
            statusTextView.setOnClickListener(v -> showsInspectionStatusOptions());
        } else {
            statusLayout.setVisibility(View.GONE);
        }
    }

    private void showsInspectionStatusOptions() {
        final List<ai_JsonStatus> arrStatus = CommonHelper.GetInspectionStatus(this);
        ArrayList<String> strings = new ArrayList<>();
        for (ai_JsonStatus oStatus : arrStatus) {
            strings.add(oStatus.sName);
        }
        String[] options = strings.toArray(new String[0]);
        new MaterialDialog.Builder(this)
                .title("Update Status")
                .items(options)
                .itemsCallbackSingleChoice(-1, (dialog, view, which, text) -> {
                    try {
                        ai_JsonStatus oStatus = arrStatus.get(which);
                        updateInspectionStatus(oStatus.sName, oStatus.sColorCode);
                    } catch (Exception e) {
                        //
                    }
                    return true;
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    private void updateInspectionStatus(String sName, String sColorCode) {
        final MaterialDialog progressDialog =
                CommonUI.ShowMaterialProgressDialog(this, "Message", "Processing...");
        final String sURL = "/IOAPI/UpdateInspectionStatusAPI";
        CommonRequest.requestURL(this, sURL, () -> {
            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(if_InspectionInfo.this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(if_InspectionInfo.this, "sToken"));
            oParams.add("iInspectionID", String.valueOf(iSInsID));
            oParams.add("sStatus", sName);
            oParams.add("sColorCode", sColorCode);
            return oParams;
        }, (response, error) -> {
            ai_Inspection oIns = CommonDB_Inspection.GetInspectionBySID(iSInsID);
            if (oIns != null && response != null) requestIns(false);
            CommonUI.DismissMaterialProgressDialog(progressDialog);
        });
    }
}
