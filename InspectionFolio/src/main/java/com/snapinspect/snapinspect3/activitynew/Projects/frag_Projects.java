package com.snapinspect.snapinspect3.activitynew.Projects;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.ResultReceiver;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_Project;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.async.SyncProjectService;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.ThrottledSearch;
import com.snapinspect.snapinspect3.views.EmptyDataView;
import com.softw4re.views.InfiniteListView;
import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;
import java.util.*;

public class frag_Projects extends Fragment implements ProjectsListAdapter.Listener {
    private InfiniteListView<ai_Project> lvProjects;
    private boolean isListViewLoading = false;
    private MaterialDialog progressDialog;
    private EditText searchText;
    private View projectsContainer;
    private SwipeRefreshLayout emptyDataRefreshLayout;
    private EmptyDataView emptyDataView;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.frag_projects, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        emptyDataRefreshLayout = view.findViewById(R.id.view_swipe_refresh);
        emptyDataRefreshLayout.setOnRefreshListener(() -> {
            isListViewLoading = true;
            loadProjects();
        });
        projectsContainer = view.findViewById(R.id.projects_container);
        lvProjects = view.findViewById(R.id.lv_Projects);
        searchText = view.findViewById(R.id.search_projects);
        new ThrottledSearch(getActivity(), searchTerm -> reloadListView()).bindTo(searchText);
        searchText.setOnEditorActionListener((v, actionId, event) -> {
            if (EditorInfo.IME_ACTION_SEARCH == actionId) {
                CommonHelper.hideSoftKeyboard(getActivity());
                return true;
            }
            return false;
        });

        emptyDataView = view.findViewById(R.id.view_empty_data);
        emptyDataView.setListener(() ->
                CommonHelper.openURL(getContext(), getString(R.string.projects_support_url)));
    }

    @Override
    public void onAttach(@NonNull @NotNull Context context) {
        super.onAttach(context);
        // Load projects from server
        String dateValue = CommonHelper.GetPreferenceString(getContext(), Constants.Keys.sProjectsLastUpdated);
        Date lastUpdatedDate = DateUtils.parse(dateValue, "yyyy-MM-dd HH:mm:ss");
        boolean isForceUpdate = CommonHelper.getInt(CommonHelper.GetPreferenceString(context, Constants.Keys.bForceSyncProjects)) > 0;
        boolean isFirstTime = isFirstTimeSyncProjects();
        if (isForceUpdate || isFirstTime || lastUpdatedDate == null
                || DateUtils.daysPassed(new Date(), lastUpdatedDate) > 1) {
            isListViewLoading = false;
            loadProjects();
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Load projects for local database
        reloadListView();
    }

    @Override
    public void onRefresh() {
        isListViewLoading = true;
        loadProjects();
    }

    private void loadProjects() {
        if (getContext() == null) return;
        Intent intent = new Intent(getContext(), SyncProjectService.class);
        intent.putExtra(Constants.Keys.oResultReceiver,
                new SyncProjectResultReceiver(new Handler(Looper.getMainLooper()), this));
        intent.putExtra(Constants.Keys.bInitialSync, isFirstTimeSyncProjects());
        getContext().startService(intent);
    }

    @Override
    public void didSelectProject(ai_Project project) {
        Intent intent = new Intent(getActivity(), if_ProjectInspection.class);
        intent.putExtra(Constants.Extras.iProjectID, project.getId());
        startActivity(intent);
    }

    private void reloadListView() {
        if (isFirstTimeSyncProjects()) return;
        String searchTerm = searchText.getText().toString();
        List<ai_Project> lsProject;
        int currentUserId = CommonHelper.getInt(CommonHelper.GetPreferenceString(getContext(), Constants.Keys.iCustomerID));
        String filterInspectorStatement =
                (currentUserId > 0 && !CommonJson.isCompanyAdmin(getContext()))
                        ? "ARR_INSPECTOR LIKE '%[" + currentUserId + "%]' AND " : "";
        if (!StringUtils.isEmpty(searchTerm)) {
         lsProject = ai_Project.find(ai_Project.class, filterInspectorStatement +
                        "B_DELETED = ? AND B_ACTIVE = ? AND (" +
                        "S_REFERENCE LIKE '%%" + searchTerm + "%%' OR " +
                        "S_NAME LIKE '%%" + searchTerm + "%%' OR " +
                        "S_DESCRIPTION LIKE '%%" + searchTerm + "%%')", "0", "1");
        } else {
            lsProject = ai_Project.find(ai_Project.class, filterInspectorStatement +
                    "B_DELETED = ? AND B_ACTIVE = ?", "0", "1");
        }

        // Sort projects by the count of uncompleted inspections
        Collections.sort(lsProject, (o1, o2) -> o2.getUncompletedInsCount() - o1.getUncompletedInsCount());

        if (!lsProject.isEmpty() || !StringUtils.isEmpty(searchTerm)) {
            ProjectsListAdapter adapter = new ProjectsListAdapter(getActivity(), -1, new ArrayList<>(lsProject));
            adapter.setListener(this);
            lvProjects.setAdapter(adapter);
            adapter.notifyDataSetChanged();
            lvProjects.hasMore(false);
            projectsContainer.setVisibility(View.VISIBLE);
            emptyDataRefreshLayout.setVisibility(View.GONE);
        } else {
            projectsContainer.setVisibility(View.GONE);
            emptyDataRefreshLayout.setVisibility(View.VISIBLE);
        }
    }

    private void showLoading(String sTitle, String sMessage) {
        if (isListViewLoading) {
            if (lvProjects != null) lvProjects.startLoading();
            if (emptyDataRefreshLayout.getVisibility() == View.VISIBLE)
                emptyDataRefreshLayout.setRefreshing(true);
        } else {
            if (progressDialog != null && progressDialog.isShowing()) {
                progressDialog.setTitle(sTitle);
                progressDialog.setContent(sMessage);
            } else {
                progressDialog = CommonUI.ShowMaterialProgressDialog(getContext(), sTitle, sMessage);
            }
        }
    }

    public void hideLoading() {
        if (lvProjects != null) lvProjects.stopLoading();
        CommonUI.DismissMaterialProgressDialog(getActivity(), progressDialog);
        if (emptyDataRefreshLayout.getVisibility() == View.VISIBLE) {
            emptyDataRefreshLayout.setRefreshing(false);
        }
    }

    private boolean isFirstTimeSyncProjects() {
        return StringUtils.isEmpty(CommonHelper.GetPreferenceString(getContext(), Constants.Keys.sProjectsLastUpdated));
    }

    private static class SyncProjectResultReceiver extends ResultReceiver {
        private final WeakReference<frag_Projects> fragmentWeakReference;

        private frag_Projects getProjectsFragment() {
            return fragmentWeakReference.get();
        }

        public SyncProjectResultReceiver(Handler handler, frag_Projects fragment) {
            super(handler);
            fragmentWeakReference = new WeakReference<>(fragment);
        }

        @Override
        protected void onReceiveResult(int resultCode, Bundle resultData) {
            super.onReceiveResult(resultCode, resultData);
            if (getProjectsFragment() == null) return;
            switch (resultCode) {
                case SyncProjectService.SHOW_LOADING:
                    getProjectsFragment().showLoading(
                            resultData.getString(Constants.Keys.sTitle),
                            resultData.getString(Constants.Keys.sMessage));
                    break;
                case SyncProjectService.HIDE_LOADING:
                    getProjectsFragment().hideLoading();
                    break;
                case SyncProjectService.SYNC_SUCCESS:
                    getProjectsFragment().reloadListView();
                    CommonHelper.SavePreference(getProjectsFragment().getContext(),
                            Constants.Keys.bForceSyncProjects, "0");
                    break;
            }
        }
    }
}
