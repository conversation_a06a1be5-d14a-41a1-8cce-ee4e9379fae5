package com.snapinspect.snapinspect3.activitynew.tasks

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.annotation.NonNull
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.outlined.FilterAlt
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import com.snapinspect.snapinspect3.Helper.CommonHelper
import com.snapinspect.snapinspect3.Helper.CommonTask
import com.snapinspect.snapinspect3.Helper.CommonUI
import com.snapinspect.snapinspect3.Helper.Constants
import com.snapinspect.snapinspect3.IF_Object.ai_Task
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.SI_DB.db_Tasks
import com.snapinspect.snapinspect3.util.DateUtils
import com.snapinspect.snapinspect3.util.DateUtils.DateFormat
import com.snapinspect.snapinspect3.views.composables.*
import com.snapinspect.snapinspect3.views.tasks.SelectedFilterData
import com.snapinspect.snapinspect3.views.tasks.TaskFilterData
import com.snapinspect.snapinspect3.views.tasks.TaskFilterNavigation
import com.snapinspect.snapinspect3.views.tasks.TaskRowView
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.*

class TasksViewModel : ViewModel() {
    private val _uiState = MutableStateFlow<TasksUiState>(TasksUiState.Success)
    val uiState = _uiState.asStateFlow()
    
    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing = _isRefreshing.asStateFlow()
    
    private val _searchQuery = MutableStateFlow("")
    val searchQuery = _searchQuery.asStateFlow()
    
    private val _tasks = MutableStateFlow<List<ai_Task>>(emptyList())
    val tasks = _tasks.asStateFlow()

    private val _selectedFilterData = MutableStateFlow(SelectedFilterData())
    val selectedFilterData = _selectedFilterData.asStateFlow()

    private val _filterData = MutableStateFlow(TaskFilterData())
    val filterData = _filterData.asStateFlow()

    fun loadTasksIfNeeded(
        context: Context,
        dispatcher: CoroutineDispatcher = Dispatchers.Main
    ) {
        if (!shouldSyncTasks(context)) return
        viewModelScope.launch(dispatcher) {
            syncCustomerTasks(context)
        }
    }

    private fun getLastSyncDate(context: Context): Date? {
        val savedLastSyncDate = CommonHelper.GetPreferenceString(
            context,
            Constants.Keys.DATE_SYNC_CUSTOMER_TASKS
        ) ?: Constants.Values.sSyncDateDefault  
        return DateUtils.parse(savedLastSyncDate, Constants.UTC,  DateUtils.possibleDateFormats)
    }
        
    private fun shouldSyncTasks(context: Context): Boolean {
        try {
            val lastSyncDate = getLastSyncDate(context)
            val currentUtcDate = DateUtils.getUTCDate()
            
            return lastSyncDate != null && 
                   currentUtcDate != null &&
                   DateUtils.daysPassed(currentUtcDate, lastSyncDate) > 1
        } catch (e: Exception) {
            return true // If there's any error parsing dates, sync to be safe
        }
    }

    fun loadTasksFromDB(
        context: Context,
        dispatcher: CoroutineDispatcher = Dispatchers.IO
    ) {
        viewModelScope.launch(dispatcher) {
            val iCustomerID = CommonHelper.GetPreferenceInt(context, Constants.Keys.iCustomerID)
            _tasks.value = db_Tasks.getMyTasks(iCustomerID, selectedFilterData.value)
            _filterData.value = db_Tasks.getTaskFilterData(context)
        }
    }

    private suspend fun syncCustomerTasks(
        context: Context,
        dispatcher: CoroutineDispatcher = Dispatchers.IO
    ) {
        _uiState.value = TasksUiState.Loading
        val lastSyncDate = getLastSyncDate(context)?.let {
            DateUtils.format(it, DateFormat.DATE_TIME_SHORT, Constants.UTC)
        } ?: Constants.Values.sSyncDateDefault
        
        val result = withContext(dispatcher) {
            CommonTask.syncCustomerTasks(
                context, 
                lastSyncDate, 
                Constants.Limits.iDisplayLengthForTasks
            )
        }

        result.fold(
            onSuccess = {
                loadTasksFromDB(context)
                _uiState.value = TasksUiState.Success
            },
            onFailure = { error ->
                _uiState.value = TasksUiState.Error(error.message ?: "Failed to load tasks")
            }
        )
    }
    
    fun refresh(
        context: Context,
        dispatcher: CoroutineDispatcher = Dispatchers.Main
    ) {
        viewModelScope.launch(dispatcher) {
            _isRefreshing.value = true
            syncCustomerTasks(context)
            _isRefreshing.value = false
        }
    }
    
    fun onSearchQueryChange(query: String) {
        _searchQuery.value = query
    }

    fun filterTasks(
        context: Context,
        filterType: TaskCompletionStatus,
        dispatcher: CoroutineDispatcher = Dispatchers.IO
    ) {
        _selectedFilterData.value = _selectedFilterData.value.copy(taskCompletionStatus = filterType)
        loadTasksFromDB(context, dispatcher)
    }

    fun applyFilters(
        context: Context,
        selectedFilterData: SelectedFilterData,
        dispatcher: CoroutineDispatcher = Dispatchers.IO
    ) {
        _selectedFilterData.value = selectedFilterData
        loadTasksFromDB(context, dispatcher)
    }
}

sealed class TasksUiState {
    data object Loading : TasksUiState()
    data object Success : TasksUiState()
    data class Error(val message: String) : TasksUiState()
}

class TasksFragment : Fragment() {

    private var onFilterTypeChanged: ((TaskCompletionStatus) -> Unit)? = null
    
    private val viewModel: TasksViewModel by lazy {
        ViewModelProvider(this)[TasksViewModel::class.java]
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            setContent {
                SnapTheme {
                    TasksScreen(
                        viewModel = viewModel,
                        onTaskClick = handleNavigation
                    )
                }
            }
        }
    }

    override fun onViewCreated(@NonNull view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState);
        context?.let { 
            viewModel.loadTasksFromDB(it)
        }
    }

    override fun onResume() {
        super.onResume()
        context?.let { 
            viewModel.loadTasksIfNeeded(it) 
        }
    }

    private val handleNavigation: (ai_Task) -> Unit = { task ->
        val intent = Intent(requireActivity(), if_EditTask::class.java).apply {
            putExtra(Constants.Extras.iSAssetID, task.iPropertyID)
            putExtra(Constants.Extras.iSTaskID, task.iSNotificationID)
        }
        startActivity(intent)
    }

    fun showFilterTasksOptions() {
        val context = requireContext()
        val options = TaskCompletionStatus.entries.map(TaskCompletionStatus::label).toTypedArray()
        CommonUI.showOptionsDialog(context, "Filter tasks", options) { dialog, _, which, _ ->
            val selectedFilter = TaskCompletionStatus.entries[which]
            viewModel.filterTasks(context, selectedFilter)
            dialog.dismiss()
            onFilterTypeChanged?.invoke(selectedFilter)
            true
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun TasksScreen(
    viewModel: TasksViewModel,
    onTaskClick: (ai_Task) -> Unit
) {
    val uiState by viewModel.uiState.collectAsState()
    val tasks by viewModel.tasks.collectAsState()
    val isRefreshing by viewModel.isRefreshing.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    val filterData by viewModel.filterData.collectAsState()
    val selectedFilterData by viewModel.selectedFilterData.collectAsState()

    val context = LocalContext.current

    val pullRefreshState = rememberPullRefreshState(
        refreshing = isRefreshing,
        onRefresh = { viewModel.refresh(context) }
    )

    val filteredTasks = remember(tasks, searchQuery) {
        tasks.filter { task ->
            searchQuery.isEmpty() || task.matchSearch(searchQuery)
        }
    }

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(colorResource(id = R.color.colorPrimary))
    ) {
        Column(modifier = Modifier.fillMaxSize().background(Color.White)) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(colorResource(id = R.color.colorPrimary)),
                horizontalArrangement = Arrangement.spacedBy(dimens.zero),
           ) {
                SearchBar(
                    modifier = Modifier.weight(1f),
                    paddingEnd = dimens.zero,
                    value = searchQuery,
                    onValueChange = viewModel::onSearchQueryChange,
                    placeholderText = "Search",
                    cornerRadius = dimens.semiMedium,
                )

                TaskFilterButton(
                    filterData = filterData,
                    selectedFilterData = selectedFilterData,
                    onApplyFilters = { newFilterData ->
                        viewModel.applyFilters(context, newFilterData)
                    }
                )
            }

            when {
                uiState is TasksUiState.Loading && tasks.isEmpty() -> {
                    EmptyStateContent(isLoading = true)
                }

                tasks.isEmpty() -> {
                    EmptyStateContent(isLoading = false)
                }

                else -> {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .pullRefresh(pullRefreshState)
                    ) {
                        TaskList(
                            tasks = filteredTasks,
                            onTaskClick = onTaskClick
                        )

                        PullRefreshIndicator(
                            refreshing = isRefreshing,
                            state = pullRefreshState,
                            modifier = Modifier.align(Alignment.TopCenter)
                        )
                    }
                }
            }
        }
    }
}

@Composable
@OptIn(ExperimentalMaterial3Api::class)
private fun TaskFilterButton(
    filterData: TaskFilterData,
    selectedFilterData: SelectedFilterData,
    onApplyFilters: (SelectedFilterData) -> Unit
) {
    val showBottomSheet = remember { mutableStateOf(false) }
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()
    
    IconButton(
        onClick = { showBottomSheet.value = true },
        modifier = Modifier.padding(end = dimens.smaller),
    ) {
        Icon(
            imageVector = Icons.Outlined.FilterAlt,
            contentDescription = "Filter",
            tint = Color.White
        )
    }
    
    if (showBottomSheet.value) {
        ModalBottomSheet(
            onDismissRequest = { showBottomSheet.value = false },
            sheetState = sheetState,
            containerColor = Color.White,
        ) {
            TaskFilterNavigation(
                modifier = Modifier
                    .fillMaxWidth()
                    .height((LocalConfiguration.current.screenHeightDp * 0.8f).dp),
                filterData = filterData,
                selectedFilterData = selectedFilterData,
                onApplyFilters = { newFilterData ->
                    scope.launch {
                        onApplyFilters(newFilterData)
                        sheetState.hide()
                        showBottomSheet.value = false
                    }
                },
                onDismiss = {
                    scope.launch {
                        sheetState.hide()
                        showBottomSheet.value = false
                    }
                }
            )
        }
    }
}

@Composable
private fun EmptyStateContent(isLoading: Boolean) {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        if (isLoading) {
            LoadingIndicator(
                size = 28.dp, 
                color = colorResource(id = R.color.gray_color_666)
            )
        } else {
            Text(
                text = "No tasks found",
                style = typography.h4,
                color = colors.color656565
            )
        }
    }
}

@Composable
private fun TaskList(
    tasks: List<ai_Task>,
    onTaskClick: (ai_Task) -> Unit
) {
    LazyColumn {
        items(
            items = tasks,
            key = { it.iSNotificationID }
        ) { task ->
            TaskRowView(
                task = task,
                onClick = { onTaskClick(task) }
            )
            Divider(
                color = colors.colorAAAAAA.copy(alpha = 0.5f),
                thickness = 0.5.dp,
                modifier = Modifier.padding(horizontal = dimens.extraNormal)
            )
        }
        // extra padding to avoid the last item being hidden
        item {
            Spacer(modifier = Modifier.height(dimens.xlarge))
        }
    }
}

@Composable
@Preview
private fun TasksScreenPreview() {
    SnapTheme {
        TasksScreen(
            viewModel = TasksViewModel(),
            onTaskClick = {}
        )
    }
}