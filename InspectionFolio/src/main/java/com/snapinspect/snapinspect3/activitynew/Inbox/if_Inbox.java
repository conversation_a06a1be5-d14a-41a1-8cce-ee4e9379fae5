package com.snapinspect.snapinspect3.activitynew.Inbox;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.RelativeLayout;
import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.Adapter.InboxAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.InboxActionType;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Inbox;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.Comments.if_InspectionComments;
import com.snapinspect.snapinspect3.util.ArrayUtils;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.softw4re.views.InfiniteListView;
import fj.data.Either;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.*;

public class if_Inbox extends Activity implements InboxAdapter.Callback {

    private InfiniteListView<Either<String, ai_Inbox>> listView;
    private RelativeLayout emptyAlertBox;
    private Menu oMenu;
    private MaterialDialog oDialog;

    private static final int iDisplayLength = 20;
    private int iDisplayStart;

    private final ArrayList<ai_Inbox> lsInbox = new ArrayList<>();
    private int iActionID = -1;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_if_inbox);
        getActionBar().setDisplayHomeAsUpEnabled(true);
        getActionBar().setTitle(R.string.title_activity_if__inbox);

        emptyAlertBox = findViewById(R.id.empty_box);

        listView = findViewById(R.id.inbox_list_view);

        getAllInbox(true);

        iActionID = getIntent().getIntExtra(Constants.Extras.iActionID, -1);
        listView.setAdapter(new InboxAdapter(this, -1, new ArrayList<>(), this));
    }

    @Override
    public void onResume() {
        super.onResume();
        if (iActionID > 0) {
            String actionType = getIntent().getStringExtra(Constants.Extras.iActionType);
            if (actionType != null && !StringUtils.isEmpty(actionType))
                parseActionType(ai_Inbox.getActionType(actionType), iActionID);
            iActionID = -1;
        }
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_inbox, menu);
        oMenu = menu;
        menu.findItem(R.id.action_clear).setVisible(false);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        switch (id) {
            case R.id.action_clear:
                clearInbox();
                break;
            case android.R.id.home:
                finish();
                break;
            default:
                break;
        }
        return super.onOptionsItemSelected(item);
    }

    private void hideRefreshIndicator() {
        new Handler().post(() -> listView.stopLoading());
    }

    private void reloadInbox(ArrayList<ai_Inbox> items) {
        lsInbox.addAll(items);

        final Map<String, List<ai_Inbox>> map = new HashMap<>();
        ArrayList<String> lsHeaders = new ArrayList<>();
        for (ai_Inbox oIB : lsInbox) {
            List<ai_Inbox> inboxList = map.get(oIB.sDate);
            if (inboxList == null) {
                inboxList = new ArrayList<>();
                lsHeaders.add(oIB.sDate);
            }
            inboxList.add(oIB);
            map.put(oIB.sDate, inboxList);
        }

        Collections.sort(lsHeaders, (h1, h2) -> {
            ai_Inbox c1 = map.get(h1).get(0);
            ai_Inbox c2 = map.get(h2).get(0);
            return c2.dateTime.compareTo(c1.dateTime);
        });

        displayItems(!map.isEmpty());

        ArrayList<Either<String, ai_Inbox>> data = new ArrayList<>();
        for (String header : lsHeaders) {
            List<ai_Inbox> grouped = map.get(header);
            if (ArrayUtils.isEmpty(grouped)) continue;
            data.add(Either.left(DateUtils.formatAsInboxNotificationDate(grouped.get(0).dateTime)));
            for (ai_Inbox inbox : grouped) data.add(Either.right(inbox));
        }

        listView.clearList();
        listView.addAll(data);
    }

    private void displayItems(boolean isShowed) {
        oMenu.findItem(R.id.action_clear).setVisible(isShowed);
        emptyAlertBox.setVisibility(isShowed ? View.GONE : View.VISIBLE);
    }

    private void parseAllInbox(JSONObject response) {
        try {
            JSONArray jsonArray = response.getJSONArray("lsInbox");

            ArrayList<ai_Inbox> items = new ArrayList<>();
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject obj = jsonArray.getJSONObject(i);
                ai_Inbox oInbox = parseJsonToInbox(obj);
                items.add(oInbox);
            }

            reloadInbox(items);
            listView.hasMore(items.size() >= iDisplayLength);
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    private ai_Inbox parseJsonToInbox(JSONObject json) {
        ai_Inbox oInbox = new ai_Inbox();

        try {
            oInbox.iSInboxID = json.optInt("iInboxID");
            oInbox.iFromCustomerID = json.optInt("iFromCustomerID");
            oInbox.iToCustomerID = json.optInt("iToCustomerID");
            oInbox.iCompanyID = json.optInt("iCompanyID");
            oInbox.sComments = json.optString("sComments");
            String sTimestamp = json.optString("dtDateTime");
            if (sTimestamp.length() > 2) {
                sTimestamp = sTimestamp.substring(sTimestamp.indexOf("(") + 1);
                sTimestamp = sTimestamp.substring(0, sTimestamp.indexOf(")"));
                long timestamp = Long.parseLong(sTimestamp);
                Date date = new Date(timestamp);
                oInbox.sDateTime = sTimestamp;
                oInbox.dateTime = date;
                oInbox.sDate = CommonHelper.getDateTimeStrFromDate(date, "EEE d/M/yyyy");
                oInbox.sTime = CommonHelper.getDateTimeStrFromDate(date, "h:mm a");
            }

            oInbox.colorString = CommonHelper.getUserColorString(oInbox.iFromCustomerID);
            String param = json.getString("sAction");
            JSONObject paramObj = new JSONObject(param);

            oInbox.actionType = ai_Inbox.getActionType(paramObj.getString("Func"));
            oInbox.iActionID = paramObj.optInt("ID");
        } catch (JSONException e) {
            e.printStackTrace();
        }

        return oInbox;
    }

    private void getAllInbox(boolean isShowing) {
        try {
            if (isShowing)
                oDialog = CommonUI.ShowMaterialProgressDialog(this, "Running", "Connecting to Server");
            String sURL = "/ioapi/GetInbox";

            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
            oParams.add("bDismiss", "false");
            oParams.add("iDisplayLength", "" + iDisplayLength);
            oParams.add("iDisplayStart", "" + iDisplayStart);

            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    CommonUI.DismissMaterialProgressDialog(if_Inbox.this, oDialog);
                    hideRefreshIndicator();
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            if (response.getBoolean("success")) {
                                Log.e("osama", "" + response);
                                parseAllInbox(response);
                                iDisplayStart += iDisplayLength;
                            } else {
                                runOnUiThread(() -> ShowAlert("Failed", "Please try again."));
                            }
                            return;
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest", ex);
                        }

                    }
                    runOnUiThread(() -> ShowAlert(
                            "Error! Failed Connection", "Please try again later."));
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_InspectionComments", ex, this);
        }
    }

    private void clearInbox() {
        oDialog = CommonUI.ShowMaterialProgressDialog(this, "Running", "Connecting to Server");
        for (final ai_Inbox oInbox : lsInbox) {
            CommonRequest.requestURL(this, "/IOAPI/DismissInbox", () -> {
                RequestParams oParams = new RequestParams();
                oParams.add("iCustomerID", CommonHelper.GetPreferenceString(if_Inbox.this, "iCustomerID"));
                oParams.add("sToken", CommonHelper.GetPreferenceString(if_Inbox.this, "sToken"));
                oParams.add("iInboxID", "" + oInbox.iSInboxID);
                return oParams;
            }, (response, error) -> {
                if (response != null) removeInbox(oInbox.iSInboxID);
                else if (error != null) ShowAlert("Failed", "Please try again.");

                if (lsInbox.isEmpty()) CommonUI.DismissMaterialProgressDialog(oDialog);
            });
        }
    }

    private void ShowAlert(String sTitle, String sMessage) {
        CommonUI.ShowAlert(this, sTitle, sMessage);
    }

    private void removeInbox(int isInboxID) {
        for (ai_Inbox inbox: lsInbox) {
            if (inbox.iSInboxID == isInboxID) {
                lsInbox.remove(inbox);
                break;
            }
        }
        listView.clearList();
        reloadInbox(lsInbox);
    }

    private void parseActionType(InboxActionType actionType, int iActionID) {
        Intent intent = null;
        switch (actionType) {
            case VIEW_INS_COMMENT:
                intent = new Intent(this, if_InspectionComments.class);
                intent.putExtra(Constants.Extras.iSInsID, iActionID);
                break;
            case UPDATE_INS_STATUS:
                intent = new Intent(this, if_InspectionInfo.class);
                intent.putExtra(Constants.Extras.iSInsID, iActionID);
                break;
            default:
                break;
        }
        if (intent != null) startActivity(intent);
    }

    @Override
    public void onNewLoadRequired() {
        listView.startLoading();
        getAllInbox(false);
    }

    @Override
    public void onRefresh() {
        iDisplayStart = 0;
        lsInbox.clear();

        getAllInbox(false);
    }

    @Override
    public void onItemClick(ai_Inbox item) {
        parseActionType(item.actionType, item.iActionID);
    }

    @Override
    public void onItemLongClick(ai_Inbox item) {
        //
    }
}
