package com.snapinspect.snapinspect3.activitynew;

import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import androidx.core.util.Pair;
import androidx.recyclerview.widget.LinearLayoutManager;

import android.util.Log;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewConfiguration;
import android.widget.EditText;
import android.widget.TextView;

import com.snapinspect.snapinspect3.Adapter.ConfigItemAdapter;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.app.App;
import com.woxthebox.draglistview.DragItem;
import com.woxthebox.draglistview.DragListView;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Observable;
import java.util.Observer;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_CHK;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_CMT;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_MCHK;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_PTO;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_SCAN;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_SCHK;

/*
 * @Created by osama on 16/06/17.
 */

public class if_OrderInsItem extends Activity implements Observer {
    private ai_InsItem oInsItem;
    private DragListView oListView;
    ConfigItemAdapter listAdapter;
    private TextView txtTimer;
    App baseApplication;
    private List<String> lsConfig;
    private List<ai_InsItem> lsInsItem;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);

            getActionBar().setDisplayHomeAsUpEnabled(true);

            getActionBar().setTitle("Re-Order Config");

            setContentView(R.layout.activity_if_order_inspection);
            getOverflowMenu();

            //tiemr part
            txtTimer = findViewById(R.id.txt_timer);
            baseApplication = (App) getApplication();
            baseApplication.getObserver().addObserver(this);

            long iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, 0);
            oInsItem = ai_InsItem.findById(ai_InsItem.class, iInsItemID);

            lsInsItem = ai_InsItem.find(ai_InsItem.class, "I_P_INS_ITEM_ID = ? and B_DELETED = 0", "" + oInsItem.getId());

            oListView = findViewById(R.id.order_inspection_listview);
            oListView.setDragListListener(new DragListView.DragListListener() {
                @Override
                public void onItemDragStarted(int position) {

                }

                @Override
                public void onItemDragging(int itemPosition, float x, float y) {

                }

                @Override
                public void onItemDragEnded(int fromPosition, int toPosition) {
                    if (fromPosition != toPosition) {
                        orderConfig(fromPosition, toPosition);
                    }
                }
            });

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_inspection.onCreate", ex, this);
        }
    }

    private void getOverflowMenu() {

        try {
            ViewConfiguration config = ViewConfiguration.get(this);
            Field menuKeyField = ViewConfiguration.class.getDeclaredField("sHasPermanentMenuKey");
            if(menuKeyField != null) {
                menuKeyField.setAccessible(true);
                menuKeyField.setBoolean(config, false);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_order_ins, menu);

        return true;
    }

    @Override
    public boolean onOptionsItemSelected( MenuItem item )
    {
        try {
            if (item.getItemId() == R.id.action_save) {
                saveInsItem();

            } else {
                onBackPressed();

            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_inspection.onOptionItemsSelected", ex, this);
        }
        return true;
    }

    private void saveInsItem() {
        oInsItem.save();
        for (ai_InsItem it : lsInsItem) {
            it.save();
        }

        onBackPressed();
    }

    private void updateConfig(ai_InsItem insItem) {
        String pConfigOne = oInsItem.sConfigOne;
        String prefix = getScheme(pConfigOne);
        int position = getConfigId(prefix, oInsItem);
        String cConfigOne = CommonHelper.getConfigById(position, insItem);
        String cValueOne = CommonHelper.getValueById(position, insItem);

        String pConfigTwo = oInsItem.sConfigTwo;
        prefix = getScheme(pConfigTwo);
        position = getConfigId(prefix, oInsItem);
        String cConfigTwo = CommonHelper.getConfigById(position, insItem);
        String cValueTwo = CommonHelper.getValueById(position, insItem);

        String pConfigThree = oInsItem.sConfigThree;
        prefix = getScheme(pConfigThree);
        position = getConfigId(prefix, oInsItem);
        String cConfigThree = CommonHelper.getConfigById(position, insItem);
        String cValueThree = CommonHelper.getValueById(position, insItem);

        String pConfigFour = oInsItem.sConfigFour;
        prefix = getScheme(pConfigFour);
        position = getConfigId(prefix, oInsItem);
        String cConfigFour = CommonHelper.getConfigById(position, insItem);
        String cValueFour = CommonHelper.getValueById(position, insItem);

        String pConfigFive = oInsItem.sConfigFive;
        prefix = getScheme(pConfigFive);
        position = getConfigId(prefix, oInsItem);
        String cConfigFive = CommonHelper.getConfigById(position, insItem);
        String cValueFive = CommonHelper.getValueById(position, insItem);

        String pConfigSix = oInsItem.sConfigSix;
        prefix = getScheme(pConfigSix);
        position = getConfigId(prefix, oInsItem);
        String cConfigSix = CommonHelper.getConfigById(position, insItem);
        String cValueSix = CommonHelper.getValueById(position, insItem);

        insItem.sConfigOne = cConfigOne;
        insItem.sValueOne = cValueOne;
        insItem.sConfigTwo = cConfigTwo;
        insItem.sValueTwo = cValueTwo;
        insItem.sConfigThree = cConfigThree;
        insItem.sValueThree = cValueThree;
        insItem.sConfigFour = cConfigFour;
        insItem.sValueFour = cValueFour;
        insItem.sConfigFive = cConfigFive;
        insItem.sValueFive = cValueFive;
        insItem.sConfigSix = cConfigSix;
        insItem.sValueSix = cValueSix;

    }

    private String getScheme(String sconfig) {
        if (sconfig.startsWith(SI_S_CONFIG_KEY_CHK)) {
            return "CHK";
        } else if (sconfig.startsWith(SI_S_CONFIG_KEY_SCHK)) {
            return "SCHK";
        } else if (sconfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
            return "MCHK";
        } else if (sconfig.startsWith(SI_S_CONFIG_KEY_CMT)) {
            return "CMT";
        } else if (sconfig.startsWith(SI_S_CONFIG_KEY_PTO)) {
            return "PTO";
        } else if (sconfig.startsWith(SI_S_CONFIG_KEY_SCAN)) {
            return "SCAN";
        } else if (sconfig.startsWith("{")) {
            return "{";
        }
        return "";
    }

    private int getConfigId(String prefix, ai_InsItem item) {
        for (int i = 1; i <= 6; i++) {
            String sConfig = "";
            switch (i) {
                case 1:
                    sConfig = item.sConfigOne;
                    if (sConfig.startsWith(prefix)) {
                        return i;
                    }
                    break;
                case 2:
                    sConfig = item.sConfigTwo;
                    if (sConfig.startsWith(prefix)) {
                        return i;
                    }
                    break;
                case 3:
                    sConfig = item.sConfigThree;
                    if (sConfig.startsWith(prefix)) {
                        return i;
                    }
                    break;
                case 4:
                    sConfig = item.sConfigFour;
                    if (sConfig.startsWith(prefix)) {
                        return i;
                    }
                    break;
                case 5:
                    sConfig = item.sConfigFive;
                    if (sConfig.startsWith(prefix)) {
                        return i;
                    }
                    break;
                case 6:
                    sConfig = item.sConfigSix;
                    if (sConfig.startsWith(prefix)) {
                        return i;
                    }
                    break;
            }
        }

        return 0;
    }

    @Override
    public void onResume(){
        super.onResume();

        EditInsItem();
    }

    private List<String> getConfigList() {

        List<String> lsConfig = new ArrayList<>();
        String sConfig = "";
        for (int i = 1; i<= 6; i++) {
            sConfig = CommonHelper.GetConfig(i, oInsItem, null);
            if (sConfig.startsWith(SI_S_CONFIG_KEY_CHK)) {
                lsConfig.add("Select Rating - Yes/No Check");
            }
            else if (sConfig.startsWith(SI_S_CONFIG_KEY_SCHK)) {
                lsConfig.add("Select Rating - Single Check");
            }
            else if (sConfig.startsWith(SI_S_CONFIG_KEY_MCHK)) {
                lsConfig.add("Select Rating - Multi Check");
            }
            else if (sConfig.startsWith(SI_S_CONFIG_KEY_CMT)) {
                lsConfig.add("Comments Box");
            }
            else if (sConfig.startsWith(SI_S_CONFIG_KEY_PTO)) {
                lsConfig.add("Photos");
            }
            else if (sConfig.startsWith(SI_S_CONFIG_KEY_SCAN)) {
                lsConfig.add("Scanner");
            }
            else  {
                lsConfig.add("Control" + i);
            }
        }

        return lsConfig;
    }

    private void orderConfig(int fromPos, int toPos) {
        List<String> lsTmp = new ArrayList<>();

        String updateConfig = getConfigById(fromPos);
        String updateValue = getValueById(fromPos);
        String tmpConfig= "", tmpValue = "";
        String tc = "", tv = "";

        for (int i = 0; i < 6; i++) {
            if (fromPos > toPos) {


                tmpConfig = getConfigById(i);
                tmpValue = getValueById(i);

                if (i < toPos) {
                    lsTmp.add(lsConfig.get(i));
                    String cUpdate = getConfigById(i);
                    String vUpdate = getValueById(i);
                    Log.e("equal", "sss" + i + " - "  + cUpdate);
                    updateConfig(i, cUpdate, vUpdate);
                } else if (i == toPos) {

                    Log.e("to", "sss" + i + " - " + updateConfig);
                    updateConfig(i, updateConfig, updateValue);
                    lsTmp.add(lsConfig.get(fromPos));

                    tc = tmpConfig;
                    tv = tmpValue;

                } else if (i > toPos && i <= fromPos) {

                    lsTmp.add(lsConfig.get(i-1));
                    String cUpdate = tc;
                    String vUpdate = tv;
                    Log.e("cc", "over"  + i + " - " + cUpdate);
                    updateConfig(i, cUpdate, vUpdate);

                    tc = tmpConfig;
                    tv = tmpValue;

                } else {
                    lsTmp.add(lsConfig.get(i));
                    String cUpdate = getConfigById(i);
                    String vUpdate = getValueById(i);
                    Log.e("equal", "sss" + i + " - "  + cUpdate);
                    updateConfig(i, cUpdate, vUpdate);
                }

            } else {
                if (i < fromPos) {
                    lsTmp.add(lsConfig.get(i));
                    String cUpdate = getConfigById(i);
                    String vUpdate = getValueById(i);
                    Log.e("equal", "sss" + i + " - "  + cUpdate);
                    updateConfig(i, cUpdate, vUpdate);
                } else if (i >= fromPos && i < toPos) {
                    lsTmp.add(lsConfig.get(i+1));
                    String cUpdate = getConfigById(i+1);
                    String vUpdate = getValueById(i+1);
                    Log.e("cc", "over" + i + " - "  + cUpdate);
                    updateConfig(i, cUpdate, vUpdate);
                } else if (i == toPos) {
                    lsTmp.add(lsConfig.get(fromPos));

                    Log.e("to", "sss" + i + " - "  + updateConfig);
                    updateConfig(i, updateConfig, updateValue);
                } else {
                    lsTmp.add(lsConfig.get(i));
                    String cUpdate = getConfigById(i);
                    String vUpdate = getValueById(i);
                    Log.e("equal", "sss" + i + " - "  + cUpdate);
                    updateConfig(i, cUpdate, vUpdate);
                }
            }
        }

        lsConfig = lsTmp;

        for (String sss : lsConfig) {
            Log.e("oama", sss);
        }

        Log.e("osama", "1 - " + oInsItem.sConfigOne);
        Log.e("osama", "1 - " + oInsItem.sValueOne);
        Log.e("osama", "2 - " + oInsItem.sConfigTwo);
        Log.e("osama", "2 - " + oInsItem.sValueTwo);
        Log.e("osama", "3 - " + oInsItem.sConfigThree);
        Log.e("osama", "3 - " + oInsItem.sValueThree);
        Log.e("osama", "4 - " + oInsItem.sConfigFour);
        Log.e("osama", "4 - " + oInsItem.sValueFour);
        Log.e("osama", "5 - " + oInsItem.sConfigFive);
        Log.e("osama", "5 - " + oInsItem.sValueFive);
        Log.e("osama", "6 - " + oInsItem.sConfigSix);
        Log.e("osama", "6 - " + oInsItem.sValueSix);

        for (ai_InsItem it : lsInsItem) {
            updateConfig(it);
        }
    }

    private void updateConfig(int position, String updatedConfig, String updatedValue) {
        position += 1;

        switch (position) {
            case 1:
                oInsItem.sConfigOne = updatedConfig;
                oInsItem.sValueOne = updatedValue;
                break;
            case 2:
                oInsItem.sConfigTwo = updatedConfig;
                oInsItem.sValueTwo = updatedValue;
                break;
            case 3:
                oInsItem.sConfigThree = updatedConfig;
                oInsItem.sValueThree = updatedValue;
                break;
            case 4:
                oInsItem.sConfigFour = updatedConfig;
                oInsItem.sValueFour = updatedValue;
                break;
            case 5:
                oInsItem.sConfigFive = updatedConfig;
                oInsItem.sValueFive = updatedValue;
                break;
            case 6:
                oInsItem.sConfigSix = updatedConfig;
                oInsItem.sValueSix = updatedValue;
                break;
        }
    }

    private String getConfigById(int position) {
        String sconfig = "";
        position += 1;
        switch (position) {
            case 1:
                sconfig = oInsItem.sConfigOne;
                break;
            case 2:
                sconfig = oInsItem.sConfigTwo;
                break;
            case 3:
                sconfig = oInsItem.sConfigThree;
                break;
            case 4:
                sconfig = oInsItem.sConfigFour;
                break;
            case 5:
                sconfig = oInsItem.sConfigFive;
                break;
            case 6:
                sconfig = oInsItem.sConfigSix;
                break;
        }

        return sconfig;
    }

    private String getValueById(int position) {
        String value = "";
        position += 1;
        switch (position) {
            case 1:
                value = oInsItem.sValueOne;
                break;
            case 2:
                value = oInsItem.sValueTwo;
                break;
            case 3:
                value = oInsItem.sValueThree;
                break;
            case 4:
                value = oInsItem.sValueFour;
                break;
            case 5:
                value = oInsItem.sValueFive;
                break;
            case 6:
                value = oInsItem.sValueSix;
                break;
        }

        return value;
    }

    public void EditInsItem(){
        try {

            ArrayList<Pair<Long, String>> mItemArray = new ArrayList<>();
            lsConfig = getConfigList();

            for (int i = 0; i < lsConfig.size(); i++) {
                mItemArray.add(new Pair<>((long) i, lsConfig.get(i)));
            }
            oListView.setLayoutManager(new LinearLayoutManager(this));

            listAdapter = new ConfigItemAdapter(mItemArray, R.layout.cell_insitem_order, R.id.order_handler, false);
            oListView.setAdapter(listAdapter, true);
            oListView.setCanDragHorizontally(false);
            oListView.setCustomDragItem(new MyDragItem(this, R.layout.cell_insitem_order));
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_inspection.EditInsItem", ex, this);
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void update(Observable observable, Object data) {
        txtTimer.setText(baseApplication.getObserver().getValue());
    }

    private static class MyDragItem extends DragItem {

        MyDragItem(Context context, int layoutId) {
            super(context, layoutId);
        }

        @Override
        public void onBindDragView(View clickedView, View dragView) {
            CharSequence text = ((EditText) clickedView.findViewById(R.id.txt_RoomName)).getText();
            ((EditText) dragView.findViewById(R.id.txt_RoomName)).setText(text);

        }
    }
}
