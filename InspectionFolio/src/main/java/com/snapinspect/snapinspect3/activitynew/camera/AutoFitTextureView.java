package com.snapinspect.snapinspect3.activitynew.camera;

import android.content.Context;
import android.content.res.TypedArray;

import android.util.AttributeSet;
import android.view.GestureDetector;
import android.view.TextureView;

import com.snapinspect.snapinspect3.R;


public class AutoFitTextureView extends TextureView {

    private GestureDetector mGestureDetector = null;

    private int mRatioWidth = 0;
    private int mRatioHeight = 0;

    public AutoFitTextureView(Context context) {
        this(context, null);
    }

    public AutoFitTextureView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public AutoFitTextureView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        TypedArray typedArray = context.obtainStyledAttributes(attrs, R.styleable.AutoFitTextureView );
        final int N = typedArray.getIndexCount();
        for (int i = 0; i < N; ++i) {
            int attr = typedArray.getIndex(i);
            switch (attr) {
                case R.styleable.AutoFitTextureView_withRatio:
                    mRatioWidth = typedArray.getInteger(attr, 0);
                    break;

                case R.styleable.AutoFitTextureView_heightRatio:
                    mRatioHeight = typedArray.getInteger(attr, 0);
                    break;
            }
        }
        typedArray.recycle();
    }

    /**
     * Sets the aspect ratio for this view. The size of the view will be measured based on the ratio
     * calculated from the parameters. Note that the actual sizes of parameters don't matter, that
     * is, calling setAspectRatio(2, 3) and setAspectRatio(4, 6) make the same result.
     *
     * @param width  Relative horizontal size
     * @param height Relative vertical size
     */
    public void setAspectRatio(int width, int height) {
        if (width < 0 || height < 0) {
            throw new IllegalArgumentException("Size cannot be negative.");
        }
        mRatioWidth = width;
        mRatioHeight = height;
        requestLayout();
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec);
        int width = MeasureSpec.getSize(widthMeasureSpec);
        int height = MeasureSpec.getSize(heightMeasureSpec);
        if (0 == mRatioWidth || 0 == mRatioHeight) {
            setMeasuredDimension(width, height);
        } else {
            if (width <= height * mRatioWidth / mRatioHeight) {
                setMeasuredDimension(width, width * mRatioHeight / mRatioWidth);
            } else {
                setMeasuredDimension(height * mRatioWidth / mRatioHeight, height);
            }
        }
    }

//    @Override
//    public boolean onTouchEvent(MotionEvent m) {
//        if (mGestureDetector != null) {
//            mGestureDetector.onTouchEvent(m);
//        }
//        return true;
//    }

    public void setGestureListener(GestureDetector.OnGestureListener gestureListener) {
        if (gestureListener != null) {
            mGestureDetector = new GestureDetector(getContext(), gestureListener);
        } else {
            mGestureDetector = null;
        }
    }

    public boolean hasGestureDetector() {
        return mGestureDetector != null;
    }
}