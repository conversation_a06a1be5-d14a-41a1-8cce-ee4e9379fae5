package com.snapinspect.snapinspect3.activitynew.inspection3rd;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.InputType;
import android.view.*;
import android.widget.*;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.IntentSenderRequest;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.Nullable;
import androidx.core.widget.ContentLoadingProgressBar;
import androidx.fragment.app.FragmentActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.afollestad.materialdialogs.MaterialDialog;
import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.QRScan.QRScannerActivity;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.SI_DB.db_Media;
import com.snapinspect.snapinspect3.Scanner.ColorActivity;
import com.snapinspect.snapinspect3.Scanner.Utils.ScannerUtils;
import com.snapinspect.snapinspect3.activity.if_CommentsLibrary;
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayPhoto;
import com.snapinspect.snapinspect3.util.InsDurationManager;
import com.snapinspect.snapinspect3.util.KeyboardHeightProvider;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.views.*;
import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip;

import java.util.ArrayList;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_Inspection_Type.Full;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_QUESTION_TYPE_P;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_Question_Type.*;

public class if_Ins_items_3rd extends FragmentActivity
        implements InsDurationManager.Delegate, KeyboardHeightProvider.KeyboardHeightObserver, Sign_Inspection_View.Listener {

    private static final int COMMENTS_LIBRARY_REQUEST_CODE = 0;

    private int iPInsItemID;
    private int iInsID;
    private LinearLayout layoutInsItems;
    private ai_InsItem oPInsItem;
    private ImageButton btnAction;
    private ai_Item oPItem;
    private InputAccessoryToolbar accessoryView;
    private View bottomNavView;
    private ScrollView scrollView;
    private InsDurationManager mDurationManager;
    private KeyboardHeightProvider keyboardHeightProvider;
    private final LibraryParams libraryParams = new LibraryParams();
    private EditText focusingEditText;
    private ActivityResultLauncher<IntentSenderRequest> scannerLauncher;
    private int iSelectedPosition = 0;
    private int iSelectedInsItemID = 0;
    private ai_Inspection oInspection;
    private boolean bRequestInspection;

    public static Intent newIntent(Context context, int iInsItemId,
                                   String sAddressOne, int iInsID, boolean bRequestInspection) {
        Intent intent = new Intent(context, if_Ins_items_3rd.class);
        intent.putExtra(Constants.Extras.iPInsItemID, iInsItemId);
        intent.putExtra(Constants.Extras.sAddress1, sAddressOne);
        intent.putExtra(Constants.Extras.iInsID, iInsID);
        intent.putExtra(Constants.Extras.bRequestInspection, bRequestInspection);
        return intent;
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        setTheme(R.style.RemoveShadowActionBar);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_ins_items_3rd);

        try {
            getActionBar().setDisplayHomeAsUpEnabled(true);
            getActionBar().setTitle(getIntent().getStringExtra(Constants.Extras.sAddress1));

            iPInsItemID = getIntent().getIntExtra(Constants.Extras.iPInsItemID, 0);

            btnAction = findViewById(R.id.btn_Inspection_Action);
            btnAction.setOnClickListener(v -> showInsAction());

            layoutInsItems = findViewById(R.id.layout_ins_items);

            iInsID = getIntent().getIntExtra(Constants.Extras.iInsID, 0);
            oInspection = ai_Inspection.findById(ai_Inspection.class, (long) iInsID);
            bRequestInspection = CommonHelper.bRequestInspection(oInspection.sCustomTwo);

            keyboardHeightProvider = new KeyboardHeightProvider(this);
            layoutInsItems.post(() -> keyboardHeightProvider.start());

            accessoryView = findViewById(R.id.input_accessory_view);
            accessoryView.setListener(new InputAccessoryToolbar.Listener() {
                @Override
                public void onCommentLibraryClick() {
                    showsCommentsLibrary();
                }

                @Override
                public void onScanCodeClick() {
                    Intent i = new Intent(if_Ins_items_3rd.this, QRScannerActivity.class);
                    i.putExtra(Constants.Extras.iPosition, libraryParams.iPosition);
                    i.putExtra(Constants.Extras.iTextPosition, libraryParams.iTextPosition);
                    i.putExtra(Constants.Extras.iInsItemID, libraryParams.iInsItemID);
                    i.putExtra(Constants.Extras.iPLayoutID, libraryParams.iPLayoutID);
                    startActivityForResult(i, Constants.RequestCodes.QR_SCANNER_REQUEST);
                }
            });

            bottomNavView = findViewById(R.id.layout_bottom_bar);
            scrollView = findViewById(R.id.scroll_view);

            registerBroadcasts();
            registerLauncher();

            getDurationManager().start();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Ins_items_3rd.onCreate", ex, this);
        }
    }

    private void registerLauncher() {
        scannerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartIntentSenderForResult(), result -> {
                    if (result.getResultCode() == Activity.RESULT_OK) {
                        Uri imageUri = ScannerUtils.getImageUriFromActivityResult(result);
                        if (imageUri != null) {
                            Intent intent = new Intent(this, ColorActivity.class);
                            intent.putExtra(Constants.Extras.iInsItemID, iSelectedInsItemID);
                            intent.putExtra(Constants.Extras.CROP_PATH, imageUri.getPath());
                            intent.putExtra(Constants.Extras.iPosition, iSelectedPosition);
                            startActivityForResult(intent, Constants.RequestCodes.SCAN_DOCUMENT_REQUEST);
                        }
                    }
                });
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data == null) return;
        if (resultCode == Constants.ResultCodes.CAMERA_RESPONSE) {
            handleCameraResult(data);
        } else {
            handleCommentsResult(requestCode, resultCode, data);
        }
    }

    private void handleCameraResult(Intent data) {
        long photoId = data.getLongExtra(Constants.Extras.iPhotoID, 0);
        ai_Photo oPhoto = ai_Photo.findById(ai_Photo.class, photoId);
        if (oPhoto != null) {
            Intent intent = new Intent(this, if_DisplayPhoto.class);
            intent.putExtra(Constants.Extras.iPhotoID, photoId);
            startActivity(intent);
        }
    }

    private void handleCommentsResult(int requestCode, int resultCode, Intent data) {
        String sComments = null;
        if (COMMENTS_LIBRARY_REQUEST_CODE == requestCode &&
                resultCode == if_CommentsLibrary.COMMENTS_LIBRARY_RESULT_CODE) {
            sComments = data.getStringExtra(Constants.Extras.sComments);
        } else if (Constants.RequestCodes.QR_SCANNER_REQUEST == requestCode &&
                resultCode == RESULT_OK) {
            sComments = data.getStringExtra(Constants.Extras.sResult);
        }

        if (focusingEditText == null || StringUtils.isEmpty(sComments)) return;

        int iInsItemID = data.getIntExtra(Constants.Extras.iInsItemID, 0);
        if (iInsItemID > 0) {
            CommonUI.insertText(focusingEditText, sComments);

            final String text = focusingEditText.getText().toString();
            ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);
            int iPosition = data.getIntExtra(Constants.Extras.iPosition, 0);
            CommonHelper.SetValue(iPosition, oInsItem, text);
            oInsItem.save();

            resetFocusingEditText();
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        keyboardHeightProvider.setKeyboardHeightObserver(this);
        new Handler(Looper.getMainLooper()).post(this::reloadInsItems);
    }

    @Override
    protected void onPause() {
        keyboardHeightProvider.setKeyboardHeightObserver(null);
        getDurationManager().saveInsTime();
        super.onPause();
    }

    @Override
    protected void onDestroy() {
        getDurationManager().stop();
        unregisterBroadcasts();
        keyboardHeightProvider.close();
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        getDurationManager().stop();
        super.onBackPressed();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (CommonHelper.isKioskMode(this)) return true;

        if (oPInsItem != null && !oPInsItem.bCompleted) {
            getMenuInflater().inflate(R.menu.menu_ins_item_done, menu);
        }
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onMenuItemSelected(int featureId, MenuItem item) {
        if (oPInsItem.hasCompulsoryItems()) {
            db_InsItem.markNeedValidateCompulsoryConfig(oPInsItem);
            new MaterialDialog.Builder(this)
                    .title("Incompleted Area")
                    .content("The inspection can not be uploaded until all compulsory questions are answered. Please follow ! mark to complete inspection.")
                    .negativeText("Skip For Now")
                    .onNegative((dialog, which) -> onBackPressed())
                    .positiveText("View Incompleted Items")
                    .onPositive((dialog, which) -> {
                        reloadInsItems();
                        scrollToFirstInvalidView();
                    })
                    .show();
        } else {
            if (item.getItemId() == R.id.action_ins_item_done) {
                doneInsItem();
            }
            onBackPressed();
        }
        return super.onMenuItemSelected(featureId, item);
    }

    @Override
    public void updateInsDuration(String duration) {
        if (CommonHelper.GetPreferenceBoolean(this, Constants.Settings.bDisplayInspectionDuration)) {
            ((TextView)findViewById(R.id.tv_inspection_time)).setText(duration);
        }
    }

    private void displayInsItems(boolean shouldBypassCompulsoryWholeIns) {
        try {
            oPInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iPInsItemID);
            oPInsItem.bInspectionByPassCompulsory = shouldBypassCompulsoryWholeIns;
            oPItem = new ai_Item(oPInsItem);
            btnAction.setVisibility(oPItem.bSimpleInsItem() ? View.GONE : View.VISIBLE);

            ArrayList<ai_InsItem> dataSource = new ArrayList<>();
            if (oPItem.bSimpleInsItem()) {
                dataSource.add(oPInsItem);
            } else {
                List<ai_InsItem> items = CommonDB.GetChildInsItem(iPInsItemID, oPInsItem.iInsID);
                for (ai_InsItem item: items) {
                    item.bInspectionByPassCompulsory = shouldBypassCompulsoryWholeIns;
                }
                dataSource.addAll(items);
            }

            layoutInsItems.removeAllViews();

            for (int position = 0; position < dataSource.size(); position++) {
                LinearLayout rootView = (LinearLayout) LayoutInflater.from(this).inflate(R.layout.cell_ins_item_3rd, null);
                LinearLayout bodyLayout = rootView.findViewById(R.id.view_Full);
                LinearLayout topLayout = rootView.findViewById(R.id.view_top_info);

                ai_Enum_Config.SI_Question_Type qType = oPItem.getQuestionType();
                ai_Enum_Config.SI_Inspection_Type insType = oInspection.getEnumInsType();

                boolean bFullInspection = insType == Full;

                if (position == 0) { // first
                    topLayout.setVisibility(View.VISIBLE);
                    TextView tvTitle = topLayout.findViewById(R.id.tv_ins_name);
                    tvTitle.setText(oPInsItem.sName);

                    TextView tvInstruction = topLayout.findViewById(R.id.tv_instruction);
                    tvInstruction.setVisibility(View.GONE);

                    String sInstruction = CommonDB.GetInstruction(oPInsItem, bFullInspection);
                    ImageButton infoButton = topLayout.findViewById(R.id.btn_instruction_tip);
                    infoButton.setOnClickListener(v ->
                            new SimpleTooltip.Builder(if_Ins_items_3rd.this)
                                    .anchorView(v)
                                    .text(sInstruction)
                                    .gravity(Gravity.BOTTOM)
                                    .backgroundColor(if_Ins_items_3rd.this.getResources().getColor(R.color.colorPrimary))
                                    .arrowColor(if_Ins_items_3rd.this.getResources().getColor(R.color.colorPrimary))
                                    .transparentOverlay(true)
                                    .build()
                                    .show());
                    infoButton.setVisibility(View.GONE);
                    if (!StringUtils.isEmpty(sInstruction)) {
                        boolean show = CommonHelper.GetPreferenceBoolean(this, Constants.Settings.bDisplayInst)
                            || CommonJson.shouldTurnOnInstruction(this);
                        infoButton.setVisibility(show ? View.GONE : View.VISIBLE);
                        tvInstruction.setVisibility(show ? View.VISIBLE : View.GONE);
                        if (show) tvInstruction.setText(sInstruction);
                    }
                } else {
                    topLayout.setVisibility(View.GONE);
                }

                bodyLayout.removeAllViews();
                if (oPItem.bSimpleInsItem()) { // alert
                    GA_Inspection_View cView = new GA_Inspection_View(
                        this, iInsID, oPInsItem, bFullInspection, bRequestInspection, position);
                    bodyLayout.addView(cView);
                } else if (qType == SI_Question_S) {  //sign
                    Sign_Inspection_View cView = new Sign_Inspection_View(
                        dataSource, this, this, oPInsItem, bFullInspection, position);
                    bodyLayout.addView(cView);
                } else if (qType == SI_Question_V) {  //video
                    Video_Inspection_View cView = new Video_Inspection_View(
                        dataSource, this, oPInsItem, bFullInspection, position);
                    bodyLayout.addView(cView);
                } else if (qType == SI_Question_C) { // full
                    ai_InsItem oInsItem = dataSource.get(position);
                    C_Inspection_View cView = new C_Inspection_View(
                        this, iInsID, oInsItem, oPInsItem, true, bRequestInspection, dataSource, position);
                    bodyLayout.addView(cView);
                }

                layoutInsItems.addView(rootView);
            }

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Ins_items_3rd.displayInsItems", ex, this);
        }
    }

    private void displayCompleteItems() {
        int count = 0, total;
        if (oPItem.bSimpleInsItem()) {
            count = oPInsItem.bEdited() ? 1 : 0;
            total = 1;
        } else {
            List<ai_InsItem> arrInsItem = CommonDB.GetChildInsItem(iPInsItemID, oPInsItem.iInsID);
            total = arrInsItem.size();
            for (ai_InsItem item: arrInsItem) {
                if (item.bEdited()) count++;
            }
        }

        String[] strings = { count + "", getString(R.string.items_out_of) , total + ""};
        TextView textView = findViewById(R.id.tv_bottom_info);
        textView.setText(String.join(" ", strings));
    }

    private void reloadInsItems() {
        displayInsItems(oInspection.shouldBypassCompulsory());
        displayCompleteItems();
    }

    private void doneInsItem() {
        oPInsItem.bCompleted = true;
        oPInsItem.save();
    }

    private void addNewInsItem() {
        try {
            final EditText editText = new EditText(this);
            editText.setText("");
            editText.setTextColor(Color.DKGRAY);
            String sTitleText = "New Item Name";
            if (oPInsItem.sQType.equalsIgnoreCase(SI_QUESTION_TYPE_P)) {
                sTitleText = "New Name";
            }
            new MaterialDialog.Builder(this)
                    .title(sTitleText)
                    .content(null)
                    .inputType(InputType.TYPE_CLASS_TEXT)
                    .input("", "", (dialog, input) -> {
                        String sValue = input.toString();
                        if (StringUtils.isEmpty(sValue)) {
                            Toast.makeText(getBaseContext(), "This field can not be empty.", Toast.LENGTH_LONG).show();
                        } else {
                            List<ai_InsItem> lsTempInsItem = ai_InsItem.find(ai_InsItem.class, "I_P_INS_ITEM_ID = ? and S_NAME = ? and B_Deleted = 0", "" + iPInsItemID, sValue);
                            if (lsTempInsItem != null && lsTempInsItem.size() > 0) {
                                Toast.makeText(getBaseContext(), "The item name is already in this room. Please use another item name", Toast.LENGTH_LONG).show();
                            } else {
                                List<ai_InsItem> lsTempInsItem1 = ai_InsItem.find(ai_InsItem.class, "I_P_INS_ITEM_ID = ? and B_Deleted = 0", "" + iPInsItemID);
                                String sValue1 = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigOne);
                                String sValue2 = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigTwo);
                                String sValue3 = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigThree);
                                String sValue4 = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigFour);
                                String sValue5 = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigFive);
                                String sValue6 = CommonHelper.GetDefaultCheckboxValue(oPInsItem.sConfigSix);
                                ai_InsItem oTempInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iPInsItemID);
                                ai_InsItem oInsItem = new ai_InsItem(
                                        iPInsItemID, oTempInsItem.iInsID, 0,
                                        sValue, sValue1, sValue2, sValue3, sValue4, sValue5, sValue6,
                                        oTempInsItem.sQType,
                                        "", "", "", "", "", "",
                                        "", false, lsTempInsItem1.size() + 1,
                                        0, "c", "", ""
                                );
                                oInsItem.save();

                                reloadInsItems();
                                scrollToBottom();
                            }
                        }
                    }).show();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Ins_items_3rd.addNewInsItem", ex, this);
        }
    }

    private void showInsAction() {
        String[] options = { getResources().getString(R.string.add_new_item) };
        new MaterialDialog.Builder(this)
                .title(R.string.alert_title_action)
                .items(options)
                .itemsCallbackSingleChoice(-1, (dialog, view, which, text) -> {
                    if (which == 0) addNewInsItem();
                    return true;
                })
                //.autoDismiss(false)
                .negativeText(R.string.md_cancel_label)
                .onNegative((dialog, which) -> dialog.dismiss())
                .show();
    }

    private void scrollToBottom() {
        scrollView.postDelayed(() -> scrollView.fullScroll(View.FOCUS_DOWN), 200);
    }

    private void scrollToFirstInvalidView() {
        List<ai_InsItem> arrInsItem = CommonDB.GetChildInsItem(iPInsItemID, oPInsItem.iInsID);
        if (arrInsItem == null) return;

        int targetPos = 0;
        if (!oPInsItem.bInspectionByPassCompulsory) {
            for (int i = 0, length = arrInsItem.size(); i < length; i++) {
                if (arrInsItem.get(i).hasCompulsoryConfigs()) {
                    targetPos = i;
                    break;
                }
            }
        }

        if (targetPos < layoutInsItems.getChildCount()) {
            View targetView = layoutInsItems.getChildAt(targetPos);
            scrollView.postDelayed(() -> scrollView.smoothScrollTo(0, targetView.getTop()), 200);
        }
    }

    private String[] broadcastNames() {
        return new String[] {
            Constants.Broadcasts.sActionInsItem, Constants.Broadcasts.sDeleteInsItem,
            Constants.Broadcasts.sReloadInsItem, Constants.Broadcasts.sInsItemChanged,
            Constants.Broadcasts.sActionDownloadAllPhotos, Constants.Broadcasts.sEditTextFocused,
            Constants.Broadcasts.ACTION_SCAN_DOCUMENT
        };
    }
    private void unregisterBroadcasts() {
        for (String ignored : broadcastNames())
            LocalBroadcastManager.getInstance(this).unregisterReceiver(mMessageReceiver);
    }
    private void registerBroadcasts() {
        for (String evt: broadcastNames())
            LocalBroadcastManager.getInstance(this).registerReceiver(mMessageReceiver, new IntentFilter(evt));
    }

    private final BroadcastReceiver mMessageReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            // Get extra data included in the Intent
            String sAction = intent.getAction();
            if (sAction == null) return;
            if (sAction.equalsIgnoreCase(Constants.Broadcasts.sActionInsItem)) {
                long iInsItemId = intent.getLongExtra("InsItemID", 0);
                ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, iInsItemId);
                ai_InsItem oPInsItem = ai_InsItem.findById(ai_InsItem.class, (long) oInsItem.iPInsItemID);
                duplicateItemIfNeeded(oPInsItem, oInsItem);
            } else if (sAction.equalsIgnoreCase(Constants.Broadcasts.sDeleteInsItem)
                    || sAction.equalsIgnoreCase(Constants.Broadcasts.sReloadInsItem)
                    || sAction.equalsIgnoreCase(Constants.Broadcasts.sInsItemChanged)) {
                reloadInsItems();
            } else if (sAction.equalsIgnoreCase(Constants.Broadcasts.sActionDownloadAllPhotos)) {
                List<ai_Photo> photos = db_Media.GetPhotosForInspection(iInsID);
                new ImageDownloader(photos, false, if_Ins_items_3rd.this, () -> reloadInsItems()).execute();
            } else if (sAction.equalsIgnoreCase(Constants.Broadcasts.sEditTextFocused)) {
                libraryParams.iInsItemID = intent.getIntExtra(Constants.Extras.iInsItemID, 0);
                libraryParams.iPosition = intent.getIntExtra(Constants.Extras.iPosition, 0);
                libraryParams.iPLayoutID= intent.getIntExtra(Constants.Extras.iPLayoutID, 0);
                View view = getCurrentFocus();
                if (view instanceof EditText) {
                    resetFocusingEditText();
                    focusingEditText = (EditText)view;
                }
            } else if (sAction.equalsIgnoreCase(Constants.Broadcasts.ACTION_SCAN_DOCUMENT)) {
                iSelectedPosition = intent.getIntExtra(Constants.Extras.iPosition, 0);
                iSelectedInsItemID = intent.getIntExtra(Constants.Extras.iInsItemID, 0);
                ScannerUtils.launchDocumentScanner(if_Ins_items_3rd.this, scannerLauncher);
            }
        }
    };

    private void duplicateItemIfNeeded(final ai_InsItem oPInsItem, final ai_InsItem origInsItem) {
        try {
            String sTitleText = "Duplicate Item";
            if (oPInsItem.sQType.equalsIgnoreCase("S")) {
                sTitleText = "New Name";
            }

            new MaterialDialog.Builder(this)
                    .title(sTitleText)
                    .content(null)
                    .inputType(InputType.TYPE_CLASS_TEXT)
                    .input("Enter name", origInsItem != null ? origInsItem.sName + " - Copy" : "", (dialog, input) -> {
                        String sValue = input.toString();
                        if (StringUtils.isEmpty(sValue)) {
                            Toast.makeText(getBaseContext(), "This field can not be empty.", Toast.LENGTH_LONG).show();
                        } else {
                            if (CommonDB_Inspection.bValidateDuplicateItemName(origInsItem.iPInsItemID, sValue)) {
                                Toast.makeText(getBaseContext(), CommonUI_InsItem.sMessage_AddEditItem_DuplicateName, Toast.LENGTH_LONG).show();
                            } else {
                                CommonDB_Inspection.DuplicateInsItem(origInsItem, oPInsItem, sValue);
                                reloadInsItems();
                                scrollToBottom();
                            }
                        }
                    }).show();

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Ins_items_3rd.duplicateItemIfNeeded", ex, this);
        }
    }

    private void showsCommentsLibrary() {
        if (libraryParams.iInsItemID == 0) return;
        if (focusingEditText != null) {
            libraryParams.iTextPosition = focusingEditText.getSelectionStart();
        }
        Intent oIntent = new Intent(this, if_CommentsLibrary.class);
        oIntent.putExtra(Constants.Extras.iPosition, libraryParams.iPosition);
        oIntent.putExtra(Constants.Extras.iTextPosition, libraryParams.iTextPosition);
        oIntent.putExtra(Constants.Extras.iInsItemID, libraryParams.iInsItemID);
        oIntent.putExtra(Constants.Extras.iPLayoutID, libraryParams.iPLayoutID);
        startActivityForResult(oIntent, COMMENTS_LIBRARY_REQUEST_CODE);
    }

    @Override
    public void onKeyboardHeightChanged(int height, int orientation) {
        boolean keyboardIsShown = height > 0;

        if (libraryParams.iInsItemID > 0) {
            accessoryView.setVisibility(keyboardIsShown ? View.VISIBLE : View.GONE);
        } else {
            accessoryView.setVisibility(View.GONE);
        }

        bottomNavView.setVisibility(keyboardIsShown ? View.GONE : View.VISIBLE);

        scrollView.postDelayed(() -> setScrollChangeListener(keyboardIsShown), 200);
    }

    @Override
    public void onRefresh() {
        displayInsItems(oInspection.shouldBypassCompulsory());
    }

    private void resetFocusingEditText() {
        focusingEditText = null;
    }

    private void setScrollChangeListener(boolean keyboardIsShown) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (keyboardIsShown) {
                scrollView.setOnScrollChangeListener((v, scrollX, scrollY, oldScrollX, oldScrollY) -> {
                    CommonHelper.hideSoftKeyboard(this);
                    if (focusingEditText != null) focusingEditText.clearFocus();
                });
            } else {
                scrollView.setOnScrollChangeListener(null);
            }
        }
    }

    private static class LibraryParams {
        int iPosition;
        int iTextPosition;
        int iInsItemID;
        int iPLayoutID;
    }

    private InsDurationManager getDurationManager() {
        if (mDurationManager == null)
            mDurationManager = new InsDurationManager(this, iInsID, this);
        return mDurationManager;
    }
}
