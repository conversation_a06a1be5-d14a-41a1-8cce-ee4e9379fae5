package com.snapinspect.snapinspect3.activitynew.products

import android.content.Context
import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.filled.Info
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.snapinspect.snapinspect3.Helper.Constants
import com.snapinspect.snapinspect3.IF_Object.ai_Product
import com.snapinspect.snapinspect3.IF_Object.ai_ProductCost
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.SI_DB.db_Product
import com.snapinspect.snapinspect3.SI_DB.db_ProductCost
import com.snapinspect.snapinspect3.activitynew.products.ProductCostsViewModel.ViewState
import com.snapinspect.snapinspect3.views.composables.FitHeightTextField
import com.snapinspect.snapinspect3.views.composables.getActionBarSize
import com.snapinspect.snapinspect3.util.NumberUtils
import com.snapinspect.snapinspect3.views.composables.SnapTheme
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

class if_ProductCosts : ComponentActivity() {
    private lateinit var viewModel: ProductCostsViewModel
    private lateinit var addProductLauncher: ActivityResultLauncher<Intent>

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setTheme(R.style.RemoveShadowActionBar)
        actionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            show()
            setTitle(R.string.title_product_costs)
        }

        addProductLauncher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Constants.ResultCodes.ADD_PRODUCT_RESULT) {
                val data = result.data
                val iProductID = data?.getLongExtra(Constants.Extras.I_PRODUCT_ID, 0) ?: 0
                if (iProductID > 0) {
                    val product = db_Product.getProductByID(iProductID)
                    viewModel.addProductCost(product)
                }
            }
        }

        val iInsItemID = intent.getIntExtra(Constants.Extras.iInsItemID, 0)
        val iPosition = intent.getIntExtra(Constants.Extras.iPosition, 0)

        setContent {
            viewModel = viewModel(
                factory = ProductCostsViewModel.Factory(this@if_ProductCosts, iInsItemID, iPosition)
            )
            SnapTheme {
                ProductCostsView(
                    viewModel = viewModel,
                    navigateToAddProduct = ::navigateToAddProduct,
                    navigateToProduct = { product ->
                        val intent = Intent(this, if_ProductDetails::class.java)
                        intent.putExtra(Constants.Extras.I_PRODUCT_ID, product.id)
                        startActivity(intent)
                    }
                )
            }
        }

        val productCosts = db_ProductCost.getAllProductCosts(iInsItemID)
        if (productCosts.isEmpty()) navigateToAddProduct()
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        val menuRes = when (viewModel.viewState) {
            ViewState.VIEW -> R.menu.menu_edit
            ViewState.EDIT -> R.menu.menu_save
        }
        menu?.let { menuInflater.inflate(menuRes, it) }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean = when (item.itemId) {
        android.R.id.home -> {
            finish()
            true
        }
        R.id.action_menu_edit, R.id.action_menu_save -> {
            viewModel.toggleViewState()
            updateNavigationTitle()
            invalidateOptionsMenu()
            true
        }
        else -> super.onOptionsItemSelected(item)
    }

    private fun updateNavigationTitle() {
        val titleRes = when (viewModel.viewState) {
            ViewState.VIEW -> R.string.title_product_costs
            ViewState.EDIT -> R.string.title_edit_product_costs
        }
        actionBar?.setTitle(titleRes)
    }

    private fun navigateToAddProduct() {
        val iInsItemID = intent.getIntExtra(Constants.Extras.iInsItemID, 0)
        val intent = Intent(this, if_Products::class.java).apply {
            putExtra(Constants.Extras.iInsItemID, iInsItemID)
        }
        addProductLauncher.launch(intent)
    }
}

class ProductCostsViewModel(
    context: Context,
    private val iInsItemID: Int,
    private val iPosition: Int
) : ViewModel() {
    private val contextRef: WeakReference<Context> = WeakReference(context)
    enum class ViewState {
        VIEW, EDIT
    }
    class Factory(
        private val context: Context,
        private val iInsItemID: Int,
        private val iPosition: Int
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(ProductCostsViewModel::class.java)) {
                @Suppress("UNCHECKED_CAST")
                return ProductCostsViewModel(context, iInsItemID, iPosition) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }

    private val _productCosts = MutableStateFlow<List<ai_ProductCost>>(emptyList())
    val productCosts: StateFlow<List<ai_ProductCost>> = _productCosts

    private val _totalCost = MutableStateFlow(0.0)
    val totalCost: StateFlow<Double> = _totalCost

    var viewState by mutableStateOf(ViewState.VIEW)
        private set

    init {
        viewModelScope.launch {
            loadProductCosts()
        }
    }

    private fun loadProductCosts() {
        _productCosts.value = db_ProductCost.getAllProductCosts(iInsItemID)
        _totalCost.value = db_ProductCost.getTotalCost(iInsItemID)
        db_ProductCost.updateInsItemCost(iInsItemID, iPosition, _totalCost.value)
    }

    fun toggleViewState() {
        viewState = when (viewState) {
            ViewState.VIEW -> ViewState.EDIT
            ViewState.EDIT -> ViewState.VIEW
        }
    }

    fun addProductCost(product: ai_Product) {
        viewModelScope.launch {
            contextRef.get()?.let {
                val newCost = ai_ProductCost(it, product, iInsItemID)
                db_ProductCost.saveProductCost(newCost)
                loadProductCosts()
            }
        }
    }

    fun removeProductCost(productCost: ai_ProductCost) {
        viewModelScope.launch {
            contextRef.get()?.let {
                db_ProductCost.deleteProductCost(productCost.id)
                loadProductCosts()
            }
        }
    }

    fun updateProductCostUnit(productCost: ai_ProductCost, newQuantity: Double) {
        viewModelScope.launch {
            contextRef.get()?.let {
                val updatedProductCost = productCost.apply {
                    dUnit = newQuantity
                    dTotalCost = newQuantity * dUnitCost
                }
                db_ProductCost.saveProductCost(updatedProductCost)
                loadProductCosts()
            }
        }
    }

    fun updateProductCostPrice(productCost: ai_ProductCost, newPrice: Double) {
        viewModelScope.launch {
            contextRef.get()?.let {
                val updatedProductCost = productCost.apply {
                    dUnitCost = newPrice
                    dTotalCost = dUnit * newPrice
                }
                db_ProductCost.saveProductCost(updatedProductCost)
                loadProductCosts()
            }
        }
    }

    fun updateProductCostNotes(productCost: ai_ProductCost, newNotes: String) {
        viewModelScope.launch {
            contextRef.get()?.let {
                val updatedProductCost = productCost.apply {
                    sNotes = newNotes
                }
                db_ProductCost.saveProductCost(updatedProductCost)
                loadProductCosts()
            }
        }
    }

}

@Composable
fun ProductCostsView(
    viewModel: ProductCostsViewModel,
    navigateToAddProduct: () -> Unit,
    navigateToProduct: (ai_Product) -> Unit = {}
) {
    val productCosts by viewModel.productCosts.collectAsState()
    val totalCost by viewModel.totalCost.collectAsState()

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(colorResource(id = R.color.colorPrimary))
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = getActionBarSize())
                .background(Color.White)
        ) {
            LazyColumn(
                modifier = Modifier.weight(1f)
            ) {
                items(productCosts) { productCost ->
                    when (viewModel.viewState) {
                        ViewState.VIEW -> ProductCostItem(
                            productCost = productCost,
                            onViewProduct = { navigateToProduct(productCost.associatedProduct) }
                        )
                        ViewState.EDIT -> ProductCostEditItem(
                            productCost = productCost,
                            onQuantityChange = { newQuantity ->
                                viewModel.updateProductCostUnit(productCost, newQuantity)
                            },
                            onPriceChange = { newPrice ->
                                viewModel.updateProductCostPrice(productCost, newPrice)
                            },
                            onNotesChange = { newNotes ->
                                viewModel.updateProductCostNotes(productCost, newNotes)
                            },
                            onRemove = {
                                viewModel.removeProductCost(productCost)
                            }
                        )
                    }
                    Divider(color = Color.LightGray, thickness = 1.dp)
                }
                if (productCosts.isNotEmpty()) {
                    item {
                        TotalCost(totalCost)
                    }
                }
            }
        }

        if (viewModel.viewState == ViewState.VIEW) {
            FloatingActionButton(
                onClick = navigateToAddProduct,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(16.dp),
                backgroundColor = colorResource(id = R.color.colorPrimary)
            ) {
                Icon(Icons.Filled.Add, contentDescription = "Add Product Cost", tint = Color.White)
            }
        }
    }
}

@Composable
fun ProductCostEditItem(
    productCost: ai_ProductCost,
    onQuantityChange: (Double) -> Unit,
    onPriceChange: (Double) -> Unit,
    onNotesChange: (String) -> Unit,
    onRemove: () -> Unit
) {

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                productCost.associatedProduct.sName,
                color = Color.Black,
                fontSize = dimensionResource(id = R.dimen.text_h5).value.sp,
                fontWeight = FontWeight.Bold
            )
            Text(
                productCost.associatedProduct.sSKU,
                color = colorResource(id = R.color.light_gray),
                fontWeight = FontWeight.Normal
            )
        }

        // sNotes
        OutlinedTextField(
            value = productCost.sNotes ?: "",
            onValueChange = { onNotesChange(it) },
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(8.dp)),
            placeholder = {
                Column {
                    Text("Add Note...")
                    Spacer(modifier = Modifier.fillMaxHeight())
                }
            },
            shape = RoundedCornerShape(8.dp)
        )

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                if (productCost.associatedProduct.bOneOffCost) {
                    Text("${productCost.dUnit}")
                } else {
                    FitHeightTextField(
                        value = productCost.dUnit.toString(),
                        onValueChange = { onQuantityChange(it.toDoubleOrNull() ?: 0.0) },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier
                            .width(80.dp)
                            .height(dimensionResource(id = R.dimen.default_height).value.dp),
                        singleLine = true,
                        fontSize = dimensionResource(id = R.dimen.text_normal).value.sp,
                        textColor = colorResource(id = R.color.dark_input_text_color),
                        textAlign = TextAlign.Center,
                        borderColor = colorResource(id = R.color.light_gray),
                        borderWidth = 1f
                    )
                }

                Text(productCost.sUnitName())
                Text("x")

                if (productCost.associatedProduct.bAllowEdit) {
                    FitHeightTextField(
                        value = productCost.dUnitCost.toString(),
                        onValueChange = { onPriceChange(it.toDoubleOrNull() ?: 0.0) },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
                        modifier = Modifier
                            .width(80.dp)
                            .height(dimensionResource(id = R.dimen.default_height).value.dp),
                        singleLine = true,
                        fontSize = dimensionResource(id = R.dimen.text_normal).value.sp,
                        textColor = colorResource(id = R.color.dark_input_text_color),
                        textAlign = TextAlign.Center,
                        borderColor = colorResource(id = R.color.light_gray),
                        borderWidth = 1f
                    )
                } else {
                    Text(NumberUtils.formatCurrencyWithoutGrouping(productCost.dUnitCost))
                }
            }
            IconButton(onClick = onRemove, modifier = Modifier.size(24.dp).align(Alignment.CenterVertically)) {
                Icon(Icons.Filled.Delete, contentDescription = "Delete", tint = Color.Gray)
            }
        }
        Text("Total: ${NumberUtils.formatCurrencyWithoutGrouping(productCost.dTotalCost)}")
    }
}

@Composable
fun ProductCostItem(productCost: ai_ProductCost, onViewProduct: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    productCost.associatedProduct.sName,
                    color = Color.Black,
                    fontSize = dimensionResource(id = R.dimen.text_h5).value.sp,
                    fontWeight = FontWeight.Bold
                )
                IconButton(onClick = onViewProduct, modifier = Modifier.size(16.dp)) {
                    Icon(
                        Icons.Filled.Info,
                        contentDescription = "Info",
                        tint = Color.Gray
                    )
                }
            }
            Text(
                productCost.associatedProduct.sSKU,
                color = colorResource(id = R.color.light_gray),
                fontWeight = FontWeight.Normal
            )
        }

        if (!productCost.sNotes.isNullOrEmpty()) {
            Text(productCost.sNotes, color = Color.Gray)
        }

        Text("${productCost.dUnit} ${productCost.sUnitName()} x ${NumberUtils.formatCurrencyWithoutGrouping(productCost.dUnitCost)}")
        Text("Total: ${NumberUtils.formatCurrencyWithoutGrouping(productCost.dTotalCost)}")
    }
}

@Composable
private fun TotalCost(total: Double) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(16.dp),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        Text("Total:", fontWeight = FontWeight.Bold)
        Text(NumberUtils.formatCurrencyWithoutGrouping(total), fontWeight = FontWeight.Bold)
    }
}

@Preview(
    name = "Light Mode", uiMode = Configuration.UI_MODE_NIGHT_NO, showBackground = true
)
@Composable
fun PreviewProductCostsViewWithLightMode() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
    ) {
        TotalCost(100.0000999)
    }
}