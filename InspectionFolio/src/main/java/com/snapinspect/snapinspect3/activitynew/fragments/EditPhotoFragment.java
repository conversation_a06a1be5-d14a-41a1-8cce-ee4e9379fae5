package com.snapinspect.snapinspect3.activitynew.fragments;

import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.snapinspect.snapinspect3.R;
import com.theartofdev.edmodo.cropper.CropImageView;

public class EditPhotoFragment extends Fragment {

    private EditPhotoCallback mCallback;

    public interface EditPhotoCallback {
        void didFinishEditPhoto(Bitmap bitmap);
    }

    private final Uri mSourceUri;
    private CropImageView mCropView;

    public EditPhotoFragment(Uri sourceUri) {
        mSourceUri = sourceUri;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_edit_photo, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // Crop image view
        mCropView = view.findViewById(R.id.cropImageView);
        mCropView.setImageUriAsync(mSourceUri);
        mCropView.setMultiTouchEnabled(true);

        // Save button
        view.findViewById(R.id.done_button).setOnClickListener(v -> saveCroppedImage());
        view.findViewById(R.id.cancel_button).setOnClickListener(v -> dismiss());

        // Rotation button
        view.findViewById(R.id.btn_rotate_left).setOnClickListener(v -> {
            mCropView.rotateImage(-90);
        });
        view.findViewById(R.id.btn_rotate_right).setOnClickListener(v -> {
            mCropView.rotateImage(90);
        });
    }

    private void dismiss() {
        if (getActivity() == null) return;
        getActivity().getSupportFragmentManager().popBackStack();
    }

    private void saveCroppedImage() {
        if (mCallback != null) {
            mCallback.didFinishEditPhoto(mCropView.getCroppedImage());
        }
        dismiss();
    }

    public void setCallback(EditPhotoCallback callback) {
        mCallback = callback;
    }
}
