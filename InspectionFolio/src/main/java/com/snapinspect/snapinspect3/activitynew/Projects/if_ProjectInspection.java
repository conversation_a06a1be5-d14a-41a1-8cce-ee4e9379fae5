package com.snapinspect.snapinspect3.activitynew.Projects;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.ResultReceiver;
import android.view.MenuItem;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import androidx.annotation.ArrayRes;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import com.afollestad.materialdialogs.MaterialDialog;
import com.orm.SugarRecord;
import com.orm.query.Condition;
import com.orm.query.Select;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_Projects;
import com.snapinspect.snapinspect3.activity.UploadService;
import com.snapinspect.snapinspect3.activitynew.if_existasset;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Ins_3rd;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Layout_3rd;
import com.snapinspect.snapinspect3.async.SyncProjectService;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.ThrottledSearch;
import com.snapinspect.snapinspect3.views.EmptyDataView;
import com.snapinspect.snapinspect3.views.SegmentedControl;
import com.softw4re.views.InfiniteListView;

import java.lang.ref.WeakReference;
import java.util.*;

public class if_ProjectInspection extends Activity implements ProjectInspectionsListAdapter.Listener {

    private ai_Project mProject;

    private EditText searchBar;
    private boolean mHasInspections;

    public enum InspectionStatus {
        IN_PROGRESS, COMPLETED;
        static InspectionStatus fromOrdinal(int x) {
            return x == 1 ? COMPLETED : IN_PROGRESS;
        }

        static List<InspectionStatus> allCases() {
            return Arrays.asList(IN_PROGRESS, COMPLETED);
        }

        String getName() {
            switch (this) {
                case IN_PROGRESS: return "In Progress";
                case COMPLETED: return "Completed";
                default:
                    break;
            }
            return "";
        }
    }

    private InspectionStatus mInspectionStatus = InspectionStatus.IN_PROGRESS;

    private InfiniteListView<Object> listView;

    public void setInspectionStatus(InspectionStatus status) {
        mInspectionStatus = status;
        reloadListView();
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_project_inspection);
        try {
            getActionBar().setDisplayHomeAsUpEnabled(true);
            setupViews();
            setupData();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        reloadListView();
    }

    @Override
    public boolean onMenuItemSelected(int featureId, @NonNull MenuItem item) {
        super.onBackPressed();
        return super.onMenuItemSelected(featureId, item);
    }

    private void reloadListView() {
        List<v_ProjectInspection> projectInspections = new ArrayList<>();
        List<v_ProjectInspection> lsProjectInspection = db_Projects.getProjectInspection(
                mProject.iSProjectID, mInspectionStatus, searchBar.getText().toString());
        if (mInspectionStatus == InspectionStatus.IN_PROGRESS) {
            List<v_ProjectInspection> lsCompletedNotUploaded = new ArrayList<>();
            List<v_ProjectInspection> lsStartedNotCompleted = new ArrayList<>();
            List<v_ProjectInspection> lsNotStarted = new ArrayList<>();
            for (v_ProjectInspection projectInspection : lsProjectInspection) {
                if (projectInspection.isCompletedNotUploaded() || projectInspection.isCompletedAndContinue())
                    lsCompletedNotUploaded.add(projectInspection);
                else if (projectInspection.iInspectionID > 0) lsStartedNotCompleted.add(projectInspection);
                else lsNotStarted.add(projectInspection);
            }
            Collections.sort(lsStartedNotCompleted, (o1, o2) -> o2.dtInsStart.compareTo(o1.dtInsStart));
            Collections.sort(lsCompletedNotUploaded, (o1, o2) -> o2.dtInsStart.compareTo(o1.dtInsStart));
            projectInspections.addAll(lsStartedNotCompleted);
            projectInspections.addAll(lsCompletedNotUploaded);
            projectInspections.addAll(lsNotStarted);
        } else {
            List<v_ProjectInspection> lsEditedInspections = new ArrayList<>();
            List<v_ProjectInspection> lsUploadedInspections = new ArrayList<>();
            for (v_ProjectInspection projectInspection : lsProjectInspection) {
                if (projectInspection.isCompletedAndContinue())
                    lsEditedInspections.add(projectInspection);
                else
                    lsUploadedInspections.add(projectInspection);
            }
            Collections.sort(lsEditedInspections, (o1, o2) -> o2.dtInsStart.compareTo(o1.dtInsStart));
            Collections.sort(lsUploadedInspections, (o1, o2) -> o2.dtInsStart.compareTo(o1.dtInsStart));
            projectInspections.addAll(lsEditedInspections);
            projectInspections.addAll(lsUploadedInspections);
        }

        listView = findViewById(R.id.lv_Inspections);
        EmptyDataView emptyDataView = findViewById(R.id.view_empty_data);
        emptyDataView.setListener(() ->
            CommonHelper.openURL(this, getString(R.string.projects_support_url)));
        if (projectInspections.isEmpty() && StringUtils.isEmpty(searchBar.getText().toString())) {
            listView.setVisibility(View.GONE);
            emptyDataView.setVisibility(View.VISIBLE);
            emptyDataView.setHasInspections(mHasInspections);
            emptyDataView.setInspectionStatus(mInspectionStatus);
        } else {
            listView.setVisibility(View.VISIBLE);
            emptyDataView.setVisibility(View.GONE);

            ArrayList<Object> data = new ArrayList<>();
            data.add(mProject);
            data.addAll(projectInspections);

            ProjectInspectionsListAdapter adapter = new ProjectInspectionsListAdapter(this, -1, new ArrayList<>(data));
            adapter.setListener(this);
            listView.setAdapter(adapter);
            adapter.notifyDataSetChanged();
            listView.hasMore(false);
        }
    }

    @Override
    public void didSelectProjectInspection(v_ProjectInspection projectInspection) {
        if (projectInspection.isUploaded() && !projectInspection.isCompletedAndContinue()) {
            @ArrayRes int actionsRes = CommonJson.disableEditInspection(this) ?
                    R.array.project_inspection_actions_no_edit : R.array.project_inspection_actions;
            new MaterialDialog.Builder(this)
                .title(R.string.alert_title_action)
                .items(actionsRes)
                .itemsCallback((dialog, itemView, position, text) -> didSelectInspectionAction(projectInspection, text.toString()))
                .positiveText(R.string.action_cancel)
                .show();
        } else {
            ai_Inspection oInspection = CommonDB_Inspection.GetInspectionByID(projectInspection.iInspectionID);
            if (oInspection != null && oInspection.getId() > 0) {
                startActivity(if_Ins_3rd.newIntent(this,
                        oInspection.getId().intValue(), oInspection.sType, oInspection.sPTC));
            } else {
                ai_User assignedUser = projectInspection.getAssignedInspector();
                if (assignedUser != null && !assignedUser.isLoggedIn(if_ProjectInspection.this)) {
                    new MaterialDialog.Builder(if_ProjectInspection.this)
                        .title(R.string.alert_title_message)
                        .content(getString(R.string.inspection_is_assigned_to_other_user, assignedUser.sName))
                        .positiveText(R.string.start)
                        .onPositive((dialog, which) -> checkIfInspectionLocked(projectInspection))
                        .negativeText(R.string.md_cancel_label)
                        .show();
                } else {
                    checkIfInspectionLocked(projectInspection);
                }
            }
        }
    }

    @Override
    public void onRefresh() {
        if (mProject == null) {
            listView.stopLoading();
            return;
        }
        Intent intent = new Intent(this, SyncProjectService.class);
        intent.putExtra(Constants.Keys.oResultReceiver,
                new SyncProjectResultReceiver(new Handler(Looper.getMainLooper()), this));
        startService(intent);
    }

    @Override
    public void uploadProjectInspection(v_ProjectInspection projectInspection) {
        CommonUI.ShowAlert(this,
            getString(R.string.alert_title_message),
            getString(R.string.msg_uploading_inspection),
            R.string.alert_action_complete_upload,
            R.string.alert_action_cancel, () -> {
                CommonValidate.Permission_Validate(if_ProjectInspection.this);
                int iInsID = projectInspection.iInspectionID;
                // mark inspection as complete
                ai_Inspection oInspection = CommonDB_Inspection.GetInspectionByID(iInsID);
                if (oInspection != null) {
                    oInspection.dtEndDate = CommonHelper.sDateToString(new Date());
                    oInspection.bComplete = true;
                    oInspection.save();
                }
                if (CommonValidate.validateInspectionsCompulsoryItems(if_ProjectInspection.this, iInsID)) {
                    setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
                    // upload inspection
                    Intent oIntent = new Intent(if_ProjectInspection.this, UploadService.class);
                    oIntent.putExtra("receiver", new UploadReceiver(
                            new Handler(Looper.getMainLooper()), if_ProjectInspection.this, () -> {
                        fetchLatestProject(projectInspection);
                        fetchLatestProjectInspection(projectInspection);
                    }));
                    oIntent.putExtra(Constants.Extras.iInsID, iInsID);
                    if_ProjectInspection.this.startService(oIntent);
                }
            }
        );
    }

    private void fetchLatestProject(v_ProjectInspection projectInspection) {
        NetworkUtils.getProject(this, projectInspection.iProjectID, project -> {
            if (project != null) {
                project.checkIfExistAndSave();
                setupData();
                reloadListView();
            }
        });
    }

    private void fetchLatestProjectInspection(v_ProjectInspection projectInspection) {
        NetworkUtils.getProjectInspection(this, projectInspection.iSProjectAssetInsTypeID, projectInspection1 -> {
            if (projectInspection1 != null) {
                projectInspection1.checkIfExistAndSave();
                setupData();
                reloadListView();
            }
        });
    }

    private void checkIfInspectionLocked(v_ProjectInspection projectInspection) {
        // if no internet should start inspection, otherwise when have internet then check _iLock
        if (!NetworkUtils.isNetworkAvailable(this)) {
            startInspection(projectInspection);
            return;
        }
        MaterialDialog progressDialog = CommonUI.ShowMaterialProgressDialog(
                this, "Message", "Loading project inspection...");
        NetworkUtils.getProjectInspection(this, projectInspection.iSProjectAssetInsTypeID, p -> {
            CommonUI.DismissMaterialProgressDialog(progressDialog);
            if (p == null) return;
            p.checkIfExistAndSave();
            if (p.isUploaded()) {
                fetchLatestProject(projectInspection);
                CommonUI.ShowAlert(if_ProjectInspection.this, "Message", getString(R.string.msg_project_ins_already_uploaded));
            } else {
                ai_User lockedInspector = p.getLockedInspector();
                if (lockedInspector != null) {
                    String sMessage;
                    if (lockedInspector.isLoggedIn(if_ProjectInspection.this)) {
                        sMessage = getString(R.string.message_locked_on_other_device);
                    } else {
                        sMessage = "The inspection has been started by other inspector" +
                                (StringUtils.isEmpty(lockedInspector.sName) ? "" : " " + lockedInspector.sName) +
                                ", are you sure to start it again?";
                    }
                    new MaterialDialog.Builder(if_ProjectInspection.this)
                            .title(R.string.alert_title_message)
                            .content(sMessage)
                            .positiveText(R.string.start)
                            .onPositive((dialog, which) -> startInspection(projectInspection))
                            .negativeText(R.string.md_cancel_label)
                            .show();
                } else {
                    startInspection(projectInspection);
                }
            }
        });
    }

    private void setupData() {
        long projectId = getIntent().getLongExtra(Constants.Extras.iProjectID, 0);
        mProject = ai_Project.findById(ai_Project.class, projectId);
        mHasInspections = ai_ProjectInspection.count(
                ai_ProjectInspection.class, "I_PROJECT_ID = " + mProject.iSProjectID, null) > 0;
        try {
            getActionBar().setTitle(mProject.sName);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    private void setupViews() {
        // Segmented control
        SegmentedControl segmentedControl = findViewById(R.id.segmentedControl);
        segmentedControl.setCornerRadius(18);
        segmentedControl.setDeactiveColor(ContextCompat.getColor(this, R.color.colorPrimary));
        segmentedControl.setActiveColor(ContextCompat.getColor(this, R.color.white_color));
        ArrayList<String> items = new ArrayList<>();
        for (InspectionStatus item: InspectionStatus.allCases()) {
            items.add(item.getName());
        }
        segmentedControl.setControlItems(items);
        segmentedControl.setListener(this, selectedIndex ->
                setInspectionStatus(InspectionStatus.fromOrdinal(selectedIndex)));

        // search bar
        searchBar = findViewById(R.id.search_inspection);
        new ThrottledSearch(this, searchTerm -> reloadListView()).bindTo(searchBar);
        searchBar.setOnEditorActionListener((v, actionId, event) -> {
            if (EditorInfo.IME_ACTION_SEARCH == actionId) {
                CommonHelper.hideSoftKeyboard(this);
                return true;
            }
            return false;
        });
    }

    private void startInspection(v_ProjectInspection projectInspection) {
        if (!CommonUI.bAppPermission(this)) return;
        List<ai_InsType> oInsTypes = SugarRecord.find(ai_InsType.class, "I_S_INS_TYPE_ID = ?",
                String.valueOf(projectInspection.iInsTypeID));
        if (oInsTypes.isEmpty()) {
            CommonUI.ShowAlert(this, getString(R.string.title_alert_error),
                    getString(R.string.alert_message_inspection_type_not_found));
            return;
        }
        ai_InsType oInsType = oInsTypes.get(0);
        List<ai_AssetLayout> lsAssetLayout = CommonDB.GetAssetLayouts(projectInspection.iAssetID, oInsType.sPTC, this);
        if (lsAssetLayout == null || lsAssetLayout.isEmpty()) {
            List<ai_Layout> lsLayoutItem = Select.from(ai_Layout.class)
                    .where(Condition.prop("S_PTC").eq(oInsType.sPTC), Condition.prop("I_SP_LAYOUT_ID").eq("" + 0))
                    .list();
            boolean bPass = true;
            for (ai_Layout ai_layout : lsLayoutItem) {
                String sConfig = oInsType.sType.equals("F") ? ai_layout.sFConfig : ai_layout.sSConfig;
                if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null) {
                    bPass = false;
                    break;
                }
            }
            if (bPass) {
                IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                Date oNow = new Date();
                int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                        this, new ArrayList<>(), oInsType, projectInspection.iAssetID,
                        "", CommonHelper.sDateToString(oNow), "",
                        projectInspection.sAddressOne, projectInspection.sAddressTwo, 0, "");
                gotoInspection(iInspectionID, projectInspection.iSProjectAssetInsTypeID);
            } else {
                startActivity(if_Layout_3rd.newIntent(this, projectInspection.iAssetID,
                        projectInspection.iInsTypeID, projectInspection.sAddressOne,
                        projectInspection.sAddressTwo, projectInspection.iSProjectAssetInsTypeID));
            }
        } else {
            IF_CreateInspection oCreateInspection = new IF_CreateInspection();
            int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                    this, new ArrayList<>(lsAssetLayout), oInsType, projectInspection.iAssetID,
                    "", CommonHelper.sDateToString(null), "",
                    projectInspection.sAddressOne, projectInspection.sAddressTwo, 0, "");
            gotoInspection(iInspectionID, projectInspection.iSProjectAssetInsTypeID);
        }
    }

    private void gotoInspection(int iInspectionID, int iSProjectInspectionID) {
        startActivity(if_Ins_3rd.newIntent(this, iInspectionID));
        CommonProjectInspection.startInspection(this, iSProjectInspectionID, iInspectionID);
    }

    private static class UploadReceiver extends ResultReceiver {
        interface Listener {
            void onUploadComplete();
        }
        private final WeakReference<Context> mContext;
        private final MaterialDialog oDialog;
        private final Listener mListener;
        public UploadReceiver(Handler handler, Context context, Listener listener) {
            super(handler);
            mContext = new WeakReference<>(context);
            oDialog = CommonUI.ShowMaterialProgressDialog(context,
                    context.getString(R.string.alert_title_message), "Running. Please wait ...");
            mListener = listener;
        }

        @Override
        protected void onReceiveResult(int resultCode, Bundle resultData) {
            try {
                super.onReceiveResult(resultCode, resultData);
                switch (resultCode) {
                    case UploadService.UPDATE_PROGRESS:
                        if (!StringUtils.isEmpty(resultData.getString(Constants.Keys.sTitle))) {
                            oDialog.setTitle("Running. Please wait... - " + resultData.getString(Constants.Keys.sTitle));
                        }
                        oDialog.setContent(resultData.getString(Constants.Keys.sMessage));
                        break;
                    case UploadService.UPDATE_SUCCESS:
                    case UploadService.UPDATE_SUCCESSNODATA:
                        CommonUI.DismissMaterialProgressDialog(oDialog);
                        new MaterialDialog.Builder(mContext.get())
                                .title(resultData.getString(Constants.Keys.sTitle))
                                .content(resultData.getString(Constants.Keys.sMessage))
                                .positiveText(R.string.tv_ok)
                                .onPositive((dialog, which) -> {
                                    if (mListener != null) {
                                        mListener.onUploadComplete();
                                    }
                                })
                                .show();
                        break;
                    case UploadService.UPDATE_FAIL:
                        CommonUI.DismissMaterialProgressDialog(oDialog);
                        new MaterialDialog.Builder(mContext.get())
                                .title(resultData.getString(Constants.Keys.sTitle))
                                .content(resultData.getString(Constants.Keys.sMessage))
                                .positiveText(R.string.tv_ok)
                                .onPositive((dialog, which) -> dialog.dismiss())
                                .show();
                        break;
                    default:
                        break;
                }
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "if_ProjectInspection.Upload.onReceiveResult", ex, mContext.get().getApplicationContext());
            }
        }
    }

    private static class SyncProjectResultReceiver extends ResultReceiver {
        private final WeakReference<if_ProjectInspection> mWeakReference;

        private if_ProjectInspection getActivity() {
            return mWeakReference.get();
        }

        public SyncProjectResultReceiver(Handler handler, if_ProjectInspection activity) {
            super(handler);
            mWeakReference = new WeakReference<>(activity);
        }

        @Override
        protected void onReceiveResult(int resultCode, Bundle resultData) {
            super.onReceiveResult(resultCode, resultData);
            if (getActivity() == null) return;
            switch (resultCode) {
                case SyncProjectService.SHOW_LOADING:
                    getActivity().listView.startLoading();
                    break;
                case SyncProjectService.HIDE_LOADING:
                    getActivity().listView.stopLoading();
                    break;
                case SyncProjectService.SYNC_SUCCESS:
                    getActivity().setupData();
                    getActivity().reloadListView();
                    break;
                default:
                    break;
            }
        }
    }

    private void didSelectInspectionAction(v_ProjectInspection projectInspection, String text) {
        if (getString(R.string.menu_edit_inspection).contentEquals(text)) {
            CommonInspection.editInspection(this, projectInspection.iSInspectionID);
        } else if (getString(R.string.menu_update_status).contentEquals(text)) {
            CommonInspection.updateInspectionStatus(this, projectInspection.iSInspectionID, this::reloadListView);
        } else if (getString(R.string.menu_view_report).contentEquals(text)) {
            CommonHelper.DownloadReportRequest(this, projectInspection.iSInspectionID, "/IOAPI/GetReport", "");
        }
    }

}
