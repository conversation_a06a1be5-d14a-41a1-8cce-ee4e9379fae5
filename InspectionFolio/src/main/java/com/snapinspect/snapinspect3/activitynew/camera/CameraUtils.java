package com.snapinspect.snapinspect3.activitynew.camera;

import android.content.Context;
import android.graphics.ImageFormat;
import android.graphics.PointF;
import android.graphics.SurfaceTexture;
import android.hardware.camera2.CameraAccessException;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CameraManager;
import android.hardware.camera2.params.StreamConfigurationMap;
import android.os.Build;
import androidx.annotation.RequiresApi;
import android.util.Size;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

public class CameraUtils {

    private static final int DEFAULT_WIDTH = 640;
    private static final int DEFAULT_HEIGHT = 480;
//    private static final int MAX_PREVIEW_WIDTH = 1920;
//    private static final int MAX_PREVIEW_HEIGHT = 1080;


    private CameraUtils() {
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static boolean isSupported(
            CameraCharacteristics characteristics,
            CameraCharacteristics.Key<int[]> modes, Integer mode) {
        int[] ints = characteristics.get(modes);
        if (ints == null) return false;
        for (Integer value : ints) {
            if (value.equals(mode)) {
                return true;
            }
        }
        return false;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static boolean isContinuousAutoFocusSupported(CameraCharacteristics cameraCharacteristics) {
        return isSupported(cameraCharacteristics,
                CameraCharacteristics.CONTROL_AF_AVAILABLE_MODES,
                CameraCharacteristics.CONTROL_AF_MODE_CONTINUOUS_PICTURE);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static boolean isAutoExposureSupported(CameraCharacteristics cameraCharacteristics, int mode) {
        return isSupported(cameraCharacteristics, CameraCharacteristics.CONTROL_AE_AVAILABLE_MODES, mode);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static boolean isAutoWhiteBalanceSupported(CameraCharacteristics cameraCharacteristics) {
        return isSupported(cameraCharacteristics,
                CameraCharacteristics.CONTROL_AWB_AVAILABLE_MODES,
                CameraCharacteristics.CONTROL_AWB_MODE_AUTO);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static boolean isManualFocusSupported(CameraCharacteristics cameraCharacteristics) {
        return isSupported(cameraCharacteristics,
                CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES,
                CameraCharacteristics.REQUEST_AVAILABLE_CAPABILITIES_MANUAL_SENSOR);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static boolean isMeteringAreaAFSupported(CameraCharacteristics cameraCharacteristics) {
        Integer maxRegions = cameraCharacteristics.get(CameraCharacteristics.CONTROL_MAX_REGIONS_AF);
        return maxRegions != null && maxRegions > 0;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static boolean isMeteringAreaAESupported(CameraCharacteristics cameraCharacteristics) {
        Integer maxRegions = cameraCharacteristics.get(CameraCharacteristics.CONTROL_MAX_REGIONS_AE);
        return maxRegions != null && maxRegions > 0;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static Size chooseOptimalSize(CameraCharacteristics cameraCharacteristics, int textureViewWidth, int textureViewHeight, Size aspectRatio) {


//        if (_maxWidth > MAX_PREVIEW_WIDTH) {
//            _maxWidth = MAX_PREVIEW_WIDTH;
//        }
//
//        if (_maxHeight > MAX_PREVIEW_HEIGHT) {
//            _maxHeight = MAX_PREVIEW_HEIGHT;
//        }

        StreamConfigurationMap map = cameraCharacteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
        if (map == null) {
            return new Size(0, 0);
        }

        Size[] choices = map.getOutputSizes(SurfaceTexture.class);

        // Collect the supported resolutions that are at least as big as the preview Surface
        List<Size> bigEnough = new ArrayList<Size>();
        // Collect the supported resolutions that are smaller than the preview Surface
        List<Size> notBigEnough = new ArrayList<Size>();
        int w = aspectRatio.getWidth();
        int h = aspectRatio.getHeight();
        for (Size option : choices) {
            if (/*option.getWidth() <= _maxWidth &&
                    option.getHeight() <= _maxHeight &&*/
                    option.getHeight() == option.getWidth() * h / w) {
                if (option.getWidth() >= textureViewWidth && option.getHeight() >= textureViewHeight) {
                    bigEnough.add(option);
                } else {
                    notBigEnough.add(option);
                }
            }
        }

        if (bigEnough.size() > 0) {
            Collections.sort(bigEnough, new CompareSizesByArea());
            return bigEnough.get(0);
        }
        if (notBigEnough.size() > 0) {
            //Collections.sort(notBigEnough, new CompareSizesByArea());
            return notBigEnough.get(0);
        }
        return choices[0];
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static Size getCaptureSize(
            CameraCharacteristics cameraCharacteristics,
            CompareSizesByArea compareSizesByArea) {
        StreamConfigurationMap map = cameraCharacteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
        if (map == null) {
            return new Size(0, 0);
        }
        return Collections.max(Arrays.asList(map.getOutputSizes(ImageFormat.JPEG)), compareSizesByArea);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static Size getCaptureSize(
            CameraCharacteristics cameraCharacteristics,
            CompareSizesByArea compareSizesByArea, int width, int height) {
        StreamConfigurationMap map = cameraCharacteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
        if (map == null) {
            return new Size(0, 0);
        }
        Size[] choices = map.getOutputSizes(SurfaceTexture.class);

        // Collect the supported resolutions that are at least as big as the preview Surface
        List<Size> bigEnough = new ArrayList<Size>();
        // Collect the supported resolutions that are smaller than the preview Surface
        List<Size> notBigEnough = new ArrayList<Size>();
        int w = width;
        int h = height;
        for (Size option : choices) {
            if (/*option.getWidth() <= _maxWidth &&
                    option.getHeight() <= _maxHeight &&*/
                    option.getHeight() == option.getWidth() * h / w) {
                if (option.getWidth() >= width && option.getHeight() >= height) {
                    bigEnough.add(option);
                } else {
                    notBigEnough.add(option);
                }
            }
        }

        if (bigEnough.size() > 0) {
            return Collections.max(bigEnough, new CompareSizesByArea());
        }
        if (notBigEnough.size() > 0) {
            return Collections.max(notBigEnough, new CompareSizesByArea());
        }
        return choices[0];
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static Size getPreviewSize(CameraCharacteristics cameraCharacteristics, float aspectRatio) {
        StreamConfigurationMap map =
                cameraCharacteristics.get(CameraCharacteristics.SCALER_STREAM_CONFIGURATION_MAP);
        if (map == null) {
            return new Size(0, 0);
        }
        map.getOutputSizes(SurfaceTexture.class);
        return chooseOutputSize(Arrays.asList(map.getOutputSizes(SurfaceTexture.class)), aspectRatio);
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static Size chooseOutputSize(List<Size> sizes, float aspectRatio) {
        if (aspectRatio > 1.0f) {
            // land scape
            for (Size size : sizes) {
                if (sizes.get(0).getHeight() == sizes.get(0).getWidth() * aspectRatio/*9 / 16 && sizes.get(0).getHeight() < 1080*/) {
                    return size;
                }
            }
            return sizes.get(0);
        } else {
            // portrait or square
//            List<Size> potentials = new ArrayList<>();
            for (Size size : sizes) {
                if (((float) size.getHeight()) / ((float) size.getWidth()) == aspectRatio) {
                    return size;
//                    potentials.add(size);
                }
            }
//            for (Size potential : potentials) {
//                if (potential.getHeight() == 1080 || potential.getWidth() == 720) {
//                    return potential;
//                }
//            }
            return sizes.get(0);
        }
    }

    public static float lerp(float a, float b, float t) {
        return a + t * (b - a);
    }

    public static PointF normalizedSensorCoordsForNormalizedDisplayCoords(
            float nx, float ny, int sensorOrientation) {
        switch (sensorOrientation) {
            case 0:
                return new PointF(nx, ny);
            case 90:
                return new PointF(ny, 1.0f - nx);
            case 180:
                return new PointF(1.0f - nx, 1.0f - ny);
            case 270:
                return new PointF(1.0f - ny, nx);
            default:
                return null;
        }
    }

    /**
     * Clamps x to between min and max (inclusive on both ends, x = min --> min,
     * x = max --> max).
     */
    public static int clamp(int x, int min, int max) {
        if (x > max) {
            return max;
        }
        if (x < min) {
            return min;
        }
        return x;
    }

    /**
     * Clamps x to between min and max (inclusive on both ends, x = min --> min,
     * x = max --> max).
     */
    public static float clamp(float x, float min, float max) {
        if (x > max) {
            return max;
        }
        if (x < min) {
            return min;
        }
        return x;
    }

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    public static CameraCharacteristics getCameraCharacteristics(Context context, int desiredLensFacing) throws CameraAccessException {
        CameraManager cameraManager = (CameraManager) context.getSystemService(Context.CAMERA_SERVICE);
        for (String cameraID : cameraManager.getCameraIdList())
        {
            CameraCharacteristics cameraCharacteristics = cameraManager.getCameraCharacteristics(cameraID);
            Integer lensFacing = cameraCharacteristics.get(CameraCharacteristics.LENS_FACING);
            if (lensFacing != null && lensFacing == desiredLensFacing)
            {
                return cameraCharacteristics;
            }
        }
        throw new CameraAccessException(CameraAccessException.CAMERA_ERROR, "Could not find camera");
    }
}
