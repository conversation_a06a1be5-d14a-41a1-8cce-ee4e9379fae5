package com.snapinspect.snapinspect3.activitynew.camera;

import android.os.Build;
import androidx.annotation.RequiresApi;
import android.util.Size;

import java.util.Comparator;

public class CompareSizesByArea implements Comparator<Size> {

    @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
    @Override
    public int compare(Size lhs, Size rhs) {
        return Integer.signum((int) ((long) lhs.getWidth() * lhs.getHeight() - (long) rhs.getWidth() * rhs.getHeight()));
    }
}