package com.snapinspect.snapinspect3.activitynew.tasks

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.PullRefreshState
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.snapinspect.snapinspect3.Helper.CommonTask
import com.snapinspect.snapinspect3.Helper.Constants
import com.snapinspect.snapinspect3.IF_Object.ai_Task
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.SI_DB.db_Tasks
import com.snapinspect.snapinspect3.views.composables.*
import com.snapinspect.snapinspect3.views.tasks.TaskRowView
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.lang.ref.WeakReference
import java.util.*

class if_AssetTasks : ComponentActivity() {
    private lateinit var viewModel: AssetTasksViewModel

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setupActionBar()
        
        val assetId = intent.getIntExtra(Constants.Extras.iSAssetID, 0)
        val taskId = intent.getIntExtra(Constants.Extras.iSTaskID, 0)
        
        setContent {
            SnapTheme {
                viewModel = viewModel(
                    factory = AssetTasksViewModel.Factory(
                        applicationContext, assetId, taskId
                    )
                )
                AssetTasksScreen(
                    viewModel = viewModel,
                    onNavigate = ::handleNavigation
                )
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (::viewModel.isInitialized) {
            viewModel.loadTasksFromDB()
        }
    }

    private fun setupActionBar() {
        setTheme(R.style.RemoveShadowActionBar)
        actionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            show()
            title = ""
        }
    }

    private fun handleNavigation(action: AssetTasksAction) = when (action) {
            is AssetTasksAction.AddTask -> navigateToEditTask(
                assetId = action.assetId,
                pTaskId = action.pTaskId
            )

            is AssetTasksAction.ViewTaskDetail -> navigateToEditTask(
                assetId = action.assetId,
                taskId = action.taskId
            )
        }
    
    private fun navigateToEditTask(assetId: Int, taskId: Int? = null, pTaskId: Int? = null) {
        val intent = Intent(this, if_EditTask::class.java).apply {
            putExtra(Constants.Extras.iSAssetID, assetId)
            taskId?.let { putExtra(Constants.Extras.iSTaskID, it) }
            pTaskId?.let { putExtra(Constants.Extras.iPSTaskID, it) }
        }
        startActivity(intent)
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menuInflater.inflate(R.menu.menu_asset_tasks, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean = 
        when (item.itemId) {
            android.R.id.home -> {
                finish()
                true
            }
            R.id.action_filter_all -> {
                viewModel.onFilterTypeChange(TaskCompletionStatus.All)
                true
            }
            R.id.action_filter_inProgress -> {
                viewModel.onFilterTypeChange(TaskCompletionStatus.InProgress)
                true
            }
            R.id.action_filter_completed -> {
                viewModel.onFilterTypeChange(TaskCompletionStatus.Completed)
                true
            }
            else -> super.onOptionsItemSelected(item)
        }
}

sealed interface AssetTasksUiState {
    data object Loading : AssetTasksUiState
    data object Success : AssetTasksUiState
    data class Error(val message: String) : AssetTasksUiState
}

sealed interface AssetTasksAction {
    data class AddTask(val assetId: Int, val pTaskId: Int? = null) : AssetTasksAction
    data class ViewTaskDetail(val taskId: Int, val assetId: Int) : AssetTasksAction
}

enum class TaskCompletionStatus(val displayName: String) {
    All("Tasks"), 
    InProgress("In Progress"), 
    Completed("Completed")
}

val TaskCompletionStatus.label: String
    get() = when (this) {
        TaskCompletionStatus.All -> "All Tasks"
        TaskCompletionStatus.InProgress -> "In Progress"
        TaskCompletionStatus.Completed -> "Completed"
    }

class AssetTasksViewModel(context: Context, val assetId: Int, val taskId: Int) : ViewModel() {
    private val contextRef = WeakReference(context)
    
    private val _uiState = MutableStateFlow<AssetTasksUiState>(AssetTasksUiState.Loading)
    val uiState: StateFlow<AssetTasksUiState> = _uiState

    private val _searchQuery = MutableStateFlow("")
    val searchQuery: StateFlow<String> = _searchQuery

    private val _tasks = MutableStateFlow<List<ai_Task>>(emptyList())
    val tasks: StateFlow<List<ai_Task>> = _tasks

    private val _filterType = MutableStateFlow(TaskCompletionStatus.InProgress)
    val filterType: StateFlow<TaskCompletionStatus> = _filterType

    private val _isRefreshing = MutableStateFlow(false)
    val isRefreshing: StateFlow<Boolean> = _isRefreshing

    init {
        loadInitialData()
    }

    private fun loadInitialData() {
        loadTasksFromDB()
        loadTasks()
    }

    fun loadTasksFromDB() {
        _tasks.value = db_Tasks.getTasks(assetId, taskId, filterType.value)
    }

    private fun loadTasks() {
        viewModelScope.launch {
            _uiState.value = AssetTasksUiState.Loading
           refresh()
        }
    }

    fun onSearchQueryChange(query: String) {
        _searchQuery.value = query
    }

    fun onFilterTypeChange(type: TaskCompletionStatus) {
        _filterType.value = type
        loadTasksFromDB()
    }

    fun refresh() {
        viewModelScope.launch {
            _isRefreshing.value = true
            loadAssetTasks()
            _isRefreshing.value = false
        }
    }

    class Factory(
        private val context: Context,
        private val assetId: Int,
        private val taskId: Int
    ) : ViewModelProvider.Factory {
        override fun <T : ViewModel> create(modelClass: Class<T>): T {
            if (modelClass.isAssignableFrom(AssetTasksViewModel::class.java)) {
                @Suppress("UNCHECKED_CAST")
                return AssetTasksViewModel(context, assetId, taskId) as T
            }
            throw IllegalArgumentException("Unknown ViewModel class")
        }
    }

    private suspend fun loadAssetTasks() {
        contextRef.get()?.let { context ->
            try {
                withContext(Dispatchers.IO) {
                    CommonTask.loadAssetTasks(context, assetId)
                }
                loadTasksFromDB()
                _uiState.value = AssetTasksUiState.Success
            } catch (e: Exception) {
                _uiState.value = AssetTasksUiState.Error(e.message ?: "Failed to load tasks")
            }
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun AssetTasksScreen(
    viewModel: AssetTasksViewModel,
    onNavigate: (AssetTasksAction) -> Unit
) {
    val uiState by viewModel.uiState.collectAsState()
    val searchQuery by viewModel.searchQuery.collectAsState()
    val tasks by viewModel.tasks.collectAsState()
    val filterType by viewModel.filterType.collectAsState()
    val isRefreshing by viewModel.isRefreshing.collectAsState()

    val pullRefreshState = rememberPullRefreshState(
        refreshing = isRefreshing,
        onRefresh = { viewModel.refresh() }
    )

    AssetTasksContent(
        uiState = uiState,
        searchQuery = searchQuery,
        tasks = tasks,
        filterType = filterType,
        isRefreshing = isRefreshing,
        pullRefreshState = pullRefreshState,
        onSearchQueryChange = viewModel::onSearchQueryChange,
        onTaskClick = { task -> 
            onNavigate(AssetTasksAction.ViewTaskDetail(task.iSNotificationID, viewModel.assetId))
        },
        onAddTaskClick = { 
            onNavigate(AssetTasksAction.AddTask(viewModel.assetId, viewModel.taskId))
        }
    )
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun AssetTasksContent(
    uiState: AssetTasksUiState,
    searchQuery: String,
    tasks: List<ai_Task>,
    filterType: TaskCompletionStatus,
    isRefreshing: Boolean,
    pullRefreshState: PullRefreshState,
    onSearchQueryChange: (String) -> Unit,
    onTaskClick: (ai_Task) -> Unit,
    onAddTaskClick: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(colorResource(id = R.color.colorPrimary))
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = getActionBarSize())
                .background(Color.White)
                .pullRefresh(pullRefreshState)
        ) {
            Column(modifier = Modifier.fillMaxSize()) {
                if (tasks.isEmpty()) {
                    EmptyStateContent(uiState)
                } else {
                    TaskListContent(
                        searchQuery = searchQuery,
                        tasks = tasks,
                        isRefreshing = isRefreshing,
                        pullRefreshState = pullRefreshState,
                        onSearchQueryChange = onSearchQueryChange,
                        onTaskClick = onTaskClick
                    )
                }
            }
        }

        AddTaskButton(
            modifier = Modifier
                .align(Alignment.BottomEnd)
                .padding(dimens.normal),
            onClick = onAddTaskClick
        )

        TitleView(
            title = filterType.displayName,
            modifier = Modifier.align(Alignment.TopCenter)
        )
    }
}

@Composable
private fun EmptyStateContent(uiState: AssetTasksUiState) {
    when (uiState) {
        is AssetTasksUiState.Loading -> LoadingIndicator(
            size = 28.dp, 
            color = colorResource(id = R.color.gray_color_666)
        )
        is AssetTasksUiState.Success -> Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = "No tasks found",
                style = typography.h4,
                color = colors.color656565
            )
        }
        is AssetTasksUiState.Error -> { }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
private fun TaskListContent(
    searchQuery: String,
    tasks: List<ai_Task>,
    isRefreshing: Boolean,
    pullRefreshState: PullRefreshState,
    onSearchQueryChange: (String) -> Unit,
    onTaskClick: (ai_Task) -> Unit
) {
    SearchBar(
        value = searchQuery,
        onValueChange = onSearchQueryChange
    )

    Box {
        val filteredTasks = tasks.filter { it.matchSearch(searchQuery) }
        TaskList(
            tasks = filteredTasks,
            onTaskClick = onTaskClick
        )
        PullRefreshIndicator(
            refreshing = isRefreshing,
            state = pullRefreshState,
            modifier = Modifier.align(Alignment.TopCenter)
        )
    }
}

@Composable
fun TitleView(
    title: String,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier.height(getActionBarSize()),
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = title,
            color = colorResource(R.color.white_color),
            fontSize = dimensionResource(R.dimen.text_h4).value.sp,
            fontWeight = FontWeight.SemiBold,
        )
    }
}

@Composable
private fun TaskList(
    tasks: List<ai_Task>,
    onTaskClick: (ai_Task) -> Unit
) {
    LazyColumn {
        items(tasks) { task ->
            TaskRowView(
                task = task,
                onClick = { onTaskClick(task) }
            )
            Divider(
                color = colors.colorAAAAAA.copy(alpha = 0.5f),
                thickness = 0.5.dp,
                modifier = Modifier.padding(horizontal = dimens.extraNormal)
            )
        }
        // extra padding to avoid the last item being hidden
        item {
            Spacer(modifier = Modifier.height(dimens.xlarge))
        }
    }
}

@Composable
private fun AddTaskButton(
    modifier: Modifier = Modifier,
    onClick: () -> Unit
) {
    FloatingActionButton(
        onClick = onClick,
        modifier = modifier,
        backgroundColor = colorResource(id = R.color.colorPrimary)
    ) {
        Icon(
            Icons.Filled.Add,
            contentDescription = "Add Task",
            tint = Color.White
        )
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Preview(showBackground = true)
@Composable
private fun PreviewAssetTasksContent() {
    val isRefreshing = remember { mutableStateOf(false) }
    val pullRefreshState = rememberPullRefreshState(
        refreshing = isRefreshing.value,
        onRefresh = { }
    )

    val tasks = listOf(
        ai_Task().apply {
            iSNotificationID = 1
            sTitle = "Task 1"
            sDescription = "Description 1"
            sStatus = "In Progress"
            sStatusCode = "#FF0000"
            sCategory = "Category 1"
            dtDateDue = Date()
        },
        ai_Task().apply {
            iSNotificationID = 2
            sTitle = "Task 2"
            sDescription = "Description 2"
            sStatus = "Completed"
            sStatusCode = "#00FF00"
            sCategory = "Category 2"
            dtDateDue = Date()
        }
    )

    SnapTheme {
        AssetTasksContent(
            uiState = AssetTasksUiState.Success,
            searchQuery = "",
            tasks = tasks,
            filterType = TaskCompletionStatus.All,
            isRefreshing = isRefreshing.value,
            pullRefreshState = pullRefreshState,
            onSearchQueryChange = {},
            onTaskClick = {},
            onAddTaskClick = {}
        )
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Preview(showBackground = true)
@Composable
private fun PreviewAssetTasksEmptyContent() {
    val isRefreshing = remember { mutableStateOf(false) }
    val pullRefreshState = rememberPullRefreshState(
        refreshing = isRefreshing.value,
        onRefresh = { }
    )
    SnapTheme {
        AssetTasksContent(
            uiState = AssetTasksUiState.Success,
            searchQuery = "",
            tasks = emptyList(),
            filterType = TaskCompletionStatus.All,
            isRefreshing = isRefreshing.value,
            pullRefreshState = pullRefreshState,
            onSearchQueryChange = {},
            onTaskClick = {},
            onAddTaskClick = {}
        )
    }
}