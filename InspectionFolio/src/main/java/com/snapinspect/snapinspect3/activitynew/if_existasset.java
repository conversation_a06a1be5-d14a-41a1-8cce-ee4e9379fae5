package com.snapinspect.snapinspect3.activitynew;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.ColorStateList;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.os.*;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.*;
import android.view.animation.Animation;
import android.view.animation.TranslateAnimation;
import android.widget.*;
import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.orm.query.Condition;
import com.orm.query.Select;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_Asset;
import com.snapinspect.snapinspect3.SI_DB.db_Inspection;
import com.snapinspect.snapinspect3.activity.*;
import com.snapinspect.snapinspect3.activity.if_CameraX.CameraSaveOption;
import com.snapinspect.snapinspect3.activitynew.Comments.if_InspectionComments;
import com.snapinspect.snapinspect3.activitynew.Home.if_HomeTab;
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayFile;
import com.snapinspect.snapinspect3.activitynew.fragments.ItemSelectionDialog;
import com.snapinspect.snapinspect3.activitynew.fragments.InsTypeSelection;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Layout_3rd;
import com.snapinspect.snapinspect3.activitynew.tasks.if_AssetTasks;
import com.snapinspect.snapinspect3.app.App;
import com.snapinspect.snapinspect3.util.ArrayUtils;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.SystemUiHider;
import com.snapinspect.snapinspect3.views.CircularTextView;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.InputStream;
import java.lang.ref.WeakReference;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_existasset extends Activity implements SwipeRefreshLayout.OnRefreshListener, View.OnClickListener {
    private static final String PROMPT_MESSAGE = "Choose your property layout here and select the number of rooms that need inspecting. You can edit these items in the next screen.";
    private static final int CONFIG_NO_SCHEDUEL = 1;  //IF NOT, 0
    private static final int ASSET_PHOTO_RESPONSE = 2;
    private List<ai_Inspection> lsCurrentInspection;
    private List<ai_Inspection> lsCompletedInspection;
    private List<ai_Inspection> lsUploadedInspection;
    private List<ai_InsType> lsInsType;
    private ArrayList<ai_AssetSchedule> lsAssetSchedule;
    private int iSAssetID;
    private ai_Assets oAsset;
    private ExpandableListView listView;
    private final String[] mRegions = {
            "Schedule",
            "Inspection in Progress",
            "Completed (Ready to Upload)",
            "Uploaded Inspections"
        };
    BaseExpandableListAdapter adapter;
    MaterialDialog oDialog;
    private float fDensity;
    private ImageView ivAsset;
    private TextView tvAddress;
    private TextView tvAssignTo;
    private TextView tvLastInspection;
    private SwipeRefreshLayout swipeRefreshLayout;

    List<ai_JsonStatus> arrStatus = new ArrayList<>();
    private LinearLayout promptView, pullRefreshView;

    private ai_File oPropertyPhoto;

    private boolean hasFetchedScheduleData = false;
    private boolean isFetchingScheduleData = false;

    public static Intent newIntent(Context context, int iSAssetID) {
        Intent intent = new Intent(context, if_existasset.class);
        intent.putExtra(Constants.Extras.iSAssetID, iSAssetID);
        return intent;
    }

    //ATableView tableView;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        hasFetchedScheduleData = false;
        isFetchingScheduleData = false;
        lsAssetSchedule = new ArrayList<>();
        //ActionBar oBar = ;
        //ai_File oFile = new ai_File();
        //  oFile.iSObjectID = 0;
        // oFile.save();
        getActionBar().setDisplayHomeAsUpEnabled(true);
        // lsCompletedInspection = new List<ai_Inspection>();
        getActionBar().show();
        iSAssetID = getIntent().getIntExtra(Constants.Extras.iSAssetID, 0);
        setContentView(R.layout.activity_existasset);

        listView = findViewById(R.id.if_exist_asset_list_item);

        setupListView();

        LayoutInflater myinflater = getLayoutInflater();
        ViewGroup listHeader = (ViewGroup) myinflater.inflate(R.layout.header_exit_asset_listview, listView, false);

        listView.addHeaderView(listHeader, null, false);

        //Header View
        tvAddress = listHeader.findViewById(R.id.header_exist_asset_tv_address);
        tvAssignTo = listHeader.findViewById(R.id.header_exist_asset_tv_assigned);
        tvLastInspection = listHeader.findViewById(R.id.header_exist_asset_tv_last_inspection);

        ivAsset = listHeader.findViewById(R.id.header_exist_asset_iv_asset);
        Button btSchedule = listHeader.findViewById(R.id.header_exist_asset_listview_btn_schedule);
        if (CONFIG_NO_SCHEDUEL == 1) {
            btSchedule.setVisibility(View.GONE);
        } else {
            btSchedule.setVisibility(View.VISIBLE);
            btSchedule.setOnClickListener(this);
        }

        listHeader.findViewById(R.id.header_exist_asset_listview_btn_inspection).setOnClickListener(this);
        listHeader.findViewById(R.id.header_exist_asset_layer_asset).setOnClickListener(this);
        listHeader.findViewById(R.id.header_exist_asset_btn_map).setOnClickListener(this);
        pullRefreshView = listHeader.findViewById(R.id.header_exist_asset_layer_pull_refresh);  //pull to refresh view
        // pullRefreshView.setVisibility(View.GONE);
        fDensity = ((App) getApplication()).GetDeviceDensity();

        //Osama Add, Prompt
        promptView = findViewById(R.id.prompt_layout);
        TextView tvMessage = promptView.findViewById(R.id.view_prompt_tv_message);
        tvMessage.setText(PROMPT_MESSAGE);
        Button btnClose = promptView.findViewById(R.id.view_prompt_btn_close);
        if (btnClose != null) {
            btnClose.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    showPopUp(false);
                }
            });
        }
        oPropertyPhoto = CommonDB.GetPropertyPhoto(iSAssetID);
        swipeRefreshLayout = findViewById(R.id.if_exist_asset_refresh);
        if (oPropertyPhoto != null && oPropertyPhoto.getId() > 0) {
            swipeRefreshLayout.setOnRefreshListener(this);
        } else {
            swipeRefreshLayout.setEnabled(false);
        }
        arrStatus = CommonHelper.GetInspectionStatus(this);

        // request location permission
        CommonValidate.requestPermissions(this, Constants.LOCATION_PERMISSIONS);
    }

    private void setupListView() {
        adapter = new BaseExpandableListAdapter() {
            @Override
            public int getGroupCount() {
                return mRegions.length;
            }

            @Override
            public int getChildrenCount(int groupPosition) {
                if (groupPosition == 0) { //Schedule
                    return ArrayUtils.isEmpty(lsAssetSchedule) ? 1 : lsAssetSchedule.size();
                } else if (groupPosition == 1) { //Inspection in Progress
                    return ArrayUtils.isEmpty(lsCurrentInspection) ? 1 : lsCurrentInspection.size();
                } else if (groupPosition == 2) { //Completed (Ready to Upload)
                    return ArrayUtils.isEmpty(lsCompletedInspection) ? 1 : lsCompletedInspection.size();
                }

                return ArrayUtils.isEmpty(lsUploadedInspection) ? 1 : lsUploadedInspection.size();
            }

            @Override
            public Object getGroup(int groupPosition) {
                return mRegions[groupPosition];
            }

            @Override
            public Object getChild(int groupPosition, int childPosition) {
                return "";
            }

            @Override
            public long getGroupId(int groupPosition) {
                return groupPosition;
            }

            @Override
            public long getChildId(int groupPosition, int childPosition) {
                return childPosition;
            }

            @Override
            public boolean hasStableIds() {
                return true;
            }


            @Override
            public View getGroupView(int groupPosition, boolean isExpanded, View convertView, ViewGroup parent) {
                View view = LayoutInflater.from(getApplicationContext()).inflate(R.layout.list_exist_asset_layout, parent, false);
                TextView textView1 = view.findViewById(R.id.list_exist_asset_layout_title);
                ImageView ivExpand = view.findViewById(R.id.list_exist_asset_layout_expand_iv);
                if (isExpanded) {
                    if (groupPosition == 1 && !lsCurrentInspection.isEmpty()
                            || groupPosition == 2 && !lsCompletedInspection.isEmpty()
                            || groupPosition == 3 && !lsUploadedInspection.isEmpty()) { //Inspection in Progress
                        ivExpand.setImageResource(R.drawable.ic_expand);
                    }
                } else {
                    ivExpand.setImageResource(R.drawable.ic_collpase);
                }
                LinearLayout btnDownload = view.findViewById(R.id.list_exist_asset_layout_btn_download);
                if (groupPosition == 3) {
                    btnDownload.setVisibility(View.VISIBLE);
                } else {
                    btnDownload.setVisibility(View.GONE);
                }
                btnDownload.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // Log.e("osama", "download");
                        refreshTableView();
                    }
                });

                textView1.setText(mRegions[groupPosition]);
                return view;
            }

            @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN)
            @SuppressLint("SetTextI18n")
            @Override
            public View getChildView(int groupPosition, int childPosition, boolean isLastChild, View convertView, ViewGroup parent) {
                View view = LayoutInflater.from(getApplicationContext()).inflate(R.layout.list_exist_asset_child_layout, parent, false);
                TextView tvTitle = view.findViewById(R.id.list_exist_asset_child_layout_title);
                TextView tvDetails = view.findViewById(R.id.list_exist_asset_child_layout_details);
                ImageView ivIndicator = view.findViewById(R.id.list_exist_asset_child_layout_expand_iv);
                LinearLayout viewIndicator = view.findViewById(R.id.view_inspection_status);
                LinearLayout mainLayer = view.findViewById(R.id.layer_asset);
                TextView tvNoInspection = view.findViewById(R.id.tv_no_inspection);

                ivIndicator.setImageResource(R.drawable.collpase);

                if (groupPosition == 0) {
                    if (lsAssetSchedule.isEmpty()) {
                        mainLayer.setVisibility(View.GONE);
                        tvNoInspection.setVisibility(View.VISIBLE);
                        tvNoInspection.setText(isFetchingScheduleData ? "Loading..." : "No Asset Schedules.");
                    } else {
                        GradientDrawable gd = new GradientDrawable();
                        int color = getResources().getColor(R.color.red_color);
                        gd.setColor(color);
                        gd.setCornerRadius(CommonHelper.pxFromDp(if_existasset.this, 3));


                        viewIndicator.setBackground(gd);
                        mainLayer.setVisibility(View.VISIBLE);
                        tvNoInspection.setVisibility(View.GONE);

                        ai_AssetSchedule schedule = lsAssetSchedule.get(childPosition);
                        tvTitle.setText(schedule.sInspectorName);

                        String sDate = DateUtils.cleanDateIfTimeIsZero(
                                schedule.dtSchedule, "MMM dd, yyyy HH:mm", "MMM dd, yyyy");
                        tvDetails.setText(schedule.sInsTitle + " " + sDate);
                    }
                } else if (groupPosition == 1) {
                    if (ArrayUtils.isEmpty(lsCurrentInspection)) {

                        mainLayer.setVisibility(View.GONE);
                        tvNoInspection.setVisibility(View.VISIBLE);
                        tvNoInspection.setText("No Inspection in Progress");

                    } else {
                        GradientDrawable gd = new GradientDrawable();
                        int color = getResources().getColor(R.color.red_color);
                        gd.setColor(color);
                        gd.setCornerRadius(CommonHelper.pxFromDp(if_existasset.this, 3));


                        viewIndicator.setBackground(gd);
                        mainLayer.setVisibility(View.VISIBLE);
                        tvNoInspection.setVisibility(View.GONE);

                        ai_Inspection oIns = lsCurrentInspection.get(childPosition);
                        if (oIns.bComplete) {
                            ivIndicator.setImageResource(R.drawable.check);
                        } else {
                            ivIndicator.setImageResource(R.drawable.collpase);
                        }
                        tvTitle.setText(oIns.sTitle);
                        tvDetails.setText(oIns.sInsTitle + " @ " + ((oIns.dtEndDate == null || oIns.dtEndDate.equals("")) ? oIns.dtStartDate : oIns.dtEndDate));
                    }
                } else if (groupPosition == 2) {
                    if (ArrayUtils.isEmpty(lsCompletedInspection)) {
                        mainLayer.setVisibility(View.GONE);
                        tvNoInspection.setVisibility(View.VISIBLE);
                        tvNoInspection.setText("No Completed Inspections");
                    } else {
                        GradientDrawable gd = new GradientDrawable();
                        int color = getResources().getColor(R.color.blue_color);
                        gd.setColor(color);
                        gd.setCornerRadius(CommonHelper.pxFromDp(if_existasset.this, 3));


                        viewIndicator.setBackground(gd);
                        mainLayer.setVisibility(View.VISIBLE);
                        tvNoInspection.setVisibility(View.GONE);

                        final ai_Inspection oIns = lsCompletedInspection.get(childPosition);
                        view.findViewById(R.id.list_exist_asset_child_layout_btn_expand).setVisibility(View.GONE);
                        view.findViewById(R.id.list_exist_asset_child_layout_btn_expand_upload).setVisibility(View.VISIBLE);
                        view.findViewById(R.id.list_exist_asset_child_layout_btn_expand_upload).setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View view) {
                                UploadIndividualInspection(oIns.getId().intValue());
                            }
                        });


                        final long iTempID = lsCompletedInspection.get(childPosition).getId();
                        tvTitle.setOnClickListener(new View.OnClickListener() {
                            @Override
                            public void onClick(View v) {
                                CommonInspection.gotoInspection(if_existasset.this, (int) iTempID);
                            }
                        });
                        tvTitle.setText(oIns.sTitle);
                        tvDetails.setText(oIns.sInsTitle + " @ " + (oIns.dtEndDate == null || oIns.dtEndDate.equals("") ? oIns.dtStartDate : oIns.dtEndDate));
                    }
                } else if (groupPosition == 3) {
                    if (ArrayUtils.isEmpty(lsUploadedInspection)) {
                        mainLayer.setVisibility(View.GONE);
                        tvNoInspection.setVisibility(View.VISIBLE);
                        tvNoInspection.setText("No Inspection in Progress");
                    } else {
                        GradientDrawable gd = new GradientDrawable();
                        int color = getResources().getColor(R.color.green_color);
                        gd.setColor(color);
                        gd.setCornerRadius(CommonHelper.pxFromDp(if_existasset.this, 3));


                        viewIndicator.setBackground(gd);
                        mainLayer.setVisibility(View.VISIBLE);
                        tvNoInspection.setVisibility(View.GONE);

                        ai_Inspection oIns = lsUploadedInspection.get(childPosition);
                        tvTitle.setText(oIns.sTitle);
                        String sStatus = CommonJson.GetJsonKeyValue("Status", oIns.sCustomOne);
                        if (sStatus != null && sStatus.trim().length() > 0) {
                            ivIndicator.setVisibility(View.INVISIBLE);
                            sStatus = sStatus.trim();
                            String sStatusCode = CommonJson.GetJsonKeyValue("S_Code", oIns.sCustomOne);
                            String sColor = (sStatusCode == null || sStatusCode.trim().length() == 0) ? "#CCCCCC" : sStatusCode;
                            CircularTextView oTextView = view.findViewById(R.id.list_exist_asset_child_layout_expand_iv_textview);
                            oTextView.setVisibility(View.VISIBLE);
                            oTextView.setText(CommonHelper.GetStatusCode(sStatus));


                            oTextView.setSolidColor(sColor.trim());
                            oTextView.setTextColor(Color.WHITE);


                            /*oTextView.setStrokeColor(sColor.trim());
                            oTextView.setSolidColor("#FFFFFF");
                            try {

                                int iTempColor = Color.parseColor(sColor.trim());
                                oTextView.setTextColor(iTempColor);
                            }catch(Exception eee){
                                oTextView.setTextColor(Color.parseColor("#CCCCCC"));
                            }*/
                            // oTextView.setStrokeWidth(2);
                        }


                        //  ivIndicator.setImageResource();
                        // ivIndicator.setImageResource(R.drawable.check);
                        tvDetails.setText(oIns.sInsTitle + " @ " + (oIns.dtEndDate == null || oIns.dtEndDate.equals("") ? oIns.dtStartDate : oIns.dtEndDate));
                    }
                }
                return view;
            }

            @Override
            public boolean isChildSelectable(int groupPosition, int childPosition) {
                return true;
            }
        };

        listView.setOnChildClickListener(new ExpandableListView.OnChildClickListener() {
            @Override
            public boolean onChildClick(ExpandableListView parent, View v, int groupPosition, int childPosition, long id) {
                //get the group header
                if (groupPosition == 0 && !lsAssetSchedule.isEmpty()) {
                    CommonSchedule.viewSchedule(if_existasset.this, lsAssetSchedule.get(childPosition));
                } else if (groupPosition == 1 && lsCurrentInspection.size() > 0) {
                    long lInspectionID = lsCurrentInspection.get(childPosition).getId();
                    int iInspectionID = (int) lInspectionID;
                    CommonInspection.gotoInspection(if_existasset.this, iInspectionID);
                } else if (groupPosition == 2 && lsCompletedInspection.size() > 0) {

                    long lInspectionID = lsCompletedInspection.get(childPosition).getId();
                    int iInspectionID = (int) lInspectionID;
                    CommonInspection.gotoInspection(if_existasset.this, iInspectionID);

                } else if (groupPosition == 3 && lsUploadedInspection.size() > 0) {
                    long lInspectionID = lsUploadedInspection.get(childPosition).iSInsID;
                    final int iSInsID = (int) lInspectionID;
                    final int iInspectionID = lsUploadedInspection.get(childPosition).getId().intValue();
                    final ai_Inspection oIns = lsUploadedInspection.get(childPosition);
                    ArrayList<String> lsOptions = new ArrayList<String>();
                    lsOptions.add("Download PDF");
                    lsOptions.add("Send PDF");
                    if (CommonUI.bEnableWord(if_existasset.this)){
                        lsOptions.add("Send Word");
                    }

                    if (CommonValidate.bInspectionReviewEnabled(oIns)) {
                        lsOptions.add("Review");
                        if (!CommonJson.disableEditInspection(if_existasset.this)) {
                            if (arrStatus != null && !arrStatus.isEmpty()) {
                                lsOptions.add("Update Status");
                            }
                        }
                    } else {
                        if (!CommonJson.disableNewInspection(if_existasset.this) 
                            && !CommonJson.disableCopyInspection(if_existasset.this)) {
                            lsOptions.add("Copy Inspection");
                        }
                        if (!CommonJson.disableEditInspection(if_existasset.this)) {
                            lsOptions.add("Edit Inspection");
                            if (arrStatus != null && !arrStatus.isEmpty()) {
                                lsOptions.add("Update Status");
                            }
                        }
                    }
                    lsOptions.add("Comments");

                    new MaterialDialog.Builder(if_existasset.this)
                            .title(R.string.alert_title_action)
                            .items(lsOptions)
                            .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                                @Override
                                public boolean onSelection(MaterialDialog dialog, View view, int which, CharSequence text) {
                                    if (text.toString().equalsIgnoreCase("Download PDF")){
                                        CommonHelper.DownloadReportRequest(if_existasset.this, iSInsID, "/IOAPI/GetReport", "");
                                    }
                                    else if (text.toString().equalsIgnoreCase("Send PDF")){
                                        GetReportRequest(iSInsID, "/IOAPI/GetReportEmail", "", oIns.iSAssetID);
                                    }
                                    else if (text.toString().equalsIgnoreCase("Send Word")){
                                        GetReportRequest(iSInsID, "/IOAPI/GetReportEmail", "D", oIns.iSAssetID);
                                    }
                                    else if (text.toString().equalsIgnoreCase("Review")){
                                        CommonInspection.editInspection(if_existasset.this, iSInsID);
                                    }
                                    else if (text.toString().equalsIgnoreCase("Edit Inspection")){
                                        CommonInspection.editInspection(if_existasset.this, iSInsID);
                                    }
                                    else if (text.toString().equalsIgnoreCase("Update Status")) {
                                        CommonInspection.updateInspectionStatus(
                                            if_existasset.this, oIns.iSInsID, () -> {
                                                reloadInspectionsFromDB();
                                                ReloadInspectionDataTable();
                                            }
                                        );
                                    }
                                    else if (text.toString().equalsIgnoreCase("Comments")){
                                        goToComments(iSInsID);
                                    }
                                    else if (text.toString().equalsIgnoreCase("Copy Inspection")) {
                                        dialog.dismiss();

                                        new MaterialDialog.Builder(if_existasset.this)
                                                .title("Copy Inspection")
                                                .items(new String[]{"Copy Inspection Data Only", "Copy Inspection (With Photos)"})
                                                .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                                                    @Override
                                                    public boolean onSelection(MaterialDialog dialog, View view, int i, CharSequence text) {
                                                        if (text.toString().equalsIgnoreCase("Copy Inspection Data Only")) {
                                                            CopyInspectionAlert(iSInsID, oIns, false);

                                                        } else if (text.toString().equalsIgnoreCase("Copy Inspection (With Photos)")) {
                                                            CopyInspectionAlert(iSInsID, oIns, true);
                                                        }

                                                        return true;
                                                    }
                                                })
                                                .negativeText(R.string.md_cancel_label)
                                                .show();


                                    }

                                    return true;
                                }
                            })
                            .negativeText(R.string.md_cancel_label)
                            .show();

                }
                return false;
            }
        });
        listView.setOnGroupClickListener(new ExpandableListView.OnGroupClickListener() {
            @Override
            public boolean onGroupClick(ExpandableListView parent, View v, int groupPosition, long id) {
                if (parent.isGroupExpanded(groupPosition)) {
                    parent.collapseGroup(groupPosition);
                } else {
                    boolean animateExpansion = false;
                    parent.expandGroup(groupPosition, animateExpansion);
                    if (groupPosition == 0 && !hasFetchedScheduleData) { // schedules
                        fetchAssetSchedules();
                    }
                }
                //telling the listView we have handled the group click, and don't want the default actions.
                return true;
            }
        });
        listView.setAdapter(adapter);
    }

    private void fetchAssetSchedules() {
        if (!NetworkUtils.isNetworkAvailable(this)) {
            lsAssetSchedule = new ArrayList<>();
            adapter.notifyDataSetChanged();
            return;
        }
        isFetchingScheduleData = true;
        CommonRequest.requestURL(this, "/IOAPI/GetAssetSchedule", () -> {
            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
            oParams.add("iAssetID", "" + oAsset.iSAssetID);
            return oParams;
        }, (response, error) -> {
            isFetchingScheduleData = false;
            if (response != null) {
                hasFetchedScheduleData = true;
                try {
                    JSONArray schedules = response.getJSONArray("lsSchedule");
                    if (schedules.length() > 0) {
                        ArrayList<ai_AssetSchedule> assetSchedules = new ArrayList<>();
                        for (int i = 0; i < schedules.length(); i++) {
                            JSONObject obj = schedules.getJSONObject(i);
                            ai_AssetSchedule assetSchedule = new ai_AssetSchedule(obj);
                            assetSchedules.add(assetSchedule);
                        }
                        lsAssetSchedule = assetSchedules;
                    }
                } catch (JSONException e) {
                    lsAssetSchedule = new ArrayList<>();
                    ai_BugHandler.ai_Handler_Exception(e);
                }
            } else {
                lsAssetSchedule = new ArrayList<>();
            }
            adapter.notifyDataSetChanged();
        });
    }

    //Osama Add, show/hide prompt view
    private void showPopUp(boolean showed) {
        if (promptView != null) {
            promptView.setVisibility(showed ? View.VISIBLE : View.GONE);
            SlideToDown(showed);
        }

    }

    public int dpToPx(int dp) {
        DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
        return Math.round(dp * (displayMetrics.xdpi / DisplayMetrics.DENSITY_DEFAULT));
    }

    public void SlideToDown(boolean showed) {
        // Log.e("osama", showed ? " true " : " false ");
        int startYOff = showed ? -dpToPx(400) : 0;
        int endYOff = showed ? 0 : -dpToPx(400);
        Animation slide = new TranslateAnimation(0, 0, startYOff, endYOff);
        slide.setDuration(400);
        slide.setFillAfter(true);
        slide.setFillEnabled(true);
        promptView.startAnimation(slide);


        slide.setAnimationListener(new Animation.AnimationListener() {

            @Override
            public void onAnimationStart(Animation animation) {

            }

            @Override
            public void onAnimationRepeat(Animation animation) {
            }

            @Override
            public void onAnimationEnd(Animation animation) {

                promptView.clearAnimation();
                LinearLayout.LayoutParams lp = new LinearLayout.LayoutParams(
                        promptView.getWidth(), promptView.getHeight());
                lp.setMargins(0, dpToPx(0), 0, 0);
                promptView.setLayoutParams(lp);
            }

        });
    }

    private void DisplayPropertyPhoto() {
        oPropertyPhoto = CommonDB.GetPropertyPhoto(oAsset.iSAssetID);
        //Osama Add, We can photo path from ai_asset SFieldOne.
        //String photoPath = oAsset.sFieldOne;
        try {
            if (oPropertyPhoto == null || oPropertyPhoto.sFile == null) {
                findViewById(R.id.header_exist_asset_layer_pull_refresh).setVisibility(View.GONE);
            } else if (oPropertyPhoto.sFile != null) {
                if (CommonHelper.bFileExist(oPropertyPhoto.sFile)) {
                    Bitmap bitmap = BitmapFactory.decodeFile(oPropertyPhoto.sFile);
                    if (bitmap != null) {
                        ivAsset.setImageBitmap(bitmap);
                        ivAsset.setOnClickListener(v -> {
                            startActivity(if_DisplayFile.newIntent(if_existasset.this, oPropertyPhoto.getId()));
                        });
                    }
                    findViewById(R.id.header_exist_asset_layer_pull_refresh).setVisibility(View.GONE);
                } else {
                    findViewById(R.id.header_exist_asset_layer_pull_refresh).setVisibility(View.VISIBLE);
                }
            }
        } catch (Exception eee) {

        }
    }

    @Override
    public void onResume() {

        try {
            super.onResume();
          //  CommonHelper.trackEvent(this, "Android Exist Assets", null);
            GeoLocationManager.getInstance(this).startUpdateLocation();

            View newInspectionContainer = findViewById(R.id.btn_start_inspection_container);
            newInspectionContainer.setVisibility(CommonJson.disableNewInspection(this) ? View.GONE : View.VISIBLE);

            reloadInspectionsFromDB();
          /*  List<ai_Assets> lsAssets = ai_Assets.find(ai_Assets.class, "I_S_ASSET_ID = ?", "" + iSAssetID);
            if (lsAssets != null && lsAssets.size() == 1) {
                oAsset = lsAssets.get(0);
                DisplayPropertyPhoto();

            }*/
            oAsset = db_Asset.GetAssetBySAssetID(iSAssetID);
            if (oAsset != null) {
                DisplayPropertyPhoto();
            } else {
                CommonUI.showToast(this, "Asset can not be found. Please sync the mobile app. ");
            }
            getActionBar().setTitle(oAsset.sAddressOne);

            tvAddress.setText(oAsset.sAddressOne + " " + oAsset.sAddressTwo);
            ai_User oCustomer = CommonDB.GetUser(oAsset.iCustomerID);
            if (oCustomer != null && oCustomer.iCustomerID > 0) {
                tvAssignTo.setText(String.format("Manager: %s", oCustomer.sName));
            } else {
                tvAssignTo.setText(String.format("Manager: %s", "Unknown"));
            }
            String sLastInspection = CommonJson.GetJsonKeyValue("SUBLBL", oAsset.sFieldOne);
            String sTitleID = CommonJson.GetJsonKeyValue("SUBT", oAsset.sFieldOne);
            if (sLastInspection != null && sLastInspection.length() > 0) {
                //sTitleID
                if (sTitleID == null || sTitleID.equalsIgnoreCase("")) {
                    tvLastInspection.setText(String.format("Last Inspection: %s", sLastInspection));
                } else if (sTitleID.equals("0")) {
                    tvLastInspection.setText(sLastInspection);
                }
            } else {
                if (sTitleID == null || sTitleID.equalsIgnoreCase("")) {
                    tvLastInspection.setText("Not inspected previously");
                } else if (sTitleID.equals("0")) {
                    tvLastInspection.setText("");
                }


                // tvLastInspection.setText("Last Inspection: Not inspected previously");
            }
            if (CommonDB.ValidateChildExist(if_existasset.this, iSAssetID) > 0 || oAsset.bPush) {
                LinearLayout llApartments = findViewById(R.id.header_exist_asset_layer_go_apartment);
                if (oAsset.iSPAssetID > 0) {
                    for (int i = 0; i < llApartments.getChildCount(); i++) {
                        if (llApartments.getChildAt(0) instanceof TextView) {
                            ((TextView) llApartments.getChildAt(0)).setText("Rooms");
                        }
                    }
                }


                llApartments.setVisibility(View.VISIBLE);
                llApartments.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        Intent oIntent = new Intent(if_existasset.this, if_Asset_2nd.class);
                        // oIntent.putExtra("lClientObjectID", item.lClientObjectID);
                        oIntent.putExtra(Constants.Extras.iSPAssetID, oAsset.iSAssetID);
                        oIntent.putExtra("sTitle", oAsset.sAddressOne);
                        startActivity(oIntent);
                    }
                });
            }

            LinearLayout llTasks = findViewById(R.id.header_exist_asset_layer_go_tasks);
            llTasks.setVisibility(CommonJson.bEnableTasks(this) ? View.VISIBLE : View.GONE);
            llTasks.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    Intent oIntent = new Intent(if_existasset.this, if_AssetTasks.class);
                    oIntent.putExtra(Constants.Extras.iSAssetID, oAsset.iSAssetID);
                    startActivity(oIntent);
                }
            });

            //Osama Add, Show Prompt
            showPopUp(false);

            CommonDB.InsertLog(if_existasset.this, "Event", "Exist Asset View " + oAsset.sAddressOne);

            ReloadInspectionDataTable();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.onResume", ex, this);
        }
    }

    private void reloadInspectionsFromDB() {
        lsCurrentInspection = db_Inspection.GetInspectionBySAssetID(iSAssetID, false, false, false);
        lsCompletedInspection = db_Inspection.GetInspectionBySAssetID(iSAssetID, true, false, false);
        lsUploadedInspection = db_Inspection.GetInspectionBySAssetID(iSAssetID, true, true, false);
    }

    @Override
    protected void onPause() {
        super.onPause();
        GeoLocationManager.getInstance(this).stopUpdateLocation();
    }

    private void goToComments(int iSInsID) {
        Intent intent = new Intent(this, if_InspectionComments.class);
        //intent.putExtra(Constants.Extras.iInspectionID, iInspectionID);
        intent.putExtra("iSInsID", iSInsID);
        this.startActivity(intent);
    }

    public void UploadIndividualInspection(int iInsID) {
        CommonValidate.Permission_Validate(if_existasset.this);

        if (!CommonValidate.validateInspectionsCompulsoryItems(this, iInsID)) return;

        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LOCKED);
        //new ProgressDialog(if_existasset.this, android.R.style.Theme_Material_Dialog_Alert);
        oDialog = CommonUI.ShowMaterialProgressDialog(if_existasset.this, "Message", "Running. Please wait ...");
//        oDialog = CommonUI.GetProgressDialog(if_existasset.this, "Message", "Running. Please wait ...", true);

        Intent oIntent = new Intent(this, UploadService.class);
        oIntent.putExtra("receiver", new UploadReceiver(new Handler()));
        oIntent.putExtra(Constants.Extras.iInsID, iInsID);
        startService(oIntent);
    }

    @SuppressLint("ParcelCreator")
    private class UploadReceiver extends ResultReceiver {
        public UploadReceiver(Handler handler) {
            super(handler);
        }

        @Override
        protected void onReceiveResult(int resultCode, Bundle resultData) {
            try {
                super.onReceiveResult(resultCode, resultData);
                if (resultCode == UploadService.UPDATE_PROGRESS) {
                    if (resultData.getString("Title").equalsIgnoreCase("")) {

                    } else {
                        oDialog.setTitle("Running. Please wait... - " + resultData.getString("Title"));
                    }
                    oDialog.setContent(resultData.getString("Message"));
//                    oDialog.setMessage(resultData.getString("Message"));
                } else if (resultCode == UploadService.UPDATE_SUCCESS) {
                    if (oDialog != null) {
                        oDialog.dismiss();

//                        AlertDialog.Builder oAlert = CommonUI.GetAlertBuilder(resultData.getString("Title"),
//                                resultData.getString("Message"), if_existasset.this, false, false);
//                        oAlert.setPositiveButton("OK", new DialogInterface.OnClickListener() {
//                            @Override
//                            public void onClick(DialogInterface dialogInterface, int i) {
//                                dialogInterface.dismiss();
//                                lsCompletedInspection = db_Inspection.GetInspectionBySAssetID(iSAssetID, true, false, false);
//                                lsUploadedInspection = db_Inspection.GetInspectionBySAssetID(iSAssetID, true, true, false);
//                                adapter.notifyDataSetChanged();
//                                ReloadInspectionDataTable();
//                            }
//                        });
//                        oAlert.show();

                        new MaterialDialog.Builder(if_existasset.this)
                                .title(resultData.getString("Title"))
                                .content(resultData.getString("Message"))
                                .positiveText(R.string.tv_ok)
                                .onPositive(new MaterialDialog.SingleButtonCallback() {
                                    @Override
                                    public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                        reloadInspectionsFromDB();
                                        ReloadInspectionDataTable();
                                    }
                                })
                                .show();
                    }
                } else if (resultCode == UploadService.UPDATE_SUCCESSNODATA) {
                    if (oDialog != null) {
                        oDialog.dismiss();
//                        AlertDialog.Builder oAlert = CommonUI.GetAlertBuilder(resultData.getString("Title"),
//                                resultData.getString("Message"), if_existasset.this, false, false);
//                        oAlert.setPositiveButton("OK", new DialogInterface.OnClickListener() {
//                            @Override
//                            public void onClick(DialogInterface dialogInterface, int i) {
//                                dialogInterface.dismiss();
//
//                                lsCompletedInspection = db_Inspection.GetInspectionBySAssetID(iSAssetID, true, false, false);
//                                lsUploadedInspection = db_Inspection.GetInspectionBySAssetID(iSAssetID, true, true, false);
//                                adapter.notifyDataSetChanged();
//                                ReloadInspectionDataTable();
//
//                            }
//                        });
//                        oAlert.show();

                        new MaterialDialog.Builder(if_existasset.this)
                                .title(resultData.getString("Title"))
                                .content(resultData.getString("Message"))
                                .positiveText(R.string.tv_ok)
                                .onPositive(new MaterialDialog.SingleButtonCallback() {
                                    @Override
                                    public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                        reloadInspectionsFromDB();
                                        ReloadInspectionDataTable();
                                    }
                                })
                                .show();
                    }
                } else if (resultCode == UploadService.UPDATE_FAIL) {
                    if (oDialog != null) {
                        oDialog.dismiss();
//                        AlertDialog.Builder oAlert = CommonUI.GetAlertBuilder(resultData.getString("Title"), resultData.getString("Message")
//                                , if_existasset.this, false, false);
//                        oAlert.setPositiveButton("OK", new DialogInterface.OnClickListener() {
//                            @Override
//                            public void onClick(DialogInterface dialogInterface, int i) {
//                                dialogInterface.dismiss();
//                                //Intent intent = getIntent();
//                                //intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
//                                //finish();
//                                //startActivity(intent);
//                                //setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_SENSOR);
//                            }
//                        });
//                        oAlert.show();

                        new MaterialDialog.Builder(if_existasset.this)
                                .title(resultData.getString("Title"))
                                .content(resultData.getString("Message"))
                                .positiveText(R.string.tv_ok)
                                .onPositive(new MaterialDialog.SingleButtonCallback() {
                                    @Override
                                    public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                        dialog.dismiss();
                                    }
                                })
                                .show();
                    }
                }
            } catch (Exception ex) {

                ai_BugHandler.ai_Handler_Exception("Exception", "if_home.Upload.onReceiveResult", ex, getApplicationContext());

            }
        }
    }

    private void ReloadInspectionDataTable() {
        for (int i = 0; i < adapter.getGroupCount(); i++) {
            if (i == 0 && !hasFetchedScheduleData) {
                listView.collapseGroup(i);
            } else {
                listView.expandGroup(i);
            }
        }
        adapter.notifyDataSetChanged();
        // ((FrameLayout)findViewById(R.id.framelayout_existasset)).scrollTo(0, 0);
        // listView.setSelectedChild(0, 0, true);
    }

    private void ConstructInsType2() {
        if (!CommonValidate.validateNewInspection(this)) return;
        
        if (ArrayUtils.isEmpty(lsInsType))
            lsInsType = CommonDB_Inspection.GetInsType_CustomPermission(this);

        if (!ArrayUtils.isEmpty(lsInsType)) {
            List<InsTypeSelection> lsInsTypeSelection = new ArrayList<>();
            for (ai_InsType oInsType : lsInsType) {
                lsInsTypeSelection.add(InsTypeSelection.fromInsType(oInsType));
            }

            new ItemSelectionDialog<>(this, getString(R.string.title_choose_inspection_type), lsInsTypeSelection, () -> {
                return SharedConfig.getInstance(this).getRecentInsTypes();
            }, selection -> {
                ai_InsType aiInsType = db_Inspection.GetInsTypeBySInsTypeID(selection.getId());
                if (aiInsType == null) {
                    CommonUI.showToast(this, "Inspection type not found");
                    return null;
                }
                SharedConfig.getInstance(this).saveInsType(selection.getId());
                final ai_Inspection oInsTemp = CommonDB_Inspection.GetInspectionByAssetInsType(iSAssetID, aiInsType.iSInsTypeID);
                if (oInsTemp != null && oInsTemp.iSAssetID > 0) {
                    final int iInsID_Temp = (oInsTemp.getId()).intValue();
                    new MaterialDialog.Builder(if_existasset.this)
                            .title("Message")
                            .content("You have an incompleted " + oInsTemp.sInsTitle + ". Choose 'Continue' to work on existing inspection, or 'New' to start new inspection.")
                            .positiveText("Continue")
                            .neutralText("New")
                            .negativeText("Cancel")
                            .onPositive(new MaterialDialog.SingleButtonCallback() {
                                @Override
                                public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                    CommonInspection.gotoInspection(if_existasset.this, iInsID_Temp);
                                }
                            })
                            .onNeutral(new MaterialDialog.SingleButtonCallback() {
                                @Override
                                public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                    checkSchedulesBeforeNewInspection(aiInsType);
                                }
                            })
                            .onNegative(new MaterialDialog.SingleButtonCallback() {
                                @Override
                                public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                    dialog.dismiss();
                                }
                            })
                            .show();
                } else {
                    checkSchedulesBeforeNewInspection(aiInsType);
                }

                return null;
            }).show();
        } else {
            CommonUI.ShowAlert(this, "Error", "Inspection type can not be found. Please sync the mobile app. ");
        }
    }

    private void SelectInsType() {
        try {
            if (!CommonUI.bAppPermission(this)) return;

            if (CommonDB.ValidateChildExist(if_existasset.this, iSAssetID) > 0) {
                String[] lsOption = new String[2];
                if (oAsset.iSPAssetID > 0) {
                    lsOption = new String[]{"Inspect the Asset", "View Rooms"};
                } else {
                    lsOption = new String[]{"Inspect the Asset", "View Apartments"};
                }

//                AlertDialog.Builder oBuilder = CommonUI.GetAlertBuilder("Action", "", if_existasset.this, true, false);
//
//                oBuilder.setItems(lsOption, new DialogInterface.OnClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialogInterface, int i) {
//                        if (i == 0) {
//                            ConstructInsType();
//                        } else {
//                            Intent oIntent = new Intent(if_existasset.this, if_Asset_2nd.class);
//                            // oIntent.putExtra("lClientObjectID", item.lClientObjectID);
//                            oIntent.putExtra(Constants.Extras.iSPAssetID, oAsset.iSAssetID);
//                            oIntent.putExtra("sTitle", oAsset.sAddressOne);
//                            startActivity(oIntent);
//                        }
//                    }
//                });
//
//                oBuilder.show();

                new MaterialDialog.Builder(if_existasset.this)
                        .title(R.string.alert_title_action)
                        .items(lsOption)
                        .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                            @Override
                            public boolean onSelection(MaterialDialog dialog, View view, int i, CharSequence text) {
                                if (i == 0) {
                                    ConstructInsType2();
                                } else {
                                    Intent oIntent = new Intent(if_existasset.this, if_Asset_2nd.class);
                                    // oIntent.putExtra("lClientObjectID", item.lClientObjectID);
                                    oIntent.putExtra(Constants.Extras.iSPAssetID, oAsset.iSAssetID);
                                    oIntent.putExtra("sTitle", oAsset.sAddressOne);
                                    startActivity(oIntent);
                                }

                                return true;
                            }
                        })
                        .negativeText(R.string.md_cancel_label)
                        .show();
            } else {
                ConstructInsType2();
            }

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.SelectInsType", ex, this);
        }
    }

    @Override
    public void onRefresh() {
        if (NetworkUtils.isNetworkAvailable(if_existasset.this)) {
            if (!CommonUI.bAppPermission(if_existasset.this)) {
                return;
            }
            swipeRefreshLayout.setRefreshing(true);
            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
            oParams.add("iSAssetID", String.valueOf(oPropertyPhoto.iSObjectID));
            IF_RestClient.post("/IOAPI/DownloadAssetPhoto", oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, JSONObject response) {
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            if (response.getBoolean("success")) {
                                final String downloadURL = response.getString("sDownloadURL");
                                //final String sFilePath = CommonHelper.GetFileSaveFileName();
                                new Handler().post(() -> {
                                    try {
                                        new LoadImage(if_existasset.this).execute(downloadURL);
                                    } catch (Exception e) {
                                        ai_BugHandler.ai_Handler_Exception("Per", "if_signup.IF_RestClient.Runnable", e);
                                    }
                                });
                            } else {
                                new Handler().post(() -> swipeRefreshLayout.setRefreshing(false));
                            }
                            return;
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.DownloadPropertyPhoto", ex, null);
                        }

                    }
                    new Handler().post(() -> swipeRefreshLayout.setRefreshing(false));
                }

                @Override
                public void onFailure(int statusCode, org.apache.http.Header[] headers, Throwable e, JSONObject errorResponse) {
                    // oDialog.dismiss();
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            swipeRefreshLayout.setRefreshing(false);
                        }
                    });
                }
            });
        } else {
            Toast.makeText(this, "Please connect to internet to download Asset Photo", Toast.LENGTH_LONG).show();
            new Handler().post(new Runnable() {
                @Override
                public void run() {
                    swipeRefreshLayout.setRefreshing(false);
                }
            });
        }


    }

    private static class LoadImage extends AsyncTask<String, String, Bitmap> {
        //ProgressDialog oDialog;

        private final WeakReference<if_existasset> weakReference;
        LoadImage(if_existasset activity) {
            weakReference = new WeakReference<>(activity);
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();

            //  oDialog = ProgressDialog.show(oContext, "Connecting to Server", "Downloading Data...");
        }

        protected Bitmap doInBackground(String... args) {
            Bitmap bitmap = null;
            try {
                bitmap = BitmapFactory.decodeStream((InputStream) new URL(args[0]).getContent());

            } catch (Exception e) {
                e.printStackTrace();
            }
            return bitmap;
        }

        protected void onPostExecute(Bitmap bitmap) {
            //oDialog.dismiss();
            if_existasset activity = weakReference.get();
            if (activity == null) return;

            if (bitmap != null) {
                // O_FileName oFileName = CommonHelper.GetFileName();
                final String sFilePath = CommonHelper.GetFileSaveFileName();
                CommonHelper.SaveImage(sFilePath, bitmap);
                activity.oPropertyPhoto.sFile = sFilePath;
                activity.oPropertyPhoto.save();
                //  selectedPhoto.sFile = oFileName.sFilePath;
                //  selectedPhoto.sThumb = oFileName.sThumbNail;
                //  int iPhotoID = UpdatePhoto(selectedPhoto);

                bitmap.recycle();
                activity.DisplayPropertyPhoto();
                activity.swipeRefreshLayout.setRefreshing(false);
                //  selectedPhoto = null;

                //   Intent oIntent = new Intent(oContext, if_photoshow.class);
                //   oIntent.putExtra(Constants.Extras.iPhotoID, iPhotoID);
                //  oContext.startActivity(oIntent);
            }
        }
    }

    @Override
    public void onClick(View view) {
        switch (view.getId()) {
            case R.id.header_exist_asset_btn_map:
                if (NetworkUtils.isNetworkAvailable(if_existasset.this)) {
                    Intent oIntent = new Intent(if_existasset.this, if_InspectionMap.class);
                    oIntent.putExtra(Constants.Extras.iSPAssetID, oAsset.iSAssetID);
                    startActivity(oIntent);
                } else {
                    ShowAlert("Error", "Please connect to internet to view map!");
                }
                break;
            case R.id.header_exist_asset_layer_asset:
                startActivity(if_AssetDetails.newIntent(this, oAsset.iSAssetID));
                break;
            case R.id.header_exist_asset_listview_btn_inspection:
                SelectInsType();
                break;
            case R.id.header_exist_asset_listview_btn_schedule:
                break;
            default:
                throw new IllegalArgumentException();
        }
    }

    class AsyncLoadInspectionResult {
        public int iInspetionID = 0;
        public int iSAssetID = 0;
        public int iSInsTypeID = 0;
        public String sAddress1 = "";
        public String sAddress2 = "";
        public int iSScheduleID = 0;

        public AsyncLoadInspectionResult() {

        }
    }

    class AsyncLoadInspection extends AsyncTask<ai_InsType, Void, AsyncLoadInspectionResult> {
        // ProgressDialog pd;
        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            oDialog = CommonUI.ShowMaterialProgressDialog(if_existasset.this, "Message", "Processing");
//            oDialog = CommonUI.GetProgressDialog(if_existasset.this, "Message", "Processing", true);

        }

        @Override
        protected AsyncLoadInspectionResult doInBackground(ai_InsType... params) {
            AsyncLoadInspectionResult oResult = new AsyncLoadInspectionResult();
            try {
                List<ai_AssetLayout> lsAssetLayout = CommonDB.GetAssetLayouts(iSAssetID, params[0].sPTC, if_existasset.this);
                if (lsAssetLayout == null || lsAssetLayout.isEmpty()) {
                    List<ai_Layout> lsLayoutItem = Select.from(ai_Layout.class)
                            .where(Condition.prop("S_PTC").eq(params[0].sPTC), Condition.prop("I_SP_LAYOUT_ID").eq("" + 0))
                            .list();
                    boolean bPass = true;
                    for (ai_Layout aiLayout : lsLayoutItem) {
                        String sConfig = params[0].sType.equals("F") ? aiLayout.sFConfig : aiLayout.sSConfig;
                        if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null) {
                            bPass = false;
                            break;
                        }
                    }
                    if (bPass) {
                        IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                        Date oNow = new Date();
                        oResult.iInspetionID = oCreateInspection.CreateInspectionFromAssetLayout(
                            if_existasset.this, new ArrayList<>(), params[0], oAsset.iSAssetID, "",
                            CommonHelper.sDateToString(oNow), "", oAsset.sAddressOne, oAsset.sAddressTwo, 0, "");
                    } else {
                        oResult.iSAssetID = oAsset.iSAssetID;
                        oResult.iSInsTypeID = params[0].iSInsTypeID;
                        oResult.sAddress1 = oAsset.sAddressOne;
                        oResult.sAddress2 = oAsset.sAddressTwo;
                        oResult.iSScheduleID = 0;
                    }
                } else {
                    IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                    oResult.iInspetionID = oCreateInspection.CreateInspectionFromAssetLayout(
                        if_existasset.this, new ArrayList<>(lsAssetLayout), params[0], oAsset.iSAssetID, "",
                        CommonHelper.sDateToString(null), "", oAsset.sAddressOne, oAsset.sAddressTwo, 0, "");
                }
                return oResult;
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.SelectedInsType", ex, if_existasset.this);
            }
            return oResult;
        }

        @Override
        protected void onPostExecute(AsyncLoadInspectionResult oResult) {
            super.onPostExecute(oResult);
            CommonUI.DismissMaterialProgressDialog(oDialog);

            if (oResult.iInspetionID > 0) {
                CommonInspection.gotoInspection(if_existasset.this, oResult.iInspetionID);
            } else if (oResult.iInspetionID == 0 && oResult.iSInsTypeID > 0) {
                startActivity(if_Layout_3rd.newIntent(if_existasset.this, oAsset.iSAssetID, oResult.iSInsTypeID,
                        oAsset.sAddressOne, oAsset.sAddressTwo));
            }

        }
    }

    private void checkSchedulesBeforeNewInspection(ai_InsType oInsType) {
        try {
            List<v_Schedule> lsSchedules = CommonDB_Schedule.getSchedulesByAssetID(iSAssetID, oInsType.iSInsTypeID);
            if (!lsSchedules.isEmpty()) {
                showAlertForExistingSchedules(oInsType);
            } else {
                SelectedInsType(oInsType);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.checkSchedulesBeforeNewInspection", 
                ex, this);
            SelectedInsType(oInsType); // Proceed with inspection even if schedule check fails
        }
    }
    
    private void showAlertForExistingSchedules(final ai_InsType oInsType) {
        new MaterialDialog.Builder(if_existasset.this)
                .title("Message")
                .content("Oops! It looks like this inspection has already been scheduled. Please go to the 'Schedules' section to begin. If this is a new inspection, you can ignore this message.")
                .negativeText("Ignore & Continue")
                .positiveText("Go to schedules")
                .onNegative(new MaterialDialog.SingleButtonCallback() {
                    @Override
                    public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                        SelectedInsType(oInsType);
                    }
                })
                .onPositive(new MaterialDialog.SingleButtonCallback() {
                    @Override
                    public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                        switchToSchedulesTab();
                    }
                })
                .show();
    }
    
    // close and navigate to schedules tab
    private void switchToSchedulesTab() {
        Intent intent = new Intent(Constants.Broadcasts.sHomeTabSwitched);
        intent.putExtra(Constants.Extras.iTabIndex, if_HomeTab.FRAGMENT_TAB.SCHEDULES.ordinal());
        LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
        finish();
    }
    
    private void SelectedInsType(ai_InsType oInsType) {
        CommonInspection.fetchAssetLayoutsIfNeed(this, oAsset.iSAssetID, oInsType.iSInsTypeID, assetLayouts -> {
            if (assetLayouts == null || assetLayouts.isEmpty()) {
                new AsyncLoadInspection().execute(oInsType);
            } else { // have layouts
                new if_Layout_3rd.AsyncStartInspection(
                        this, oAsset.iSAssetID, assetLayouts, oInsType, null, 0,
                        oAsset.sAddressOne, oAsset.sAddressTwo, 0, "").execute();
            }
        });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_asset_exist, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_camera) {
            if (!CommonValidate.validateGPSLocationPermission(this)) return true;
            Intent oIntent = if_CameraX.newIntent(
                this, 0, 0, false, iSAssetID, CameraSaveOption.FILE, Constants.Limits.iSinglePhotoLimit);
            startActivityForResult(oIntent, ASSET_PHOTO_RESPONSE);
        } else {
            super.onBackPressed();
        }
        return true;
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent result) {
        if (requestCode == ASSET_PHOTO_RESPONSE && resultCode == Constants.ResultCodes.CAMERA_RESPONSE) {
            String content = result.getStringExtra(Constants.Extras.CAMERA_RESPONSE);
            if (content != null) {
                try {
                    long lClientFileID = result.getLongExtra(Constants.Extras.CLIENT_FILE_ID, 0);
                    CommonDB.MarkFileAttachCMD(lClientFileID);
                    CommonDB.MarkFileAssetPhoto(lClientFileID, iSAssetID);
                    Bitmap bitmap = BitmapFactory.decodeFile(content);
                    // if (oAsset != null) {
                    //     oAsset.sFieldOne = content;
                    //      oAsset.save();
                    //  }
                    if (bitmap != null) {
                        ivAsset.setImageBitmap(bitmap);
                        ivAsset.setOnClickListener(v -> {
                            startActivity(if_DisplayFile.newIntent(if_existasset.this, oPropertyPhoto.getId()));
                        });
                    }

                    new UploadAssetPhotoTask(this, lClientFileID).execute();
                } catch (Exception ex) {
                    ai_BugHandler.ai_Handler_Exception(ex);
                }
            }
        }
    }

    private void duplicateInspection(int iInspectionID, final ai_Inspection oIns, final ai_InsType oInsType, final Boolean bMediaTransfer) {
        if (NetworkUtils.isNetworkAvailable(if_existasset.this)) {
            try {
                //oDialog = ProgressDialog.show(if_existasset.this, "Connect to server", "Download Data...");

                oDialog = CommonUI.ShowMaterialProgressDialog(if_existasset.this, "Message", "Downloading data ...");
//                oDialog = CommonUI.GetProgressDialog(if_existasset.this, "Message", "Downloading data ...", true);

                RequestParams oParams = new RequestParams();

                oParams.add("iCustomerID", CommonHelper.GetPreferenceString(if_existasset.this, "iCustomerID"));
                oParams.add("sToken", CommonHelper.GetPreferenceString(if_existasset.this, "sToken"));
                oParams.add("iInspectionID", String.valueOf(iInspectionID));
                oParams.add("bMediaTransfer", String.valueOf(bMediaTransfer));
                try {
                    oParams.add("iCopyToInsTypeID", String.valueOf(oInsType.iSInsTypeID));
                    oParams.add("sLocalDate", CommonHelper.sDateToStringFormat(null, "yyyy-MM-dd HH:mm"));

                }catch(Exception eee){}

                final String sURL = "/IOAPI/MirrorInspectionAPI";
                IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                    @Override
                    public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                        oDialog.dismiss();
                        if (statusCode == 200) {
                            try {
                                if (response.optBoolean("success", false)) {
                                    try {
                                        int iTempID = CommonInspection.CopyInspection(response, bMediaTransfer, oInsType, if_existasset.this);
                                        if (iTempID > 0) {
                                            CommonInspection.gotoInspection(if_existasset.this, iTempID);
                                        }
                                    } catch (Exception ex) {
                                        ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.duplicateInspection.4", ex, if_existasset.this);
                                    }
                                } else {
                                    String sMessage = response.optString("message", "The inspection can not be copied.");
                                    if (!StringUtils.isEmpty(sMessage)) {
                                        new Handler(Looper.getMainLooper()).post(new Runnable() {
                                            @Override
                                            public void run() {
                                                oDialog.dismiss();
                                                ShowAlert("Error", sMessage);
                                            }
                                        });
                                    } else {
                                        new Handler(Looper.getMainLooper()).post(new Runnable() {
                                            @Override
                                            public void run() {
                                                oDialog.dismiss();
                                                ShowAlert("Error", "Copy inspection failed. <NAME_EMAIL>");
                                            }
                                        });

                                    }
                                }
                            } catch (Exception exx) {
                                ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.duplicateInspection", exx, if_existasset.this);
                            }

                        }
                    }

                    @Override
                    public void onFailure(int statusCode, org.apache.http.Header[] headers, java.lang.Throwable e, org.json.JSONObject errorResponse) {

                        new Handler().post(new Runnable() {
                            @Override
                            public void run() {
                                oDialog.dismiss();
                                ShowAlert("Error", "No server response. <NAME_EMAIL>");

                            }
                        });
                    }
                });
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "if_exist.Upload.onReceiveResult", ex, getApplicationContext());
            }
        } else {
            ShowAlert("Error", "Please connect to internet to copy inspection!");
        }
    }

    private void CopyInspectionAlert(final int iInspectionID, final ai_Inspection oIns, final Boolean bMediaTransfer) {
        if (!CommonUI.bAppPermission(if_existasset.this)) {
            ShowAlert("Message", "Please enable permission.");
            return;
        }

        if (!CommonValidate.validateNewInspection(this)) return;

        try {
            final ArrayList<ai_InsType> menuItemTypes = new ArrayList<>();
            List<ai_InsType> lsInsTypes = CommonDB_Inspection.GetInsType_CustomPermission(this);
            for (ai_InsType aInsType : lsInsTypes) {
                if (aInsType.sPTC.equals(oIns.sPTC)
                        && oIns.sType.equals(aInsType.sType)
                        && !menuItemTypes.contains(aInsType)
                        && aInsType.sInsTitle != null) {
                    menuItemTypes.add(aInsType);
                }
            }
            if (menuItemTypes.isEmpty()) return;

            List<InsTypeSelection> lsInsTypeSelection = new ArrayList<>();
            for (ai_InsType oInsType : menuItemTypes) {
                lsInsTypeSelection.add(InsTypeSelection.fromInsType(oInsType));
            }

            new ItemSelectionDialog<>(this, getString(R.string.title_choose_inspection_type), lsInsTypeSelection, () -> {
                return SharedConfig.getInstance(this).getRecentInsTypes();
            }, selection -> {
                ai_InsType aiInsType = db_Inspection.GetInsTypeBySInsTypeID(selection.getId());
                duplicateInspection(iInspectionID, oIns, aiInsType, bMediaTransfer);
                SharedConfig.getInstance(this).saveInsType(selection.getId());
                return null;
            }).show();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.Copy Inspection", ex, getApplicationContext());
        }

    }

    private void goToServerEmailAcitivty(String title, String subject, String body, int iInsID) {
        Intent intent = new Intent(this, if_serveremail.class);
        intent.putExtra(Constants.Extras.mailto, title);
        intent.putExtra(Constants.Extras.subject, subject);
//        intent.putExtra(Constants.Extras.body, Html.fromHtml(body));
        intent.putExtra(Constants.Extras.iInsID, iInsID);
        intent.putExtra(Constants.Extras.body, body);

        startActivity(intent);
    }
    private void GetReportRequestAction(int iInspectionID, final String sURL, String sType, final String sRecipient) {
        oDialog = CommonUI.ShowMaterialProgressDialog(if_existasset.this, "Message", "Connecting to Server");
//        oDialog = CommonUI.GetProgressDialog(if_existasset.this, "Message", "Connecting to Server", true);

        RequestParams oParams = new RequestParams();
        oParams.add("iCustomerID", CommonHelper.GetPreferenceString(if_existasset.this, "iCustomerID"));
        oParams.add("sToken", CommonHelper.GetPreferenceString(if_existasset.this, "sToken"));
        oParams.add("iInsID", "" + iInspectionID);
        if (sType.equalsIgnoreCase("D")) {
            oParams.add("sType", sType);
        }
        final int iInsID = iInspectionID;
        IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
            @Override
            public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                oDialog.dismiss();
                if (statusCode == 200) {
                    //Push to next Activity
                    try {
                        if (response.getBoolean("success")) {
                            if (sURL.contains("Email")) {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        try {
                                            boolean bDisplayInst = CommonHelper.GetPreferenceBoolean(if_existasset.this, "bLocalEmailClient");


                                            if (!bDisplayInst) {
                                                goToServerEmailAcitivty(sRecipient == null ? "" : sRecipient, response.getString("sSubject"), response.getString("sMessageBody"), iInsID);
                                            } else {
                                                Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
                                                if (sRecipient != null && sRecipient.length() > 0) {
                                                    emailIntent.setData(Uri.parse("mailto:" + sRecipient));
                                                }
                                                emailIntent.putExtra(Intent.EXTRA_SUBJECT, response.getString("sSubject"));
                                                emailIntent.putExtra(Intent.EXTRA_TEXT, StringUtils.convertHtml(response.getString("sMessageBody")));

                                                try {
                                                    startActivity(Intent.createChooser(emailIntent, "Send email using..."));
                                                } catch (android.content.ActivityNotFoundException ex) {
                                                    ex.printStackTrace();
                                                }
                                            }
                                        } catch (Exception ex) {
                                            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.GetReportRequest.SendEmail", ex, null);
                                        }
                                    }
                                });


                            } else {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        try {
                                            startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(response.getString("sURL"))));
                                        } catch (Exception ex) {
                                            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.GetReportRequest.ViewReport", ex, null);
                                        }
                                    }
                                });
                            }

                            return;
                        } else {
                            new Handler().post(new Runnable() {
                                @Override
                                public void run() {
                                    ShowAlert("Failed", "Please try again.");
                                }
                            });

                            return;
                        }
                    } catch (Exception ex) {
                        // ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest", ex);
                        ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.GetReportRequest", ex, null);
                    }

                }
                new Handler().post(new Runnable() {
                    @Override
                    public void run() {
                        ShowAlert("Error! Failed Connection", "Please try again later.");
                    }
                });
            }
        });

    }

    private void GetReportRequest(final int iInspectionID, final String sURL, final String sType, int iSAssetID) {
        try {
            List<ai_Contact> lsContact = Select.from(ai_Contact.class).where(Condition.prop("i_S_Asset_ID").eq(iSAssetID)).list();
            if (ArrayUtils.isEmpty(lsContact)) {
                GetReportRequestAction(iInspectionID, sURL, sType, "");
            } else {
                int iCount = 0;
                for (int i = 0; i < lsContact.size(); i++) {
                    if (lsContact.get(i).sEmail != null && lsContact.get(i).sEmail.trim().length() > 0) {
                        iCount ++ ;
                    }
                }

                final String[] lsItems = new String[iCount];
                for (int i = 0; i < lsContact.size(); i++) {
                    if (lsContact.get(i).sEmail != null && lsContact.get(i).sEmail.trim().length() > 0) {
                        lsItems[i] = lsContact.get(i).sEmail + " (" + lsContact.get(i).sTag + ")";
                    }
                }

                final List<String> selectedItems = new ArrayList<String>();

//                AlertDialog.Builder builder =
//                        new AlertDialog.Builder(if_existasset.this, R.style.DialogTheme);
//
//
//
//                builder.setMultiChoiceItems(lsItems, null, new DialogInterface.OnMultiChoiceClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialog, int which, boolean isChecked) {
//                        if (isChecked) {
//                            String sSelect = lsItems[which].toString();
//                            selectedItems.add(sSelect.substring(0, sSelect.indexOf("(")).trim());
//                        }
//                        else{
//                            String sSelect = lsItems[which].toString();
//                            selectedItems.remove(sSelect.substring(0, sSelect.indexOf("(")).trim());
//                        }
//                    }
//                }).setPositiveButton("Next", new DialogInterface.OnClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//
//                        String sResult = String.join(";", selectedItems);
//                        GetReportRequestAction(iInspectionID, sURL, sType, sResult);
//                    }
//                }).setNegativeButton("Cancel",
//                        new DialogInterface.OnClickListener() {
//
//                            public void onClick(DialogInterface dialog, int which) {
//                                dialog.dismiss();
//                            }
//                        }
//                );
//
//                builder.show();

                new MaterialDialog.Builder(this)
                        .title("Send Report")
                        .content("Please select Recipients to send")
                        .items(lsItems)
                        .itemsCallbackMultiChoice(null, new MaterialDialog.ListCallbackMultiChoice() {
                            @Override
                            public boolean onSelection(MaterialDialog dialog, Integer[] which, CharSequence[] text) {
                                /**
                                 * If you use alwaysCallMultiChoiceCallback(), which is discussed below,
                                 * returning false here won't allow the newly selected check box to actually be selected.
                                 * See the limited multi choice dialog example in the sample project for details.
                                 **/
                                Log.e("osama", "which" + which + " -- " + text);

                                for (int i : which) {
                                    String sSelect = lsItems[i];
                                    selectedItems.add(sSelect.substring(0, sSelect.indexOf("(")).trim());
                                }

                                String sResult = String.join(";", selectedItems);
                                GetReportRequestAction(iInspectionID, sURL, sType, sResult);
                                return true;
                            }
                        })
                        .widgetColor(Color.GRAY)
                        .choiceWidgetColor(ColorStateList.valueOf(Color.GRAY))
                        .positiveText("Next")
                        .onPositive(new MaterialDialog.SingleButtonCallback() {
                            @Override
                            public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                Log.e("osama", "Next Clicked" + which);
                            }
                        })
                        .negativeText(R.string.md_cancel_label)
                        .show();
            }




    }catch(Exception ex)

    {
        ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.GetReportRequest", ex, this);
    }

}
    private void ShowAlert(String sTitle, String sMessage){
        CommonUI.ShowAlert(if_existasset.this, sTitle, sMessage);
//        AlertDialog.Builder builder = CommonUI.GetAlertBuilder(sTitle, sMessage, if_existasset.this, false, true);
//
//        builder.show();
    }

    public void refreshTableView()
    {
        try {
            oDialog = CommonUI.ShowMaterialProgressDialog(if_existasset.this, "Message", "Downloading Data ...");
//            oDialog =  CommonUI.GetProgressDialog(if_existasset.this, "Message", "Downloading Data ...", true);

            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(if_existasset.this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(if_existasset.this, "sToken"));
            oParams.add("iObjectID", String.valueOf(oAsset.iSAssetID));
            final String sURL = "/IOAPI/SearchInspectionAPI";

            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    oDialog.dismiss();
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            if (response.getBoolean("success")) {
                                try {
                                    final JSONArray oItems = response.getJSONArray("lsInspection");

                                    new Handler().post(new Runnable() {
                                        @Override
                                        public void run() {
                                            try {
                                                db_Inspection.DeleteAllAssetInspection(iSAssetID);
                                                //List<ai_InsType> lsInsTypes = db_Inspection.GetAllInsType();
                                                //List<ai_Inspection> lsInspection = ai_Inspection.find()
                                                ArrayList<Integer> arrList = CommonDB_Inspection.GetInsType_CustomPermission_IntArray(if_existasset.this);
                                                for (int i = 0; i < oItems.length(); i++) {
                                                    JSONObject oOb = oItems.getJSONObject(i);
                                                    try{
                                                        int iInsTypeID = oOb.getInt("iInsTypeID");
                                                        if (arrList.contains(iInsTypeID)){
                                                            continue;
                                                        }
                                                    }catch(Exception eec1){

                                                    }

                                                    CommonInspection.ReconsolidateInspectionWithServer(oOb);
                                                }
                                                lsUploadedInspection = db_Inspection.GetInspectionBySAssetID(iSAssetID, true, true, false);
                                                adapter.notifyDataSetChanged();
                                                ReloadInspectionDataTable();
                                                //tableView.reloadData();
                                            } catch (Exception e) {
                                                ai_BugHandler.ai_Handler_Exception("Per", "if_signup.IF_RestClient.Runnable", e);
                                            }
                                        }
                                    });
                                } catch (Exception ex) {
                                    ai_BugHandler.ai_Handler_Exception("Exception", "if_EditContact.UpdateContact.Save", ex, if_existasset.this);
                                }
                            } else if (!response.getBoolean("success")) {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        oDialog.dismiss();
                                        try {
                                            ShowAlert("Error", response.getString("message"));
                                        } catch (Exception ex) {
                                            ShowAlert("Error", "Asset Response Error. <NAME_EMAIL>.");
                                        }
                                    }
                                });

                            } else {

                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        oDialog.dismiss();
                                        ShowAlert("Error", "Please connect to internet to download server inspections.");
                                    }
                                });

                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditContact.UpdateContact", ex, if_existasset.this);
                        }

                    }
                }

                @Override
                public void onFailure(int statusCode, org.apache.http.Header[] headers, java.lang.Throwable e, org.json.JSONObject errorResponse) {

                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            oDialog.dismiss();
                            ShowAlert("Error", "Please connect to internet to download server inspections.");

                        }
                    });
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_exist.Upload.onReceiveResult", ex, getApplicationContext());
        }
    }

    // Async Task to upload asset photo
    public static class UploadAssetPhotoTask extends AsyncTask<Void, Void, Boolean> {
        private final WeakReference<Activity> activityWeakReference;
        private final long lClientFileID;
        private MaterialDialog oDialog;

        UploadAssetPhotoTask(Activity activity, long lClientFileID) {
            activityWeakReference = new WeakReference<>(activity);
            this.lClientFileID = lClientFileID;
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            oDialog = CommonUI.ShowMaterialProgressDialog(activityWeakReference.get(), "Message", "Uploading ...");
        }

        @Override
        protected Boolean doInBackground(Void... voids) {
            ai_File oFile = ai_File.findById(ai_File.class, lClientFileID);
            Context context = activityWeakReference.get();
            AtomicBoolean isUploadSuccess = new AtomicBoolean(false);
            CommonRequest.uploadFile(context, oFile, (response, error) -> {
                if (error == null) {
                    HashMap<String, String> lsParams = new HashMap<>();
                    lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(context, "iCustomerID"));
                    lsParams.put("sToken", CommonHelper.GetPreferenceString(context, "sToken"));
                    lsParams.put("iSFileID", "" + oFile.iFileID);
                    JSONObject oJson = IF_SyncClient.PostRequest("/IOAPI/AttachAssetPhoto", lsParams);
                    try {
                        if (oJson != null && !oJson.getBoolean("success")) {
                            oJson = IF_SyncClient.PostRequest("/IOAPI/AttachAssetPhoto", lsParams);
                            if (oJson == null || !oJson.getBoolean("success")) {
                                isUploadSuccess.set(false);
                                return;
                            }
                        }
                        oFile.sCustomOne = CommonJson.RemoveJsonKey("AttachCMD", oFile.sCustomOne);
                        oFile.save();
                        isUploadSuccess.set(true);
                    } catch (Exception ex) {
                        ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.updateAssetPhoto", ex, context);
                    }
                } else {
                    isUploadSuccess.set(false);
                }
            });
            return isUploadSuccess.get();
        }

        @Override
        protected void onPostExecute(Boolean success) {
            super.onPostExecute(success);
            Activity activity = activityWeakReference.get();
            CommonUI.DismissMaterialProgressDialog(activity, oDialog);
            if (!success) {
                CommonUI.ShowAlert(activity, "Error", "Upload unsuccessful, please try again.");
            }
        }
    }
}
