package com.snapinspect.snapinspect3.activitynew.Edit;

import android.app.Activity;
import android.app.DatePickerDialog;
import android.os.Bundle;
import android.text.InputType;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.ListView;
import androidx.annotation.Nullable;
import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.Adapter.FormItemsAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.O_ContactType;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Contact;
import com.snapinspect.snapinspect3.IF_Object.if_FormItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_Asset;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.*;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.ViewType.*;
import static com.snapinspect.snapinspect3.util.DateUtils.DATE_RANGE_FORMAT;

public class if_EditContact_2nd extends Activity implements FormItemsAdapter.Delegate {

    private List<if_FormItem> mDataSource;
    private ListView mListView;
    private int iSAssetID, iSContactID;
    private ai_Contact mContact;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_update_contact_2nd);

        try {
            getActionBar().setTitle(getNavTitle());
            getActionBar().setDisplayHomeAsUpEnabled(true);

            View bottomContainer = findViewById(R.id.btn_bottom_container);
            if (!NetworkUtils.isNetworkAvailable(this)) {
                bottomContainer.setVisibility(View.GONE);
                CommonUI.ShowAlert(this, "Error",
                        "To create or update contact information, please make sure you are connected to Internet.",
                        R.string.action_cancel, this::finish);
                return;
            }

            iSContactID = getIntent().getIntExtra(Constants.Extras.iSContactID, 0);
            iSAssetID = getIntent().getIntExtra(Constants.Extras.iSAssetID, 0);

            mContact = db_Asset.GetContactByID(iSContactID);
            if (mContact == null) mContact = new ai_Contact();

            mListView = findViewById(R.id.lv_formItems);
            mListView.setAdapter(new FormItemsAdapter(this, getDataSource(), this));

            bottomContainer.setVisibility(View.VISIBLE);
            Button btnBottom = findViewById(R.id.btn_bottom);
            btnBottom.setText(getBottomTitle());
            btnBottom.setOnClickListener(v -> requestSubmitFormData());

            O_ContactType contactType = mContact.getContactType(this);
            if (contactType != null) selectContactType(contactType);

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (iSContactID > 0) {
            getMenuInflater().inflate(R.menu.menu_delete_contact, menu);
        }
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_delete_contact) {
            CommonUI.ShowAlert(
                this,
                getString(R.string.alert_title_message),
                getString(R.string.alert_delete_contact_message),
                R.string.alert_action_yes,
                R.string.alert_action_no,
                this::requestDeleteContact
            );
        } else {
            onBackPressed();
        }
        return true;
    }

    @Override
    public void onBackPressed() {
        CommonHelper.hideSoftKeyboard(this);
        super.onBackPressed();
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    private String getNavTitle() {
        int titleRes;
        if (iSContactID > 0) {
            titleRes = R.string.edit_contact;
        } else {
            titleRes = R.string.new_contact;
        }
        return getResources().getString(titleRes);
    }

    private String getBottomTitle() {
        int titleRes;
        if (iSContactID > 0) {
            titleRes = R.string.save_contact;
        } else {
            titleRes = R.string.create_contact;
        }
        return getResources().getString(titleRes);
    }

    private List<if_FormItem> getDataSource() {
        if (mDataSource == null) {
            ArrayList<if_FormItem> items = new ArrayList<>();

            if_FormItem firstName = new if_FormItem(if_FormItem.Identifiers.firstName, textInput);
            firstName.title = "First Name";
            firstName.requiredTitle = "Required";
            firstName.value = mContact.sFirstName;
            firstName.inputType = InputType.TYPE_TEXT_FLAG_CAP_WORDS;
            items.add(firstName);

            if_FormItem lastName = new if_FormItem(if_FormItem.Identifiers.lastName, textInput);
            lastName.title = "Last Name";
            lastName.value = mContact.sLastName;
            lastName.inputType = InputType.TYPE_TEXT_FLAG_CAP_WORDS;
            items.add(lastName);

            if_FormItem email = new if_FormItem(if_FormItem.Identifiers.email, textInput);
            email.title = "Email";
            email.value = mContact.sEmail;
            email.inputType = InputType.TYPE_TEXT_VARIATION_EMAIL_ADDRESS;
            items.add(email);

            if_FormItem phone = new if_FormItem(if_FormItem.Identifiers.phone, textInput);
            phone.title = "Phone";
            phone.value = mContact.sPhone;
            phone.inputType = InputType.TYPE_CLASS_PHONE;
            items.add(phone);

            if_FormItem mobile = new if_FormItem(if_FormItem.Identifiers.mobile, textInput);
            mobile.title = "Mobile";
            mobile.value = mContact.sMobile;
            mobile.inputType = InputType.TYPE_CLASS_PHONE;
            items.add(mobile);

            if_FormItem type = new if_FormItem(if_FormItem.Identifiers.contactType, textSelect);
            type.title = "Type";
            type.value = mContact.sTag;
            type.selectable = iSContactID <= 0;
            items.add(type);

            mDataSource = items;
        }
        return mDataSource;
    }

    private String validateFormItems() {
        String[] validateItems = { firstName, email, contactType };
        for (if_FormItem formItem : getDataSource()) {
            formItem.validateResult = if_FormItem.ValidateResult.none;
            if (Arrays.asList(validateItems).contains(formItem.identifier)) {
                String value = !StringUtils.isEmpty(formItem.value) ? formItem.value.trim() : "";
                boolean bFirstName = formItem.identifier.equalsIgnoreCase(firstName);
                boolean bEmail = formItem.identifier.equalsIgnoreCase(email);
                boolean bContactType = formItem.identifier.equalsIgnoreCase(contactType);
                if ((bFirstName && value.length() == 0) ||
                        (bEmail && value.length() > 0 && !CommonHelper.IsEmail(value)) ||
                        (formItem.selectable && bContactType && value.length() == 0)) {
                    formItem.validateResult = if_FormItem.ValidateResult.failure;

                    if (bFirstName)
                        return "First Name can not be empty.";
                    else if (bEmail)
                        return "Invalid Email Address";
                    else if (bContactType)
                        return "Please select a Contact Type";
                }
            }
        }

        return null;
    }

    private void requestSubmitFormData() {
        if (NetworkUtils.isNetworkAvailable(this)) {
            String sMessage = validateFormItems();
            if (!StringUtils.isEmpty(sMessage)) {
                CommonUI.ShowAlert(this, "Error", sMessage);
            } else {
                final MaterialDialog progressDialog = CommonUI.ShowMaterialProgressDialog(
                        this, "Message", "Processing. Please wait ...");
                CommonHelper.hideSoftKeyboard(this);
                List<if_FormItem> data = getDataSource();
                RequestParams oParams = new RequestParams();
                for (if_FormItem item : data) {
                    String value = !StringUtils.isEmpty(item.value) ? item.value.trim() : "";
                    if (bDateRange.equalsIgnoreCase(item.identifier)) {
                        oParams.add(Constants.Keys.kDateFrom, value);
                        oParams.add(Constants.Keys.kDateTo, !StringUtils.isEmpty(item.value2) ? item.value2.trim() : "");
                    } else {
                        oParams.add(item.identifier, value);
                    }
                }
                oParams.add("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                oParams.add("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
                oParams.add("iContactID", "" + iSContactID);
                oParams.add("iPropertyID", "" + iSAssetID);

                String sURL = "/IOAPI/App_UpdateContact";
                IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                    @Override
                    public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                        runOnUiThread(() -> progressDialog.dismiss());
                        try {
                            if (statusCode == 200 && response.getBoolean("success")) {
                                JSONObject oObject = response.getJSONObject("oContact");
                                mContact.sFirstName= oObject.getString("sFirstName");
                                mContact.sLastName = oObject.getString("sLastName");
                                mContact.sMobile = oObject.getString("sMobile");
                                mContact.sEmail = oObject.getString("sEmail");
                                mContact.sPhone = oObject.getString("sPhone");
                                mContact.iSContactID = oObject.getInt("iContactID");
                                mContact.iSAssetID = oObject.getInt("iPropertyID");
                                mContact.sTag = oObject.getString("sTag");
                                mContact.sFieldOne = oObject.getString("sCustom");
                                mContact.sFieldTwo = oObject.getString("sCustom2");
                                String sFieldThree = oObject.getString("sCustom3");
                                sFieldThree = CommonJson.AddJsonKeyValue(sFieldThree,
                                        ai_Contact.Keys.kIsPrimary, oObject.optString("bPrimary", ""));
                                sFieldThree = CommonJson.AddJsonKeyValue(sFieldThree,
                                        ai_Contact.Keys.kDateFrom, oObject.optString("dtFrom", ""));
                                sFieldThree = CommonJson.AddJsonKeyValue(sFieldThree,
                                        ai_Contact.Keys.kDateTo, oObject.optString("dtTo", ""));
                                mContact.sFieldThree = sFieldThree;
                                mContact.save();
                                finish();
                            } else {
                                CommonUI.ShowAlert(if_EditContact_2nd.this, "Error", "To create or update contact information, please make sure you are connected to Internet.");
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditContact_2nd.UpdateContact", ex, if_EditContact_2nd.this);
                        }
                    }

                    @Override
                    public void onFailure(int statusCode, org.apache.http.Header[] headers, java.lang.Throwable e, org.json.JSONObject errorResponse) {
                        runOnUiThread(() -> progressDialog.dismiss());
                        CommonUI.ShowAlert(if_EditContact_2nd.this, "Error", "To update contact information, please make sure you are connected to Internet.");
                    }
                });
            }
        } else {
            CommonUI.ShowAlert(this, "Error", "To update asset or contact information, please make sure the device is connected to Internet. Please note, inspecting assets DO NOT require internet but uploading inspection data requires either 3G or WiFi. If you want to inspect a NEW asset without internet, please go to Inspection Tab, and add a new inspection from there so you can carry on inspection on a new asset without Internet.");
        }
    }

    private void requestDeleteContact() {
        if (NetworkUtils.isNetworkAvailable(this)) {
            final MaterialDialog progressDialog = CommonUI.ShowMaterialProgressDialog(
                    this, "Message", "Processing. Please wait ...");
            CommonHelper.hideSoftKeyboard(this);
            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
            oParams.add("iContactID", "" + iSContactID);
            oParams.add("iPropertyID", "" + iSAssetID);

            String sURL = "/IOAPI/App_DeleteContact";
            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    runOnUiThread(progressDialog::dismiss);
                    try {
                        if (statusCode == 200 && response.getBoolean("success")) {
                            mContact.bDeleted = true;
                            mContact.save();
                            finish();
                        } else {
                            CommonUI.ShowAlert(if_EditContact_2nd.this, "Error", response.optString(
                                "message", "To delete contact information, please make sure you are connected to Internet."));
                        }
                    } catch (Exception ex) {
                        ai_BugHandler.ai_Handler_Exception("Exception", "if_EditContact_2nd.UpdateContact", ex, if_EditContact_2nd.this);
                    }
                }

                @Override
                public void onFailure(int statusCode, org.apache.http.Header[] headers, java.lang.Throwable e, org.json.JSONObject errorResponse) {
                    runOnUiThread(progressDialog::dismiss);
                    CommonUI.ShowAlert(if_EditContact_2nd.this, "Error", "To delete contact information, please make sure you are connected to Internet.");
                }
            });
        } else {
            CommonUI.ShowAlert(this, "Error", "To delete contact information, please make sure the device is connected to Internet.");
        }
    }

    @Override
    public void itemSelect(if_FormItem item, final int position) {
        if (!item.selectable) return;
        CommonHelper.hideSoftKeyboard(this);
        List<O_ContactType> contactTypes = CommonJson.getContactTypes(this);
        final String[] options = new String[contactTypes.size()];
        for (int i = 0; i < contactTypes.size(); i++) {
            options[i] = contactTypes.get(i).sLabel;
        }
        new MaterialDialog.Builder(this)
                .title("Contact Type")
                .items(options)
                .itemsCallbackSingleChoice(-1, (dialog, view1, which, text) -> {
                    item.value = (String) text;
                    selectContactType(contactTypes.get(which));
                    return true;
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    @Override
    public void dateRangeSelect(if_FormItem item, Constants.DateRange dateRange, String value, int position) {
        CommonHelper.hideSoftKeyboard(this);
        final Calendar calendar = Calendar.getInstance();
        try {
            if (!StringUtils.isEmpty(value)) {
                calendar.setTime(DateUtils.parse(value, DATE_RANGE_FORMAT));
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditContact_2nd.dateRangeSelect", ex, if_EditContact_2nd.this);
        }
        DatePickerDialog datePickerDialog = new DatePickerDialog(this,
                (view, year, monthOfYear, dayOfMonth) -> {
                    calendar.set(year, monthOfYear, dayOfMonth);
                    String selectedDate = DateUtils.format(calendar.getTime(), DATE_RANGE_FORMAT);
                    switch (dateRange) {
                        case FROM:
                            item.value = selectedDate;
                            break;
                        case TO:
                            item.value2 = selectedDate;
                            break;
                    }
                    reloadListView();
                }, calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH));
        datePickerDialog.show();
    }

    private void reloadListView() {
        FormItemsAdapter adapter = (FormItemsAdapter) mListView.getAdapter();
        adapter.notifyDataSetChanged();
    }

    private void selectContactType(O_ContactType oContactType) {
        List<if_FormItem> dataSource = getDataSource();
        // remove the items with identifier bIsPrimary and bDateRange
        for (int i = dataSource.size() - 1; i >= 0; i--) {
            if (dataSource.get(i).identifier.equalsIgnoreCase(bIsPrimary) ||
                    dataSource.get(i).identifier.equalsIgnoreCase(bDateRange)) {
                dataSource.remove(i);
            }
        }

        // add the items with identifier bIsPrimary and bDateRange if needed
        if (oContactType.bPrimary) {
            if_FormItem item = new if_FormItem(bIsPrimary, checkBox);
            item.identifier = bIsPrimary;
            item.title = "Primary";
            item.value = String.valueOf(mContact.isPrimary());
            dataSource.add(item);
        }

        if (oContactType.bDateRange) {
            if_FormItem item = new if_FormItem(bDateRange, dateRange);
            item.identifier = bDateRange;
            item.title = "Occupancy Period";
            item.value = mContact.dtFrom();
            item.value2 = mContact.dtTo();
            dataSource.add(item);
        }

        // Reload the list view
        mDataSource = dataSource;
        FormItemsAdapter adapter = (FormItemsAdapter) mListView.getAdapter();
        adapter.setFormItems(getDataSource());
        adapter.notifyDataSetChanged();
    }

    @Override
    public void attachmentSelect(if_FormItem item, int position) {

    }
}
