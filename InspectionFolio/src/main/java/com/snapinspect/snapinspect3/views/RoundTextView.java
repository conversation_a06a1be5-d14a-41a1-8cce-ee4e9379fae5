package com.snapinspect.snapinspect3.views;

/**
 * @Created by <PERSON><PERSON><PERSON> on 6/20/18.
 */

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.util.AttributeSet;

import androidx.annotation.RequiresApi;


public class RoundTextView extends androidx.appcompat.widget.AppCompatTextView {
    private float strokeWidth = 0;
    public int strokeColor, solidColor;
    private int cornerRadius;

    public RoundTextView(Context context) {
        super(context);
        setWillNotDraw(false);
    }

    public RoundTextView(Context context, AttributeSet attrs) {
        super(context, attrs);
        setWillNotDraw(false);
    }

    public RoundTextView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        setWillNotDraw(false);
    }


    @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN)
    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);

        GradientDrawable gd = new GradientDrawable();

        // Specify the shape of drawable
        gd.setShape(GradientDrawable.RECTANGLE);

        // Set the fill color of drawable
        gd.setColor(solidColor); // make the background transparent


        // Make the border rounded
        gd.setCornerRadius(cornerRadius); // border corner radius

        // Finally, apply the GradientDrawable as TextView background
        this.setBackground(gd);
    }

    public void setCornerRadius(int dp) {
        float scale = getContext().getResources().getDisplayMetrics().density;
        cornerRadius = (int) (dp * scale);

    }

    public void setStrokeWidth(int dp) {
        float scale = getContext().getResources().getDisplayMetrics().density;
        strokeWidth = dp * scale;

    }

    public void setStrokeColor(String color) {
        try {
            strokeColor = Color.parseColor(color);
        } catch (Exception ex) {
            strokeColor = Color.parseColor("#CCCCCC");
        }

    }

    public void setSolidColor(int color) {
        solidColor = color;
        this.invalidate();
        this.requestLayout();


    }
}