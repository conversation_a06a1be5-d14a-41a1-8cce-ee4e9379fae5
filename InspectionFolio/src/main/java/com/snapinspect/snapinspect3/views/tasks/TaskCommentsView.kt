package com.snapinspect.snapinspect3.views.tasks

import androidx.compose.foundation.layout.*
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.snapinspect.snapinspect3.Helper.Constants
import com.snapinspect.snapinspect3.IF_Object.ai_TaskComment
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.util.DateUtils
import com.snapinspect.snapinspect3.views.composables.colors
import com.snapinspect.snapinspect3.views.composables.dimens
import com.snapinspect.snapinspect3.views.composables.typography
import java.util.*

@Composable
fun TaskCommentsView(
    modifier: Modifier = Modifier,
    comments: List<ai_TaskComment>,
    onClickFile: (TaskCommentFile) -> Unit = {},
    horizontalPadding: PaddingValues = PaddingValues(horizontal = dimens.normal),
    verticalSpacing: Dp = dimens.medium,
    hasMoreComments: Boolean = false,
    onLoadMore: () -> Unit = {},
    loadMoreText: String = "More Previous Comments",
    loadMoreStyle: TextStyle = typography.normal.copy(
        fontWeight = FontWeight.SemiBold,
        fontSize = 16.sp,
        color = colors.color4E69FF
    ),
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontalPadding),
        verticalArrangement = Arrangement.spacedBy(verticalSpacing)
    ) {
        if (hasMoreComments) {
            LoadMoreButton(
                text = loadMoreText,
                style = loadMoreStyle,
                onClick = onLoadMore
            )
        }

        comments.forEach { comment ->
            CommentRow(comment = comment, onClickFile = onClickFile)
        }
    }
}

@Composable
private fun LoadMoreButton(
    text: String,
    style: TextStyle,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    TextButton(
        onClick = onClick,
        modifier = modifier.fillMaxWidth()
    ) {
        Text(
            text = text,
            style = style,
            modifier = Modifier.padding(vertical = dimens.small)
        )
    }
}

@Composable
private fun CommentRow(
    comment: ai_TaskComment,
    modifier: Modifier = Modifier,
    onClickFile: (TaskCommentFile) -> Unit = {},
    avatarSize: Dp = 32.dp,
    authorNameStyle: TextStyle = typography.normal.copy(
        fontWeight = FontWeight.SemiBold,
        fontSize = 14.sp,
        color = colors.color080808
    ),
    timestampStyle: TextStyle = typography.normal.copy(
        fontSize = 12.sp,
        color = colors.colorB3B4B4
    ),
    contentStyle: TextStyle = typography.normal.copy(
        fontSize = 16.sp,
        color = colors.color050505
    ),
    fileStyle: TextStyle = typography.normal.copy(
        fontSize = 16.sp,
        fontWeight = FontWeight.SemiBold,
        color = colors.color3E6BFF,
    )
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(dimens.small),
        verticalAlignment = Alignment.Top
    ) {
        UserAvatarView(
            user = comment.author,
            size = avatarSize,
        )

        CommentContent(
            authorName = comment.author?.sName ?: Constants.Values.ANONYMOUS_USER,
            timestamp = DateUtils.toRelative(LocalContext.current, comment.dtDateTime, Date()),
            content = comment.sDesp,
            commentType = comment.sType,
            onClickFile = onClickFile,
            avatarSize = avatarSize,
            authorNameStyle = authorNameStyle,
            timestampStyle = timestampStyle,
            contentStyle = contentStyle,
            fileStyle = fileStyle,
        )
    }
}

@Composable
private fun CommentContent(
    authorName: String,
    timestamp: String,
    content: String,
    commentType: String,
    onClickFile: (TaskCommentFile) -> Unit = {},
    avatarSize: Dp,
    authorNameStyle: TextStyle,
    timestampStyle: TextStyle,
    contentStyle: TextStyle,
    fileStyle: TextStyle,
) {
    Column(
        verticalArrangement = Arrangement.spacedBy(dimens.smaller)
    ) {
        Row(
            modifier = Modifier.height(avatarSize),
            horizontalArrangement = Arrangement.spacedBy(dimens.small),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = authorName, style = authorNameStyle)
            Text(text = timestamp, style = timestampStyle)
        }

        when (commentType) {
            ai_TaskComment.COMMENT_TYPE_TEXT -> {
                Text(text = content, style = contentStyle)
            }
            ai_TaskComment.COMMENT_TYPE_FILE -> {
                val fileInfo = TaskCommentFile.fromJson(content)
                if (fileInfo != null) {
                    FileRow(
                        file = fileInfo,
                        style = fileStyle,
                        onClick = onClickFile
                    )
                } else {
                    Text(text = content, style = contentStyle)
                }
            }
            else -> {
                Text(text = content, style = contentStyle)
            }
        }
    }
}

@Composable
private fun FileRow(file: TaskCommentFile, style: TextStyle, onClick: (TaskCommentFile) -> Unit) {
    TextButton(
        onClick = { onClick(file) },
        contentPadding = PaddingValues(0.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(dimens.semiMedium),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Icon(
                painter = painterResource(R.drawable.icon_custom_info_attachment),
                contentDescription = null,
                modifier = Modifier.size(22.dp),
                tint = style.color
            )
            
            Text(
                text = file.fileName,
                style = style
            )
        }
    }
}