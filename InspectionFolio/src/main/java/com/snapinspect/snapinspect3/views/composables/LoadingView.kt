package com.snapinspect.snapinspect3.views.composables

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.snapinspect.snapinspect3.R

sealed interface LoadingState {
    data object None : LoadingState
    data class Loading(val message: String = "") : LoadingState
    data class Error(val message: String) : LoadingState
}

@Composable
fun LoadingView(
    state: LoadingState,
    onDismissError: () -> Unit,
    modifier: Modifier = Modifier,
    backgroundColor: Color = colorResource(id = R.color.black_overlay),
    contentColor: Color = colorResource(id = R.color.white_color)
) {
    when (state) {
        is LoadingState.Loading -> LoadingContent(
            message = state.message,
            modifier = modifier,
            backgroundColor = backgroundColor,
            contentColor = contentColor
        )
        is LoadingState.Error -> ErrorDialog(
            message = state.message,
            onDismiss = onDismissError
        )
        LoadingState.None -> Unit
    }
}

@Composable
private fun LoadingContent(
    message: String,
    modifier: Modifier = Modifier,
    backgroundColor: Color,
    contentColor: Color
) {
    Box(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Surface(
            modifier = Modifier
                .wrapContentSize()
                .clip(MaterialTheme.shapes.medium),
            color = backgroundColor
        ) {
            Column(
                modifier = Modifier.padding(dimensionResource(id = R.dimen.margin_20)),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                CircularProgressIndicator(
                    color = contentColor
                )
                if (message.isNotEmpty()) {
                    Spacer(modifier = Modifier.height(16.dp))
                    Text(
                        text = message,
                        style = MaterialTheme.typography.titleMedium,
                        color = contentColor
                    )
                }
            }
        }
    }
}

@Composable
private fun ErrorDialog(
    message: String,
    onDismiss: () -> Unit
) {
    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("Error") },
        text = { Text(message) },
        confirmButton = {
            TextButton(onClick = onDismiss) {
                Text("OK")
            }
        }
    )
}

@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
private fun LoadingViewPreview_Loading() {
    MaterialTheme {
        LoadingView(
            state = LoadingState.Loading("Loading your data..."),
            onDismissError = {}
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
private fun LoadingViewPreview_LoadingNoMessage() {
    MaterialTheme {
        LoadingView(
            state = LoadingState.Loading(),
            onDismissError = {}
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
private fun LoadingViewPreview_Error() {
    MaterialTheme {
        LoadingView(
            state = LoadingState.Error("Something went wrong!"),
            onDismissError = {}
        )
    }
}

// Optional: Preview with custom colors
@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
private fun LoadingViewPreview_CustomColors() {
    SnapTheme {
        LoadingView(
            state = LoadingState.Loading("Custom colors"),
            onDismissError = {},
            backgroundColor = colors.colorFF5151,
            contentColor = colors.colorF7F7F7
        )
    }
}