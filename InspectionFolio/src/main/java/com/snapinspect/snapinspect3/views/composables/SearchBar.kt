package com.snapinspect.snapinspect3.views.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.Icon
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.Search
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.snapinspect.snapinspect3.R

@Composable
fun SearchBar(
    modifier: Modifier = Modifier,
    value: String,
    paddingStart: Dp = dimens.normal,
    paddingEnd: Dp = dimens.normal,
    paddingVertical: Dp = dimens.small,
    height: Dp = 36.dp,
    placeholderText: String = "Search",
    onValueChange: (String) -> Unit,
    textFieldColor: Color = colorResource(id = R.color.white_color),
    backgroundColor: Color = colorResource(id = R.color.colorPrimary),
    cornerRadius: Dp = 8.dp,
) {
    val focusManager = LocalFocusManager.current
    val keyboardController = LocalSoftwareKeyboardController.current

    FitHeightTextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .padding(start = paddingStart, end = paddingEnd)
            .padding(vertical = paddingVertical)
            .height(height),
        placeholderText = placeholderText,
        keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
        keyboardActions = KeyboardActions(
            onDone = {
                keyboardController?.hide()
                focusManager.clearFocus()
            }
        ),
        leadingIcon = {
            Icon(
                Icons.Default.Search,
                contentDescription = null,
                tint = colorResource(R.color.light_gray)
            )
        },
        trailingIcon = {
            if (value.isNotEmpty()) {
                Icon(
                    Icons.Default.Clear,
                    contentDescription = null,
                    tint = colorResource(R.color.light_gray),
                    modifier = Modifier.clickable {
                        onValueChange("")
                    }
                )
            }
        },
        fontSize = dimensionResource(R.dimen.text_normal).value.sp,
        textContainerColor = textFieldColor,
        cornerRadius = cornerRadius
    )
}

@Composable
@Preview
fun SearchBarPreview() {
    SnapTheme {
        SearchBar(
            value = "Search",
            onValueChange = {},
            textFieldColor = colorResource(id = R.color.light_gray_color),
            backgroundColor = colorResource(id = R.color.colorPrimary),
            cornerRadius = 10.dp
        )
    }
}