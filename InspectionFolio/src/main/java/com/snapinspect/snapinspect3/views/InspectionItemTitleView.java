package com.snapinspect.snapinspect3.views;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.LinearLayout;
import android.widget.TextView;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.TextViewUtils;

public class InspectionItemTitleView extends LinearLayout {

    private TextView mParentItemTitleTextView;
    private TextView mItemTitleTextView;

    public InspectionItemTitleView(Context context) {
        super(context);
    }

    public InspectionItemTitleView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        setupViews();
    }

    private void setupViews() {
        mParentItemTitleTextView = findViewById(R.id.inspection_parent_item_title);
        mItemTitleTextView = findViewById(R.id.inspection_item_title);
    }

    public void setItemTitles(String parentItemTitle, String itemTitle) {
        TextViewUtils.updateText(mParentItemTitleTextView, parentItemTitle);
        TextViewUtils.updateText(mItemTitleTextView, itemTitle);
    }
}
