package com.snapinspect.snapinspect3.views.tasks

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.layout.Arrangement.spacedBy
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.snapinspect.snapinspect3.IF_Object.TaskPriority
import com.snapinspect.snapinspect3.IF_Object.ai_Task
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.util.DateUtils
import com.snapinspect.snapinspect3.util.StringUtils
import com.snapinspect.snapinspect3.views.composables.*
import java.util.*

private object TaskRowDefaults {
    val TagShape = RoundedCornerShape(5.dp)
    val StatusCircleSize = 44.dp
    val TitleFontSize = 17.sp
    val TagFontSize = 12.sp
    val StatusIconSize = 20.dp
    val StatusFontSize = 16.sp
}

@Composable
fun TaskRowView(
    task: ai_Task,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {}
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(
                vertical = dimensionResource(R.dimen.margin_10),
                horizontal = dimensionResource(R.dimen.margin_20)
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = spacedBy(dimensionResource(R.dimen.margin_small))
    ) {
        TaskInfoView(
            task = task,
            modifier = Modifier.weight(1f)
        )
        TaskStatusView(task = task)
    }
}

@Composable
private fun TaskInfoView(
    task: ai_Task,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier,
        verticalArrangement = spacedBy(dimensionResource(R.dimen.margin_10))
    ) {
        TaskTitle(title = task.sTitle)
        TaskTags(task = task)
    }
}

@Composable
private fun TaskTitle(title: String) {
    Text(
        text = title,
        fontSize = TaskRowDefaults.TitleFontSize,
        color = Color(0xFF080808),
        maxLines = 2,
        overflow = TextOverflow.Ellipsis
    )
}

@Composable
private fun TaskTags(task: ai_Task) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = spacedBy(dimensionResource(R.dimen.border_width))
    ) {
        TaskPriorityTag(task = task)

        task.sCategory?.takeIf { it.isNotEmpty() && it != "null" }?.let {
            TaskCategoryTag(
                modifier = Modifier.weight(1f, fill = false),
                sCategory = it)
        }

        task.dtDateDue?.let { dueDate ->
            val formattedDate = DateUtils.formatAsTaskDueDate(dueDate)
            TaskDueDateTag(
                dueDate = formattedDate
            )
        }
    }
}

@Composable
private fun TaskPriorityTag(modifier: Modifier = Modifier, task: ai_Task) {
    Text(
        text = task.priority.displayName,
        fontWeight = FontWeight.Normal,
        fontSize = TaskRowDefaults.TagFontSize,
        modifier = modifier
            .background(
                color = colorResource(task.priority.backgroundColor),
                shape = TaskRowDefaults.TagShape
            )
            .padding(dimens.smaller),
        color = Color.White
    )
}

@Composable
private fun TaskCategoryTag(modifier: Modifier = Modifier, sCategory: String) {
    Text(
        text = sCategory,
        fontWeight = FontWeight.SemiBold,
        fontSize = TaskRowDefaults.TagFontSize,
        modifier = modifier
            .dashedBorder(
                width = 1.dp,
                color = colors.color337ab7,
                shape = TaskRowDefaults.TagShape
            )
            .padding(dimens.smaller),
        color = colors.color337ab7,
        maxLines = 1,
        overflow = TextOverflow.Ellipsis
    )
}

@Composable
private fun TaskDueDateTag(modifier: Modifier = Modifier, dueDate: String) {
    Row(
        modifier = modifier
            .background(
                color = Color(0xFFEAEAEA),
                shape = TaskRowDefaults.TagShape
            )
            .padding(dimens.smaller),
        horizontalArrangement = spacedBy(dimensionResource(R.dimen.margin_smallest)),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Filled.DateRange,
            contentDescription = "Due Date",
            modifier = Modifier.size(13.dp),
            tint = Color.Black
        )
        Text(
            text = dueDate,
            fontWeight = FontWeight.SemiBold,
            fontSize = TaskRowDefaults.TagFontSize,
            color = Color.Black,
            maxLines = 1
        )
    }
}

@Composable
private fun TaskStatusView(task: ai_Task) {
    if (task.bClosed) {
        Box(
            modifier = Modifier
                .size(TaskRowDefaults.StatusCircleSize)
                .border(
                    width = 1.dp,
                    color = Color(0xFF54B445),
                    shape = CircleShape
                ),
            contentAlignment = Alignment.Center
        ) {
            Icon(
                imageVector = Icons.Filled.Check,
                contentDescription = "Closed",
                modifier = Modifier
                    .height(TaskRowDefaults.StatusIconSize),
                tint = Color(0xFF54B445)
            )
        }
    } else if (task.sStatus.isNotEmpty()) {
        Text(
            text = StringUtils.getFirstLetters(task.sStatus),
            fontWeight = FontWeight.SemiBold,
            fontSize = TaskRowDefaults.StatusFontSize,
            color = Color.White,
            modifier = Modifier
                .size(TaskRowDefaults.StatusCircleSize)
                .background(
                    color = task.sStatusCode?.let { Color.hex(it) } ?: Color(0xFFBEBE),
                    shape = CircleShape
                )
                .wrapContentSize(Alignment.Center)
        )
    }
}

@Composable
@Preview(showBackground = true)
fun TaskRowViewPreview() {
    val task = ai_Task().apply {
        sTitle = "Task Title"
        iPriority = TaskPriority.MILD.value
        sCategory = "Category"
        dtDateDue = Date()
        bClosed = true
        sStatus = "Status 1"
        sStatusCode = "#FF0000"
    }

    val task2 = ai_Task().apply {
        sTitle = "Task Title"
        iPriority = TaskPriority.URGENT.value
        sCategory = "Category"
        dtDateDue = Date()
        bClosed = false
        sStatus = "Status 1"
        sStatusCode = "#FF0000"
    }

    val taskWithLongText = ai_Task().apply {
        sTitle = "Task Title with a long text that should be ellipsized"
        iPriority = TaskPriority.MILD.value
        sCategory = "Category with a long text that should be ellipsized"
        dtDateDue = Date()
        bClosed = false
        sStatus = "Status 1"
        sStatusCode = "#FF0000"
    }

    SnapTheme {
        Column(modifier = Modifier.background(Color.White)) {
            TaskRowView(task) { /* onClick */ }
            Divider(
                color = Color(0xFFAAAAAA).copy(alpha = 0.5f),
                thickness = 0.5.dp,
                modifier = Modifier.padding(horizontal = dimensionResource(R.dimen.margin_20))
            )
            TaskRowView(task2) { /* onClick */ }
            Divider(
                color = Color(0xFFAAAAAA).copy(alpha = 0.5f),
                thickness = 0.5.dp,
                modifier = Modifier.padding(horizontal = dimensionResource(R.dimen.margin_20))
            )
            TaskRowView(taskWithLongText) { /* onClick */ }
        }
    }
}