package com.snapinspect.snapinspect3.views;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.AsyncTask;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.*;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activity.if_NoticeList;
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayPhoto;

import java.io.InputStream;
import java.lang.ref.WeakReference;
import java.net.URL;
import java.util.Date;
import java.util.HashMap;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.*;

/*
 * @Created by osama on 09/06/17.
 */
public class GA_Inspection_View extends LinearLayout {
    private Context oContext;
    private final HashMap<ai_InsItem, Integer> mIdMap = new HashMap<>();
    private ai_InsItem oPInsItem;

    private final int iDefaultButtonWidth = 70;
    private final int iDefaultButtonHeight = 42;
    private boolean bFullInspection = false;
    private boolean bRequestInspection = false;
    private float oDensity;
    private int iButtonPerRow;
    private ai_Photo selectedPhoto;
    private int iInspectionId;

    public GA_Inspection_View(Context context) {
        super(context);
    }

    public GA_Inspection_View(Context _oContext, int insId, ai_InsItem _oPInsItem, boolean _bFull, boolean _bRequestInspection, int pos) {
        super(_oContext);

        oContext = _oContext;
        mIdMap.put(oPInsItem, 0);
        oPInsItem = _oPInsItem;
        bFullInspection = _bFull;
        this.iInspectionId = insId;
        bRequestInspection = _bRequestInspection;
        DisplayMetrics oMetrics = new DisplayMetrics();
        ((WindowManager) oContext.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay().getMetrics(oMetrics);

        oDensity = oMetrics.density;
        iButtonPerRow = ((int)(oMetrics.widthPixels/oDensity) - CommonUI_InsItem.INSPECT_CELL_MARGIN_LEFT * 2) / iDefaultButtonWidth;

        this.setLayoutParams(new LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        this.setPadding(0, CommonHelper.pxFromDp(oContext, 10), 0, CommonHelper.pxFromDp(oContext, 10));

        InspectionView();
    }

    public void InspectionView() {
        LayoutInflater inflater = (LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View row = inflater.inflate(R.layout.cell_ins_full, this, false);
        try {
            LinearLayout oParentLayout = row.findViewById(R.id.view_Full);
            LinearLayout headLayout = row.findViewById(R.id.view_head);

            CommonUI_InsItem.OneViewInspection_Label(oContext, oPInsItem, bFullInspection, headLayout);
            final boolean bReview = CommonValidate.bItemReviewEnabled(oPInsItem);
            String[] lsOption = CommonConfig.MenuText_ItemsView_GA_Type(oContext, bRequestInspection,bReview);
            if (lsOption !=  null && lsOption.length > 0) {
                ImageButton menuBtn = new ImageButton(oContext);
                CommonUI_InsItem.OneViewInspection_CommandButton(oContext, menuBtn, oPInsItem, bReview);

                menuBtn.setOnClickListener(v -> {
                    if (CommonHelper.isKioskMode(oContext)){
                        Toast.makeText(oContext, "You can not edit this inspection.", Toast.LENGTH_LONG).show();
                        return;
                    }

                    new MaterialDialog.Builder(oContext)
                            .title(R.string.alert_title_action)
                            .items(lsOption)
                            .itemsCallbackSingleChoice(-1, (dialog, view, i, text) -> {
                                if (text.toString().equalsIgnoreCase(CommonConfig.sMenu_ItemTask)){
                                    oContext.startActivity(if_NoticeList.newIntent(oContext, oPInsItem.iInsID, oPInsItem.getId()));
                                } else if (text.toString().equalsIgnoreCase( CommonConfig.sMenu_ReviewNotes)){
                                    ShowAlert("Review Notes", CommonValidate.sItemReviewNotes(oPInsItem));
                                }
                                return true;
                            })
                            .negativeText(R.string.md_cancel_label)
                            .show();
                });
                headLayout.addView(menuBtn);

            }

            TextView oInstruction = new TextView(oContext);
            CommonUI_InsItem.OneViewInspection_InstructionLabel(oContext, oInstruction, CommonDB.GetInstruction(oPInsItem, bFullInspection));

            oParentLayout.addView(oInstruction);

            String sConfig1 = (oPInsItem.sConfigOne == null || oPInsItem.sConfigOne.trim().equalsIgnoreCase("")) ? "" : oPInsItem.sConfigOne.trim();
            String sConfig2 = (oPInsItem.sConfigTwo == null || oPInsItem.sConfigTwo.trim().equalsIgnoreCase("")) ? "" : oPInsItem.sConfigTwo.trim();
            String sConfig3 = (oPInsItem.sConfigThree == null || oPInsItem.sConfigThree.trim().equalsIgnoreCase("")) ? "" : oPInsItem.sConfigThree.trim();
            String sConfig4 = (oPInsItem.sConfigFour == null || oPInsItem.sConfigFour.trim().equalsIgnoreCase("")) ? "" : oPInsItem.sConfigFour.trim();
            String sConfig5 = (oPInsItem.sConfigFive == null || oPInsItem.sConfigFive.trim().equalsIgnoreCase("")) ? "" : oPInsItem.sConfigFive.trim();
            String sConfig6 = (oPInsItem.sConfigSix == null || oPInsItem.sConfigSix.trim().equalsIgnoreCase("")) ? "" : oPInsItem.sConfigSix.trim();
            if (!sConfig1.isEmpty() && sConfig1.length() > 0) {
                FrameLayout oFV1Layout = GetLayout(sConfig1, 1, oPInsItem, inflater);
                oParentLayout.addView(oFV1Layout);
            }
            if (!sConfig2.isEmpty() && sConfig2.length() > 0) {
                FrameLayout oFV1Layout = GetLayout(sConfig2, 2, oPInsItem, inflater);
                oParentLayout.addView(oFV1Layout);
            }
            if (!sConfig3.isEmpty() && sConfig3.length() > 0) {
                FrameLayout oFV1Layout = GetLayout(sConfig3, 3, oPInsItem, inflater);
                oParentLayout.addView(oFV1Layout);
            }
            if (!sConfig4.isEmpty() && sConfig4.length() > 0) {
                FrameLayout oFV1Layout = GetLayout(sConfig4, 4, oPInsItem, inflater);
                oParentLayout.addView(oFV1Layout);
            }
            if (!sConfig5.isEmpty() && sConfig5.length() > 0) {
                FrameLayout oFV1Layout = GetLayout(sConfig5, 5, oPInsItem, inflater);
                oParentLayout.addView(oFV1Layout);
            }
            if (!sConfig6.isEmpty() && sConfig6.length() > 0) {
                FrameLayout oFV1Layout = GetLayout(sConfig6, 6, oPInsItem, inflater);
                oParentLayout.addView(oFV1Layout);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "Ins_GA_Adapter.GetView", ex,oContext);
        }

        this.addView(row);
    }

    @SuppressLint("SetTextI18n")
    private FrameLayout GetLayout(String sFV1, final int iPosition, final ai_InsItem oInsItem, LayoutInflater inflater) {
        try {
            final String sValue = CommonHelper.GetValue(iPosition, oInsItem);
            long lInsItemID = oInsItem.getId();

            FrameLayout oLayout = new FrameLayout(oContext);
            FrameLayout.LayoutParams oParams1 = new FrameLayout.LayoutParams(
                    LinearLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.WRAP_CONTENT);
            oLayout.setLayoutParams(oParams1);

            int iControlType = CommonInsItem.getControlType(sFV1);
            if (iControlType == SI_CONTROL_TYPE_CHK) {
                CommonUI_InsItem.InsItem_RenderCHK(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, iButtonPerRow, iDefaultButtonWidth, iDefaultButtonHeight, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_SCHK) {
                CommonUI_InsItem.InsItem_RenderSCHK(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, iButtonPerRow, iDefaultButtonWidth, iDefaultButtonHeight, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_MCHK) {
                CommonUI_InsItem.InsItem_RenderMCHK(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, iButtonPerRow, iDefaultButtonWidth, iDefaultButtonHeight, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_CMT) {
                CommonUI_InsItem.InsItem_RenderCMT(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_PTO) {
                CommonUI_InsItem.InsItem_RenderPTO(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_LST) {
                CommonUI_InsItem.InsItem_RenderLST(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_DT) {
                CommonUI_InsItem.InsItem_RenderDT(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_SCAN) {
                CommonUI_InsItem.InsItem_RenderSCAN(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_SEL || iControlType == SI_CONTROL_TYPE_MSEL) {
                CommonUI_InsItem.InsItem_RenderSEL(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_NUM) {
                CommonUI_InsItem.InsItem_RenderNUM(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_COST) {
                CommonUI_InsItem.InsItem_RenderCOST(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            }

            return oLayout;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "Ins_GA_Adapter.GetLayout", ex, oContext);
            FrameLayout oLayout = new FrameLayout(oContext);
            FrameLayout.LayoutParams oParams1 = new FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.WRAP_CONTENT);
            oLayout.setLayoutParams(oParams1);
            return oLayout;
        }
    }

    private int UpdatePhoto(ai_Photo aPhoto) {
        if (aPhoto != null && aPhoto.getId() > 0) {
            ai_Photo uPhoto = ai_Photo.findById(ai_Photo.class, aPhoto.getId());
            uPhoto.iSPhotoID = aPhoto.iSPhotoID;
            uPhoto.sThumb = aPhoto.sThumb;
            uPhoto.sFile = aPhoto.sFile;
            uPhoto.sComments = EscapeString(aPhoto.sComments);
            uPhoto.bUploaded = aPhoto.bUploaded;
            uPhoto.bDeleted = aPhoto.bDeleted;

            uPhoto.save();
            return uPhoto.getId().intValue();
        } else {
            ai_Photo sPhoto = new ai_Photo();
            sPhoto.iInsItemID = aPhoto.iInsItemID;
            sPhoto.iInsID = aPhoto.iInsID;
            sPhoto.sThumb = aPhoto.sThumb;
            sPhoto.iSPhotoID = aPhoto.iSPhotoID;
            sPhoto.sFile = aPhoto.sFile;
            sPhoto.iSize = CommonHelper.GetFileLength(aPhoto.sFile);
            Date oNow = new Date();
            sPhoto.dtDateTime = CommonHelper.sDateToString(oNow);

            sPhoto.save();
            return sPhoto.getId().intValue();
        }
    }

    private static String EscapeString(String sValue){
        return sValue.replaceAll("'","''");
    }

    private static class LoadImage extends AsyncTask<String, String, Bitmap> {
        private MaterialDialog oDialog;
        private final WeakReference<GA_Inspection_View> weakReference;

        LoadImage(GA_Inspection_View view) {
            weakReference = new WeakReference<>(view);
        }

        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            GA_Inspection_View view = weakReference.get();
            if (view == null) return;
            oDialog = CommonUI.ShowMaterialProgressDialog(view.oContext, "Message", "Downloading data ...");
        }

        protected Bitmap doInBackground(String... args) {
            Bitmap bitmap = null;
            try {
               bitmap = BitmapFactory.decodeStream((InputStream)new URL(args[0]).getContent());

            } catch (Exception e) {
                e.printStackTrace();
            }
            return bitmap;
        }

        protected void onPostExecute(Bitmap bitmap) {
            oDialog.dismiss();

            GA_Inspection_View view = weakReference.get();
            if (view != null && bitmap != null) {
                O_FileName oFileName = O_FileName.getPhotoFileName();

                CommonHelper.SaveImage(oFileName.sFilePath, bitmap);
                CommonHelper.SaveThumb(oFileName.sThumbNail, bitmap);

                view.selectedPhoto.sFile = oFileName.sFilePath;
                view.selectedPhoto.sThumb = oFileName.sThumbNail;
                int iPhotoID = view.UpdatePhoto(view.selectedPhoto);

                bitmap.recycle();
                view.selectedPhoto = null;

                view.oContext.startActivity(if_DisplayPhoto.newIntent(view.oContext, iPhotoID));
            }
        }
    }

    private void ShowAlert(String sTitle, String sMessage) {
        CommonUI.ShowAlert(oContext, sTitle, sMessage);
    }
}
