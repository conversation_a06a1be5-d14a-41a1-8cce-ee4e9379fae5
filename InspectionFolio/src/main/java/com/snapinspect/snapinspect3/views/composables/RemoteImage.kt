package com.snapinspect.snapinspect3.views.composables

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import android.util.Size
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.CircularProgressIndicator
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.snapinspect.snapinspect3.Helper.CommonHelper
import com.snapinspect.snapinspect3.R
import com.squareup.picasso.Picasso
import com.squareup.picasso.Target
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.io.File

@Composable
fun RemoteImage(
    imageUrl: String?, 
    size: Size, 
    localFilePath: Uri? = null,
    modifier: Modifier = Modifier,
    didDownloadImage: (Bitmap) -> Unit = {},
) {
    var loadedBitmap by remember { mutableStateOf<Bitmap?>(null) }
    var isLoading by remember { mutableStateOf(true) }
    var isError by remember { mutableStateOf(false) }
    val coroutineScope = rememberCoroutineScope()

    val target = rememberUpdatedState(object : Target {
        override fun onBitmapLoaded(bitmap: Bitmap, from: Picasso.LoadedFrom) {
            loadedBitmap = bitmap
            isLoading = false
            coroutineScope.launch(Dispatchers.IO) {
                // Save image to local storage if needed
                if (from == Picasso.LoadedFrom.NETWORK) {
                    localFilePath?.path?.let { path ->
                        if (!CommonHelper.bFileExist(path)) {
                            CommonHelper.SaveImage(path, bitmap, 100)
                        }
                    }
                    didDownloadImage(bitmap)
                }
            }
        }

        override fun onBitmapFailed(e: Exception, errorDrawable: Drawable?) {
            isLoading = false
            isError = true
        }

        override fun onPrepareLoad(placeHolderDrawable: Drawable?) {
            isLoading = true
            isError = false
        }
    })

    // If imageUrl is null or empty, show placeholder image immediately
    if (imageUrl.isNullOrEmpty()) {
        isError = true
        isLoading = false
    } else {
        DisposableEffect(imageUrl) {
            val imageFile = File(imageUrl)
            if (imageFile.exists()) {
                Picasso.get()
                    .load(imageFile)
                    .resize(size.width, size.height)
                    .centerCrop()
                    .into(target.value)
            } else {
                Picasso.get()
                    .load(imageUrl)
                    .resize(size.width, size.height)
                    .centerCrop()
                    .into(target.value)
            }

            onDispose {
                Picasso.get().cancelRequest(target.value)
            }
        }
    }

    Box(modifier = modifier.size(size.width.dp, size.height.dp).clip(RoundedCornerShape(8.dp))) {
        when {
            isLoading -> LoadingIndicator()
            isError -> ImagePlaceholder()
            loadedBitmap != null -> DisplayImage(bitmap = loadedBitmap!!)
        }
    }
}

@Composable
fun LoadingIndicator(
    size: Dp = 10.dp,
    color: Color = colorResource(id = R.color.light_gray_color)
) {
    Box(modifier = Modifier.fillMaxSize()) {
        CircularProgressIndicator(
            modifier = Modifier
                .size(size)
                .align(Alignment.Center),
            color = color,
            strokeWidth = 2.dp
        )
    }
}

@Composable
fun ImagePlaceholder() {
    Image(
        painter = painterResource(R.drawable.no_image_available),
        contentDescription = null,
        contentScale = ContentScale.Fit,
        modifier = Modifier.fillMaxSize()
    )
}

@Composable
fun DisplayImage(bitmap: Bitmap) {
    Image(
        bitmap = bitmap.asImageBitmap(),
        contentDescription = null,
        contentScale = ContentScale.Crop,
        modifier = Modifier.fillMaxSize()
    )
}