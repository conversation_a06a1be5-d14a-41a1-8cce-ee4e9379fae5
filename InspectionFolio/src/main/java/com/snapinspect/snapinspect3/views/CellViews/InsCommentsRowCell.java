package com.snapinspect.snapinspect3.views.CellViews;

import android.annotation.SuppressLint;
import android.content.Context;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.appcompat.widget.LinearLayoutCompat;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonRequest;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Comment;
import com.snapinspect.snapinspect3.IF_Object.ai_User;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.CircleView;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.squareup.picasso.Picasso;

/**
 * <AUTHOR> 13.02.18
 */
public class InsCommentsRowCell extends LinearLayoutCompat {
    private TextView tvName, tvShortName, tvDesc, tvTime;
    private ImageView ivComment;
    private RelativeLayout circleView;
    private CircleView nameBGView;
    private final Context oContext;

    public InsCommentsRowCell(Context context) {
        super(context);
        oContext = context;
        initLayout();
    }

    @SuppressLint("SetTextI18n")
    public void displayComment(ai_Comment oComment, boolean isFirstRow) {
        circleView.setVisibility(isFirstRow ? VISIBLE : GONE);
        tvName.setVisibility(isFirstRow ? VISIBLE : GONE);

        int themeColor = oContext.getResources().getColor(oComment.colorResId);
        nameBGView.setCircleColor(themeColor);
        tvName.setTextColor(themeColor);

        ai_User oUser = CommonDB.GetUserSugar(oComment.iCustomerID);
        String[] names = oUser.sName.split(" ");
        if (names != null) {
            String shortName = names[0].substring(0,1).toUpperCase();
            if (names.length > 1) {
                shortName += names[names.length-1].substring(0,1).toUpperCase();
            }

            tvShortName.setText(shortName);
        }

        tvName.setText(oUser.sName);
        tvTime.setText(oComment.sTime);

        if (isComment(oComment) || isUser(oComment)) {
            tvDesc.setVisibility(VISIBLE);
            ivComment.setVisibility(GONE);
            tvDesc.setText(StringUtils.convertHtml(attributedString(oComment.sDescription, '@', ' ')));
        } else if (isPhoto(oComment)) {
            tvDesc.setVisibility(GONE);
            ivComment.setVisibility(VISIBLE);
            getDownloadableUrl(oComment);
            //Picasso.with(getContext()).load(oComment.sPhotoUrl).placeholder(R.drawable.image_placeholder).into(ivComment);
        }
    }

    private void getDownloadableUrl(final ai_Comment oComment ) {
        if (oComment.sPhotoUrl != null && oComment.sPhotoUrl.length() > 0){
            Picasso.get().load(oComment.sPhotoUrl).placeholder(R.drawable.image_placeholder).into(ivComment);
        }
        else {
            CommonRequest.requestURL(oContext, "/IOAPI/GetAssetFile", () -> {
                RequestParams oParams = new RequestParams();
                oParams.add("iCustomerID", CommonHelper.GetPreferenceString(oContext, "iCustomerID"));
                oParams.add("sToken", CommonHelper.GetPreferenceString(oContext, "sToken"));
                oParams.add("iFileID", "" + oComment.sDescription);
                return oParams;
            }, (response, error) -> {
                try {
                    if (response != null) {
                        String url = response.getString("sDownloadURL");
                        oComment.sPhotoUrl = url;
                        Picasso.get().load(url).placeholder(R.drawable.image_placeholder).into(ivComment);
                    }
                } catch (Exception exception) {
                    ai_BugHandler.ai_Handler_Exception(exception);
                }
            });
        }
    }

    private String attributedString(String text, Character pat1, Character pat2) {
        String updated = "";
        int start = -1; // '(' position in string
        int end = 0; // ')' position in string
        for(int i = 0; i < text.length(); i++) {

            if (i == text.length() -1) {
                if (start == -1 && end == 0) {
                    updated = text;
                } else {
                    if (start < end) {
                        if (end < i) {
                            String normalStr = text.substring(end, i+1);

                            updated += normalStr;
                        }
                    } else {
                        String boldStr = text.substring(start, i+1);
                        updated = updated + "<b>" + boldStr + "</b>";
                    }
                }
            } else {

                if (text.charAt(i) == pat1) {
                    start = i;

                    if (start > end) {
                        String normalStr = text.substring(end, start);

                        updated += normalStr;updated += normalStr;
                    }
                } else if (text.charAt(i) == pat2 && start >= end) {

                    end = i;

                    if (end > start && start >= 0) {
                        String boldStr = text.substring(start, end);
                        updated = updated + "<b>" + boldStr + "</b>";
                    } else {
                        end = 0;
                    }
                }

            }
        }

        if (updated.isEmpty()){
            updated = text;
        }

        return updated;
    }

    private boolean isComment(ai_Comment oComment) {
        return oComment.sType.equalsIgnoreCase("c");
    }

    private boolean isUser(ai_Comment oComment) {
        return oComment.sType.equalsIgnoreCase("a");
    }

    private boolean isPhoto(ai_Comment oComment) {
        return oComment.sType.equalsIgnoreCase("f");
    }

    private void initLayout() {
        setOrientation(VERTICAL);
        inflate(getContext(), R.layout.view_notes_cell, this);

        int smallPadding = getResources().getDimensionPixelSize(R.dimen.margin_smallest);
        setPadding(0, smallPadding, 0, smallPadding);

        circleView = findViewById(R.id.view_head_indicator);
        nameBGView = findViewById(R.id.name_bg_view);
        tvName = findViewById(R.id.view_note_cell_tv_name);
        tvShortName = findViewById(R.id.view_note_cell_short_name);
        tvTime = findViewById(R.id.view_note_cell_tv_time);
        tvDesc = findViewById(R.id.view_note_cell_tv_desc);
        ivComment = findViewById(R.id.view_note_cell_iv_comment);
    }

}
