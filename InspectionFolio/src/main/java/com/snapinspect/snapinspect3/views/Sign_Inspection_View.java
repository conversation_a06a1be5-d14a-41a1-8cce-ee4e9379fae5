package com.snapinspect.snapinspect3.views;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.InputType;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activity.if_sign;
import com.snapinspect.snapinspect3.activitynew.if_InsItemFloorPlans;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/*
 * @Created by Osama on 14/06/14.
 */
public class Sign_Inspection_View extends FrameLayout {
    private ArrayList<ai_InsItem> lsInsItem;
    private Context oContext;
    private int position;
    private boolean bFullInspection = true;
    private HashMap<ai_InsItem, Integer> mIdMap = new HashMap<>();
    private Listener mListener;
    private ai_InsItem oPInsItem;

    public interface Listener {
        void onRefresh();
    }

    public Sign_Inspection_View(Context context) {
        super(context);
    }

    public Sign_Inspection_View(ArrayList<ai_InsItem> _lsInsItem, Context _oContext, Listener listener, ai_InsItem _oPInsItem, boolean _bFull, int pos) {
        super(_oContext);

        oContext = _oContext;
        lsInsItem = _lsInsItem;
        mListener = listener;
        oPInsItem = _oPInsItem;
        position = pos;
        bFullInspection = _bFull;
        for (int i = 0; i < _lsInsItem.size(); ++i) {
            mIdMap.put(_lsInsItem.get(i), i);
        }

        setLayoutParams(new FrameLayout.LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT));
        setPadding(0, CommonHelper.pxFromDp(oContext, 10), 0, CommonHelper.pxFromDp(oContext, 10));
        setView();
    }

    public void setView() {
        LayoutInflater inflater = (LayoutInflater)oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View row = inflater.inflate(R.layout.view_ins_sign, this, false);
        try {
            final ai_InsItem oInsItem = lsInsItem.get(position);
            long lInsItemID = oInsItem.getId();
            LinearLayout headLayout = row.findViewById(R.id.view_head);
            CommonUI_InsItem.OneViewInspection_Label(oContext, oInsItem, bFullInspection, headLayout);

            DisplayMetrics metrics = oContext.getResources().getDisplayMetrics();
            boolean bReview = CommonValidate.bItemReviewEnabled(oInsItem);
            List<ai_Photo> lsPhoto = CommonDB.GetPhotoByInsItemIDSugar((int) lInsItemID);
            String[] lsOption = CommonConfig.MenuText_ItemsView_Sign_Type(oContext, false, bReview, lsPhoto);

            if (lsOption.length > 0) {
                ImageButton menuBtn = new ImageButton(oContext);
                CommonUI_InsItem.OneViewInspection_CommandButton(oContext, menuBtn, oInsItem, bReview);
                menuBtn.setOnClickListener(v -> new MaterialDialog.Builder(oContext)
                        .title(R.string.alert_title_action)
                        .items(lsOption)
                        .itemsCallbackSingleChoice(-1, (dialog, view, which, text) -> {
                            ProcessAction(text.toString(), oInsItem, position);
                            return true;
                        })
                        .negativeText(R.string.md_cancel_label)
                        .show());
                headLayout.addView(menuBtn);
            }

            TextView oInstruction = row.findViewById(R.id.txt_Sign_Inst);
            CommonUI_InsItem.OneViewInspection_InstructionLabel(oContext, oInstruction, CommonDB.GetInstruction(oInsItem, bFullInspection));

            Button oButton = row.findViewById(R.id.btn_Sign);
            oButton.setTag((int) lInsItemID);

            LinearLayout signLayout = row.findViewById(R.id.ll_Sign);
            if (lsPhoto != null && lsPhoto.size() == 1) {
                ai_Photo oPhoto = lsPhoto.get(0);
                if (!StringUtils.isEmpty(oPhoto.sFile) &&
                        CommonHelper.bFileExist(oPhoto.getFile()) && CommonHelper.GetFileLength(oPhoto.getFile()) > 0) {
                    ImageView oImageView = row.findViewById(R.id.iv_Sign);
                    signLayout.setVisibility(View.VISIBLE);
                    oImageView.setVisibility(VISIBLE);
                    oImageView.setPadding(10, 10, 10, 10);
                    //oImageView.setPadding(50,10,50,10);
                    oImageView.setTag(lsPhoto.get(0).getId());

                    Bitmap myBitmap = BitmapFactory.decodeFile(lsPhoto.get(0).getFile());
                    oImageView.setImageBitmap(Bitmap.createScaledBitmap(myBitmap, ((int) metrics.density * 220), ((int) metrics.density * 110), true));
                    oButton.setVisibility(GONE);
                } else if (oPhoto.bUploaded && oPhoto.iSPhotoID > 0) {
                    signLayout.setVisibility(View.GONE);
                    oButton.setVisibility(VISIBLE);
                    oButton.setText(R.string.btn_sign_click_to_view);
                    oButton.setMinWidth(200);
                    oButton.setOnClickListener(view -> {
                        CommonUI_InsItem.downloadPhoto(oContext, oPhoto,
                            () -> CommonUI_InsItem.broadcastInsItemSaved(oContext, oPInsItem.getId(), position));
                    });
                }
            } else {
                signLayout.setVisibility(View.GONE);
                oButton.setVisibility(VISIBLE);
                oButton.setText(R.string.btn_signature);
                oButton.setMinWidth(200);
                oButton.setOnClickListener(view -> {
                    final int iID = Integer.parseInt(view.getTag().toString());
                    Intent oIntent = new Intent(oContext, if_sign.class);
                    oIntent.putExtra(Constants.Extras.iInsItemID, iID);

                    oContext.startActivity(oIntent);
                });
            }
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "Ins_Sign_Adapter.getView", ex, oContext);
        }

        addView(row);

        if (!oPInsItem.bInspectionByPassCompulsory && oPInsItem.checkIfNeedValidatingCompulsoryItem(null)) {
            final ai_InsItem oInsItem = lsInsItem.get(position);
            if (!oInsItem.checkIfNeedValidatingCompulsoryItem(null) || !oInsItem.hasCompulsoryConfigs())
                return;
            // mark icon
            ImageButton button = new ImageButton(oContext);
            button.setBackgroundResource(R.color.transparent);
            button.setImageResource(R.drawable.icon_exclamation_sign);
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                    CommonHelper.pxFromDp(oContext, CommonUI_InsItem.INSPECT_CELL_MARGIN_LEFT), FrameLayout.LayoutParams.MATCH_PARENT);
            params.setMargins(0, CommonHelper.pxFromDp(oContext, 10), 0, 0);
            button.setLayoutParams(params);
            addView(button, 0);
        }
    }

    public void ProcessAction(String sSelectedText, ai_InsItem oInsItem, int iPosition){
        if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_DuplicateItem)) {
            Intent intent = new Intent(Constants.Broadcasts.sActionInsItem);
            intent.putExtra("InsItemID", oInsItem.getId());
            LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
        } else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_DeleteItem)) {
            if (!CommonValidate.bEditInspectionNotAllowDeleteItem(oContext, oInsItem.getId())) return;
            new MaterialDialog.Builder(oContext)
                    .title("Message")
                    .content(R.string.lbl_DeleteItemPrompt, oInsItem.sName)
                    .positiveText(R.string.tv_ok)
                    .onPositive((dialog, which) -> {
                        oInsItem.bDeleted = true;
                        oInsItem.save();
                        lsInsItem.remove(iPosition);

                        Intent intent = new Intent(Constants.Broadcasts.sDeleteInsItem);
                        // You can also include some extra data.
                        intent.putExtra(Constants.Extras.iPInsItemID, oInsItem.iPInsItemID);
                        intent.putExtra(Constants.Extras.iPosition, (iPosition - 1) > 0 ? (iPosition - 1) : 0);
                        LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
                    })
                    .show();
        } else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_EditItemName)){
            ShowEditAlert(oInsItem);
        } else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_RemoveSignature_Sign)){
            final int iID = oInsItem.getId().intValue();
            new MaterialDialog.Builder(oContext)
                    .title("Message")
                    .content(R.string.lbl_DeleteSignPrompt)
                    .positiveText(R.string.tv_ok)
                    .onPositive((dialog, which) -> {
                        if(CommonDB.DeletePhotoByInsItemID(iID, oContext))
                            mListener.onRefresh();
                    })
                    .negativeText(R.string.md_cancel_label)
                    .onNegative((dialog, which) -> dialog.dismiss())
                    .show();
        } else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_FloorPlan_DropPin)) {
            oContext.startActivity(if_InsItemFloorPlans.newIntent(oContext, oInsItem.getId()));
        }

    }



   /* public void SelectedRoomAction(int iOrder, final int iPosition, TextView oTextView, final ai_InsItem oInsItem){
        if (iOrder == 0) {
            Intent intent = new Intent(Constants.Broadcasts.sActionInsItem);

            intent.putExtra("InsItemID", oInsItem.getId());
            LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
        } else if (iOrder  == 1) {
//
//            AlertDialog.Builder builder = CommonUI.GetAlertBuilder("Message", oContext.getString(R.string.lbl_DeleteItemPrompt), oContext, true, false);
//
//            builder.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
//                public void onClick(DialogInterface dialog, int which) {
//
//                    oInsItem.bDeleted = true;
//                    oInsItem.save();
//                    lsInsItem.remove(iPosition);
//                    mIdMap = new HashMap<ai_InsItem, Integer>();
//                    for (int i = 0; i < lsInsItem.size(); ++i) {
//                        mIdMap.put(lsInsItem.get(i), i);
//                    }
//
//                    mListner.onRefresh();
//
//                }
//            });

//            builder.show();

            new MaterialDialog.Builder(oContext)
                    .title("Message")
                    .content(R.string.lbl_DeleteItemPrompt)
                    .positiveText(R.string.tv_ok)
                    .onPositive(new MaterialDialog.SingleButtonCallback() {
                        @Override
                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                            oInsItem.bDeleted = true;
                            oInsItem.save();
                            lsInsItem.remove(iPosition);
                            mIdMap = new HashMap<ai_InsItem, Integer>();
                            for (int i = 0; i < lsInsItem.size(); ++i) {
                                mIdMap.put(lsInsItem.get(i), i);
                            }

                            mListner.onRefresh();
                        }
                    })
                    .show();
        }
        else if (iOrder == 2){
            //int lTag = Integer.parseInt(sTag);
            ShowEditAlert(oInsItem);
        }
        else {
//            AlertDialog.Builder builder = new AlertDialog.Builder(oContext);

            final int iID = oInsItem.getId().intValue();
//            final int iID = Integer.parseInt(view.getTag().toString());
//            builder.setMessage(oContext.getString(R.string.lbl_DeleteSignPrompt));
//            builder.setPositiveButton("Yes", new DialogInterface.OnClickListener() {
//                public void onClick(DialogInterface dialog, int which) {
//                    if(CommonDB.DeletePhotoByInsItemID(iID, oContext))
//                        mListner.onRefresh();
//
//                }
//            });
//            builder.setNegativeButton("No", new DialogInterface.OnClickListener() {
//                public void onClick(DialogInterface dialog, int which) {
//                    dialog.dismiss();
//                }
//            });
//            builder.show();

            new MaterialDialog.Builder(oContext)
                    .title("Message")
                    .content(R.string.lbl_DeleteSignPrompt)
                    .positiveText(R.string.tv_ok)
                    .onPositive(new MaterialDialog.SingleButtonCallback() {
                        @Override
                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                            if(CommonDB.DeletePhotoByInsItemID(iID, oContext))
                                mListner.onRefresh();
                        }
                    })
                    .negativeText(R.string.md_cancel_label)
                    .onNegative(new MaterialDialog.SingleButtonCallback() {
                        @Override
                        public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                            dialog.dismiss();
                        }
                    })
                    .show();
        }
    } */
    private void ShowEditAlert(final ai_InsItem oInsItem){
        try {

            new MaterialDialog.Builder(oContext)
                    .title("Edit Item")
                    .inputType(InputType.TYPE_CLASS_TEXT)
                    .input("Please enter Item Name ...", oInsItem != null ? oInsItem.sName : "", new MaterialDialog.InputCallback() {
                        @Override
                        public void onInput(MaterialDialog dialog, CharSequence input) {
                            String sValue = input.toString();

                            if (sValue == null || sValue.equalsIgnoreCase("")) {
                                Toast.makeText(oContext, "This field can not be empty.", Toast.LENGTH_LONG).show();
                            } else if (sValue.equalsIgnoreCase(oInsItem.sName)){

                            } else {
                                if (CommonDB_Inspection.bValidateDuplicateItemName(oInsItem.iPInsItemID, sValue)) {
                                    Toast.makeText(oContext, CommonUI_InsItem.sMessage_AddEditItem_DuplicateName, Toast.LENGTH_LONG).show();
                                } else {
                                    oInsItem.sName = sValue;
                                    oInsItem.sNameChanged = "c";
                                    oInsItem.save();
                                    CommonUI_InsItem.broadcastInsItemSaved(oContext, oPInsItem.getId(), position);
                                    mIdMap = new HashMap<ai_InsItem, Integer>();
                                    for (int i = 0; i < lsInsItem.size(); ++i) {
                                        mIdMap.put(lsInsItem.get(i), i);
                                    }
                                }
                            }

                            mListener.onRefresh();
                        }
                    }).show();
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_ins_full.ShowEditAlert", ex, oContext);
        }
    }

}
