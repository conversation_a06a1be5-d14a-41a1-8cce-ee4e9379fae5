package com.snapinspect.snapinspect3.views;

import android.app.Dialog;
import android.content.DialogInterface;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.*;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;
import androidx.fragment.app.DialogFragment;
import com.snapinspect.snapinspect3.R;
import org.jetbrains.annotations.NotNull;

import java.util.Calendar;

public class DateTimePickerDialog extends DialogFragment
        implements DatePicker.OnDateChangedListener, TimePicker.OnTimeChangedListener {

    public enum DateTimePickerMode {
        DATE,
        TIME,
        DATE_TIME;

        public boolean showsTime() {
            return this == TIME || this == DATE_TIME;
        }

        public boolean showsDate() {
            return this == DATE || this == DATE_TIME;
        }
    }
    private final Calendar calendar;
    private final DateTimePickerMode dateTimePickerMode;

    public interface OnDateTimeSetListener {
        void onDateTimeChanged(Calendar calendar);
        void onDateTimeCleaned();
        void dismiss();
    }

    private OnDateTimeSetListener dateTimeSetListener;

    public DateTimePickerDialog(Calendar calendar, DateTimePickerMode mode) {
        this.calendar = calendar;
        this.dateTimePickerMode = mode;
    }

    @Override @NonNull
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        LayoutInflater inflater = LayoutInflater.from(getActivity());
        View root = inflater.inflate(
                R.layout.dialog_datetime_picker,
                getActivity().findViewById(R.id.dialog_date_time)
        );

        DatePicker datePicker = root.findViewById(R.id.datePicker);
        datePicker.setOnDateChangedListener(this);
        datePicker.updateDate(
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );

        Button cancelBtn = root.findViewById(R.id.cancel_button);
        cancelBtn.setOnClickListener(v -> dismiss());

        Button doneBtn = root.findViewById(R.id.done_button);
        doneBtn.setOnClickListener(v -> {
            if (dateTimeSetListener != null) dateTimeSetListener.onDateTimeChanged(calendar);
            dismiss();
        });

        Button clearBtn = root.findViewById(R.id.clean_button);
        clearBtn.setOnClickListener(v -> {
            if (dateTimeSetListener != null) dateTimeSetListener.onDateTimeCleaned();
            dismiss();
        });

        // Tabs
        TabHost tabhost = root.findViewById(R.id.tabHost);
        tabhost.setup();

        if (dateTimePickerMode.showsDate()) {
            /// date
            TabHost.TabSpec dateSpec = tabhost.newTabSpec("Date");
            dateSpec.setContent(R.id.tab_date_picker);
            dateSpec.setIndicator(getString(R.string.title_date));
            tabhost.addTab(dateSpec);
        }

        // time
        if (dateTimePickerMode.showsTime()) {
            TimePicker timePicker = root.findViewById(R.id.timePicker);
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                timePicker.setHour(calendar.get(Calendar.HOUR_OF_DAY));
                timePicker.setMinute(calendar.get(Calendar.MINUTE));
            }
            timePicker.setIs24HourView(true);
            timePicker.setOnTimeChangedListener(this);
            timePicker.setVisibility(View.VISIBLE);
            /// time
            TabHost.TabSpec timeSpec = tabhost.newTabSpec("Date");
            timeSpec.setContent(R.id.tab_time_picker);
            timeSpec.setIndicator(getString(R.string.title_time));
            tabhost.addTab(timeSpec);
        }

        AlertDialog.Builder db = new AlertDialog.Builder(getContext());
        db.setView(root);
        return db.create();
    }

    public void setDateTimeSetListener(OnDateTimeSetListener listener) {
        dateTimeSetListener = listener;
    }

    @Override
    public void onDismiss(@NonNull @NotNull DialogInterface dialog) {
        super.onDismiss(dialog);
        if (dateTimeSetListener != null) dateTimeSetListener.dismiss();
    }

    @Override
    public void onDateChanged(DatePicker view, int year, int monthOfYear, int dayOfMonth) {
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, monthOfYear);
        calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
    }

    @Override
    public void onTimeChanged(TimePicker view, int hourOfDay, int minute) {
        calendar.set(Calendar.HOUR_OF_DAY, hourOfDay);
        calendar.set(Calendar.MINUTE, minute);
    }
}
