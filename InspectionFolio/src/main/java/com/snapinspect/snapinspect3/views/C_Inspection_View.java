package com.snapinspect.snapinspect3.views;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.text.InputType;
import android.util.DisplayMetrics;
import android.view.LayoutInflater;
import android.view.View;
import android.view.WindowManager;
import android.widget.*;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activity.if_NoticeList;
import com.snapinspect.snapinspect3.activitynew.if_InsItemFloorPlans;

import java.util.ArrayList;
import java.util.HashMap;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.*;

/*
 * @Created by o<PERSON><PERSON> on 07/06/17.
 */

public class C_Inspection_View extends LinearLayout {
    private Context oContext;
    private LayoutInflater inflater = null;
    private ai_InsItem oInsItem;
    private ai_InsItem oPInsItem;
    private boolean bFullInspection = true;
    private boolean bRequestInspection = false;
    private ArrayList<ai_InsItem> lsInsItem;
    private int position;
    private int iButtonPerRow;
    private final int iDefaultButtonWidth = 70;
    private final int iDefaultButtonHeight = 42;
    private float oDensity;
    private int iInspectionId;

    private HashMap<ai_InsItem, Integer> mIdMap = new HashMap<>();

    public C_Inspection_View(Context context) {
        super(context);
    }

    public C_Inspection_View(Context context,int iInspectionId, ai_InsItem oInsItem, ai_InsItem oPInsItem,
                             boolean bFull, boolean bRequestInspection, ArrayList<ai_InsItem>itemList, int pos) {
        super(context);

        this.oContext = context;
        this.oInsItem = oInsItem;
        this.oPInsItem = oPInsItem;
        this.bFullInspection = bFull;
        this.bRequestInspection = bRequestInspection;
        this.lsInsItem = itemList;
        this.position = pos;
        this.iInspectionId = iInspectionId;

        DisplayMetrics oMetrics = new DisplayMetrics();
        ((WindowManager) oContext.getSystemService(Context.WINDOW_SERVICE)).getDefaultDisplay().getMetrics(oMetrics);
        //Point size = new Point()
        //display.getSize(size);
        oDensity = oMetrics.density;

        iButtonPerRow = ((int)(oMetrics.widthPixels/oDensity) - CommonUI_InsItem.INSPECT_CELL_MARGIN_LEFT * 2) / iDefaultButtonWidth;

        for (int i = 0; i < lsInsItem.size(); ++i) {
            mIdMap.put(lsInsItem.get(i), i);
        }

        this.setLayoutParams(new LinearLayout.LayoutParams(LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        this.setPadding(0, CommonHelper.pxFromDp(oContext, 10), 0, CommonHelper.pxFromDp(oContext, 10));

        InspectionView();
    }

    private void InspectionView() {
        this.inflater = LayoutInflater.from(oContext);

        View view = inflater.inflate(R.layout.cell_ins_full, this, false);
        LinearLayout oParentLayout = view.findViewById(R.id.view_Full);
        LinearLayout headLayout = view.findViewById(R.id.view_head);

        CommonUI_InsItem.OneViewInspection_Label(oContext, oInsItem, bFullInspection, headLayout);
        final boolean bReview = CommonValidate.bItemReviewEnabled(oInsItem);
        String[] lsOption = CommonConfig.MenuText_ItemsView_C_Type(
                oContext, bRequestInspection, bReview, CommonHelper.hasEnabledFloorPlan(oContext));

        // add blueprint drop pin icon
        if (oInsItem.hasFloorPlanDropPins()) {
            ImageButton dropPinButton = new ImageButton(oContext);
            CommonUI_InsItem.OneViewInspection_FloorPlanButton(oContext, dropPinButton);
            headLayout.addView(dropPinButton);
        }

        if (lsOption.length > 0) {
            ImageButton menuBtn = new ImageButton(oContext);
            CommonUI_InsItem.OneViewInspection_CommandButton(oContext, menuBtn, oInsItem, bReview);
            menuBtn.setOnClickListener(v -> {
                if (CommonHelper.isKioskMode(oContext)){
                    Toast.makeText(oContext, "You can not edit this inspection.", Toast.LENGTH_LONG).show();
                    return;
                }
                new MaterialDialog.Builder(oContext)
                        .title(R.string.alert_title_action)
                        .items(lsOption)
                        .itemsCallbackSingleChoice(-1, (dialog, view1, which, text) -> {
                            ProcessAction(text.toString(), oInsItem, position);
                            return true;
                        })
                        .negativeText(R.string.md_cancel_label)
                        .show();
            });
            headLayout.addView(menuBtn);
        }

        TextView oInstruction = new TextView(oContext);
        CommonUI_InsItem.OneViewInspection_InstructionLabel(oContext, oInstruction, CommonDB.GetInstruction(oInsItem, bFullInspection));

        oParentLayout.addView(oInstruction);

        String sConfig1 = CommonHelper.GetConfig(1, oInsItem, oPInsItem);
        String sConfig2 = CommonHelper.GetConfig(2, oInsItem, oPInsItem);
        String sConfig3 = CommonHelper.GetConfig(3, oInsItem, oPInsItem);
        String sConfig4 = CommonHelper.GetConfig(4, oInsItem, oPInsItem);
        String sConfig5 = CommonHelper.GetConfig(5, oInsItem, oPInsItem);
        String sConfig6 = CommonHelper.GetConfig(6, oInsItem, oPInsItem);
        if (sConfig1 != null && !sConfig1.isEmpty()) {
            FrameLayout oFV1Layout = GetLayout(sConfig1, 1, oInsItem, inflater);
            oParentLayout.addView(oFV1Layout);
        }
        if (sConfig2 != null && !sConfig2.isEmpty()) {
            FrameLayout oFV1Layout = GetLayout(sConfig2, 2, oInsItem, inflater);
            oParentLayout.addView(oFV1Layout);
        }
        if (sConfig3 != null && !sConfig3.isEmpty()) {
            FrameLayout oFV1Layout = GetLayout(sConfig3, 3, oInsItem, inflater);
            oParentLayout.addView(oFV1Layout);
        }
        if (sConfig4 != null && !sConfig4.isEmpty()) {
            FrameLayout oFV1Layout = GetLayout(sConfig4, 4, oInsItem, inflater);
            oParentLayout.addView(oFV1Layout);
        }
        if (sConfig5 != null && !sConfig5.isEmpty()) {
            FrameLayout oFV1Layout = GetLayout(sConfig5, 5, oInsItem, inflater);
            oParentLayout.addView(oFV1Layout);
        }
        if (sConfig6 != null && !sConfig6.isEmpty()) {
            FrameLayout oFV1Layout = GetLayout(sConfig6, 6, oInsItem, inflater);
            oParentLayout.addView(oFV1Layout);
        }

        this.addView(view);
    }

    @SuppressLint("SetTextI18n")
    private FrameLayout GetLayout(String sFV1, final int iPosition, final ai_InsItem oInsItem, LayoutInflater inflater){
        try {
            // Hide the item if has the attribute "_bWeb" = 1
            if (CommonInsItem.getConfig_bWeb(sFV1)) {
                FrameLayout oLayout = new FrameLayout(oContext);
                FrameLayout.LayoutParams oParams1 = new FrameLayout.LayoutParams(
                        FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.WRAP_CONTENT);
                oLayout.setLayoutParams(oParams1);
                return oLayout;
            }

            String sValue = CommonHelper.GetValue(iPosition, oInsItem);
            FrameLayout oLayout = new FrameLayout(oContext);
            FrameLayout.LayoutParams oParams1 = new FrameLayout.LayoutParams(
                    LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
            oLayout.setLayoutParams(oParams1);

            int iControlType = CommonInsItem.getControlType(sFV1);
            if (iControlType == SI_CONTROL_TYPE_CHK) {
                CommonUI_InsItem.InsItem_RenderCHK(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, iButtonPerRow, iDefaultButtonWidth, iDefaultButtonHeight, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_SCHK) {
                CommonUI_InsItem.InsItem_RenderSCHK(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, iButtonPerRow, iDefaultButtonWidth, iDefaultButtonHeight, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_MCHK) {
                CommonUI_InsItem.InsItem_RenderMCHK(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, iButtonPerRow, iDefaultButtonWidth, iDefaultButtonHeight, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_CMT) {
                CommonUI_InsItem.InsItem_RenderCMT(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_PTO) {
                CommonUI_InsItem.InsItem_RenderPTO(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_NUM) {
                CommonUI_InsItem.InsItem_RenderNUM(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_LST) {
                CommonUI_InsItem.InsItem_RenderLST(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_DT) {
                CommonUI_InsItem.InsItem_RenderDT(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_SCAN) {
                CommonUI_InsItem.InsItem_RenderSCAN(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_SEL || iControlType == SI_CONTROL_TYPE_MSEL) {
                CommonUI_InsItem.InsItem_RenderSEL(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            } else if (iControlType == SI_CONTROL_TYPE_COST) {
                CommonUI_InsItem.InsItem_RenderCOST(oContext, oInsItem, oPInsItem, sFV1, sValue, iPosition, oLayout);
            }
            return oLayout;
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "Ins_C_Adapter.GetLayout", ex,oContext);
            FrameLayout oLayout = new FrameLayout(oContext);
            FrameLayout.LayoutParams oParams1 = new FrameLayout.LayoutParams(
                    LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
            oLayout.setLayoutParams(oParams1);
            return oLayout;
        }
    }

    public void ProcessAction(String sSelectedText, ai_InsItem oInsItem, int iPosition){
        if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_DuplicateItem)) {
            Intent intent = new Intent(Constants.Broadcasts.sActionInsItem);
            // You can also include some extra data.
            intent.putExtra("InsItemID", oInsItem.getId());
            LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
        }
        else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_EditItemName)){

            ShowEditAlert(oInsItem);
        }
        else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_DeleteItem)) {
            if (!CommonValidate.bEditInspectionNotAllowDeleteItem(oContext, oInsItem.getId())) return;
            new MaterialDialog.Builder(oContext)
                    .title("Message")
                    .content(R.string.lbl_DeleteItemPrompt, oInsItem.sName)
                    .positiveText(R.string.tv_ok)
                    .onPositive((dialog, which) -> {
                        oInsItem.bDeleted = true;
                        oInsItem.save();
                        lsInsItem.remove(iPosition);

                        Intent intent = new Intent(Constants.Broadcasts.sDeleteInsItem);
                        // You can also include some extra data.
                        intent.putExtra(Constants.Extras.iPInsItemID, oInsItem.iPInsItemID);
                        intent.putExtra(Constants.Extras.iPosition, Math.max((iPosition - 1), 0));
                        LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
                    })
                    .show();
        }
        else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_ItemTask)){
            oContext.startActivity(if_NoticeList.newIntent(oContext, oInsItem.iInsID, oInsItem.getId()));
        }
        else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_ReviewNotes)){
            CommonUI.ShowAlert(oContext, "Review Notes", CommonValidate.sItemReviewNotes(oInsItem));
        } else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_FloorPlan_DropPin)) {
            oContext.startActivity(if_InsItemFloorPlans.newIntent(oContext, oInsItem.getId()));
        }
    }

    private void ShowEditAlert(final ai_InsItem oInsItem){
        try {
            new MaterialDialog.Builder(oContext)
                    .title("Edit Item")
                    .inputType(InputType.TYPE_CLASS_TEXT)
                    .input("Please enter Item Name ...", oInsItem != null ? oInsItem.sName : "", (dialog, input) -> {
                        String sValue = input.toString();
                        if (sValue.isEmpty()) {
                            Toast.makeText(oContext, "This field can not be empty.", Toast.LENGTH_LONG).show();
                        } else if (sValue.equalsIgnoreCase(oInsItem.sName)){

                        } else {
                             if (CommonDB_Inspection.bValidateDuplicateItemName(oInsItem.iPInsItemID, sValue)) {
                                Toast.makeText(oContext, CommonUI_InsItem.sMessage_AddEditItem_DuplicateName, Toast.LENGTH_LONG).show();
                            } else {
                                oInsItem.sName = sValue;
                                oInsItem.sNameChanged = "c";
                                oInsItem.save();
                                CommonUI_InsItem.broadcastInsItemSaved(oContext, oPInsItem.getId(), position);
                                mIdMap = new HashMap<>();
                                for (int i = 0; i < lsInsItem.size(); ++i) {
                                    mIdMap.put(lsInsItem.get(i), i);
                                }
                            }
                        }
                    }).show();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_ins_full.ShowEditAlert", ex, oContext);
        }
    }
}
