package com.snapinspect.snapinspect3.views.tasks

import android.app.DatePickerDialog
import android.content.Context
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import com.snapinspect.snapinspect3.Helper.CommonUI
import com.snapinspect.snapinspect3.Helper.Constants
import com.snapinspect.snapinspect3.IF_Object.ai_CustomInfo
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.util.DateUtils
import com.snapinspect.snapinspect3.views.composables.*
import org.json.JSONObject
import java.text.SimpleDateFormat
import java.util.*

data class ai_CustomInfoGroup(
    val sGroup: String,
    var lsCustomInfo: List<ai_CustomInfo>
)

fun List<ai_CustomInfo>.groupByGroup(): List<ai_CustomInfoGroup> {
    return this.groupBy {
        it.sGroup.ifEmpty { Constants.Values.kCustomInfoDefaultLabel }
    }
    .map { (group, infos) ->
        ai_CustomInfoGroup(sGroup = group, lsCustomInfo = infos)
    }
    .sortedByDescending { it.sGroup == Constants.Values.kCustomInfoDefaultLabel }
}

@Composable
fun TaskCustomInfosView(
    groupedCustomInfos: List<ai_CustomInfoGroup>,
    onCustomInfoChanged: (ai_CustomInfo) -> Unit,
    onFileAction: (ai_CustomInfo, AttachmentAction, TaskCustomInfoFile?) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(dimens.zero)
    ) {
        groupedCustomInfos.forEach { group ->
            ExpandableColumn(
                title = group.sGroup,
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = dimens.normal)
                        .padding(bottom = dimens.semiMedium),
                    verticalArrangement = Arrangement.spacedBy(dimens.semiMedium)
                ) {
                    group.lsCustomInfo.forEach { customInfo ->
                        TaskCustomInfoRow(
                            customInfo = customInfo,
                            onCustomInfoChanged = onCustomInfoChanged,
                            fileAction = onFileAction
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun TaskCustomInfoRow(
    customInfo: ai_CustomInfo,
    onCustomInfoChanged: (ai_CustomInfo) -> Unit,
    fileAction: (ai_CustomInfo, AttachmentAction, TaskCustomInfoFile?) -> Unit = { _, _ , _ -> }
) {
    when (customInfo.sQType) {
        ai_CustomInfo.QuestionType.MULTIPLE_CHECK -> TaskCustomRowMultipleCheckView(customInfo, onCustomInfoChanged)
        ai_CustomInfo.QuestionType.FILE -> TaskCustomRowFileView(customInfo, fileAction)
        ai_CustomInfo.QuestionType.CHECK -> TaskCustomRowCheckView(customInfo, onCustomInfoChanged)
        ai_CustomInfo.QuestionType.RADIO -> TaskCustomRowRadioView(customInfo, onCustomInfoChanged)
        ai_CustomInfo.QuestionType.TEXT_AREA -> TaskCustomRowTextAreaView(customInfo, onCustomInfoChanged)
        ai_CustomInfo.QuestionType.TEXT -> TaskCustomRowTextView(customInfo, onCustomInfoChanged)
        ai_CustomInfo.QuestionType.DATE -> TaskCustomRowDateView(customInfo, onCustomInfoChanged)
        null -> {} // Handle null case
    }
}

@Composable
private fun TaskCustomRowMultipleCheckView(
    customInfo: ai_CustomInfo,
    onCustomInfoChanged: (ai_CustomInfo) -> Unit
) {
    val selectedOptions = remember(customInfo.sValue) {
        customInfo.sValue.takeIf { it.isNotEmpty() }?.split(",") ?: emptyList()
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(dimens.smaller)
    ) {
        TaskCustomRowTitleView(title = customInfo.sLabel)
        
        customInfo.options.forEach { option ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable {
                        val newSelection = if (selectedOptions.contains(option)) {
                            selectedOptions - option
                        } else {
                            selectedOptions + option
                        }
                        val newCustomInfo = customInfo.copy().apply {
                            sValue = newSelection.joinToString(",")
                        }
                        onCustomInfoChanged(newCustomInfo)
                    }
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Checkbox(
                    checked = selectedOptions.contains(option),
                    onCheckedChange = null
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = option,
                    style = TextStyle(
                        fontSize = 16.sp,
                        color = Color(0xFF4A4A4A)
                    )
                )
            }
        }
    }
}

enum class AttachmentAction {
    UPLOAD_FILE,
    UPLOAD_PHOTO,
    TAKE_PHOTO,
    VIEW_FILE,
    DELETE_FILE;

    fun getTitle(context: Context): String {
        return when (this) {
            UPLOAD_FILE -> ContextCompat.getString(context, R.string.upload_file)
            UPLOAD_PHOTO -> ContextCompat.getString(context, R.string.upload_photo)
            TAKE_PHOTO -> ContextCompat.getString(context, R.string.take_photo)
            VIEW_FILE -> ContextCompat.getString(context, R.string.view_file)
            DELETE_FILE -> ContextCompat.getString(context, R.string.delete_file)
        }
    }
}

@Composable
private fun TaskCustomRowFileView(
    customInfo: ai_CustomInfo,
    fileAction: (ai_CustomInfo, AttachmentAction, TaskCustomInfoFile?) -> Unit
) {
    val context = LocalContext.current
    val fileInfo = customInfo.sValue.takeUnless(String::isEmpty)?.let(TaskCustomInfoFile::fromJson)
    val availableActions by remember {
        derivedStateOf {
            if (fileInfo != null) {
                listOf(AttachmentAction.VIEW_FILE, AttachmentAction.DELETE_FILE)
            } else {
                listOf(AttachmentAction.UPLOAD_FILE, AttachmentAction.UPLOAD_PHOTO, AttachmentAction.TAKE_PHOTO)
            }
        }
    }

    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(dimens.smaller)
    ) {
        TaskCustomRowTitleView(title = customInfo.sLabel)

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    CommonUI.showOptionsDialog(
                        context,
                        ContextCompat.getString(context, R.string.please_select),
                        availableActions.map { it.getTitle(context) }.toTypedArray()
                    ) { dialog, _, which, _ ->
                        fileAction(customInfo, availableActions[which], fileInfo)
                        dialog.dismiss()
                        true
                    }
                }
                .padding(vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                painter = painterResource(R.drawable.icon_custom_info_attachment),
                contentDescription = null,
                modifier = Modifier.size(24.dp)
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = fileInfo?.fileName ?: "No file chosen",
                style = TextStyle(
                    fontSize = 16.sp,
                    color = Color(0xFF4A4A4A)
                )
            )
        }
    }
}

@Composable
private fun TaskCustomRowCheckView(
    customInfo: ai_CustomInfo,
    onCustomInfoChanged: (ai_CustomInfo) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(dimens.smaller)
    ) {
        TaskCustomRowTitleView(title = customInfo.sLabel)
        
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    val newCustomInfo = customInfo.copy().apply {
                        sValue = if (sValue == "1") "0" else "1"
                    }
                    onCustomInfoChanged(newCustomInfo)
                }
                .padding(vertical = 4.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Checkbox(
                checked = customInfo.sValue == "1",
                onCheckedChange = null,
                colors = CheckboxDefaults.colors(
                    checkedColor = colors.color3E6BFF,
                    uncheckedColor = colors.colorBEBEBE
                )
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = customInfo.sLabel,
                style = TextStyle(
                    fontSize = 16.sp,
                    color = Color(0xFF4A4A4A)
                )
            )
        }
    }
}

@Composable
private fun TaskCustomRowRadioView(
    customInfo: ai_CustomInfo,
    onCustomInfoChanged: (ai_CustomInfo) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(dimens.smaller)
    ) {
        TaskCustomRowTitleView(title = customInfo.sLabel)
        
        customInfo.options.forEach { option ->
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .clickable {
                        val newCustomInfo = customInfo.copy().apply {
                            sValue = option
                        }
                        onCustomInfoChanged(newCustomInfo)
                    }
                    .padding(vertical = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                RadioButton(
                    selected = customInfo.sValue == option,
                    onClick = null,
                    colors = RadioButtonDefaults.colors(
                        selectedColor = colors.color3E6BFF,
                        unselectedColor = colors.colorBEBEBE
                    )
                )
                Spacer(modifier = Modifier.width(8.dp))
                Text(
                    text = option,
                    style = TextStyle(
                        fontSize = 16.sp,
                        color = Color(0xFF4A4A4A)
                    )
                )
            }
        }
    }
}

@Composable
private fun TaskCustomRowTextAreaView(
    customInfo: ai_CustomInfo,
    onCustomInfoChanged: (ai_CustomInfo) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(dimens.small)
    ) {
        TaskCustomRowTitleView(title = customInfo.sLabel)
        
        FitHeightTextField(
            value = customInfo.sValue,
            onValueChange = {
                val newCustomInfo = customInfo.copy().apply {
                    sValue = it
                }
                onCustomInfoChanged(newCustomInfo)
            },
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(dimens.extraLarge),
            placeholderText = "Enter Info...",
            fontSize = dimensionResource(R.dimen.text_h5).value.sp,
            textColor = colors.color080808,
            borderColor = colors.colorDFDFDF,
            borderWidth = 1F,
            maxLines = 5,
            singleLine = false,
            cornerRadius = dimens.smaller
        )
    }
}

@Composable
private fun TaskCustomRowTextView(
    customInfo: ai_CustomInfo,
    onCustomInfoChanged: (ai_CustomInfo) -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(dimens.smaller)
    ) {
        TaskCustomRowTitleView(title = customInfo.sLabel)
        
        FitHeightTextField(
            value = customInfo.sValue,
            onValueChange = { 
                val newCustomInfo = customInfo.copy().apply {
                    sValue = it
                }
                onCustomInfoChanged(newCustomInfo)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(dimens.larger),
            placeholderText = "Enter Info...",
            textColor = colors.color080808,
            singleLine = true,
            borderColor = colors.colorDFDFDF,
            borderWidth = 1F,
            cornerRadius = dimens.smaller
        )
    }
}

@Composable
private fun TaskCustomRowDateView(
    customInfo: ai_CustomInfo,
    onCustomInfoChanged: (ai_CustomInfo) -> Unit
) {
    val context = LocalContext.current
    val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
    
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(dimens.smaller)
    ) {
        TaskCustomRowTitleView(title = customInfo.sLabel)
        
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .clickable {
                    val calendar = Calendar.getInstance()
                    if (customInfo.sValue.isNotEmpty()) {
                        try {
                            calendar.time = dateFormat.parse(customInfo.sValue) ?: Date()
                        } catch (e: Exception) {
                            // Use current date if parsing fails
                        }
                    }

                    DatePickerDialog(
                        context,
                        { _, year, month, dayOfMonth ->
                            calendar.set(year, month, dayOfMonth)
                            val newCustomInfo = customInfo.copy().apply {
                                sValue = dateFormat.format(calendar.time)
                            }
                            onCustomInfoChanged(newCustomInfo)
                        },
                        calendar.get(Calendar.YEAR),
                        calendar.get(Calendar.MONTH),
                        calendar.get(Calendar.DAY_OF_MONTH)
                    ).show()
                }
                .padding(vertical = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            Icon(
                painter = painterResource(R.drawable.calendar_icon),
                contentDescription = null
            )

            Text(
                text = if (customInfo.sValue.isNotEmpty()) {
                    val date = dateFormat.parse(customInfo.sValue)
                    date?.let { DateUtils.format(date, "MMM dd, yyyy") } ?: "No Date"
                } else "No Date",
                style = TextStyle(
                    fontSize = 16.sp,
                    color = Color(0xFF4A4A4A)
                )
            )
        }
    }
}

@Composable
private fun TaskCustomRowTitleView(title: String) {
    Text(
        text = "$title:",
        style = typography.normal.copy(
            fontWeight = FontWeight.Normal,
            color = Color(0xFF535353)
        )
    )
}

sealed class FileInfo(
    open val fileId: Int,
    open val fileName: String
) {
    abstract val fileIdKey: String
    abstract val fileNameKey: String

    fun toJson(): String = JSONObject().apply {
        put(fileIdKey, fileId)
        put(fileNameKey, fileName)
    }.toString() 
}

data class TaskCustomInfoFile(
    override val fileId: Int,
    override val fileName: String
) : FileInfo(fileId, fileName) {
    override val fileIdKey: String = KEY_FILE_ID
    override val fileNameKey: String = KEY_FILE_NAME

    companion object {
        const val KEY_FILE_ID = "f_id"
        const val KEY_FILE_NAME = "f_nm"

        fun fromJson(jsonStr: String): TaskCustomInfoFile? {
            return try {
                val json = JSONObject(jsonStr)
                TaskCustomInfoFile(
                    fileId = json.getInt(KEY_FILE_ID),
                    fileName = json.getString(KEY_FILE_NAME)
                )
            } catch (e: Exception) {
                null
            }
        }
    }
}

data class TaskCommentFile(
    override val fileId: Int,
    override val fileName: String
) : FileInfo(fileId, fileName) {
    override val fileIdKey: String = KEY_FILE_ID
    override val fileNameKey: String = KEY_FILE_NAME

    companion object {
        const val KEY_FILE_ID = "iFileID"
        const val KEY_FILE_NAME = "sFileName"

        fun fromJson(jsonStr: String): TaskCommentFile? {
            return try {
                val json = JSONObject(jsonStr)
                TaskCommentFile(
                    fileId = json.getInt(KEY_FILE_ID),
                    fileName = json.getString(KEY_FILE_NAME)
                )
            } catch (e: Exception) {
                null
            }
        }
    }
}