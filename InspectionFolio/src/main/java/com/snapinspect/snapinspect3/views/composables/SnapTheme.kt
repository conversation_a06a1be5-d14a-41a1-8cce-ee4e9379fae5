package com.snapinspect.snapinspect3.views.composables

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

// Theme data classes
data class SnapColors(
    val color050505: Color,
    val color080808: Color,
    val color2B2B2B: Color,
    val color4A4A4A: Color,
    val color535353: Color,
    val color656565: Color,
    val color6D7278: Color,
    val color929292: Color,
    val color9B9B9B: Color,
    val color9C9C9C: Color,
    val colorAAAAAA: Color,
    val colorB3B4B4: Color,
    val colorBEBEBE: Color,
    val colorD8D8D8: Color,
    val colorDFDFDF: Color,
    val colorE2E2E2: Color,
    val colorEBEBEB: Color,
    val colorF7F7F7: Color,
    val colorF9F9F9: Color,
    val color053BFF: Color,
    val color3E6BFF: Color,
    val color477BFF: Color,
    val color4E69FF: Color,
    val color08BC64: Color,
    val color31D264: Color,
    val colorFF3A3A: Color,
    val colorFF5151: Color,
    val colorFF6868: Color,
    val colorFF6969: Color,
    val color337ab7: Color,
)

data class SnapDimensions(
    val zero: Dp = 0.dp,
    val one: Dp = 1.dp,
    val smallest: Dp = 4.dp,
    val smaller: Dp = 6.dp,
    val small: Dp = 8.dp,
    val semiMedium: Dp = 10.dp,
    val medium: Dp = 12.dp,
    val extraMedium: Dp = 14.dp,
    val normal: Dp = 16.dp,
    val extraNormal: Dp = 20.dp,
    val extraExtraNormal: Dp = 25.dp,
    val large: Dp = 32.dp,
    val larger: Dp = 40.dp,
    val extraLarge: Dp = 50.dp,
    val xlarge: Dp = 86.dp,
    val xxlarge: Dp = 120.dp
)

data class SnapTypography(
    val h0: TextStyle = TextStyle(fontSize = 28.sp, fontWeight = FontWeight.Bold),
    val h1: TextStyle = TextStyle(fontSize = 24.sp, fontWeight = FontWeight.Bold),
    val h2: TextStyle = TextStyle(fontSize = 22.sp, fontWeight = FontWeight.Bold), 
    val h3: TextStyle = TextStyle(fontSize = 20.sp, fontWeight = FontWeight.SemiBold),
    val h4: TextStyle = TextStyle(fontSize = 18.sp, fontWeight = FontWeight.SemiBold),
    val h5: TextStyle = TextStyle(fontSize = 16.sp, fontWeight = FontWeight.SemiBold),
    val normal: TextStyle = TextStyle(fontSize = 14.sp, fontWeight = FontWeight.Normal),
    val small: TextStyle = TextStyle(fontSize = 12.sp, fontWeight = FontWeight.Normal),
    val smallest: TextStyle = TextStyle(fontSize = 11.sp, fontWeight = FontWeight.Normal)
)

// Local compositions
val LocalColors = staticCompositionLocalOf<SnapColors> { error("No SnapColors provided") }
val LocalDimensions = staticCompositionLocalOf<SnapDimensions> { error("No SnapDimensions provided") }
val LocalTypography = staticCompositionLocalOf<SnapTypography> { error("No SnapTypography provided") }

// Theme colors
private val DarkColors = SnapColors(
    color050505 = Color(0xFF050505), // "#050505"
    color080808 = Color(0xFF080808), // "#080808"
    color2B2B2B = Color(0xFF2B2B2B), // "#2B2B2B"
    color4A4A4A = Color(0xFF4A4A4A), // "#4A4A4A"
    color535353 = Color(0xFF535353), // "#535353"
    color656565 = Color(0xFF656565), // "#656565"
    color6D7278 = Color(0xFF6D7278), // "#6D7278"
    color929292 = Color(0xFF929292), // "#929292"
    color9B9B9B = Color(0xFF9B9B9B), // "#9B9B9B"
    color9C9C9C = Color(0xFF9C9C9C), // "#9C9C9C"
    colorAAAAAA = Color(0xFFAAAAAA), // "#AAAAAA"
    colorB3B4B4 = Color(0xFFB3B4B4), // "#B3B4B4"
    colorBEBEBE = Color(0xFFBEBEBE), // "#BEBEBE"
    colorD8D8D8 = Color(0xFFD8D8D8), // "#D8D8D8"
    colorDFDFDF = Color(0xFFDFDFDF), // "#DFDFDF"
    colorE2E2E2 = Color(0xFFE2E2E2), // "#E2E2E2"
    colorEBEBEB = Color(0xFFEBEBEB), // "#EBEBEB"
    colorF7F7F7 = Color(0xFFF7F7F7), // "#F7F7F7"
    colorF9F9F9 = Color(0xFFF9F9F9), // "#F9F9F9"
    color053BFF = Color(0xFF053BFF), // "#053BFF"
    color3E6BFF = Color(0xFF3E6BFF), // "#3E6BFF"
    color477BFF = Color(0xFF477BFF), // "#477BFF"
    color4E69FF = Color(0xFF4E69FF), // "#4E69FF"
    color08BC64 = Color(0xFF08BC64), // "#08BC64"
    color31D264 = Color(0xFF31D264), // "#31D264"
    colorFF3A3A = Color(0xFFFF3A3A), // "#FF3A3A"
    colorFF5151 = Color(0xFFFF5151), // "#FF5151"   
    colorFF6868 = Color(0xFFFF6868), // "#FF6868"
    colorFF6969 = Color(0xFFFF6969), // "#FF6969"
    color337ab7 = Color(0xFF337ab7),  // "#337ab7"
)

private val LightColors = SnapColors(
    color050505 = Color(0xFF050505), // "#050505"
    color080808 = Color(0xFF080808), // "#080808"
    color2B2B2B = Color(0xFF2B2B2B), // "#2B2B2B"
    color4A4A4A = Color(0xFF4A4A4A), // "#4A4A4A"
    color535353 = Color(0xFF535353), // "#535353"
    color656565 = Color(0xFF656565), // "#656565"
    color6D7278 = Color(0xFF6D7278), // "#6D7278"
    color929292 = Color(0xFF929292), // "#929292"
    color9B9B9B = Color(0xFF9B9B9B), // "#9B9B9B"
    color9C9C9C = Color(0xFF9C9C9C), // "#9C9C9C"
    colorAAAAAA = Color(0xFFAAAAAA), // "#AAAAAA"
    colorB3B4B4 = Color(0xFFB3B4B4), // "#B3B4B4"
    colorBEBEBE = Color(0xFFBEBEBE), // "#BEBEBE"
    colorD8D8D8 = Color(0xFFD8D8D8), // "#D8D8D8"
    colorDFDFDF = Color(0xFFDFDFDF), // "#DFDFDF"
    colorE2E2E2 = Color(0xFFE2E2E2), // "#E2E2E2"
    colorEBEBEB = Color(0xFFEBEBEB), // "#EBEBEB"
    colorF7F7F7 = Color(0xFFF7F7F7), // "#F7F7F7"
    colorF9F9F9 = Color(0xFFF9F9F9), // "#F9F9F9"
    color053BFF = Color(0xFF053BFF), // "#053BFF"
    color3E6BFF = Color(0xFF3E6BFF), // "#3E6BFF"
    color477BFF = Color(0xFF477BFF), // "#477BFF"
    color4E69FF = Color(0xFF4E69FF), // "#4E69FF"
    color08BC64 = Color(0xFF08BC64), // "#08BC64"
    color31D264 = Color(0xFF31D264), // "#31D264"
    colorFF3A3A = Color(0xFFFF3A3A), // "#FF3A3A"
    colorFF5151 = Color(0xFFFF5151), // "#FF5151"
    colorFF6868 = Color(0xFFFF6868), // "#FF6868"
    colorFF6969 = Color(0xFFFF6969), // "#FF6969"
    color337ab7 = Color(0xFF337ab7),  // "#337ab7"
)

@Composable
fun SnapTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    colors: SnapColors = if (darkTheme) DarkColors else LightColors,
    dimens: SnapDimensions = SnapDimensions(),
    typography: SnapTypography = SnapTypography(),
    content: @Composable () -> Unit
) {
    CompositionLocalProvider(
        LocalColors provides colors,
        LocalDimensions provides dimens,
        LocalTypography provides typography
    ) {
        MaterialTheme {
            content()
        }
    }
}

// Provide access to the color values
val colors: SnapColors
    @Composable
    @ReadOnlyComposable
    get() = LocalColors.current

// Provide access to the layout values
val dimens: SnapDimensions
    @Composable
    @ReadOnlyComposable
    get() = LocalDimensions.current

// Provide access to the typography values
val typography: SnapTypography
    @Composable
    @ReadOnlyComposable
    get() = LocalTypography.current