package com.snapinspect.snapinspect3.views;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.view.View;
import android.widget.Button;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.Projects.if_ProjectInspection;

import static com.snapinspect.snapinspect3.activitynew.Projects.if_ProjectInspection.InspectionStatus.IN_PROGRESS;

public class EmptyDataView extends FrameLayout {

    public interface Listener {
        void learnMore();
    }
    private Listener mListener;

    private TextView tvTitle;
    private TextView tvDescription;
    private ImageView screenshotView;
    private Button btnLearnMore;
    private final ViewType mViewType;
    private boolean mHasInspections = false;
    private if_ProjectInspection.InspectionStatus mInspectionStatus = IN_PROGRESS;

    public enum ViewType {
        PROJECTS, PROJECT_INSPECTIONS, FLOOR_PLANS;

        static ViewType fromOrdinal(int x) {
            return ViewType.values()[x];
        }
    }
    public EmptyDataView(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        inflate(context, R.layout.layout_projects_empty_data, this);

        TypedArray typedArray = getContext().getTheme().obtainStyledAttributes(attrs,
                R.styleable.EmptyDataView, 0, 0);
        try {
            int ordinal = typedArray.getInt(R.styleable.EmptyDataView_viewType, ViewType.PROJECTS.ordinal());
            mViewType = ViewType.fromOrdinal(ordinal);
        } finally {
            typedArray.recycle();
        }
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        tvTitle = findViewById(R.id.tv_title);
        tvDescription = findViewById(R.id.tv_description);
        screenshotView = findViewById(R.id.iv_screenshot);
        btnLearnMore = findViewById(R.id.btn_learn_more);
        btnLearnMore.setVisibility(View.INVISIBLE);

        btnLearnMore.setOnClickListener(v -> {
            if (mListener != null) {
                mListener.learnMore();
            }
        });
        updateViews();
    }

    private void updateViews() {
        switch (mViewType) {
            case PROJECTS:
                tvTitle.setText(R.string.title_projects_empty_data);
                tvDescription.setText(R.string.description_projects_empty_data);
                screenshotView.setImageDrawable(ContextCompat.getDrawable(getContext(),
                        R.drawable.ic_screenshot_no_active_projects));
                screenshotView.setVisibility(VISIBLE);
                btnLearnMore.setVisibility(VISIBLE);
                break;
            case PROJECT_INSPECTIONS:
                if (!mHasInspections) {
                    tvTitle.setText(R.string.title_project_inspections_empty_data);
                    tvDescription.setText(R.string.description_project_inspections_empty_data);
                    screenshotView.setImageDrawable(ContextCompat.getDrawable(getContext(),
                            R.drawable.ic_screenshot_no_pending_project_inspections));
                    screenshotView.setVisibility(VISIBLE);
                    btnLearnMore.setVisibility(VISIBLE);
                } else {
                    switch (mInspectionStatus) {
                        case IN_PROGRESS:
                            tvTitle.setText(R.string.title_project_inspections_empty_in_progress);
                            break;
                        case COMPLETED:
                            tvTitle.setText(R.string.title_project_inspections_empty_completed);
                            break;
                    }
                    tvDescription.setText(" ");
                    btnLearnMore.setVisibility(INVISIBLE);
                    screenshotView.setVisibility(INVISIBLE);
                }
                break;
            case FLOOR_PLANS:
                tvTitle.setText(R.string.title_floor_plans_empty_data);
                tvDescription.setText(R.string.description_floor_plans_empty_data);
                screenshotView.setImageDrawable(null);
                screenshotView.setVisibility(VISIBLE);
                btnLearnMore.setVisibility(VISIBLE);
                break;
        }
    }

    public void setHasInspections(boolean hasInspections) {
        mHasInspections = hasInspections;
        updateViews();
    }

    public void setInspectionStatus(if_ProjectInspection.InspectionStatus inspectionStatus) {
        this.mInspectionStatus = inspectionStatus;
        updateViews();
    }

    public void setListener(Listener listener) {
        mListener = listener;
    }
}
