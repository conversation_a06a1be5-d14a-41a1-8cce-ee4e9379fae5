package com.snapinspect.snapinspect3.views.tasks

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Add
import androidx.compose.material.icons.filled.CheckCircleOutline
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.snapinspect.snapinspect3.IF_Object.ai_Task
import com.snapinspect.snapinspect3.SI_DB.db_Tasks
import com.snapinspect.snapinspect3.util.StringUtils
import com.snapinspect.snapinspect3.views.composables.*

private object Constants {
    const val MAX_SUBTASK_DISPLAY = 5
    const val BUTTON_FONT_SIZE = 14
    const val CHECKMARK_SIZE = 20
    const val TITLE_FONT_SIZE = 17
    const val STATUS_FONT_SIZE = 14
    const val STATUS_SIZE = 35
}

@Composable
fun TaskSubTasksView(
    task: ai_Task,
    onViewAll: () -> Unit,
    onAddSubTask: () -> Unit,
    onViewSubTask: (ai_Task) -> Unit,
    onCompleteSubTask: (ai_Task) -> Unit
) {
    var showCompleteDialog by remember { mutableStateOf(false) }
    var selectedTask by remember { mutableStateOf<ai_Task?>(null) }

    ExpandableColumn(title = "Sub Tasks") {
        // Tasks List
        task.subTasks.take(Constants.MAX_SUBTASK_DISPLAY).forEach { subTask ->
            SubTaskCell(
                task = subTask,
                onComplete = {
                    selectedTask = subTask
                    showCompleteDialog = true
                },
                onView = { onViewSubTask(subTask) }
            )
        }

        // Footer
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            if (task.subTasks.size > Constants.MAX_SUBTASK_DISPLAY) {
                FooterButton(
                    text = "View All",
                    icon = Icons.Default.ChevronRight,
                    onClick = onViewAll,
                    reversed = true
                )
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            FooterButton(
                text = "Sub Task",
                icon = Icons.Default.Add,
                onClick = onAddSubTask
            )
        }
    }

    if (showCompleteDialog) {
        AlertDialog(
            onDismissRequest = { showCompleteDialog = false },
            title = { Text("Complete Task") },
            text = { Text("Are you sure to mark this task as Completed?") },
            confirmButton = {
                TextButton(onClick = {
                    selectedTask?.let { onCompleteSubTask(it) }
                    showCompleteDialog = false
                }) {
                    Text("Complete")
                }
            },
            dismissButton = {
                TextButton(onClick = { showCompleteDialog = false }) {
                    Text("Cancel")
                }
            }
        )
    }
}

@Composable
private fun SubTaskCell(
    task: ai_Task,
    onComplete: () -> Unit,
    onView: () -> Unit
) {
    Column {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = dimens.normal, vertical = dimens.small)
                .clickable(onClick = onView),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.CheckCircleOutline,
                contentDescription = "Complete",
                modifier = Modifier
                    .size(dimens.extraExtraNormal)
                    .clickable(onClick = { if (!task.bClosed) onComplete() }),
                tint = if (task.bClosed) Color.Green else Color.Gray
            )

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = task.sTitle,
                fontSize = Constants.TITLE_FONT_SIZE.sp,
                color = Color(0xFF080808)
            )

            Spacer(modifier = Modifier.weight(1f))

            task.sStatus?.takeIf { it.isNotEmpty() }?.let { status ->
                val backgroundColor = task.sStatusCode?.let { Color.hex(it) } ?: colors.colorDFDFDF
                Text(
                    text = StringUtils.getFirstLetters(status) ?: "",
                    style = TextStyle(
                        fontSize = Constants.STATUS_FONT_SIZE.sp,
                        fontWeight = FontWeight.SemiBold,
                        color = Color.White
                    ),
                    modifier = Modifier
                        .size(Constants.STATUS_SIZE.dp)
                        .background(
                            color = backgroundColor,
                            shape = CircleShape
                        )
                        .wrapContentSize(Alignment.Center)
                        .padding(horizontal = dimens.small, vertical = dimens.smallest),
                    textAlign = TextAlign.Center,
                )
            }
        }

        androidx.compose.material.Divider(
            color = colors.colorAAAAAA.copy(alpha = 0.5f),
            thickness = 0.5.dp,
            modifier = Modifier.padding(horizontal = dimens.extraNormal)
        )
    }
}

@Composable
private fun FooterButton(
    text: String,
    icon: ImageVector,
    onClick: () -> Unit,
    reversed: Boolean = false
) {
    Box(
        modifier = Modifier
            .dashedBorder(
                width = 1.dp,
                color = Color(0xFF3E6BFF),
                shape = RoundedCornerShape(10.dp)
            )
            .clickable(onClick = onClick)
            .padding(horizontal = 16.dp, vertical = 8.dp)
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center
        ) {
            if (reversed) {
                Text(
                    text = text,
                    fontSize = Constants.BUTTON_FONT_SIZE.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF3E6BFF)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color(0xFF3E6BFF)
                )
            } else {
                Icon(
                    imageVector = icon,
                    contentDescription = null,
                    tint = Color(0xFF3E6BFF)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text(
                    text = text,
                    fontSize = Constants.BUTTON_FONT_SIZE.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color(0xFF3E6BFF)
                )
            }
        }
    }
}

val ai_Task.subTasks: List<ai_Task>
    get() = db_Tasks.getTasks(iPropertyID, iSNotificationID)
