package com.snapinspect.snapinspect3.views.tasks

import android.content.Context
import android.util.Size
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.AlertDialog
import androidx.compose.material.Divider
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.snapinspect.snapinspect3.Helper.CommonDB
import com.snapinspect.snapinspect3.Helper.CommonHelper
import com.snapinspect.snapinspect3.Helper.CommonRequests
import com.snapinspect.snapinspect3.IF_Object.FileId
import com.snapinspect.snapinspect3.IF_Object.O_FileName
import com.snapinspect.snapinspect3.IF_Object.TaskAttachments
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.views.composables.*
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

enum class AttachmentType {
    PHOTO, VIDEO
}

data class TaskPhotoParams(
    val action: PhotoAction,
    val fileId: FileId,
    val photoIds: List<FileId>
)

data class TaskVideoParams(
    val action: VideoAction,
    val fileId: FileId?
)

@Composable
fun TaskAttachmentsView(
    attachments: TaskAttachments,
    onAddAttachment: (AttachmentType) -> Unit,
    onPhotoAction: (TaskPhotoParams) -> Unit,
    onVideoAction: (TaskVideoParams) -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = dimens.normal, vertical = dimens.semiMedium),
        horizontalArrangement = Arrangement.spacedBy(dimens.semiMedium),
        verticalAlignment = Alignment.Top
    ) {
        // Photos Section
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(dimens.small)
        ) {
            Text(
                text = "Photos",
                style = TextStyle(
                    fontSize = 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colors.colorB3B4B4
                )
            )

            Row(
                horizontalArrangement = Arrangement.spacedBy(dimens.small)
            ) {
                AddAttachmentButton(
                    type = AttachmentType.PHOTO,
                    count = attachments.photos.size,
                    onAdd = { onAddAttachment(it) }
                )

                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(dimens.smaller)
                ) {
                    items(attachments.photos) { photo ->
                        PhotoThumbView(
                            fileId = photo,
                            onAction = { action, photoId ->
                                onPhotoAction(TaskPhotoParams(action, photoId, attachments.photos))
                            }
                        )
                    }
                }
            }
        }

        // Mobile app doesn't support recording video for task yet
        attachments.video?.let { videoId ->
            getVideoThumbUrl(videoId)?.let {
                // Videos Section
                Column(
                    verticalArrangement = Arrangement.spacedBy(dimens.small)
                ) {
                    Text(
                        text = "Videos",
                        style = TextStyle(
                            fontSize = 16.sp,
                            fontWeight = FontWeight.SemiBold,
                            color = colors.colorB3B4B4
                        )
                    )

                    VideoThumbView(
                        fileId = videoId,
                        onAdd = { onAddAttachment(AttachmentType.VIDEO) },
                        onPlay = { onVideoAction(TaskVideoParams(VideoAction.PLAY, attachments.video)) },
                        onDelete = { onVideoAction(TaskVideoParams(VideoAction.DELETE, attachments.video)) }
                    )
                }
            }
        }
    }
}

@Composable
private fun AddAttachmentButton(
    type: AttachmentType,
    count: Int = 0,
    onAdd: (AttachmentType) -> Unit
) {
    Box(
        modifier = Modifier
            .size(48.dp)
            .clickable { onAdd(type) }
    ) {
        Image(
            painter = painterResource(
                id = when (type) {
                    AttachmentType.PHOTO -> R.drawable.icon_camera
                    AttachmentType.VIDEO -> R.drawable.ic_camera
                }
            ),
            contentDescription = null,
            modifier = Modifier
                .size(44.dp)
                .align(Alignment.Center)
        )

        if (type == AttachmentType.PHOTO) {
            Text(
                modifier = Modifier
                    .offset(x = 2.dp, y = (-1).dp)
                    .background(
                        color = if (count > 0) Color.Red else colors.color9C9C9C,
                        shape = CircleShape
                    )
                    .size(20.dp)
                    .align(Alignment.TopEnd)
                    .wrapContentHeight(Alignment.CenterVertically)
                    .wrapContentWidth(Alignment.CenterHorizontally),
                text = if (count > 0) count.toString() else "+",
                style = TextStyle(
                    fontSize = if (count > 0) 12.sp else 16.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.White
                )
            )
        }
    }
}

@Composable
private fun PhotoThumbView(
    fileId: FileId,
    onAction: (PhotoAction, FileId) -> Unit = { _, _ -> }
) {
    var photoUrl by remember { mutableStateOf<String?>(null) }
    var isLoading by remember { mutableStateOf(false) }
    var showActionSheet by remember { mutableStateOf(false) }
    val context = LocalContext.current

    LaunchedEffect(fileId) {
        isLoading = true
        photoUrl = getPhotoThumbUrl(context, fileId)
        isLoading = false
    }

    Box(
        modifier = Modifier
            .size(44.dp)
            .clip(CircleShape),
        contentAlignment = Alignment.Center
    ) {
        if (isLoading) {
            LoadingIndicator()
        } else {
            photoUrl?.let {
                RemoteImage(
                    imageUrl = photoUrl,
                    size = Size(44, 44),
                    modifier = Modifier
                        .clip(CircleShape)
                        .clickable {
                            onAction(PhotoAction.VIEW, fileId)
                        },
                    didDownloadImage = { bitmap ->
                        fileId.asPhoto()?.let { photo ->
                            O_FileName.getPhotoFileName().let { fileName ->
                                CommonHelper.SaveImage(fileName.sThumbNail, bitmap)
                                photo.apply {
                                    sThumb = fileName.sThumbNail
                                }
                                CommonDB.save(photo)
                            }
                        }
                    }
                )
            }
        }
    }

    if (showActionSheet) {
        AlertDialog(
            onDismissRequest = { showActionSheet = false },
            title = { Text("Photo In Cloud") },
            text = { Text("The photo is already in our Cloud Server. You can download to view it. Or just to view comments without download the photo.") },
            buttons = {
                Column {
                    PhotoAction.entries.forEach { action ->
                        TextButton(onClick = {
                            onAction(action, fileId)
                            showActionSheet = false
                        }) {
                            Text(action.title)
                        }
                    }
                    TextButton(onClick = { showActionSheet = false }) {
                        Text("Cancel")
                    }
                }
            }
        )
    }
}

enum class PhotoAction {
    DOWNLOAD,
    VIEW,
    DOWNLOAD_ALL;

    val title: String
        get() = when (this) {
            DOWNLOAD -> "Download Photo"
            VIEW -> "View Photo Only"
            DOWNLOAD_ALL -> "Download All Photos"
        }
}

enum class VideoAction {
    PLAY,
    DELETE;

    val title: String
        get() = when (this) {
            PLAY -> "Play Video"
            DELETE -> "Delete Video"
        }
}

@Composable
fun VideoThumbView(
    fileId: FileId?,
    onAdd: () -> Unit,
    onPlay: () -> Unit,
    onDelete: () -> Unit
) {
    var videoThumbUrl by remember { mutableStateOf<String?>(null) }
    val context = LocalContext.current

    LaunchedEffect(fileId) {
        videoThumbUrl = fileId?.let { getVideoThumbUrl(it) }
    }

    Box(
        modifier = Modifier
            .size(48.dp)
            .clip(CircleShape)
            .background(colors.colorDFDFDF)
    ) {
        videoThumbUrl?.takeIf { it.isNotEmpty() }?.let {
            RemoteImage(
                imageUrl = it,
                size = Size(48, 48),
                localFilePath = null,
                modifier = Modifier
                    .align(Alignment.Center)
                    .clickable {
                        // Mobile app only supports playing video
                        onPlay()
                        /*
                        CommonUI.showOptionsDialog(
                            context,
                            "Please select",
                            VideoAction.entries.map(VideoAction::title).toTypedArray()
                        ) { dialog, _, which, _ ->
                            when (VideoAction.entries[which]) {
                                VideoAction.PLAY -> onPlay()
                                VideoAction.DELETE -> onDelete()
                            }
                            dialog.dismiss()
                            true
                        }
                         */
                    },
                didDownloadImage = { bitmap ->
                    fileId?.asVideo()?.let { video ->
                        O_FileName.getVideoFileName().let { fileName ->
                            CommonHelper.SaveImage(fileName.sThumbNail, bitmap)
                            video.sThumb = fileName.sThumbNail
                            CommonDB.save(video)
                        }
                    }
                }
            )
        } ?: Image(
            painter = painterResource(id = R.drawable.ic_camera),
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.Center)
                .clickable(onClick = onAdd)
        )
    }
}

// Functions
suspend fun getPhotoThumbUrl(context: Context, fileId: FileId): String? {
    return fileId.asPhoto()?.thumb
        ?.takeIf { CommonHelper.bFileExist(it) }
        ?: withContext(Dispatchers.IO) {
            CommonRequests.getPhotoThumbURL(context, fileId.id).getOrNull()
        }
}

fun getVideoThumbUrl(fileId: FileId): String? {
    return fileId.asVideo()?.thumb
        ?.takeIf { CommonHelper.bFileExist(it) }
        ?: fileId.asVideo()?.ssThumbURL?.toString()
}

fun getVideoFileURL(fileId: FileId): String? {
    return fileId.asVideo()?.file
        ?.takeIf { CommonHelper.bFileExist(it) }
        ?: fileId.asVideo()?.ssFileURL?.toString()
}

// Preview
@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
fun TaskAttachmentsViewPreview() {
    val emptyTaskSettings = TaskAttachments()
    val taskSettings = TaskAttachments(
        photos = listOf(FileId.Remote(1), FileId.Remote(2))
    )

    SnapTheme {
        Column {
            TaskAttachmentsView(
                attachments = emptyTaskSettings,
                onAddAttachment = { },
                onPhotoAction = { },
                onVideoAction = { }
            )

            Divider(color = colors.colorDFDFDF, thickness = 1.dp)

            TaskAttachmentsView(
                attachments = taskSettings,
                onAddAttachment = { },
                onPhotoAction = { },
                onVideoAction = { }
            )
        }
    }
}