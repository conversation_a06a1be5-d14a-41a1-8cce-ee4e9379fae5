package com.snapinspect.snapinspect3.views.tasks

import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.snapinspect.snapinspect3.IF_Object.v_Asset
import com.snapinspect.snapinspect3.views.composables.colors
import com.snapinspect.snapinspect3.views.composables.dimens
import com.snapinspect.snapinspect3.views.composables.typography

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun TaskAssetAddressView(
    title: String,
    asset: v_Asset,
    modifier: Modifier = Modifier
) {
    Column(modifier = modifier) {
        Text(
            modifier = Modifier.padding(horizontal = dimens.normal),
            text = title,
            style = typography.h5.copy(
                color = colors.colorB3B4B4
            )
        )

        FlowRow(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = dimens.normal, vertical = dimens.small),
            horizontalArrangement = Arrangement.spacedBy(dimens.small),
            verticalArrangement = Arrangement.spacedBy(dimens.small)
        ) {
            asset.sBuildingAddress?.takeIf { it.isNotEmpty() }?.let {
                AddressTag(text = it, color = Color(0xFF1c84c6))
            }

            asset.sUnitAddress?.takeIf { it.isNotEmpty() }?.let {
                AddressTag(text = it, color = Color(0xFF1ab394))
            }

            asset.sRoomAddress?.takeIf { it.isNotEmpty() }?.let {
                AddressTag(text = it, color = Color(0xFFf8ac59))
            }
        }
    }
}

@Composable
private fun AddressTag(
    text: String,
    color: Color,
    modifier: Modifier = Modifier
) {
    Text(
        text = text,
        style = TextStyle(
            fontSize = 14.sp,
            color = color
        ),
        modifier = modifier
            .padding(end = dimens.smaller, bottom = dimens.smaller)
            .border(
                width = 1.dp,
                color = color,
                shape = RoundedCornerShape(dimens.smaller)
            )
            .padding(horizontal = dimens.small, vertical = dimens.smaller)
    )
}
