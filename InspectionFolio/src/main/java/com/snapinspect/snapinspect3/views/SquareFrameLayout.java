package com.snapinspect.snapinspect3.views;

import android.content.Context;
        import android.util.AttributeSet;
        import android.widget.FrameLayout;

public class SquareFrameLayout extends FrameLayout {

    public SquareFrameLayout(Context context) {
        super(context);
    }

    public SquareFrameLayout(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public SquareFrameLayout(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onMeasure(int widthMeasureSpec, int heightMeasureSpec) {
        // Use widthMeasureSpec for both width and height
        super.onMeasure(widthMeasureSpec, widthMeasureSpec);
    }
}
