package com.snapinspect.snapinspect3.views;

import android.content.Context;
import android.graphics.Canvas;
import android.util.AttributeSet;
import android.view.Gravity;
import android.view.View;
import android.widget.LinearLayout;


import java.util.ArrayList;
import java.util.List;

/**
 * @Created by <PERSON><PERSON><PERSON> on 6/20/18.
 */

public class SegmentedControl extends LinearLayout {
    private Context mContext;
    private int activeColor, deactiveColor;
    private int cornerRadius;
    private final List<RoundTextView> textViews = new ArrayList<>();
    private Listener mListener;

    public int selectedIndex = 0;

    public interface Listener {
        void onSegmentedValueChanged(int selectedIndex);
    }

    public SegmentedControl(Context context) {
        super(context);

        mContext = context;
        setWillNotDraw(false);
    }

    public SegmentedControl(Context context, AttributeSet attrs)
    {
        super(context, attrs);

        mContext = context;
        setWillNotDraw(false);
    }

    public SegmentedControl(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);

        mContext = context;
        setWillNotDraw(false);
    }

    public void setListener(Context context, Listener listener) {
        mContext = context;
        mListener = listener;
    }

    @Override
    public void draw(Canvas canvas) {
        super.draw(canvas);

        this.setBackgroundColor(deactiveColor);

        for (int i = 0; i < textViews.size(); i++) {
            RoundTextView textView = textViews.get(i);

            textView.setCornerRadius(cornerRadius);

            textView.setSolidColor(i == selectedIndex ? activeColor : deactiveColor);
            textView.setTextColor(i == selectedIndex ? deactiveColor : activeColor);
            textView.invalidate();
        }
    }

    public void setControlItems(List<String> buttons) {
        if (this.getChildCount() > 0)
            this.removeAllViews();

        if (textViews.size() > 0) {
            return;
        }

        this.setOrientation(LinearLayout.HORIZONTAL);

        for (int i = 0; i < buttons.size(); i++) {
            String btnTitle = buttons.get(i);

            LinearLayout.LayoutParams linearParams = new LinearLayout.LayoutParams(
                    0,
                    LayoutParams.MATCH_PARENT, 1.0f);

            RoundTextView textView = new RoundTextView(mContext);
            textView.setCornerRadius(cornerRadius);

            this.addView(textView, linearParams);

            textView.setTextSize(14);
            textView.setGravity(Gravity.CENTER);
            textView.setText(btnTitle);
            textView.setTag(i);

            textViews.add(textView);

            textView.setOnClickListener(new OnClickListener() {
                @Override
                public void onClick(View view) {
                    int tag = (int) view.getTag();
                    selectedIndex = tag;
                    SegmentedControl.this.invalidate();
                    mListener.onSegmentedValueChanged(selectedIndex);
                }
            });
        }

        this.requestLayout();
        this.invalidate();
    }

    public void setSelectedIndex (int index) {
        selectedIndex = index;
        this.requestLayout();
        this.invalidate();
    }

    public void setActiveColor(int color) {
        activeColor = color;
        this.invalidate();
    }
    public void setDeactiveColor(int color) {
        deactiveColor = color;
        this.invalidate();
    }
    public void setCornerRadius(int radius) {
        cornerRadius = radius;
        this.invalidate();
    }
}
