package com.snapinspect.snapinspect3.views.tasks

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material.Divider
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.Send
import androidx.compose.material.icons.filled.AttachFile
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.core.content.ContextCompat
import com.snapinspect.snapinspect3.Helper.CommonUI
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.views.composables.SnapTheme
import com.snapinspect.snapinspect3.views.composables.colors
import com.snapinspect.snapinspect3.views.composables.dimens
import com.snapinspect.snapinspect3.views.composables.typography

@Composable
fun TaskCommentComposeView(
    modifier: Modifier = Modifier,
    initialComment: String = "",
    onSubmit: (String) -> Unit,
    onFileAction: (AttachmentAction) -> Unit = {}
) {
    var commentText by remember { mutableStateOf(initialComment) }
    val isCommentValid = commentText.isNotBlank()

    CommentInputRow(
        modifier = modifier,
        commentText = commentText,
        onCommentChange = { commentText = it },
        onSubmit = {
            if (isCommentValid) {
                onSubmit(commentText)
                commentText = ""
            }
        },
        isCommentValid = isCommentValid,
        onFileAction = onFileAction
    )
}

@Composable
private fun CommentInputRow(
    modifier: Modifier,
    commentText: String,
    onCommentChange: (String) -> Unit,
    onSubmit: () -> Unit,
    isCommentValid: Boolean,
    onFileAction: (AttachmentAction) -> Unit
) {
    Box(modifier = modifier) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp)
                .background(Color.White),
            horizontalArrangement = Arrangement.spacedBy(dimens.smaller),
            verticalAlignment = Alignment.CenterVertically
        ) {
            CommentTextField(
                commentText = commentText,
                onCommentChange = onCommentChange,
                modifier = Modifier
                    .padding(horizontal = dimens.normal)
                    .weight(1f)
                    .fillMaxHeight()
            )

            AddFileButton(
                modifier = Modifier,
                onFileAction = onFileAction
            )

            VerticalDivider()

            SendButton(
                onClick = onSubmit,
                enabled = isCommentValid
            )
        }

        Divider(
            modifier = Modifier
                .fillMaxWidth()
                .align(Alignment.TopStart),
            color = colors.colorDFDFDF,
            thickness = 0.5.dp
        )
    }
}

@Composable
private fun CommentTextField(
    commentText: String,
    onCommentChange: (String) -> Unit,
    modifier: Modifier = Modifier
) {
    BasicTextField(
        value = commentText,
        onValueChange = onCommentChange,
        modifier = modifier.padding(vertical = dimens.smaller),
        textStyle = typography.normal.copy(color = colors.color929292),
        decorationBox = { innerTextField ->
            Box(contentAlignment = Alignment.CenterStart) {
                if (commentText.isEmpty()) {
                    Text(
                        text = "Enter Comments...",
                        color = colors.color929292
                    )
                }
                innerTextField()
            }
        }
    )
}

@Composable
private fun AddFileButton(
    modifier: Modifier,
    onFileAction: (AttachmentAction) -> Unit
) {
    val context = LocalContext.current
    val actions = listOf(
        AttachmentAction.UPLOAD_FILE,
        AttachmentAction.UPLOAD_PHOTO,
        AttachmentAction.TAKE_PHOTO
    )

    Box(modifier = modifier) {
        IconButton(
            onClick = {
                CommonUI.showOptionsDialog(
                    context,
                    ContextCompat.getString(context, R.string.please_select),
                    actions.map { it.getTitle(context) }.toTypedArray()
                ) { dialog, _, which, _ ->
                    dialog.dismiss()
                    onFileAction(actions[which])
                    true
                }
            },
            modifier = Modifier.size(40.dp)
        ) {
            Icon(
                imageVector = Icons.Default.AttachFile,
                contentDescription = "Attach file",
                modifier = Modifier.size(20.dp),
                tint = colors.color4E69FF
            )
        }
    }
}

@Composable
private fun VerticalDivider() {
    Divider(
        modifier = Modifier
            .width(1.dp)
            .height(24.dp)
            .background(colors.colorD8D8D8)
    )
}

@Composable
private fun SendButton(
    onClick: () -> Unit,
    enabled: Boolean
) {
    IconButton(
        modifier = Modifier.size(40.dp),
        onClick = onClick,
        enabled = enabled
    ) {
        val iconTint = if (enabled) {
            colors.color4E69FF
        } else {
            colors.color4E69FF.copy(alpha = 0.7f)
        }

        Icon(
            imageVector = Icons.AutoMirrored.Filled.Send,
            contentDescription = "Send comment",
            modifier = Modifier.size(20.dp),
            tint = iconTint
        )
    }
}

@Composable
@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
fun TaskCommentComposeViewPreview() {
    SnapTheme {
        TaskCommentComposeView(
            onSubmit = {},
            onFileAction = {}
        )
    }
}
