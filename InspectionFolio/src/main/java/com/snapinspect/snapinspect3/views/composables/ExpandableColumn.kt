package com.snapinspect.snapinspect3.views.composables

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.snapinspect.snapinspect3.R

@Composable
fun ExpandableColumn(
    modifier: Modifier = Modifier,
    title: String? = null,
    isExpandable: Boolean = true,
    initialExpanded: Boolean = true,
    borderColor: Color = colors.colorE2E2E2,
    backgroundColor: Color = Color.White,
    titleColor: Color = colors.color535353,
    iconTintColor: Color = colors.color9C9C9C,
    padding: Dp = dimens.normal,
    cornerRadius: Dp = dimens.normal,
    content: @Composable () -> Unit,
) {
    var isExpanded by remember { mutableStateOf(initialExpanded) }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(padding)
            .border(
                width = 0.5.dp,
                color = borderColor,
                shape = RoundedCornerShape(cornerRadius)
            )
            .background(
                color = backgroundColor,
                shape = RoundedCornerShape(cornerRadius)
            ),
        horizontalAlignment = Alignment.Start,
        verticalArrangement = Arrangement.spacedBy(dimens.smallest)
    ) {
        title?.let {
            ExpandableHeader(
                title = it,
                isExpandable = isExpandable,
                isExpanded = isExpanded,
                onExpandToggle = { isExpanded = !isExpanded },
                titleColor = titleColor,
                iconTintColor = iconTintColor,
                padding = padding
            )
        }

        if (isExpanded) {
            content()
        }
    }
}

@Composable
private fun ExpandableHeader(
    title: String,
    isExpandable: Boolean,
    isExpanded: Boolean,
    onExpandToggle: () -> Unit,
    titleColor: Color,
    iconTintColor: Color,
    padding: Dp
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = dimens.semiMedium)
            .padding(horizontal = padding),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            style = TextStyle(
                fontSize = 16.sp,
                fontWeight = FontWeight.SemiBold,
                color = titleColor
            ),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
        )

        if (isExpandable) {
            Image(
                painter = painterResource(
                    id = if (isExpanded) R.drawable.icon_arrow_collapse_green
                    else R.drawable.icon_arrow_expand_green
                ),
                contentDescription = if (isExpanded) "Collapse" else "Expand",
                colorFilter = ColorFilter.tint(iconTintColor),
                modifier = Modifier
                    .padding(end = dimens.small)
                    .clickableWithoutRipple(onExpandToggle)
            )
        }
    }
}