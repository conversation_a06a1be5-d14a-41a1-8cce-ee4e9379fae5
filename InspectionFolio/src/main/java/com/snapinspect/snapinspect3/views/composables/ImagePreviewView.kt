package com.snapinspect.snapinspect3.views.composables

import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.Icon
import androidx.compose.material.IconButton
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.squareup.picasso.Picasso
import com.squareup.picasso.Target

@Composable
fun ImagePreviewView(
    url: String?,
    closeAction: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
            .pointerInput(Unit) {
                detectTapGestures(
                    onTap = { }
                )
            }
    ) {
        PicassoImage(
            url = url,
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Fit
        )

        IconButton(
            onClick = closeAction,
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(
                    horizontal = 16.dp,
                    vertical = 30.dp
                )
        ) {
            Icon(
                imageVector = Icons.Default.Close,
                contentDescription = "Close",
                tint = Color.White,
                modifier = Modifier.size(30.dp)
            )
        }
    }
}

@Composable
fun PicassoImage(
    url: String?,
    modifier: Modifier = Modifier,
    contentScale: ContentScale = ContentScale.Fit
) {
    var image by remember { mutableStateOf<Bitmap?>(null) }
    var isLoading by remember { mutableStateOf(true) }

    DisposableEffect(url) {
        val target = object : Target {
            override fun onBitmapLoaded(bitmap: Bitmap?, from: Picasso.LoadedFrom?) {
                image = bitmap
                isLoading = false
            }

            override fun onBitmapFailed(e: Exception?, errorDrawable: Drawable?) {
                isLoading = false
            }

            override fun onPrepareLoad(placeHolderDrawable: Drawable?) {
                isLoading = true
            }
        }

        if (!url.isNullOrEmpty()) {
            Picasso.get().load(url).into(target)
        } else {
            isLoading = false
        }

        onDispose {
            Picasso.get().cancelRequest(target)
        }
    }

    Box(modifier = modifier) {
        if (isLoading) {
            LoadingIndicator()
        } else if (image == null) {
            ImagePlaceholder()
        } else {
            image?.let {
                Image(
                    bitmap = it.asImageBitmap(),
                    contentDescription = "Loaded image",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = contentScale
                )
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ImagePreviewViewPreview() {
    ImagePreviewView(
        url = "https://example.com/image.jpg",
        closeAction = { println("Close action") }
    )
}