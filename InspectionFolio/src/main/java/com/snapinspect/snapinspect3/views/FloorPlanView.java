package com.snapinspect.snapinspect3.views;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.constraintlayout.widget.ConstraintLayout;
// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.CommonUI_InsItem;
import com.snapinspect.snapinspect3.Helper.CommonUI_Photo;
import com.snapinspect.snapinspect3.IF_Object.ai_FloorPlan;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_Media;
import com.snapinspect.snapinspect3.activitynew.if_InsItemFloorPlans;
import com.squareup.picasso.Picasso;
import org.jetbrains.annotations.Nls;

import java.io.File;
import java.util.List;

public class FloorPlanView extends ConstraintLayout {

    public static class FloorPlanViewModel {
        public final ai_FloorPlan floorPlan;
        public final int iPhotoID;
        public final int iSPhotoID;

        public FloorPlanViewModel(ai_FloorPlan floorPlan, int iPhotoID, int iSPhotoID) {
            this.floorPlan = floorPlan;
            this.iPhotoID = iPhotoID;
            this.iSPhotoID = iSPhotoID;
        }

        public FloorPlanViewModel(if_InsItemFloorPlans.FloorPlanAnnotation floorPlanAnnotation) {
            this.floorPlan = floorPlanAnnotation.floorPlan;
            this.iPhotoID = floorPlanAnnotation.iPhotoID;
            this.iSPhotoID = floorPlanAnnotation.iSPhotoID;
        }
    }

    private ImageView mFloorPlanImageView;
    private TextView mFloorPlanNameTextView;
    private View mIndicatorView;

    public interface OnFloorPlanClickListener {
        void onFloorPlanClick(ai_FloorPlan floorPlan);
    }

    private OnFloorPlanClickListener onFloorPlanClickListener;
    private FloorPlanViewModel mFloorPlanViewModel;

    /**
     * Sets the click listener for the blueprint.
     *
     * @param onFloorPlanClickListener The click listener to be set
     */
    public void setOnFloorPlanClickListener(OnFloorPlanClickListener onFloorPlanClickListener) {
        this.onFloorPlanClickListener = onFloorPlanClickListener;
    }

    public FloorPlanView(Context context) {
        super(context);
    }

    public FloorPlanView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        setupViews();
    }

    public void setFloorPlan(FloorPlanViewModel floorPlanViewModel) {
        mFloorPlanViewModel = floorPlanViewModel;
        if (floorPlanViewModel == null) return;

        if (mFloorPlanViewModel.iSPhotoID > 0) {
            ai_Photo oPhoto = db_Media.getPhotoByServerID(mFloorPlanViewModel.iSPhotoID);
            if (oPhoto == null) {
                oPhoto = new ai_Photo();
                oPhoto.iSPhotoID = mFloorPlanViewModel.iSPhotoID;
                oPhoto.bUploaded = true;
                CommonDB.savePhoto(oPhoto);
            }
            CommonUI_Photo.showFullFile(getContext(), oPhoto.getId(), mFloorPlanImageView, mIndicatorView);
        } else if (mFloorPlanViewModel.iPhotoID > 0) {
            CommonUI_Photo.showFullFile(getContext(), mFloorPlanViewModel.iPhotoID, mFloorPlanImageView, mIndicatorView);
        } else {
            Picasso.get().load(new File(mFloorPlanViewModel.floorPlan.getImageFilePath())).into(mFloorPlanImageView);
        }
        mFloorPlanNameTextView.setText(mFloorPlanViewModel.floorPlan.sTitle);
    }

    private void setupViews() {
        mFloorPlanImageView = findViewById(R.id.floor_plan_image);
        mFloorPlanNameTextView = findViewById(R.id.floor_plan_name);
        mIndicatorView = findViewById(R.id.indicator_view);
        setOnClickListener(v -> {
            if (onFloorPlanClickListener != null && mFloorPlanViewModel != null) {
                onFloorPlanClickListener.onFloorPlanClick(mFloorPlanViewModel.floorPlan);
            }
        });
    }
}
