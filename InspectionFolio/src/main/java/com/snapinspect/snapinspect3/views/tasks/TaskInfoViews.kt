package com.snapinspect.snapinspect3.views.tasks

import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.WifiOff
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.snapinspect.snapinspect3.Helper.NetworkUtils
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.views.composables.FitHeightTextField
import com.snapinspect.snapinspect3.views.composables.SnapTheme
import com.snapinspect.snapinspect3.views.composables.colors
import com.snapinspect.snapinspect3.views.composables.dimens

@Composable
fun AssetAddressView(
    assetAddress: String,
    sInsItemTitle: String? = null,
    backgroundColor: Color,
    contentColor: Color,
    onTap: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(backgroundColor)
            .clickable(onClick = onTap)
            .padding(horizontal = 16.dp, vertical = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(
            modifier = Modifier.weight(1f)
        ) {
            Text(
                text = assetAddress,
                fontSize = 17.sp,
                color = contentColor,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )

            sInsItemTitle?.let {
                Text(
                    text = it,
                    fontSize = 14.sp,
                    color = contentColor,
                    maxLines = 1
                )
            }
        }


        Icon(
            imageVector = if (NetworkUtils.isNetworkAvailable(LocalContext.current))
                Icons.Filled.ChevronRight else Icons.Filled.WifiOff,
            contentDescription = null,
            tint = contentColor
        )
    }
}

@Composable
fun TaskTitleView(
    title: String = "Title",
    text: MutableState<String>,
    placeholder: String,
    textDidChange: (String) -> Unit
) {
    Column(
        horizontalAlignment = Alignment.Start,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = dimens.small)
            .padding(horizontal = dimens.normal)
    ) {
        Text(
            text = title,
            style = TextStyle(
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp,
                color = colors.colorB3B4B4
            ),
            modifier = Modifier.background(Color.Transparent)
        )

        FitHeightTextField(
            value = text.value,
            onValueChange = {
                text.value = it
                textDidChange(it)
            },
            leadingIcon = null,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = dimens.medium),
            placeholderText = placeholder,
            paddingHorizontal = dimens.zero,
            singleLine = true,
            fontSize = dimensionResource(R.dimen.text_h5).value.sp,
            textColor = colors.color080808,
            textAlign = TextAlign.Left,
            borderColor = Color.Transparent,
            borderWidth = 0f
        )

        val color by animateColorAsState(
            targetValue = if (text.value.isEmpty()) colors.colorDFDFDF else colors.color053BFF,
            animationSpec = tween(durationMillis = 200),
            label = "dividerColorAnimation"
        )
        HorizontalDivider(color = color, thickness = 1.dp)
    }
}

@Composable
fun TaskDescriptionView(
    title: String = "Description",
    text: MutableState<String>,
    placeholder: String,
    textDidChange: (String) -> Unit
) {
   Column(
        horizontalAlignment = Alignment.Start,
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = dimens.small)
            .padding(horizontal = dimens.normal)
    ) {
        Text(
            text = title,
            style = TextStyle(
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp,
                color = colors.colorB3B4B4
            ),
            modifier = Modifier.background(Color.Transparent)
        )

        FitHeightTextField(
            value = text.value,
            onValueChange = {
                text.value = it
                textDidChange(it)
            },
            leadingIcon = null,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = dimens.medium),
            placeholderText = placeholder,
            paddingHorizontal = dimens.zero,
            singleLine = false,
            maxLines = 10,
            fontSize = dimensionResource(R.dimen.text_h5).value.sp,
            textColor = colors.color080808,
            textAlign = TextAlign.Left,
            borderColor = Color.Transparent,
            borderWidth = 0f
        )

        val color by animateColorAsState(
            targetValue = if (text.value.isEmpty()) colors.colorDFDFDF else colors.color053BFF,
            animationSpec = tween(durationMillis = 200),
            label = "dividerColorAnimation"
        )
       HorizontalDivider(color = color, thickness = 1.dp)
    }

}

@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
private fun TaskInfoViewsPreview() {
    val title = remember { mutableStateOf("Clean the house") }
    val description = remember { mutableStateOf("Clean the house and make it look nice, the steps are as follows: \n1. Clean the house \n2. Make it look nice \n3. Make it smell nice") }

    SnapTheme {
        Column(
            modifier = Modifier.fillMaxSize()
        ) {
            TaskTitleView(
                title = "Title",
                text = title,
                placeholder = "Edit Title",
                textDidChange = { title.value = it }
            )

            TaskSettingsView(onUpdate = { })

            TaskDescriptionView(
                title = "Description",
                text = description,
                placeholder = "Edit Description",
                textDidChange = { description.value = it }
            )
        }
    }
}