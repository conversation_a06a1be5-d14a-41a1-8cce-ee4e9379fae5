package com.snapinspect.snapinspect3.views;

import android.content.Context;
import android.util.AttributeSet;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import com.snapinspect.snapinspect3.R;

public class InputAccessoryToolbar extends LinearLayout {

    public interface Listener {
        void onCommentLibraryClick();
        void onScanCodeClick();
    }

    private Listener mListener;
    public void setListener(Listener listener) {
        this.mListener = listener;
    }

    public InputAccessoryToolbar(Context context) {
        super(context);
    }

    public InputAccessoryToolbar(Context context, AttributeSet attrs) {
        super(context, attrs);
    }

    public InputAccessoryToolbar(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
    }

    @Override
    protected void onFinishInflate() {
        super.onFinishInflate();
        setupViews();
    }

    private void setupViews() {
        ImageButton scanCodeButton = findViewById(R.id.btn_qrcode);
        scanCodeButton.setOnClickListener(v -> {
            if (mListener != null) mListener.onScanCodeClick();
        });

        Button commentLibraryButton = findViewById(R.id.btn_comments_library);
        commentLibraryButton.setOnClickListener(v -> {
            if (mListener != null) mListener.onCommentLibraryClick();
        });
    }

}
