package com.snapinspect.snapinspect3.views.CellViews;

import android.content.Context;
import android.widget.TextView;
import androidx.appcompat.widget.LinearLayoutCompat;
import com.snapinspect.snapinspect3.R;

public class InsCommentHeaderCell extends LinearLayoutCompat {
    private TextView tvTitle;

    public InsCommentHeaderCell(Context context) {
        super(context);
        initLayout();
    }

    private void initLayout() {
        setOrientation(VERTICAL);
        inflate(getContext(), R.layout.view_notes_head_cell, this);

        int smallPadding = getResources().getDimensionPixelSize(R.dimen.margin_smallest);
        setPadding(0, smallPadding, 0, smallPadding);

        tvTitle = findViewById(R.id.header_cell_tv_title);
    }

    public void displayItem(String title) {
        tvTitle.setText(title);
    }
}
