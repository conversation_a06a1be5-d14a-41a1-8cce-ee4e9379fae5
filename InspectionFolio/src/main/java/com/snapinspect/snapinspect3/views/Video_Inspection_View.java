package com.snapinspect.snapinspect3.views;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.InputType;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.*;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.SI_DB.db_Media;
import com.snapinspect.snapinspect3.activity.if_video;
import com.snapinspect.snapinspect3.activity.if_video_old;
import com.snapinspect.snapinspect3.activity.if_videoshow;
import com.snapinspect.snapinspect3.activitynew.if_InsItemFloorPlans;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

import static com.snapinspect.snapinspect3.Helper.Constants.Values.DEFAULT_VIDEO_QUALITY;


/*
 * @Created by Osama on 14/06/14.
 */

public class Video_Inspection_View extends LinearLayout {
    private final ArrayList<ai_InsItem> lsInsItem;
    private final ai_InsItem oPInsItem;
    private final Context oContext;
    private final int position;
    private HashMap<ai_InsItem, Integer> mIdMap = new HashMap<>();
    private boolean bFullInspection = true;

    public Video_Inspection_View(ArrayList<ai_InsItem> _lsInsItem, Context _oContext, ai_InsItem _oPInsItem, boolean _bFull, int pos) {
        super(_oContext);

        oContext = _oContext;
        lsInsItem = _lsInsItem;
        oPInsItem = _oPInsItem;
        bFullInspection = _bFull;
        position = pos;

        for (int i = 0; i < _lsInsItem.size(); ++i) {
            mIdMap.put(_lsInsItem.get(i), i);
        }

        this.setLayoutParams(new LinearLayout.LayoutParams(
                LayoutParams.MATCH_PARENT, LinearLayout.LayoutParams.WRAP_CONTENT));
        this.setPadding(
                0, CommonHelper.pxFromDp(oContext, 10), 0,
                CommonHelper.pxFromDp(oContext, 10));
        setView();
    }

    public void setView() {
        LayoutInflater inflater = (LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        View row = inflater.inflate(R.layout.cell_ins_video, this, false);
        try {
            final ai_InsItem oInsItem = lsInsItem.get(position);
            LinearLayout headLayout = row.findViewById(R.id.view_head);
            long lInsItemID = oInsItem.getId();

            CommonUI_InsItem.OneViewInspection_Label(oContext, oInsItem, bFullInspection, headLayout);

            boolean bReview = CommonValidate.bItemReviewEnabled(oInsItem);
            ai_Video oVideo = db_Media.GetVideoByInsItemID(oInsItem);
            boolean bRequestInspection = CommonHelper.isKioskMode(oContext);
            String[] lsOption = CommonConfig.MenuText_ItemsView_Video_Type(
                    oContext, bRequestInspection, bReview, oVideo, CommonConfig.bHideAddVideo(oContext, oPInsItem));
            if (lsOption != null && lsOption.length > 0) {
                ImageButton menuBtn = new ImageButton(oContext);
                CommonUI_InsItem.OneViewInspection_CommandButton(oContext, menuBtn, oInsItem, bReview);

                menuBtn.setOnClickListener(v -> new MaterialDialog.Builder(oContext)
                        .title(R.string.alert_title_action)
                        .items(lsOption)
                        .itemsCallbackSingleChoice(-1, (dialog, view, which, text) -> {
                            ProcessAction(text.toString(), oInsItem, position);
                            return true;
                        })
                        .negativeText(R.string.md_cancel_label)
                        .show());
                headLayout.addView(menuBtn);
            }

            TextView oInstruction = row.findViewById(R.id.txt_Video_Inst);
            CommonUI_InsItem.OneViewInspection_InstructionLabel(oContext, oInstruction, CommonDB.GetInstruction(oInsItem, bFullInspection));

            Button oButton = row.findViewById(R.id.btn_Record);
            oButton.setTag(lInsItemID);

            if (oVideo != null && oVideo.getId() > 0) {
                oButton.setVisibility(View.GONE);
                ImageView oImageView = row.findViewById(R.id.iv_Video);
                oImageView.setPadding(10, 10, 10, 10);
                String dd = oVideo.getFile() + "|" + oInsItem.getId() + "|" + oVideo.getId();
                oImageView.setTag(dd);
                oImageView.setOnClickListener(view -> {
                    String[] sParams = view.getTag().toString().split("\\|");
                    oContext.startActivity(if_videoshow.newIntent(oContext,
                            Long.parseLong(sParams[1]), 0, Long.parseLong(sParams[2]), sParams[0]));
                });

                TextView durationTxt = row.findViewById(R.id.durationText);
                if (!StringUtils.isEmpty(oVideo.sThumb) && CommonHelper.bFileExist(oVideo.getThumb())) {
                    Bitmap myBitmap = BitmapFactory.decodeFile(oVideo.getThumb());
                    oImageView.setImageBitmap(myBitmap);

                    durationTxt.setVisibility(VISIBLE);
                    long duration = CommonHelper.getVideoDuration(oContext, oVideo.getFile());
                    durationTxt.setText(DateUtils.clockStringFromMilliseconds(duration));
                } else {
                    durationTxt.setVisibility(GONE);
                }

            } else {
                row.findViewById(R.id.view_video_container).setVisibility(GONE);
                ImageView oImageView = row.findViewById(R.id.iv_Play);
                oImageView.setVisibility(View.GONE);
                // oButton.setText("Click to record");
                // oButton.setBackgroundResource(R.drawable.btn_green);
                oButton.setOnClickListener(view -> {
                    if (!CommonValidate.validateGPSLocationPermission(oContext)) return;
                    if (!CommonValidate.bEditInspectionNotAllowAddVideo(oContext, oInsItem.getId())) return;
                    int iVideoLength = 0;
                    try {
                        if (oPInsItem.sConfigOne != null && oPInsItem.sConfigOne.length() > 0) {
                            iVideoLength = CommonHelper.getInt(oPInsItem.sConfigOne);
                            if (iVideoLength <= 0) {
                                iVideoLength = CommonHelper.getInt(CommonJson.GetJsonKeyValue("iD", oPInsItem.sConfigOne));
                            }
                        }
                    } catch (Exception ex) {
                        ai_BugHandler.ai_Handler_Exception("Exception", "Video_Inspection_View.VideoLength", ex, oContext);
                    }

                    long iInsItemID = (long) view.getTag();
                    VideoQuality videoQuality = Objects.requireNonNullElse(db_InsItem.getInsTypeVideoSize(iInsItemID), DEFAULT_VIDEO_QUALITY);
                    if (CommonHelper.bUseNewVideo()) {
                        oContext.startActivity(if_video.newIntent(oContext, 0, iInsItemID, iVideoLength > 0 ? iVideoLength : 300000, videoQuality));
                    } else {
                        oContext.startActivity(if_video_old.newIntent(oContext, 0, iInsItemID, iVideoLength > 0 ? iVideoLength : 300000));
                    }
                });
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "Video_Inspection_View.GetView", ex, oContext);
        }

        this.addView(row);

        if (!oPInsItem.bInspectionByPassCompulsory && oPInsItem.checkIfNeedValidatingCompulsoryItem(null)) {
            final ai_InsItem oInsItem = lsInsItem.get(position);
            if (!oInsItem.checkIfNeedValidatingCompulsoryItem(null) || oInsItem.hasCompulsoryConfigs())
                return;

            // mark icon
            ImageButton button = new ImageButton(oContext);
            button.setBackgroundResource(R.color.transparent);
            button.setImageResource(R.drawable.icon_exclamation_sign);
            FrameLayout.LayoutParams params = new FrameLayout.LayoutParams(
                    CommonHelper.pxFromDp(oContext, CommonUI_InsItem.INSPECT_CELL_MARGIN_LEFT),
                    FrameLayout.LayoutParams.MATCH_PARENT);
            params.setMargins(0, CommonHelper.pxFromDp(oContext, 10), 0, 0);
            button.setLayoutParams(params);
            addView(button, 0);
        }
    }

    public void ProcessAction(String sSelectedText, ai_InsItem oInsItem, int iPosition) {
        if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_DeleteItem)) {
            if (!CommonValidate.bEditInspectionNotAllowDeleteItem(oContext, oInsItem.getId())) return;
            new MaterialDialog.Builder(oContext)
                    .title("Message")
                    .content(R.string.lbl_DeleteItemPrompt, oInsItem.sName)
                    .positiveText("Yes")
                    .onPositive((dialog, which) -> {
                        if (oPInsItem.sConfig != null && oPInsItem.sConfig.contains("VADD")) {
                            oInsItem.bDeleted = true;
                            oInsItem.save();
                            lsInsItem.remove(iPosition);
                            Intent intent = new Intent(Constants.Broadcasts.sDeleteInsItem);
                            // You can also include some extra data.
                            intent.putExtra(Constants.Extras.iPInsItemID, oInsItem.iPInsItemID);
                            intent.putExtra(Constants.Extras.iPosition, Math.max((iPosition - 1), 0));
                            LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
                        } else {
                            Toast.makeText(oContext, "This item can not be deleted.", Toast.LENGTH_LONG).show();
                        }
                    })
                    .show();
        } else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_DuplicateItem)) {
            Intent intent = new Intent(Constants.Broadcasts.sActionInsItem);
            // You can also include some extra data.
            intent.putExtra("InsItemID", oInsItem.getId());
            LocalBroadcastManager.getInstance(oContext).sendBroadcast(intent);
        } else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_EditItemName)) {
            //int lTag = Integer.parseInt(sTag);
            ShowEditAlert(oInsItem);
        } else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_ResumeRecording_Video)) { //edit video
            if (!CommonValidate.validateGPSLocationPermission(oContext)) return;
            if (!CommonValidate.bEditInspectionNotResumeRecordVideo(oContext, oInsItem.getId())) return;
            
            final long iID = oInsItem.getId();
            List<ai_Video> lsVideo = CommonDB.GetInsItemVideosSugar(iID);
            if (lsVideo != null && lsVideo.size() > 0) {
                int iVideoLength = 0;
                try {
                    if (oPInsItem.sConfigOne != null && oPInsItem.sConfigOne.length() > 0) {
                        iVideoLength = CommonHelper.getInt(oPInsItem.sConfigOne);
                        if (iVideoLength <= 0) {
                            iVideoLength = CommonHelper.getInt(CommonJson.GetJsonKeyValue("iD", oPInsItem.sConfigOne));
                        }
                    }
                } catch (Exception ex) {
                    ai_BugHandler.ai_Handler_Exception("Exception", "Video_Inspection_View.VideoLength", ex, oContext);
                }

                VideoQuality videoQuality = Objects.requireNonNullElse(db_InsItem.getInsTypeVideoSize(iID), DEFAULT_VIDEO_QUALITY);
                if (CommonHelper.bUseNewVideo()) {
                    oContext.startActivity(if_video.newIntent(oContext, 0, iID, iVideoLength > 0 ? iVideoLength : 300000, videoQuality));
                } else {
                    oContext.startActivity(if_video_old.newIntent(oContext, 0, iID, iVideoLength > 0 ? iVideoLength : 300000));
                }
            }
        } else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_Delete_Video)) {
            long lInsItemID = oInsItem.getId();
            ai_Video oVideo = CommonDB.GetInsItemMainVideoSugar(lInsItemID);
            if (lInsItemID > 0 && oVideo != null && oVideo.getId() > 0) {
                if (!CommonValidate.bEditInspectionNotAllowDeleteVideo(oContext, lInsItemID)) return;
                new MaterialDialog.Builder(oContext)
                        .title("Message")
                        .content(R.string.lbl_DeleteVideoPrompt)
                        .positiveText("Yes")
                        .onPositive((dialog, which) -> {
                            CommonDB.DeleteVideoByID(oVideo.getId());
                            oInsItem.sValueOne = "";
                            oInsItem.save();
                            CommonUI_InsItem.broadcastInsItemSaved(oContext, oPInsItem.getId(), position);
                        })
                        .show();
            }
        } else if (sSelectedText.equalsIgnoreCase(CommonConfig.sMenu_FloorPlan_DropPin)) {
            oContext.startActivity(if_InsItemFloorPlans.newIntent(oContext, oInsItem.getId()));
        }
    }

    private void ShowEditAlert(final ai_InsItem oInsItem) {
        try {
            final EditText input = new EditText(oContext);
            input.setText(oInsItem.sName);
            input.setHint("Please enter Item Name ...");
            new MaterialDialog.Builder(oContext)
                    .title("Edit Item")
                    .inputType(InputType.TYPE_CLASS_TEXT)
                    .input("Please enter Item Name ...", oInsItem.sName, (dialog, input1) -> {
                        String sValue = input1.toString();
                        if (sValue == null || sValue.equalsIgnoreCase("")) {
                            Toast.makeText(oContext, "This field can not be empty.", Toast.LENGTH_LONG).show();
                        } else if (sValue.equalsIgnoreCase(oInsItem.sName)) {

                        } else {
                            if (CommonDB_Inspection.bValidateDuplicateItemName(oInsItem.iPInsItemID, sValue)) {
                                Toast.makeText(oContext, CommonUI_InsItem.sMessage_AddEditItem_DuplicateName, Toast.LENGTH_LONG).show();
                            } else {
                                oInsItem.sName = sValue;
                                oInsItem.sNameChanged = "c";
                                oInsItem.save();
                                CommonUI_InsItem.broadcastInsItemSaved(oContext, oPInsItem.getId(), position);
                                mIdMap = new HashMap<>();
                                for (int i = 0; i < lsInsItem.size(); ++i) {
                                    mIdMap.put(lsInsItem.get(i), i);
                                }
                            }
                        }
                    }).show();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_ins_full.ShowEditAlert", ex, oContext);
        }
    }
}
