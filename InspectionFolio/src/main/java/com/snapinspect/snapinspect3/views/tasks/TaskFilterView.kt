package com.snapinspect.snapinspect3.views.tasks

import android.view.View
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.automirrored.outlined.List
import androidx.compose.material.icons.filled.Cancel
import androidx.compose.material.icons.filled.CheckCircle
import androidx.compose.material.icons.outlined.Folder
import androidx.compose.material.icons.outlined.ImportExport
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.dimensionResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.snapinspect.snapinspect3.IF_Object.TaskPriority
import com.snapinspect.snapinspect3.IF_Object.ai_JsonStatus
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.activitynew.fragments.SelectableItem
import com.snapinspect.snapinspect3.activitynew.fragments.TaskCategorySelection
import com.snapinspect.snapinspect3.activitynew.tasks.TaskCompletionStatus
import com.snapinspect.snapinspect3.activitynew.tasks.label
import com.snapinspect.snapinspect3.views.composables.Seperator
import com.snapinspect.snapinspect3.views.composables.SnapTheme
import com.snapinspect.snapinspect3.views.composables.colors
import com.snapinspect.snapinspect3.views.composables.dimens

data class TaskPrioritySelection(
    override val id: Int,
    override val title: String,
    override val titleAlignment: Int = View.TEXT_ALIGNMENT_TEXT_START,
) : SelectableItem {
    companion object {
        fun fromTaskPriority(taskPriority: TaskPriority): TaskPrioritySelection {
            return TaskPrioritySelection(taskPriority.value, taskPriority.displayName)
        }
    }

    override fun fetchSubItems(): List<TaskPrioritySelection> = emptyList()
}

data class TaskStatusSelection(
    override val id: Int,
    override val title: String,
    override val titleAlignment: Int = View.TEXT_ALIGNMENT_TEXT_START,
) : SelectableItem {
    companion object {
        fun fromTaskStatus(taskStatus: ai_JsonStatus): TaskStatusSelection {
            return TaskStatusSelection(taskStatus.iStatusID, taskStatus.sName)
        }
    }

    override fun fetchSubItems(): List<TaskStatusSelection> = emptyList()
}

// MARK: - Task Filter Data Provider
interface TaskFilterDataProvider {
    val filterTypes: List<FilterType>
    val taskCompletionStatuses: List<TaskCompletionStatus>
    val statuses: List<TaskStatusSelection>
    val categories: List<TaskCategorySelection>
    val priorities: List<TaskPrioritySelection>
}

data class TaskFilterData(
    override val filterTypes: List<FilterType> = FilterType.entries,
    override val taskCompletionStatuses: List<TaskCompletionStatus> = TaskCompletionStatus.entries,
    override val statuses: List<TaskStatusSelection> = emptyList(),
    override val categories: List<TaskCategorySelection> = emptyList(),
    override val priorities: List<TaskPrioritySelection> = emptyList()
) : TaskFilterDataProvider

// MARK: - Selected Filter Data Provider
interface SelectedFilterDataProvider {
    val taskCompletionStatus: TaskCompletionStatus
    val taskStatuses: Set<TaskStatusSelection>
    val taskCategories: Set<TaskCategorySelection>
    val taskPriorities: Set<TaskPrioritySelection>
}

data class SelectedFilterData(
    override var taskCompletionStatus: TaskCompletionStatus = TaskCompletionStatus.InProgress,
    override val taskStatuses: Set<TaskStatusSelection> = emptySet(),
    override val taskCategories: Set<TaskCategorySelection> = emptySet(),
    override val taskPriorities: Set<TaskPrioritySelection> = emptySet()
) : SelectedFilterDataProvider {
    val isEmpty: Boolean
        get() = taskStatuses.isEmpty() && taskCategories.isEmpty() && taskPriorities.isEmpty()
}

// MARK: - Filter Type
enum class FilterType(val title: String) {
    STATUS("Status"),
    CATEGORY("Category"),
    PRIORITY("Priority");

    val navigationTitle: String
        get() = "Filter by $title"
        
    val icon: ImageVector
        get() = when (this) {
            STATUS -> Icons.AutoMirrored.Outlined.List
            CATEGORY -> Icons.Outlined.Folder
            PRIORITY -> Icons.Outlined.ImportExport
        }
}

// MARK: - Task Filter Navigation
private sealed class TaskFilterRoute(val route: String) {
    data object Main : TaskFilterRoute("main")
    data object Filter : TaskFilterRoute("filter/{filterType}") {
        fun createRoute(filterType: FilterType) = "filter/${filterType.name}"
    }
    data object SubCategory : TaskFilterRoute("filter/{filterType}/{categoryId}") {
        fun createRoute(filterType: FilterType, categoryId: Int) = "filter/${filterType.name}/$categoryId"

        const val FILTER_TYPE_ARG = "filterType"
        const val CATEGORY_ID_ARG = "categoryId"
    }

    companion object {
        const val FILTER_TYPE_ARG = "filterType" // Used by Filter route as well
    }
}

@Composable
fun TaskFilterNavigation(
    modifier: Modifier = Modifier,
    filterData: TaskFilterData = TaskFilterData(),
    selectedFilterData: SelectedFilterData = SelectedFilterData(),
    onApplyFilters: (SelectedFilterData) -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    val navController = rememberNavController()
    val originalCompletionStatus = selectedFilterData.taskCompletionStatus
    var currentSelectedFilterData by remember { mutableStateOf(selectedFilterData) }

    val canApplyFilters by derivedStateOf {
        !currentSelectedFilterData.isEmpty || originalCompletionStatus != currentSelectedFilterData.taskCompletionStatus
    }

    NavHost(
        navController = navController,
        startDestination = TaskFilterRoute.Main.route,
        modifier = modifier
    ) {
        composable(TaskFilterRoute.Main.route) {
            TaskFilterView(
                modifier = modifier,
                navController = navController,
                canApplyFilters = canApplyFilters,
                filterData = filterData,
                selectedFilterData = currentSelectedFilterData,
                onApplyFilters = { onApplyFilters(currentSelectedFilterData) },
                onCompletionStatusChanged = { status ->
                    currentSelectedFilterData = currentSelectedFilterData.copy(taskCompletionStatus = status)
                },
                onDismiss = onDismiss
            )
        }

        // Filter screen
        composable(
            route = TaskFilterRoute.Filter.route,
            arguments = listOf(navArgument(TaskFilterRoute.FILTER_TYPE_ARG) {
                type = NavType.StringType
            })
        ) { backStackEntry ->
            val filterTypeString = backStackEntry.arguments?.getString(TaskFilterRoute.FILTER_TYPE_ARG) ?: return@composable
            when (val filterType = FilterType.valueOf(filterTypeString)) {
                FilterType.STATUS -> StatusFilterScreen(
                    filterType = filterType,
                    filterData = filterData,
                    selectedFilterData = currentSelectedFilterData,
                    onFilterDataChanged = { currentSelectedFilterData = it },
                    onBack = { navController.popBackStack() }
                )

                FilterType.CATEGORY -> CategoryFilterScreen(
                    filterType = filterType,
                    filterData = filterData,
                    selectedFilterData = currentSelectedFilterData,
                    onFilterDataChanged = { currentSelectedFilterData = it },
                    onNavigateToSubCategory = { categoryId ->
                        navController.navigate(TaskFilterRoute.SubCategory.createRoute(filterType, categoryId))
                    },
                    onBack = { navController.popBackStack() }
                )

                FilterType.PRIORITY -> PriorityFilterScreen(
                    filterType = filterType,
                    filterData = filterData,
                    selectedFilterData = currentSelectedFilterData,
                    onFilterDataChanged = { currentSelectedFilterData = it },
                    onBack = { navController.popBackStack() }
                )
            }
        }

        // Sub-category screen
        composable(
            route = TaskFilterRoute.SubCategory.route,
            arguments = listOf(
                navArgument(TaskFilterRoute.SubCategory.FILTER_TYPE_ARG) { type = NavType.StringType },
                navArgument(TaskFilterRoute.SubCategory.CATEGORY_ID_ARG) { type = NavType.IntType }
            )
        ) { backStackEntry ->
            val filterTypeString = backStackEntry.arguments?.getString(TaskFilterRoute.SubCategory.FILTER_TYPE_ARG) ?: return@composable
            val categoryId = backStackEntry.arguments?.getInt(TaskFilterRoute.SubCategory.CATEGORY_ID_ARG) ?: return@composable
            val filterType = FilterType.valueOf(filterTypeString)

            SubCategoryFilterScreen(
                categoryId = categoryId,
                filterData = filterData,
                selectedFilterData = currentSelectedFilterData,
                onFilterDataChanged = { currentSelectedFilterData = it },
                onNavigateToSubCategory = { subCategoryId ->
                    navController.navigate(TaskFilterRoute.SubCategory.createRoute(filterType, subCategoryId))
                },
                onBack = { navController.popBackStack() }
            )
        }
    }
}

@Composable
private fun StatusFilterScreen(
    filterType: FilterType,
    filterData: TaskFilterData,
    selectedFilterData: SelectedFilterData,
    onFilterDataChanged: (SelectedFilterData) -> Unit,
    onBack: () -> Unit
) {
    FilterItemsView(
        title = filterType.navigationTitle,
        items = filterData.statuses,
        selectedItems = selectedFilterData.taskStatuses,
        onItemSelected = { status ->
            val updatedStatuses = selectedFilterData.taskStatuses.toMutableSet().apply {
                if (contains(status)) remove(status) else add(status)
            }
            onFilterDataChanged(selectedFilterData.copy(taskStatuses = updatedStatuses))
        },
        onReset = {
            onFilterDataChanged(selectedFilterData.copy(taskStatuses = emptySet()))
        },
        onBack = onBack
    )
}

@Composable
private fun CategoryFilterScreen(
    filterType: FilterType,
    filterData: TaskFilterData,
    selectedFilterData: SelectedFilterData,
    onFilterDataChanged: (SelectedFilterData) -> Unit,
    onNavigateToSubCategory: (Int) -> Unit,
    onBack: () -> Unit
) {
    FilterItemsView(
        title = filterType.navigationTitle,
        items = filterData.categories,
        selectedItems = selectedFilterData.taskCategories,
        onItemSelected = { category ->
            val updatedCategories = selectedFilterData.taskCategories.toMutableSet().apply {
                if (contains(category)) remove(category) else add(category)
            }
            onFilterDataChanged(selectedFilterData.copy(taskCategories = updatedCategories))
        },
        onNavigateToSubItems = { category ->
            onNavigateToSubCategory(category.id)
        },
        onReset = {
            onFilterDataChanged(selectedFilterData.copy(taskCategories = emptySet()))
        },
        onBack = onBack
    )
}

@Composable
private fun PriorityFilterScreen(
    filterType: FilterType,
    filterData: TaskFilterData,
    selectedFilterData: SelectedFilterData,
    onFilterDataChanged: (SelectedFilterData) -> Unit,
    onBack: () -> Unit
) {
    FilterItemsView(
        title = filterType.navigationTitle,
        items = filterData.priorities,
        selectedItems = selectedFilterData.taskPriorities,
        onItemSelected = { priority ->
            val updatedPriorities = selectedFilterData.taskPriorities.toMutableSet().apply {
                if (contains(priority)) remove(priority) else add(priority)
            }
            onFilterDataChanged(selectedFilterData.copy(taskPriorities = updatedPriorities))
        },
        onReset = {
            onFilterDataChanged(selectedFilterData.copy(taskPriorities = emptySet()))
        },
        onBack = onBack
    )
}

@Composable
private fun SubCategoryFilterScreen(
    categoryId: Int,
    filterData: TaskFilterData,
    selectedFilterData: SelectedFilterData,
    onFilterDataChanged: (SelectedFilterData) -> Unit,
    onNavigateToSubCategory: (Int) -> Unit,
    onBack: () -> Unit
) {
    // Find the parent category by ID
    val parentCategory = filterData.categories.find { it.id == categoryId } ?: return
    // Fetch sub-categories
    val subCategories: List<TaskCategorySelection> = parentCategory.fetchSubItems() as? List<TaskCategorySelection> ?: emptyList() // Added safe cast

    // Show subcategories filter view
    FilterItemsView(
        title = parentCategory.title,
        parentItem = parentCategory,
        items = subCategories,
        selectedItems = selectedFilterData.taskCategories,
        onItemSelected = { category ->
            val updatedCategories = selectedFilterData.taskCategories.toMutableSet().apply {
                if (contains(category)) remove(category) else add(category)
            }
            onFilterDataChanged(selectedFilterData.copy(taskCategories = updatedCategories))
        },
        onNavigateToSubItems = { category ->
            onNavigateToSubCategory(category.id)
        },
        onReset = {
            val updatedCategories = selectedFilterData.taskCategories.toMutableSet().apply {
                removeAll(subCategories.toSet())
            }
            onFilterDataChanged(selectedFilterData.copy(taskCategories = updatedCategories))
        },
        onBack = onBack
    )
}

@Composable
private fun TaskFilterView(
    modifier: Modifier = Modifier,
    navController: NavController = rememberNavController(),
    canApplyFilters: Boolean,
    filterData: TaskFilterData = TaskFilterData(),
    selectedFilterData: SelectedFilterData = SelectedFilterData(),
    onCompletionStatusChanged: (TaskCompletionStatus) -> Unit = {},
    onApplyFilters:() -> Unit = {},
    onDismiss: () -> Unit = {}
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(bottom = dimens.large)
    ) {
        FilterHeader(
            title = "Filters",
            onDismiss = onDismiss
        )
        
        Seperator()

        // Filter Content
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .fillMaxWidth()
        ) {
            // Completion Status Section
            item {
                FilterSectionHeader(title = "Completion Status")
            }
            
            items(filterData.taskCompletionStatuses) { status ->
                CompletionStatusRow(
                    title = status.label,
                    isSelected = selectedFilterData.taskCompletionStatus == status,
                    onSelected = { 
                        onCompletionStatusChanged(status)
                    }
                )
                Seperator()
            }

            // Other Filters Section
            item {
                FilterSectionHeader(title = "Others")
            }
            
            items(filterData.filterTypes) { filterType ->
                FilterTypeRow(
                    filterType = filterType,
                    selectedCount = getSelectedCountForType(filterType, selectedFilterData),
                    onClick = { navController.navigate(TaskFilterRoute.Filter.createRoute(filterType)) }
                )
                Seperator()
            }
        }

        // Apply Button
        ApplyFilterButton(
            enabled = canApplyFilters,
            onClick = onApplyFilters
        )
    }
}

@Composable
private fun FilterHeader(
    title: String,
    onDismiss: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                vertical = dimens.medium,
                horizontal = dimens.extraNormal
            )
    ) {
        IconButton(
            onClick = onDismiss,
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .size(30.dp)
        ) {
            Icon(
                imageVector = Icons.Filled.Cancel,
                contentDescription = "Close",
                tint = colors.colorBEBEBE
            )
        }
        
        Text(
            text = title,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

@Composable
private fun ApplyFilterButton(
    enabled: Boolean,
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        enabled = enabled,
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                horizontal = dimensionResource(R.dimen.margin_20),
                vertical = dimensionResource(R.dimen.margin_10)
            )
            .height(48.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = if (enabled) Color(0xFF3E6BFF) else Color(0xFFEBEBEB),
            contentColor = if (enabled) Color.White else Color.Gray
        )
    ) {
        Text(
            text = "Apply",
            fontSize = 15.sp,
            fontWeight = FontWeight.SemiBold
        )
    }
}

@Composable
private fun FilterSectionHeader(title: String) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = dimens.small)
    ) {
        Text(
            text = title,
            fontSize = 15.sp,
            color = colors.colorAAAAAA,
            modifier = Modifier.padding(
                horizontal = dimens.extraNormal,
                vertical = dimens.semiMedium
            )
        )
        Seperator()
    }
}

@Composable
private fun CompletionStatusRow(
    title: String,
    isSelected: Boolean,
    onSelected: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onSelected)
            .padding(
                horizontal = dimens.extraNormal,
                vertical = dimens.semiMedium
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(dimensionResource(R.dimen.margin_small))
    ) {
        Text(
            text = title,
            fontSize = 16.sp,
            color = Color(0xFF080808)
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        if (isSelected) {
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = "Selected",
                tint = Color(0xFF3E6BFF)
            )
        }
    }
}

@Composable
private fun FilterTypeRow(
    filterType: FilterType,
    selectedCount: Int,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onClick)
            .padding(
                horizontal = dimens.extraNormal,
                vertical = dimens.medium
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(dimens.small)
    ) {
        Icon(
            imageVector = filterType.icon,
            contentDescription = filterType.title,
            tint = Color(0xFF080808)
        )
        
        Text(
            text = filterType.title,
            fontSize = 16.sp,
            color = Color(0xFF080808)
        )
        
        Spacer(modifier = Modifier.weight(1f))
        
        if (selectedCount > 0) {
            Box(
                modifier = Modifier
                    .size(24.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF3E6BFF)),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = selectedCount.toString(),
                    fontSize = 14.sp,
                    color = Color.White
                )
            }
        }

        Icon(
            imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
            contentDescription = "Next",
            tint = colors.colorBEBEBE
        )
    }
}

private fun getSelectedCountForType(filterType: FilterType, selectedFilterData: SelectedFilterData): Int {
    return when (filterType) {
        FilterType.STATUS -> selectedFilterData.taskStatuses.size
        FilterType.CATEGORY -> selectedFilterData.taskCategories.size
        FilterType.PRIORITY -> selectedFilterData.taskPriorities.size
    }
}

@Composable
private fun <T : SelectableItem> FilterItemsView(
    title: String,
    parentItem: T? = null,
    items: List<T>,
    selectedItems: Set<T>,
    onItemSelected: (T) -> Unit,
    onNavigateToSubItems: (T) -> Unit = {},
    onReset: () -> Unit,
    onBack: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxWidth()
    ) {
        FilterItemsHeader(
            title = title,
            onReset = onReset,
            onBack = onBack
        )
        
        Seperator()

        // Items list
        LazyColumn(
            modifier = Modifier
                .weight(1f)
                .padding(bottom = dimens.larger)
        ) {
            parentItem?.let {
                item {
                    FilterSectionHeader("Main Category")
                }
                item {
                    FilterItemRow(
                        item = it,
                        hasSubItems = false,
                        isSelected = selectedItems.contains(it),
                        onSelected = { onItemSelected(it) },
                        showNavigationIcon = false
                    )
                }
                item {
                    Seperator()
                }
                item {
                    FilterSectionHeader("Sub Category")
                }
            }
            items(items) { item ->
                val subItems = item.fetchSubItems()
                val hasSubItems = subItems.isNotEmpty()
                FilterItemRow(
                    item = item,
                    hasSubItems = hasSubItems,
                    isSelected = selectedItems.contains(item),
                    onSelected = {
                        if (hasSubItems) {
                            onNavigateToSubItems(item)
                        } else {
                            onItemSelected(item)
                        }
                    },
                    showNavigationIcon = hasSubItems
                )
                Seperator()
            }
        }
    }
}

@Composable
private fun FilterItemsHeader(
    title: String,
    onReset: () -> Unit,
    onBack: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(
                vertical = dimens.normal,
                horizontal = dimens.normal
            )
    ) {
        IconButton(
            onClick = onBack,
            modifier = Modifier
                .align(Alignment.CenterStart)
                .size(24.dp)
        ) {
            Icon(
                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                contentDescription = "Back",
                tint = colors.colorBEBEBE
            )
        }
        
        Text(
            text = title,
            fontSize = 18.sp,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.align(Alignment.Center)
        )
        
        Text(
            text = "Reset",
            fontSize = 16.sp,
            color = Color(0xFF3E6BFF),
            modifier = Modifier
                .align(Alignment.CenterEnd)
                .clickable { onReset() }
                .padding(horizontal = dimens.small)
        )
    }
}

@Composable
private fun <T : SelectableItem> FilterItemRow(
    item: T,
    hasSubItems: Boolean,
    isSelected: Boolean,
    onSelected: () -> Unit,
    showNavigationIcon: Boolean = true
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable(onClick = onSelected)
            .padding(
                vertical = dimens.medium,
                horizontal = dimens.extraNormal
            ),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(dimens.small)
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = item.title,
            fontSize = 16.sp,
            color = Color(0xFF080808)
        )
        
        if (hasSubItems) {
            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "Next",
                tint = colors.colorBEBEBE
            )
        } else if (isSelected) {
            Icon(
                imageVector = Icons.Default.CheckCircle,
                contentDescription = "Selected",
                tint = Color(0xFF3E6BFF)
            )
        } else {
            Spacer(modifier = Modifier.width(24.dp))
        }
    }
}

@Composable
@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
fun TaskFilterViewPreview() {
    // Mock TaskFilterData for Preview
    val mockFilterData = TaskFilterData(
        statuses = listOf(
            TaskStatusSelection(1, "Open"),
            TaskStatusSelection(2, "In Progress"),
            TaskStatusSelection(3, "Closed")
        ),
        categories = listOf(
            TaskCategorySelection(1, "General"),
            TaskCategorySelection(2, "Maintenance"),
            TaskCategorySelection(3, "Inspection")
        ),
        priorities = listOf(
            TaskPrioritySelection(1, "Low"),
            TaskPrioritySelection(2, "Medium"),
            TaskPrioritySelection(3, "High")
        )
    )
    val mockSelectedData = SelectedFilterData(
        taskCompletionStatus = TaskCompletionStatus.InProgress,
        taskStatuses = setOf(TaskStatusSelection(1, "Open")),
        taskPriorities = setOf(TaskPrioritySelection(3, "High"))
    )

    SnapTheme {
        TaskFilterNavigation(
            filterData = mockFilterData,
            selectedFilterData = mockSelectedData
        )
    }
}