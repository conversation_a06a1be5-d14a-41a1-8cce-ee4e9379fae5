package com.snapinspect.snapinspect3.views.tasks

import android.content.res.Configuration
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.Divider
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.CheckBox
import androidx.compose.material.icons.filled.CheckBoxOutlineBlank
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.snapinspect.snapinspect3.Helper.CommonDB
import com.snapinspect.snapinspect3.IF_Object.ai_User
import com.snapinspect.snapinspect3.views.composables.SearchBar
import com.snapinspect.snapinspect3.views.composables.SnapTheme
import com.snapinspect.snapinspect3.views.composables.colors
import com.snapinspect.snapinspect3.views.composables.dimens

@Composable
fun TaskMemberSelectView(
    modifier: Modifier = Modifier,
    allUsers: List<ai_User> = CommonDB.GetAllUsers(),
    selectedMembers: List<ai_User>,
    isSingleSelection: Boolean = false,
    onDone: (List<Int>) -> Unit,
    onDismiss: () -> Unit,
    backgroundColor: Color = Color.White,
    buttonColors: ButtonColors = ButtonDefaults.buttonColors(),
    textStyle: TextStyle = TextStyle.Default
) {
    var searchText by remember { mutableStateOf("") }
    var selection by remember { mutableStateOf(selectedMembers.map { it.iCustomerID ?: 0 }.toSet()) }
    
    val filteredUsers by remember(searchText, selection, allUsers) {
        derivedStateOf {
            allUsers
                .sortedBy { !selection.contains(it.iCustomerID ?: 0) }
                .filter { user ->
                    searchText.isEmpty() ||
                        (user.sName ?: "").contains(searchText, ignoreCase = true) ||
                        (user.sEmail ?: "").contains(searchText, ignoreCase = true)
                }
        }
    }

    Column(
        modifier = modifier.background(backgroundColor)
    ) {
        SearchBar(
            value = searchText,
            onValueChange = { searchText = it },
            backgroundColor = Color.Transparent,
            textFieldColor = colors.colorF7F7F7,
        )

        LazyColumn(
            modifier = Modifier
                .background(backgroundColor)
                .weight(1f),
        ) {
            items(filteredUsers) { user ->
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .clickable {
                            selection = toggleSelection(user.iCustomerID ?: 0, isSingleSelection, selection)
                        }
                        .padding(top = dimens.medium)
                        .padding(horizontal = dimens.medium),
                    verticalArrangement = Arrangement.spacedBy(dimens.medium)
                ) {
                    UserListItem(
                        user = user,
                        isSelected = selection.contains(user.iCustomerID ?: 0),
                        isSingleSelection = isSingleSelection,
                        textStyle = textStyle
                    )

                    Divider(
                        color = Color(0xFFAAAAAA).copy(alpha = 0.5f),
                        thickness = 0.5.dp,
                    )
                }
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .background(backgroundColor)
                .padding(horizontal = dimens.medium)
                .padding(top = dimens.medium)
                .padding(bottom = dimens.large),
            horizontalArrangement = Arrangement.spacedBy(dimens.small)
        ) {
            Button(
                onClick = onDismiss,
                modifier = Modifier.weight(1f),
                colors = buttonColors.copy(containerColor = colors.colorB3B4B4)
            ) {
                Text("Cancel")
            }
            
            Button(
                onClick = { 
                    onDone(selection.toList())
                    onDismiss()
                },
                modifier = Modifier.weight(1f),
                colors = buttonColors.copy(containerColor = colors.color3E6BFF)
            ) {
                Text("Done")
            }
        }
    }
}

@Composable
private fun UserListItem(
    user: ai_User,
    isSelected: Boolean,
    isSingleSelection: Boolean,
    textStyle: TextStyle = TextStyle.Default
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = dimens.medium)
            .padding(horizontal = dimens.medium),
        verticalAlignment = Alignment.CenterVertically
    ) {
        UserAvatarView(
            user = user,
            fontSize = 16.sp,
            size = 44.dp
        )

        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = dimens.medium),
            verticalArrangement = Arrangement.spacedBy(dimens.smallest)
        ) {
            Text(
                text = user.sName ?: "",
                style = textStyle.copy(
                    fontWeight = FontWeight.SemiBold,
                    fontSize = 14.sp
                )
            )
            Text(
                text = user.sEmail ?: "",
                style = textStyle.copy(
                    fontSize = 12.sp,
                    color = Color.Gray
                )
            )
        }

        val icon = when {
            isSelected && isSingleSelection -> Icons.Filled.Check
            isSelected -> Icons.Filled.CheckBox
            !isSingleSelection -> Icons.Filled.CheckBoxOutlineBlank
            else -> null
        }
        
        icon?.let {
            Icon(
                imageVector = it,
                contentDescription = if (isSelected) "Selected" else "Not Selected",
                tint = if (isSelected) colors.color3E6BFF else colors.colorBEBEBE
            )
        }
    }
}

private fun toggleSelection(
    userId: Int,
    isSingleSelection: Boolean,
    selection: Set<Int>
): Set<Int> {
    return when {
        isSingleSelection -> setOf(userId)
        userId in selection -> selection - userId
        else -> selection + userId
    }
}

@Preview(name = "Light Mode", uiMode = Configuration.UI_MODE_NIGHT_NO, showBackground = true)
@Composable
private fun TaskMemberSelectViewPreview() {
    val dummyUsers = listOf(
        ai_User().apply { iCustomerID = 1; sName = "John McLean"; sEmail = "<EMAIL>" },
        ai_User().apply { iCustomerID = 2; sName = "Jane Smith"; sEmail = "<EMAIL>" },
        ai_User().apply { iCustomerID = 3; sName = "Alice Johnson"; sEmail = "<EMAIL>" },
        ai_User().apply { iCustomerID = 4; sName = "Bob Brown"; sEmail = "<EMAIL>" },
        ai_User().apply { iCustomerID = 5; sName = "Sarah Wilson"; sEmail = "<EMAIL>" }
    )

    SnapTheme {
        TaskMemberSelectView(
            allUsers = dummyUsers,
            selectedMembers = listOf(dummyUsers[0], dummyUsers[2]),
            onDone = {},
            onDismiss = {}
        )
    }
}
