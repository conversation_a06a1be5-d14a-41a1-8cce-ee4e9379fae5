package com.snapinspect.snapinspect3.views.composables

import android.R
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.TypedValue
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.compose.foundation.gestures.animateScrollBy
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.material.Divider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.localbroadcastmanager.content.LocalBroadcastManager
import com.snapinspect.snapinspect3.Helper.Constants

@Composable
fun getActionBarSize(): Dp {
    return getDimenInDp(R.attr.actionBarSize)
}

@Composable
fun getDimenInDp(dimenRes: Int): Dp {
    val context = LocalContext.current
    val typedValue = TypedValue()
    context.theme.resolveAttribute(dimenRes, typedValue, true)
    val pixels = typedValue.getDimension(context.resources.displayMetrics)
    return (pixels / LocalDensity.current.density).dp
}

@Composable
fun Dp.toPx() = with(LocalDensity.current) {
    <EMAIL>()
}

fun hideKeyboard(context: Context) {
    val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
    var view = (context as Activity).currentFocus
    if (view == null) view = View(context)
    // Hide the keyboard
    imm.hideSoftInputFromWindow(view.windowToken, 0)
}

fun Color.Companion.hex(hex: String): Color? {
    return try {
        Color(android.graphics.Color.parseColor(hex.trim()))
    } catch (e: Exception) {
        null
    }
}

fun Context.sendBroadcast(action: String, extras: Bundle? = null) {
    with(LocalBroadcastManager.getInstance(this)) {
        sendBroadcast(Intent(action).apply {
            extras?.let { putExtras(it) }
        })
    }
}

suspend fun LazyListState.scrollToTop() {
    this.animateScrollToItem(0)
}

suspend fun LazyListState.scrollToBottom() {
    this.animateScrollToItem(index = layoutInfo.totalItemsCount - 1)
}

@Composable
fun Seperator(
    modifier: Modifier = Modifier.padding(horizontal = dimens.extraNormal),
    color: Color = colors.colorAAAAAA.copy(alpha = 0.5f),
    thickness: Dp = 0.5.dp,
) {
    Divider(
        color = color,
        thickness = thickness,
        modifier = modifier
    )
}