package com.snapinspect.snapinspect3.views.tasks

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AddCircle
import androidx.compose.material.icons.filled.MoreHoriz
import androidx.compose.material.icons.sharp.Add
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.snapinspect.snapinspect3.Helper.CommonDB
import com.snapinspect.snapinspect3.Helper.CommonHelper
import com.snapinspect.snapinspect3.Helper.CommonUI
import com.snapinspect.snapinspect3.Helper.SharedConfig
import com.snapinspect.snapinspect3.IF_Object.*
import com.snapinspect.snapinspect3.IF_Object.ai_User
import com.snapinspect.snapinspect3.R
import com.snapinspect.snapinspect3.activitynew.fragments.ItemSelectionDialog
import com.snapinspect.snapinspect3.activitynew.fragments.LayoutSelection
import com.snapinspect.snapinspect3.activitynew.fragments.TaskCategorySelection
import com.snapinspect.snapinspect3.util.DateUtils
import com.snapinspect.snapinspect3.views.composables.*
import kotlinx.coroutines.launch
import java.util.*

data class TaskSettingState(
    val settings: TaskSettings,
    val action: TaskSettingAction
)

enum class TaskSettingAction {
    UPDATE_DUE_DATE,
    UPDATE_ASSIGN_TO, 
    UPDATE_STATUS,
    UPDATE_PRIORITY,
    UPDATE_TASK_CATEGORY,
    UPDATE_MEMBERS
}

@Composable
fun TaskSettingsView(
    settings: TaskSettings = TaskSettings(),
    onUpdate: (TaskSettingState) -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = dimens.normal, vertical = dimens.smallest),
        verticalArrangement = Arrangement.spacedBy(dimens.smaller)
    ) {
        TopRow(settings = settings, onUpdate = onUpdate)
        BottomRow(settings = settings, onUpdate = onUpdate)
    }
}

@Composable
private fun TopRow(
    settings: TaskSettings,
    onUpdate: (TaskSettingState) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        DueDateSection(
            settings = settings,
            onUpdate = onUpdate,
            modifier = Modifier.weight(1f)
        )
        AssignToSection(
            settings = settings,
            onUpdate = onUpdate,
            modifier = Modifier.weight(1f)
        )
        StatusSection(
            settings = settings,
            onUpdate = onUpdate,
            modifier = Modifier.weight(1f)
        )
    }
}

@Composable
private fun BottomRow(
    settings: TaskSettings,
    onUpdate: (TaskSettingState) -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween
    ) {
        PrioritySection(
            settings = settings,
            onUpdate = onUpdate,
            modifier = Modifier.weight(1f)
        )
        TaskCategorySection(
            settings = settings,
            onUpdate = onUpdate,
            modifier = Modifier.weight(1f)
        )
        MembersSection(
            settings = settings,
            onUpdate = onUpdate,
            modifier = Modifier.weight(1f)
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun DueDateSection(
    settings: TaskSettings,
    onUpdate: (TaskSettingState) -> Unit,
    modifier: Modifier = Modifier
) {
    var showDatePicker by remember { mutableStateOf(false) }

    SettingSection(
        title = "Due Date:",
        stackAlignment = Alignment.Start,
        alignment = Alignment.Center,
        onClick = { showDatePicker = true },
        modifier = modifier
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            Icon(
                painter = painterResource(R.drawable.calendar_icon),
                contentDescription = null
            )
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = settings.dueDate?.let {
                    DateUtils.format(it, "MMM dd, yyyy")
                } ?: "Due Date",
                style = TextStyle(
                    fontSize = 14.sp,
                    color = colors.color6D7278
                )
            )
        }
    }

    if (showDatePicker) {
        val datePickerState = rememberDatePickerState(
            initialSelectedDateMillis = settings.dueDate?.time ?: System.currentTimeMillis()
        )
        
        DatePickerDialog(
            onDismissRequest = { showDatePicker = false },
            confirmButton = {
                TextButton(onClick = {
                    datePickerState.selectedDateMillis?.let { selectedDateMillis ->
                        val selectedDate = Date(selectedDateMillis)
                        val copy = settings.copy(dueDate = selectedDate)
                        onUpdate(TaskSettingState(settings = copy, action = TaskSettingAction.UPDATE_DUE_DATE))
                    }
                    showDatePicker = false
                }) {
                    Text("OK")
                }
            },
            dismissButton = {
                TextButton(onClick = { showDatePicker = false }) {
                    Text("Cancel")
                }
            }
        ) {
            DatePicker(state = datePickerState)
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun AssignToSection(
    settings: TaskSettings,
    onUpdate: (TaskSettingState) -> Unit,
    modifier: Modifier = Modifier
) {
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()
    val context = LocalContext.current

    SettingSection(
        title = "Managed By:",
        stackAlignment = Alignment.CenterHorizontally,
        alignment = Alignment.Center,
        onClick = {
            when {
                settings.isSubTask -> CommonUI.showToast(context, "This field cannot be changed") 
                else -> scope.launch { sheetState.show() }
            }
        },
        modifier = modifier
    ) {
        settings.assignToName?.let { name ->
            Text(
                text = name,
                style = TextStyle(
                    fontSize = 14.sp,
                    color = colors.color6D7278
                )
            )
        } ?: Icon(
            imageVector = Icons.Filled.AddCircle,
            contentDescription = "Select assignee",
            tint = colors.colorB3B4B4
        )
    }

    if (sheetState.isVisible) {
        ModalBottomSheet(
            onDismissRequest = { scope.launch { sheetState.hide() } },
            sheetState = sheetState,
            containerColor = Color.White,
        ) {
            TaskMemberSelectView(
                modifier = Modifier
                    .fillMaxWidth()
                    .height((LocalConfiguration.current.screenHeightDp * 0.8f).dp),
                allUsers = CommonDB.GetAllUsers() ?: emptyList(),
                selectedMembers = settings.assignToUser ?.let { listOf(it) } ?: emptyList(),
                isSingleSelection = true,
                onDone = { selectedMembers ->
                    val selectedUserId = selectedMembers.firstOrNull()
                    val copy = settings.copy(assignTo = selectedUserId)
                    onUpdate(
                        TaskSettingState(
                            settings = copy,
                            action = TaskSettingAction.UPDATE_ASSIGN_TO
                        )
                    )
                    scope.launch { sheetState.hide() }
                },
                onDismiss = { scope.launch { sheetState.hide() } }
            )
        }
    }
}

@Composable
private fun StatusSection(
    settings: TaskSettings,
    onUpdate: (TaskSettingState) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val statuses = CommonHelper.GetTaskStatus(context)

    SettingSection(
        title = "Status:",
        stackAlignment = Alignment.End,
        alignment = Alignment.CenterEnd,
        onClick = {
            CommonUI.showOptionsDialog(
                context,
                "Please select",
                statuses.map { it.sName }.toTypedArray()
            ) { dialog, _, which, _ ->
                val copy = settings.copy(
                    status = statuses[which].sName,
                    statusCode = statuses[which].sColorCode
                )
                onUpdate(TaskSettingState(settings = copy, action = TaskSettingAction.UPDATE_STATUS))
                dialog.dismiss()
                true
            }
        },
        modifier = modifier
    ) {
        settings.status?.takeIf { it.isNotEmpty() }?.let { status ->
            Text(
                text = status,
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = Color.White
                ),
                modifier = Modifier
                    .background(
                        settings.statusCode?.let { Color.hex(it) } ?: colors.colorDFDFDF,
                        shape = RoundedCornerShape(dimens.medium)
                    )
                    .padding(horizontal = dimens.semiMedium, vertical = dimens.smaller)
            )
        } ?: Icon(
            imageVector = Icons.Filled.AddCircle,
            contentDescription = null,
            tint = colors.colorB3B4B4,
            modifier = Modifier.padding(end = dimens.extraNormal)
        )
    }
}

@Composable
private fun PrioritySection(
    settings: TaskSettings,
    onUpdate: (TaskSettingState) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val priorityOptions = TaskPriority.entries

    SettingSection(
        title = "Priority:",
        stackAlignment = Alignment.Start,
        alignment = Alignment.Center,
        onClick = {
            CommonUI.showOptionsDialog(
                context,
                "Please select",
                priorityOptions.map { it.displayName }.toTypedArray()
            ) { dialog, _, which, _ ->
                val copy = settings.copy(priority = priorityOptions[which])
                onUpdate(TaskSettingState(settings = copy, action = TaskSettingAction.UPDATE_PRIORITY))
                dialog.dismiss()
                true
            }
        },
        modifier = modifier
    ) {
        Text(
            text = settings.priority.displayName,
            style = TextStyle(
                fontSize = 12.sp,
                color = colorResource(id = settings.priority.textColor)
            ),
            modifier = Modifier
                .background(
                    color = colorResource(id = settings.priority.backgroundColor),
                    shape = RoundedCornerShape(5.dp)
                )
                .padding(dimens.smaller),
        )
    }
}

@Composable
private fun TaskCategorySection(
    settings: TaskSettings,
    onUpdate: (TaskSettingState) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    SettingSection(
        title = "Category:",
        stackAlignment = Alignment.CenterHorizontally,
        alignment = Alignment.Center,
        onClick = {
            val dialog = ItemSelectionDialog(
                context = context,
                context.getString(R.string.title_choose_task_category),
                items = CommonDB.getNoticeCategories(0)
                    .map { 
                        val ai = ai_NoticeCategory()
                        ai.iSNoticeCategoryID = it.iSNoticeCategoryID
                        ai.sName = it.sName ?: ""
                        ai
                    }
                    .map(TaskCategorySelection.Companion::fromTaskCategory),
                recentIds = SharedConfig.getInstance(context)::getRecentNoticeCategories,
                onItemSelected = { item ->
                    val copy = settings.copy(
                        taskCategory = item.title,
                        taskCategoryID = item.id
                    )
                    onUpdate(TaskSettingState(settings = copy, action = TaskSettingAction.UPDATE_TASK_CATEGORY))
                    SharedConfig.getInstance(context).saveNoticeCategory(item.id)
                }
            )
            dialog.show()
         },
        modifier = modifier
    ) {
        settings.taskCategory?.takeIf { it.isNotEmpty() }?.let {
            Text(
                text = it,
                style = TextStyle(
                    fontSize = 12.sp,
                    fontWeight = FontWeight.SemiBold,
                    color = colors.color337ab7,
                ),
                modifier = Modifier
                    .dashedBorder(
                        width = 1.dp,
                        color = colors.color337ab7,
                        shape = RoundedCornerShape(5.dp)
                    )
                    .padding(dimens.smaller),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        } ?: Icon(
            imageVector = Icons.Filled.AddCircle,
            contentDescription = "Select category", 
            tint = colors.colorB3B4B4
        )
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun MembersSection(
    settings: TaskSettings,
    onUpdate: (TaskSettingState) -> Unit,
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    val sheetState = rememberModalBottomSheetState(skipPartiallyExpanded = true)
    val scope = rememberCoroutineScope()
    val maxDisplayCount = 3
    val avatarSize = 30.dp
    val offsetX = 8.dp

    SettingSection(
        title = "Members:",
        stackAlignment = Alignment.End,
        alignment = Alignment.CenterEnd,
        onClick = {
            when {
                settings.isSubTask -> CommonUI.showToast(context, "This field cannot be changed") 
                else -> scope.launch { sheetState.show() }
            }
        },
        modifier = modifier
    ) {
        when {
            settings.members.isNotEmpty() -> MemberAvatarList(
                modifier = Modifier
                    .padding(end = if (settings.memberToUsers.size > 1) dimens.zero else dimens.normal),
                members = settings.memberToUsers,
                maxDisplayCount = maxDisplayCount,
                avatarSize = avatarSize,
                offsetX = offsetX
            )

            else -> AddMemberButton(
                modifier = Modifier.padding(end = dimens.medium),
                avatarSize = avatarSize
            )
        }

        if (sheetState.isVisible) {
            ModalBottomSheet(
                onDismissRequest = { scope.launch { sheetState.hide() } },
                sheetState = sheetState,
                containerColor = Color.White,
            ) {
                TaskMemberSelectView(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height((LocalConfiguration.current.screenHeightDp * 0.8f).dp),
                    selectedMembers = settings.memberToUsers,
                    onDone = { selectedMembers ->
                        val copy = settings.copy(
                            members = selectedMembers
                        )
                        onUpdate(
                            TaskSettingState(
                                settings = copy,
                                action = TaskSettingAction.UPDATE_MEMBERS
                            )
                        )
                        scope.launch { sheetState.hide() }
                    },
                    onDismiss = { scope.launch { sheetState.hide() } }
                )
            }
        }
    }
}

@Composable
private fun MemberAvatarList(
    modifier: Modifier = Modifier,
    members: List<ai_User>,
    maxDisplayCount: Int,
    avatarSize: Dp,
    offsetX: Dp
) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.CenterEnd
    ) {
        members.take(maxDisplayCount).forEachIndexed { index, user ->
            Box(
                modifier = Modifier.offset(
                    x = -(index.toFloat() * (avatarSize - offsetX).value).dp
                ),
                contentAlignment = Alignment.CenterEnd
            ) {
                UserAvatarView(user = user, size = avatarSize, backgroundColor = Color.White)
            }
        }

        if (members.size > maxDisplayCount) {
            Box(
                modifier = Modifier.offset(
                    x = -(maxDisplayCount.toFloat() * (avatarSize - offsetX).value).dp
                ),
                contentAlignment = Alignment.CenterEnd
            ) {
                MoreMembersButton(avatarSize = avatarSize)
            }
        }
    }
}

@Composable
private fun MoreMembersButton(avatarSize: Dp) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .size(avatarSize)
            .background(Color.White)
            .dashedBorder(1.dp, colors.color9C9C9C, CircleShape)
    ) {
        Icon(
            imageVector = Icons.Filled.MoreHoriz,
            contentDescription = null,
            tint = colors.color9C9C9C,
            modifier = Modifier.size(avatarSize / 2)
        )
    }
}

@Composable
private fun AddMemberButton(
    modifier: Modifier = Modifier,
    avatarSize: Dp
) {
    Box(
        contentAlignment = Alignment.Center,
        modifier = modifier
            .size(avatarSize)
            .background(Color.White)
            .dashedBorder(1.dp, colors.color9C9C9C, CircleShape)
    ) {
        Icon(
            imageVector = Icons.Sharp.Add,
            contentDescription = null,
            tint = colors.color9C9C9C,
            modifier = Modifier.size(avatarSize / 2)
        )
    }
}

@Composable
private fun SettingSection(
    title: String,
    stackAlignment: Alignment.Horizontal,
    alignment: Alignment,
    modifier: Modifier = Modifier,
    onClick: () -> Unit = {},
    content: @Composable () -> Unit
) {
    Column(
        modifier = modifier.clickableWithoutRipple(
            onClick = onClick
        ),
        horizontalAlignment = stackAlignment,
    ) {
        Text(
            text = title,
            style = TextStyle(
                fontWeight = FontWeight.SemiBold,
                fontSize = 16.sp,
                color = colors.colorB3B4B4
            )
        )
        Box(
            modifier = Modifier.height(dimens.extraLarge),
            contentAlignment = when(alignment) {
                Alignment.Start -> Alignment.CenterStart
                Alignment.End -> Alignment.CenterEnd
                else -> Alignment.Center
            }
        ) {
            content()
        }
    }
}

@Composable
fun UserAvatarView(
    user: ai_User,
    fontWeight: FontWeight = FontWeight.SemiBold,
    fontSize: TextUnit = 12.sp,
    size: Dp,
    backgroundColor: Color = Color.Transparent
) {
    val avatarColor = Color.hex(user.getAvatarColor()) ?: Color.Gray
    Box(
        modifier = Modifier
            .size(size)
            .background(backgroundColor)
            .dashedBorder(1.dp, avatarColor, CircleShape)
    ) {
        Text(
            text = user.getNameLetters() ?: "",
            fontSize = fontSize,
            fontWeight = fontWeight,
            color = avatarColor,
            modifier = Modifier.align(Alignment.Center)
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
private fun TaskSettingsViewPreview() {
    val settings = remember {
        mutableStateOf(
            TaskSettings(
                dueDate = Date(),
                status = "In Progress",
                statusCode = "#FF0000",
                priority = TaskPriority.HIGH,
                taskCategory = "Category 1",
                members = emptyList()
            )
        )
    }

    val emptySettings = TaskSettings()

    SnapTheme {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(dimens.normal)
        ) {
            TaskSettingsView(
                settings = settings.value,
                onUpdate = { state -> settings.value = state.settings }
            )

            Text("Empty settings")
            TaskSettingsView(emptySettings, onUpdate = {})
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFFFFFFFF)
@Composable
private fun MemberAvatarListPreview() {
    val members = listOf(
        ai_User().apply { iCustomerID = 1; sName = "John Doe"; sEmail = "<EMAIL>" },
        ai_User().apply { iCustomerID = 2; sName = "Jane Smith"; sEmail = "<EMAIL>" },
        ai_User().apply { iCustomerID = 3; sName = "Alice Johnson"; sEmail = "<EMAIL>" },
        ai_User().apply { iCustomerID = 4; sName = "Bob Brown"; sEmail = "<EMAIL>" }
    )

    SnapTheme {
        MemberAvatarList(
            members = members,
            maxDisplayCount = 3,
            avatarSize = 30.dp,
            offsetX = 5.dp
        )
    }
}