package com.snapinspect.snapinspect3.views.composables

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.LocalTextStyle
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import com.snapinspect.snapinspect3.R

private val iconSpacing = 8.dp
private const val placeholderAlpha = 0.3f

@Composable
fun FitHeightTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    leadingIcon: @Composable (() -> Unit)? = null,
    trailingIcon: @Composable (() -> Unit)? = null,
    placeholderText: String = "",
    paddingHorizontal: Dp = 8.dp,
    keyboardOptions: KeyboardOptions = KeyboardOptions.Default,
    keyboardActions: KeyboardActions = KeyboardActions.Default,
    fontSize: TextUnit = MaterialTheme.typography.body2.fontSize,
    textColor: Color = MaterialTheme.colors.onSurface,
    textContainerColor: Color = MaterialTheme.colors.surface,
    textAlign: TextAlign = TextAlign.Start,
    singleLine: Boolean = true,
    maxLines: Int = if (singleLine) 1 else Int.MAX_VALUE,
    minLines: Int = 1,
    borderColor: Color = Color.Transparent,
    borderWidth: Float = 0f,
    cornerRadius: Dp = 4.dp
) {
    val shape = RoundedCornerShape(cornerRadius)
    val cursorColor = SolidColor(MaterialTheme.colors.primary)
    BasicTextField(
        value = value,
        onValueChange = onValueChange,
        modifier = modifier
            .background(textContainerColor, shape)
            .border(borderWidth.dp, borderColor, shape)
            .fillMaxWidth(),
        singleLine = singleLine,
        maxLines = maxLines,
        minLines = minLines,
        cursorBrush = cursorColor,
        textStyle = LocalTextStyle.current.copy(
            color = textColor,
            fontSize = fontSize,
            textAlign = textAlign
        ),
        keyboardOptions = keyboardOptions,
        keyboardActions = keyboardActions,
        decorationBox = { innerTextField ->
            TextFieldDecoration(
                value = value,
                placeholderText = placeholderText,
                fontSize = fontSize,
                paddingHorizontal = paddingHorizontal,
                leadingIcon = leadingIcon,
                trailingIcon = trailingIcon,
                innerTextField = innerTextField
            )
        }
    )
}

@Composable
private fun TextFieldDecoration(
    value: String,
    placeholderText: String,
    fontSize: TextUnit,
    paddingHorizontal: Dp = 8.dp,
    leadingIcon: @Composable (() -> Unit)?,
    trailingIcon: @Composable (() -> Unit)?,
    innerTextField: @Composable () -> Unit
) {
    Row(
        modifier = Modifier.padding(horizontal = paddingHorizontal),
        verticalAlignment = Alignment.CenterVertically
    ) {
        if (leadingIcon != null) {
            leadingIcon()
            Spacer(modifier = Modifier.width(iconSpacing))
        }
        Box(Modifier.weight(1f)) {
            if (value.isEmpty()) {
                Text(
                    text = placeholderText,
                    style = LocalTextStyle.current.copy(
                        color = MaterialTheme.colors.onSurface.copy(alpha = placeholderAlpha),
                        fontSize = fontSize
                    )
                )
            }
            innerTextField()
        }
        if (trailingIcon != null) {
            Spacer(modifier = Modifier.width(iconSpacing))
            trailingIcon()
        }
    }
}

@Composable
@Preview(showBackground = true)
fun FitHeightTextFieldPreview() {
    var text by remember { mutableStateOf("1.0") }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.White),
        contentAlignment = Alignment.Center
    ) {
        FitHeightTextField(
            value = text,
            onValueChange = { text = it },
            modifier = Modifier.padding(16.dp).width(80.dp).height(40.dp),
            textAlign = TextAlign.Center,
            textContainerColor = Color.LightGray,
            borderColor = colorResource(id = R.color.light_gray),
            borderWidth = 1f,
            cornerRadius = 10.dp
        )
    }
}