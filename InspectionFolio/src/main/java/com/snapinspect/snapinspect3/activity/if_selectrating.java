package com.snapinspect.snapinspect3.activity;

import android.app.ActionBar;
import android.app.Activity;
import android.content.Context;
import android.os.Bundle;
import android.text.InputType;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonInsItem;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_ArrayOptions;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.R;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_CONTROL_TYPE_SEL;

public class if_selectrating extends Activity {
    private int iPosition = 0;
    private int iInsItemID = 0;
    private ai_InsItem oInsItem;
    private ai_InsItem oPInsItem;
    String sConfig = "";
    int sType;
    String sContent = "";
    String sValue = "";
    boolean bAdd = false;
    String sDelimiter = ",";
    private SelectRatingListAdapter adapter;
    private ArrayList<ai_ArrayOptions> lsOptions = new ArrayList<ai_ArrayOptions>();
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_selectrating);
        ActionBar oBar = getActionBar();
        oBar.show();
        oBar.setDisplayHomeAsUpEnabled(true);

        iPosition = getIntent().getIntExtra(Constants.Extras.iPosition, 0);
        iInsItemID = getIntent().getIntExtra(Constants.Extras.iInsItemID, 0);
        oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);
    }
    @Override
    protected void onResume() {
        try {
            super.onResume();
            oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);
            if (oInsItem.iPInsItemID > 0){
                oPInsItem = ai_InsItem.findById(ai_InsItem.class, (long)oInsItem.iPInsItemID);
            }
            getActionBar().setTitle(oInsItem.sName);

            sConfig = CommonHelper.GetConfig(iPosition, oInsItem, oPInsItem);
            sType = CommonInsItem.getControlType(sConfig);
            sContent = CommonJson.GetJsonKeyValue("Content", sConfig);
            bAdd = CommonHelper.getInt(CommonJson.GetJsonKeyValue("bAdd", sConfig)) == 1;
            lsOptions = new ArrayList<>();


            List<String> lsContent = Arrays.asList(CommonInsItem.GetConfig_StringArray(sContent));
            sValue = CommonHelper.GetValue(iPosition, oInsItem) == null ? "" : CommonHelper.GetValue(iPosition, oInsItem);
            String[] arrValue = sValue.split(",");
            for (String c : lsContent){
                if (c != null && c.trim().length() >0) {
                    lsOptions.add((new ai_ArrayOptions(c.trim(), false)));
                }
            }
            for (String c : arrValue){
                if (c != null && c.trim().length() >0) {
                    if (lsContent.contains(c)) {
                        for (ai_ArrayOptions oOption : lsOptions) {
                            if (oOption.sText.equalsIgnoreCase(c)) {
                                oOption.bCheck = true;
                                break;
                            }
                        }
                    } else {
                        lsOptions.add((new ai_ArrayOptions(c, true)));
                    }
                }
            }

            adapter = new SelectRatingListAdapter(this,
                    R.layout.frag_assets_textview, lsOptions == null ? (new ArrayList<ai_ArrayOptions>()) : lsOptions);
            ListView oListView = findViewById(R.id.lv_SelectRating);

            oListView.setAdapter(adapter);

            adapter.notifyDataSetChanged();

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_selectrating.onResume", ex, this);
        }
        // Normal case behavior follows
    }
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {

        getMenuInflater().inflate(R.menu.menu_if_selectrating, menu);
        if (!bAdd){
            MenuItem oItem = menu.findItem( R.id.action_add_selectrating);
            oItem.setVisible(false);
        }
        return true;
    }
    @Override
    public boolean onOptionsItemSelected( MenuItem item ) {

        try {
            if (item.getItemId() == R.id.action_add_selectrating) {
                ShowAddAlert();
            } else if (item.getItemId() == R.id.action_complete_selectrating) {
                onBackPressed();
            } else {
                onBackPressed();
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_selectrating.onOptionItemsSelected", ex, this);
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        SaveItems();
        super.onBackPressed();
    }

    private void SaveItems() {
        StringBuilder oValue = new StringBuilder();
        for (ai_ArrayOptions cc : lsOptions){
            if (cc.bCheck) {
                oValue.append(cc);
                oValue.append(",");
            }
        }
        sValue = oValue.toString();
        if (sValue.endsWith(",")) {
            sValue = sValue.substring(0, sValue.length() - 1);
        }

        CommonHelper.SetValue(iPosition, oInsItem, sValue);
        oInsItem.save();
    }

    private void ShowAddAlert() {
        try {
            final EditText input = new EditText(this);
            input.setText("");
            String sTitleText = "New Item Name";
            if (oPInsItem == null) {
                sTitleText = "New Name";
            }

            new MaterialDialog.Builder(this)
                    .title(sTitleText)
                    .content(null)
                    .inputType(InputType.TYPE_CLASS_TEXT)
                    .input("", "", (dialog, inputValue) -> {
                        String sValue = inputValue.toString();
                        if (sValue.equalsIgnoreCase("")) {
                            Toast.makeText(getBaseContext(), "This field can not be empty.", Toast.LENGTH_LONG).show();
                        } else if (sValue.contains(",")) {
                            Toast.makeText(getBaseContext(), "Comma is not allowed for this field. Please remove Comma and try again.", Toast.LENGTH_LONG).show();
                        } else {
                            if (sType == SI_CONTROL_TYPE_SEL) {
                                //need to complete here about mSEL, SEL, and click again to cancel.
                                for (ai_ArrayOptions o : lsOptions) {
                                    o.bCheck = false;
                                }
                            }
                            lsOptions.add(new ai_ArrayOptions(sValue, true));
                            adapter.UpdateItems(lsOptions);
                            adapter.notifyDataSetChanged();
                        }
                    }).show();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_ins_full.ShowAddAlert", ex, this);
        }
    }

    private class SelectRatingListAdapter extends ArrayAdapter<ai_ArrayOptions> {

        HashMap<ai_ArrayOptions, Integer> mIdMap = new HashMap<ai_ArrayOptions, Integer>();
        Context oContext;

        public SelectRatingListAdapter(Context context, int textViewResourceId,
                                       List<ai_ArrayOptions> objects) {
            super(context, textViewResourceId, objects);
            this.oContext = context;
            for (int i = 0; i < objects.size(); ++i) {
                mIdMap.put(objects.get(i), i);
            }
        }

        public void UpdateItems(List<ai_ArrayOptions> lsArrayOptions) {

            mIdMap = new HashMap<ai_ArrayOptions, Integer>();
            for (int i = 0; i < lsArrayOptions.size(); ++i) {
                mIdMap.put(lsArrayOptions.get(i), i);

            }

        }

        @Override
        public long getItemId(int position) {
            ai_ArrayOptions item = getItem(position);
            return mIdMap.get(item);
        }

        @Override
        public boolean hasStableIds() {
            return true;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = (LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            final View row = inflater.inflate(R.layout.frag_assets_textview, parent, false);
            try {
                final ai_ArrayOptions oOption = lsOptions.get(position);
                TextView oTextView = row.findViewById(R.id.txt_AssetAddress);
                oTextView.setText(oOption.sText);
                if (oOption.bCheck) {
                    row.setBackgroundResource(R.color.light_gray);
                }

                row.setOnClickListener(view -> {
                    if (oOption.bCheck) {
                        oOption.bCheck = false;
                    } else {
                        if (sType == SI_CONTROL_TYPE_SEL) {
                            //need to complete here about mSEL, SEL, and click again to cancel.
                            for (ai_ArrayOptions o : lsOptions) {
                                if (!o.sText.equalsIgnoreCase(oOption.sText)) {
                                    o.bCheck = false;
                                }
                            }
                        }

                        oOption.bCheck = !oOption.bCheck;
                        if (oOption.bCheck) {
                            row.setBackgroundResource(R.color.light_gray);
                        } else {
                            row.setBackgroundResource(R.color.white_color);
                        }
                    }
                    adapter.notifyDataSetChanged();
                });
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.getView", ex, oContext);
            }

            return (row);
        }
    }
}
