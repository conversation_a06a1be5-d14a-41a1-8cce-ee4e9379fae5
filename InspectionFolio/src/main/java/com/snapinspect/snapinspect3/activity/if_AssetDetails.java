package com.snapinspect.snapinspect3.activity;

import android.app.ActionBar;
import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.util.Pair;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.ImageButton;
import android.widget.ListView;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.IntentSenderRequest;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.afollestad.materialdialogs.MaterialDialog;
import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Adapter.AssetDetailsAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.Scanner.ColorActivity;
import com.snapinspect.snapinspect3.Scanner.Utils.ScannerUtils;
import com.snapinspect.snapinspect3.activitynew.Edit.if_EditContact_2nd;
import com.snapinspect.snapinspect3.activitynew.Edit.if_EditCustomInfo;
import com.snapinspect.snapinspect3.activitynew.Edit.if_UpdateAsset_2nd;
import com.snapinspect.snapinspect3.activitynew.if_DisplayFloorPlan;
import com.snapinspect.snapinspect3.util.ArrayUtils;
import com.snapinspect.snapinspect3.util.SystemUiHider;
import com.snapinspect.snapinspect3.views.EmptyDataView;
import com.snapinspect.snapinspect3.views.FloorPlanView;
import com.snapinspect.snapinspect3.views.SegmentedControl;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_PTO;
import static com.snapinspect.snapinspect3.activitynew.if_DisplayFloorPlan.FloorPlanLayerType;


/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_AssetDetails extends FragmentActivity
        implements SegmentedControl.Listener, AssetDetailsAdapter.Listener {

    private static final int SEGMENT_ASSET_INFO_INDEX = 0;
    private static final int SEGMENT_CONTACT_INDEX = 1;
    private static final int SEGMENT_CUSTOM_INFO_INDEX = 2;
    private static final int SEGMENT_CONTACT_FLOOR_PLAN_INDEX = 3;

    private final List<O_MapItem<?>> lsAddressInfo = new ArrayList<>();
    private final List<O_MapItem<?>> lsContactInfo = new ArrayList<>();
    private final List<O_MapItem<?>> lsCustomInfo = new ArrayList<>();
    private final List<O_MapItem<?>> lsFloorPlanInfo = new ArrayList<>();

    private List<ai_Contact> lsContact;
    private List<ai_FloorPlan> lsFloorPlan;
    private ai_Assets oAsset;
    private int iPropertyID = 0;
    private AssetDetailsAdapter adapter;
    private SwipeRefreshLayout swipeRefreshLayout;
    private SwipeRefreshLayout emptySwipeRefreshLayout;
    private ImageButton btnAddBlueprint;
    private int selectedControlIndex = 0;
    private ActivityResultLauncher<IntentSenderRequest> scannerLauncher;

    public static Intent newIntent(Context context, int iSAssetID) {
        Intent intent = new Intent(context, if_AssetDetails.class);
        intent.putExtra(Constants.Extras.iSAssetID, iSAssetID);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        SegmentedControl sControl;
        try {
            super.onCreate(savedInstanceState);
            setContentView(R.layout.activity_assetdetails);

            iPropertyID = getIntent().getIntExtra(Constants.Extras.iSAssetID, 0);

            ActionBar oBar = getActionBar();
            oBar.show();
            oBar.setDisplayHomeAsUpEnabled(true);

            swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout);
            emptySwipeRefreshLayout = findViewById(R.id.empty_data_refresh_layout);
            EmptyDataView emptyDataView = findViewById(R.id.view_empty_data);
            emptyDataView.setListener(() ->
                CommonHelper.openURL(this, getString(R.string.blueprints_support_url)));

            ListView listview = findViewById(R.id.acitivity_assetdetails_listview);
            sControl = findViewById(R.id.segmentedControl);
            sControl.setCornerRadius(18);
            sControl.setDeactiveColor(getResources().getColor(R.color.colorPrimary));
            sControl.setActiveColor(getResources().getColor(R.color.white_color));

            List<String> buttonList = new ArrayList<>();
            buttonList.add(SEGMENT_ASSET_INFO_INDEX, "Asset Info");
            buttonList.add(SEGMENT_CONTACT_INDEX, "Contact");
            buttonList.add(SEGMENT_CUSTOM_INFO_INDEX, "Custom Info");
            if (CommonHelper.hasEnabledFloorPlan(this)) {
                buttonList.add(SEGMENT_CONTACT_FLOOR_PLAN_INDEX, "Blueprints");
            }

            sControl.setListener(if_AssetDetails.this, this);
            sControl.setControlItems(buttonList);

            adapter = new AssetDetailsAdapter(this, this);
            listview.setAdapter(adapter);
            listview.setOnItemClickListener((parent, view, position, id) -> {
                O_MapItem item = (O_MapItem) adapter.getItem(position);
                if (item.sType == O_MapItem.kContact && !CommonJson.disableEditAsset(this)) {
                    onEditContact((ai_Contact) item.value);
                }
            });

            btnAddBlueprint = findViewById(R.id.button_add_blueprint);
            btnAddBlueprint.setOnClickListener(v -> onButtonAddBlueprintClicked());

            scannerLauncher = registerForActivityResult(
                new ActivityResultContracts.StartIntentSenderForResult(), result -> {
                    if (result.getResultCode() == Activity.RESULT_OK) {
                        Uri imageUri = ScannerUtils.getImageUriFromActivityResult(result);
                        if (imageUri != null) {
                            Intent intent = new Intent(this, ColorActivity.class);
                            intent.putExtra(Constants.Extras.iSAssetID, oAsset.iSAssetID);
                            intent.putExtra(Constants.Extras.CROP_PATH, imageUri.getPath());
                            startActivityForResult(intent, Constants.RequestCodes.SCAN_DOCUMENT_REQUEST);
                        }
                    }
                });

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_assetdetails.onCreate", ex, this);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        lsContact = SugarRecord.find(ai_Contact.class, "I_S_Asset_ID = ? AND B_DELETED = 0", String.valueOf(iPropertyID));
        List<ai_Assets> lsAssets = SugarRecord.find(ai_Assets.class, "I_S_Asset_ID = ?", "" + iPropertyID);
        if (lsAssets != null && lsAssets.size() == 1) {
            oAsset = lsAssets.get(0);
            getActionBar().setTitle(oAsset.sAddressOne);
        }

       // CommonHelper.trackEvent(this, "Android Asset Details Resume", null);
        CommonDB.InsertLog(if_AssetDetails.this, "Event", "Asset Details View");

        // getAssetCustomInfo(true);
        configureAssetInfos(false);
    }

    private void configureAssetInfos(boolean isEditing) {
        O_MapItem item;
        switch (selectedControlIndex) {
            case SEGMENT_ASSET_INFO_INDEX: {
                //Address Tab
                lsAddressInfo.clear();
                if (oAsset != null) {
                    item = new O_MapItem<String>();
                    item.sType = O_MapItem.kAddressInfo;
                    item.value = oAsset.sAddressOne + " " + oAsset.sAddressTwo;
                    item.sInfoName = getResources().getString(R.string.asset_info);
                    item.sActionName = getResources().getString(R.string.edit_asset_info);
                    lsAddressInfo.add(item);

                    //Reference
                    item = new O_MapItem<String>();
                    item.sType = O_MapItem.kMapData;
                    item.sKey = "Reference";
                    item.value = oAsset.sFieldThree; //TODO set asset's reference.

                    lsAddressInfo.add(item);

                    //Manager
                    item = new O_MapItem<String>();
                    item.sType = O_MapItem.kMapData;
                    item.sKey = "Manager";
                    ai_User oCustomer = CommonDB.GetUser(oAsset.iCustomerID);
                    item.value = (oCustomer == null || oCustomer.sName == null) ? "Unknown" : oCustomer.sName;

                    lsAddressInfo.add(item);

                    //Key
                    item = new O_MapItem<String>();
                    item.sType = O_MapItem.kMapData;
                    item.sKey = "Key";
                    item.value = oAsset.sKey;

                    lsAddressInfo.add(item);

                    item = new O_MapItem<String>();
                    item.sType = O_MapItem.kMapData;
                    item.sKey = "Alarm";
                    item.value = oAsset.sAlarm;

                    lsAddressInfo.add(item);
                }

                if (lsAddressInfo.isEmpty()) {
                    item = new O_MapItem<String>();
                    item.sType = O_MapItem.kNone;
                    item.value = "The asset is not available.";

                    lsAddressInfo.add(item);
                }
            }
            break;
            case SEGMENT_CONTACT_INDEX: {
                //Contact Tab
                lsContactInfo.clear();
                if (oAsset != null) {
                    item = new O_MapItem<String>();
                    item.sType = O_MapItem.kAddress;
                    item.value = oAsset.sAddressOne + " " + oAsset.sAddressTwo;
                    lsContactInfo.add(item);
                }

                if (lsContact != null && !lsContact.isEmpty()) {
                    for (ai_Contact contact : lsContact) {
                        item = new O_MapItem<ai_Contact>();
                        item.sType = O_MapItem.kContact;
                        item.value = contact;
                        item.isEditable = true;
                        lsContactInfo.add(item);
                    }
                } else {
                    item = new O_MapItem<String>();
                    item.sType = O_MapItem.kNone;
                    item.value = "You have no contacts added.";

                    lsContactInfo.add(item);
                }
            }
            break;
            case SEGMENT_CUSTOM_INFO_INDEX: {
                //Custom Info Tab
                lsCustomInfo.clear();
                if (oAsset != null) {
                    item = new O_MapItem<String>();
                    item.sType = O_MapItem.kAddressInfo;
                    item.value = oAsset.sAddressOne + " " + oAsset.sAddressTwo;
                    item.sInfoName = getResources().getString(R.string.asset_custom_info);
                    item.sActionName = getResources().getString(R.string.edit_custom_info);
                    lsCustomInfo.add(item);
                }
                try {
                    String sCustomInfo = CommonHelper.GetPreferenceString(this, Constants.Keys.sAssetAttributes);
                    JSONArray arrCustomInfo = new JSONArray(sCustomInfo);
                    for (int i = 0; i < arrCustomInfo.length(); i++) {
                        try {
                            JSONObject oObject = arrCustomInfo.getJSONObject(i);
                            String sTempValue = "", sAssetAttributeID = null;
                            try {
                                JSONObject oTemp = new JSONObject(oAsset.sFieldOne);
                                sAssetAttributeID = Constants.Values.kAssetPropertyPrefix + oObject.getString("iAssetAttributeID");
                                sTempValue = oTemp.getString(sAssetAttributeID);
                            } catch (Exception ee) {

                            }

                            String sLabel = oObject.getString("sLabel");
                            boolean hasPTO = hasPTOValue(sTempValue);
                            boolean isPTOField = oObject.getString("sType").equalsIgnoreCase(SI_S_CONFIG_KEY_PTO);
                            if (isPTOField && (!hasPTO || isEditing)) continue;

                            item = new O_MapItem<String>();
                            item.hasPTO = isPTOField && hasPTO;
                            item.sAssetAttributeID = sAssetAttributeID;
                            item.sType = O_MapItem.kMapData;
                            item.sKey = sLabel;
                            item.value = sTempValue;
                            item.updateCustomInfoType(oObject.getString("sType"));
                            lsCustomInfo.add(item);

                        } catch (Exception ex) {

                        }
                    }
                } catch (Exception ex) {
                    ai_BugHandler.ai_Handler_Exception(ex);
                }
            }
            break;
            case SEGMENT_CONTACT_FLOOR_PLAN_INDEX: {
                // Blueprint Tab
                lsFloorPlan = CommonDB_FloorPlan.getFloorPlans(oAsset.iSAssetID);
                lsFloorPlanInfo.clear();
                if (lsFloorPlan != null && !lsFloorPlan.isEmpty()) {
                    List<List<ai_FloorPlan>> groups = ArrayUtils.split(lsFloorPlan, Constants.Limits.kFloorPlanGroupSize);
                    for (List<ai_FloorPlan> group : groups) {
                        item = new O_MapItem<Pair<ai_FloorPlan, ai_FloorPlan>>();
                        item.sType = O_MapItem.kFloorPlan;
                        item.value = new Pair<>(
                            new FloorPlanView.FloorPlanViewModel(group.get(0), 0, 0),
                            group.size() > 1 ? new FloorPlanView.FloorPlanViewModel(group.get(1), 0, 0) : null);
                        lsFloorPlanInfo.add(item);
                    }
                }
            }
            break;
            default:
                break;
        }

        displayAssetInfo();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (selectedControlIndex == SEGMENT_CONTACT_INDEX && !CommonJson.disableEditAsset(this)) {
            getMenuInflater().inflate(R.menu.menu_add_contact, menu);
        }
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_menu_add_contact) {
            onClickOnAddressHeaderView();
        } else {
            super.onBackPressed();
        }
        return true;
    }

    private void displayAssetInfo() {
        swipeRefreshLayout.setVisibility(View.VISIBLE);
        emptySwipeRefreshLayout.setVisibility(View.GONE);
        switch (selectedControlIndex) {
            case SEGMENT_ASSET_INFO_INDEX:
                adapter.setAssetInfos(lsAddressInfo);
                break;
            case SEGMENT_CONTACT_INDEX:
                adapter.setAssetInfos(lsContactInfo);
                break;
            case SEGMENT_CUSTOM_INFO_INDEX:
                adapter.setAssetInfos(lsCustomInfo);
                break;
            case SEGMENT_CONTACT_FLOOR_PLAN_INDEX:
                if (lsFloorPlanInfo.isEmpty()) {
                    swipeRefreshLayout.setVisibility(View.GONE);
                    emptySwipeRefreshLayout.setVisibility(View.VISIBLE);
                } else {
                    adapter.setAssetInfos(lsFloorPlanInfo);
                }
                break;
        }
    }

    private boolean hasPTOValue(String sValue) {
        return if_FormItem.hasFile(sValue);
    }

    @Override
    public void onSegmentedValueChanged(int selectedIndex) {
        this.selectedControlIndex = selectedIndex;
        invalidateOptionsMenu();
        configureAssetInfos(false);
        if (selectedIndex == SEGMENT_CONTACT_FLOOR_PLAN_INDEX) {
            loadAssetFloorPlans();
            setSwipeRefreshLayout(true, this::loadAssetFloorPlans);
            btnAddBlueprint.setVisibility(CommonJson.disableEditAsset(this) ? View.GONE : View.VISIBLE);
        } else {
            setSwipeRefreshLayout(false, null);
            btnAddBlueprint.setVisibility(View.GONE);
        }
    }

    private void setSwipeRefreshLayout(boolean enable, @Nullable SwipeRefreshLayout.OnRefreshListener listener) {
        swipeRefreshLayout.setEnabled(enable);
        swipeRefreshLayout.setOnRefreshListener(listener);
        emptySwipeRefreshLayout.setEnabled(enable);
        emptySwipeRefreshLayout.setOnRefreshListener(listener);
    }

    private void onEditAddressClicked() {
        Intent oIntent = new Intent(this, if_UpdateAsset_2nd.class);
        oIntent.putExtra(Constants.Extras.iSAssetID, oAsset.iSAssetID);
        oIntent.putExtra(Constants.Extras.iSPAssetID, oAsset.iSPAssetID);
        startActivity(oIntent);
    }

    private void onNewContactClicked() {
        Intent oIntent = new Intent(this, if_EditContact_2nd.class);
        oIntent.putExtra(Constants.Extras.iSAssetID, oAsset.iSAssetID);
        oIntent.putExtra(Constants.Extras.iSContactID, 0);
        startActivity(oIntent);
    }

    private void onEditCustomInfoClicked() {
        Intent oIntent = new Intent(this, if_EditCustomInfo.class);
        oIntent.putExtra(Constants.Extras.iSAssetID, oAsset.iSAssetID);
        startActivity(oIntent);
    }

    private void onButtonAddBlueprintClicked() {
        ScannerUtils.launchDocumentScanner(this, scannerLauncher);
    }

    @Override
    public void onEditContact(ai_Contact contact) {
        Intent oIntent = new Intent(this, if_EditContact_2nd.class);
        oIntent.putExtra(Constants.Extras.iSAssetID, contact.iSAssetID);
        oIntent.putExtra(Constants.Extras.iSContactID, contact.iSContactID);
        startActivity(oIntent);
    }

    @Override
    public void onClickOnFloorPlan(ai_FloorPlan floorPlan) {
        startActivity(if_DisplayFloorPlan.newIntent(this, floorPlan.getId(), FloorPlanLayerType.ASSET));
    }

    @Override
    public void onClickOnAddressHeaderView() {
        switch (selectedControlIndex) {
            case SEGMENT_ASSET_INFO_INDEX:
                onEditAddressClicked();
                break;
            case SEGMENT_CONTACT_INDEX:
                onNewContactClicked();
                break;
            case SEGMENT_CUSTOM_INFO_INDEX:
                onEditCustomInfoClicked();
                break;
            case SEGMENT_CONTACT_FLOOR_PLAN_INDEX:
                break;
        }
    }

    private void loadAssetFloorPlans() {
        if (!NetworkUtils.isNetworkAvailable(this)) {
            CommonUI.ShowAlert(this, getString(R.string.title_alert_error), getString(R.string.failed_connection));
            return;
        }

        MaterialDialog materialDialog = null;
        if (lsFloorPlan != null && !lsFloorPlan.isEmpty()) {
            setLoading(true);
        } else {
            materialDialog = CommonUI.ShowMaterialProgressDialog(this, "Connecting to server...", "Please wait...");
        }
        MaterialDialog finalMaterialDialog = materialDialog;
        CommonFloorPlan.loadFloorPlans(this, oAsset.iSAssetID,
            new CommonFloorPlan.RequestCompletion<List<ai_FloorPlan>>() {
                @Override
                public void onSuccess(List<ai_FloorPlan> floorPlans) {
                    setLoading(false);
                    if (finalMaterialDialog != null) {
                        finalMaterialDialog.dismiss();
                    }
                    lsFloorPlan = floorPlans;
                    configureAssetInfos(false);
                }

                @Override
                public void onFailure(Error error) {
                    setLoading(false);
                    if (finalMaterialDialog != null) {
                        finalMaterialDialog.dismiss();
                    }
                }
            },
            (progress, total) -> {
                if (finalMaterialDialog != null) {
                    finalMaterialDialog.setContent(getString(R.string.message_progress_download_floor_plan, progress, total));
                }
            }
        );
    }

    private void setLoading(boolean isLoading) {
        swipeRefreshLayout.setRefreshing(isLoading);
        emptySwipeRefreshLayout.setRefreshing(isLoading);
    }

}
