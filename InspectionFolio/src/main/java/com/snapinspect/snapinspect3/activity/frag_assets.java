package com.snapinspect.snapinspect3.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonValidate;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_AssetView;
import com.snapinspect.snapinspect3.IF_Object.ai_Assets;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_Asset;
import com.snapinspect.snapinspect3.SI_DB.db_AssetView;
import com.snapinspect.snapinspect3.activitynew.Home.if_HomeTab;
import com.snapinspect.snapinspect3.activitynew.if_existasset;
import com.snapinspect.snapinspect3.util.ThrottledSearch;
import com.softw4re.views.InfiniteListAdapter;
import com.softw4re.views.InfiniteListView;
import org.jetbrains.annotations.NotNull;

import java.util.*;

/**
 * @Created by TerrySun on 11/03/14.
 */

public class frag_assets extends Fragment {
    private static final int ASSETS_PAGE_SIZE = 300;

    public int iSPAssetID = 0;

    public interface Listener {
        void updateBarTitle(String title);
    }

    private List<ai_Assets> lsAssets;
    private AssetsListAdapter adapter;
    private InfiniteListView<ai_Assets> oInfiniteListView;
    private EditText oSearchBar;
    private String sSortString = "";
    private int iCursor = 0;
    private String sGroupPermission = "";
    private int[] lsSelectedAstViewIDs;
    private ai_AssetView assetViewForAllAssets;
    private Listener mListener;

    public void setListener(Listener listener) {
        mListener = listener;
    }
    public void setSortString(String sSort) {
        this.sSortString = sSort;
    }

    @SuppressLint("ClickableViewAccessibility")
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.frag_assets, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        try {
            oSearchBar = view.findViewById(R.id.search_asset);
            oSearchBar.setOnEditorActionListener((v, actionId, event) -> {
                if (EditorInfo.IME_ACTION_SEARCH == actionId) {
                    CommonHelper.hideSoftKeyboard(getActivity());
                    return true;
                }
                return false;
            });
            new ThrottledSearch(getActivity(), searchTerm -> ReloadData()).bindTo(oSearchBar);

            oInfiniteListView = view.findViewById(R.id.lv_Assets);
            updateSelectedAssetViewIDs();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_Assets.main", ex, getActivity());
        }
    }

    public void updateSelectedAssetViewIDs() {
        List<ai_AssetView> selectedAstViews = db_AssetView.getDefaultAvailableAstViews(getContext());
        int[] selectedAstViewIDs = new int[selectedAstViews.size()];
        for (int i = 0; i < selectedAstViews.size(); i++) {
            selectedAstViewIDs[i] = selectedAstViews.get(i).iSAssetViewID;
        }
        lsSelectedAstViewIDs = selectedAstViewIDs;
    }

    @Override
    public void onAttach(@NonNull @NotNull Context context) {
        super.onAttach(context);
        assetViewForAllAssets = db_AssetView.getAssetViewForAllAssets(context);
    }

    @Override
    public void onResume() {
        try {
            super.onResume();
            ReloadData();
           /* if (mListener != null) {
                ai_AssetView v = db_AssetView.getSavedSelectedAssetView(getContext());
                String title;
                title = v != null ? v.sName : getString(R.string.title_activity_if_assets);
                mListener.updateBarTitle(title);
            } */
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_Assets.onResume", ex, getActivity());
        }
        //  hideSoftKeyboard(getActivity());
    }

    /**
     * Reloads the data for the assets list view.
     */
    public void ReloadData() {
        try {
            if (CommonValidate.bCompanyGroupEnabled(this.getActivity())) {
                sGroupPermission = CommonJson.GetJsonKeyValue("_Group", CommonHelper.GetPreferenceString(getActivity(), "PerDetail"));
                sGroupPermission = sGroupPermission == null ? "" : sGroupPermission;
            }

            lsAssets = db_Asset.getAssetList(getContext(), oSearchBar.getText().toString(),
                    iSPAssetID, 0, sSortString, sGroupPermission, lsSelectedAstViewIDs);
            iCursor = 0;
            adapter = new AssetsListAdapter(this,
                    R.layout.frag_assets_textview, lsAssets == null ? (new ArrayList<>()) : lsAssets);
            oInfiniteListView.setAdapter(adapter);
            oInfiniteListView.hasMore(adapter.lsAssets.size() == ASSETS_PAGE_SIZE);
            adapter.notifyDataSetChanged();

            // Update the navigation bar title to the selected asset view name
            if (mListener != null) {
                ai_AssetView v = db_AssetView.getSavedSelectedAssetView(getContext());
                String title = v != null ? v.sName : getString(R.string.title_activity_if_assets);
                mListener.updateBarTitle(title);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_Assets.ReloadData", ex, getActivity());
        }
    }

    private void loadMoreAssets() {
        iCursor = iCursor + 300;
        List<ai_Assets> lsAssetTemp;
        lsAssetTemp = db_Asset.getAssetList(getContext(), oSearchBar.getText().toString(),
                iSPAssetID, iCursor, sSortString, sGroupPermission, lsSelectedAstViewIDs);
        lsAssets.addAll(lsAssetTemp);
        adapter.ReloadMap();
        adapter.notifyDataSetChanged();

        oInfiniteListView.hasMore(lsAssetTemp.size() == ASSETS_PAGE_SIZE);
    }

    public void showRefreshIndicator() {
        if (oInfiniteListView != null)
            oInfiniteListView.startLoading();
    }

    public void hideRefreshIndicator() {
        if (oInfiniteListView != null)
            oInfiniteListView.stopLoading();
    }

    private void onRefreshData() {
        if (getActivity() instanceof if_HomeTab) {
            ((if_HomeTab) getActivity()).syncService();
        }
    }

    public void showAvailableAssetViews() {
        if (getContext() == null || !"1".equals(CommonJson.sFolder(getContext()))) return;

        List<ai_AssetView> availableAstViews = db_AssetView.getAvailableAssetViewsForCurrentUser(getContext());
        if (availableAstViews == null || availableAstViews.isEmpty()) return;

        if (assetViewForAllAssets != null) {
            availableAstViews.add(0, assetViewForAllAssets);
        }
        List<String> selectedAstViewNames = new ArrayList<>();
        for (ai_AssetView astView : availableAstViews) {
            selectedAstViewNames.add(astView.sName);
        }

        new MaterialDialog.Builder(getContext())
                .title(R.string.select_asset_view)
                .items(selectedAstViewNames)
                .itemsCallback((dialog, itemView, position, text) -> {
                    ai_AssetView selectedAstView = availableAstViews.get(position);
                    if (selectedAstView.iSAssetViewID == assetViewForAllAssets.iSAssetViewID) {
                        CommonHelper.SavePreference(getContext(), Constants.Keys.kSelectedAssetViewID, "");
                    } else {
                        CommonHelper.SavePreference(getContext(),
                                Constants.Keys.kSelectedAssetViewID,
                                String.valueOf(selectedAstView.iSAssetViewID));
                    }
                    updateSelectedAssetViewIDs();
                    ReloadData();
                })
                .show();
    }

    private class AssetsListAdapter extends InfiniteListAdapter<ai_Assets> {

        HashMap<ai_Assets, Integer> mIdMap = new HashMap<>();
        List<ai_Assets> lsAssets;
        frag_assets mFragAssets;

        public AssetsListAdapter(frag_assets fragAssets, int textViewResourceId, List<ai_Assets> objects) {
            super(fragAssets.getActivity(), textViewResourceId, (ArrayList<ai_Assets>) objects);
            mFragAssets = fragAssets;
            lsAssets = objects;
            for (int i = 0; i < objects.size(); ++i) {
                mIdMap.put(objects.get(i), i);
            }
        }

        public void ReloadMap() {
            for (int i = 0; i < lsAssets.size(); ++i) {
                mIdMap.put(lsAssets.get(i), i);
            }
        }

        @Override
        public long getItemId(int position) {
            ai_Assets item = getItem(position);
            return mIdMap.get(item);
        }

        @Override
        public boolean hasStableIds() {
            return true;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = (LayoutInflater) mFragAssets.getActivity().getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            View row;

            row = inflater.inflate(R.layout.frag_assets_textview, parent, false);
            try {
                final ai_Assets oAsset = lsAssets.get(position);
                TextView oTextView = row.findViewById(R.id.txt_AssetAddress);
                oTextView.setText(String.format("%s, %s", oAsset.sAddressOne, oAsset.sAddressTwo));
                TextView oTextView_Info = row.findViewById(R.id.txt_AssetAddress_Info);

                if (oAsset.sFieldThree == null || oAsset.sFieldThree.trim().length() == 0) {
                    oTextView_Info.setVisibility(View.GONE);
                } else {
                    oTextView_Info.setText(oAsset.sFieldThree);
                    oTextView_Info.setVisibility(View.VISIBLE);
                }

            } catch (Exception exx) {
                exx.printStackTrace();
            }
            return row;
        }

        @Override
        public void onNewLoadRequired() {
            mFragAssets.showRefreshIndicator();
            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                mFragAssets.loadMoreAssets();
                mFragAssets.hideRefreshIndicator();
            }, 300);
        }

        @Override
        public void onRefresh() {
            mFragAssets.onRefreshData();
        }

        @Override
        public void onItemClick(int i) {
            ai_Assets item = this.getItem(i);
            startActivity(if_existasset.newIntent(getActivity(), item.iSAssetID));
        }

        @Override
        public void onItemLongClick(int i) {
            // Do nothing
        }
    }
}
