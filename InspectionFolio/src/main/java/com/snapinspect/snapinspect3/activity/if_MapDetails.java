package com.snapinspect.snapinspect3.activity;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Color;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.net.Uri;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.app.Activity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import android.util.Log;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.afollestad.materialdialogs.MaterialDialog;
import com.google.android.gms.maps.CameraUpdate;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.MapFragment;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.CameraPosition;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.google.android.gms.maps.model.PolylineOptions;
import com.orm.query.Condition;
import com.orm.query.Select;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_AssetLayout;
import com.snapinspect.snapinspect3.IF_Object.ai_Assets;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_CheckList;
import com.snapinspect.snapinspect3.IF_Object.ai_InsType;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.IF_Object.ai_Layout;
import com.snapinspect.snapinspect3.IF_Object.ai_Schedule;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_Inspection;
import com.snapinspect.snapinspect3.activitynew.fragments.ItemSelectionDialog;
import com.snapinspect.snapinspect3.activitynew.fragments.InsTypeSelection;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Ins_3rd;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Layout_3rd;

import com.snapinspect.snapinspect3.util.ArrayUtils;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.DecimalFormat;
import java.util.*;

public class if_MapDetails extends Activity implements OnMapReadyCallback {
    ai_Schedule oSchedule;
    ai_Assets oAsset;
    int iSAssetID;
    private  static int markType = 0;
    private static int mapType = 0;
    private static final String TAG = "MapDetails";
    MapFragment mapFragment;
    private GoogleMap mMap;
    List<Marker> markersList = new ArrayList<Marker>();
    LatLngBounds.Builder builder;
    CameraUpdate cu;
    String dateTime;
    Marker currentMarker = null;
    double curLatitude = 0.0, curLongitude = 0.0;
    public final static String MODE_DRIVING = "driving";
    public final static String MODE_WALKING = "walking";
    TextView distanceText, durationText;
    LinearLayout estimatedLt;
    MaterialDialog oDialog = null;
    private List<ai_InsType> lsInsType;
    private boolean isShowed = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(com.snapinspect.snapinspect3.R.layout.activity_if__map_details);

        CommonHelper.hideSoftKeyboard(this);
        getActionBar().setDisplayHomeAsUpEnabled(true);

        distanceText = findViewById(R.id.distanceText);
        durationText = findViewById(R.id.durationText);
        estimatedLt = findViewById(R.id.estimatedLayout);

        try {
            mapType = getIntent().getIntExtra("MapType", 0);
            markType = getIntent().getIntExtra("MarkType", 0);
            TextView addText = findViewById(R.id.addText);

            if (mapType == 0) {
                int sID = getIntent().getIntExtra("ScheduleID", 0);
                int iAssetID = getIntent().getIntExtra("AssetID", 0);
                if (iAssetID == 0) {
                    oSchedule = CommonDB_Schedule.GetScheduleBySScheduleID(sID);
                }
                else{
                    oAsset = CommonDB_Assets.GetAssetBy_iSAssetID(iAssetID);
                }

                addText.setText(oSchedule.sAddressOne + "," + oSchedule.sAddressTwo);
                TextView dateText = findViewById(R.id.dateText);
                dateText.setText(oSchedule.dtDateTime);
                TextView iTypeText = findViewById(R.id.typeText);

                List<ai_InsType> lsInsType = ai_InsType.find(ai_InsType.class, "I_S_INS_TYPE_ID = ?", "" + oSchedule.iSInsTypeID);
                if (lsInsType != null && lsInsType.size() == 1) {
                    ai_InsType oInsType = lsInsType.get(0);
                    iTypeText.setText("Type: " + oInsType.sInsTitle);
                }

                dateText.setText("Date: " + oSchedule.dtDateTime);
                dateTime = oSchedule.dtDateTime;


                getActionBar().setTitle(oSchedule.sAddressOne +  "," + oSchedule.sAddressTwo);

            } else {
                LinearLayout slayout = findViewById(R.id.scheduleLayout);
                slayout.setVisibility(View.GONE);

                iSAssetID = getIntent().getIntExtra(Constants.Extras.iSPAssetID, 0);

                List<ai_Assets> lsAssets = ai_Assets.find(ai_Assets.class, "I_S_ASSET_ID = ?", "" + iSAssetID);
                if (lsAssets != null && lsAssets.size() == 1) {
                    oAsset = lsAssets.get(0);
                }

                getActionBar().setTitle(oAsset.sAddressOne +  "," + oAsset.sAddressTwo);
                addText.setText(oAsset.sAddressOne + "," + oAsset.sAddressTwo);
                dateTime = "";
            }

            mapFragment = (MapFragment) getFragmentManager()
                    .findFragmentById(R.id.map);

            Button routeBtn = findViewById(R.id.routeBtn);
            routeBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (markersList.size() > 0) {
                        Marker m = markersList.get(0);
                        Intent intent = new Intent(android.content.Intent.ACTION_VIEW,
                                Uri.parse("http://maps.google.com/maps?saddr=" + curLatitude + "," + curLongitude + "&daddr=" + m.getPosition().latitude + "," + m.getPosition().longitude));
                        startActivity(intent);
                    }
                }
            });

            Button startIPBtn = findViewById(R.id.inspectionBtn);
            startIPBtn.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (mapType == 0)
                        ViewSchedule(oSchedule);
                    else
                        SelectInsType();
                }
            });
            if (Build.VERSION.SDK_INT >= 23) {
                int iLocationPermission = ContextCompat.checkSelfPermission(if_MapDetails.this,
                        Manifest.permission.ACCESS_FINE_LOCATION);

                if (iLocationPermission != PackageManager.PERMISSION_GRANTED ){
                    ActivityCompat.requestPermissions(if_MapDetails.this,
                            new String[]{android.Manifest.permission.ACCESS_FINE_LOCATION},
                            0);
                }
            }
        }catch (Exception e) {
            e.printStackTrace();
        }
    }


    private void ConstructInsType(){
        if (!CommonValidate.validateNewInspection(this)) return;
        
        if (ArrayUtils.isEmpty(lsInsType)) {
            lsInsType = ai_InsType.listAll(ai_InsType.class);
        }

        if (ArrayUtils.isNotEmpty(lsInsType)) {
            List<InsTypeSelection> lsInsTypeSelection = new ArrayList<>();
            for (ai_InsType oInsType : lsInsType) {
                lsInsTypeSelection.add(InsTypeSelection.fromInsType(oInsType));
            }

            new ItemSelectionDialog<>(
                    this, getString(R.string.title_choose_inspection_type), lsInsTypeSelection,
                    () -> SharedConfig.getInstance(this).getRecentInsTypes(),
                    selection -> {
                        ai_InsType insType = db_Inspection.GetInsTypeBySInsTypeID(selection.getId());
                        SelectedInsType(insType, lsInsType.size());
                        SharedConfig.getInstance(this).saveInsType(selection.getId());
                        return null;
                    }
            ).show();
        }
    }
    private void SelectInsType(){
        try {
            if (!CommonUI.bAppPermission(this)) return;
            if (CommonDB.ValidateChildExist(if_MapDetails.this, iSAssetID) > 0){
//                AlertDialog.Builder oBuilder =  CommonUI.GetAlertBuilder("Action", "", if_MapDetails.this, true, false);

                String[] lsOption = new String[]{"Inspect the Asset", "View Apartments"};

//                oBuilder.setItems(lsOption, new DialogInterface.OnClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialogInterface, int i) {
//                        if (i == 0){
//                            ConstructInsType();
//                        }
//                        else{
//                            Intent oIntent = new Intent(if_MapDetails.this, if_Asset_2nd.class);
//                            // oIntent.putExtra("lClientObjectID", item.lClientObjectID);
//                            oIntent.putExtra(Constants.Extras.iSPAssetID, oAsset.iSAssetID);
//                            oIntent.putExtra("sTitle", oAsset.sAddressOne);
//                            startActivity(oIntent);
//                        }
//                    }
//                });
//
//                oBuilder.show();

                new MaterialDialog.Builder(this)
                        .title(R.string.alert_title_action)
                        .items(lsOption)
                        .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                            @Override
                            public boolean onSelection(MaterialDialog dialog, View view, int i, CharSequence text) {
                                if (i == 0){
                                    ConstructInsType();
                                }
                                else{
                                    Intent oIntent = new Intent(if_MapDetails.this, if_Asset_2nd.class);
                                    // oIntent.putExtra("lClientObjectID", item.lClientObjectID);
                                    oIntent.putExtra(Constants.Extras.iSPAssetID, oAsset.iSAssetID);
                                    oIntent.putExtra("sTitle", oAsset.sAddressOne);
                                    startActivity(oIntent);
                                }
                                return true;
                            }
                        })
                        .negativeText(R.string.md_cancel_label)
                        .show();
            }
            else{
                ConstructInsType();
            }

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.SelectInsType", ex, this);
        }
    }
    private void SelectedInsType(ai_InsType oInsType, int iInsTypeCount){
        new AsyncLoadInspection().execute(oInsType);
    }
    class AsyncLoadInspectionResult {
        public int iInspetionID = 0;
        public int iSAssetID = 0;
        public int iSInsTypeID = 0;
        public String sAddress1 = "";
        public String sAddress2 = "";
        public int iSScheduleID = 0;
        public AsyncLoadInspectionResult(){

        }
    }
    class AsyncLoadInspection extends AsyncTask<ai_InsType, Void, AsyncLoadInspectionResult> {
        MaterialDialog pd;
        @Override
        protected void onPreExecute() {
            super.onPreExecute();
            pd = CommonUI.ShowMaterialProgressDialog(if_MapDetails.this, "Message", "Processing...");
//            pd = CommonUI.GetProgressDialog(if_MapDetails.this, "Message", "Processing...", true);

        }

        @Override
        protected AsyncLoadInspectionResult doInBackground(ai_InsType... params) {
            AsyncLoadInspectionResult oResult = new AsyncLoadInspectionResult();
            try{

                List<ai_AssetLayout> lsAssetLayout = CommonDB.GetAssetLayouts(iSAssetID, params[0].sPTC, if_MapDetails.this);
                if (lsAssetLayout == null || lsAssetLayout.size() == 0){
                    List<ai_Layout> lsLayoutItem = Select.from(ai_Layout.class).where(Condition.prop("S_PTC").eq(params[0].sPTC), Condition.prop("I_SP_LAYOUT_ID").eq("" + 0)).list();
                    boolean bPass = true;
                    for (int i=0; i< lsLayoutItem.size(); i++){
                        String sConfig = params[0].sType.equals( "F") ? lsLayoutItem.get(i).sFConfig : lsLayoutItem.get(i).sSConfig;
                        if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null){
                            bPass = false;
                            break;
                        }

                    }
                    if (bPass){
                        IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                        Date oNow = new Date();
                        int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                                if_MapDetails.this, new ArrayList<ai_AssetLayout>(), params[0],
                                oAsset.iSAssetID, "", CommonHelper.sDateToString(oNow),
                                "", oAsset.sAddressOne, oAsset.sAddressTwo, 0, "");
                        //  CommonDB.InsertLog(if_existasset.this, "Ins Setup", "Load Ins By Pass - " +
                        //          oInsType.iSInsTypeID + " - InsTypeCount: " + iInsTypeCount  + " Address1: "
                        //          + oAsset.sAddressOne + ", " + oAsset.sAddressTwo);
                        //GoToInspection(iInspectionID);

                        oResult.iInspetionID = iInspectionID;
                        return oResult;
                    }
                    else{
                        //   CommonDB.InsertLog(if_existasset.this, "Ins Setup", "Setup Layout - "  +
                        //           oInsType.iSInsTypeID + " - InsTypeCount: " + iInsTypeCount  + " Address1: "
                        //           + oAsset.sAddressOne + ", " + oAsset.sAddressTwo);

                        oResult.iSAssetID = oAsset.iSAssetID;
                        oResult.iSInsTypeID = params[0].iSInsTypeID;
                        oResult.sAddress1 = oAsset.sAddressOne;
                        oResult.sAddress2 = oAsset.sAddressTwo;
                        oResult.iSScheduleID = 0;
                        return oResult;
                    }
                }
                else{
                    IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                    int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                            if_MapDetails.this, new ArrayList<ai_AssetLayout>(lsAssetLayout), params[0],
                            oAsset.iSAssetID, "", CommonHelper.sDateToString(null), "",
                            oAsset.sAddressOne, oAsset.sAddressTwo, 0, "");
                    //CommonDB.InsertLog(if_existasset.this, "Ins Setup", "Load Ins From Existing Layout InsTypeID: " +
                    //        oInsType.iSInsTypeID + " - InsTypeCount: " + iInsTypeCount  + " Address1: "
                    //        + oAsset.sAddressOne + ", " + oAsset.sAddressTwo);

                    //GoToInspection(iInspectionID);
                    oResult.iInspetionID = iInspectionID;
                    return oResult;
                }
            }catch(Exception ex){
                ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.SelectedInsType", ex, if_MapDetails.this);
            }
            return oResult;
        }

        @Override
        protected void onPostExecute(AsyncLoadInspectionResult oResult) {
            super.onPostExecute(oResult);
            if (pd != null)
            {
                pd.dismiss();
            }

            if (oResult.iInspetionID > 0){
                GoToInspection(oResult.iInspetionID);
            }
            else if (oResult.iInspetionID == 0 && oResult.iSInsTypeID > 0) {
                startActivity(if_Layout_3rd.newIntent(if_MapDetails.this,
                    oAsset.iSAssetID, oResult.iSInsTypeID, oAsset.sAddressOne, oAsset.sAddressTwo));
            }

        }
    }
    private void ViewSchedule(ai_Schedule oSchedule){
        List<ai_Inspection> lsInspection = ai_Inspection.find(ai_Inspection.class, "I_S_SCHEDULE_ID=? and B_DELETED = 0", "" + oSchedule.iSScheduleID);
        if (lsInspection != null && lsInspection.size() > 0) {
            ai_Inspection oInspection = lsInspection.get(0);
            long lInspectionID = oInspection.getId();
            int iInspection = (int) lInspectionID;
            Intent oIntent = if_Ins_3rd.newIntent(this, iInspection, oInspection.sType, oInspection.sPTC);
            if (CommonHelper.isKioskMode(this)) {
                startActivityForResult(oIntent, 2);
            } else {
                startActivity(oIntent);
            }
            finish();
        } else {
            boolean bPass = true;
            List<ai_InsType> lsInsType = ai_InsType.listAll(ai_InsType.class);
            ai_InsType oInsType = null;
            if (lsInsType != null && lsInsType.size() > 0) {
                for (int i = 0; i < lsInsType.size(); i++) {
                    if (oSchedule.iSInsTypeID == lsInsType.get(i).iSInsTypeID) {
                        oInsType = lsInsType.get(i);
                        break;
                    }
                }
            }
            if (oInsType == null) {
                return;
            }
            List<ai_AssetLayout> lsAssetLayout = CommonDB.GetAssetLayouts(oSchedule.iSAssetID, oSchedule.sPTC, this);
            if (lsAssetLayout == null || lsAssetLayout.size() == 0) {
                List<ai_Layout> lsLayoutItem = Select.from(ai_Layout.class).where(Condition.prop("S_PTC").eq(oSchedule.sPTC), Condition.prop("I_SP_LAYOUT_ID").eq("" + 0)).list();

                for (int i = 0; i < lsLayoutItem.size(); i++) {
                    String sConfig = oSchedule.sType.equals("F") ? lsLayoutItem.get(i).sFConfig : lsLayoutItem.get(i).sSConfig;

                    if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null) {
                        bPass = false;
                        break;
                    }
                }
                if (bPass) {
                    IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                    Date oNow = new Date();
                    int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                            this, new ArrayList<>(), oInsType, oSchedule.iSAssetID, "",
                            CommonHelper.sDateToString(oNow), "", oSchedule.sAddressOne, oSchedule.sAddressTwo,
                            oSchedule.iSScheduleID, oSchedule.getScheduleDate());
                    CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                    GoToInspection(iInspectionID);

                 //  CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                } else {
                    startActivity(if_Layout_3rd.newIntent(this, oSchedule.iSAssetID, oInsType.iSInsTypeID,
                            oSchedule.sAddressOne, oSchedule.sAddressTwo, oSchedule.iSScheduleID, "",
                            oSchedule.getScheduleDate()));
                    finish();
                }
            } else {
                List<ai_CheckList> lsCheckList = ai_CheckList.find(ai_CheckList.class, "s_PTC = ?", oInsType.sPTC);
                ai_CheckList oChecklist = null;
                if (lsCheckList != null && lsCheckList.size() == 1) {
                    oChecklist = lsCheckList.get(0);
                }
                boolean bSameVer = true;
                for (ai_AssetLayout oAL : lsAssetLayout) {
                    if ((oAL.sFieldThree != null) && (!oAL.sFieldThree.equals("" + oChecklist.iLayoutVerID))) {
                        bSameVer = false;
                        break;
                    }

                }
                if (bSameVer) {
                    IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                    int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                            this, new ArrayList<ai_AssetLayout>(lsAssetLayout), oInsType, oSchedule.iSAssetID, "",
                            CommonHelper.sDateToString(null), "", oSchedule.sAddressOne, oSchedule.sAddressTwo,
                            oSchedule.iSScheduleID, oSchedule.getScheduleDate());
                    CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                    GoToInspection(iInspectionID);

                } else {
                    for (ai_AssetLayout oAL : lsAssetLayout) {
                        oAL.delete();
                    }
                    startActivity(if_Layout_3rd.newIntent(this, oSchedule.iSAssetID, oInsType.iSInsTypeID,
                            oSchedule.sAddressOne, oSchedule.sAddressTwo, oSchedule.iSScheduleID, "",
                            oSchedule.getScheduleDate()));
                    finish();
                }
            }
        }
    }

    private void GoToInspection(int iInspectionID) {
        Intent oIntent = if_Ins_3rd.newIntent(this, iInspectionID);
        if (CommonHelper.isKioskMode(if_MapDetails.this)) {
            startActivityForResult(oIntent, 2);
        } else {
            startActivity(oIntent);
        }
        finish();
    }

    private final GoogleMap.OnMyLocationChangeListener myLocationChangeListener = new GoogleMap.OnMyLocationChangeListener() {
        @Override
        public void onMyLocationChange(Location location) {
            LatLng loc = new LatLng(location.getLatitude(), location.getLongitude());

            if(mMap != null){
                if (!isShowed) {
                    isShowed = true;
                    builder = new LatLngBounds.Builder();
                    for (Marker m : markersList) {
                        builder.include(m.getPosition());
                    }
                    builder.include(loc);
                    /**initialize the padding for map boundary*/
                    int padding = 50;
                    /**create the bounds from latlngBuilder to set into map camera*/
                    LatLngBounds bounds = builder.build();
                    /**create the camera with bounds and padding to set into map*/
                    cu = CameraUpdateFactory.newLatLngBounds(bounds, padding);
                    /**call the map call back to know map is loaded or not*/
                    mMap.setOnMapLoadedCallback(new GoogleMap.OnMapLoadedCallback() {
                        @Override
                        public void onMapLoaded() {
                            /**set animated zoom camera into map*/
                            mMap.animateCamera(cu);

                        }
                    });

                }

                if (markersList.size() > 0) {
                    Marker m = markersList.get(0);
                    if (Math.abs(curLatitude - location.getLatitude()) > 10 || Math.abs(curLongitude - location.getLongitude()) > 10) {
                        // Getting URL to the Google Directions API
//                        LatLng newLoc = new LatLng(-36.892088, 174.7077659);
                        String url = getDirectionsUrl(loc, m.getPosition());

                        DownloadTask downloadTask = new DownloadTask();

                        // Start downloading json data from Google Directions API
                        downloadTask.execute(url);
                    }
                }
                    //CalculationByDistance(loc, m.getPosition());
                //    getRoutDistane(m.getPosition().latitude, m.getPosition().longitude, location.getLatitude(), location.getLongitude());

                curLatitude = location.getLatitude();
                curLongitude = location.getLongitude();
            }
        }
    };

    @Override
    public void onMapReady(GoogleMap googleMap) {
        try{
            googleMap.setMapType(GoogleMap.MAP_TYPE_NORMAL);
            googleMap.setTrafficEnabled(true);

            googleMap.getUiSettings().setZoomControlsEnabled(true);

            mMap = googleMap;
            markersList.clear();
            if (mapType == 0)
                showLocationFromAddress(this, oSchedule.sAddressOne + "," + oSchedule.sAddressTwo);
            else
                showLocationFromAddress(this, oAsset.sAddressOne + "," + oAsset.sAddressTwo);

            mMap.setOnMyLocationChangeListener(myLocationChangeListener);
            setMyCurrentLocation(true);

        }catch(SecurityException e){
            e.printStackTrace();
        }catch(Exception e){
            e.printStackTrace();
        }

    }

    public double CalculationByDistance(LatLng StartP, LatLng EndP) {
        int Radius = 6371;// radius of earth in Km
        double lat1 = StartP.latitude;
        double lat2 = EndP.latitude;
        double lon1 = StartP.longitude;
        double lon2 = EndP.longitude;
        double dLat = Math.toRadians(lat2 - lat1);
        double dLon = Math.toRadians(lon2 - lon1);
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2)
                + Math.cos(Math.toRadians(lat1))
                * Math.cos(Math.toRadians(lat2)) * Math.sin(dLon / 2)
                * Math.sin(dLon / 2);
        double c = 2 * Math.asin(Math.sqrt(a));
        double valueResult = Radius * c;
        double km = valueResult / 1;
        DecimalFormat newFormat = new DecimalFormat("####");
        int kmInDec = Integer.valueOf(newFormat.format(km));
        double meter = valueResult % 1000;
        int meterInDec = Integer.valueOf(newFormat.format(meter));
        Log.e("Radius Value", "" + valueResult + "   KM  " + kmInDec
                + " Meter   " + meterInDec);

        distanceText.setText(kmInDec + " Km " + meterInDec + " m");

        return Radius * c;
    }
//calculate Distance and Duration
    private String getDirectionsUrl(LatLng origin,LatLng dest){

        // Origin of route
        String str_origin = "origin="+origin.latitude+","+origin.longitude;

        // Destination of route
        String str_dest = "destination="+dest.latitude+","+dest.longitude;


        // Sensor enabled
        String sensor = "sensor=false";

        // Building the parameters to the web service
        String parameters = str_origin+"&"+str_dest+"&"+sensor;

        // Output format
        String output = "json";

        // Building the url to the web service
        String url = "https://maps.googleapis.com/maps/api/directions/"+output+"?"+parameters;


        return url;
    }

    /** A method to download json data from url */
    private String downloadUrl(String strUrl) throws IOException{
        String data = "";
        InputStream iStream = null;
        HttpURLConnection urlConnection = null;
        try{
            URL url = new URL(strUrl);

            // Creating an http connection to communicate with url
            urlConnection = (HttpURLConnection) url.openConnection();

            // Connecting to url
            urlConnection.connect();

            // Reading data from url
            iStream = urlConnection.getInputStream();

            BufferedReader br = new BufferedReader(new InputStreamReader(iStream));

            StringBuffer sb  = new StringBuffer();

            String line = "";
            while( ( line = br.readLine())  != null){
                sb.append(line);
            }

            data = sb.toString();

            br.close();

        }catch(Exception e){
            Log.e(TAG, "error -->" + e);

        }finally{
            iStream.close();
            urlConnection.disconnect();
        }
        return data;
    }



    // Fetches data from url passed
    private class DownloadTask extends AsyncTask<String, Void, String>{

        // Downloading data in non-ui thread
        @Override
        protected void onPreExecute() {
            super.onPreExecute();

            oDialog = CommonUI.ShowMaterialProgressDialog(if_MapDetails.this, "Message", "Processing Request ...");
//            oDialog = CommonUI.GetProgressDialog(if_MapDetails.this, "Message", "Processing Request ...", true);


        }
        @Override
        protected String doInBackground(String... url) {

            // For storing data from web service
            String data = "";

            try{
                // Fetching the data from web service
                data = downloadUrl(url[0]);
                Log.e(TAG, "url----> " + url[0]);
            }catch(Exception e){
                Log.e("Background Task",e.toString());
            }
            return data;
        }

        // Executes in UI thread, after the execution of
        // doInBackground()
        @Override
        protected void onPostExecute(String result) {
            super.onPostExecute(result);

            ParserTask parserTask = new ParserTask();

            // Invokes the thread for parsing the JSON data
            parserTask.execute(result);
        }
    }
    private void ShowAlert(final String sTitle, String sMessage){
        CommonUI.ShowAlert(this, sTitle, sMessage);
//        AlertDialog.Builder builder = CommonUI.GetAlertBuilder(sTitle, sMessage, if_MapDetails.this, false, true);
//
//        builder.show();

    }
    /** A class to parse the Google Places in JSON format */
    private class ParserTask extends AsyncTask<String, Integer, List<List<HashMap<String,String>>> >{

        // Parsing the data in non-ui thread
        @Override
        protected List<List<HashMap<String, String>>> doInBackground(String... jsonData) {

            JSONObject jObject;
            List<List<HashMap<String, String>>> routes = null;

            try{
                jObject = new JSONObject(jsonData[0]);
                DirectionsJSONParser parser = new DirectionsJSONParser();

                // Starts parsing data
                routes = parser.parse(jObject);
            }catch(Exception e){
                e.printStackTrace();
            }
            return routes;
        }

        // Executes in UI thread, after the parsing process
        @Override
        protected void onPostExecute(List<List<HashMap<String, String>>> result) {
            CommonUI.DismissMaterialProgressDialog(oDialog);
            ArrayList<LatLng> points = null;
            PolylineOptions lineOptions = null;
            MarkerOptions markerOptions = new MarkerOptions();
            String distance = "";
            String duration = "";

            if(result.size()<1){

//                estimatedLt.setVisibility(View.GONE);
                ShowAlert("Error", "Cannot calculate values.");
                return;
            } else {
//                estimatedLt.setVisibility(View.VISIBLE);
            }


            // Traversing through all the routes
            for(int i=0;i<result.size();i++){
                points = new ArrayList<LatLng>();
                lineOptions = new PolylineOptions();

                // Fetching i-th route
                List<HashMap<String, String>> path = result.get(i);

                // Fetching all the points in i-th route
                for(int j=0;j<path.size();j++){
                    HashMap<String,String> point = path.get(j);

                    if(j==0){    // Get distance from the list
                        distance = point.get("distance");
                        continue;
                    }else if(j==1){ // Get duration from the list
                        duration = point.get("duration");
                        continue;
                    }

                    double lat = Double.parseDouble(point.get("lat"));
                    double lng = Double.parseDouble(point.get("lng"));
                    LatLng position = new LatLng(lat, lng);

                    points.add(position);
                }

                // Adding all the points in the route to LineOptions
                lineOptions.addAll(points);
                lineOptions.width(2);
                lineOptions.color(Color.RED);

            }

            distanceText.setText("Distance: " + distance);
            durationText.setText("Duration: " + duration);
            // Drawing polyline in the Google Map for the
            // i-th route
            mMap.addPolyline(lineOptions);
        }
    }

    private String getAddress(double latitude, double longitude) {
        StringBuilder result = new StringBuilder();
        try {
            Geocoder geocoder = new Geocoder(this, Locale.US);
            List<Address> addresses = geocoder.getFromLocation(latitude, longitude, 1);
            if (addresses.size() > 0) {
                Address address = addresses.get(0);
                result.append(address.getLocality()).append("\n");
                result.append(address.getCountryName());
            }
        } catch (IOException e) {
            Log.e("tag", e.getMessage());
        }

        return result.toString();
    }

    public void setMyCurrentLocation(boolean flag) {

        if (ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            // TODO: Consider calling
            return;
        }
        mMap.setMyLocationEnabled(flag);
    }
    public void showLocationFromAddress(Context context, String strAddress) {

        Geocoder coder = new Geocoder(context);
        List<Address> address;
        LatLng p1 = null;

        try {
            address = coder.getFromLocationName(strAddress, 5);
            if (address == null) {
                return;
            }
            Address location = address.get(0);
            location.getLatitude();
            location.getLongitude();

            p1 = new LatLng(location.getLatitude(), location.getLongitude() );

            CameraPosition position = CameraPosition.builder()
                    .target(p1)
                    .zoom(1.5f)
                    .bearing(0.0f)
                    .tilt(0.0f)
                    .build();

            mMap.animateCamera(CameraUpdateFactory
                    .newCameraPosition(position), null);

            MarkerOptions mapOptions;
            if (markType == 1) {
                mapOptions = new MarkerOptions().position(p1).title(strAddress).snippet(dateTime).icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_MAGENTA));
            } else if (markType == 0) {
                mapOptions = new MarkerOptions().position(p1).title(strAddress).snippet(dateTime).icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN));
            } else {
                mapOptions = new MarkerOptions().position(p1).title(strAddress).snippet(dateTime).icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_RED));
            }

            Marker  marker = mMap.addMarker(mapOptions);
            marker.showInfoWindow();
            markersList.add(marker);

        } catch (Exception ex) {

            ex.printStackTrace();
            Log.e(TAG, "error -- " + ex);
        }
    }
    @Override
    public void onResume(){
        super.onResume();
        try{
            mapFragment.getMapAsync(this);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.onResume", ex, this);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        try {
            switch (item.getItemId()) {
                default:
                    finish();
                    onBackPressed();
                    break;
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_quickphrase.onOptionsItemSelected", ex, this);
        }
        return true;
    }
}
