package com.snapinspect.snapinspect3.activity;

import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.util.SystemUiHider;
import com.snapinspect.snapinspect3.R;

import android.app.ActionBar;
import android.app.Activity;
import android.app.Fragment;
import android.app.FragmentTransaction;
import android.os.Bundle;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_Sync extends Activity {
    private String sType;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            ActionBar oBar = getActionBar();
            oBar.show();
            //oBar.setDisplayHomeAsUpEnabled(true);
            //getOverflowMenu();
            oBar.setNavigationMode(ActionBar.NAVIGATION_MODE_TABS);
            setContentView(R.layout.activity_sync);
            sType = getIntent().getStringExtra(Constants.Extras.sType);
            if (sType.equals("I")) {
                Fragment oSyncFrag = new frag_uploadinspection();
                FragmentTransaction oTransaction = getFragmentManager().beginTransaction();
                oTransaction.add(R.id.fragment_SyncContainer, oSyncFrag).commit();
            } else {
                Fragment oSyncFrag = new frag_sync();
                FragmentTransaction oTransaction = getFragmentManager().beginTransaction();
                oTransaction.add(R.id.fragment_SyncContainer, oSyncFrag).commit();
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_sync.onCreate", ex, this);
        }

    }

}
