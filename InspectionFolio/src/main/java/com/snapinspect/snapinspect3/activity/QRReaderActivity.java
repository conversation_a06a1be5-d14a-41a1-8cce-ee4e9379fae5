package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import com.google.zxing.*;
import com.google.zxing.common.HybridBinarizer;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.Helper.IF_RestClient;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.Home.if_HomeTab;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.io.FileNotFoundException;

public class QRReaderActivity extends Activity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_qrreader);
        try {
            getActionBar().hide();

            Intent intent = getIntent();
            String action = intent.getAction();

            // if this is from the share menu
            if (!Intent.ACTION_SEND.equals(action)) finish();
            else {
                Uri imageUri = intent.getParcelableExtra(Intent.EXTRA_STREAM);

                try {
                    Bitmap bm = BitmapFactory.decodeStream(getContentResolver().openInputStream(imageUri));
                    MediaStore.Images.Media.insertImage(getContentResolver(), bm, "111", "");
                    String result = scanQRImage(bm);

                    if (!StringUtils.isEmpty(result) && result.startsWith("SI_ExternalIns:")) {
                        String sToken = result.replace("SI_ExternalIns://", "");
                        if (sToken.contains("_")) {
                            String[] arrToken = sToken.split("_");
                            String downloadableUri = IF_RestClient.getAbsoluteUrl(String.format(
                                    "/SyncExternal/GetRequestExternalAccess?iRequestID=%s&sRequestCode=%s", arrToken[0], arrToken[1]));
                            final String sEmail = CommonHelper.GetPreferenceString(QRReaderActivity.this, "sEmail");
                            if (sEmail == null || sEmail.isEmpty()) {
                                CommonHelper.SavePreference(QRReaderActivity.this, Constants.Settings.bKiosk, "1");
                            }

                            Intent intentTemp = new Intent(QRReaderActivity.this, if_HomeTab.class);
                            intentTemp.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                            intentTemp.putExtra(Constants.Extras.sKioskUri, downloadableUri);
                            startActivity(intentTemp);
                            finish();
                        }
                    } else {
                        ShowAlert("Message", "Sorry, we can not recognize your QR Code. Please try again. ");
                        finish();
                    }
                } catch (FileNotFoundException e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }
    }

    public String scanQRImage(Bitmap bMap) {
        String contents = null;

        int[] intArray = new int[bMap.getWidth()*bMap.getHeight()];
        //copy pixel data from the Bitmap into the 'intArray' array
        bMap.getPixels(intArray, 0, bMap.getWidth(), 0, 0, bMap.getWidth(), bMap.getHeight());

        LuminanceSource source = new RGBLuminanceSource(bMap.getWidth(), bMap.getHeight(), intArray);
        BinaryBitmap bitmap = new BinaryBitmap(new HybridBinarizer(source));

        Reader reader = new MultiFormatReader();
        try {
            Result result = reader.decode(bitmap);
            contents = result.getText();
        }
        catch (Exception e) {
           // Log.e("QrTest", "Error decoding barcode", e);
        }
        return contents;
    }
    private void ShowAlert(String sTitle, String sMessage){
        CommonUI.ShowAlert(this, sTitle, sMessage);
    }
}

