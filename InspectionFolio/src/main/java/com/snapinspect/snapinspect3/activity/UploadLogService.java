package com.snapinspect.snapinspect3.activity;

import android.app.IntentService;
import android.content.Intent;

//import com.amazonaws.services.simpledb.model.BatchPutAttributesRequest;
//import com.amazonaws.services.simpledb.model.ReplaceableAttribute;
//import com.amazonaws.services.simpledb.model.ReplaceableItem;
//import com.amazonaws.services.simpledb.util.SimpleDBUtils;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.IF_SyncClient;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
//import com.amazonaws.services.simpledb.AmazonSimpleDBClient;

import org.json.JSONObject;

import java.util.HashMap;
import java.util.Random;

/**
 * Created by <PERSON><PERSON><PERSON> on 12/10/14.
 */
public class UploadLogService extends IntentService {

    public UploadLogService() {
        super("UploadLogIntentService");
    }
    @Override
    protected void onHandleIntent(Intent workIntent) {
        try{
          /*  ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
            lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
            lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));*/

            HashMap<String, String> lsParams = new HashMap<String, String>();
            lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));

            JSONObject oJson = IF_SyncClient.PostRequest("/Sync/WriteClientLog", lsParams);
            if (oJson != null && oJson.getBoolean("success")) {
               /* AWSCredentials oCre = new BasicSessionCredentials(oJson.getString("sAccessKey"), oJson.getString("sSecretKey"), oJson.getString("sToken"));
                AmazonSimpleDBClient oClient = new AmazonSimpleDBClient(oCre);
                oClient.setEndpoint("https://sdb.us-west-2.amazonaws.com");
                boolean bLoop = true;
                String sCustomerID = CommonHelper.GetPreferenceString(this, "iCustomerID");
                String sCompanyID = CommonHelper.GetPreferenceString(this, "iCompanyID");
                while (bLoop) {
                    List<ai_Log> lsLog = ai_Log.findWithQuery(ai_Log.class, "SELECT * FROM AILOG WHERE B_DELETED = 0 and B_UPLOADed = 'false' LIMIT 25");
                    if (lsLog == null || lsLog.size() == 0){
                        bLoop = false;
                    }
                    else{
                        BatchPutAttributesRequest oRequest = new BatchPutAttributesRequest();
                        oRequest.setDomainName(oJson.getString("sDomain"));
                        for (ai_Log oTemp : lsLog){
                            ReplaceableAttribute oCustomer = new ReplaceableAttribute("CustomerID", sCustomerID, true);
                            ReplaceableAttribute oCompany = new ReplaceableAttribute("CompanyID", sCompanyID == null ? "" + 0 : sCompanyID, true);
                            ReplaceableAttribute oType = new ReplaceableAttribute("Type", oTemp.sType, true);
                            ReplaceableAttribute oMessage = new ReplaceableAttribute("Message", oTemp.sMessage, true);
                            ReplaceableAttribute oDate = new ReplaceableAttribute("Date", oTemp.dtDateTime, true);
                            ArrayList<ReplaceableAttribute> aa = new ArrayList<ReplaceableAttribute>();
                            aa.add(oCustomer);
                            aa.add(oCompany);
                            aa.add(oType);
                            aa.add(oMessage);
                            aa.add(oDate);
                            ReplaceableItem oItem = new ReplaceableItem("A_" + sCustomerID + "_" + CommonHelper.GetTimeString() + "_" + RandomString(7), aa);
                            oRequest.withItems(oItem);
                        }
                        //oClient.
                        oClient.batchPutAttributes(oRequest);
                        for (ai_Log oTemp : lsLog) {
                            oTemp.bUploaded = true;
                            oTemp.save();

                        }
                       // if ([oResponse boxUsage] > 0){
                       //     for (ai_Log *oLog in arrLog){
                       //         //oLog.bUploaded = true;
                       //         [db_Log UpdateLogStatus:oLog.iLogID];
                       //     }
                       // }

                    }
                }*/


            }


        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadLogService.onHandlerIntent", ex, this);
        }
    }
    public static String RandomString(int randomLength) {
        Random generator = new Random();
        StringBuilder randomStringBuilder = new StringBuilder();
        //int randomLength = generator.nextInt(MAX_LENGTH);
        char tempChar;
        for (int i = 0; i < randomLength; i++){
            tempChar = (char) (generator.nextInt(96) + 32);
            randomStringBuilder.append(tempChar);
        }
        return randomStringBuilder.toString();
    }
}
