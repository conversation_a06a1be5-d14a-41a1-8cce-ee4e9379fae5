package com.snapinspect.snapinspect3.activity;

import android.annotation.SuppressLint;
import android.app.ActionBar;
import android.app.Activity;
import android.app.DatePickerDialog;
import android.os.Handler;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.DatePicker;
import android.widget.EditText;
import android.widget.TextView;

import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.Helper.IF_RestClient;
import com.snapinspect.snapinspect3.Helper.NetworkUtils;
import com.snapinspect.snapinspect3.IF_Object.ai_Assets;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.R;

import org.apache.http.NameValuePair;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;


public class if_UpdateAsset extends Activity {

    private int iSAssetID = 0;
    private int iSPAssetID = 0;
    private ai_Assets oAsset = null;
    private EditText edit_Address1;
    private EditText edit_Address2;
    private EditText edit_Key;
    private EditText edit_Alarm;
    private EditText edit_Due;
    private Button oButton;
    MaterialDialog oDialog;
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        ActionBar oBar = getActionBar();
        oBar.show();
        oBar.setDisplayHomeAsUpEnabled(true);
        if (CommonUI.bBelowLollipop()){
            setContentView(R.layout.activity_if__update_asset_low);
        }
        else {
            setContentView(R.layout.activity_if__update_asset);
        }
        getWindow().setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
        try {
            iSAssetID = getIntent().getIntExtra(Constants.Extras.iSAssetID, 0);
            iSPAssetID = getIntent().getIntExtra(Constants.Extras.iSPAssetID, 0);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_UpdateAsset.onCreate", ex, this);
            iSAssetID = 0;
            iSPAssetID = 0;

        }
        setTitle("Add New Asset");
        if (iSAssetID > 0){
            List<ai_Assets> lsAssets = ai_Assets.find(ai_Assets.class, "I_S_Asset_ID = ?", "" + iSAssetID);
            if (lsAssets != null && lsAssets.size() == 1) {
                oAsset = lsAssets.get(0);
                setTitle(oAsset.sAddressOne);
            }
            else{
                oAsset = new ai_Assets();
                oAsset.iSPAssetID = iSPAssetID;
            }
        }
        else{
            ((TextView)findViewById(R.id.tv_UpdateAsset_Title)).setText("Add New Asset");
            oAsset = new ai_Assets();
            oAsset.iSPAssetID = iSPAssetID;
        }
        edit_Address1 = findViewById(R.id.updateAsset_et_address1);
        edit_Address1.setText( oAsset.sAddressOne == null ? "" : oAsset.sAddressOne);
        /*edit_Address1.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                new Handler().post(new Runnable() {
                    @Override
                    public void run() {
                        updateUI(0);
                    }
                });

            }
        });*/
        edit_Address2 = findViewById(R.id.updateAsset_et_address2);
        edit_Address2.setText(oAsset.sAddressTwo == null ? "" : oAsset.sAddressTwo);
        /*edit_Address2.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                updateUI(1);
            }
        });*/
        edit_Alarm = findViewById(R.id.updateAsset_et_alarm);
        edit_Alarm.setText(oAsset.sAlarm == null ? "" : oAsset.sAlarm);
        /*edit_Alarm.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });*/
        edit_Key = findViewById(R.id.updateAsset_et_key);
        edit_Key.setText(oAsset.sKey == null ? "" : oAsset.sKey);
        /*edit_Key.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
            }
        });*/
        edit_Due = findViewById(R.id.edit_UpdateAsset_Due);
        edit_Due.setText(oAsset.dtInsDue == null ? "" : oAsset.dtInsDue);
        edit_Due.setFocusable(false);
        edit_Due.setFocusableInTouchMode(false);
        edit_Due.setClickable(true);
        edit_Due.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                Calendar myCalendar = Calendar.getInstance();
                String sText = (((EditText)view).getText()).toString();
                if (sText != null && sText.length() > 0){
                    EditText oEditText = (EditText)view;
                    String sDueDate = oEditText.getText().toString();
                    try {
                        SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy");
                        myCalendar.setTime(sdf.parse(sDueDate));// all done
                    } catch (Exception ex) {

                    }

                }

                new DatePickerDialog(if_UpdateAsset.this, date, myCalendar
                        .get(Calendar.YEAR), myCalendar.get(Calendar.MONTH),
                        myCalendar.get(Calendar.DAY_OF_MONTH)).show();
            }
        });
        oButton = findViewById(R.id.btn_UpdateAsset);
        oButton.setEnabled(true);
        oButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View view) {
                String sAddress1 = edit_Address1.getText().toString();
                String sAddress2 = edit_Address2.getText().toString();
                String sKey = edit_Key.getText().toString();
                String sAlarm = edit_Alarm.getText().toString();
                String sDue = edit_Due.getText().toString();
                sAddress1 = (sAddress1 == null ? "" : sAddress1.trim());
                sAddress2 = (sAddress2 == null ? "" : sAddress2.trim());
                sKey = (sKey == null ? "" : sKey.trim());
                sAlarm = (sAlarm == null ? "" : sAlarm.trim());
                sDue = (sDue == null ? "" : sDue.trim());

                if (sAddress1.length() == 0) {
                    updateUI(0);
                    return;
                }
                if (sAddress2.length() == 0){
                    updateUI(1);
                    return;
                }
                if (CommonDB.ValidateAssetExist_ExcludeSelf(sAddress1, sAddress2, iSAssetID) > 0){
                    ShowAlert("Error", "The address you enter is already exist, plesae go 'Back' and search for the address again.");
                    return;
                }

                UpdateAsset(sAddress1, sAddress2, sKey, sAlarm, sDue);
            }
        });
        oButton.setBackgroundColor( getResources().getColor(R.color.pink_color));

    }

    @Override
    protected void onResume() {
        super.onResume();

        String address1 = edit_Address1.getText().toString().trim();
        String address2 = edit_Address2.getText().toString().trim();
        String key = edit_Key.getText().toString().trim();
        String alarm = edit_Alarm.getText().toString().trim();

        boolean isEnable = !address1.isEmpty() && !address2.isEmpty();
        //updateSaveButtonStatus(isEnable);
    }

    private void UpdateAsset(String sAddress1, String sAddress2, String sKey, String sAlarm, String sDue){
        if (NetworkUtils.isNetworkAvailable(if_UpdateAsset.this)) {
            oDialog = CommonUI.ShowMaterialProgressDialog(this, "Message", "Running. Please wait ...");
//            oDialog = CommonUI.GetProgressDialog(this, "Message", "Running. Please wait ...", true);

            ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(if_UpdateAsset.this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(if_UpdateAsset.this, "sToken"));
            oParams.add("iSPropertyID", "" + iSPAssetID);
            oParams.add("iPropertyID", "" + iSAssetID);
            oParams.add("sAddress1", sAddress1);
            oParams.add("sAddress2", sAddress2);
            oParams.add("sKey", sKey);
            oParams.add("sAlarm", sAlarm);
            oParams.add("sInsDue", sDue);
            String sURL = "/IOAPI/App_UpdateProperty";
            IF_RestClient.post( sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    oDialog.dismiss();
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            if (response.getBoolean("success")) {
                                try {
                                    JSONObject oObject = response.getJSONObject("oProperty");
                                    oAsset.sAddressOne = oObject.getString("sAddress1");
                                    oAsset.sAddressTwo = oObject.getString("sAddress2");
                                    oAsset.sKey = oObject.getString("sKey");
                                    oAsset.sAlarm = oObject.getString("sAlarm");
                                    oAsset.dtInsDue = ((oObject.getString("dtDue") == null || oObject.getString("dtDue").length() < 11)
                                            ? "" : oObject.getString("dtDue").substring(0, 12));
                                    oAsset.sFilter = CommonHelper.GetFilter(oAsset.sAddressOne, oAsset.sAddressTwo);
                                    if (oAsset.iSAssetID == 0){

                                        oAsset.iSAssetID = oObject.getInt("iPropertyID");
                                        //Object cc = oObject.get("iSPropertyID");
                                        //String sCC = oObject.getString("iSPropertyID");

                                        oAsset.iSPAssetID = (oObject.isNull("iSPropertyID") ? 0 : oObject.getInt("iSPropertyID"));

                                    }

                                    oAsset.save();
                                }catch(Exception ex){
                                    ai_BugHandler.ai_Handler_Exception("Exception", "if_UpdateAsset.UpdateAsset.Save", ex, if_UpdateAsset.this);
                                }
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        oDialog.dismiss();

//                                        Intent oIntent = new Intent(if_UpdateAsset.this, if_existasset.class);
//                                        // oIntent.putExtra("lClientObjectID", item.lClientObjectID);
//                                        oIntent.putExtra(Constants.Extras.iSAssetID, oAsset.iSAssetID);
//                                        startActivity(oIntent);
                                        finish();
                                    }
                                });

                                return;
                            }
                            else if (!response.getBoolean("success")){
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        oDialog.dismiss();
                                        try {
                                            ShowAlert("Error", response.getString("message"));
                                        }catch(Exception ex){
                                            ShowAlert("Error", "Asset Response Error. <NAME_EMAIL>.");
                                        }
                                    }
                                });
                            }
                            else {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        oDialog.dismiss();
                                        ShowAlert("Error", "To update asset information, please make sure you are connected to Internet. Otherwise please go back to 'Inspection Tab' and click 'New Inspection' button to start a new Inspection without Internet Connection.");
                                    }
                                });

                                return;
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Exception", "if_UpdateAsset.UpdateAsset", ex, if_UpdateAsset.this);
                        }

                    }
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            oDialog.dismiss();
                            ShowAlert("Error! Failed Connection", "Please try again later.");
                        }
                    });
                }
                @Override
                public void onFailure(int statusCode, org.apache.http.Header[] headers, java.lang.Throwable e, org.json.JSONObject errorResponse) {

                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            oDialog.dismiss();
                            ShowAlert("Error", "To update asset information, please make sure you are connected to Internet.");

                        }
                    });
                }
            });
        }
        else{
            ShowAlert("Error", "To update asset or contact information, please make sure the device is connected to Internet. Please note, inspecting assets DO NOT require internet but uploading inspection data requires either 3G or WiFi. If you want to inspect a NEW asset without internet, please go to Inspection Tab, and add a new inspection from there so you can carry on inspection on a new asset without Internet.");
        }



        //oAsset.save();
    }
    private void ShowAlert(String sTitle, String sMessage){
        CommonUI.ShowAlert(if_UpdateAsset.this, sTitle, sMessage);
    }
    DatePickerDialog.OnDateSetListener date = new DatePickerDialog.OnDateSetListener() {

        @Override
        public void onDateSet(DatePicker view, int year, int monthOfYear,
                              int dayOfMonth) {

            Calendar myCalendar = Calendar.getInstance();
            myCalendar.set(Calendar.YEAR, year);
            myCalendar.set(Calendar.MONTH, monthOfYear);
            myCalendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);
            SimpleDateFormat sdf = new SimpleDateFormat("MMM dd, yyyy");

            UpdateDueDate(sdf.format(myCalendar.getTime()));
        }

    };

    private void UpdateDueDate(String sUpdateDate) {

        edit_Due.setText(sUpdateDate);

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {

        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        onBackPressed();

        return true;
    }

    /*@SuppressLint("NewApi")
    private void updateSaveButtonStatus(boolean isEnable) {
        if (oButton != null) {
            oButton.setEnabled(isEnable);
            //oButton.setBackgroundColor(isEnable ? Color.pink : Color.gray);
            oButton.setBackgroundColor(isEnable ? getResources().getColor(R.color.pink_color) : getResources().getColor(R.color.gray_color));
        }
    }*/

    @SuppressLint("NewApi")
    private void updateUI(int idx) {
        try {
            String address1 = edit_Address1.getText().toString().trim();
            String address2 = edit_Address2.getText().toString().trim();
            String key = edit_Key.getText().toString().trim();
            String alarm = edit_Alarm.getText().toString().trim();

            boolean isEnable = !address1.isEmpty() && !address2.isEmpty();
           // updateSaveButtonStatus(isEnable);

            if (idx == 0) {
                boolean isAddress1Empty = address1.isEmpty();

              //  findViewById(R.id.updateAsset_line_address1).setBackgroundColor(isAddress1Empty ?
               //         getColor(R.color.red_color) : getColor(R.color.black));
                TextView alertAdd1 = findViewById(R.id.updateAsset_alert_address1);
                alertAdd1.setVisibility(isAddress1Empty ? View.VISIBLE : View.INVISIBLE);
                alertAdd1.setText(isAddress1Empty ? "Line 1 can not be empty!" : "");
            } else if (idx == 1) {
                boolean isAddress2Empty = address2.isEmpty();
             //   findViewById(R.id.updateAsset_line_address2).setBackgroundColor(isAddress2Empty ?
              //          getColor(R.color.red_color) : getColor(R.color.black));
                TextView alertAdd2 = findViewById(R.id.updateAsset_alert_address2);
                alertAdd2.setVisibility(isAddress2Empty ? View.VISIBLE : View.INVISIBLE);
                alertAdd2.setText(isAddress2Empty ? "Line 2 can not be empty!" : "");
            }
        }catch(Exception ex){
            ex.printStackTrace();
        }
    }
}
