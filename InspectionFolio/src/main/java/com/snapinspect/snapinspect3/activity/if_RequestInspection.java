package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.Home.if_HomeTab;
import com.snapinspect.snapinspect3.util.StringUtils;

public class if_RequestInspection extends Activity {
    public static String REQUEST_INSPECTION = "/Request";
    public static String GET_REQUEST_INSPECTION = "/GetRequest";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_request_inspection);
        final String url = getIntent().getStringExtra(Constants.Extras.sKioskUri);
        if (!StringUtils.isEmpty(url)) {
            if (url.toLowerCase().contains(Constants.ExternalLinks.externalRequest)){
                final String sEmail = CommonHelper.GetPreferenceString(this, "sEmail");
                if (StringUtils.isEmpty(sEmail)) {
                    CommonHelper.SavePreference(this, Constants.Settings.bKiosk, "1");
                }

             //   String downloadableUri = data.toString().replace(REQUEST_INSPECTION, GET_REQUEST_INSPECTION);
//              "https://my.snapinspect.com/external/getrequestinspection?irequestid=5133&srequestcode=nxjoicumg8uw6hlsxif6fx6eckst4bzf63o7od1j"
//              downloadableUri = "https://my.snapinspect.com/external/getrequestinspection?irequestid=5133&srequestcode=nXjoiCuMG8uW6hLSXIF6fx6ECkST4BzF63O7oD1j";

                Intent intent = new Intent(if_RequestInspection.this, if_HomeTab.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                intent.putExtra(Constants.Extras.sKioskUri, url);
                startActivity(intent);
                finish();
            }
            else if (url.toLowerCase().contains(Constants.ExternalLinks.request)) {
                final String sEmail = CommonHelper.GetPreferenceString(this, "sEmail");
                if (StringUtils.isEmpty(sEmail)) {
                    CommonHelper.SavePreference(this, Constants.Settings.bKiosk, "1");
                }

                String downloadableUri = url.replace(REQUEST_INSPECTION, GET_REQUEST_INSPECTION);
//              "https://my.snapinspect.com/external/getrequestinspection?irequestid=5133&srequestcode=nxjoicumg8uw6hlsxif6fx6eckst4bzf63o7od1j"
//              downloadableUri = "https://my.snapinspect.com/external/getrequestinspection?irequestid=5133&srequestcode=nXjoiCuMG8uW6hLSXIF6fx6ECkST4BzF63O7oD1j";

                Intent intent = new Intent(if_RequestInspection.this, if_HomeTab.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                intent.putExtra(Constants.Extras.sKioskUri, downloadableUri);
                startActivity(intent);
                finish();
            }
        }
    }

}
