package com.snapinspect.snapinspect3.activity;

import android.app.ActionBar;
import android.app.Activity;
import android.app.FragmentTransaction;
import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.WindowManager;

import androidx.fragment.app.FragmentActivity;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.Edit.if_UpdateAsset_2nd;

public class if_Asset_2nd extends FragmentActivity {
    public int iSPAssetID = 0;
    public String sTitle = "";

    @Override
    protected void onCreate(Bundle savedInstanceState) {

        super.onCreate(savedInstanceState);
        iSPAssetID = getIntent().getIntExtra(Constants.Extras.iSPAssetID, 0);
        sTitle = getIntent().getStringExtra("sTitle");
        ActionBar oBar = getActionBar();
        getWindow().setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_ALWAYS_HIDDEN);
        // lsCurrentInspection = new List<ai_Inspection>();
        // lsCompletedInspection = new List<ai_Inspection>();
        oBar.setTitle(sTitle);
        oBar.show();
        oBar.setDisplayHomeAsUpEnabled(true);
        setContentView(R.layout.activity_if__asset_2nd);
        if (savedInstanceState == null) {
            frag_assets oFragAssets = new frag_assets() ;
            oFragAssets.iSPAssetID = iSPAssetID;
            getSupportFragmentManager().beginTransaction().replace(R.id.rl_asset_2nd, oFragAssets).commit();
        }
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (CommonJson.disableEditAsset(this)) return false;
        getMenuInflater().inflate(R.menu.menu_home, menu);
        menu.findItem( R.id.action_syncassets).setVisible(false);
        menu.findItem( R.id.action_uploadinspections).setVisible(false);
      //  menu.findItem( R.id.action_newins).setVisible(false);
        menu.findItem( R.id.action_settings).setVisible(false);
        menu.findItem(R.id.action_sortassets).setVisible(false);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected( MenuItem item) {
        if (item.getItemId() == R.id.action_newasset) {
            Intent oIntent = new Intent(this, if_UpdateAsset_2nd.class);
            oIntent.putExtra(Constants.Extras.iSAssetID,  0);
            oIntent.putExtra(Constants.Extras.iSPAssetID,  iSPAssetID);
            startActivity(oIntent);
        } else {
            onBackPressed();
        }
        return true;
    }

}
