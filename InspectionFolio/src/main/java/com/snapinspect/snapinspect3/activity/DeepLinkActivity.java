package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.if_existasset;
import com.snapinspect.snapinspect3.util.StringUtils;

public class DeepLinkActivity extends Activity {

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        checkIntent(getIntent());
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        checkIntent(intent);
    }

    private void checkIntent(Intent intent) {
        if ("android.intent.action.VIEW".equals(intent.getAction()) && intent.getData() != null
                && getString(R.string.deeplink_url_host).equals(intent.getData().getHost())) {
            String path = intent.getData().getPath();
            if (!StringUtils.isEmpty(path)) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                path = path.toLowerCase();
                if (path.contains(Constants.ExternalLinks.externalSchedule)) {
                    intent.putExtra(Constants.Extras.sKioskUri, intent.getData().toString());
                    intent.setClass(this, if_login.class);
                } else if (path.contains(Constants.ExternalLinks.getRequestInspection)
                        || path.contains(Constants.ExternalLinks.syncExternalRequest)
                        || path.contains(Constants.ExternalLinks.request)
                        || path.contains(Constants.ExternalLinks.externalRequest)) {
                    intent.putExtra(Constants.Extras.sKioskUri, intent.getData().toString());
                    intent.setClass(this, if_RequestInspection.class);
                } else if (path.contains(Constants.ExternalLinks.assetDetails)) {
                    intent.putExtra(Constants.Extras.iSAssetID, CommonHelper.getInt(intent.getData().getLastPathSegment()));
                    intent.setClass(this, if_existasset.class);
                }
            }
        }

        startActivity(intent);
        finish();
    }

}