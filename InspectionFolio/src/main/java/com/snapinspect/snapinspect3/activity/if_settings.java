package com.snapinspect.snapinspect3.activity;

import android.annotation.SuppressLint;
import android.app.ActionBar;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.ResultReceiver;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;

import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Adapter.SettingsAdapter;
import com.snapinspect.snapinspect3.BuildConfig;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;

import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.SystemUiHider;
import com.snapinspect.snapinspect3.views.StickyHeaderListView.StickyHeaderListView;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_settings extends Activity {

    private StickyHeaderListView listView;
    private SettingsAdapter adapter;

    LinkedHashMap<String, List<Setting>> map = new LinkedHashMap<>();
    private MaterialDialog mProgressDialog;

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    protected void onPause() {
        super.onPause();
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
           // CommonHelper.trackEvent(this, "Android View Settings", null);
            setContentView(R.layout.activity_settings);
            ActionBar oBar = getActionBar();
            oBar.setDisplayHomeAsUpEnabled(true);

            listView = findViewById(R.id.activity_settings_listview);
            listView.getListView().setDividerHeight(0);

            if (readSettings()) {
                adapter = new SettingsAdapter(this, map);
                adapter.setOnSharedClickedListener(type -> {
                    switch (type) {
                        case LOG_OUT:
                            logout();
                            break;
                        case FORCE_SUBMIT_DATA_TIP:
                            CommonUI.longToast(this, R.string.force_submit_data_tip);
                            break;
                        case FORCE_SUBMIT_DATA:
                            showResetRecoverOption();
                            break;
                    }
                });
                listView.setAdapter(adapter);
            }

            updateLocalSettingsBasedOnRoleRestriction();

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_settings.onCreate", ex, this);
        }
    }

    private boolean readSettings() {
        try {
            //Settings
            Setting savePhotoOption = new Setting();
            savePhotoOption.title = "Save Photo To Library";
            savePhotoOption.type = Setting.Type.SAVE_PHOTO;
            savePhotoOption.value = Constants.Settings.bSaveLocal;

            Setting autoLoginOption = new Setting();
            autoLoginOption.title = "Auto Login";
            autoLoginOption.type = Setting.Type.AUTOLOGIN;
            autoLoginOption.value = Constants.Settings.bAutoLogin;

            Setting dateTimeOption = new Setting();
            dateTimeOption.title = "Photo DateTime Stamp";
            dateTimeOption.type = Setting.Type.PHOTO_STAMP;
            dateTimeOption.value = Constants.Settings.bPhotoStamp;

            Setting instructionsOption = new Setting();
            instructionsOption.title = "Display Instructions";
            instructionsOption.type = Setting.Type.DISPLAY_INST;
            instructionsOption.value = Constants.Settings.bDisplayInst;

            Setting bEnableWord = new Setting();
            bEnableWord.title = "Enable Word";
            bEnableWord.type = Setting.Type.ENABLE_WORD;
            bEnableWord.value = "bEnableWord";

            Setting bDisplayInspectionDuration = new Setting();
            bDisplayInspectionDuration.title = "Inspection Timer";
            bDisplayInspectionDuration.type = Setting.Type.INSPECTION_DURATION;
            bDisplayInspectionDuration.value = Constants.Settings.bDisplayInspectionDuration;

            Setting localEmailOption = new Setting();
            localEmailOption.title = "Local Email Client";
            localEmailOption.type = Setting.Type.LOCAL_EMAIL;
            localEmailOption.value = "bLocalEmailClient";

            Setting fixRotationOption = new Setting();
            fixRotationOption.title = "Fix Camera Rotation";
            fixRotationOption.type = Setting.Type.CAM_ROTATION;
            fixRotationOption.value = Constants.Settings.bFixCamRotation;

            Setting fixCameraOption = new Setting();
            fixCameraOption.title = "Fixed Camera";
            fixCameraOption.type = Setting.Type.FIX_CAMERA;
            fixCameraOption.value = Constants.Settings.bFixCamera;

            Setting showGeoTag = new Setting();
            showGeoTag.title = "Photo GPS Location";
            showGeoTag.type = Setting.Type.PHOTO_GEO_TAG;
            showGeoTag.value = Constants.Settings.bShowGeoTag;

            /*
            Setting cameraOption = new Setting();
            cameraOption.title = "Camera V1";
            cameraOption.type = "bCAMERA1";
            cameraOption.value = "bCameraV1.1";

           Setting newInspectionOption = new Setting();
            newInspectionOption.title = "New Inspection View";
            newInspectionOption.type = Constants.Settings.bNewInspectionUI;
            newInspectionOption.value = Constants.Settings.bNewInspectionUI;
            */

            Setting startChatOption = new Setting();
            startChatOption.title = "Start a chat with Support";
            startChatOption.type = Setting.Type.ASK_SUPPORT;

            Setting versionOption = new Setting();
            versionOption.title = "Version " + BuildConfig.VERSION_NAME + " (" + BuildConfig.VERSION_CODE + ")";
            versionOption.type = Setting.Type.APP_VERSION;

            // User Info
            Setting userOption = new Setting();
            User user = new User();
            user.company = CommonHelper.GetPreferenceString(this, "CompanyName");
            user.name = CommonHelper.GetPreferenceString(if_settings.this, "FirstName") + " "
                    + CommonHelper.GetPreferenceString(if_settings.this, "LastName");
            user.email = CommonHelper.GetPreferenceString(if_settings.this, "sEmail").toLowerCase();
            userOption.user = user;

            List<Setting> userSettings = new ArrayList<>();
            userSettings.add(userOption);
            map.put("User Info", userSettings);

            // General
            List<Setting> generalSettings = new ArrayList<>();
            generalSettings.add(autoLoginOption);
            map.put("General", generalSettings);

            // Inspection Settings
            List<Setting> inspectionSettings = new ArrayList<>();
            // inspectionSettings.add(newInspectionOption);
            inspectionSettings.add(instructionsOption);
            inspectionSettings.add(bEnableWord);
            inspectionSettings.add(bDisplayInspectionDuration);
            map.put("Inspection Settings", inspectionSettings);

            // Photo & Video
            List<Setting> photoVideoSettings = new ArrayList<>();
            photoVideoSettings.add(savePhotoOption);
            photoVideoSettings.add(dateTimeOption);
            photoVideoSettings.add(showGeoTag);
            //photoVideoSettings.add() //Camera Alternative View
            //photoVideoSettings.add(cameraOption);
            photoVideoSettings.add(fixRotationOption);
            photoVideoSettings.add(fixCameraOption);
            map.put("Photo & Video", photoVideoSettings);

            // Email
            List<Setting> emailSettings = new ArrayList<>();
            emailSettings.add(localEmailOption);
            map.put("Email", emailSettings);

            // Help & Support
            List<Setting> helpSettings = new ArrayList<>();
            helpSettings.add(startChatOption);
            map.put("Help & Support", helpSettings);

            // App Version
            List<Setting> appVersionSettings = new ArrayList<>();
            appVersionSettings.add(versionOption);
            map.put("App Version", appVersionSettings);

            // Logout
            List<Setting> logoutSettings = new ArrayList<>();
            Setting logoutOption = new Setting();
            logoutSettings.add(logoutOption);
            map.put("Logout", logoutSettings);

        } catch (Exception e) {
            e.printStackTrace();
        }

        return true;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_settings, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        try {
            if (item.getItemId() == R.id.action_logout) {
                logout();
            } else {
                onBackPressed();
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_settings.onOptionsItemSelected", ex, this);
        }

        return true;
    }

    private void logout() {
        // only set the app not auto login
        CommonHelper.SavePreference(if_settings.this, Constants.Settings.bAutoLogin, "false");
        Intent oIntent = new Intent(if_settings.this, if_login.class);
        CommonHelper.IntercomLogout(if_settings.this);
        startActivity(oIntent);
        finish();
    }

    private void forceSubmitData() {
        mProgressDialog = CommonUI.ShowMaterialProgressDialog(
                this, "Uploading... Please keep SnapInspect in foreground until finish.", "");
        Intent oIntent = new Intent(this, SyncService.class);
        oIntent.putExtra("receiver", new ForceUploadDataSyncReceiver(mProgressDialog));
        oIntent.putExtra(Constants.Extras.bForceSubmitData, true);
        oIntent.putExtra(Constants.Extras.bRequestInspection, false);
        startService(oIntent);
    }

    private void finishAndForceSync() {
        setResult(Constants.ResultCodes.FORCE_SYNC);
        finish();
    }

    private void showResetRecoverOption() {
        new MaterialDialog.Builder(this)
                .items(R.array.reset_recover_options)
                .negativeText(R.string.menu_cancel)
                .itemsCallback(new MaterialDialog.ListCallback() {
                    @Override
                    public void onSelection(MaterialDialog dialog, View itemView, int position, CharSequence text) {
                        switch (position) {
                            case 0:
                                // Submit Data
                                CommonUI.showConfirmDialog(
                                        if_settings.this,
                                        R.string.title_dialog_submit_data,
                                        R.string.msg_dialog_submit_data,
                                        R.string.alert_action_yes,
                                        R.string.alert_action_cancel,
                                        (dialog1, which) -> forceSubmitData(),
                                        null);
                                break;
                            case 1:
                                // Reset Sync
                                CommonHelper.resetSyncData(if_settings.this);
                                CommonUI.showConfirmDialog(
                                        if_settings.this,
                                        R.string.title_dialog_reset_sync,
                                        R.string.msg_dialog_reset_sync,
                                        R.string.alert_action_ok,
                                        0,
                                        (dialog1, which) -> finishAndForceSync(),
                                        null);
                                break;
                        }
                    }
                })
                .show();
    }

    public static class Setting {
        public enum Type {
            LOG_OUT,
            FORCE_SUBMIT_DATA_TIP,
            FORCE_SUBMIT_DATA,
            SAVE_PHOTO,
            AUTOLOGIN,
            PHOTO_STAMP,
            DISPLAY_INST,
            ENABLE_WORD,
            INSPECTION_DURATION,
            LOCAL_EMAIL,
            CAM_ROTATION,
            FIX_CAMERA,
            PHOTO_GEO_TAG,
            ASK_SUPPORT,
            APP_VERSION,
            HOW_TO_VIDEOS,
        }

        public String title;
        public Type type;
        public String value;
        public User user;
    }

    public static class User {
        public String name;
        public String company;
        public String email;

        public String getLetters() {
            return StringUtils.getFirstLetters(name);
        }
    }

    // Update local settings based on role restriction
    private void updateLocalSettingsBasedOnRoleRestriction() {
        if (CommonJson.shouldTurnOnInstruction(this)) {
            CommonHelper.SavePreferenceBoolean(this, Constants.Settings.bDisplayInst, true);
        }
        if (CommonJson.shouldTurnOnPhotoDate(this)) {
            CommonHelper.SavePreferenceBoolean(this, Constants.Settings.bPhotoStamp, true); 
        }
        if (CommonJson.shouldTurnOnLocation(this)) {
            CommonHelper.SavePreferenceBoolean(this, Constants.Settings.bShowGeoTag, true);
        }
    }
}

@SuppressLint("ParcelCreator")
class ForceUploadDataSyncReceiver extends ResultReceiver {

    private final MaterialDialog mProgressDialog;

    ForceUploadDataSyncReceiver(MaterialDialog progressDialog) {
        super(new Handler());
        mProgressDialog = progressDialog;
    }

    @Override
    protected void onReceiveResult(int resultCode, Bundle resultData) {
        try {
            super.onReceiveResult(resultCode, resultData);
            if (resultCode == SyncService.SYNC_PROGRESS || resultCode == SyncService.SUBMIT_PROCESS) {
                mProgressDialog.setContent(resultData.getString("Message"));
            } else if (resultCode == SyncService.SYNC_SUCCESS) {
                mProgressDialog.setContent("Submit - Upload File Success");
                mProgressDialog.dismiss();
            } else if (resultCode == SyncService.SYNC_FAIL) {
                mProgressDialog.setContent("Auth Submit Data Fail, Please try again.");
                mProgressDialog.dismiss();
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }
}

