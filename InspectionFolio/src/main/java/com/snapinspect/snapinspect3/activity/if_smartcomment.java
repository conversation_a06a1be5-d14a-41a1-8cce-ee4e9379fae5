package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.view.*;
import android.view.inputmethod.EditorInfo;
import android.widget.*;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonRequestInspection;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.SimpleTextWatcher;
import com.snapinspect.snapinspect3.util.SystemUiHider;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.*;

//import io.intercom.android.sdk.activities.MainActivity;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_smartcomment extends Activity {

    private static final int SEARCH_DELAY = 300;

    public ArrayList<HashMap<String, String>> lsQuickPhrase;
    public ArrayList<HashMap<String, String>> lsMainSmartComments;

    private final ArrayList<HashMap<String, String>> selectedPhrases = new ArrayList<>();

    private String sWords;
    private int iTextPosition;
    private int iPosition;
    private int iInsItemID;
    private int iPLayoutID;
    int iCheckListID;

    private ListView oListView;
    private EditText oSearch;
    private Ins_QP_Adapter adapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            setTheme(R.style.RemoveShadowActionBar);
            super.onCreate(savedInstanceState);
            setContentView(R.layout.activity_smartcomment);

            lsQuickPhrase = new ArrayList<>();
            lsMainSmartComments = new ArrayList<>();

            iTextPosition = getIntent().getIntExtra(Constants.Extras.iTextPosition, 0);
            iPosition = getIntent().getIntExtra(Constants.Extras.iPosition, 0);
            sWords = getIntent().getStringExtra(Constants.Extras.sWords);
            iInsItemID = getIntent().getIntExtra(Constants.Extras.iInsItemID, 0);
            iPLayoutID = getIntent().getIntExtra(Constants.Extras.iPLayoutID, 0);

            oListView = findViewById(R.id.lv_quickphrase);
            oListView.setItemsCanFocus(false);
            oListView.setChoiceMode(ListView.CHOICE_MODE_MULTIPLE);
            oListView.setOnItemClickListener(new ListItemClickListener());

            ReadFile();

            CommonHelper.hideSoftKeyboard(this);

            try {
                oSearch = findViewById(R.id.search_asset);
                oSearch.addTextChangedListener(new SimpleTextWatcher() {

                    private Timer throttleTimer = new Timer();

                    @Override
                    public void afterTextChanged(Editable editable) {
                        throttleTimer.cancel();
                        throttleTimer = new Timer();
                        throttleTimer.schedule(new TimerTask() {
                            @Override
                            public void run() {
                                updateDateSetWithSearchText();
                            }
                        }, SEARCH_DELAY );
                    }

                });

                oSearch.setOnEditorActionListener((v, actionId, event) -> {
                    if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                        CommonHelper.hideSoftKeyboard(if_smartcomment.this);
                        return true;
                    }
                    return false;
                });

            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "if_smartcomment.main", ex, this);
            }

            getActionBar().setDisplayHomeAsUpEnabled(true);

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_smartcomment.onCreate", ex, this);
        }
    }

    private void updateDateSetWithSearchText () {
        selectedPhrases.clear();

        String searchText = oSearch.getText().toString();
        if (searchText == "") {
            lsQuickPhrase = (ArrayList<HashMap<String,String>>)lsMainSmartComments.clone();
        } else {
            if (lsQuickPhrase != null) {
                lsQuickPhrase.clear();
            }

            for (int i = 0; i < lsMainSmartComments.size(); i++) {
                HashMap<String, String> item = lsMainSmartComments.get(i);
                String title = item.get("T"), body = item.get("C");

                if (title.toLowerCase().contains(searchText.toLowerCase())
                        || body.toLowerCase().contains(searchText.toLowerCase())) {
                    lsQuickPhrase.add(lsMainSmartComments.get(i));
                }
            }
        }

        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                adapter = new Ins_QP_Adapter(if_smartcomment.this, R.layout.cell_smartcomment_textview, lsQuickPhrase);
                oListView.setAdapter(adapter);
                adapter.notifyDataSetChanged();
            }
        });
    }

    private void saveSelectedComments() {

        if (selectedPhrases.size() == 0) { return; }

        ArrayList<String> items = new ArrayList<>();
        for (int i = 0; i < selectedPhrases.size(); i++) {
            items.add(selectedPhrases.get(i).get("C"));
        }

        String selectedText = String.join(" ", items);
        String prefix = sWords.substring(0, iTextPosition);
        if (prefix.length() > 0 && !prefix.endsWith(" ")) {
            selectedText = " " + selectedText;
        }

        String suffix = sWords.substring(iTextPosition);
        if (suffix.length() > 0 && !suffix.startsWith(" ")) {
            selectedText += " ";
        }

        ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);
        String sResult = prefix + selectedText + suffix;
        CommonHelper.SetValue(iPosition, oInsItem, sResult);
        oInsItem.save();
    }

    @Override
    public void onBackPressed() {
        saveSelectedComments();
        super.onBackPressed();
    }

    @Override
    public void onResume() {
        super.onResume();
        CommonHelper.hideSoftKeyboard(this);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu){
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_smartcomments, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        try {
            onBackPressed();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_quickphrase.onOptionsItemSelected", ex, this);
        }
        return super.onOptionsItemSelected(item);
    }

    /*
    public void hideSoftKeyboard() {
        if(getCurrentFocus()!=null) {
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
            inputMethodManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
        }
    }
    */

    public void ReadFile () {
        try {
            if (lsQuickPhrase != null) {
                lsQuickPhrase.clear();
            }

            iCheckListID = -1;
            ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);

            int iInsID = oInsItem.iInsID;
            ai_Inspection oInspection = ai_Inspection.findById(ai_Inspection.class, (long) iInsID);
            List<ai_CheckList> lsCheckList = ai_CheckList.listAll(ai_CheckList.class);
            for (ai_CheckList oCheck : lsCheckList) {
                if (oCheck.sPTC.equals(oInspection.sPTC)) {
                    iCheckListID = oCheck.iSCheckListID;
                    break;
                }
            }
            if (iCheckListID <= -1) return;

            String fileName = "SC_" + iCheckListID + ".json";
            String jsonStr = CommonRequestInspection.ReadFileToText(this.getFilesDir() + "/" + fileName);


            if (jsonStr != null && jsonStr.length() > 0) {
                JSONObject jsonObj = new JSONObject(jsonStr);

                // Getting data JSON Array nodes
                JSONArray data = jsonObj.getJSONArray("Data");
                // looping through All nodes
                List<ai_Layout> lsLayout = ai_Layout.find(ai_Layout.class, "I_S_LAYOUT_ID = ?", "" + oInsItem.iSLayoutID);
                ai_Layout oLayout = null;
                if (lsLayout != null && lsLayout.size() > 0) {
                    oLayout = lsLayout.get(0);
                }

                for (int i = 0; i < data.length(); i++) {
                    JSONObject c = data.getJSONObject(i);

                    int iLayout = 0;
                    try {
                        iLayout = c.getInt("I");
                    } catch (Exception eeeee) {

                    }

                    if (oLayout != null) {
                        if (iLayout == oLayout.iSPLayoutID || iLayout == oLayout.iSLayoutID) {
                            String t = c.getString("T");
                            String ct = c.getString("C");

                            HashMap<String, String> parsedData = new HashMap<String, String>();

                            // adding each child node to HashMap key => value
                            parsedData.put("T", t);
                            parsedData.put("C", ct);
                           // lsQuickPhrase.add((HashMap<String, String>) parsedData.clone());
                            lsMainSmartComments.add((HashMap<String, String>)parsedData.clone());
                        } else if (iLayout == 0) {
                            String t = c.getString("T");
                            String ct = c.getString("C");

                            HashMap<String, String> parsedData = new HashMap<String, String>();

                            // adding each child node to HashMap key => value
                            parsedData.put("T", t);
                            parsedData.put("C", ct);
                           // lsQuickPhrase.add((HashMap<String, String>)parsedData.clone());
                            lsMainSmartComments.add((HashMap<String, String>)parsedData.clone());
                        }
                    } else if (iLayout == iPLayoutID) {
                        String t = c.getString("T");
                        String ct = c.getString("C");

                        HashMap<String, String> parsedData = new HashMap<String, String>();

                        parsedData.put("T", t);
                        parsedData.put("C", ct);

                      //  lsQuickPhrase.add((HashMap<String, String>)parsedData.clone());
                        lsMainSmartComments.add((HashMap<String, String>)parsedData.clone());
                    }
                }

                lsQuickPhrase = (ArrayList<HashMap<String,String>>)lsMainSmartComments.clone();

                adapter = new Ins_QP_Adapter(this, R.layout.cell_smartcomment_textview, lsQuickPhrase);
                oListView.setAdapter(adapter);
                adapter.notifyDataSetChanged();
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public class ListItemClickListener implements AdapterView.OnItemClickListener {

        @Override
        public void onItemClick(AdapterView<?> adapterView, View view, int i, long l) {
            HashMap<String, String> item = lsQuickPhrase.get(i);
            if (selectedPhrases.contains(item)) {
                selectedPhrases.remove(item);
                view.setBackgroundColor(Color.WHITE);
            } else {
                selectedPhrases.add(item);
                view.setBackgroundColor(Color.LTGRAY);
            }
        }
    }

    private class Ins_QP_Adapter extends ArrayAdapter<HashMap<String, String>> {

        //class for caching the views in a row
        private class ViewHolder {
            TextView title, comment;
        }

        private ViewHolder viewHolder;
        private final Context oContext;

        public Ins_QP_Adapter(
                Context context,
                int textViewResourceId,
                ArrayList<HashMap<String, String>> Strings
        ) {
            super(context, textViewResourceId, Strings);
            oContext = context;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {

            if (convertView == null) {
                //inflate the custom layout
                convertView = ((LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.cell_smartcomment_textview, null);
                viewHolder = new ViewHolder();

                viewHolder.title = convertView.findViewById(R.id.txt_title);
                viewHolder.comment = convertView.findViewById(R.id.txt_comments);

                //link the cached views to the convertview
                convertView.setTag(viewHolder);
            } else viewHolder = (ViewHolder) convertView.getTag();

            //set the data to be displayed
            viewHolder.title.setText(lsQuickPhrase.get(position).get("T"));
            viewHolder.comment.setText(lsQuickPhrase.get(position).get("C"));

            //return the view to be displayed
            return convertView;
        }
    }
}

