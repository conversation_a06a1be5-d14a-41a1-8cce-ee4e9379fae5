package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.graphics.Point;
import android.os.Bundle;
import android.os.Handler;
import android.text.Editable;
import android.util.Pair;
import android.view.*;
import android.view.inputmethod.EditorInfo;
import android.widget.*;
import androidx.recyclerview.widget.LinearLayoutManager;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.SimpleTextWatcher;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.woxthebox.draglistview.DragItem;
import com.woxthebox.draglistview.DragItemAdapter;
import com.woxthebox.draglistview.DragListView;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.*;

public class if_CommentsLibrary extends Activity {

    public static final int COMMENTS_LIBRARY_RESULT_CODE = 0;
    private static final int DELAY = 300;

    private enum TabItem {
        global, area, item, selected;

        private static TabItem withViewId(int viewId) {
            switch (viewId) {
                case R.id.btn_comments_global: return TabItem.global;
                case R.id.btn_comments_area_only: return TabItem.area;
                case R.id.btn_comments_item_only: return TabItem.item;
                case R.id.btn_comments_selected: return TabItem.selected;
            }
            return TabItem.global;
        }
    }

    private TabItem currentTab = TabItem.global;
    private DragListView dragListView;
    private ListView oListView;
    private EditText oSearchBar;
    private Button[] btnTabs;
    private View vBottomLine;
    private TextView badgeView;

    private ArrayList<O_Comment> comments = new ArrayList<>();
    private final ArrayList<O_Comment> selectedComments = new ArrayList<>();

    private int iPLayoutID;
    private ai_InsItem oInsItem;
    private ai_InsItem oPInsItem;
    private ai_Layout oLayout;
    private List<JSONObject> arrSmartComments;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        setTheme(R.style.RemoveShadowActionBar);
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_comments_library);

        btnTabs = new Button[] {
                findViewById(R.id.btn_comments_global),
                findViewById(R.id.btn_comments_area_only),
                findViewById(R.id.btn_comments_item_only),
                findViewById(R.id.btn_comments_selected)
            };
        dragListView = findViewById(R.id.lv_comments_selected);
        oListView = findViewById(R.id.lv_comments_library);
        vBottomLine = findViewById(R.id.line_selected_bottom);
        badgeView = findViewById(R.id.tv_comments_badge);
        oSearchBar = findViewById(R.id.search_asset);

        try {
            getActionBar().setDisplayHomeAsUpEnabled(true);

            badgeView.setVisibility(View.GONE);

            for (Button tab: btnTabs) {
                tab.setOnClickListener((View v) -> {
                    UpdateBtnTabColors(v);
                    AnimateMoveBottomLine(v, true);
                    CommonHelper.hideSoftKeyboard(if_CommentsLibrary.this);
                    currentTab = TabItem.withViewId(v.getId());
                    ReloadCommentsWithSearchText(oSearchBar.getText().toString());
                });
            }

            oListView.setOnItemClickListener(new CommentItemOnClickListener());

            dragListView.setVisibility(View.GONE);
            dragListView.setDragListListener(new DragListView.DragListListener() {
                @Override
                public void onItemDragStarted(int position) { }

                @Override
                public void onItemDragging(int itemPosition, float x, float y) { }

                @Override
                public void onItemDragEnded(int fromPosition, int toPosition) {
                    if (fromPosition != toPosition) {
                        O_Comment comment = selectedComments.get(fromPosition);
                        int idx = comment.indexIn(selectedComments);
                        if (idx > -1) {
                            selectedComments.remove(idx);
                            selectedComments.add(toPosition, comment);
                            reloadListViewData(selectedComments);
                        }
                    }
                }
            });

            oSearchBar.addTextChangedListener(new SimpleTextWatcher() {

                private Timer throttleTimer = new Timer();

                @Override
                public void afterTextChanged(Editable editable) {
                    throttleTimer.cancel();
                    throttleTimer = new Timer();
                    throttleTimer.schedule(new TimerTask() {
                        @Override
                        public void run() {
                            ReloadCommentsWithSearchText(editable.toString());
                        }
                    }, DELAY);
                }

            });

            oSearchBar.setOnEditorActionListener((TextView v, int actionId, KeyEvent event) -> {
                if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                    CommonHelper.hideSoftKeyboard(if_CommentsLibrary.this);
                    return true;
                }
                return false;
            });

            parseSmartComments();

            int itemAreaState = arrSmartComments == null ? View.GONE : View.VISIBLE;
            findViewById(R.id.btn_comments_item_only).setVisibility(itemAreaState);
            findViewById(R.id.btn_comments_area_only).setVisibility(itemAreaState);

        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }

        ReloadCommentsWithSearchText("");
        new Handler().postDelayed(() -> {
            UpdateBtnTabColors(btnTabs[0]);
            AnimateMoveBottomLine(btnTabs[0], false);
        }, DELAY);

    }

    @Override
    public void onResume() {
        super.onResume();
        CommonHelper.hideSoftKeyboard(this);
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_sort_save, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        try {
            if (item.getItemId() == R.id.action_menu_sort) {
                showsSortOptionsView();
            } else {
                onBackPressed();
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(
                "Exception", "if_CommentsLibrary.onOptionsItemSelected", ex, this
            );
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        CommonHelper.hideSoftKeyboard(this);
        saveSelectedComments();
        super.onBackPressed();
    }

    private void showsSortOptionsView() {
        new MaterialDialog.Builder(this)
                .title(R.string.alert_title_action)
                .items(R.array.comments_sort_options)
                .itemsCallbackSingleChoice(-1, (dialog, view, which, text) -> {
                    boolean isDesc = which == 1;
                    CommonHelper.SavePreferenceBoolean(this, Constants.Settings.bCommentSortDesc, isDesc);
                    sortComments();
                    return true;
                })
                .negativeText(R.string.md_cancel_label)
                .show();
    }

    private void sortComments() {
        boolean isDesc = CommonHelper.GetPreferenceBoolean(this, Constants.Settings.bCommentSortDesc);
        if (arrSmartComments != null && !arrSmartComments.isEmpty()) {
            Collections.sort(arrSmartComments, (o1, o2) -> {
                return getWordsFromComment(isDesc ? o2 : o1)
                        .compareToIgnoreCase(getWordsFromComment(isDesc ? o1 : o2));
            });
        }
        ReloadCommentsWithSearchText(oSearchBar.getText().toString());
    }

    private class CommentItemOnClickListener implements AdapterView.OnItemClickListener {
        @Override
        public void onItemClick(AdapterView<?> parent, View view, int position, long id) {
            O_Comment comment = comments.get(position);

            if (comment.containedIn(selectedComments)) {
                int idx = comment.indexIn(selectedComments);
                selectedComments.remove(idx);
            } else {
                selectedComments.add(comment);
            }

            badgeView.setText(selectedComments.size() + "");
            badgeView.setVisibility(selectedComments.isEmpty() ? View.GONE : View.VISIBLE);

            reloadListViewData(null);
        }
    }

    private void reloadListViewData(ArrayList<O_Comment> dataSource) {
        if (dataSource != null) {
            Comments_Adapter commentsAdapter = new Comments_Adapter(
                    if_CommentsLibrary.this, R.layout.cell_comments_library, dataSource
            );
            oListView.setAdapter(commentsAdapter);
        }

        Comments_Adapter adapter = (Comments_Adapter)oListView.getAdapter();
        adapter.notifyDataSetChanged();
    }

    private class Comments_Adapter extends ArrayAdapter<O_Comment> {

        private class ViewHolder {
            TextView tv_title, tv_body;
            ImageView icon_add;
        }

        private ViewHolder viewHolder;
        private final Context oContext;
        private final ArrayList<O_Comment> oComments;

        public Comments_Adapter(Context context, int textViewResourceId, ArrayList<O_Comment> comments) {
            super(context, textViewResourceId, comments);
            oContext = context;
            oComments = comments;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {

            if (convertView == null) {
                //inflate the custom layout
                convertView = ((LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE))
                        .inflate(R.layout.cell_comments_library, null);
                viewHolder = new ViewHolder();

                viewHolder.icon_add = convertView.findViewById(R.id.icon_add_comment);
                viewHolder.tv_title = convertView.findViewById(R.id.tv_title);
                viewHolder.tv_body = convertView.findViewById(R.id.tv_body);

                convertView.setTag(viewHolder);
            } else viewHolder = (ViewHolder) convertView.getTag();

            //set the data to be displayed
            O_Comment comment = oComments.get(position);
            String title = comment.sTitle;
            if (!StringUtils.isEmpty(title)) {
                viewHolder.tv_title.setVisibility(View.VISIBLE);
                viewHolder.tv_title.setText(title);
            } else {
                viewHolder.tv_title.setVisibility(View.GONE);
                viewHolder.tv_title.setText("");
            }

            viewHolder.tv_body.setText(comment.sBody);

            int color = comment.containedIn(selectedComments) ? Color.LTGRAY : Color.WHITE;
            convertView.setBackgroundColor(color);

            return convertView;
        }
    }

    private static class CommentDragItem extends DragItem {
        CommentDragItem(Context context, int layoutId) {
            super(context, layoutId);
        }

        @Override
        public void onBindDragView(View clickedView, View dragView) {
            CharSequence title = ((TextView)clickedView.findViewById(R.id.tv_title)).getText();
            CharSequence body = ((TextView)clickedView.findViewById(R.id.tv_body)).getText();

            TextView tv_title = dragView.findViewById(R.id.tv_title);
            TextView tv_body = dragView.findViewById(R.id.tv_body);
            if (title != null && title.length() > 0) {
                tv_title.setVisibility(View.VISIBLE);
                tv_title.setText(title);
            } else {
                tv_title.setVisibility(View.GONE);
                tv_title.setText("");
            }

            tv_body.setText(body);
            dragView.setBackgroundColor(dragView.getResources().getColor(R.color.gray_color));
        }
    }

    public class CommentsEditAdapter extends DragItemAdapter<Pair<Long, O_Comment>, CommentsEditAdapter.ViewHolder> {

        private final int mLayoutId;
        private final int mGrabHandleId;
        private final boolean mDragOnLongPress;

        public CommentsEditAdapter(ArrayList<Pair<Long, O_Comment>> list, int layoutId, int grabHandleId, boolean dragOnLongPress) {
            mLayoutId = layoutId;
            mGrabHandleId = grabHandleId;
            mDragOnLongPress = dragOnLongPress;
            setHasStableIds(true);
            setItemList(list);
        }

        public void setItemArray(ArrayList<Pair<Long, O_Comment>> list) {
            setItemList(list);
        }

        @Override
        public long getUniqueItemId(int position) {
            return mItemList.get(position).first;
        }

        @Override
        public ViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(parent.getContext()).inflate(mLayoutId, parent, false);
            return new ViewHolder(view);
        }

        @Override
        public void onBindViewHolder(ViewHolder holder, int position) {
            super.onBindViewHolder(holder, position);

            O_Comment comment = mItemList.get(position).second;
            String title = comment.sTitle;
            if (!StringUtils.isEmpty(title)) {
                holder.tv_title.setVisibility(View.VISIBLE);
                holder.tv_title.setText(title);
            } else {
                holder.tv_title.setVisibility(View.GONE);
                holder.tv_title.setText("");
            }

            holder.tv_body.setText(comment.sBody);
            holder.btn_Deselect.setOnClickListener(v -> {
                int idx = comment.indexIn(selectedComments);
                if (idx > -1) {
                    selectedComments.remove(idx);
                    ReloadCommentsWithSearchText("");
                }
            });

            holder.itemView.setTag(mItemList.get(position));
        }

        class ViewHolder extends DragItemAdapter.ViewHolder {
            TextView tv_title, tv_body;
            ImageButton btn_Deselect;

            ViewHolder(final View itemView) {
                super(itemView, mGrabHandleId, mDragOnLongPress);
                tv_title = itemView.findViewById(R.id.tv_title);
                tv_body = itemView.findViewById(R.id.tv_body);
                btn_Deselect = itemView.findViewById(R.id.btn_DeselectComment);
            }

            @Override
            public void onItemClicked(View view) {

            }

            @Override
            public boolean onItemLongClicked(View view) {
                return true;
            }
        }
    }


    private void ReloadCommentsWithSearchText(String searchText) {
        try {
            comments.clear();
            boolean isSearchAll = searchText == null || searchText.length() == 0;

            switch (currentTab) {
                case global:
                    List<ai_QuickPhrase> lsQuickPhrase = isSearchAll ? ai_QuickPhrase.listAll(ai_QuickPhrase.class) :
                            CommonDB.SearchPhrase(searchText, if_CommentsLibrary.this);
                    boolean sortByDesc = CommonHelper.GetPreferenceBoolean(this, Constants.Settings.bCommentSortDesc);
                    Collections.sort(lsQuickPhrase, (o1, o2) -> {
                        String first = sortByDesc ? o2.sComments : o1.sComments;
                        String second = sortByDesc ? o1.sComments : o2.sComments;
                        return first.trim().compareToIgnoreCase(second.trim());
                    });

                    for (int i = 0; i < lsQuickPhrase.size(); i++) {
                        comments.add(new O_Comment(lsQuickPhrase.get(i)));
                    }
                    break;
                case area:
                case item:
                    if (arrSmartComments == null) break;

                    for (int i = 0; i < arrSmartComments.size(); i++) {
                        JSONObject item = arrSmartComments.get(i);

                        int iLayout = 0;
                        try { iLayout = item.getInt("I"); } catch (Exception e) { }

                        if ((currentTab == TabItem.area && oPInsItem.iSLayoutID == iLayout) ||
                                (currentTab == TabItem.item && (iLayout == oInsItem.iSLayoutID))) {

                            String title = "", body = "";
                            try {
                                title = item.getString("T");
                                body = item.getString("C");
                            } catch (Exception e) { }

                            if (isSearchAll ||
                                    title.toLowerCase().contains(searchText.toLowerCase()) ||
                                    body.toLowerCase().contains(searchText.toLowerCase())) {

                                HashMap<String, String> map = new HashMap<>();
                                map.put("T", title);
                                map.put("C", body);
                                map.put("I", iLayout + "");
                                comments.add(new O_Comment(map));
                            }

                        }
                    }

                    break;

                case selected:
                    comments = (ArrayList<O_Comment>)selectedComments.clone();
                    break;
            }

            runOnUiThread(() -> {
                if (currentTab != TabItem.selected) {
                    oListView.setVisibility(View.VISIBLE);
                    dragListView.setVisibility(View.GONE);

                    reloadListViewData(comments);
                } else {
                    dragListView.setVisibility(View.VISIBLE);
                    oListView.setVisibility(View.GONE);

                    dragListView.setLayoutManager(new LinearLayoutManager(if_CommentsLibrary.this));
                    ArrayList<Pair<Long, O_Comment>> dataSource = new ArrayList<>();
                    for (int i = 0; i < comments.size(); i++) {
                        dataSource.add(new Pair<>((long) i, comments.get(i)));
                    }
                    CommentsEditAdapter adapter = new CommentsEditAdapter(
                            dataSource, R.layout.cell_comments_library_edit, R.id.order_handler, false
                        );
                    dragListView.setAdapter(adapter, true);
                    dragListView.setCanDragHorizontally(false);
                    dragListView.setCustomDragItem(
                            new CommentDragItem(if_CommentsLibrary.this, R.layout.cell_comments_library_edit)
                    );
                }
            });

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(
                    "Exception", "if_CommentsLibrary.ReloadCommentsWithSearchText", ex, this
            );
        }

    }

    private void parseSmartComments() {

        int iInsItemID = getIntent().getIntExtra(Constants.Extras.iInsItemID, 0);
        iPLayoutID = getIntent().getIntExtra(Constants.Extras.iPLayoutID, 0);

        if (iPLayoutID <= 0 || iInsItemID <= 0) return;

        try {
            int iCheckListID = -1;
            oInsItem = ai_InsItem.findById(ai_InsItem.class, (long)iInsItemID);
            oPInsItem = ai_InsItem.findById(ai_InsItem.class, (long)oInsItem.iPInsItemID);

            int iInsID = oInsItem.iInsID;
            ai_Inspection oInspection = ai_Inspection.findById(ai_Inspection.class, (long) iInsID);

            List<ai_CheckList> lsCheckList = ai_CheckList.listAll(ai_CheckList.class);
            for (ai_CheckList oCheck : lsCheckList) {
                if (oCheck.sPTC.equals(oInspection.sPTC)) {
                    iCheckListID = oCheck.iSCheckListID;
                    break;
                }
            }
            if (iCheckListID <= -1) return;

            String fileName = "SC_" + iCheckListID + ".json";
            String jsonStr = CommonRequestInspection.ReadFileToText(getFilesDir() + "/" + fileName);

            if (jsonStr != null) {
                JSONArray array = new JSONObject(jsonStr).getJSONArray("Data");
                arrSmartComments = new ArrayList<>();
                for (int i = 0; i < array.length(); i++) {
                    JSONObject item = array.getJSONObject(i);
                    int iLayout = 0;
                    try { iLayout = item.getInt("I"); } catch (Exception e) { }
                    if (oPInsItem.iSLayoutID == iLayout || iLayout == oInsItem.iSLayoutID) {
                        arrSmartComments.add(item);
                    }
                }

                List<ai_Layout> lsLayout = ai_Layout.find(
                        ai_Layout.class, "I_S_LAYOUT_ID = ?", "" + oInsItem.iSLayoutID
                    );
                if (lsLayout != null && lsLayout.size() > 0) {
                    oLayout = lsLayout.get(0);
                }
            }

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(
                    "Exception", "if_CommentsLibrary.parseSmartComments", ex, this
                );
        }

    }

    private String getWordsFromComment(JSONObject comment) {
        String title = "", body = "";
        try {
            title = comment.getString("T");
            body = comment.getString("C");
        } catch (Exception e) { }

        return (title + body).trim();
    }

    private void saveSelectedComments() {
        if (selectedComments == null || selectedComments.size() == 0) { return; }
        ArrayList<String> items = new ArrayList<>();
        for (int i = 0; i < selectedComments.size(); i++) {
            items.add(selectedComments.get(i).sBody);
        }

        String selectedText = String.join(" ", items);
        Intent intent = getIntent();
        intent.putExtra(Constants.Extras.sComments, selectedText);
        setResult(COMMENTS_LIBRARY_RESULT_CODE, intent);
    }

    private void UpdateBtnTabColors(View selectedOne) {
        for (Button btnTab : btnTabs) {
            int color = getResources().getColor(
                    btnTab == selectedOne ? R.color.comments_tab_selected : R.color.comments_tab_normal
                );
            btnTab.setTextColor(color);
        }
    }

    private void AnimateMoveBottomLine(View btnTab, Boolean animated) {
        View tab = findViewById(R.id.layout_tab_bar);
        Point centerOfBtn = new Point((int) (btnTab.getWidth() * 0.5), 0);
        Point targetPos = ViewUtils.convertPoint(centerOfBtn, btnTab, tab);
        float posX = (float) (targetPos.x - vBottomLine.getWidth() * 0.5);
        if (animated) {
            vBottomLine.animate().setDuration(100).x(posX);
        } else {
            vBottomLine.setX(posX);
        }
    }


}
