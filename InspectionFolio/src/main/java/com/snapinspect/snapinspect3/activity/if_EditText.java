package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.Layout;
import android.text.Spannable;
import android.text.SpannableString;
import android.text.style.BackgroundColorSpan;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.TextView;
import android.widget.Toast;

import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.SimpleTextWatcher;
import com.snapinspect.snapinspect3.util.SystemUiHider;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_EditText extends Activity {
    private int iInsItemID = 0;
    private int iCheckListID = 0;
    private int iPLayoutID = 0;
    private String sComments;
    private String sTitle;
    private String sWords;
    private int iTextPosition;
    private int iPosition;
    private final String sType = "";
    private ai_InsItem oInsItem;
    private EditText oSearch;
    private final String sCurrentString = "";
    private ArrayList<String> lsCommentTexts = new ArrayList<String>();
    private final ArrayList<String> lsMainTexts = new ArrayList<String>();
    private final List<Integer> starPositions = new ArrayList<Integer>();
    private ListView oListView;
    private Ins_QP_Adapter adapter;
    private  EditText oEditText;
    private  boolean isEditable = false;
    private int startPosition;
    private int endPosition;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            getActionBar().show();
            getActionBar().setDisplayHomeAsUpEnabled(true);

            setContentView(R.layout.activity_edittext);

            iInsItemID = getIntent().getIntExtra(Constants.Extras.iInsItemID, 0);
            iCheckListID = getIntent().getIntExtra("iCheckListID", 0);
            sTitle = getIntent().getStringExtra("smart_title");
            sComments = getIntent().getStringExtra("smart_comments");
            sWords = getIntent().getStringExtra("sWord");
            iTextPosition = getIntent().getIntExtra(Constants.Extras.iTextPosition, 0);
            iPosition = getIntent().getIntExtra(Constants.Extras.iPosition, 0);
            iPLayoutID = getIntent().getIntExtra(Constants.Extras.iPLayoutID, 0);

            getActionBar().setTitle(sTitle);
            adapter = new Ins_QP_Adapter(this,
                    R.layout.cell_smarttext_textview, lsCommentTexts);
            oListView = findViewById(R.id.lv_Comment_QuickText);
            oListView.setAdapter(adapter);
//            oListView.setItemsCanFocus(false);
            oListView.setChoiceMode(ListView.CHOICE_MODE_SINGLE);
            oListView.setOnItemClickListener(new ListClickHandler());
            oEditText = findViewById(R.id.txt_comments);
            oEditText.addTextChangedListener(new SimpleTextWatcher() {
                @Override
                public void afterTextChanged(Editable editable) {
                    String sText = editable.toString();
                }
            });

            View.OnTouchListener otl = new View.OnTouchListener() {
                @Override
                public boolean onTouch(View v, MotionEvent event) {
                    switch (event.getAction()) {
                        case MotionEvent.ACTION_DOWN:
                            Layout layout = ((EditText) v).getLayout();
                            float x = event.getX() + oEditText.getScrollX();
                            float y = event.getY() + oEditText.getScrollY();

                            int offsetY = layout.getLineForVertical((int)y);
                            int offsetX = layout.getOffsetForHorizontal(offsetY, x);
                            if(offsetX>-1) {
                                boolean findSmart = false;
                                for(int i = 0; i < starPositions.size(); i = i+2) {
                                    if (starPositions.size() > i+1) {
                                        if (starPositions.get(i) < offsetX && offsetX < starPositions.get(i + 1)) {
                                            String comment = oEditText.getText().toString();
                                            startPosition = starPositions.get(i);
                                            endPosition = starPositions.get(i+1);
                                            findSmart = true;
                                            String subStr = comment.substring(starPositions.get(i)+2, starPositions.get(i+1));
                                            ReadFile(subStr);
                                            CommonHelper.hideSoftKeyboard(if_EditText.this);
                                            break;
                                        }
                                    }
                                }
                                if (!findSmart) {
                                    ReadFile("");
                                }
                            }

                            break;
                    }
                    return true;
                }
            };
            oEditText.setOnTouchListener(otl);

            oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);
            oSearch = findViewById(R.id.search_smarttext);
            try {
                oSearch.setOnEditorActionListener(new TextView.OnEditorActionListener() {
                    @Override
                    public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                        if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                            //performSearch();
                            String searchText = oSearch.getText().toString();
                            if (searchText == "") {
                                lsCommentTexts = lsMainTexts;
                            } else {
                                if (lsCommentTexts != null) {
                                    lsCommentTexts.clear();
                                }

                                for (int i = 0; i < lsMainTexts.size(); i++) {
                                    String title = lsMainTexts.get(i);
                                    if (title.toLowerCase().contains(searchText.toLowerCase())) {
                                        lsCommentTexts.add(lsMainTexts.get(i));
                                    }
                                }
                            }

                            oListView = findViewById(R.id.lv_Comment_QuickText);
                            oListView.setAdapter(adapter);
                            oListView.setItemsCanFocus(false);
                            oListView.setOnItemClickListener(new ListClickHandler() );
                            adapter.notifyDataSetChanged();
                            CommonHelper.hideSoftKeyboard(if_EditText.this);
                            return true;
                        }
                        return false;
                    }
                });
            }catch(Exception ex){
                ai_BugHandler.ai_Handler_Exception("Exception", "if_EditText.main", ex, this);
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditText.onCreate", ex, this);
        }
    }

    public void ReadFile (String selectedText){
        try {
            if (selectedText.equals("")) {
                if (lsCommentTexts.size() > 0)
                    lsCommentTexts.clear();
                oListView = findViewById(R.id.lv_Comment_QuickText);
                oListView.setAdapter(adapter);
                oListView.setItemsCanFocus(false);
                oListView.setOnItemClickListener(new ListClickHandler() );
                adapter.notifyDataSetChanged();
                return;
            }
            String fileName = "ST_" + iCheckListID + ".json";

            File file = new File(this.getFilesDir() + "/" + fileName);
            FileInputStream stream = new FileInputStream(file);
            String jsonStr = null;
            try {
                FileChannel fc = stream.getChannel();
                MappedByteBuffer bb = fc.map(FileChannel.MapMode.READ_ONLY, 0, fc.size());

                jsonStr = Charset.defaultCharset().decode(bb).toString();
            }
            catch(Exception e){
                e.printStackTrace();
            }
            finally {
                stream.close();
            }
            if (lsCommentTexts.size() > 0)
                lsCommentTexts.clear();
            if (lsMainTexts.size() > 0)
                lsMainTexts.clear();

            if (jsonStr != null) {
                JSONObject jsonObj = new JSONObject(jsonStr);

                // Getting data JSON Array nodes
                JSONArray data = jsonObj.getJSONArray("Data");
                for (int i = 0; i < data.length(); i++) {
                    JSONObject c = data.getJSONObject(i);
                    String  t = c.getString("T");
                    if (t.equals(selectedText)) {
                        if (c.getString("S") != null) {
                            JSONArray sArry = c.getJSONArray("S");
                            for (int j = 0; j < sArry.length(); j++) {
                                String str = sArry.getString(j);
                                lsCommentTexts.add(str);
                                lsMainTexts.add(str);
                            }
                        }
                    }
                }

                oListView = findViewById(R.id.lv_Comment_QuickText);
                oListView.setAdapter(adapter);
                oListView.setItemsCanFocus(false);
                oListView.setOnItemClickListener(new ListClickHandler() );
                adapter.notifyDataSetChanged();

            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    @Override
    protected void onResume() {
        try {
            super.onResume();
            oEditText.setText(sComments);
           setHighlight(oEditText);
           // oEditText.setEnabled(false);

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditText.onResume", ex, this);
        }
    }
    public void setHighlight (EditText editText) {
        String str = editText.getText().toString();
        String guess = "**";

        if (starPositions.size() > 0)
            starPositions.clear();

        for (int index = str.indexOf(guess);
             index >= 0;
             index = str.indexOf(guess, index + 1))
        {
            starPositions.add(index);
        }
        Spannable wordtoSpan = new SpannableString(str);
        for (int idx = 0; idx < starPositions.size(); idx = idx + 2) {
            if (starPositions.size() > idx+1)
                wordtoSpan.setSpan(new BackgroundColorSpan(Color.RED), starPositions.get(idx), starPositions.get(idx+1)+2, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE);

        }
        oEditText.setText(wordtoSpan);
       // oEditText.setBackgroundColor(Color.RED);
    }

    public void editEnableClicked(View v) {
        isEditable = !isEditable;
        oEditText.setEnabled(isEditable);
        Button btn = (Button)v;
        btn.setText(isEditable? "Edit Enable" : "Edit Disable");
        Toast.makeText(getApplicationContext(), "Editing Comment Text " + (isEditable? "Enabled" : "Disabled"), Toast.LENGTH_SHORT).show();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_edittext, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected( MenuItem item )
    {
        try {
            if (item.getItemId() == android.R.id.home) {
                Intent oIntent = new Intent(if_EditText.this, if_smartcomment.class);
                oIntent.putExtra(Constants.Extras.sWords, oEditText.getText().toString());
                oIntent.putExtra(Constants.Extras.iPosition, iPosition);
                oIntent.putExtra(Constants.Extras.iTextPosition, oEditText.getSelectionStart());
                oIntent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
                oIntent.putExtra(Constants.Extras.iPLayoutID, iPLayoutID);
                oIntent.setFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION);
                startActivity(oIntent);
                finish();
                onBackPressed();
            } else if (item.getItemId() == R.id.action_Save) {
                String sText = oEditText.getText().toString();

                ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);
                String sResult = sWords.substring(0, iTextPosition) + sText + " " + sWords.substring(iTextPosition);
                CommonHelper.SetValue(iPosition, oInsItem, sResult);
                oInsItem.save();
                onBackPressed();
                finish();
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditText.onOptionsItemSelected", ex, this);
        }

        return true;
    }

    private void ShowAlert(String sTitle, String sMessage){
        AlertDialog.Builder builder = CommonUI.GetAlertBuilder(sTitle, sMessage, if_EditText.this, false, true);

        builder.show();
    }
    public class ListClickHandler implements AdapterView.OnItemClickListener {

        @Override
        public void onItemClick(AdapterView<?> adapter, View view, int position, long arg3) {
            TextView title_txt = view.findViewById(R.id.txt_comment);
            String text = title_txt.getText().toString();
            String comment = oEditText.getText().toString();
            String new_comment = comment.substring(0, startPosition) + text + comment.substring(endPosition+2);
            oEditText.setText(new_comment);

            setHighlight(oEditText);
            lsCommentTexts.clear();
            ReadFile("");
        }
    }

    private class Ins_QP_Adapter extends ArrayAdapter<String>
    {
        private final Context oContext;
        public Ins_QP_Adapter(Context context, int textViewResourceId,
                              ArrayList<String> Strings) {

            //let android do the initializing :)
            super(context, textViewResourceId, Strings);
            oContext = context;
        }


        //class for caching the views in a row
        private class ViewHolder
        {
            TextView title,comment;
        }

        ViewHolder viewHolder;

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {

            if(convertView==null)
            {
                //inflate the custom layout
                convertView = ((LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.cell_smarttext_textview, null);
                viewHolder=new ViewHolder();

                viewHolder.title= convertView.findViewById(R.id.txt_comment);
                //link the cached views to the convertview
                convertView.setTag(viewHolder);
            }
            else
                viewHolder=(ViewHolder) convertView.getTag();
            //set the data to be displayed

            viewHolder.title.setText(lsCommentTexts.get(position));

            //return the view to be displayed
            return convertView;
        }
    }
}
