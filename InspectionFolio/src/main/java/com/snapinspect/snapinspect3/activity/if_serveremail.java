package com.snapinspect.snapinspect3.activity;

import android.graphics.Color;
import android.os.Bundle;
import android.app.Activity;
import android.os.Handler;
import android.os.Looper;
import android.text.Html;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.EditText;
import android.widget.TextView;

import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.IF_RestClient;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.StringUtils;

public class if_serveremail extends Activity {

    MaterialDialog oDialog;
    private EditText titleTextview, subjectTextview, bodyTextview;
    private String title, subject, body;
    private int iInsID;
    private final String successTitle = "Success!";
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_serveremail);

        getActionBar().setDisplayHomeAsUpEnabled(true);
        getActionBar().show();
        int titleId = getResources().getIdentifier("action_bar_title", "id", "android");
        TextView abTitle = findViewById(titleId);
        abTitle.setTextColor(Color.WHITE);

        titleTextview = findViewById(R.id.title_id);
        subjectTextview = findViewById(R.id.subject_id);
        bodyTextview = findViewById(R.id.body_id);

        title = getIntent().getStringExtra(Constants.Extras.mailto);
        subject = getIntent().getStringExtra(Constants.Extras.subject);
        body = getIntent().getStringExtra(Constants.Extras.body);
        iInsID = getIntent().getIntExtra(Constants.Extras.iInsID, 0);
        titleTextview.setText(title);
        subjectTextview.setText(subject);
        /*titleTextview.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                String string = s.toString();
                if (CommonHelper.IsEmail(string)) {
                    string = string + "; ";
                    titleTextview.setText(string);
                    titleTextview.invalidate();
                }
            }
        });*/
        bodyTextview.setText(StringUtils.convertHtml(body));

        getActionBar().setTitle(subject);

    }
   /* public static boolean isEmailValid(String email) {
        boolean isValid = false;

        String expression = "^[\\w\\.-]+@([\\w\\-]+\\.)+[A-Z]{3}$";
        CharSequence inputStr = email;

        Pattern pattern = Pattern.compile(expression, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(inputStr);
        if (matcher.matches()) {
            isValid = true;
        }
        return isValid;
    }*/


    @Override
    public void onResume(){
        super.onResume();

    }
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_server_email, menu);
        return true;
    }
    @Override
    public boolean onOptionsItemSelected( MenuItem item )
    {
        try {
            if (item.getItemId() == android.R.id.home) {
                finish();
            } else if (item.getItemId() == R.id.action_save) {
                sendEmail();
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_home.onOptionItemSelected", ex, this);
        }

        return true;
    }
    public void sendEmail()
    {
       // final ProgressDialog oDialog;
        try {
            String recipient = titleTextview.getText().toString().replaceAll("\\s+", "").trim();

            if (recipient == null || recipient.length() == 0) {
                ShowAlert("Error", "Please Enter Email Address.");
                return;
            }
            if (!recipient.endsWith(";")){
                recipient = recipient + ";";
            }
            //String[] parts = recipient.split(";"); // escape .
           // if (parts != null && parts.length > 0) {

           //     String part1 = parts[0];
           //     recipient = part1;
           // }
           // for (String part : parts){
             //   if (!CommonHelper.IsEmail(part)){
            //        ShowAlert("Error", "Invalid Email Address. Please use ; to seperate email address.");
             //       return;
             //   }
           // }


            //Log.e("osama", "title -- >" + recipient);
            String htmlbody = Html.toHtml(bodyTextview.getText());
            oDialog = CommonUI.ShowMaterialProgressDialog(this, "Message", "Processing Request ...");
//            oDialog =CommonUI.GetProgressDialog(this, "Message", "Processing Request ...", true);


            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(if_serveremail.this, "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(if_serveremail.this, "sToken"));
            oParams.add("sRecipient", recipient);
            oParams.add("sSubject", subjectTextview.getText().toString());
            oParams.add("sBody", htmlbody);
            oParams.add("iInsID", "" + iInsID);
            oParams.add("sType", "i");
            final String sURL = "/IOAPI/SendEmail";

            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    CommonUI.DismissMaterialProgressDialog(if_serveremail.this, oDialog);
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            String message, title;
                            if (response != null) {
                                //
                                if (response.getBoolean("success")) {
                                    title = successTitle;
                                    message = "Sent email successfully";
                                } else {
                                    message = "Asset Response Error. <NAME_EMAIL>.";
                                    title = "Error";
                                }
                                try {
                                    message = response.getString("message");
                                } catch (Exception ex) {
                                    //
                                }
                            } else {
                                title = "Error";
                                message = "To update contact information, please make sure you are connected to Internet.";
                            }

                            String finalMessage = message;
                            new Handler(Looper.getMainLooper()).post(new Runnable() {
                                public void run() {
                                    ShowAlert(title, finalMessage);
                                }
                            });
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditContact.UpdateContact", ex, if_serveremail.this);
                        }

                    }
                }

                @Override
                public void onFailure(int statusCode, org.apache.http.Header[] headers, java.lang.Throwable e, org.json.JSONObject errorResponse) {

                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            oDialog.dismiss();
                            ShowAlert("Error", "To update asset or contact information, please make sure you are connected to Internet.");

                        }
                    });
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_exist.Upload.onReceiveResult", ex, getApplicationContext());
        }
    }
    private void ShowAlert(final String sTitle, String sMessage){
        CommonUI.ShowAlert(if_serveremail.this, sTitle, sMessage);
    }
}
