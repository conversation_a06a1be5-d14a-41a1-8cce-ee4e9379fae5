package com.snapinspect.snapinspect3.activity;

import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.net.Uri;
import android.os.Bundle;
import android.app.Activity;
import androidx.core.app.ActivityCompat;
import android.view.MenuItem;

import com.google.android.gms.maps.CameraUpdate;
import com.google.android.gms.maps.CameraUpdateFactory;
import com.google.android.gms.maps.GoogleMap;
import com.google.android.gms.maps.MapFragment;
import com.google.android.gms.maps.OnMapReadyCallback;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.CameraPosition;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.snapinspect.snapinspect3.Helper.CommonDB_Assets;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_Assets;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.R;

import java.util.ArrayList;
import java.util.List;

public class if_InspectionMap extends Activity implements OnMapReadyCallback {

    ai_Assets oAsset;
    MapFragment mapFragment;
    private GoogleMap mMap;
    List<Marker> markersList = new ArrayList<Marker>();
    LatLngBounds.Builder builder;
    CameraUpdate cu;
    double curLatitude = 0.0, curLongitude = 0.0;
    private static final String TAG = "if_InspectionMap";
    private boolean isShowed = false;
    private LatLng assetAddress;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if__inspection_map);
        CommonHelper.hideSoftKeyboard(this);
        getActionBar().setDisplayHomeAsUpEnabled(true);

        int iSAssetID = getIntent().getIntExtra(Constants.Extras.iSPAssetID, 0);
        String sSearchAddress = "";

        oAsset = CommonDB_Assets.GetAssetBy_iSAssetID(iSAssetID);
        sSearchAddress = oAsset.sAddressOne + ", " + oAsset.sAddressTwo;
        if (oAsset.iSPAssetID > 0) {
            ai_Assets oTemp = CommonDB_Assets.GetAssetBy_iSAssetID(oAsset.iSPAssetID);
            sSearchAddress = sSearchAddress + ", " + oTemp.sAddressOne + ", " + oTemp.sAddressTwo;
            if (oTemp.iSPAssetID > 0){
                ai_Assets oTemp1 = CommonDB_Assets.GetAssetBy_iSAssetID(oTemp.iSPAssetID);
                sSearchAddress = sSearchAddress + ", " + oTemp1.sAddressOne + ", " + oTemp1.sAddressTwo;
            }
        }

        getActionBar().setTitle(sSearchAddress);
        mapFragment = (MapFragment) getFragmentManager()
                .findFragmentById(R.id.map);
    }

    private final GoogleMap.OnMyLocationChangeListener myLocationChangeListener = new GoogleMap.OnMyLocationChangeListener() {
        @Override
        public void onMyLocationChange(Location location) {
            LatLng loc = new LatLng(location.getLatitude(), location.getLongitude());

            if (isShowed)
                return;
            isShowed = true;

            if(mMap != null){
                if (markersList.size() > 0) {
                    builder = new LatLngBounds.Builder();
                    for (Marker m : markersList) {
                        builder.include(m.getPosition());
                    }
                    builder.include(loc);
                    /**initialize the padding for map boundary*/
                    int padding = 50;
                    /**create the bounds from latlngBuilder to set into map camera*/
                    LatLngBounds bounds = builder.build();
                    /**create the camera with bounds and padding to set into map*/
                    cu = CameraUpdateFactory.newLatLngBounds(bounds, padding);
                    /**call the map call back to know map is loaded or not*/
                    mMap.setOnMapLoadedCallback(new GoogleMap.OnMapLoadedCallback() {
                        @Override
                        public void onMapLoaded() {
                            /**set animated zoom camera into map*/
                            mMap.animateCamera(cu);

                        }
                    });

                }
                curLatitude = location.getLatitude();
                curLongitude = location.getLongitude();
            }
        }
    };

    @Override
    public void onMapReady(GoogleMap googleMap) {
        try{
            googleMap.setMapType(GoogleMap.MAP_TYPE_NORMAL);
            googleMap.setTrafficEnabled(true);

            googleMap.getUiSettings().setZoomControlsEnabled(true);



            mMap = googleMap;
            markersList.clear();
            showLocationFromAddress(this, oAsset.sAddressOne + "," + oAsset.sAddressTwo);

            mMap.setOnMyLocationChangeListener(myLocationChangeListener);
            setMyCurrentLocation(true);

            mMap.setOnInfoWindowClickListener(new GoogleMap.OnInfoWindowClickListener() {
                @Override
                public void onInfoWindowClick(Marker marker) {
                    try {
                        if (assetAddress != null) {
                            final Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("http://maps.google.com/maps?" + "saddr="+ assetAddress.latitude + "," + assetAddress.longitude + "&daddr=" + curLatitude + "," + curLongitude));
                            intent.setClassName("com.google.android.apps.maps","com.google.android.maps.MapsActivity");
                            startActivity(intent);
                        }
//
//                        String title = marker.getTitle();
//
//
//                        Intent intent1 = new Intent(if_InspectionMap.this, if_MapDetails.class);
//                        intent1.putExtra(Constants.Extras.iSPAssetID, oAsset.iSAssetID);
//                        intent1.putExtra("MarkType", 2);
//                        intent1.putExtra("MapType", 1);
//
//                        startActivity(intent1);
//                        finish();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
        }catch(SecurityException e){
            e.printStackTrace();
        }catch(Exception e){
            e.printStackTrace();
        }

    }
    public void setMyCurrentLocation(boolean flag) {

        if (ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED && ActivityCompat.checkSelfPermission(this, android.Manifest.permission.ACCESS_COARSE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            // TODO: Consider calling
            return;
        }
        mMap.setMyLocationEnabled(flag);
    }
    public void showLocationFromAddress(Context context, String strAddress) {

        Geocoder coder = new Geocoder(context);
        List<Address> address;
        LatLng p1 = null;

        try {
             address = coder.getFromLocationName(strAddress, 5);
            if (address == null) {
                return;
            }
            Address location = address.get(0);
            location.getLatitude();
            location.getLongitude();

            p1 = new LatLng(location.getLatitude(), location.getLongitude() );
            assetAddress = p1;

            CameraPosition position = CameraPosition.builder()
                    .target(p1)
                    .zoom(10.0f)
                    .bearing(0.0f)
                    .tilt(0.0f)
                    .build();

            mMap.animateCamera(CameraUpdateFactory
                    .newCameraPosition(position), null);

            MarkerOptions mapOptions;

            mapOptions = new MarkerOptions().position(p1).title(strAddress).icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_RED));

            Marker  marker = mMap.addMarker(mapOptions);
            marker.showInfoWindow();
            markersList.add(marker);

        } catch (Exception ex) {
            ex.printStackTrace();
            ShowAlert("Error", "No Internet!");
        }
    }

    private void ShowAlert(final String sTitle, String sMessage){
        CommonUI.ShowAlert(this, sTitle, sMessage);
//        AlertDialog.Builder builder = CommonUI.GetAlertBuilder(sTitle, sMessage, if_InspectionMap.this, false, true);
//
//        builder.show();
    }
    @Override
    public void onResume(){
        super.onResume();
        try{
            mapFragment.getMapAsync(this);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.onResume", ex, this);
        }
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        try {
            switch (item.getItemId()) {
                default:
                    finish();
                    onBackPressed();
                    break;
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_quickphrase.onOptionsItemSelected", ex, this);
        }
        return true;
    }
}
