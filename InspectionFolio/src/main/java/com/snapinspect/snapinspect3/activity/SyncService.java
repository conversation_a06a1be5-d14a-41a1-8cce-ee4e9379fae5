package com.snapinspect.snapinspect3.activity;

import android.app.IntentService;
import android.content.Intent;
import android.os.Bundle;
import android.os.ResultReceiver;
import android.util.Log;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.BasicSessionCredentials;
import com.amazonaws.event.ProgressEvent;
import com.amazonaws.event.ProgressListener;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.*;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_File;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.FileUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.io.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by TerrySun on 12/03/14.
 */
public class SyncService extends IntentService {

    //NotificationManager nman;
    //Notification n;
    public SyncService() {
        super("SimpleIntentService");
    }
    private ResultReceiver oReceiver;
    public static final int SYNC_PROGRESS = 8344;
    public static final int SYNC_SUCCESS = 8345;
    public static final int SYNC_FAIL = 8346;
    public static final int SUBMIT_PROCESS = 8347;
    public static final int SYNC_TOASTMESSAGE = 8378;

    private static final String RECEIVING_DATA_MESSAGE = " Receiving Data ... ";
    
    public void PublishProgress(int lResult, String sTitle,  String sMessage){
        try {
            Bundle resultData = new Bundle();
            resultData.putString(Constants.Keys.sTitle, sTitle);
            resultData.putString(Constants.Keys.sMessage, sMessage);
            oReceiver.send(lResult, resultData);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.PublishProgress", ex, this);
        }

    }

    private boolean AttachPropertyPhotos(List<ai_File> lsFile){
        try{
            if (lsFile != null && lsFile.size() > 0){
                int counter = 1;
                for (ai_File oFile : lsFile){
                    if (CommonHelper.bFileExist(oFile.sFile)){
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Upload Photo - " + counter, "Upload Photo - " + counter);

                        PublishProgress(SYNC_PROGRESS, "", "Attach Asset File " + counter + " of " + lsFile.size());
                        // PublishProgress(UPDATE_PROGRESS, "", "Upload Photo " + counter + " of " + lsPhoto.size());
                       /* ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
                        lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
                        lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));
                        lsParams.add(new BasicNameValuePair("iSFileID", "" + oFile.iFileID));*/
                        HashMap<String, String> lsParams = new HashMap<>();
                        lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                        lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
                        lsParams.put("iSFileID", "" + oFile.iFileID);
                        JSONObject oJson = IF_SyncClient.PostRequest("/IOAPI/AttachAssetPhoto", lsParams);
                        try{
                            if (oJson != null && !oJson.getBoolean("success")){
                                oJson = IF_SyncClient.PostRequest("/IOAPI/AttachAssetPhoto", lsParams);
                                if (oJson != null && !oJson.getBoolean("success")){
                                    return false;
                                }
                            }
                            oFile.sCustomOne = CommonJson.RemoveJsonKey("AttachCMD", oFile.sCustomOne);
                            oFile.save();
                        }catch (Exception ex){
                            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto.InterException", ex);
                            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.AttachPropertyPhoto.InterException", ex, this);
                            return false;
                        }
                        counter++;
                    }
                }
                //HashMap<String, Object> oMap = new HashMap<String, Object>();
                //  oMap.put("Title", sTitle);
                // oMap.put("Photos", counter + " out of " + lsPhoto.size() + " Uploaded");
                // CommonHelper.IntercomEvent(this, "Android Upload Inspection Photos", oMap);
                // CommonDB.InsertLog(this, "Upload", "" + counter + " Photos Uploaded - " + sTitle);
            }

        }catch(Exception ex){
            //ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhoto", ex, this);
            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.UploadFile", ex, this);
            return false;
        }
        return true;
    }

    private boolean UploadFiles(List<ai_File> lsFile){
        try{
            if (lsFile != null && lsFile.size() > 0){
                int counter = 1;
                for (ai_File oFile : lsFile){
                    if (CommonHelper.bFileExist(oFile.sFile)){
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Upload Photo - " + counter, "Upload Photo - " + counter);
                        PublishProgress(SYNC_PROGRESS, "", "Upload Asset File " + counter + " of " + lsFile.size());
                        HashMap<String, String> lsParams = new HashMap<>();
                        lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                        lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
                        lsParams.put("iPropertyID", "" + oFile.iSObjectID);
                        lsParams.put("dtDateTaken", oFile.dtDateTime);
                        lsParams.put("iSize", "" + oFile.iSize);
                        lsParams.put("sClientFileName", oFile.sFile.substring(oFile.sFile.lastIndexOf("/")+1));

                        JSONObject oJson = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFile", lsParams);
                        try{
                            if (oJson != null && oJson.getBoolean("success")){
                                if (IF_SyncClient.UploadFile(oJson.getString("sURL"),oFile.sFile ) != 200){
                                    if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200){
                                       // sMessage.append("Failed Upload Photo Stage 2").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                oJson = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFile", lsParams);
                                if (oJson != null && oJson.getBoolean("success")){
                                    if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200){
                                        if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200){
                                           // sMessage.append("Failed Upload Photo Stage 2").append('\n');
                                            return false;
                                        }
                                    }
                                }
                                else{
                                    //sMessage.append("Failed Upload Photo Stage 1").append('\n');
                                    return false;
                                }
                            }
                            int iSFileID = oJson.getJSONObject("oFile").getInt("iFileID");
                            if (iSFileID > 0){
                                //lsParams.remove(2);lsParams.remove(2); lsParams.remove(2);

                                lsParams.clear();

                               /* lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
                                lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));
                                lsParams.add(new BasicNameValuePair("iSFileID", "" + iSFileID));*/
                                lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                                lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
                                lsParams.put("iSFileID", "" + iSFileID);
                                JSONObject oReturn = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFileSuccess", lsParams);
                                if (oReturn != null && oReturn.getBoolean("success")){
                                    oFile.iFileID = iSFileID;
                                    oFile.bUploaded = true;

                                    oFile.save();
                                }
                                else{
                                    oReturn = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFileSuccess", lsParams);
                                    if (oReturn != null && oReturn.getBoolean("success")){
                                        oFile.iFileID = iSFileID;
                                        oFile.bUploaded = true;
                                        oFile.save();

                                    }
                                    else{
                                      //  sMessage.append("Failed Upload Photo Stage 3").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                              //  sMessage.append("Failed Upload Photo Stage 1.1").append('\n');
                                return false;
                            }
                        }catch (Exception ex){
                            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto.InterException", ex);
                            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.UploadAssetFile.InterException", ex, this);
                            return false;
                        }
                        counter++;
                    }
                }
                //HashMap<String, Object> oMap = new HashMap<String, Object>();
              //  oMap.put("Title", sTitle);
               // oMap.put("Photos", counter + " out of " + lsPhoto.size() + " Uploaded");
               // CommonHelper.IntercomEvent(this, "Android Upload Inspection Photos", oMap);
               // CommonDB.InsertLog(this, "Upload", "" + counter + " Photos Uploaded - " + sTitle);
            }
            else{
                HashMap<String, Object> oMap = new HashMap<String, Object>();

                oMap.put("Files", "no files uploaded");
                CommonHelper.trackEvent(this, "Android Sync Upload Files", oMap);
               // CommonDB.InsertLog(this, LogType.ClientLog.toString(), "Photo list empty or no photo (BYPASS)");

            }

        }catch(Exception ex){
            //ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhoto", ex, this);
            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.UploadFile", ex, this);
            return false;
        }
        return true;
    }

    @Override
    protected void onHandleIntent(Intent workIntent) {
        // Gets data from the incoming Intent
        try{
            if (workIntent.getBooleanExtra(Constants.Extras.bForceSubmitData, false)){
                oReceiver = workIntent.getParcelableExtra("receiver");
                PublishProgress(SYNC_PROGRESS, "Submit Data", "Connecting ... ");
                boolean isExternal = workIntent.getBooleanExtra(Constants.Extras.bRequestInspection, false);
                SubmitData_Force(isExternal);
                PublishProgress(SYNC_SUCCESS, "Completed", "Please contact your administrator ... ");
            }
            else {
                oReceiver = workIntent.getParcelableExtra("receiver");
                if (!NetworkUtils.isNetworkAvailable(getApplicationContext())) {
                    PublishProgress(SYNC_FAIL, "Sync Failed", "Please Connect to Internet ... ");
                    return;
                }
                PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), "Connecting to Cloud ... ");

                try {
                    List<ai_File> lsFiles = CommonDB.ReadyToUploadFile();
                    UploadFiles(lsFiles);
                    List<ai_File> lsAttachFiles = CommonDB.ReadyToAttachPhoto();
                    AttachPropertyPhotos(lsAttachFiles);
                }   catch (Exception ex) {
                    ex.printStackTrace();
                }


                String sDateTime = CommonHelper.GetPreferenceString(this, "sSyncDate");
                if (sDateTime == null || sDateTime.equalsIgnoreCase("")) {
                    sDateTime = "1980-1-1 0:0:0";
                }
                try {
                    if (sDateTime.startsWith("1979-1-1") || sDateTime.startsWith("1979-01-01")) {
                        CommonDB.ResetDB();
                    }
                }catch(Exception eeeee){}
                HashMap<String, Object> oMap = new HashMap<String, Object>();
                oMap.put("CustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                oMap.put("SyncDate", sDateTime);
                CommonHelper.trackEvent(this, "Android Sync Start", oMap);
           /* ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
            lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
            lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));
            lsParams.add(new BasicNameValuePair("sDateTime", sDateTime));
            lsParams.add(new BasicNameValuePair("sDeviceInfo", CommonHelper.GetDeviceInfo()));*/

                HashMap<String, String> lsParams = new HashMap<String, String>();
                lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));

                lsParams.put("sDateTime", sDateTime);
                lsParams.put("sDeviceInfo", CommonHelper.GetDeviceInfo());


                JSONObject oJson = IF_SyncClient.PostRequest("/Sync/ServerApp_Action", lsParams);
                CommonDB.InsertLog(this, "Sync", "Init Request - " + CommonHelper.GetPreferenceString(this, "iCustomerID") + ", " + sDateTime);
                if (oJson != null && oJson.getBoolean("success")) {
                    PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), RECEIVING_DATA_MESSAGE);
                    oMap = new HashMap<String, Object>();
                    oMap.put("sURL", oJson.getString("sURL"));
                    CommonHelper.trackEvent(this, "Android Sync GetURL", oMap);
                    String sResult = DownloadFile(oJson.getString("sURL"));
                    if (sResult != null && sResult.length() > 0) {
                        CommonHelper.trackEvent(this, "Android Sync Download Success", null);
                        PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), "Processing Data ... ");
                        CommonDB.InsertLog(this, "Sync", "DownloadXML - " + oJson.getString("sFileName"));
                        ProcessingFile(sResult);
                        SyncSmartComments();
                        syncLatestUpdatedProducts();
                        syncPropertyLayouts();
                        PublishProgress(SYNC_SUCCESS, "Synced", "Synced Successful ... ");
                    } else {
                        CommonDB.InsertLog(this, "Sync", "Empty XML 1");
                        PublishProgress(SYNC_FAIL, "Synced", "No Data 1 ... ");
                    }
                } else {
                    oJson = IF_SyncClient.PostRequest("/Sync/ServerApp_Action", lsParams);
                    if (oJson != null && oJson.getBoolean("success")) {
                        PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), RECEIVING_DATA_MESSAGE);
                        String sResult = DownloadFile(oJson.getString("sURL"));
                        if (sResult != null && sResult.length() > 0) {
                            PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), "Processing Data ... ");
                            CommonDB.InsertLog(this, "Sync", "DownloadXML - " + oJson.getString("sFileName"));
                            ProcessingFile(sResult);
                            SyncSmartComments();
                            syncLatestUpdatedProducts();
                            syncPropertyLayouts();
                            PublishProgress(SYNC_SUCCESS, "Synced", "Synced Successful ... ");
                        } else {
                            CommonDB.InsertLog(this, "Sync", "Empty XML 2");
                            PublishProgress(SYNC_FAIL, "Synced", "No Data 2 ... ");
                        }
                    } else {
                        CommonDB.InsertLog(this, "Sync", "DownloadXML Fail");
                        if (oJson != null && oJson.has("message"))
                            PublishProgress(SYNC_FAIL, "Synced", oJson.optString("message", "Failed to Sync ..."));
                        else
                            PublishProgress(SYNC_FAIL, "Synced", "Failed to Sync ...");
                    }
                }
            }


        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.onHandleIntent", ex, this);
            Log.e("osama", "Sync Failed");
//            PublishProgress(SYNC_FAIL, "Synced", "Download Failed ... ");
        }


    }

    public static final byte[] intToByteArray(int value) {
        return new byte[] {
                (byte)(value >>> 24),
                (byte)(value >>> 16),
                (byte)(value >>> 8),
                (byte)value};
    }
    public void SyncSmartComments()
    {
        try {
            String sDateTime = CommonHelper.GetPreferenceString(this, "SC_Sync");
            if (sDateTime == null || sDateTime.equalsIgnoreCase("")) {
                sDateTime = "1980-1-1 0:0:0";
            }
            HashMap<String, Object> oMap = new HashMap<String, Object>();
            oMap.put("CustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            oMap.put("SyncDate", sDateTime);
            CommonHelper.trackEvent(this, "Android Smart Comments Sync Start", oMap);
           /* ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
            lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
            lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));
            lsParams.add(new BasicNameValuePair("sDateTime", sDateTime));*/
            HashMap<String, String> lsParams = new HashMap<String, String>();
            lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));

            lsParams.put("sDateTime", sDateTime);
            PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), "Prepare Smart Comments ...");
            JSONObject oJson = IF_SyncClient.PostRequest("/Sync/smartcomments_sync", lsParams);

            if (oJson != null && oJson.getBoolean("success")) {
                PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), RECEIVING_DATA_MESSAGE);
                String sDataTime = oJson.getString("SyncDate");
                CommonHelper.SavePreference(this, "SC_Sync", sDataTime);
                final JSONArray jArray = oJson.getJSONArray("lsReturn");
                try {
                    for (int i = 0; i < jArray.length(); i++) {
                        try {
                            JSONObject oOb = jArray.getJSONObject(i);
                            String downloadURL = oOb.getString("sDownloadURL");
                            String sS3Path = oOb.getString("sS3Path");
                            String sFileName = sS3Path.substring(sS3Path.indexOf("/")+1 );
                            IF_SyncClient.DownloadFile(downloadURL, sFileName);
                            //new DownloadFileFromURL().execute(downloadURL);
                        }catch(Exception exx) {
                            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.SyncSmartComments.For", exx, this);
                        }
                    }
                } catch (Exception e) {
                    ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.SyncSmartComments", e, this);
                }

                /*new Handler().post(new Runnable() {
                    @Override
                    public void run() {
                        try {
                            for (int i = 0; i < jArray.length(); i++) {
                                JSONObject oOb = jArray.getJSONObject(i);
                                String downloadURL = oOb.getString("sDownloadURL");
                                new DownloadFileFromURL().execute(downloadURL);
                            }
                        } catch (Exception e) {
                            ai_BugHandler.ai_Handler_Exception("Per", "if_signup.IF_RestClient.Runnable", e);
                        }
                    }
                });*/
            }
        }catch (Exception ex) {
            Log.e("Tag", String.valueOf(ex));
        }
    }
    private String DownloadFile(String sPath){
        try {
            String sResult = IF_SyncClient.DownloadData(sPath);
            return sResult;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.DownloadFile", ex, this);
        }
        return null;
    }
  /*  class DownloadFileFromURL extends AsyncTask<String, String, String> {


        @Override
        protected void onPreExecute() {
            super.onPreExecute();
        }

        @Override
        protected String doInBackground(String... f_url) {
            int count;
            try {
                URL url = new URL(f_url[0]);
                String[] splited = f_url[0].split("/");
                String filename = splited[splited.length - 1];
                splited = filename.split(".json");
                filename = splited[0] + ".json";

                URLConnection conection = url.openConnection();
                conection.connect();

                // this will be useful so that you can show a tipical 0-100%
                // progress bar
                int lenghtOfFile = conection.getContentLength();

                // download the file
                InputStream input = new BufferedInputStream(url.openStream(),
                        8192);

                // Output stream
                OutputStream output = new FileOutputStream(CommonHelper.sFileRoot
                       + "/" + filename);

                byte data[] = new byte[1024];

                long total = 0;

                while ((count = input.read(data)) != -1) {
                    total += count;
                    // publishing the progress....
                    // After this onProgressUpdate will be called
                    publishProgress("" + (int) ((total * 100) / lenghtOfFile));

                    // writing data to file
                    output.write(data, 0, count);
                }

                // flushing output
                output.flush();

                // closing streams
                output.close();
                input.close();

            } catch (Exception e) {
                Log.e("Error: ", e.getMessage());
            }

            return null;
        }


        protected void onProgressUpdate(String... progress) {
            // setting progress percentage
        }

        @Override
        protected void onPostExecute(String file_url) {
            // dismiss the dialog after the file was downloaded
        }
    }*/

    private void ProcessingFile(String sResult){
        IF_ProcessFile oProcessFile = new IF_ProcessFile(this);
        try{
            // saved
            String savedFolder = CommonJson.sFolder(this);
            String savedFolderPermission = CommonJson.sFolderPermission(this);
            oProcessFile.ProcessDownloadFile(sResult, this);
            // check if changed
            if (!savedFolder.equals(CommonJson.sFolder(this)) ||
                    !savedFolderPermission.equals(CommonJson.sFolderPermission(this))) {
                // clear selected asset view
                CommonHelper.SavePreference(this, Constants.Keys.kSelectedAssetViewID, "");
            }

            String sTemp = CommonHelper.GetPreferenceString(this, "bSubmit");
            if (sTemp != null && sTemp.equalsIgnoreCase("true")){
                //Submit Data Need Here
                SubmitData();
            }
        }catch (Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.ProcessingFile", ex, this);
        }
    }
    private void SubmitData_Force(boolean isExternalSync) {
        try {
            File rootFile = new File(CommonHelper.sFileRoot);

            File[] internalFiles = getFilesDir().listFiles();
            if (internalFiles != null) {
                for (File f: internalFiles) {
                    if ("json".equalsIgnoreCase(FileUtils.getExtension(f))) {
                        FileUtils.copyFile(f, rootFile, true);
                    }
                }
            }

            String sDBPath = copyDBToSDCard();

            // count files
            File[] files = rootFile.listFiles();
            if (files != null) {
                HashMap<String, Integer> groupedByExtension = new HashMap<>();
                for (File f : files) {
                    String extension = FileUtils.getExtension(f);
                    if (extension == null) continue;
                    int count = 0;
                    if (groupedByExtension.containsKey(extension)) {
                       count = groupedByExtension.get(extension);
                    }
                    groupedByExtension.put(extension, count + 1);
                }

                ArrayList<String> contents = new ArrayList<>();
                for (String key: groupedByExtension.keySet()) {
                    contents.add("." + key + " " + groupedByExtension.get(key) + " files need to submit");
                }
                String text = String.join("\n", contents);
                FileUtils.writeToTextFile("summary", text);
            }

            // prefs
            Map<String, ?> prefs = CommonHelper.getAllPreferences(this);
            FileUtils.writeToTextFile("prefs", prefs.toString());

            HashMap<String, String> lsParams = new HashMap<>();
            String sURL;
            if (isExternalSync) {
                sURL = "/SyncExternal/GetFolderToken";
                lsParams.put("sID", "G8MWqXvicHGxrZjmEFnUML2L5LtTVo2plMOPla1I9EI6RdPVUkOLci8L");
            } else {
                sURL = "/Sync/GetFolderToken";
                lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
            }

            PublishProgress(SYNC_PROGRESS, "Submit Data", "Preparing ... ");
            JSONObject oReturn = IF_SyncClient.PostRequest(sURL, lsParams);
            if (oReturn != null) {
                String sKey = oReturn.getString("sAccessKey");
                String sSecrect = oReturn.getString("sSecretKey");
                String sToken = oReturn.getString("sToken");
                String sServerFolder = oReturn.getString("sFolder");
                String sBucket = oReturn.getString("sBucket");
                ClientConfiguration oConfig = new ClientConfiguration();
                //oConfig.setSocketTimeout(8000000);
                //oConfig.setConnectionTimeout(8000000);
                oConfig.setMaxConnections(10);
                oConfig.setMaxErrorRetry(10);
                oConfig.setProtocol(Protocol.HTTPS);
                AmazonS3 oClient = new AmazonS3Client(new BasicSessionCredentials(sKey, sSecrect, sToken), oConfig);
                SubmitFile(sKey, sSecrect, sToken, sServerFolder, sDBPath, oClient, 1, sBucket);
                try {
                    int numOfUploading = 1;
                    File[] lsFile = new File(CommonHelper.sFileRoot).listFiles();
                    if (lsFile != null && lsFile.length > 0) {
                        for (File file : lsFile) {
                            if (!file.getAbsolutePath().equalsIgnoreCase(sDBPath)) {
                                numOfUploading += 1;
                                SubmitFile(sKey, sSecrect, sToken, sServerFolder,
                                        file.getAbsolutePath(), oClient, numOfUploading, sBucket);
                            }
                        }
                    }

                    File[] tmpFiles = new File(CommonHelper.sFileRoot + Constants.Paths.tempFolder).listFiles();
                    if (tmpFiles != null && tmpFiles.length > 0) {
                        for (File f: tmpFiles) {
                            numOfUploading += 1;
                            SubmitFile(sKey, sSecrect, sToken, sServerFolder + Constants.Paths.tempFolder,
                                    f.getAbsolutePath(), oClient, numOfUploading, sBucket);
                        }
                    }
                } catch (Exception exx) {

                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.SubmitData", ex, this);
        }
    }

    private void SubmitData() {
        try {
            String sDBPath = copyDBToSDCard();
           /* ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
            lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
            lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));*/
            HashMap<String, String> lsParams = new HashMap<String, String>();
            lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
            lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));

            PublishProgress(SYNC_PROGRESS, "Submit Data", "Preparing ... ");
            JSONObject oReturn = IF_SyncClient.PostRequest("/Sync/GetFolderToken", lsParams);
            if (oReturn != null) {
                String sKey = oReturn.getString("sAccessKey");
                String sSecrect = oReturn.getString("sSecretKey");
                String sToken = oReturn.getString("sToken");
                String sServerFolder = oReturn.getString("sFolder");
                String sBucket = oReturn.getString("sBucket");
                ClientConfiguration oConfig = new ClientConfiguration();
                //oConfig.setSocketTimeout(8000000);
                //oConfig.setConnectionTimeout(8000000);
                oConfig.setMaxConnections(10);
                oConfig.setMaxErrorRetry(10);
                oConfig.setProtocol(Protocol.HTTPS);
                AmazonS3 oClient = new AmazonS3Client(new BasicSessionCredentials(sKey, sSecrect, sToken), oConfig);
                SubmitFile(sKey, sSecrect, sToken, sServerFolder, sDBPath, oClient, 1, sBucket);

                try {
                    File[] lsFile = new File(CommonHelper.sFileRoot).listFiles();
                    if (lsFile != null && lsFile.length > 0) {
                        for (int i = 0; i < lsFile.length; i++) {
                            if (!lsFile[i].getAbsolutePath().equalsIgnoreCase(sDBPath)) {
                                SubmitFile(sKey, sSecrect, sToken, sServerFolder, lsFile[i].getAbsolutePath(), oClient, i + 2, sBucket);
                            }
                        }
                    }
                } catch (Exception ex) {

                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.SubmitData", ex, this);
        }
    }
    private void SubmitFile(String sKey, String sSecrect, String sToken, String sServerFolder,
                            String sDBPath,AmazonS3 oClient, int iCount, String sBucket){
        try {
            File oFile = new File(sDBPath);
            String sBucketName = sBucket;
            String sS3URL =  sServerFolder + "/" + oFile.getName();
            if (oFile.exists() && (!oFile.isDirectory())) {

                PublishProgress(SYNC_TOASTMESSAGE, "", "Submit File " + oFile.getName());
                final String sMss = "Submit Data File " + (iCount) + " - ";
                if (oFile.length() < 52428800) {
                    final long lFileLength = oFile.length();
                    //TransferManager oManager = new TransferManager(oClient);

                    PutObjectRequest oRequest = new PutObjectRequest(sBucketName, sS3URL, oFile);
                    oRequest.setCannedAcl(CannedAccessControlList.Private);
                    oRequest.setGeneralProgressListener(new ProgressListener() {
                        @Override
                        public void progressChanged(ProgressEvent progressEvent) {
                            PublishProgress(SUBMIT_PROCESS, "", sMss + String.format("%.2f", (((float)progressEvent.getBytesTransferred())/(float)lFileLength)) + "%");

                        }
                    });
                    oClient.putObject(oRequest);

                   /* final Upload oUpload = oManager.upload(oRequest);


                    oUpload.addProgressListener(new ProgressListener() {
                        @Override
                        public void progressChanged(ProgressEvent progressEvent) {

                            PublishProgress(SUBMIT_PROCESS, "", sMss + String.format("%.2f", oUpload.getProgress().getPercentTransferred()) + "%");

                        }
                    });
                    oUpload.waitForCompletion();*/
                }
                else{
                    long iFilePosition = 0;
                    long iPartSize = 5242880;
                    long iContentLength = oFile.length();

                    InitiateMultipartUploadRequest oRequest = new InitiateMultipartUploadRequest(sBucketName, sS3URL);
                    oRequest.setCannedACL(CannedAccessControlList.PublicRead);

                    InitiateMultipartUploadResult oResult = oClient.initiateMultipartUpload(oRequest);
                    ArrayList<PartETag> lsTag = new ArrayList<>();
                    for (int i=1; iFilePosition < iContentLength; i++){
                        iPartSize = Math.min(iPartSize, iContentLength - iFilePosition);
                        UploadPartRequest oTempRequest = new UploadPartRequest().withBucketName(sBucketName).withKey(sS3URL).withUploadId(oResult.getUploadId())
                                .withPartNumber(i).withFileOffset(iFilePosition).withFile(oFile).withPartSize(iPartSize);
                        boolean bPartSuccess = false;
                        while (!bPartSuccess){
                            if (lsTag.add(oClient.uploadPart(oTempRequest).getPartETag())){
                                bPartSuccess = true;
                                float sPercetange = ((float)iFilePosition / (float)iContentLength) * 100;
                                PublishProgress(SUBMIT_PROCESS, "", sMss + String.format("%.2f", sPercetange) + "%");
                                iFilePosition = iFilePosition + iPartSize;
                            }
                            else{
                                bPartSuccess = false;
                            }
                        }
                    }
                    CompleteMultipartUploadRequest oCompleteRequest = new CompleteMultipartUploadRequest(sBucketName, sS3URL, oResult.getUploadId(), lsTag);

                    CompleteMultipartUploadResult oFinalReport = oClient.completeMultipartUpload(oCompleteRequest);

                }
            }
            else {
                CommonDB.InsertLog(this, "Submit Data", "No File " + oFile.getAbsolutePath() + " S3URL " + sS3URL);
            }
        } catch (Exception ex) {
            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.SendFile", ex);
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.SendFile", ex, this);
            CommonDB.InsertLog(this, "Submit Data", "Failed Exception ");
        }
    }
    public String copyDBToSDCard() {
        try {
            InputStream myInput = new FileInputStream(this.getDatabasePath("if_data.db"));
            String sDBPath = CommonHelper.sFileRoot + "/abccd.db";
            File file = new File(sDBPath);
            if (!file.exists()){
                try {
                    file.createNewFile();
                } catch (IOException e) {
                    Log.i("FO","File creation failed for " + file);
                }
            }

            OutputStream myOutput = new FileOutputStream(sDBPath);

            byte[] buffer = new byte[1024];
            int length;
            while ((length = myInput.read(buffer))>0){
                myOutput.write(buffer, 0, length);
            }

            //Close the streams
            myOutput.flush();
            myOutput.close();
            myInput.close();
            Log.i("FO","copied");
            return sDBPath;

        } catch (Exception e) {
            Log.i("FO","exception="+e);
            return null;
        }
    }

    private void syncLatestUpdatedProducts() {
        // check if product feature is enabled
        if (!CommonJson.isEnabledProduct(this)) return;

        // sync products
        PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), "Prepare Products ...");
        CommonProduct.syncAndSaveAllProduct(this, this::onProgress);
        PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), RECEIVING_DATA_MESSAGE);
    }

    private void syncPropertyLayouts() {
        // sync property layouts
        PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), "Prepare Asset Layouts ...");
        CommonPropertyLayout.syncAndSaveAllPropertyLayouts(
            this, Constants.Limits.PROPERTY_LAYOUT_SYNC_LIMIT, this::onProgress
        );
        PublishProgress(SYNC_PROGRESS, getString(R.string.syncing), RECEIVING_DATA_MESSAGE);
    }

    private void onProgress(int action, String message, String progress) {
        PublishProgress(SYNC_PROGRESS, message, progress);
    }
}
