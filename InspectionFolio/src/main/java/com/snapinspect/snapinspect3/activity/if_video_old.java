package com.snapinspect.snapinspect3.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.graphics.*;
import android.hardware.Camera;
import android.hardware.*;
import android.location.Location;
import android.media.AudioManager;
import android.media.MediaRecorder;
import android.net.Uri;
import android.os.*;
import android.util.Log;
import android.util.Size;
import android.view.*;
import android.view.ScaleGestureDetector.SimpleOnScaleGestureListener;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.squareup.picasso.Picasso;

import java.io.File;
import java.io.IOException;
import java.lang.invoke.ConstantCallSite;
import java.lang.ref.WeakReference;
import java.util.*;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;

import static com.snapinspect.snapinspect3.Helper.Constants.Values.*;

public class if_video_old extends Activity
        implements SensorEventListener, MediaRecorder.OnInfoListener, Camera.PreviewCallback {

    private static final String CLASS_LABEL = "if_video";
    private static final String TAG = "if_video";

    private enum RecordingState {
        RECORDING, STOPPED
    }

    private VideoQuality videoQuality = Constants.Values.DEFAULT_VIDEO_QUALITY;
    //setting value

    private boolean mRecording = false;
    private boolean isPreviewOn = false;
    private int defaultCameraId = -1;
    private int defaultScreenResolution = -1;
    private int cameraSelection = 0;

    private Camera mCamera;
    private RelativeLayout preview;
    private CameraView mPreview;
    private Camera.Parameters param;

    private SensorManager mSensorManager;
    private Sensor mOrientation;

    private boolean bFlash = false;
    private boolean bAutoFocus = false;
    private boolean bManualFocus;
    private BroadcastReceiver mReceiver = null;
    private boolean isFlashOn = false;

    private Button shutterBtn, switchBtn, lightBtn, focusBtn;
    private ImageView previewImageView;
    private TextView timeLab, blinkLabel;
    private DrawingView drawingView;
    private TextView zoomLevelTextView;

    private long iInsItemID, iNoticeID;
    private int iVideoLength;
    private MediaRecorder mMediaRecorder;
    private SurfaceHolder mHolder;

    private Animation blkAnimation;
    private PowerManager.WakeLock mWakeLock;

    private Timer timer;
    private TimerTask timerTask;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private RecordingState mRecordingState = RecordingState.STOPPED;
    private String sTempFilePath;
    // The video duration for the current recording
    private long mCurrentRecordingStartTime = 0;
    private float latestZoomValue = 0;

    public static Intent newIntent(Context context, long iNoticeID, long iInsItemID, int iVideoLength) {
        Intent intent = new Intent(context, if_video_old.class);
        intent.putExtra(Constants.Extras.iNoticeID, iNoticeID);
        intent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        intent.putExtra(Constants.Extras.iVideoLength, iVideoLength);
        return intent;
    }

    @SuppressLint("InvalidWakeLockTag")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        setContentView(R.layout.activity_if_video_recorder_old);
        getActionBar().hide();
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        getWindow().setFormat(PixelFormat.TRANSLUCENT);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
        mWakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK, CLASS_LABEL);
        mWakeLock.acquire();

        iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, 0);
        iNoticeID = getIntent().getLongExtra(Constants.Extras.iNoticeID, 0);
        iVideoLength = getIntent().getIntExtra(Constants.Extras.iVideoLength, 300000);

        initialize();
        setLayout();
        enableZoom();

        CommonValidate.Permission_Validate(this);
        // request location permission
        CommonValidate.requestPermissions(this, Constants.LOCATION_PERMISSIONS);
        // request write_external_storage permission if needed
        CommonValidate.validateWriteExternalStoragePermission(this, 0);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constants.RequestCodes.iVideoClips
                && resultCode == if_VideoClips.RESULT_START_MERGE) {
            startMerge();
        }
    }

    public void setLayout() {
        int sHeight = getWindowManager().getDefaultDisplay().getHeight();
        int sWidth = getWindowManager().getDefaultDisplay().getWidth();

        RelativeLayout topView = findViewById(R.id.top_view);
        RelativeLayout botView = findViewById(R.id.bottom_view);
        RelativeLayout.LayoutParams paramsTop = (RelativeLayout.LayoutParams) topView.getLayoutParams();
        RelativeLayout.LayoutParams paramsBot = (RelativeLayout.LayoutParams)botView.getLayoutParams();

        bAutoFocus = getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA_AUTOFOCUS);
        bManualFocus = CommonHelper.GetPreferenceBoolean(if_video_old.this, "bVideoFocus");

        if (bAutoFocus) {
            drawingView = new DrawingView(this);
            RelativeLayout.LayoutParams layoutParamsDrawing = new RelativeLayout.LayoutParams(WRAP_CONTENT, MATCH_PARENT);
            layoutParamsDrawing.setMargins(paramsTop.leftMargin + paramsTop.width + sHeight, 0, sWidth - paramsBot.width - paramsTop.width, 0);
            drawingView.setLayoutParams(layoutParamsDrawing);
            this.addContentView(drawingView, layoutParamsDrawing);
        }
    }

    @SuppressLint("ClickableViewAccessibility")
    private void enableZoom() {
        if (mCamera == null) return;
        Camera.Parameters params = mCamera.getParameters();
        if (!params.isZoomSupported()) return;
        latestZoomValue = Math.max(params.getZoom(), 1);

        ScaleGestureDetector scaleGestureDetector = new ScaleGestureDetector(this, new SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                float value = latestZoomValue * detector.getScaleFactor();
                float maxZoom = Math.min(
                     params.getMaxZoom(), Constants.Limits.iMaxiumVideoLevel
                );
                if (value < 1) value = 1;
                else if (value > maxZoom) value = maxZoom;
                updateCameraZoom(value);
                latestZoomValue = value;
                return true;
            }
        });

        preview.setOnTouchListener((v, event) -> {
            scaleGestureDetector.onTouchEvent(event);
            return true;
        });
    }

    public void initialize() {
        AudioManager audioManager;
        mSensorManager = (SensorManager) getSystemService(Context.SENSOR_SERVICE);
        mOrientation = mSensorManager.getDefaultSensor(Sensor.TYPE_ORIENTATION);
        preview = findViewById(R.id.camera_view);

        Button doneBtn = findViewById(R.id.doneBtn);
//        doneBtn.setRotation(-90);
        switchBtn = findViewById(R.id.switchBtn);
//        switchBtn.setRotation(-90);
        lightBtn = findViewById(R.id.lightBtn);
//        lightBtn.setRotation(-90);
        focusBtn = findViewById(R.id.focusBtn);
        focusBtn.setVisibility(View.GONE);
        previewImageView = findViewById(R.id.previewImageView);

        timeLab = findViewById(R.id.timeLab);
//        timeLab.setRotation(-90);
        blinkLabel = findViewById(R.id.blinkLabel);
        blinkLabel.setVisibility(View.GONE);
        audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
        shutterBtn = findViewById(R.id.shutterBtn);
//        shutterBtn.setRotation(-90);
        shutterBtn.setOnClickListener(view -> {
            if (!CommonUI.bAppPermission(if_video_old.this)){
                return;
            }
            view.setEnabled(false);
            switch (mRecordingState) {
                case RECORDING:
                    stopVideoRecording();
                    break;
                case STOPPED: {
                    if (null != mMediaRecorder || SetupMediaRecorder()) {
                        startVideoRecording();
                    }
                }
                break;
            }
            view.setEnabled(true);
        });
        zoomLevelTextView = findViewById(R.id.zoomLevelText);

        updatePreviewThumbnail();
        initCamera();

        // Set up duration
        timeLab.setText(DateUtils.clockStringFromMilliseconds(getTotalVideoDuration()));
    }
    public void startTimer() {
        timer = new Timer();
        initializeTimerTask();
        timer.schedule(timerTask, 0, 1000);
    }

    public void stopTimerTask() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    public void initializeTimerTask() {
        mCurrentRecordingStartTime = System.currentTimeMillis();
        timerTask = new TimerTask() {
            public void run() {
                handler.post(() -> updateTimeTextView());
            }
        };
    }

    private void updateTimeTextView() {
        long currentDuration = System.currentTimeMillis() - mCurrentRecordingStartTime;
        String sTime = DateUtils.clockStringFromMilliseconds(getTotalVideoDuration() + currentDuration);
        Log.d(TAG, "Current Duration: " + sTime);
        timeLab.setText(sTime);
    }

    private void updatePreviewThumbnail() {
        List<ai_Video> videoClips = getVideoClips();
        if (videoClips.isEmpty()) return;
        ai_Video recentVideo = videoClips.get(videoClips.size() - 1);
        Picasso.get()
                .load(new File(recentVideo.getThumb()))
                .into(previewImageView);
    }

    private void startVideoRecording() {
        try {
            mRecording = true;
            shutterBtn.setBackgroundResource(R.drawable.record_stop);
            previewImageView.setEnabled(false);
            startTimer();

            if (mMediaRecorder != null) {
                mMediaRecorder.start();
            }
            mRecordingState = RecordingState.RECORDING;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    private void stopVideoRecording() {
        try {
            stopTimerTask();
            mRecording = false;
            mRecordingState = RecordingState.STOPPED;

            mCamera.lock();
            if (mMediaRecorder != null) {
                mMediaRecorder.stop();
                saveVideoRecord();
            }
            mCamera.unlock();

            releaseMediaRecorder();

            if (mCamera != null) {
                mCamera.reconnect();
                mCamera.startPreview();
                mCamera.setPreviewCallback(this);
            }

            previewImageView.setEnabled(true);
            shutterBtn.setBackgroundResource(R.drawable.record_start);
            updatePreviewThumbnail();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private boolean SetupMediaRecorder(){
        try{
            mMediaRecorder = new MediaRecorder();

            Camera.Parameters oParams = mCamera.getParameters();
            List<String> focusModes = oParams.getSupportedFocusModes();
            if (focusModes.contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO)) {
                oParams.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO);
            } else if (focusModes.contains(Camera.Parameters.FOCUS_MODE_AUTO)) {
                oParams.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
            }
            //List<Camera.Size> lsSize = oParams.getSupportedPictureSizes();

            //oParams.setPictureSize(640, 480);
            Size resolution = videoQuality.getResolution();
            oParams.setPreviewSize(resolution.getWidth(), resolution.getHeight());
            mCamera.setParameters(oParams);
            List<Camera.Size> supportedPictureSizes = oParams.getSupportedPictureSizes();
            //String nbb= "cc";

            mCamera.unlock();
            mMediaRecorder.setCamera(mCamera);
            mMediaRecorder.setAudioSource(MediaRecorder.AudioSource.CAMCORDER);
            //User videosource.camera will break certain device . for ex Galaxy Tab Lite 3
            // mMediaRecorder.setVideoSource(MediaRecorder.VideoSource.CAMERA);
            mMediaRecorder.setVideoSource(MediaRecorder.VideoSource.DEFAULT);
            mMediaRecorder.setOutputFormat(MediaRecorder.OutputFormat.MPEG_4);
            mMediaRecorder.setVideoEncodingBitRate(videoQuality.getBitRate());
            
            // Set frame rate
            mMediaRecorder.setVideoFrameRate(videoQuality.getFrameRateRange().getUpper());

            mMediaRecorder.setMaxDuration((int) (iVideoLength - getTotalVideoDuration()));
            mMediaRecorder.setAudioEncoder(MediaRecorder.AudioEncoder.AAC);

            mMediaRecorder.setVideoEncoder(MediaRecorder.VideoEncoder.H264);
            mMediaRecorder.setAudioChannels(AUDIO_CHANNELS);
            mMediaRecorder.setAudioSamplingRate(AUDIO_SAMPLING_RATE);
            mMediaRecorder.setAudioEncodingBitRate(AUDIO_BITRATE);
            mMediaRecorder.setVideoSize(
                videoQuality.getResolution().getWidth(),
                videoQuality.getResolution().getHeight()
            );
            mMediaRecorder.setPreviewDisplay(mPreview.getHolder().getSurface());
            mMediaRecorder.setOnInfoListener(if_video_old.this);

            sTempFilePath = getTempClipVideoPath();
            mMediaRecorder.setOutputFile(sTempFilePath);

            try {
                mMediaRecorder.prepare();
            } catch (IllegalStateException e) {
                releaseMediaRecorder();
                return false;
            } catch (IOException e) {
                releaseMediaRecorder();
                return false;
            }
            return true;
        }catch (Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_video.SetupMediaRecorder", ex, this);
        }
        return false;
    }

    private void releaseMediaRecorder(){
        try {
            if (mMediaRecorder != null) {
                mMediaRecorder.reset();   // clear recorder configuration
                mMediaRecorder.release(); // release the recorder object
                mMediaRecorder = null;
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_video.releaseMediaRecorder", ex, this);
        }
    }

    public void startBlinkingAnimation() {
        if (validateVideoPermission()) {
            if (blinkLabel.getVisibility() == View.GONE) {
                blinkLabel.setVisibility(View.VISIBLE);
                blinkLabel.setText(R.string.video_recording_orientation_alert);
                blkAnimation = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.blinking_animation);
                blinkLabel.startAnimation(blkAnimation);
            }
        }
    }

    public void stopBlinkAnimation() {
        if (blkAnimation != null && blinkLabel.getVisibility() == View.VISIBLE) {

            blkAnimation.cancel();
            blkAnimation.reset();

            blkAnimation = null;
            blinkLabel.setVisibility(View.GONE);
            blinkLabel.clearAnimation();
        }
    }

    public void initCamera() {
        try {
            if (mRecording) stopVideoRecording();

            if (preview.getChildCount() > 0)
                preview.removeAllViews();

            setCamera();

            if (mCamera != null && mPreview != null) {

                mPreview.setLayoutParams(new RelativeLayout.LayoutParams(MATCH_PARENT, MATCH_PARENT));

                preview.addView(mPreview);
              /*  preview.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        mCamera.autoFocus(null);
                    }
                });*/
                mPreview.setKeepScreenOn(true);
                bFlash = getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA_FLASH);
                if (Build.MODEL.equalsIgnoreCase("Nexus 7")) {
                    bFlash = false;
                }

                if (!bFlash) {
                    lightBtn.setVisibility(View.GONE);
                } else {
                    isFlashOn = CommonHelper.GetPreferenceBoolean(if_video_old.this, "bVideoFlash");
                    if (isFlashOn)
                        setFlashOn();
                    else
                        setFlashOff();
                }
            }
        } catch (Exception e) {

        }
    }

    public void doneClicked(View v) {
        if (mRecording) stopVideoRecording();

        if (getVideoClips().size() > 1) {
            startMerge();
        } else {
            didFinishMergingVideos();
            finish();
        }
    }

    public void previewClicked(View v) {
        Intent intent = if_VideoClips.newIntent(this, iInsItemID, iNoticeID);
        startActivityForResult(intent, Constants.RequestCodes.iVideoClips);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {

        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return false;
        }

        return super.onKeyDown(keyCode, event);
    }

    private boolean setCamera() {
        try {
            // Find the total number of cameras available
            int numberOfCameras = Camera.getNumberOfCameras();
            // Find the ID of the default camera
            Camera.CameraInfo cameraInfo = new Camera.CameraInfo();
            for (int i = 0; i < numberOfCameras; i++) {
                Camera.getCameraInfo(i, cameraInfo);
                if (cameraInfo.facing == cameraSelection) {
                    defaultCameraId = i;
                }
            }

            if (mPreview != null) {
                mPreview.stopPreview();
            }

            if (mCamera != null) {
                mCamera.release();
                mCamera = null;
            }

            if (defaultCameraId >= 0)
                mCamera = Camera.open(defaultCameraId);
            else
                mCamera = Camera.open();

            mPreview = new CameraView(this, mCamera, 0);

            handleSurfaceChanged();
        } catch (Exception e) {
            Log.e(TAG, String.valueOf(e));
        }

        return true;
    }

   /* @Override
    public boolean onTouchEvent(MotionEvent event) {

        if (!bAutoFocus || cameraFront) return false;

        float x = event.getX();
        float y = event.getY();
        if (x >top_margin && x < bottom_margin) return false;

        if(event.getAction() == MotionEvent.ACTION_DOWN){

            if (!focusStart) {
                try{
                    focusStart = true;
                    try {
                        mCamera.autoFocus(myAutoFocusCallback);
                    } catch (Exception ex){
                        focusStart = false;
                        return false;
                    }

                    Display display = getWindowManager().getDefaultDisplay();
                    if ((int) (x - 50) <= 0)
                        x = 153;
                    if ((int) (x + 250) >= display.getWidth())
                        x = display.getWidth() - 253;

                    if ((int) (y - 50) <= 0)
                        y = 153;

                    if ((int) (y + 250) >= display.getHeight())
                        y = display.getHeight() - 253;

                    Rect rect = new Rect((int) x-100, (int) y-100, (int) (x + 100), (int) (y + 100));

                    drawingView.setHaveTouch(true, rect);
                    drawingView.setFocusStart(true);
                    drawingView.setAnimationStep(1);
                    drawingView.invalidate();

                }catch (Exception e){
                    focusStart = false;
                }
            }
        }
        return super.onTouchEvent(event);
    }*/

    private void startMerge() {
        AsyncTask<String, Integer, O_FileName> task = new MergeVideo(
                this,
                getMainVideoClip(),
                getVideoClips(),
                oFileName -> didFinishMergingVideos()
        );
        task.execute();
    }

    private void setFlashOff(){
        lightBtn.setBackgroundResource(R.drawable.light_off);

        isFlashOn = false;
        param.setFlashMode(Camera.Parameters.FLASH_MODE_OFF);

        CommonHelper.SavePreferenceBoolean(this, "bVideoFlash", false);
    }
    private void setFlashOn(){
        lightBtn.setBackgroundResource(R.drawable.light_on);

        isFlashOn = true;
        param.setFlashMode(Camera.Parameters.FLASH_MODE_TORCH);
        CommonHelper.SavePreferenceBoolean(this, "bVideoFlash", true);
    }

    public void lightClicked(View v) {
        try {
            if (isFlashOn) {
                setFlashOff();
            } else {
                setFlashOn();
            }
            mCamera.setParameters(param);
        } catch (Exception ex) {
                ex.printStackTrace();
        }
    }
    public void focusClicked(View v) {
        if (!bAutoFocus) {
            return;
        }

        if (bManualFocus) {
            bManualFocus = false;
            focusBtn.setBackgroundResource(R.drawable.a_focus);
        } else {
            bManualFocus = true;
            focusBtn.setBackgroundResource(R.drawable.m_focus);
        }

        CommonHelper.SavePreferenceBoolean(if_video_old.this, "bVideoFocus", bManualFocus);
    }

    public void switchClicked(View v) {
        try {
//            if (!has2Camera) {
//                switchBtn.setEnabled(false);
//                return;
//            }

            cameraSelection = (cameraSelection == Camera.CameraInfo.CAMERA_FACING_BACK) ?
                    Camera.CameraInfo.CAMERA_FACING_FRONT : Camera.CameraInfo.CAMERA_FACING_BACK;
            initCamera();

            if (cameraSelection == Camera.CameraInfo.CAMERA_FACING_FRONT)
                lightBtn.setVisibility(View.GONE);
            else {
                lightBtn.setVisibility(View.VISIBLE);
                if (isFlashOn) {
                    param.setFlashMode(Camera.Parameters.FLASH_MODE_TORCH);
                    mCamera.setParameters(param);
                }
            }

            switchBtn.setBackgroundResource(R.drawable.switch_clicked);

            Handler handler = new Handler();
            handler.postDelayed(new Runnable() {
                public void run() {
                    switchBtn.setBackgroundResource(R.drawable.swich);
                }
            }, 200);
        }catch(Exception ex){

        }
    }

    @SuppressLint({"SetTextI18n", "InvalidWakeLockTag"})
    @Override
    protected void onResume() {
        super.onResume();

        if (!validateVideoPermission()) {
            blinkLabel.setVisibility(View.VISIBLE);
            blinkLabel.setText(R.string.allow_permission);
        } else {
            blinkLabel.setVisibility(View.GONE);
        }

        mSensorManager.registerListener(this, mOrientation, SensorManager.SENSOR_DELAY_NORMAL);
        mSensorManager.registerListener(this,
                mSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER), SensorManager.SENSOR_DELAY_FASTEST);

        if (mWakeLock == null) {
            PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
            mWakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK, CLASS_LABEL);
            mWakeLock.acquire();
        }
        initCamera();
        updateCameraZoom(latestZoomValue);
        GeoLocationManager.getInstance(this).startUpdateLocation();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (drawingView != null) {
            drawingView.setFocusStart(false);
            drawingView.setFocusResult(false);
            drawingView.setAnimationStep(4);
        }
        mSensorManager.unregisterListener(this);
        if (mWakeLock != null) {
            mWakeLock.release();
            mWakeLock = null;
        }

        if (mRecording) stopVideoRecording();
        //when on Pause, release camera in order to be used from other applications
//        releaseCamera();

        GeoLocationManager.getInstance(this).stopUpdateLocation();
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        float azimuth_angle = event.values[0];
        float pitch_angle = event.values[1];
        float roll_angle = event.values[2];
        float angle = 0;

        if (event.sensor.getType() == Sensor.TYPE_ORIENTATION) {
            // Do something with these orientation angles.
            if (-180 < pitch_angle && pitch_angle < -10 && 45 > roll_angle && roll_angle > -45) {
                // angle :around 0 degree
                angle = -90;
                rotateControll(angle);
                startBlinkingAnimation();
            } else if (45 > pitch_angle && pitch_angle > -45 && -90 < roll_angle && roll_angle < -10) {
                // andgle : around 90 degree
                angle = 180;
                rotateControll(angle);
                startBlinkingAnimation();
            } else if (180 > pitch_angle && pitch_angle >10 && 45 > roll_angle && roll_angle > -45) {
                //agnle : around 180 degree
                angle = 90;

                rotateControll(angle);

                startBlinkingAnimation();
            } else if (45 > pitch_angle && pitch_angle > -45 && 90 > roll_angle && roll_angle >10) {
                angle = 0;

                rotateControll(angle);
                stopBlinkAnimation();
            }
        }
//        //auto focus
//        Log.e("osama", "onsenese --");
//        if (event.sensor.getType() == Sensor.TYPE_ACCELEROMETER){
//            if (!cameraFront) {
//                Log.e("osama", "getAccelerometer");
//                getAccelerometer(event);
//
//            }
//        }
    }
//    private void getAccelerometer (final SensorEvent sensorEvent){
//        if (bManualFocus) return;
//
//        if (mCamera == null || !bAutoFocus){
//            return;
//        }
//
//        final float[] values = sensorEvent.values;
//        final float x = values[0];
//        final float y = values[1];
//        final float z = values[2];
//
//        final float movement = (x * x + y * y + z * z)
//                / (SensorManager.GRAVITY_EARTH * SensorManager.GRAVITY_EARTH);
//        if (movement >= THRESHOLD && !focusStart) {
//            try{
//                focusStart = true;
//                try {
//                    mCamera.autoFocus(myAutoFocusCallback);
//                }catch(Exception ex){
//                    focusStart = false;
//                    return;
//                }
//                Display display = getWindowManager().getDefaultDisplay();
//                int left = display.getHeight() / 2;
//                int top = display.getHeight() / 2 - 100;
//                Rect rect = new Rect(left, top, left+200, top+200);
//                drawingView.setHaveTouch(true, rect);
//                drawingView.setFocusStart(true);
//                drawingView.setAnimationStep(1);
//                drawingView.invalidate();
//            }catch (Exception e){
//                focusStart = false;
////                shutterBtn.setEnabled(true);
//            }
//        }
//    }
    Camera.AutoFocusCallback myAutoFocusCallback = new Camera.AutoFocusCallback(){
        @Override
        public void onAutoFocus(boolean arg0, Camera arg1) {
            // TODO Auto-generated method stub
            drawingView.setFocusStart(false);
            drawingView.setStartTime(System.currentTimeMillis());

            if (arg0) {
                drawingView.setFocusResult(true);
                drawingView.setAnimationStep(4);
            }
            else{
                drawingView.setFocusResult(false);
                drawingView.setAnimationStep(4);
            }

            drawingView.invalidate();
        }};

    public void rotateControll (float angle) {
        return;
//        Button doneBtn = (Button) findViewById(R.id.doneBtn);
//        doneBtn.setRotation(angle);
//        switchBtn.setRotation(angle);
//        lightBtn.setRotation(angle);
//        timeLab.setRotation(angle);
//        shutterBtn.setRotation(angle);
    }


    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
    }

    private boolean hasCamera(Context context) {
        //check if the device has camera
        return context.getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA);
    }
//
    private void releaseCamera() {
        // stop and release camera
        mPreview.getHolder().removeCallback(mPreview);
        if (mCamera != null) {
            mCamera.release();
            mCamera = null;
        }
    }

    @Override
    public void onInfo(MediaRecorder mediaRecorder, int what, int i1) {
        try {
            if (what == MediaRecorder.MEDIA_RECORDER_INFO_MAX_DURATION_REACHED) {
                doneClicked(null);
            }

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_signup_2.onInfo", ex, this);
        }
    }

    private void updateZoomLevel(float zoomValue) {
        zoomLevelTextView.setText(getString(R.string.camera_zoom_level_value, String.format("%.1f", zoomValue)));
    }

    private void updateCameraZoom(float value) {
        if (mCamera == null) return;
        try {
            Camera.Parameters params = mCamera.getParameters();
            if (params.isSmoothZoomSupported()) {
                mCamera.startSmoothZoom((int) value);
                mCamera.setZoomChangeListener(new Camera.OnZoomChangeListener() {
                    @Override
                    public void onZoomChange(int zoomValue, boolean stopped, Camera camera) {
                        updateZoomLevel(zoomValue);
                    }
                });
            } else {
                param.setZoom((int) value);
                mCamera.setParameters(param);
                updateZoomLevel(value);
            }
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_video.updateCameraZoom", e, this);
        }
    }

    public String getTempClipVideoPath() {
        return String.format("%s%s/%s.mp4",
                CommonHelper.sFileRoot, Constants.Paths.tempFolder, CommonHelper.GetTimeString());
    }

    private long getTotalVideoDuration() {
        long totalDuration = 0;
        for (ai_Video video : getVideoClips()) {
            totalDuration += CommonHelper.getVideoDuration(this, video.getFile());
        }
        return totalDuration;
    }
    private List<ai_Video> getVideoClips() {
        return iNoticeID > 0 ? CommonDB.GetNoticeVideos(iNoticeID, iInsItemID) : CommonDB.GetInsItemVideos(iInsItemID);
    }

    private ai_Video getMainVideoClip() {
        return iNoticeID > 0 ? CommonDB.GetNoticeMainVideo(iNoticeID, iInsItemID) : CommonDB.GetInsItemMainVideo(iInsItemID);
    }

    @Override
    public void onPreviewFrame(byte[] bytes, Camera camera) {

    }

    private class DrawingView extends View {
        boolean focusstart;
        boolean focusresult;
        Paint drawingPaint;
        boolean havetouch;
        RectF roundingRect = null;
        float cornerX = (float)10;
        float cornerY = (float)10;
        int animationStep ;
        long animationDuration = 500;
        long startTime;

        public DrawingView(Context context) {
            super(context);
            focusstart = false;
            havetouch = false;
            focusresult = false;
            animationStep = 0;

            drawingPaint = new Paint();
            drawingPaint.setColor(Color.TRANSPARENT); //Color.WHITE
            drawingPaint.setStyle(Paint.Style.STROKE);
            drawingPaint.setStrokeWidth(4);
        }

        public void setFocusStart(boolean start){
            focusstart = start;
        }

        public void setAnimationStep(int step){
            animationStep = step;
        }

        public void setFocusResult (boolean success) {
            focusresult = success;
        }

        public void setStartTime (long time) {
            startTime = time;
        }

        public void setHaveTouch(boolean touch, Rect eventRect){
            havetouch = touch;
            roundingRect = new RectF(eventRect);
        }

        @Override
        protected void onDraw(Canvas canvas) {
            // TODO Auto-generated method stub
            if (focusstart){
                if (animationStep == 1){
                    drawingPaint.setColor(Color.WHITE);
                    float delta = -5;
                    if (roundingRect != null) {
                        canvas.drawRoundRect(roundingRect, cornerX, cornerY, drawingPaint);

                        roundingRect.inset(delta, delta);

                        if (roundingRect.width() > 300)
                            animationStep++;
                    }
                }else if (animationStep == 2){
                    float delta = 5;
                    if (roundingRect != null) {

                        canvas.drawRoundRect(roundingRect, cornerX, cornerY, drawingPaint);

                        roundingRect.inset(delta, delta);
                        if (roundingRect.width() < 150)
                            animationStep++;
                    }
                }else if (animationStep == 3){
                    float delta = -5;
                    if (roundingRect != null) {

                        canvas.drawRoundRect(roundingRect, cornerX, cornerY, drawingPaint);

                        if (roundingRect.width() < 200)
                            roundingRect.inset(delta, delta);
                    }
                }

                invalidate();
            }else{
                if (animationStep == 4){
                    //duration 500ms
                    long elapsedTime = System.currentTimeMillis() - startTime;

                    if (focusresult){
                        if (roundingRect != null) {

                            drawingPaint.setColor(Color.GREEN);
                            canvas.drawRoundRect(roundingRect, cornerX, cornerY, drawingPaint);
                        }
                    }else{
                        if (roundingRect != null) {

                            drawingPaint.setColor(Color.RED);
                            canvas.drawRoundRect(roundingRect, cornerX, cornerY, drawingPaint);
                        }
                    }


                    if (elapsedTime >= animationDuration){
                        animationStep++;
                        shutterBtn.setEnabled(true);
                    }

                    invalidate();
                }else {
                    canvas.drawColor(Color.TRANSPARENT);
                }
            }
        }
    }

    private void didFinishMergingVideos() {
        try {
            ai_Video mainVideoClip = getMainVideoClip();
            if (mainVideoClip != null && mainVideoClip.getId() > 0) {
                // Save the video to album
                try {
                    String sSaveAlbum = CommonHelper.GetPreferenceString(this, Constants.Settings.bSaveLocal);
                    if (sSaveAlbum != null && Boolean.parseBoolean(sSaveAlbum)) {
                        sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                                Uri.fromFile(new File(getMainVideoClip().getFile()))));
                    }
                } catch (Exception ex) {
                    ai_BugHandler.ai_Handler_Exception("Exception", "if_video.didFinishMergingVideos", ex, this);
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_video.SaveVideoThumb", ex, this);
        }
    }

    private void saveVideoId(long iVideoID) {
        // Save the video to local database
        if (iNoticeID > 0) {
            Intent intent = new Intent(Constants.Broadcasts.sInsertNoticeVideo);
            intent.putExtra(Constants.Extras.iVideoID, iVideoID);
            LocalBroadcastManager.getInstance(if_video_old.this).sendBroadcast(intent);
        } else {
            ai_InsItem insItem = db_InsItem.GetInsItem_ByID(iInsItemID);
            if (insItem != null) {
                insItem.sValueOne = String.valueOf(iVideoID);
                insItem.save();
            }
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mReceiver != null)
            unregisterReceiver(mReceiver);

        if (mPreview != null) {
            mPreview.stopPreview();
            if (mCamera != null) {
                mCamera = null;
            }
        }
        if (mWakeLock != null) {
            mWakeLock.release();
            mWakeLock = null;
        }
    }

    //---------------------------------------------
    // camera thread, gets and encodes video data
    //---------------------------------------------
    class CameraView extends SurfaceView implements SurfaceHolder.Callback {

//        private SurfaceHolder mHolder;
        private final int orient;

        public CameraView(Context context, Camera camera, int orientation) {
            super(context);
            mCamera = camera;
            param = mCamera.getParameters();

            mHolder = getHolder();
            mHolder.addCallback(CameraView.this);
            mHolder.setType(SurfaceHolder.SURFACE_TYPE_PUSH_BUFFERS);

            orient = orientation;
//            mRecorder.setCamera(mCamera, orientation);
        }

        @Override
        public void surfaceCreated(SurfaceHolder holder) {
            try {
                stopPreview();

                mCamera.setPreviewDisplay(holder);
            } catch (IOException exception) {
                mCamera.release();
                mCamera = null;
            }
        }

        public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
            if (isPreviewOn)
                mCamera.stopPreview();

            handleSurfaceChanged();
//            mRecorder.setCamera(mCamera, orient);

            startPreview();
        }

        @Override
        public void surfaceDestroyed(SurfaceHolder holder) {
            try {
                mHolder.removeCallback(this);
                mCamera.stopPreview();
//
                mHolder.addCallback(null);
                mCamera.setPreviewCallback(null);
            } catch (RuntimeException e) {
                // The camera has probably just been released, ignore.
            }
        }

        public void startPreview() {
            if (!isPreviewOn && mCamera != null) {
                isPreviewOn = true;
//                mCamera.startPreview();
            }
        }

        public void stopPreview() {
            if (isPreviewOn && mCamera != null) {
                isPreviewOn = false;
                mCamera.stopPreview();
            }
        }
    }

    public static List<Camera.Size> getResolutionList(Camera camera) {
        return camera.getParameters().getSupportedPreviewSizes();
    }

    public static class ResolutionComparator implements Comparator<Camera.Size> {
        @Override
        public int compare(Camera.Size size1, Camera.Size size2) {
            if (size1.height != size2.height)
                return size1.height - size2.height;
            else
                return size1.width - size2.width;
        }
    }

    public static int getRotationAngle(Activity activity) {
        int rotation = activity.getWindowManager().getDefaultDisplay().getRotation();
        int degrees = 0;

        switch (rotation) {
            case Surface.ROTATION_0:
                degrees = 0;
                break;

            case Surface.ROTATION_90:
                degrees = 90;
                break;

            case Surface.ROTATION_180:
                degrees = 180;
                break;

            case Surface.ROTATION_270:
                degrees = 270;
                break;
        }
        return degrees;
    }

    public static int determineDisplayOrientation(Activity activity, int cameraId) {
        int displayOrientation = 0;

        Camera.CameraInfo cameraInfo = new Camera.CameraInfo();
        Camera.getCameraInfo(cameraId, cameraInfo);

        int degrees = getRotationAngle(activity);

        if (cameraInfo.facing == Camera.CameraInfo.CAMERA_FACING_FRONT) {
            displayOrientation = (cameraInfo.orientation + degrees) % 360;
            displayOrientation = (360 - displayOrientation) % 360;
        } else {
            displayOrientation = (cameraInfo.orientation - degrees + 360) % 360;
        }
        return displayOrientation;
    }

    private void handleSurfaceChanged() {
        if (mCamera == null) return;
        try {
            List<Camera.Size> resolutionList = getResolutionList(mCamera);
            if (resolutionList != null && !resolutionList.isEmpty()) {
                Collections.sort(resolutionList, new ResolutionComparator());
                Camera.Size previewSize = null;

                if (defaultScreenResolution == -1) {
                    int mediumResolution = resolutionList.size() / 2;
                    if (mediumResolution >= resolutionList.size())
                        mediumResolution = resolutionList.size() - 1;
                    previewSize = resolutionList.get(mediumResolution);
                } else {
                    if (defaultScreenResolution >= resolutionList.size())
                        defaultScreenResolution = resolutionList.size() - 1;
                    previewSize = resolutionList.get(defaultScreenResolution);
                }
                if (previewSize != null) {
                    Size resolution = videoQuality.getResolution();
                    param.setPreviewSize(resolution.getWidth(), resolution.getHeight());
                }
            }

            mCamera.setDisplayOrientation(determineDisplayOrientation(this, defaultCameraId));
            List<String> focusModes = param.getSupportedFocusModes();
            if (focusModes != null && focusModes.contains(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO)) {
                param.setFocusMode(Camera.Parameters.FOCUS_MODE_CONTINUOUS_VIDEO);
            } else if (focusModes.contains(Camera.Parameters.FOCUS_MODE_AUTO)) {
                param.setFocusMode(Camera.Parameters.FOCUS_MODE_AUTO);
            }

            mCamera.setDisplayOrientation(0);
            mCamera.setParameters(param);
            mCamera.startPreview();
            mCamera.setPreviewCallback(this);

            updateZoomLevel(latestZoomValue);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    private void saveVideoRecord() {
        if (StringUtils.isEmpty(sTempFilePath)) return;

        try {
            // Get the video `O_FileName`
            O_FileName oFileName = O_FileName.getVideoFileName();
            File oFile = new File(oFileName.sFilePath);
            File oTempFile = new File(sTempFilePath);

            // Move the video file from temp directory to `CommonHelper.sFileRoot`
            FileUtils.copyFile(oTempFile, oFile, false);
            FileUtils.deleteFile(oTempFile);

            // Create the video thumbnail
            VideoUtils.saveVideoThumbnail(oFileName);

            // Update the GPS tag of the thumbnail
            Location currentLocation = GeoLocationManager.getInstance(this).getLocation();
            FileUtils.updateGpsTag(oFileName.sThumbNail, currentLocation);

            // Save the database record
            long iVideoID = CommonDB.InsertVideo(iInsItemID, iNoticeID,
                    oFileName.sThumbNail, oFileName.sFilePath, currentLocation);

            // Save the first video id to the inspection item (sValueOne) or notice (sVideoID)
            List<ai_Video> videoClips = getVideoClips();
            if (videoClips != null && videoClips.size() == 1) {
                saveVideoId(iVideoID);
            }

        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }
    }

    /**
     * Validates video permissions for video recording (RECORD_AUDIO and CAMERA) are granted,
     */
    private boolean validateVideoPermission() {
        String[] permissions = { Manifest.permission.RECORD_AUDIO, Manifest.permission.CAMERA };
        return CommonValidate.checkIfPermissionsGranted(this, permissions);
    }
}