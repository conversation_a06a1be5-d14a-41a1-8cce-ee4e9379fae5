package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.*;
import android.widget.BaseAdapter;
import android.widget.ListView;
import android.widget.TextView;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.TextViewUtils;
import com.snapinspect.snapinspect3.views.RoundTextView;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static com.snapinspect.snapinspect3.Helper.CommonDB_Inspection.GetInspectionByID;

public class if_NoticeList extends Activity {
    private long iInsItemID;
    private long iInspectionID;
    private ListView oListView;

    public static Intent newIntent(Context context, long insId, long iInsItemID) {
        Intent intent = new Intent(context, if_NoticeList.class);
        intent.putExtra(Constants.Extras.iInsID, insId);
        intent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_noticelist);
        getActionBar().setDisplayHomeAsUpEnabled(true);
        getActionBar().setTitle(R.string.title_activity_if_noticelist);

        iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, 0);
        iInspectionID = getIntent().getLongExtra(Constants.Extras.iInsID, 0);

        oListView = findViewById(R.id.lv_noticelist);
        oListView.setOnItemClickListener((parent, view, position, id) -> {
            ai_Notification item = (ai_Notification) parent.getItemAtPosition(position);
            startActivity(if_Notice.newIntent(if_NoticeList.this, item.getId(), iInsItemID, iInspectionID));
        });
        oListView.setOnItemLongClickListener((parent, view, position, id) -> {
            ai_Notification item = (ai_Notification) parent.getItemAtPosition(position);
            showsDeleteAction(item);
            return true;
        });

        try {
            ai_Inspection inspection = GetInspectionByID((int) iInspectionID);
            if (inspection != null) {
                TextViewUtils.updateText(findViewById(R.id.txt_AssetAddress), inspection.sTitle);
                ai_Assets asset = CommonDB_Assets.GetAssetBy_iSAssetID(inspection.iSAssetID);
                TextViewUtils.updateText(findViewById(R.id.txt_Asset_Ref), asset.sFieldThree);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }

    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        if (iInsItemID > 0) {
            getMenuInflater().inflate(R.menu.menu_new_notice, menu);
        }
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (item.getItemId() == R.id.action_new_notice) {
            startActivity(if_Notice.newIntent(this, 0, iInsItemID, iInspectionID));
        } else {
            onBackPressed();
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onResume() {
        super.onResume();
        reloadData();
    }

    private void reloadData() {
        NoticeListAdapter adapter = new NoticeListAdapter(
             new ArrayList<>(CommonDB_Notification.getNotifications((int) iInspectionID, (int) iInsItemID)), this);
        oListView.setAdapter(adapter);
        adapter.notifyDataSetChanged();
    }

    private void showsDeleteAction(final ai_Notification notification) {
        new MaterialDialog.Builder(this)
                .title(R.string.alert_title_message)
                .content(R.string.delete_task_confirmation)
                .positiveText(R.string.alert_action_yes)
                .negativeText(R.string.alert_action_cancel)
                .onPositive((dialog, which) -> {
                    notification.bDeleted = true;
                    notification.save();

                    // Delete photos
                    if (!StringUtils.isEmpty(notification.sPhotoURL)) {
                        List<ai_Photo> photos = CommonDB_Inspection.GetInsItemPhotos(iInsItemID, notification.sPhotoURL);
                        for (ai_Photo photo : photos) {
                            CommonDB.DeletePhotoByPhotoID(photo.getId().intValue());
                        }
                    }

                    // Delete Videos
                    if (!StringUtils.isEmpty(notification.sVideoID)) {
                        long videoId = CommonHelper.getInt(notification.sVideoID);
                        CommonDB.DeleteVideoByIDNotification(videoId, notification.getId(), if_NoticeList.this);
                    }

                    reloadData();
                })
                .onNegative((dialog, which) -> dialog.dismiss())
                .show();
    }

    private class NoticeListAdapter extends BaseAdapter {
        private final static int INVALID_ID = -1;

        private ArrayList<ai_Notification> lsNotifications_Adapter;
        private Context oContext;
        private final HashMap<ai_Notification, Integer> mIdMap = new HashMap<>();
        private LayoutInflater mInflater;

        public NoticeListAdapter(ArrayList<ai_Notification> _objects, Context _oContext) {
            try {
                oContext = _oContext;
                lsNotifications_Adapter = _objects;
                for (int i = 0; i < _objects.size(); ++i) {
                    mIdMap.put(_objects.get(i), i);
                }
                mInflater = LayoutInflater.from(_oContext);
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "NoticeListAdapter", ex, oContext);
            }
        }

        @Override
        public int getCount() {
            return mIdMap.size();
        }

        @Override
        public Object getItem(int position) {
            return lsNotifications_Adapter.get(position);
        }

        @Override
        public long getItemId(int position) {
            if (position < 0 || position >= mIdMap.size()) return INVALID_ID;
            ai_Notification item = (ai_Notification) getItem(position);
            return mIdMap.get(item);
        }

        @Override
        public boolean hasStableIds() {
            return true;
        }

        @Override
        public boolean isEnabled(int position) {
            return true;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            ViewHolder viewHolder;
            if (convertView == null) {
                convertView = mInflater.inflate(R.layout.cell_task_overview, null);
                viewHolder = new ViewHolder(convertView);
                convertView.setTag(viewHolder);
            } else {
                viewHolder = (ViewHolder) convertView.getTag();
            }

            ai_Notification item = (ai_Notification) getItem(position);

            ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, item.iInsItemID);

            TextViewUtils.updateText(viewHolder.txtInsTitle, oInsItem != null ? oInsItem.sName : null);
            TextViewUtils.updateText(viewHolder.txtName, item.sTitle);

            if (!StringUtils.isEmpty(item.sDescription))
                TextViewUtils.updateText(viewHolder.txtDescription, "\"" + item.sDescription + "\"");
            else {
                TextViewUtils.updateText(viewHolder.txtDescription, item.sDescription);
            }
            try {
                Date date = if_Notice.dueDateFormat.parse(item.sDueDate);
                String sDueDate = DateUtils.formatAsTaskDueDate(date);
                TextViewUtils.updateText(viewHolder.txtDue, sDueDate);
            } catch (Exception ignored) { }

            TextViewUtils.updateText(viewHolder.txtCategory, item.sCategory);
            viewHolder.txtCategory.setCornerRadius(10);
            viewHolder.txtCategory.setSolidColor(
                    getResources().getColor(R.color.gray_border_color)
            );

            TaskPriority taskPriority = TaskPriority.getTaskPriority(item.iPriority);
            TextViewUtils.updateText(viewHolder.txtPriorityName, taskPriority.getDisplayName());
            viewHolder.txtPriorityName.setCornerRadius(10);
            viewHolder.txtPriorityName.setSolidColor(
                    getResources().getColor(taskPriority.getBackgroundColor())
            );

            ai_User assigned = item.getTaskAssignToUser();
            if (assigned == null) viewHolder.txtAssignName.setVisibility(View.GONE);
            else {
                viewHolder.txtPriorityName.setVisibility(View.VISIBLE);
                viewHolder.txtAssignName.setCornerRadius(22);
                viewHolder.txtAssignName.setSolidColor(
                        getResources().getColor(R.color.task_assign_round_background)
                );
                TextViewUtils.updateText(viewHolder.txtAssignName, assigned.getNameLetters());
            }
            return convertView;
        }

        private class ViewHolder {
            TextView txtName, txtDescription, txtInsTitle, txtDue;
            RoundTextView txtCategory, txtPriorityName, txtAssignName;
            ViewHolder(View rootView) {
                txtName = rootView.findViewById(R.id.txt_sName);
                txtDescription = rootView.findViewById(R.id.txt_sDescription);
                txtInsTitle = rootView.findViewById(R.id.txt_sInsTitle);
                txtDue = rootView.findViewById(R.id.txt_NoticeDue);

                txtCategory = rootView.findViewById(R.id.txt_NoticeCategory);
                txtPriorityName = rootView.findViewById(R.id.txt_PriorityName);
                txtAssignName = rootView.findViewById(R.id.txt_assign_to);
            }
        }
    }
}
