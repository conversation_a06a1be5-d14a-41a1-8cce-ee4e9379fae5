package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.graphics.*;
import android.location.Location;
import android.os.Build;
import android.os.Bundle;
import android.util.AttributeSet;
import android.view.*;
import android.widget.Button;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.RequiresApi;
import com.orm.SugarRecord;
import com.orm.query.Condition;
import com.orm.query.Select;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.IF_Object.ai_Contact;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.ArrayUtils;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.io.FileOutputStream;
import java.util.Date;
import java.util.List;

public class if_sign extends Activity {

    private LinearLayout mContent;
    private signature mSignature;
    private View mView;
    private Button mClear;
    private Button mGetSign;
    private Bitmap mBitmap;
    private int iInsItemID;
    private String sName;
    private String sEmail;
    private EditText etName;
    private EditText etEmail;
    private TextView tvDeclaration;
    private TextView btnMore;
    private List<ai_Contact> lsContact;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);

            setContentView(R.layout.activity_sign);
            getActionBar().setDisplayHomeAsUpEnabled(true);
            iInsItemID = getIntent().getIntExtra(Constants.Extras.iInsItemID, 0);

            mContent = findViewById(R.id.ll_sign_area);
            mContent.getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
                @Override
                public void onGlobalLayout() {
                    mContent.getViewTreeObserver().removeOnGlobalLayoutListener(this);
                    ViewGroup.LayoutParams oParams = mContent.getLayoutParams();
                    oParams.height = mContent.getWidth() / 2;
                    mContent.setLayoutParams(oParams);
                }
            });
            mSignature = new signature(this, null);
            mSignature.setBackgroundColor(Color.WHITE);
            mContent.addView(mSignature, ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT);
            mClear = findViewById(R.id.btn_clear_sign);
            mGetSign = findViewById(R.id.btn_sign);
            mGetSign.setEnabled(false);
            mView = mContent;

            etName = findViewById(R.id.sign_et_name);
            etEmail = findViewById(R.id.sign_et_email);
            ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);
            sName = oInsItem.sValueTwo;
            sEmail = oInsItem.sValueThree;

            etName.setText(sName);
            etEmail.setText(sEmail);

            //Declaration
            ai_InsItem oPInsItem = ai_InsItem.findById(ai_InsItem.class, (long) oInsItem.iPInsItemID);

            tvDeclaration = findViewById(R.id.sign_tv_declaration);
            String sParentInstruction = CommonDB.GetInstruction(oPInsItem, true);
            String sChildInstruction = CommonDB.GetInstruction(oInsItem, true);
            if (StringUtils.isEmpty(sParentInstruction) && StringUtils.isEmpty(sChildInstruction)) {
                tvDeclaration.setVisibility(View.GONE);
                findViewById(R.id.sign_tv_declaration_title).setVisibility(View.GONE);
                findViewById(R.id.sign_tv_declaration_title).setVisibility(View.GONE);
            } else if (!StringUtils.isEmpty(sChildInstruction)) {
                sParentInstruction = sParentInstruction + "\n\n" + sChildInstruction;
            }
            tvDeclaration.setText(sParentInstruction);

            btnMore = findViewById(R.id.sign_btn_more);
            btnMore.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    CommonUI.ShowAlert(if_sign.this,"Declaration", tvDeclaration.getText().toString());
                }
            });

            tvDeclaration.post(new Runnable() {
                @Override
                public void run() {
                    int lineCount = tvDeclaration.getLineCount();
                    btnMore.setVisibility(lineCount > 4 ? View.VISIBLE : View.GONE);
                }
            });


            ai_Inspection oInspection = ai_Inspection.findById(ai_Inspection.class, (long) oInsItem.iInsID);
            lsContact = Select.from(ai_Contact.class).where(Condition.prop("i_S_Asset_ID").eq(oInspection.iSAssetID)).list();

            findViewById(R.id.sign_btn_add).setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    showContactDialog();
                }
            });

            mClear.setOnClickListener(new View.OnClickListener() {
                public void onClick(View v) {
                    mSignature.clear();
                    mGetSign.setEnabled(false);
                }
            });

            mGetSign.setOnClickListener(new View.OnClickListener() {
                public void onClick(View v) {
                    if (!CommonUI.bAppPermission(if_sign.this)) {
                        return;
                    }
                    mView.setDrawingCacheEnabled(true);
                    boolean bSuccess = mSignature.save(mView);
                    if (bSuccess) {

                        Bundle b = new Bundle();
                        b.putString("status", "done");
                        Intent intent = new Intent();
                        intent.putExtras(b);
                        setResult(RESULT_OK, intent);
                        finish();
                    }
                }
            });

            // request location permission
            CommonValidate.requestPermissions(this, Constants.LOCATION_PERMISSIONS);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
        GeoLocationManager.getInstance(this).startUpdateLocation();
    }

    @Override
    protected void onPause() {
        super.onPause();
        GeoLocationManager.getInstance(this).stopUpdateLocation();
    }

    private void showContactDialog() {
        if (!ArrayUtils.isEmpty(lsContact)) {
            AlertDialog.Builder oBuilder = CommonUI.GetAlertBuilder("Choose an Contact", "", if_sign.this, true, false);
            oBuilder.setTitle("Choose an contact");
            String[] lsOption = new String[lsContact.size()];
            for (int i = 0; i < lsContact.size(); i++) {
                lsOption[i] = lsContact.get(i).sFirstName + " " + lsContact.get(i).sLastName;
            }
            oBuilder.setItems(lsOption, (dialogInterface, i) -> selectedContact(lsContact.get(i)));
            oBuilder.show();
        } else {
            CommonUI.ShowAlert(this, "Infomation", "No contacts for this Asset. Please enter manually.");
        }
    }

    private void selectedContact(ai_Contact contact) {
        etName.setText(String.format("%s %s", contact.sFirstName, contact.sLastName));
        etEmail.setText(contact.sEmail);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        super.onBackPressed();
        return true;
    }

    public class signature extends View {
        private static final float STROKE_WIDTH = 6f;
        private static final float HALF_STROKE_WIDTH = STROKE_WIDTH / 2;
        private final Paint paint = new Paint();
        private final Path path = new Path();

        private float lastTouchX;
        private float lastTouchY;
        private final RectF dirtyRect = new RectF();

        public signature(Context context, AttributeSet attrs) {
            super(context, attrs);
            paint.setAntiAlias(true);
            paint.setColor(Color.BLACK);
            paint.setStyle(Paint.Style.STROKE);
            paint.setStrokeJoin(Paint.Join.ROUND);
            paint.setStrokeWidth(STROKE_WIDTH);
        }

        public boolean save(View v) {
            sName = etName.getText().toString();
            sEmail = etEmail.getText().toString();

            if (!StringUtils.isEmpty(sEmail) && (!CommonHelper.IsEmail(sEmail))) {
                CommonUI.ShowAlert(getContext(), "Error", "Invalid email");
            } else {
                mBitmap = Bitmap.createBitmap(mContent.getWidth(), mContent.getHeight(), Bitmap.Config.RGB_565);
                Canvas canvas = new Canvas(mBitmap);
                canvas.drawColor(Color.WHITE);
                try {
                    O_FileName oFileName = O_FileName.getPhotoFileName();
                    FileOutputStream outStream = null;
                    outStream = new FileOutputStream(oFileName.sFilePath);
                    v.draw(canvas);
                    mBitmap.compress(Bitmap.CompressFormat.JPEG, 100, outStream);
                    outStream.flush();
                    outStream.close();
                    CommonDB.DeletePhotoByInsItemID(iInsItemID, this.getContext());
                    Location currentLocation = GeoLocationManager.getInstance(if_sign.this).getLocation();
                    CommonDB.InsertInsItemPhoto(
                            if_sign.this, iInsItemID, "", oFileName.sFilePath, 1, currentLocation);
                    ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);
                    oInsItem.sValueTwo = sName;
                    oInsItem.sValueThree = sEmail;
                    oInsItem.sValueFour = CommonHelper.sDateToStringFormat(new Date(), "yyyy-MM-dd HH:mm");
                    oInsItem.save();
                    return true;
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }

            return false;
        }

        public void clear() {
            path.reset();
            invalidate();
        }

        @Override
        protected void onDraw(Canvas canvas) {
            canvas.drawPath(path, paint);
        }

        @Override
        public boolean onTouchEvent(MotionEvent event) {
            float eventX = event.getX();
            float eventY = event.getY();
            mGetSign.setEnabled(true);

            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    path.moveTo(eventX, eventY);
                    lastTouchX = eventX;
                    lastTouchY = eventY;
                    return true;

                case MotionEvent.ACTION_MOVE:

                case MotionEvent.ACTION_UP:

                    resetDirtyRect(eventX, eventY);
                    int historySize = event.getHistorySize();
                    for (int i = 0; i < historySize; i++) {
                        float historicalX = event.getHistoricalX(i);
                        float historicalY = event.getHistoricalY(i);
                        expandDirtyRect(historicalX, historicalY);
                        path.lineTo(historicalX, historicalY);
                    }
                    path.lineTo(eventX, eventY);
                    break;

                default:
                    debug("Ignored touch event: " + event);
                    return false;
            }

            invalidate((int) (dirtyRect.left - HALF_STROKE_WIDTH),
                    (int) (dirtyRect.top - HALF_STROKE_WIDTH),
                    (int) (dirtyRect.right + HALF_STROKE_WIDTH),
                    (int) (dirtyRect.bottom + HALF_STROKE_WIDTH));

            lastTouchX = eventX;
            lastTouchY = eventY;

            return true;
        }

        private void debug(String string) {
        }

        private void expandDirtyRect(float historicalX, float historicalY) {
            if (historicalX < dirtyRect.left) {
                dirtyRect.left = historicalX;
            } else if (historicalX > dirtyRect.right) {
                dirtyRect.right = historicalX;
            }

            if (historicalY < dirtyRect.top) {
                dirtyRect.top = historicalY;
            } else if (historicalY > dirtyRect.bottom) {
                dirtyRect.bottom = historicalY;
            }
        }

        private void resetDirtyRect(float eventX, float eventY) {
            dirtyRect.left = Math.min(lastTouchX, eventX);
            dirtyRect.right = Math.max(lastTouchX, eventX);
            dirtyRect.top = Math.min(lastTouchY, eventY);
            dirtyRect.bottom = Math.max(lastTouchY, eventY);
        }
    }
}
