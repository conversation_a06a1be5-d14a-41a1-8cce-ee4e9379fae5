package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.TextView;

import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_QuickPhrase;
import com.snapinspect.snapinspect3.QRScan.QRScannerActivity;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.InputAccessoryHelper;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.SystemUiHider;
import com.snapinspect.snapinspect3.util.ThrottledSearch;
import com.snapinspect.snapinspect3.views.InputAccessoryToolbar;

import java.util.ArrayList;
import java.util.List;

import static android.text.InputType.TYPE_CLASS_NUMBER;
import static android.text.InputType.TYPE_NUMBER_FLAG_DECIMAL;
import static android.text.InputType.TYPE_NUMBER_FLAG_SIGNED;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_CMT;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_S_CONFIG_KEY_NUM;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_EditComments extends Activity {

    private static final int COMMENTS_LIBRARY_REQUEST_CODE = 0;

    private int iPosition = 0;
    private int iInsItemID = 0;
    private int iPLayoutID = 0;
    private String sType = "";
    private ai_InsItem oInsItem;
    private String sCurrentString = "";
    private List<ai_QuickPhrase> lsQuickPhrase = new ArrayList<ai_QuickPhrase>();
    private ListView oListView;
    private Ins_CMT_QP_Adapter adapter;
    private EditText oEditText;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            getActionBar().show();
            getActionBar().setDisplayHomeAsUpEnabled(true);
            setContentView(R.layout.activity_editcomments);

            iPosition = getIntent().getIntExtra(Constants.Extras.iPosition, 0);
            iInsItemID = getIntent().getIntExtra(Constants.Extras.iInsItemID, 0);
            iPLayoutID = getIntent().getIntExtra(Constants.Extras.iPLayoutID, 0);
            //Log.e("osama", "iPLayoutID aaaaa----->" + String.valueOf(iPLayoutID));

            sType = getIntent().getStringExtra(Constants.Extras.sType) == null ?
                    "CMT" : getIntent().getStringExtra(Constants.Extras.sType);

            adapter = new Ins_CMT_QP_Adapter(this, R.layout.cell_quickphrase_textview, lsQuickPhrase);
            oListView = findViewById(R.id.lv_Comment_QuickText);
            oListView.setAdapter(adapter);
            oListView.setItemsCanFocus(false);

            oEditText = findViewById(R.id.txt_comments);
            if (sType.equalsIgnoreCase(SI_S_CONFIG_KEY_NUM)){
                oEditText.setInputType(TYPE_CLASS_NUMBER | TYPE_NUMBER_FLAG_DECIMAL | TYPE_NUMBER_FLAG_SIGNED);
            }
            oEditText.setCursorVisible(true);

            oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);
            String sText = CommonHelper.GetValue(iPosition, oInsItem);
            oEditText.setText(sText);

            new ThrottledSearch(this, this::editTextChanged).bindTo(oEditText);

            if (sType.equalsIgnoreCase(SI_S_CONFIG_KEY_CMT)) {
                getActionBar().setTitle("Write Comments");
            } else if (sType.equalsIgnoreCase(SI_S_CONFIG_KEY_NUM)) {
                getActionBar().setTitle("Write Number");
            }

            ai_InsItem oPInsItem = ai_InsItem.findById(ai_InsItem.class, (long)oInsItem.iPInsItemID);

            TextView oTipText = findViewById(R.id.text_comment_tip);
            String sTempConfig = CommonHelper.GetConfig(iPosition, oInsItem, oPInsItem);

            if (sTempConfig != null && sTempConfig.startsWith(SI_S_CONFIG_KEY_CMT) && sTempConfig.contains("(")) {
                String text = sTempConfig.substring(4, sTempConfig.length() - 1);
                oTipText.setText(text);
            } else {
                oTipText.setText(R.string.comments);
            }

            if (!sType.equalsIgnoreCase(SI_S_CONFIG_KEY_NUM)) {
                InputAccessoryToolbar inputAccessoryToolbar = findViewById(R.id.input_accessory_view);
                inputAccessoryToolbar.setListener(new InputAccessoryToolbar.Listener() {
                    @Override
                    public void onCommentLibraryClick() {
                        showsCommentsLibrary();
                    }

                    @Override
                    public void onScanCodeClick() {
                        Intent i = new Intent(if_EditComments.this, QRScannerActivity.class);
                        startActivityForResult(i, Constants.RequestCodes.QR_SCANNER_REQUEST);
                    }
                });
                new InputAccessoryHelper(this, this, inputAccessoryToolbar, null);
            }

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditComments.onCreate", ex, this);
        }
       // oEditText.setSelection(sText == null ? 0 : sText.length(), sText == null ? 0 : sText.length());
    }

    @Override
    protected void onResume() {
        super.onResume();
        try {
            CommonHelper.showSoftKeyboard(this, oEditText);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditComments.onResume", ex, this);
        }
        // Normal case behavior follows
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_editcomments, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected( MenuItem item ) {
        onBackPressed();
        return true;
    }

    @Override
    public void onBackPressed() {
        if (!validateConfigNum()) return;
        CommonHelper.hideSoftKeyboard(this);
        CommonHelper.SetValue(iPosition, oInsItem, oEditText.getText().toString());
        oInsItem.save();
        super.onBackPressed();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data == null) return;
        if (COMMENTS_LIBRARY_REQUEST_CODE == requestCode) {
            if (resultCode == if_CommentsLibrary.COMMENTS_LIBRARY_RESULT_CODE) {
                String sComments = data.getStringExtra(Constants.Extras.sComments);
                if (!StringUtils.isEmpty(sComments)) {
                    CommonUI.insertText(oEditText, sComments);
                    CommonHelper.SetValue(iPosition, oInsItem, oEditText.getText().toString());
                    oInsItem.save();
                }
            }
        } else if (Constants.RequestCodes.QR_SCANNER_REQUEST == requestCode) {
            if (resultCode == RESULT_OK) {
                String result = data.getStringExtra("result");
                if (!StringUtils.isEmpty(result)) {
                    CommonUI.insertText(oEditText, result);
                    CommonHelper.SetValue(iPosition, oInsItem, oEditText.getText().toString());
                    oInsItem.save();
                }
            }
        }
    }

    private void ShowAlert(String sTitle, String sMessage) {
        CommonUI.ShowAlert(this, sTitle, sMessage);
    }

    public class Ins_CMT_QP_Adapter extends ArrayAdapter<ai_QuickPhrase> {
        private final List<ai_QuickPhrase> lsQuickPhrase;
        private final Context oContext;

        public Ins_CMT_QP_Adapter(Context context, int textViewResourceId,
                              List<ai_QuickPhrase> objects) {
            super(context, textViewResourceId, objects);
            this.lsQuickPhrase = objects;
            this.oContext = context;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            View view = convertView;
            try {
                if (view == null) {
                    view = ((LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE))
                            .inflate(R.layout.cell_quickphrase_textview, null);
                }
                view.setBackgroundColor(Color.WHITE);
                TextView oTextView = view.findViewById(R.id.txt_quickphrase);
                oTextView.setText(lsQuickPhrase.get(position).sComments);
                oTextView.setTag(position);
                lsQuickPhrase.get(position).sFieldThree = "";

                oTextView.setOnClickListener((View v) -> {
                    adapter.notifyDataSetChanged();

                    int pos = CommonHelper.getInt(v.getTag().toString());
                    String sSelectedText = lsQuickPhrase.get(pos).sComments.trim();

                    int start = 0, end = oEditText.getSelectionStart();
                    String sText = oEditText.getText().toString();
                    String prefix = sText.substring(start, end);
                    if (prefix.endsWith(sCurrentString)) {
                        String finalPrefix = prefix.substring(0, prefix.length() - sCurrentString.length());
                        oEditText.getText().replace(start, end, finalPrefix);
                    }

                    CommonUI.insertText(oEditText, sSelectedText);
                });
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "if_EditComments.getView", ex, oContext);
            }
            return view;
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public boolean hasStableIds() {
            return true;
        }
    }

    private void editTextChanged(String sText) {
        try {
            int start = 0;
            int end = oEditText.getSelectionStart();
            String prefix = sText.substring(start, end);
            if (prefix.contains(" ")) {
                String[] texts = prefix.split(" ");
                if (texts.length > 0)
                    sCurrentString = texts[texts.length - 1];
            } else {
                sCurrentString = prefix;
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_EditComments.editTextChanged", ex, this);
            sCurrentString = "";
        }

        if (!StringUtils.isEmpty(sCurrentString) && sCurrentString.length() >= 3) {
            lsQuickPhrase = CommonDB.SearchPhrase(sCurrentString, if_EditComments.this);
        } else if (lsQuickPhrase != null && !lsQuickPhrase.isEmpty()) {
            lsQuickPhrase = new ArrayList<>();
        }
        adapter = new Ins_CMT_QP_Adapter(if_EditComments.this, R.layout.cell_quickphrase_textview, lsQuickPhrase);
        oListView.setAdapter(adapter);
        oListView.setItemsCanFocus(false);
    }

    private boolean validateConfigNum() {
        String sText = oEditText.getText().toString();
        if (sType.equalsIgnoreCase(SI_S_CONFIG_KEY_NUM)){
            try {
                if (!StringUtils.isEmpty(sText) && sText.trim().length() > 0) {
                    Double.parseDouble(sText);
                }
            } catch(Exception ex) {
                ShowAlert("Error", "Please enter number.");
                oEditText.setText("");
                return false;
            }
        }
        return true;
    }

    private void showsCommentsLibrary() {
        if (!validateConfigNum()) return;

        String sText = oEditText.getText().toString();
        CommonHelper.SetValue(iPosition, oInsItem, sText);
        oInsItem.save();

        Intent oIntent = new Intent(if_EditComments.this, if_CommentsLibrary.class);
        oIntent.putExtra(Constants.Extras.sWords, sText);
        oIntent.putExtra(Constants.Extras.iPosition, iPosition);
        oIntent.putExtra(Constants.Extras.iTextPosition, oEditText.getSelectionStart());
        oIntent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        oIntent.putExtra(Constants.Extras.iPLayoutID, iPLayoutID);
        startActivityForResult(oIntent, COMMENTS_LIBRARY_REQUEST_CODE);
    }
}
