package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.GridView;
import android.widget.ImageView;
import android.widget.TextView;
import com.snapinspect.snapinspect3.Adapter.VideoClipsAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.ArrayUtils;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.TextViewUtils;

import java.util.List;

public class if_VideoClips extends Activity {
    public static final int RESULT_START_MERGE = 1;
    private static final int columnCount = 3;
    private static final int columnSpacing = 20;
    private long iNoticeID, iInsItemID;

    public static Intent newIntent(Context context, long iInsItemID, long iNoticeID) {
        Intent intent = new Intent(context, if_VideoClips.class);
        intent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        intent.putExtra(Constants.Extras.iNoticeID, iNoticeID);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        CommonUI.setActionBar(this, R.string.title_activity_video_clips, true);
        setContentView(R.layout.activity_if_video_clips);
        iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, 0);
        iNoticeID = getIntent().getLongExtra(Constants.Extras.iNoticeID, 0);
    }

    @Override
    protected void onResume() {
        super.onResume();
        updateViews();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_video_clips, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onMenuItemSelected(int featureId, MenuItem item) {
        if (item.getItemId() == R.id.action_menu_merge) {
            Intent intent = getIntent();
            setResult(RESULT_START_MERGE, intent);
        }
        onBackPressed();
        return super.onMenuItemSelected(featureId, item);
    }

    private void updateViews() {
        updatePendingMergedVideos();
        updateMainClip();
    }

    private void updatePendingMergedVideos() {
        List<ai_Video> lsVideo = getVideoClips();
        if (ArrayUtils.isNotEmpty(lsVideo)) {
            // Retrieve the all the video clips except the first one
            lsVideo.remove(getMainVideoClip());
        }
        GridView gvVideoClips = findViewById(R.id.gv_video_clips);
        gvVideoClips.setNumColumns(columnCount);
        int size = (CommonUI.getScreenWidth() - CommonUI.dpToPx(columnSpacing * (1 + columnCount), this)) / columnCount;
        gvVideoClips.setAdapter(new VideoClipsAdapter(this, lsVideo, size, size));
        gvVideoClips.setOnItemClickListener((parent, view, position, id) -> {
            startActivity(if_videoshow.newIntent(this, iInsItemID, iNoticeID, lsVideo.get(position).getId(), ""));
        });
    }

    private void updateMainClip() {
        View mainClip = findViewById(R.id.view_main_clip);
        List<ai_Video> lsVideo = getVideoClips();
        if (ArrayUtils.isNotEmpty(lsVideo)) {
            ai_Video oVideo = getMainVideoClip();
            ImageView thumbView = mainClip.findViewById(R.id.img_thumb_view);
            thumbView.setImageBitmap(BitmapFactory.decodeFile(oVideo.getThumb()));
            TextView durationTextView = mainClip.findViewById(R.id.tv_duration);
            long duration = CommonHelper.getVideoDuration(this, oVideo.getFile());
            TextViewUtils.updateText(durationTextView, DateUtils.clockStringFromMilliseconds(duration));

            mainClip.setOnClickListener(v -> {
                startActivity(if_videoshow.newIntent(this, iInsItemID, iNoticeID, oVideo.getId(), ""));
            });
        }
    }

    private List<ai_Video> getVideoClips() {
        return iNoticeID > 0 ? CommonDB.GetNoticeVideos(iNoticeID, iInsItemID) : CommonDB.GetInsItemVideos(iInsItemID);
    }

    private ai_Video getMainVideoClip() {
        return iNoticeID > 0 ? CommonDB.GetNoticeMainVideo(iNoticeID, iInsItemID) : CommonDB.GetInsItemMainVideo(iInsItemID);
    }
}