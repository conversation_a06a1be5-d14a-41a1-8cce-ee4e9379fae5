package com.snapinspect.snapinspect3.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import com.afollestad.materialdialogs.MaterialDialog;
import com.datadog.android.Datadog;
import com.google.android.gms.auth.api.signin.GoogleSignIn;
import com.google.android.gms.auth.api.signin.GoogleSignInAccount;
import com.google.android.gms.auth.api.signin.GoogleSignInClient;
import com.google.android.gms.auth.api.signin.GoogleSignInOptions;
import com.google.android.gms.common.api.ApiException;
import com.google.android.gms.tasks.OnCompleteListener;
import com.google.android.gms.tasks.Task;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.google.firebase.messaging.FirebaseMessaging;
import com.loopj.android.http.RequestParams;
import com.microsoft.identity.client.*;
import com.microsoft.identity.client.exception.MsalException;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.QRScan.QRScannerActivity;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.Home.if_HomeTab;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.SystemUiHider;
import io.intercom.android.sdk.Intercom;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_login extends Activity {
    private static final int GOOGLE_SIGN_IN_RESPONSE = 2;
    private static final String[] SCOPES = {"User.Read"};

    private GoogleSignInClient mGoogleSignInClient;
    private IMultipleAccountPublicClientApplication mMultipleAccountApp;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
            setContentView(R.layout.activity_login);

            Button oButtonQRCode = findViewById(R.id.btn_qrscan);
            oButtonQRCode.setVisibility(View.GONE);

            CommonValidate.Permission_Validate(if_login.this);
            //Temp folder will call Root folder exist so didn't include ValidateRootFolderExist();
            CommonHelper.ValidateTempFolderExist();

            String sSavedEmail = CommonHelper.GetPreferenceString(if_login.this, "sEmail");
            String sSavedPassword = CommonHelper.GetPreferenceString(if_login.this, "sPassword");
            boolean bAutoLogin = CommonHelper.GetPreferenceBoolean(this, Constants.Settings.bAutoLogin);
            String sCustomerID = CommonHelper.GetPreferenceString(this, "iCustomerID");
            final String sToken = CommonHelper.GetPreferenceString(this, "sToken");
            if (!StringUtils.isEmpty(sSavedEmail) && !StringUtils.isEmpty(sCustomerID) && !StringUtils.isEmpty(sToken) && bAutoLogin) {
                FirebaseCrashlytics.getInstance().setUserId(sCustomerID);
                FirebaseCrashlytics.getInstance().setCustomKey("Email", sSavedEmail);
                goToHome();
            }
            if (sSavedEmail != null && sSavedEmail.length() > 0) {
                Button oButton = findViewById(R.id.btn_Signup);
                oButton.setVisibility(View.GONE);
            } else {
                findViewById(R.id.btn_Signup).setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // Intent intent = new Intent(if_login.this, if_signup.class);
                        // startActivity(intent);
                        //finish();
                        //  Uri uriUrl = Uri.parse("https://my.snapinspect.com/freetrial/start");
                        //Intent launchBrowser = new Intent(Intent.ACTION_VIEW, uriUrl);
                        //startActivity(launchBrowser);
                    }
                });
            }

            // Check if Kiosk mode is enabled, if so, go to home screen
            if (CommonHelper.isKioskMode(this)) {
                goToHome();
            } else {
                TextView oTextView = findViewById(R.id.AppTitle);
                oTextView.setOnLongClickListener(view -> {
                    new MaterialDialog.Builder(if_login.this)
                            .title("Warning")
                            .content("Are you sure to enable test site mode? This is not reversible unless reinstall the app. Please do not click OK, unless you received instruction from SnapInspect. Thank you!")
                            .positiveText(R.string.tv_ok)
                            .onPositive((dialog, which) -> CommonHelper.SavePreference(if_login.this, "bTestMode", "true"))
                            .show();

                    return false;
                });

                ((EditText) findViewById(R.id.edit_Email)).setText((sSavedEmail == null) ? "" : sSavedEmail);
                ((EditText) findViewById(R.id.edit_Password)).setText((sSavedPassword == null || (!bAutoLogin)) ? "" : sSavedPassword);
                findViewById(R.id.btn_Login).setOnClickListener(this::loginButtonTapped);

                ImageButton oButtonGoogle = findViewById(R.id.btn_Login_google);
                oButtonGoogle.setOnClickListener(this::loginButtonTapped);

                ImageButton oButtonAzure = findViewById(R.id.btn_Login_azure);
                oButtonAzure.setOnClickListener(this::loginButtonTapped);

                //QR Code Scanner
                findViewById(R.id.image_view_logo).setOnLongClickListener(v -> {
                    Intent i = new Intent(if_login.this, QRScannerActivity.class);
                    startActivityForResult(i, Constants.RequestCodes.QR_SCANNER_REQUEST);
                    return true;
                });

                // Google SSO
                GoogleSignInOptions gso = new GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                        .requestIdToken(getResources().getString(R.string.google_sso_server_client_id))
                        .requestEmail()
                        .build();
                mGoogleSignInClient = GoogleSignIn.getClient(this, gso);

                // Azure SSO
                PublicClientApplication.createMultipleAccountPublicClientApplication(this,
                        R.raw.msal_config,
                        new IPublicClientApplication.IMultipleAccountApplicationCreatedListener() {
                            @Override
                            public void onCreated(IMultipleAccountPublicClientApplication application) {
                                mMultipleAccountApp = application;
                            }

                            @Override
                            public void onError(MsalException exception) {
                                //Log Exception Here
                            }
                        });
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Login.onCreate", ex, this);
        }
    }

    @SuppressLint("NonConstantResourceId")
    private void loginButtonTapped(View v) {
        if (!NetworkUtils.isNetworkAvailable(this)) {
            ShowAlert(R.string.title_alert_error, R.string.failed_connection);
            return;
        }
        switch (v.getId()) {
            case R.id.btn_Login:
                loginWithEmailPassword();
                break;
            case R.id.btn_Login_google:
                loginWithGoogle();
                break;
            case R.id.btn_Login_azure:
                loginWithAzure();
                break;
            default:
                break;
        }
    }

    private void goToHome() {
        if (!CommonHelper.isKioskMode(this)) {
            // Log user info to Datadog
            String sUserName = ((EditText) findViewById(R.id.edit_Email)).getText().toString().trim();
            Datadog.setUserInfo(
                    CommonHelper.GetPreferenceString(this, "iCustomerID"),
                    StringUtils.isEmpty(sUserName) ? "" : sUserName,
                    CommonHelper.GetPreferenceString(this, "sEmail")
            );

            // Intercom login
            CommonHelper.IntercomLogin(this);
        }

        Intent intent = new Intent(this, if_HomeTab.class);
        String downloadableUri = getIntent().getStringExtra(Constants.Extras.sKioskUri);
        if (!StringUtils.isEmpty(downloadableUri)) {
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            intent.putExtra(Constants.Extras.sKioskUri, downloadableUri);
        }

        // Intercom
        Intercom.client().handlePushMessage();

        // Register FCM token to server
        new Handler(Looper.myLooper()).post(this::registerFCM);

        startActivity(intent);
        finish();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        switch (requestCode) {
            case Constants.RequestCodes.QR_SCANNER_REQUEST:
                if (resultCode == Activity.RESULT_OK) {
                    String result = data.getStringExtra(Constants.Extras.sResult);
                    if (result.startsWith("SI_ExternalIns:")) {
                        String sToken = result.replace("SI_ExternalIns://", "");
                        if (sToken.contains("_")) {
                            String[] arrToken = sToken.split("_");
                            String downloadableUri = IF_RestClient.getAbsoluteUrl(String.format(
                                    "/SyncExternal/GetRequestExternalAccess?iRequestID=%s&sRequestCode=%s", arrToken[0], arrToken[1]));
                            final String sEmail = CommonHelper.GetPreferenceString(if_login.this, "sEmail");
                            if (sEmail == null || sEmail.isEmpty()) {
                                CommonHelper.SavePreference(if_login.this, Constants.Settings.bKiosk, "1");
                            }

                            Intent intent = new Intent(if_login.this, if_HomeTab.class);
                            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                            intent.putExtra(Constants.Extras.sKioskUri, downloadableUri);
                            startActivity(intent);
                            return;
                        }
                    }
                    ShowAlert("Message", "We can not recognize your QR Code. Please try again. Please use your username and password to login if you are a SnapInspect account holder.");
                }
                break;
            case GOOGLE_SIGN_IN_RESPONSE:
                try {
                    Task<GoogleSignInAccount> task = GoogleSignIn.getSignedInAccountFromIntent(data);
                    if (!task.isCanceled()) {
                        GoogleSignInAccount account = task.getResult(ApiException.class);
                        String email = account.getEmail(),
                                sSavedEmail = CommonHelper.GetPreferenceString(if_login.this, Constants.Keys.sEmail);

                        String token = account.getIdToken();
                        if (!StringUtils.isEmpty(sSavedEmail) && !sSavedEmail.equals(email)) {
                            notifyUserToSwitchAccount(() -> authenticateWithToken(token, SSOLoginType.Google));
                        } else {
                            authenticateWithToken(token, SSOLoginType.Google);
                        }
                    }
                } catch (ApiException e) {
                    ShowAlert(R.string.title_alert_error, R.string.failed_login_with_google_sso);
                }
                break;
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        Button loginBtn = findViewById(R.id.btn_Login);
        if (loginBtn != null) loginBtn.setEnabled(true);
    }

    private void hideSoftKeyBoard() {
        InputMethodManager imm = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);

        if (imm.isAcceptingText()) { // verify if the soft keyboard is open
            imm.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
        }
    }

    private void ShowAlert(String sTitle, String sMessage) {
        CommonHelper.ShowAlert(sTitle, sMessage, this);
    }

    private void ShowAlert(@StringRes int iTitle, @StringRes int iMessage) {
        String sTitle = getResources().getString(iTitle), sMessage = getResources().getString(iMessage);
        CommonHelper.ShowAlert(sTitle, sMessage, this);
    }

    private void registerFCM() {
        FirebaseMessaging.getInstance().getToken().addOnCompleteListener(new OnCompleteListener<String>() {
            @Override
            public void onComplete(@NonNull Task<String> task) {
                if (!task.isSuccessful()) return;
                // Get new FCM registration token
                String token = task.getResult();
                String sTestToken = CommonHelper.GetPreferenceString(if_login.this, "sPushToken");
                final String sTestTokenID = CommonHelper.GetPreferenceString(if_login.this, "iPushTokenID");
                if (token != null && token.trim().length() > 0) {
                    if ((!token.equals(sTestToken)) && CommonHelper.GetPreferenceString(if_login.this, "iCustomerID") != null) {
                        CommonRequest.requestURL(if_login.this, "/IOAPI/UpdateToken", () -> {
                            RequestParams oParams = new RequestParams();
                            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(if_login.this, "iCustomerID"));
                            oParams.add("sToken", CommonHelper.GetPreferenceString(if_login.this, "sToken"));
                            oParams.add("sType", "Android");
                            oParams.add("sDeviceToken", token);
                            oParams.add("iDeviceTokenID", sTestTokenID);
                            return oParams;
                        }, (response, error) -> {
                            if (response != null) {
                                try {
                                    String sPushToken = response.getJSONObject("oPushToken").getString("sToken");
                                    String iTestTokenID = response.getJSONObject("oPushToken").getString("iPushTokenID");
                                    CommonHelper.SavePreference(if_login.this, "sPushToken", sPushToken);
                                    CommonHelper.SavePreference(if_login.this, "iPushTokenID", iTestTokenID);
                                } catch (JSONException e) {
                                    ai_BugHandler.ai_Handler_Exception(e);
                                }
                            }
                        });
                    }
                }
            }
        });
    }

    private void loginWithEmailPassword() {
        String sSavedEmail1 = CommonHelper.GetPreferenceString(if_login.this, "sEmail");
        String sSavedPassword1 = CommonHelper.GetPreferenceString(if_login.this, "sPassword");
        final String sEmail = ((EditText) findViewById(R.id.edit_Email)).getText().toString().trim();
        String sPassword = ((EditText) findViewById(R.id.edit_Password)).getText().toString().trim();

        if (sEmail.equalsIgnoreCase("") || sPassword.equalsIgnoreCase("")) {
            ShowAlert("Error", "Invalid Email or Password");
            return;
        }

        if (sEmail.equalsIgnoreCase(sSavedEmail1) && sPassword.equals(sSavedPassword1)) {
            goToHome();
            return;
        }

        if (!StringUtils.isEmpty(sSavedEmail1) && !sSavedEmail1.equals(sEmail)) {
            notifyUserToSwitchAccount(() -> authenticateWithEmail(sEmail, sPassword));
        } else {
            authenticateWithEmail(sEmail, sPassword);
        }
    }

    private void authenticateWithEmail(String sEmail, String sPassword) {
        Button loginButton = findViewById(R.id.btn_Login);
        loginButton.setEnabled(false);
        MaterialDialog progressDialog = CommonUI.ShowMaterialProgressDialog(if_login.this, "Message", "Please wait ...");
        CommonRequest.requestURL(this, "/Account/LoginAPI", () -> {
            RequestParams oParams = new RequestParams();
            oParams.add("UserName", sEmail);
            oParams.add("Password", sPassword);
            oParams.add("sDeviceInfo", "Android");
            oParams.add("sType", "Android");
            return oParams;
        }, (response, error) -> {
            loginButton.setEnabled(true);
            CommonUI.DismissMaterialProgressDialog(progressDialog);
            if (error != null) {
                ShowAlert("Error", error.getMessage());
            } else {
                handleLoginResponse(response);
            }
        });
    }

    private void loginWithGoogle() {
        Intent signInIntent = mGoogleSignInClient.getSignInIntent();
        startActivityForResult(signInIntent, GOOGLE_SIGN_IN_RESPONSE);
    }

    private void loginWithAzure() {
        if (mMultipleAccountApp == null) {
            ShowAlert(R.string.title_alert_error, R.string.failed_login_with_azure_sso);
            return;
        }

        AcquireTokenParameters parameters = new AcquireTokenParameters.Builder()
                .startAuthorizationFromActivity(this)
                .withScopes(Arrays.asList(SCOPES))
                .withPrompt(Prompt.SELECT_ACCOUNT)
                .withCallback(new AuthenticationCallback() {
                    @Override
                    public void onCancel() {

                    }

                    @Override
                    public void onSuccess(IAuthenticationResult authenticationResult) {
                        String email = authenticationResult.getAccount().getUsername();
                        String sSavedEmail = CommonHelper.GetPreferenceString(if_login.this, Constants.Keys.sEmail);

                        String token = authenticationResult.getAccount().getIdToken();
                        if (!StringUtils.isEmpty(sSavedEmail) && !sSavedEmail.equals(email)) {
                            notifyUserToSwitchAccount(() -> authenticateWithToken(token, SSOLoginType.Azure));
                        } else {
                            authenticateWithToken(token, SSOLoginType.Azure);
                        }
                    }

                    @Override
                    public void onError(MsalException exception) {
                        ShowAlert(R.string.title_alert_error, R.string.failed_login_with_azure_sso);
                    }
                })
                .build();

        mMultipleAccountApp.acquireToken(parameters);
    }

    private void authenticateWithToken(String sToken, SSOLoginType loginType) {
        if (StringUtils.isEmpty(sToken)) {
            ShowAlert("Error", loginType.failureMessage());
            return;
        }

        MaterialDialog progressDialog = CommonUI.ShowMaterialProgressDialog(
                this, "Message", "Connecting to Server");
        CommonRequest.requestURL(this, loginType.ssoAuthEndpoint(), () -> {
            RequestParams oParams = new RequestParams();
            oParams.add("sIDToken", sToken);
            oParams.add("sType", "Android");
            return oParams;
        }, (response, error) -> {
            CommonUI.DismissMaterialProgressDialog(progressDialog);
            if (error != null) {
                ShowAlert("Error", error.getMessage());
            } else {
                handleLoginResponse(response);
            }
        });
    }

    private void handleLoginResponse(JSONObject response) {
        try {
            CommonHelper.SavePreference(if_login.this, Constants.Settings.bAutoLogin, "true");
            String sSavedEmail = CommonHelper.GetPreferenceString(if_login.this, "sEmail");

            String sEmail = response.optString("sEmail");
            CommonHelper.SavePreference(if_login.this, "sEmail", sEmail);
            CommonHelper.SavePreference(if_login.this, "sPassword", ((EditText) findViewById(R.id.edit_Password)).getText().toString().trim());
            CommonHelper.SavePreference(if_login.this, "iCustomerID", response.optString("iCustomerID"));
            CommonHelper.SavePreference(if_login.this, "sToken", response.optString("sToken"));

            try {
                CommonHelper.SavePreference(if_login.this, "sHash", response.getString("sHash"));
            } catch (Exception ex) {
                //
            }
            new Handler(Looper.myLooper()).post(() -> {
                try {
                    if (StringUtils.isEmpty(sEmail) ||
                            (!StringUtils.isEmpty(sEmail) && !sEmail.equals(sSavedEmail))) {
                        CommonHelper.resetData(if_login.this);
                        CommonDB.InsertLog(if_login.this, "Login", "Login Success and Sync Start. " + sEmail);
                        CommonHelper.SavePreference(if_login.this, Constants.Settings.bKiosk, "0");
                        hideSoftKeyBoard();
                    }

                    String sHash = CommonHelper.GetPreferenceString(if_login.this, "sHash");
                    if (!StringUtils.isEmpty(sHash)) Intercom.client().setUserHash(sHash);
                } catch (Exception ex) {
                    ai_BugHandler.ai_Handler_Exception("Exception", "if_Login.handleLoginResponse", ex, null);
                }

                runOnUiThread(this::goToHome);
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_Login.RestClient.onSuccess", ex, null);
        }
    }

    private void notifyUserToSwitchAccount(Constants.OnConfirmListener listener) {
        new MaterialDialog.Builder(this)
                .title(R.string.alert_title_message)
                .content(R.string.alert_message_switch_account)
                .positiveText(R.string.alert_action_yes)
                .onPositive((dialog, which) -> {
                    if (listener != null) listener.onConfirm();
                })
                .negativeText(R.string.alert_action_no)
                .onNegative((dialog, which) -> {
                })
                .show();
    }

    enum SSOLoginType {
        Google, Azure;

        String failureMessage() {
            switch (this) {
                case Google:
                    return "Failed to login with Google account";
                case Azure:
                    return "Failed to login with Microsoft account";
                default:
                    return "Failed to login";
            }
        }

        String ssoAuthEndpoint() {
            switch (this) {
                case Google:
                    return "/IOAPI/GoogleSSOAuth_MobileApp";
                case Azure:
                    return "/IOAPI/MicrosoftSSOAuth_MobileApp";
                default:
                    return "";
            }
        }
    }
}