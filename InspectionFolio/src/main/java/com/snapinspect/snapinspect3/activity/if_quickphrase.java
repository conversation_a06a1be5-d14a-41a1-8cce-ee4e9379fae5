package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.content.Context;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.view.*;
import android.view.inputmethod.EditorInfo;
import android.widget.*;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_QuickPhrase;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.SimpleTextWatcher;
import com.snapinspect.snapinspect3.util.SystemUiHider;

import java.util.ArrayList;
import java.util.List;
import java.util.Timer;
import java.util.TimerTask;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_quickphrase extends Activity {

    private static final int SEARCH_DELAY = 300;

    private List<ai_QuickPhrase> lsQuickPhrase;
    private String sWords;
    private int iTextPosition;
    private int iPosition;
    private int iInsItemID;
    private ListView oListView;
    private EditText oSearch;
    private Ins_QP_Adapter adapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            setTheme(R.style.RemoveShadowActionBar);
            super.onCreate(savedInstanceState);
            setContentView(R.layout.activity_quickphrase);

            lsQuickPhrase = ai_QuickPhrase.listAll(ai_QuickPhrase.class);
            iTextPosition = getIntent().getIntExtra(Constants.Extras.iTextPosition, 0);
            iPosition = getIntent().getIntExtra(Constants.Extras.iPosition, 0);
            sWords = getIntent().getStringExtra(Constants.Extras.sWords);
            iInsItemID = getIntent().getIntExtra(Constants.Extras.iInsItemID, 0);

            oListView = findViewById(R.id.lv_quickphrase);
            oListView.setItemsCanFocus(false);
            oListView.setChoiceMode(ListView.CHOICE_MODE_MULTIPLE);

            adapter = new Ins_QP_Adapter(this, R.layout.cell_quickphrase_textview, lsQuickPhrase);
            oListView.setAdapter(adapter);

            getActionBar().setDisplayHomeAsUpEnabled(true);

            try {
                oSearch = findViewById(R.id.search_quickphrase);
                oSearch.addTextChangedListener(new SimpleTextWatcher() {

                    private Timer throttleTimer = new Timer();

                    @Override
                    public void afterTextChanged(Editable editable) {
                        throttleTimer.cancel();
                        throttleTimer = new Timer();
                        throttleTimer.schedule(new TimerTask() {
                            @Override
                            public void run() {
                                updateDateSetWithSearchText();
                            }
                        }, SEARCH_DELAY);

                    }

                });

                oSearch.setOnEditorActionListener((v, actionId, event) -> {
                    if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                        CommonHelper.hideSoftKeyboard(if_quickphrase.this);
                        return true;
                    }
                    return false;
                });
                // this.getActivity().getCurrentFocus().clearFocus();
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "if_quickphrase.main", ex, this);
            }

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_quickphrase.onCreate", ex, this);
        }
    }

    private void updateDateSetWithSearchText () {
        lsQuickPhrase = CommonDB.SearchPhrase(oSearch.getText().toString(), if_quickphrase.this);
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                adapter = new Ins_QP_Adapter(if_quickphrase.this, R.layout.cell_quickphrase_textview, lsQuickPhrase);
                oListView.setAdapter(adapter);
                adapter.notifyDataSetChanged();
            }
        });
    }

    private void saveSelectedPhrase() {

        ArrayList<String> items = new ArrayList<>();
        for (int i = 0; i < lsQuickPhrase.size(); i++) {
            ai_QuickPhrase oPhrase = lsQuickPhrase.get(i);
            if (oPhrase.sFieldThree != null && oPhrase.sFieldThree.equals("1")) {
                items.add(oPhrase.sComments);
            }
        }

        String selectedText = String.join(" ", items);

        String prefix = sWords.substring(0, iTextPosition);
        if (prefix.length() > 0 && !prefix.endsWith(" ")) {
            selectedText = " " + selectedText;
        }

        String suffix = sWords.substring(iTextPosition);
        if (suffix.length() > 0 && !suffix.startsWith(" ")) {
            selectedText += " ";
        }

        ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, (long)iInsItemID);
        String sResult = prefix + selectedText + suffix;
        CommonHelper.SetValue(iPosition, oInsItem, sResult);
        oInsItem.save();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        MenuInflater inflater = getMenuInflater();
        inflater.inflate(R.menu.menu_quickphrase, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        try {
            onBackPressed();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_quickphrase.onOptionsItemSelected", ex, this);
        }
        return true;
    }

    @Override
    public void onBackPressed() {
        saveSelectedPhrase();
        super.onBackPressed();
    }

    public class Ins_QP_Adapter extends ArrayAdapter<ai_QuickPhrase> {

        private final List<ai_QuickPhrase> lsQuickPhrase;
        private final Context oContext;

        public Ins_QP_Adapter(
                Context context,
                int textViewResourceId,
                List<ai_QuickPhrase> objects
        ) {
            super(context, textViewResourceId, objects);
            this.lsQuickPhrase = objects;
            this.oContext = context;
        }

        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            View view = convertView;
            try {
                if (view == null) {
                    view = ((LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE)).inflate(R.layout.cell_quickphrase_textview, null);
                }
                view.setBackgroundColor(Color.WHITE);
                TextView oTextView = view.findViewById(R.id.txt_quickphrase);
                oTextView.setText(lsQuickPhrase.get(position).sComments);
                oTextView.setTag(position);
                lsQuickPhrase.get(position).sFieldThree = "";

                oTextView.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        // TextView oTextView = ((TextView)view);
                        LinearLayout oView = (LinearLayout) view.getParent();
                        String sTag = view.getTag().toString();
                        int iCount = Integer.parseInt((sTag == null || sTag == "") ? "0" : sTag);

                        ai_QuickPhrase oPhrase = lsQuickPhrase.get(iCount);
                        if (oPhrase.sFieldThree == "") {
                            lsQuickPhrase.get(iCount).sFieldThree = "1";
                            oView.setBackgroundColor(Color.LTGRAY);
                        } else {
                            lsQuickPhrase.get(iCount).sFieldThree = "";
                            oView.setBackgroundColor(Color.WHITE);
                        }
                    }
                });
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "if_quickphrase.getView", ex, oContext);
            }

            return view;
        }

        @Override
        public long getItemId(int position) {
            return position;
        }

        @Override
        public boolean hasStableIds() {
            return true;
        }

    }

}

