package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Matrix;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.provider.MediaStore;
import android.util.DisplayMetrics;
import android.view.Display;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupMenu;
import android.widget.RelativeLayout;
import android.widget.SeekBar;
import android.widget.TextView;

import java.io.File;
import java.util.ArrayList;


import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.ImageAnnotation.DrawingView;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.SimpleTextWatcher;
import com.squareup.picasso.Picasso;

public class if_ImageAnnotation extends Activity {
    private static final int CAMERA_REQUEST = 1888;
    private static final int SELECT_PHOTO = 100;
    private static final int DEFAULT_WIDTH = 10;
    private static final String DEFAULT_TOOL = "Pen";
    private DrawingView drawingView;
    private Button wBtn;
    private Button penBtn;
    public String currentTool;
    public int currentWidth;
    public EditText inputText;
    public LinearLayout textEditView;
    public LinearLayout sliderView;
    public SeekBar seekSizeBar;
    public ArrayList<Integer> colorArray = new ArrayList<>();
    private int colorIndex = 0;
    private Bitmap curBitmap;
    private boolean isPhotoSelected;
    public ImageView imageView;
    public ai_Photo oPhoto;
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_if__image_annotation);

        getActionBar().setDisplayHomeAsUpEnabled(true);
        getActionBar().setTitle("Annotation");

            /*Color array Initialization*/
        colorArray.add(Color.RED);
        colorArray.add(Color.BLUE);
        colorArray.add(Color.YELLOW);
        colorArray.add(Color.GREEN);
        colorArray.add(Color.BLACK);
        colorArray.add(Color.WHITE);

        drawingView = findViewById(R.id.drawing);
        textEditView = findViewById(R.id.editLay);
        sliderView = findViewById(R.id.sliderLay);
        currentWidth = DEFAULT_WIDTH;
        wBtn = findViewById(R.id.widthV);
        wBtn.setText(String.valueOf(DEFAULT_WIDTH));
        currentTool = DEFAULT_TOOL;
        drawingView.setAction(currentTool);
        penBtn = findViewById(R.id.penBtn);
        penBtn.setText(DEFAULT_TOOL);


        inputText = findViewById(R.id.editText);
        inputText.addTextChangedListener(new SimpleTextWatcher() {
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                drawingView.setLabelText (s.toString());
            }
        });


        final TextView sizeView = findViewById(R.id.sizeLabel);

        seekSizeBar = findViewById(R.id.seekBar);
        seekSizeBar.setMax(190);
        seekSizeBar.setProgress(40);
        seekSizeBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {

            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                progress += 10;

                sizeView.setText(String.valueOf(progress));
                drawingView.setTextSize (progress);
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
            }

        });

        long iPhotoID = getIntent().getLongExtra(Constants.Extras.iPhotoID, 0);
        oPhoto = ai_Photo.findById(ai_Photo.class, iPhotoID);

        LoadPicture();
    }
    public void LoadPicture() {
        if (oPhoto == null) return;

        imageView = findViewById(R.id.imageView);
        Bitmap bitmap = BitmapFactory.decodeFile(oPhoto.getFile());
        curBitmap = bitmap;

        Display display = getWindowManager().getDefaultDisplay();
        int swidth = display.getWidth();
        ViewGroup.LayoutParams params = imageView.getLayoutParams();
        params.width = ViewGroup.LayoutParams.FILL_PARENT;
        params.height = swidth;

        imageView.setLayoutParams(params);
        drawingView.setLayoutParams(params);

        imageView.setImageBitmap(bitmap);
    }

    public void onUndoBtnClicked (View view) {
        drawingView.onClickUndo();
    }
    public void onRedoBtnClicked (View view) {
        drawingView.onClickRedo();
    }
    public void supportInvalidateOptionsMenu() {
       invalidateOptionsMenu();
    }
    public void toolBtnClicked (View view) {
        PopupMenu popup = new PopupMenu(this, view);
        //Inflating the Popup using xml file
        popup.getMenuInflater()
                .inflate(R.menu.pen_menu, popup.getMenu());

        //registering popup with OnMenuItemClickListener
        popup.setOnMenuItemClickListener(item -> {
            switch (item.getItemId()) {
                case R.id.id_pen:
                    //invalidateOptionsMenu();
                    supportInvalidateOptionsMenu();
                    penBtn.setText("Pen");
                    currentTool = "Pen";
                    drawingView.setAction(currentTool);

                    return true;
                case R.id.id_rect:
                    supportInvalidateOptionsMenu();
                    penBtn.setText("Rect");
                    currentTool = "Rect";
                    drawingView.setAction(currentTool);

                    return true;
                case R.id.id_oval:
                    supportInvalidateOptionsMenu();
                    penBtn.setText("Oval");
                    currentTool = "Oval";
                    drawingView.setAction(currentTool);

                    return true;
                case R.id.id_line:
                    supportInvalidateOptionsMenu();
                    penBtn.setText("Line");
                    currentTool = "Line";
                    drawingView.setAction(currentTool);

                    return true;
                case R.id.id_arrow:
                    supportInvalidateOptionsMenu();
                    penBtn.setText("Arrow");
                    currentTool = "Arrow";
                    drawingView.setAction(currentTool);

                    return true;
                case R.id.id_text:
                    supportInvalidateOptionsMenu();
                    penBtn.setText("Text");
                    currentTool = "Text";
                    drawingView.setAction(currentTool);

                    return true;
                default:
                    return false;
            }
        });
        popup.show(); //showing popup menu
    }

    public void onWidthBtnClicked (View v) {
        PopupMenu popup = new PopupMenu(this, v);
        //Inflating the Popup using xml file
        popup.getMenuInflater()
                .inflate(R.menu.width_menu, popup.getMenu());

        //registering popup with OnMenuItemClickListener
        popup.setOnMenuItemClickListener(item -> {
            switch (item.getItemId()) {
                case R.id.id_fine:
                    supportInvalidateOptionsMenu();
                    wBtn.setText("1");
                    currentWidth = 1;
                    drawingView.setLineWidth(currentWidth);
                    return true;
                case R.id.id_thin:
                    supportInvalidateOptionsMenu();
                    wBtn.setText("5");
                    currentWidth = 5;
                    drawingView.setLineWidth(currentWidth);

                    return true;
                case R.id.id_medium:
                    supportInvalidateOptionsMenu();
                    wBtn.setText("10");
                    currentWidth = 10;
                    drawingView.setLineWidth(currentWidth);

                    return true;
                case R.id.id_thick:
                    supportInvalidateOptionsMenu();
                    wBtn.setText("15");
                    currentWidth = 15;
                    drawingView.setLineWidth(currentWidth);

                    return true;
                case R.id.id_vthick:
                    supportInvalidateOptionsMenu();
                    wBtn.setText("20");
                    currentWidth = 20;
                    drawingView.setLineWidth(currentWidth);

                    return true;

                default:
                    return false;
            }
        });

        popup.show(); //showing popup menu
    }

    public void colorBtnClicked (View v) {
        ImageButton colorBtn = findViewById(R.id.imageStroke);
        colorIndex++;
        if (colorIndex >= colorArray.size())
            colorIndex = 0;
        colorBtn.setBackgroundColor(colorArray.get(colorIndex));
        drawingView.setLineColor(colorArray.get(colorIndex));
    }

    public void fillColorBtnClicked (View v) {
        ImageButton colorBtn = findViewById(R.id.imageFill);
        colorIndex++;
        if (colorIndex == colorArray.size()) {
            colorBtn.setBackgroundResource(R.drawable.transparency);
            drawingView.setFillColor(0, false);
        } else {
            if (colorIndex >= colorArray.size()) {
                colorIndex = 0;
            }
            colorBtn.setBackgroundColor(colorArray.get(colorIndex));
            drawingView.setFillColor(colorArray.get(colorIndex), true);
        }
    }
    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_annotation, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        int id = item.getItemId();
        //noinspection SimplifiableIfStatement
        switch (id) {
            case R.id.camBtn:
                Intent cameraIntent = new Intent(android.provider.MediaStore.ACTION_IMAGE_CAPTURE);
                startActivityForResult(cameraIntent, CAMERA_REQUEST);

                break;
            case R.id.phoBtn:
                Intent photoPickerIntent = new Intent(Intent.ACTION_PICK);
                photoPickerIntent.setType("image/*");
                startActivityForResult(photoPickerIntent, SELECT_PHOTO);

                break;
            case R.id.action_clear:
                drawingView.reset();
                break;
            case android.R.id.home:
                onBackPressed();
                break;
            case R.id.action_annotation_save:
                drawingView.saveAnnotation();
                this.saveImage();
                onBackPressed();
                break;
            default:
                break;
        }

        return super.onOptionsItemSelected(item);
    }

    public void saveImage() {
        if (oPhoto == null) return;

        drawingView.setDrawingCacheEnabled(true);
        drawingView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);
        Bitmap frontBm = drawingView.getDrawingCache();
        Bitmap backBm;
        if (isPhotoSelected) {
            backBm = curBitmap;
        } else {
            imageView.setDrawingCacheEnabled(true);
            imageView.setDrawingCacheQuality(View.DRAWING_CACHE_QUALITY_HIGH);
            backBm = imageView.getDrawingCache();
        }
        Bitmap bm = overlay(backBm, frontBm);
        try {
            CommonHelper.SaveImage(oPhoto.getFile(), bm);
            CommonHelper.SaveThumb(oPhoto.getThumb(), bm);

            Picasso.get().invalidate(new File(oPhoto.getFile()));
            Picasso.get().invalidate(new File(oPhoto.getThumb()));
        } catch(Exception ex) {
            ex.printStackTrace();
        }
    }

    public static Bitmap getResizedBitmap(Bitmap bm, int newHeight, int newWidth) {

        int width = bm.getWidth();

        int height = bm.getHeight();

        float scaleWidth = ((float) newWidth) / width;

        float scaleHeight = ((float) newHeight) / height;

        Matrix matrix = new Matrix();

        matrix.postScale(scaleWidth, scaleHeight);

        return Bitmap.createBitmap(bm, 0, 0, width, height, matrix, false);

    }

    public static Bitmap overlay(Bitmap bmp1, Bitmap bmp2) {
        Bitmap bmOverlay = Bitmap.createBitmap(bmp1.getWidth(), bmp1.getHeight(), bmp1.getConfig());
        Bitmap newBm = getResizedBitmap (bmp2, bmp1.getWidth(), bmp1.getHeight());

        Canvas canvas = new Canvas(bmOverlay);
        canvas.drawBitmap(bmp1, new Matrix(), null);
        canvas.drawBitmap(newBm, 0, 0, null);
        return bmOverlay;
    }
/*
    public static void addImageToGallery(final String filePath, final Context context) {

        ContentValues values;
        values = new ContentValues();

        values.put(MediaStore.Images.Media.DATE_TAKEN, System.currentTimeMillis());
        values.put(MediaStore.Images.Media.MIME_TYPE, "image/jpeg");
        values.put(MediaStore.MediaColumns.DATA, filePath);

        context.getContentResolver().insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, values);
        Toast.makeText(context, "Image is saved successfully.", Toast.LENGTH_SHORT).show();
    }
*/
    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent imageReturnedIntent) {
        super.onActivityResult(requestCode, resultCode, imageReturnedIntent);

        imageView = this.findViewById(R.id.imageView);

        switch (requestCode) {
            case SELECT_PHOTO:
                if (resultCode == RESULT_OK) {
                    Uri selectedImage = imageReturnedIntent.getData();
                    if (selectedImage == null) return;
                    drawingView.reset();

                    String[] filePath = { MediaStore.Images.Media.DATA };
                    Cursor cursor = getContentResolver().query(selectedImage, filePath, null, null, null);
                    if (cursor != null) {
                        cursor.moveToFirst();
                        String imagePath = cursor.getString(cursor.getColumnIndex(filePath[0]));

                        // Now we need to set the GUI ImageView data with data read from the picked file.
                        Bitmap bitmap = BitmapFactory.decodeFile(imagePath);
                        // At the end remember to close the cursor or you will end with the RuntimeException!
                        cursor.close();

                        BitmapDrawable ob = new BitmapDrawable(getResources(), bitmap);
                        curBitmap = bitmap;
                        isPhotoSelected = true;
                        // set drawingview size
                        float width = bitmap.getWidth();
                        float height = bitmap.getHeight();
                        DisplayMetrics displaymetrics = new DisplayMetrics();
                        getWindowManager().getDefaultDisplay().getMetrics(displaymetrics);
                        int sHeight = displaymetrics.heightPixels;
                        int sWidth = displaymetrics.widthPixels;

                        float rate = (sHeight / height > sWidth / width && sWidth > width) ? sHeight / height : sWidth / width;

                        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) drawingView.getLayoutParams();
                        params.width = (int) (width * rate);
                        params.height = (int) (height * rate);
                        drawingView.setLayoutParams(params);

                        // set imageview size
                        RelativeLayout.LayoutParams vparams = (RelativeLayout.LayoutParams) imageView.getLayoutParams();
                        vparams.width = (int) (width * rate);
                        vparams.height = (int) (height * rate);
                        imageView.setLayoutParams(vparams);

                        if (Build.VERSION.SDK_INT >= 16) {
                            imageView.setBackground(ob);
                        } else {
                            imageView.setBackgroundDrawable(ob);
                        }
                    }

                }
                break;
            case CAMERA_REQUEST:
                if (resultCode == RESULT_OK) {
                    drawingView.reset();

                    Bitmap photo = (Bitmap) imageReturnedIntent.getExtras().get("data");
                    if (photo != null) {
                        BitmapDrawable ob = new BitmapDrawable(getResources(), photo);

                        float width = photo.getWidth();
                        float height = photo.getHeight();
                        DisplayMetrics displaymetrics = new DisplayMetrics();
                        getWindowManager().getDefaultDisplay().getMetrics(displaymetrics);
                        int sHeight = displaymetrics.heightPixels;
                        int sWidth = displaymetrics.widthPixels;

                        // set drawingview size
                        RelativeLayout.LayoutParams params = (RelativeLayout.LayoutParams) drawingView.getLayoutParams();
                        float rate = (sHeight / height > sWidth / width && sWidth > width) ? sHeight / height : sWidth / width;
                        params.width = (int) (width * rate);
                        params.height = (int) (height * rate);
                        drawingView.setLayoutParams(params);

                        // Now change ImageView's dimensions to match the scaled image
                        RelativeLayout.LayoutParams vparams = (RelativeLayout.LayoutParams) imageView.getLayoutParams();
                        vparams.width = (int) (width * rate);
                        vparams.height = (int) (height * rate);
                        imageView.setLayoutParams(vparams);


                        isPhotoSelected = false;
                        if (Build.VERSION.SDK_INT >= 16) {
                            imageView.setBackground(ob);
                        } else {
                            imageView.setBackgroundDrawable(ob);
                        }
                    }
                }
        }
    }

    public void boldBtnClicked (View v) {
        if (sliderView.getVisibility() == View.VISIBLE)
            sliderView.setVisibility(View.INVISIBLE);
        drawingView.setBoldText();
    }

    public void italicBtnClicked (View v) {
        if (sliderView.getVisibility() == View.VISIBLE)
            sliderView.setVisibility(View.INVISIBLE);
        drawingView.setItalicText();
    }

    public void underBtnClicked(View v){
        if (sliderView.getVisibility() == View.VISIBLE)
            sliderView.setVisibility(View.INVISIBLE);
        drawingView.setUnderLineText();
    }

    public void trashBtnClicked(View v){
        if (sliderView.getVisibility() == View.VISIBLE)
            sliderView.setVisibility(View.INVISIBLE);
        drawingView.trash();
    }

    public void fontBtnClicked (View v) {

        if (sliderView.getVisibility() == View.VISIBLE)
            sliderView.setVisibility(View.INVISIBLE);
        else
            sliderView.setVisibility(View.VISIBLE);
    }

    public void hiddenKeyBoard() {
        if(getCurrentFocus()!=null) {
            InputMethodManager inputMethodManager = (InputMethodManager) getSystemService(INPUT_METHOD_SERVICE);
            inputMethodManager.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
        }
    }

    public  void showKeyBoard() {
        inputText.requestFocus();
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        imm.showSoftInput(inputText, InputMethodManager.SHOW_IMPLICIT);
    }
}

