package com.snapinspect.snapinspect3.activity;

import android.Manifest;
import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.PixelFormat;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.hardware.camera2.CameraCharacteristics;
import android.hardware.camera2.CaptureRequest;
import android.hardware.display.DisplayManager;
import android.location.Location;
import android.net.Uri;
import android.os.Bundle;
import android.os.Environment;
import android.os.Handler;
import android.os.Looper;
import android.provider.MediaStore;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.Range;
import android.util.Size;
import android.view.*;
import android.view.ScaleGestureDetector.SimpleOnScaleGestureListener;
import android.widget.*;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.PickVisualMediaRequest;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.camera.camera2.interop.Camera2Interop;
import androidx.camera.core.*;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.view.PreviewView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.fragment.app.FragmentTransaction;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.github.ybq.android.spinkit.SpinKitView;
import com.google.common.util.concurrent.ListenableFuture;
import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.SI_DB.db_Inspection;
import com.snapinspect.snapinspect3.activitynew.fragments.EditPhotoFragment;
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayPhoto;
import com.snapinspect.snapinspect3.activitynew.camera.CameraUtils;
import com.snapinspect.snapinspect3.activitynew.camera.FocusView;
import com.snapinspect.snapinspect3.util.*;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static androidx.camera.core.CameraSelector.LENS_FACING_BACK;
import static androidx.camera.core.CameraSelector.LENS_FACING_FRONT;
import static com.snapinspect.snapinspect3.Helper.Constants.Values.DEFAULT_IMAGE_SIZE;

public class if_CameraX extends AppCompatActivity implements SensorEventListener  {

    public enum CameraSaveOption {
        FILE, PHOTO;

        public static CameraSaveOption fromOrdinal(int x) {
            return CameraSaveOption.values()[x];
        }

        public boolean shouldSaveAsPhoto() {
            return this == CameraSaveOption.PHOTO;
        }
    }

    private static final String TAG = if_CameraX.class.getSimpleName();
    private static final int REQUEST_PERMISSIONS = 0x000001;

    private PreviewView previewView;
    // photo size, which might be from ai_InsType.sFieldOne, default is DEFAULT_IMAGE_SIZE
    private Size mPhotoSize = DEFAULT_IMAGE_SIZE;

    private DisplayManager displayManager;
    private ImageAnalysis imageAnalysis;
    private ImageCapture imageCapture;
    private ExecutorService cameraExecutor;
    private Camera camera;

    private ImageView imageView;
    private ImageView lightBtn;
    private FrameLayout libraryBtn;
    private Button doneButton;
    private Button shutterBtn;
    private View switchBtn;
    private SpinKitView loadingView;
    private FocusView focusView;
    private FrameLayout seekView;
    private SeekBar seekBar;
    private TextView zoomLevelTextView;

    private SensorManager mSensorManager;
    private Sensor mOrientationSensor;

    private int displayId = -1;
    private int mOrientation = 0;
    private int lensFacing = LENS_FACING_BACK;

    private long iPhotoId;
    private long iFileId;
    private int iInsItemID;
    private int iPosition;
    private int iSAssetID;
    private boolean bNoticePhotos;
    private CameraSaveOption oSaveOption;
    private int iMaxPhotoCount;
    private ActivityResultLauncher<PickVisualMediaRequest> mImagePicker;

    public static Intent newIntent(Context context, int iInsItemID, int iPosition, boolean bNoticePhotos,
                                   int iSAssetID, CameraSaveOption saveOption, int iMaxPhotoCount) {
        Intent intent = new Intent(context, if_CameraX.class);
        intent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        intent.putExtra(Constants.Extras.iPosition, iPosition);
        intent.putExtra(Constants.Extras.bNoticePhotos, bNoticePhotos);
        intent.putExtra(Constants.Extras.iSAssetID, iSAssetID);
        intent.putExtra(Constants.Extras.oSaveOption, saveOption.ordinal());
        intent.putExtra(Constants.Extras.I_MAX_PHOTO_COUNT, iMaxPhotoCount);
        return intent;
    }

    public static Intent newIntent(Context context, CameraSaveOption saveOption, int iInsItemID, int iPosition, boolean bNoticePhotos, int iMaxPhotoCount) {
        return newIntent(context, iInsItemID, iPosition, bNoticePhotos, 0, saveOption, iMaxPhotoCount);
    }

    public static Intent newIntent(Context context, CameraSaveOption saveOption, int iSAssetID, int iMaxPhotoCount) {
        return newIntent(context, 0, 0, false, iSAssetID, saveOption, iMaxPhotoCount);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setupIntentData();
        setupViews();
        registerImagePicker();

        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        getWindow().setFormat(PixelFormat.TRANSLUCENT);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        displayManager = (DisplayManager) getSystemService(Context.DISPLAY_SERVICE);

        //background executor
        cameraExecutor = Executors.newSingleThreadExecutor();
        displayManager.registerDisplayListener(displayListener, null);

        mSensorManager = (SensorManager)getSystemService(SENSOR_SERVICE);
        mOrientationSensor = mSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER);

        if (!hasPermissions()) {
            String[] permissions = new String[]{Manifest.permission.CAMERA};
            ActivityCompat.requestPermissions(this, permissions, REQUEST_PERMISSIONS);
        } else {
            previewView.post(this::startCamera);
        }

        // request write_external_storage permission if needed
        CommonValidate.validateWriteExternalStoragePermission(this, REQUEST_PERMISSIONS);
    }

    /**
     * Sets up the intent data by retrieving values from the intent extras.
     */
    private void setupIntentData() {
        //Getting values
        iInsItemID = getIntent().getIntExtra(Constants.Extras.iInsItemID, 0);
        iPosition = getIntent().getIntExtra(Constants.Extras.iPosition, 0);
        bNoticePhotos = getIntent().getBooleanExtra(Constants.Extras.bNoticePhotos, false);
        iSAssetID = getIntent().getIntExtra(Constants.Extras.iSAssetID, 0);
        oSaveOption = CameraSaveOption.fromOrdinal(getIntent().getIntExtra(Constants.Extras.oSaveOption, 0));
        iMaxPhotoCount = getIntent().getIntExtra(Constants.Extras.I_MAX_PHOTO_COUNT, Constants.Limits.iSinglePhotoLimit);
        mPhotoSize = getPhotoSizeFromInsItemID(iInsItemID);
    }

    private Size getPhotoSizeFromInsItemID(int iInsItemID) {
        int insTypePhotoSize = db_InsItem.getInsTypePhotoSize(iInsItemID);
        return insTypePhotoSize > 0 ? new Size(insTypePhotoSize, insTypePhotoSize) : DEFAULT_IMAGE_SIZE;
    }

    /**
     * Registers an image picker for the current activity.
     */
    private void registerImagePicker() {
        if (isSingleSelection()) {
            mImagePicker = registerForActivityResult(new ActivityResultContracts.PickVisualMedia(),
                result -> { if (result != null) showsEditPhotoFragment(result); }
            );
        } else {
            mImagePicker = registerForActivityResult(
                new ActivityResultContracts.PickMultipleVisualMedia(iMaxPhotoCount),
                this::handleMultipleImageResults
            );
        }
    }
    
    private void handleMultipleImageResults(List<Uri> results) {
        if (results == null || results.isEmpty()) return;
        
        if (results.size() > 1) {
            processMultipleImages(results);
        } else {
            showsEditPhotoFragment(results.get(0));
        }
    }
    
    private void processMultipleImages(List<Uri> results) {
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            try {
                for (Uri imageUri : results) {
                    processImageUri(imageUri);
                }
            } catch (Exception e) {
                Log.e(TAG, "Error processing image URIs", e);
            }
        });
        executor.shutdown();
    }

    private void processImageUri(Uri imageUri) {
        Bitmap oBitmap = BitmapUtils.decodeBitmap(this,
                imageUri, mPhotoSize.getWidth(), mPhotoSize.getHeight());
        Bitmap mergedBitmap = parseBitmap(oBitmap, mPhotoSize);
        Bitmap bitmap = BitmapUtils.appendTextsIfNeed(if_CameraX.this,
                BitmapUtils.fixCameraRotationIfNeed(if_CameraX.this, mergedBitmap),
                textsOnPhoto()
        );
        saveImageToLibraryIfNeed(bitmap);
        saveImage(bitmap);
    }

    /**
     * Sets up the views
     */
    private void setupViews() {
        setContentView(R.layout.activity_if_camerax);
        imageView = findViewById(R.id.imageView);
        previewView = findViewById(R.id.viewFinder);

        doneButton = findViewById(R.id.doneBtn);
        switchBtn = findViewById(R.id.switchBtn);
        switchBtn.setOnClickListener(this::switchClicked);

        libraryBtn = findViewById(R.id.libraryBtn);
        lightBtn = findViewById(R.id.lightBtn);
        shutterBtn = findViewById(R.id.shutterBtn);

        loadingView = findViewById(R.id.loading);
        focusView = findViewById(R.id.focusView);
        seekView = findViewById(R.id.seekView);

        seekBar = findViewById(R.id.seekBar);
        seekBar.setOnSeekBarChangeListener(seekBarChangeListener);

        imageView.setOnClickListener(v -> viewPhoto());
        findViewById(R.id.lightBtnView).setOnClickListener(v -> toggleFlash());
        findViewById(R.id.brightBtnView).setOnClickListener(v -> adjustBrightness());

        zoomLevelTextView = findViewById(R.id.zoomLevelText);

        // set the default zoom level
        ai_InsItem oInsItem = db_InsItem.GetInsItem_ByID(iInsItemID);
        if (oInsItem != null) {
            TextView insItemNameTextView = findViewById(R.id.inspection_item_name);
            TextViewUtils.updateText(insItemNameTextView, oInsItem.sName);
        }

        if (!CommonHelper.fixCameraIsDisabled(this)) { rotateControl(0); }
    }

    @Override
    protected void onResume() {
        super.onResume();
        GeoLocationManager.getInstance(this).startUpdateLocation();
        mSensorManager.registerListener(this, mOrientationSensor, SensorManager.SENSOR_DELAY_NORMAL);
    }

    @Override
    protected void onPause() {
        super.onPause();
        mSensorManager.unregisterListener(this);
        GeoLocationManager.getInstance(this).stopUpdateLocation();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        cameraExecutor.shutdown();
        displayManager.unregisterDisplayListener(displayListener);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (event.getKeyCode() == KeyEvent.KEYCODE_VOLUME_DOWN) {
            shutterClicked(null);
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode != REQUEST_PERMISSIONS) return;
        if (hasPermissions()) {
            previewView.post(this::startCamera);
        } else {
            finish();
        }
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        int ORIENTATION_UNKNOWN = -1;
        int _DATA_X = 0;
        int _DATA_Y = 1;
        int _DATA_Z = 2;

        if (event.sensor.getType() == Sensor.TYPE_ACCELEROMETER) {
            float[] values = event.values;
            int orientation = ORIENTATION_UNKNOWN;
            float X = -values[_DATA_X];
            float Y = -values[_DATA_Y];
            float Z = -values[_DATA_Z];
            float magnitude = X * X + Y * Y;
            if (magnitude * 4 >= Z * Z) {
                float OneEightyOverPi = 57.29577957855f;
                float angle = (float) Math.atan2(-Y, X) * OneEightyOverPi;
                orientation = 90 - Math.round(angle);
                // normalize to 0 - 359 range
                orientation = compensateOrientation(orientation);
                while (orientation >= 360) {
                    orientation -= 360;
                }
                while (orientation < 0) {
                    orientation += 360;
                }
            }

            int orientationOffset = Math.abs(orientation - mOrientation);
            if (Math.abs(orientationOffset - 90.0) < 10 || Math.abs(orientationOffset - 270) < 10) {
                int generalOrientation = getGeneralOrientation(orientation);
                if (generalOrientation != -1 && generalOrientation != mOrientation) {
                    mOrientation = generalOrientation;
                } else {
                    mOrientation = 0;
                }
                rotateControl(360 - generalOrientation);
            }
        }
    }

    private int compensateOrientation(int degrees){
        Display display = getWindowManager().getDefaultDisplay();
        switch(display.getRotation()){
            case(Surface.ROTATION_270):
                return degrees + 270;
            case(Surface.ROTATION_180):
                return degrees + 180;
            case(Surface.ROTATION_90):
                return degrees + 90;
            default:
                return degrees;
        }
    }

    private static int getGeneralOrientation(int degrees){
        if(degrees >= 330 || degrees <= 30 ) return 0;
        if(degrees <= 300 && degrees >= 240) return 270;
        if(degrees <= 210 && degrees >= 160) return 180;
        if(degrees <= 120 && degrees >= 60) return 90;
        return -1;
    }

    public void onAccuracyChanged(Sensor sensor, int accuracy) { }

    private void startCamera() {
        ListenableFuture<ProcessCameraProvider> cameraProviderFuture;
        // Gets the display id.
        displayId = previewView.getDisplay().getDisplayId();

        //Request a CameraProvider
        cameraProviderFuture = ProcessCameraProvider.getInstance(this);
        //Check for CameraProvider availability
        cameraProviderFuture.addListener(() -> {
            try {
                ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                //Unbind use cases before rebinding
                cameraProvider.unbindAll();
                bindCameraUseCases(cameraProvider);
                restoreCameraZoomRatio();
                ZoomState zoomState = camera.getCameraInfo().getZoomState().getValue();
                if (zoomState != null)
                    updateZoomLevelTextView(zoomState.getZoomRatio());
            } catch (ExecutionException | InterruptedException e) {
                // No errors need to be handled for this Future.
                // This should never be reached.
            }
        }, ContextCompat.getMainExecutor(this));
    }

    @SuppressLint("RestrictedApi")
    private void bindCameraUseCases(@NonNull ProcessCameraProvider cameraProvider) {
        DisplayMetrics metrics = new DisplayMetrics();
        previewView.getDisplay().getRealMetrics(metrics);
        int rotation = previewView.getDisplay().getRotation();

        // Capture photo aspect radio 1:1
        //preview
        Preview.Builder previewBuilder = new Preview.Builder().setTargetRotation(rotation);
        setBrightness(previewBuilder, getMBrightness());
        Preview preview = previewBuilder.build();

        //imageAnalysis
        imageAnalysis = new ImageAnalysis.Builder()
                .setTargetRotation(rotation)
                .build();

        //imageCapture
        imageCapture = new ImageCapture.Builder()
                .setCaptureMode(ImageCapture.CAPTURE_MODE_MINIMIZE_LATENCY)
                .setTargetResolution(mPhotoSize)
                .setTargetRotation(rotation)
                .build();

        CameraSelector wideAngleCamera = CommonUI.getWideAngleCameraSelector(cameraProvider);
        //Select Camera
        CameraSelector cameraSelector = wideAngleCamera != null ? wideAngleCamera : getDefaultCameraSelector();
        preview.setSurfaceProvider(previewView.getSurfaceProvider());

        try {
            camera = cameraProvider.bindToLifecycle(this,
                cameraSelector, preview, imageCapture, imageAnalysis);

            checkForFlashAvailability();
            enableZoomAndFocusFeatures();
            setEnableTakePicture(true);
        } catch (Exception ex) {
            Log.e(TAG, "Fail to bind camera!");
        }

    }

    private CameraSelector getDefaultCameraSelector() {
        return lensFacing == LENS_FACING_BACK ? CameraSelector.DEFAULT_BACK_CAMERA : CameraSelector.DEFAULT_FRONT_CAMERA;
    }

    private void rotateControl(int angle) {
        if (Constants.FixCameraOption.portrait.equals(CommonHelper.fixCameraOption(this))) {
            angle = 0; // when portrait, done in portrait, and thumbnail in portrait.
        } else if (Constants.FixCameraOption.landscape.equals(CommonHelper.fixCameraOption(this))) {
            angle = 90; // when landscape, done in landscape, and thumbnail in landscape.
        }

        // when disabled, then button, and thumbnail rotate accordingly.
        doneButton.setRotation(angle);
        switchBtn.setRotation(angle);
        lightBtn.setRotation(angle);
        libraryBtn.setRotation(angle);
        imageView.setRotation(angle);
    }

    private final DisplayManager.DisplayListener displayListener = new DisplayManager.DisplayListener() {
        @Override
        public void onDisplayAdded(int displayId) {
        }

        @Override
        public void onDisplayRemoved(int displayId) {
        }

        @Override
        public void onDisplayChanged(int displayId) {
            if (displayId != if_CameraX.this.displayId) return;
            try {
                View rootView = getWindow().getDecorView().findViewById(android.R.id.content);
                int rotation = rootView.getDisplay().getRotation();
                imageCapture.setTargetRotation(rotation);
                imageAnalysis.setTargetRotation(rotation);
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception(ex);
            }
        }
    };

    /**
     * Sets the visibility of the seek view and updates the progress of the seek bar.
     * @param isShowing true to show the seek view, false to hide it
     */
    private void showsSeekView(boolean isShowing) {
        seekView.setVisibility(isShowing ? View.VISIBLE : View.GONE);
        seekBar.setProgress(getMBrightness());
    }

    private final SeekBar.OnSeekBarChangeListener seekBarChangeListener = new SeekBar.OnSeekBarChangeListener() {
        @Override
        public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
            if (fromUser && progress != getMBrightness()) {
                CommonHelper.SavePreference(if_CameraX.this, Constants.Keys.sBrightness, progress + "");
                previewView.postDelayed(() -> startCamera(), 200);
            }
        }

        @Override
        public void onStartTrackingTouch(SeekBar seekBar) {
        }

        @Override
        public void onStopTrackingTouch(SeekBar seekBar) {
        }
    };

    private int getMBrightness() {
        return CommonHelper.getInt(
            CommonHelper.GetPreferenceString(this, Constants.Keys.sBrightness, "50")
        );
    }

    private void setBrightness(ExtendableBuilder builder, int value) {
        try {
            Camera2Interop.Extender extender = new Camera2Interop.Extender(builder);
            Range<Integer> compensationRange = CameraUtils.getCameraCharacteristics(
                    if_CameraX.this, lensFacing).get(CameraCharacteristics.CONTROL_AE_COMPENSATION_RANGE);
            if (compensationRange != null) {
                int minCompensationRange = compensationRange.getLower();
                int maxCompensationRange = compensationRange.getUpper();

                int brightness = (int) (minCompensationRange + (maxCompensationRange - minCompensationRange) * (value / 100f));
                extender.setCaptureRequestOption(CaptureRequest.CONTROL_AE_EXPOSURE_COMPENSATION, brightness);
                extender.setCaptureRequestOption(CaptureRequest.CONTROL_AE_LOCK, false);
            }
        } catch (Exception ex) {
            //
        }
    }

    private void checkForFlashAvailability() {
        boolean isFlashAvailable = camera.getCameraInfo().hasFlashUnit();
        lightBtn.setVisibility(isFlashAvailable ? View.VISIBLE : View.GONE);
    }

    @SuppressLint({"ClickableViewAccessibility", "RestrictedApi"})
    private void enableZoomAndFocusFeatures() {
        ScaleGestureDetector scaleGestureDetector = new ScaleGestureDetector(this, new SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                ZoomState zoomState = camera.getCameraInfo().getZoomState().getValue();
                if (zoomState != null) {
                    float value = Math.min(
                        zoomState.getZoomRatio() * detector.getScaleFactor(),
                        Constants.Limits.iMaxiumVideoLevel
                    );
                    camera.getCameraControl().setZoomRatio(value);
                    updateZoomLevelTextView(value);
                    CommonHelper.SavePreference(
                        if_CameraX.this, Constants.Keys.sZoomLevel,  zoomState.getZoomRatio() + "");
                }
                return true;
            }
        });

        previewView.setOnTouchListener((v, event) -> {
            scaleGestureDetector.onTouchEvent(event);
            if (!scaleGestureDetector.isInProgress()) {
                int action = event.getAction() & MotionEvent.ACTION_MASK;
                if (action == MotionEvent.ACTION_UP) {
                    float previewViewTop = findViewById(R.id.preview_top_view).getBottom();
                    setFocusViewWidthAnimation(event.getX(), event.getY() + previewViewTop);
                    showsSeekView(false);
                }
            }
            return true;
        });

        seekView.post(() -> {
            FrameLayout.LayoutParams layoutParams = (FrameLayout.LayoutParams) seekView.getLayoutParams();
            layoutParams.topMargin = findViewById(R.id.preview_bottom_view).getTop();
            seekView.setLayoutParams(layoutParams);
        });

        //  set the focus point in the center as default
        new Handler(Looper.getMainLooper()).postDelayed(() -> setFocusViewWidthAnimation(
                previewView.getWidth() >> 1,
                previewView.getTop() + previewView.getHeight() >> 1
        ), 1000);
    }

    private void updateZoomLevelTextView(float zoomRatio) {
        // keep the value only has one decimal place
        zoomLevelTextView.setText(getString(R.string.camera_zoom_level_value, String.format("%.1f", zoomRatio)));
    }

    private void restoreCameraZoomRatio() {
        String sZoomLevel = CommonHelper.GetPreferenceString(this, Constants.Keys.sZoomLevel, "1.0");
        float zoomRatio = CommonHelper.getFloat(sZoomLevel);
        camera.getCameraControl().setZoomRatio(zoomRatio);
    }

    private void toggleFlash() {
        try {
            boolean enable = false;
            Integer state = camera.getCameraInfo().getTorchState().getValue();
            if (state != null) enable = state == TorchState.OFF;
            camera.getCameraControl().enableTorch(enable);
            lightBtn.setImageResource(enable ? R.drawable.flash_on : R.drawable.flash_off);
        } catch (Exception ex) {
            Log.e(TAG, "Fail to toggle torch state");
        }
    }

    private void adjustBrightness() {
        int visibility = seekView.getVisibility();
        switch (visibility) {
            case View.VISIBLE:
                showsSeekView(false);
                break;
            case View.GONE:
                showsSeekView(true);
                break;
            default:
                break;
        }
    }

    private boolean hasPermissions() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED;
    }

    private void setFocusViewWidthAnimation(float x, float y) {
        MeteringPointFactory factory = previewView.getMeteringPointFactory();
        MeteringPoint point = factory.createPoint(x, y);
        FocusMeteringAction meteringAction = new FocusMeteringAction.Builder(point).build();
        camera.getCameraControl().startFocusAndMetering(meteringAction);

        focusView.setVisibility(View.VISIBLE);

        focusView.setX(x - (focusView.getWidth() >> 1));
        focusView.setY(y - (focusView.getHeight() >> 1));

        ObjectAnimator scaleX = ObjectAnimator.ofFloat(focusView, "scaleX", 1, 0.6f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(focusView, "scaleY", 1, 0.6f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(
            focusView, "alpha", 1f, 0.3f, 1f, 0.3f, 1f, 0.3f, 1f
        );

        AnimatorSet animSet = new AnimatorSet();
        animSet.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                focusView.setVisibility(View.INVISIBLE);
            }
        });
        animSet.play(scaleX).with(scaleY).before(alpha);
        animSet.setDuration(400);
        animSet.start();
    }

    private void saveImageToLibraryIfNeed(Bitmap bitmap) {
        if (!CommonHelper.GetPreferenceBoolean(if_CameraX.this, Constants.Settings.bSaveLocal))
            return ;

        try {
            ai_InsItem oInsItem = db_InsItem.GetInsItem_ByID(iInsItemID);
            if (oInsItem == null) return;

            String sTitle = "SnapInspect-" + CommonHelper.GetTimeString();
            ai_Inspection oIns = db_Inspection.GetInspection_ByID(oInsItem.iInsID);
            String sDescription = oIns.sTitle + " - ";
            if (oInsItem.iPInsItemID > 0) {
                ai_InsItem oPInsItem = db_InsItem.GetInsItem_ByID(oInsItem.iPInsItemID);
                sDescription = sDescription + oPInsItem.sName + " - ";
            }
            sDescription = sDescription + oInsItem.sName;

            String path = getExternalFilesDir(Environment.DIRECTORY_PICTURES) + File.separator + "SnapInspect";
            // First Create Directory
            File outputFile = new File(path);
            if (!outputFile.exists()) outputFile.mkdir();

            path = path + File.separator + sTitle + ".jpg";

            File outputFile_temp = new File(path);
            FileOutputStream out = new FileOutputStream(outputFile_temp);

            // saving the Bitmap to a file compressed as a JPEG with 85% compression rate
            bitmap.compress(Bitmap.CompressFormat.JPEG, 85, out);
            out.flush();
            out.close();

            MediaStore.Images.Media.insertImage(getContentResolver(), path, sTitle, sDescription);

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    private boolean isSingleSelection() {
        return this.iMaxPhotoCount == Constants.Limits.iSinglePhotoLimit;
    }

    private void saveImage(Bitmap bitmap) {
        String sFilePath;
        long iClientFileID;

        // delete previous photos if it is single selection
        if (isSingleSelection()) deletePreviousPhotos();

        Location currentLocation = GeoLocationManager.getInstance(this).getLocation();
        if (oSaveOption.shouldSaveAsPhoto()) {
            O_FileName oFileName = O_FileName.getPhotoFileName();
            CommonHelper.SaveImage(oFileName.sFilePath, bitmap);
            /*Create thumbnail*/
            CommonHelper.SaveThumb(oFileName.sThumbNail, bitmap);

            if (oFileName.sThumbNail != null && oFileName.sFilePath != null) {
                if (bNoticePhotos) {
                    iPhotoId = CommonDB.InsertPhoto(
                            this, iInsItemID, oFileName.sThumbNail, oFileName.sFilePath, currentLocation);
                    CommonUI_Photo.didInsertNoticePhoto(this, iPhotoId);
                } else {
                    iPhotoId = CommonDB.InsertInsItemPhoto(
                            this, iInsItemID, oFileName.sThumbNail, oFileName.sFilePath, iPosition, currentLocation);
                }
                iFileId = 0;
            }

            if (!(CommonHelper.bFileExist(oFileName.sFilePath) && CommonHelper.GetFileLength(oFileName.sFilePath) > 0)) {
                runOnUiThread(() -> CommonUI.ShowAlert(
                        if_CameraX.this,
                        "Error",
                        "Photo can not be saved, please check the storage, permission and restart the app"
                        )
                );
                return;
            }

            sFilePath = oFileName.sFilePath;

        } else {
            String sFileName = CommonHelper.GetFileSaveFileName();
            CommonHelper.SaveImage(sFileName, bitmap);

            if (!(CommonHelper.bFileExist(sFileName) && CommonHelper.GetFileLength(sFileName) > 0)) {
                runOnUiThread(() -> CommonUI.ShowAlert(
                                if_CameraX.this,
                                "Error",
                                "Photo can not be saved, please check the storage, permission and restart the app"
                        )
                );
                return;
            }

            iClientFileID = CommonDB.InsertFile(this, iSAssetID, sFileName);
            sFilePath = sFileName;
            iFileId = iClientFileID;
            iPhotoId = 0;
        }

        // Adding the geo tag
        FileUtils.updateGpsTag(sFilePath, currentLocation);

        // finish the activity if it is single photo selection
        if (isSingleSelection()) didFinishSavingImage(sFilePath);

        // update preview image
        runOnUiThread(() -> imageView.setImageBitmap(bitmap));
    }

    private void didFinishSavingImage(String sFilePath) {
        // finish the activity and return the file info
        Intent intent = new Intent();
        intent.putExtra(Constants.Extras.CAMERA_RESPONSE, sFilePath);
        intent.putExtra(Constants.Extras.CLIENT_FILE_ID, iFileId);
        setResult(Constants.ResultCodes.CAMERA_RESPONSE, intent);

        Handler mainHandler = new Handler(Looper.getMainLooper());
        Runnable myRunnable = if_CameraX.this::finish;
        mainHandler.post(myRunnable);
    }

    private void deletePreviousPhotos() {
        if (iPhotoId > 0) {
            CommonDB.DeletePhotoByPhotoID(iPhotoId, true);
        }
        if (iFileId > 0) {
            CommonDB.DeleteFileByID(iFileId, true);
        }
    }

    // Button clicks
    public void doneClicked(View view) {
        onBackPressed();
    }

    private void setEnableTakePicture(boolean can) {
        loadingView.setVisibility(can ? View.GONE: View.VISIBLE);
        shutterBtn.setBackgroundResource(can ? R.drawable.shutter_normal : R.drawable.shutter_clicked);
        shutterBtn.setEnabled(can);
        doneButton.setEnabled(can);
    }

    public void shutterClicked(View view) {
        if (camera == null || imageCapture == null) return;
        imageCapture.takePicture(cameraExecutor, new ImageCapture.OnImageCapturedCallback() {
            @Override
            public void onCaptureSuccess(@NonNull ImageProxy image) {
                runOnUiThread(() -> setEnableTakePicture(false));
                new Thread(() -> {
                    int mOrientationCopy = mOrientation;
                    if (Constants.FixCameraOption.portrait.equals(CommonHelper.fixCameraOption(if_CameraX.this))) {
                        mOrientationCopy = 0;
                    } else if (Constants.FixCameraOption.landscape.equals(CommonHelper.fixCameraOption(if_CameraX.this))) {
                        mOrientationCopy = 270;
                    }

                    Bitmap oBitmap = BitmapUtils.rotateBitmap(BitmapUtils.decodeBitmap(image), mOrientationCopy);

                    Bitmap mergedBitmap = parseBitmap(oBitmap, mPhotoSize);
                    oBitmap = BitmapUtils.appendTextsIfNeed(if_CameraX.this,
                            BitmapUtils.fixCameraRotationIfNeed(if_CameraX.this, mergedBitmap),
                            textsOnPhoto()
                    );

                    saveImageToLibraryIfNeed(oBitmap);
                    saveImage(oBitmap);
                    runOnUiThread(() -> setEnableTakePicture(true));
                }).start();
            }

            @Override
            public void onError(@NonNull ImageCaptureException exception) {
                CommonUI.longToast(if_CameraX.this, R.string.message_camera_capture_fails);
            }
        });
    }

    public void switchClicked(View view) {
        lensFacing = LENS_FACING_FRONT == lensFacing ? LENS_FACING_BACK : LENS_FACING_FRONT;
        startCamera();
    }

    public void libraryClicked(View view) {
        mImagePicker.launch(
            new PickVisualMediaRequest.Builder()
                .setMediaType(ActivityResultContracts.PickVisualMedia.ImageOnly.INSTANCE)
                .build()
        );
    }

    private void viewPhoto() {
        if (iPhotoId == 0 && iFileId == 0) return;
        if (bNoticePhotos) {
            Intent intent = new Intent(Constants.Broadcasts.sPreviewNoticePhoto);
            intent.putExtra(Constants.Extras.iPhotoID, iPhotoId);
            LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
        } else {
            ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, (long) iInsItemID);
            String sValue = CommonHelper.GetValue(iPosition, oInsItem);
            startActivity(if_DisplayPhoto.newIntent(this, iPhotoId, iPosition, iInsItemID,
                    sValue, false, false));

        }
    }

    private Bitmap parseBitmap(Bitmap oBitmap, Size dstSize) {
        final Bitmap sBitmap;
        // reduce size to dstSize if the size of oBitmap is bigger than the target
        if (oBitmap.getWidth() > dstSize.getWidth() || oBitmap.getHeight() > dstSize.getHeight()) {
            sBitmap = BitmapUtils.scaleBitmap(oBitmap, dstSize);
            oBitmap.recycle();
        } else {
            sBitmap = oBitmap;
        }

        //merge sBitmap with white blank background only while it is not a square photo
        Bitmap mergedBitmap;
        if (sBitmap.getHeight() != sBitmap.getWidth()) {
            Bitmap bBitmap = BitmapFactory.decodeResource(getResources(), R.drawable.white_bg);
            mergedBitmap = BitmapUtils.combineImages(sBitmap, bBitmap, Math.max(sBitmap.getWidth(), sBitmap.getHeight()));
            bBitmap.recycle();
            sBitmap.recycle();
        } else {
            mergedBitmap = sBitmap;
        }

        return mergedBitmap;
    }

    private String textsOnPhoto() {
        ArrayList<String> texts = new ArrayList<>();
        boolean displayPhotoDate = CommonHelper.GetPreferenceBoolean(this, Constants.Settings.bPhotoStamp)
            || CommonJson.shouldTurnOnPhotoDate(this);
        if (displayPhotoDate) {
            texts.add(CommonHelper.sDateToStringFormat(null, "yyyy-MM-dd HH:mm"));
        }

        Location currentLocation = GeoLocationManager.getInstance(this).getLocation();
        boolean displayGeoTag = CommonHelper.GetPreferenceBoolean(this, Constants.Settings.bShowGeoTag) 
            || CommonJson.shouldTurnOnLocation(this);
        if (currentLocation != null && displayGeoTag)
            texts.add(String.format("%s,%s", currentLocation.getLatitude(), currentLocation.getLongitude()));

        return String.join("\n", texts);
    }

    private void showsEditPhotoFragment(Uri imageFileUri) {
        EditPhotoFragment editPhotoFragment = getEditPhotoFragment(imageFileUri);
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();
        transaction.setCustomAnimations(
                android.R.animator.fade_in, android.R.animator.fade_out,
                android.R.animator.fade_in, android.R.animator.fade_out);
        transaction.replace(R.id.edit_photo_layout, editPhotoFragment);
        transaction.addToBackStack(null);
        transaction.commit();
    }

    @NotNull
    private EditPhotoFragment getEditPhotoFragment(Uri imageFileUri) {
        EditPhotoFragment editPhotoFragment = new EditPhotoFragment(imageFileUri);
        editPhotoFragment.setCallback(bitmap -> {
            if (bitmap == null) return;
            new Thread(() -> {
                Bitmap mergedBitmap = BitmapUtils.appendTextsIfNeed(
                        if_CameraX.this, parseBitmap(bitmap, mPhotoSize), textsOnPhoto());
                saveImage(mergedBitmap);
                runOnUiThread(() -> imageView.setImageBitmap(mergedBitmap));
            }).start();
        });
        return editPhotoFragment;
    }
}
