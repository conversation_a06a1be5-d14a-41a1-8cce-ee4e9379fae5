package com.snapinspect.snapinspect3.activity;

import android.content.Context;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonDB_Notification;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Notification;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.SystemUiHider;
import com.snapinspect.snapinspect3.R;

import android.app.Activity;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import android.view.View;
import android.widget.MediaController;
import android.widget.VideoView;

import java.util.List;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_videoshow extends Activity {
    private VideoView oVideoView;
    private int iInsItemID = 0;

    public static Intent newIntent(Context context, long iInsItemID,
                                   long iNoticeID, long iVideoID, String sVideoFilePath) {
        Intent intent = new Intent(context, if_videoshow.class);
        intent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        intent.putExtra(Constants.Extras.iNoticeID, iNoticeID);
        intent.putExtra(Constants.Extras.iVideoID, iVideoID);
        intent.putExtra(Constants.Extras.sFilePath, sVideoFilePath);
        return intent;
    }

    public static Intent newIntent(Context context, String sVideoUrl) {
        Intent intent = new Intent(context, if_videoshow.class);
        intent.putExtra(Constants.Extras.sURL, sVideoUrl);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);

            setContentView(R.layout.activity_videoshow);
            getActionBar().hide();
            if (getIntent().hasExtra(Constants.Extras.iInsItemID)){
                iInsItemID = (int) getIntent().getLongExtra(Constants.Extras.iInsItemID, 0);
            }

            oVideoView = findViewById(R.id.video_view);

            String sVideoUrl = getIntent().getStringExtra(Constants.Extras.sURL);
            if (StringUtils.isEmpty(sVideoUrl)) {
                int iVideoID = (int) getIntent().getLongExtra(Constants.Extras.iVideoID, 0);
                ai_Video oVideo = ai_Video.findById(ai_Video.class, iVideoID);
                String sFilePath;
                if (oVideo != null) {
                    sFilePath = oVideo.getFile();
                } else {
                    sFilePath = getIntent().getStringExtra(Constants.Extras.sFilePath);
                }

                if (CommonHelper.bFileExist(sFilePath)) {
                    oVideoView.setVideoPath(sFilePath);
                } else {
                    Uri sVideoURI = Uri.parse(Constants.Urls.VIDEO_URL + sFilePath);
                    oVideoView.setVideoURI(sVideoURI);
                }
            } else {
                oVideoView.setVideoURI(Uri.parse(sVideoUrl));
            }

            final MediaController oController = new MediaController(this);
            oVideoView.setMediaController(oController);
            oController.show();
            oVideoView.start();
            findViewById(R.id.btn_Back).setOnClickListener(view -> finish());

        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "if_videoshow.onCreate", ex, this);
        }
    }


}
