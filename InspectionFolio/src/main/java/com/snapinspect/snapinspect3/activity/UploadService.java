package com.snapinspect.snapinspect3.activity;

import android.app.IntentService;
import android.content.Intent;
import android.os.Bundle;
import android.os.ResultReceiver;
import android.util.Xml;

import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.SI_DB.db_Media;

import com.snapinspect.snapinspect3.SI_DB.db_ProductCost;
import com.snapinspect.snapinspect3.util.ArrayUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONObject;
import org.xmlpull.v1.XmlSerializer;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

import static com.snapinspect.snapinspect3.Helper.CommonUploadAction.UploadVideoAction;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_CONTROL_TYPE_PTO;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_CONTROL_TYPE_SCAN;

/**
 * Created by TerrySun on 20/03/14.
 */
public class UploadService extends IntentService {
    public static final int UPDATE_PROGRESS = 8344;
    public static final int UPDATE_SUCCESS = 8345;
    public static final int UPDATE_FAIL = 8346;
    public static final int UPDATE_SUCCESSNODATA = 8347;
    private ResultReceiver oReceiver;
    public String sUploadInspectionErrorMessage = "";
    // String sBucketName = "topinspecttube";

    public UploadService() {
        super("UploadIntentService");
    }
    private void PublishProgress(int lResult, String sTitle,  String sMessage){
        try {
            Bundle resultData = new Bundle();
            resultData.putString("Title", sTitle);
            resultData.putString("Message", sMessage);
            oReceiver.send(lResult, resultData);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.PublishProgress", ex, this);
        }
    }

    private final ProgressCallback progressCallback = this::PublishProgress;

    @Override
    protected void onHandleIntent(Intent workIntent) {
        try{
            sUploadInspectionErrorMessage = "";
            oReceiver = workIntent.getParcelableExtra("receiver");

            PublishProgress(UPDATE_PROGRESS, "Start", "Connecting to Server ... ");
            boolean bUploadData = false;
            CommonHelper.trackEvent(this, "Android Upload Inspections", null);
            int iInsID = workIntent.getIntExtra(Constants.Extras.iInsID, 0);

            List<ai_Inspection> lsInspection = CommonDB_Inspection.GetCompletedInspectionsNoCompulsoryItems(iInsID);
            if (lsInspection != null && lsInspection.size() > 0){
                bUploadData = true;
                HashMap<String, Object> oMap1 = new HashMap<String, Object>();
                oMap1.put("Total", "" + lsInspection.size());
                CommonHelper.trackEvent(this, "Android Sync Upload Inspections", oMap1);
                CommonDB.InsertLog(this, "Upload", "Upload Inspection - " + lsInspection.size() + " inspections attached");
            }
            if (!NetworkUtils.isNetworkAvailable(getApplicationContext())){
                PublishProgress(UPDATE_FAIL, "Error", "Please Connect to Internet ... ");
                CommonDB.InsertLog(this, "Upload", "No Internet");
                return;
            }
            int iInspectionCount = 0;
            for (ai_Inspection oIns : lsInspection){
                if (UploadInspection(oIns)){
                    HashMap<String, Object> oMap2 = new HashMap<String, Object>();
                    oMap2.put("Title", oIns.sTitle);
                    CommonHelper.trackEvent(this, "Android Sync Upload Inspection Success", oMap2);

                    CommonDB.InsertLog(this, "Upload", "Successful Upload Inspection " + oIns.sInsTitle + " for " + oIns.sAddressOne + " @ " + oIns.dtStartDate);
                    //oIns.bSynced = true;
                   // oIns.save();
                    iInspectionCount ++;
                }
                else{
                    HashMap<String, Object> oMap3 = new HashMap<String, Object>();
                    oMap3.put("Title", oIns.sTitle);
                    CommonHelper.trackEvent(this, "Android Sync Upload Inspection Fail", oMap3);
                    bUploadSuccess = false;
                    ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.onHandleIntent " + oIns.sTitle, new IOException(), UploadService.this);
                    // ai_BugHandler.ai_Handler_Exception("per", "UploadService.onHandleIntent " + oIns.sTitle, new IOException());
                    CommonDB.InsertLog(this, "Upload", "Failed Upload Inspection " + oIns.sInsTitle + " for " + oIns.sAddressOne + " @ " + oIns.dtStartDate);

                }
            }
            if (bUploadSuccess && bUploadData){
                HashMap<String, Object> oMap4 = new HashMap<String, Object>();
                oMap4.put("Total", iInspectionCount);
                CommonHelper.trackEvent(this, "Android Sync Upload Inspection All Success", oMap4);
                PublishProgress(UPDATE_SUCCESS, "Success", "" + iInspectionCount + " inspections have been uploaded to cloud.");
                CommonDB.InsertLog(this, "Upload", "" + iInspectionCount + " (" + (lsInspection == null ? 0 : lsInspection.size())  + ") inspections have been uploaded to cloud.");

                //  Notice("Upload Inspections Data - Success...", "Upload Completed ...", "Upload Completed.");
            }
            else if (!bUploadData){
                CommonDB.InsertLog(this, "Upload", "No Inspections Marked as Complete.");
                PublishProgress(UPDATE_SUCCESSNODATA, "Info", "No Inspections have been completed. Please make sure to mark the inspection as 'Complete'. Only inspections in the 'Inspection Tab' under 'Completed Inspection' section will be uploaded to cloud.");
            }
            else{
                HashMap<String, Object> oMap5 = new HashMap<String, Object>();
                oMap5.put("Total", lsInspection.size());
                oMap5.put("Failed Total", (lsInspection.size() - iInspectionCount));
                CommonHelper.trackEvent(this, "Android Sync Upload Inspection All Fail", oMap5);
                //Notice("Upload Inspections Data - Error...", "Upload Failed, Please try again ...", "Upload Failed, Please try again.");
                CommonDB.InsertLog(this, "Upload", "Failed Upload Message Displayed -" + iInspectionCount + " of " + (lsInspection == null ? 0 : lsInspection.size())  + " inspections have been uploaded to cloud. Please try again.");
                PublishProgress(UPDATE_FAIL, "Fail","" + iInspectionCount + " of " + (lsInspection == null ? 0 : lsInspection.size()) + " inspections have been uploaded to cloud successfully. " + ((sUploadInspectionErrorMessage != null && (sUploadInspectionErrorMessage.equalsIgnoreCase(""))) ? "Please Upload Again!" : sUploadInspectionErrorMessage));

            }
           /* try{
                Intent broadcastIntent = new Intent();
                broadcastIntent.setAction(if_home.ResponseReceiver.ACTION_RESP);
                broadcastIntent.addCategory(Intent.CATEGORY_DEFAULT);
                sendBroadcast(broadcastIntent);
            }catch(Exception ex){
            }*/
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.onHandleIntent", ex, this);
        }
    }
    private final StringBuilder sMessage = new StringBuilder();
    private String sCurrentUploadTitle = "";
    private XmlSerializer serializer = Xml.newSerializer();
    private boolean bUploadSuccess = true;
    private boolean UploadInspection(ai_Inspection oIns){
        try{
            // CommonHelper.ValidateTempFolderExist();
            if (CommonHelper.bRequestInspection(oIns.sCustomTwo)){
                CommonHelper.ValidateTempFolderExist();
                long lInspectionID = oIns.getId();
                int iInspectionID = (int) lInspectionID;
                sCurrentUploadTitle = oIns.sTitle;
                List<ai_Photo> lsPhoto = db_Media.GetPhotosForInspection_NeedUpload(iInspectionID);
                List<ai_Video> lsVideo = db_Media.GetVideosForInspection_NeedUpload(iInspectionID);
                PublishProgress(UPDATE_PROGRESS, "Upload Inspections - " + sCurrentUploadTitle, "Start");
                String sRequestID = CommonJson.GetJsonKeyValue("RID", oIns.sCustomTwo);
                String sRequestCode = CommonJson.GetJsonKeyValue("RCode", oIns.sCustomTwo);
                if (UploadPhoto_RequestInspection(lsPhoto, oIns.sInsTitle + " for " + oIns.sTitle + " @ " + oIns.dtStartDate, sRequestID, sRequestCode) &&
                        UploadVideo_RequestInspection(lsVideo, oIns.sInsTitle + " for " + oIns.sTitle + " @ " + oIns.dtStartDate, sRequestID, sRequestCode)) {
                    String sFilePath = CommonHelper.sFileRoot + "/" + oIns.getId() + ".xml";
                    if (PrepareUploadFile_RequestInspection(oIns, sFilePath, CommonHelper.sRequestInspection_FileName(oIns.sCustomTwo))) {
                       /* ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
                        lsParams.add(new BasicNameValuePair("iRequestID", sRequestID));
                        lsParams.add(new BasicNameValuePair("sRequestCode",  sRequestCode));
                        lsParams.add(new BasicNameValuePair("ver", "1"));*/
                        HashMap<String, String> lsParams = new HashMap<String, String>();
                        lsParams.put("iRequestID", sRequestID);
                        lsParams.put("sRequestCode", sRequestCode);

                        lsParams.put("ver", "1");
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Upload Inspection Data", "Upload Inspection Data");
                        PublishProgress(UPDATE_PROGRESS, "", "Upload Inspection Data");
                        JSONObject oReturn = IF_SyncClient.UploadData("/Sync/AppServer_RequestInspection", lsParams, sFilePath);
                        if (oReturn != null && oReturn.getBoolean("success")) {
                            oIns.iSInsID = oReturn.getInt("iInsID");
                            oIns.bSynced = true;
                            oIns.save();
                            try{
                            CommonInspection.ReconsolidateInspectionWithServer(oReturn.getJSONObject("oInspection"));
                            }catch(Exception eeeee){

                            }
                            CommonHelper.DeleteFile(sFilePath);
                            if (oIns.iSScheduleID > 0) CommonHelper.DeleteScheduleInfo(oIns.iSScheduleID);
                            if (oIns.getProjectInsID() > 0)
                                CommonHelper.SavePreference(this, Constants.Keys.bForceSyncProjects, "1");
                            return true;
                        } else {
                            sUploadInspectionErrorMessage = oReturn.getString("message");
                            sMessage.append("Failed upload inspection data.").append('\n');
                            return false;
                        }
                    } else {
                        sMessage.append("Failed prepare inspection data.").append('\n');
                        return false;
                    }
                } else {
                    sMessage.append("Failed when upload photos or videos.").append('\n');
                    return false;
                }
            }
            else if (CommonValidate.bExternalInspection(oIns.sCustomTwo)){
                CommonHelper.ValidateTempFolderExist();
                long lInspectionID = oIns.getId();
                int iInspectionID = (int) lInspectionID;
                sCurrentUploadTitle = oIns.sTitle;
                List<ai_Photo> lsPhoto = ai_Photo.find(ai_Photo.class, "I_INS_ID=? and B_DELETED = 0 and B_UPLOADED=0", "" + iInspectionID);
                List<ai_Video> lsVideo = ai_Video.find(ai_Video.class, "I_INS_ID=? and B_DELETED= 0 and B_GET_URL = 0", "" + iInspectionID);
                PublishProgress(UPDATE_PROGRESS, "Upload Inspections - " + sCurrentUploadTitle, "Start");
                String sTokenID = CommonJson.GetJsonKeyValue("iTokenID", oIns.sCustomTwo);
                String sToken = CommonJson.GetJsonKeyValue("sToken", oIns.sCustomTwo);
                if (UploadPhoto_ExternalInspection(lsPhoto, oIns.sInsTitle + " for " + oIns.sTitle + " @ " + oIns.dtStartDate, sTokenID, sToken) &&
                        UploadVideo_ExternalInspection(lsVideo, oIns.sInsTitle + " for " + oIns.sTitle + " @ " + oIns.dtStartDate, sTokenID, sToken)) {
                    String sFilePath = CommonHelper.sFileRoot + "/" + oIns.getId() + ".xml";
                    if (PrepareUploadFile_RequestInspection(oIns, sFilePath, CommonHelper.sRequestInspection_FileName(oIns.sCustomTwo))) {
                       /* ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
                        lsParams.add(new BasicNameValuePair("iRequestID", sRequestID));
                        lsParams.add(new BasicNameValuePair("sRequestCode",  sRequestCode));
                        lsParams.add(new BasicNameValuePair("ver", "1"));*/
                        HashMap<String, String> lsParams = new HashMap<String, String>();
                        lsParams.put("iTokenID", sTokenID);
                        lsParams.put("sToken", sToken);
                        try {
                            lsParams.put("sDeviceInfo", CommonHelper.GetDeviceInfo() + " - ExternalTokenID: " + sTokenID);
                        }catch(Exception cvcv){
                        }
                        lsParams.put("ver", "1");
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Upload Inspection Data", "Upload Inspection Data");
                        PublishProgress(UPDATE_PROGRESS, "", "Upload Inspection Data");
                        JSONObject oReturn = IF_SyncClient.UploadData("/SyncExternal/AppServer_ExternalInspection", lsParams, sFilePath);
                        if (oReturn != null && oReturn.getBoolean("success")) {
                            oIns.iSInsID = oReturn.getInt("iInsID");
                            oIns.bSynced = true;
                            oIns.save();
                            CommonInspection.DeleteInspection(oIns, this);
                            try{
                                CommonInspection.ReconsolidateInspectionWithServer(oReturn.getJSONObject("oInspection"));
                            }catch(Exception eeeee){

                            }
                            CommonHelper.DeleteFile(sFilePath);
                            if (oIns.iSScheduleID > 0) CommonHelper.DeleteScheduleInfo(oIns.iSScheduleID);
                            if (oIns.getProjectInsID() > 0)
                                CommonHelper.SavePreference(this, Constants.Keys.bForceSyncProjects, "1");
                            return true;
                        } else {
                            sUploadInspectionErrorMessage = oReturn.getString("message");
                            sMessage.append("Failed upload inspection data.").append('\n');
                            return false;
                        }
                    } else {
                        sMessage.append("Failed prepare inspection data.").append('\n');
                        return false;
                    }
                } else {
                    sMessage.append("Failed when upload photos or videos.").append('\n');
                    return false;
                }
            }
            else {
                int iInspectionID = oIns.getId().intValue();
                sCurrentUploadTitle = oIns.sTitle;
                List<ai_Photo> allPhotos = ai_Photo.find(ai_Photo.class, "I_INS_ID=? and B_DELETED = 0", "" + iInspectionID);
                List<ai_Photo> lsPhoto = ai_Photo.find(ai_Photo.class, "I_INS_ID=? and B_DELETED = 0 and B_UPLOADED=0", "" + iInspectionID);
                List<ai_Video> lsVideo = ai_Video.find(ai_Video.class, "I_INS_ID=? and B_DELETED= 0 and B_GET_URL = 0", "" + iInspectionID);
                PublishProgress(UPDATE_PROGRESS, "Upload Inspections - " + sCurrentUploadTitle, "Start");
                String eventTitle = oIns.sInsTitle + " for " + oIns.sTitle + " @ " + oIns.dtStartDate;
                if (savePhotoComments(allPhotos, eventTitle) &&
                        UploadPhoto(lsPhoto, eventTitle) && UploadVideo(lsVideo, eventTitle)) {
                    String sFilePath = CommonHelper.sFileRoot + "/" + oIns.getId() + ".xml";
                    if (PrepareUploadFile(oIns, sFilePath)) {
                        /*ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
                        lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
                        lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));
                        lsParams.add(new BasicNameValuePair("ver", "1"));*/
                        HashMap<String, String> lsParams = new HashMap<>();
                        lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                        lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
                        try {
                            lsParams.put("sDeviceInfo", CommonHelper.GetDeviceInfo());
                        } catch (Exception cvcv) {
                        }
                        lsParams.put("ver", "1");
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Upload Inspection Data", "Upload Inspection Data");
                        PublishProgress(UPDATE_PROGRESS, "", "Upload Inspection Data");
                        JSONObject oReturn = IF_SyncClient.UploadData("/Sync/AppServer", lsParams, sFilePath);
                        if (oReturn != null && oReturn.getBoolean("success")) {
                            oIns.iSInsID = oReturn.getInt("iInsID");
                            oIns.bSynced = true;
                            oIns.save();
                            CommonInspection.DeleteInspection(oIns, this);
                            try {
                                CommonInspection.ReconsolidateInspectionWithServer(oReturn.getJSONObject("oInspection"));
                            } catch (Exception eeeee) {

                            }

                            CommonHelper.DeleteFile(sFilePath);
                            if (oIns.iSScheduleID > 0) CommonHelper.DeleteScheduleInfo(oIns.iSScheduleID);
                            if (oIns.getProjectInsID() > 0) {
                                CommonHelper.SavePreference(this, Constants.Keys.bForceSyncProjects, "1");
                            }
                            return true;
                        } else {
                            sUploadInspectionErrorMessage = oReturn.getString("message");
                            sMessage.append("Failed upload inspection data.").append('\n');
                            return false;
                        }
                    } else {
                        sMessage.append("Failed prepare inspection data.").append('\n');
                        return false;
                    }
                } else {
                    sMessage.append("Failed when upload photos or videos.").append('\n');
                    return false;
                }
            }
            //return true;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadInspection", ex, this);
            return false;
        }
    }
    private ai_Layout GetLayout(List<ai_Layout> lsLayout, int iSLayoutID){
        for (int i=0; i < lsLayout.size(); i++){
            if (lsLayout.get(i).iSLayoutID == iSLayoutID){
                return lsLayout.get(i);
            }
        }
        return null;
    }
    private void LoadiMarkToXML(List<ai_Layout> lsLayout, int iSLayoutID){
        int iMark = 0;
        try{
            iMark = Integer.parseInt(GetLayout(lsLayout, iSLayoutID).sFieldTwo);
        }catch(Exception ex){

        }
        try{
            serializer.attribute(null, "iM", "" + iMark);
        }catch(Exception ee){

        }
    }
    private boolean PrepareUploadFile_RequestInspection(ai_Inspection oIns, String sFilePath, String sJsonFilePath){
        try{
            File newxmlfile = new File(sFilePath);
            try {
                newxmlfile.createNewFile();
            } catch (IOException e) {
                ai_BugHandler.ai_Handler_Exception(e);
                return false;
            }
            FileOutputStream fileos =  new FileOutputStream(newxmlfile);

            serializer = Xml.newSerializer();
            serializer.setOutput(fileos, "UTF-8");
            serializer.startDocument(null, Boolean.valueOf(true));
            serializer.startTag(null, "Ins");
            serializer.attribute(null, "iInsTypeID", "" + oIns.iSInsTypeID);
            serializer.attribute(null, "iAssetID", "" + oIns.iSAssetID);
            serializer.attribute(null, "sPTC", oIns.sPTC);
            serializer.attribute(null, "iScheduleID", "" + oIns.iSScheduleID);
            serializer.attribute(null, "iProjectInsID", "" + oIns.getProjectInsID());
            // TimeZone Identifier
            serializer.attribute(null, "sTZID",  TimeZone.getDefault().getID());
            serializer.attribute(null, "sType", oIns.sType);
            int iTimer = 0;
            String sDateSchedule = "";
            try {
                String sCustomOne = oIns.sCustomOne;
                if (!StringUtils.isEmpty(sCustomOne)) {
                    String sTimer = CommonJson.GetJsonKeyValue("InsTimer", sCustomOne);
                    iTimer = Integer.parseInt(sTimer);
                    sDateSchedule = StringUtils.ifEmpty(
                            CommonJson.GetJsonKeyValue(Constants.Keys.DATE_SCHEDULE_PICKED, sCustomOne), "");
                }
            } catch(Exception exxx) {

            }
            serializer.attribute(null, "InsTimer", "" + iTimer);
            serializer.attribute(null, Constants.Keys.DATE_SCHEDULE_PICKED, sDateSchedule);

            try{


                if (oIns.iSInsID > 0) {

                    serializer.attribute(null, "iSInsID", "" + oIns.iSInsID);
                }

            }catch(Exception exxx){

            }

            WriteText("sInsTitle", oIns.sInsTitle);
            WriteText("sTitle", oIns.sTitle);
            //Date dtStart = org.apache.http.impl.cookie.DateUtils.parseDate(oIns.dtStartDate);
            //Date dtEnd = org.apache.http.impl.cookie.DateUtils.parseDate(oIns.dtEndDate);
            WriteText("dtStartDate", oIns.dtStartDate);

            WriteText("dtEndDate", oIns.dtEndDate);
            WriteText("sLat", oIns.sLat);
            WriteText("sLong", oIns.sLong);
            WriteText("sAddress1", oIns.sAddressOne);
            WriteText("sAddress2", oIns.sAddressTwo);
            List<ai_Layout> lsTemp1stLayout = CommonRequestInspection.LoadLayout(0, sJsonFilePath);
            List<ai_InsItem> ls1stInsItem = CommonDB.GetChildInsItem_UploadService(0, oIns.getId());
            if (ls1stInsItem != null && !ls1stInsItem.isEmpty()) {
                // sort by iSort
                Collections.sort(ls1stInsItem, (o1, o2) -> o1.iSort - o2.iSort);
                for (ai_InsItem oInsItem : ls1stInsItem) {
                    try {
                        serializer.startTag(null, "L1");
                        serializer.attribute(null, "iItemID", "" + oInsItem.getId());
                        serializer.attribute(null, "iSLayoutID", "" + oInsItem.iSLayoutID);
                        serializer.attribute(null, "sQT", oInsItem.sQType);
                        serializer.attribute(null, "iSALayoutID", "" + oInsItem.iSAssetLayoutID);
                        LoadiMarkToXML(lsTemp1stLayout, oInsItem.iSLayoutID);
                        ProcessInsItemBody(oInsItem, null, oInsItem.sQType);
                        List<ai_InsItem> lsChild = ai_InsItem.findWithQuery(ai_InsItem.class,
                                "SELECT * FROM AIIns_Item WHERE I_Ins_ID = ? and B_Deleted = 0 and I_P_INS_ITEM_ID = ?", "" + oIns.getId(), "" + oInsItem.getId());
                        // sort by iSort
                        Collections.sort(lsChild, (o1, o2) -> o1.iSort - o2.iSort);
                        List<ai_Layout> lsTemp2ndLayout = CommonRequestInspection.LoadLayout(oInsItem.iSLayoutID, sJsonFilePath);
                        for (ai_InsItem oChildInsItem : lsChild) {
                            try {
                                serializer.startTag(null, "L2");
                                serializer.attribute(null, "iItemID", "" + oChildInsItem.getId());
                                serializer.attribute(null, "iSLayoutID", "" + oChildInsItem.iSLayoutID);
                                serializer.attribute(null, "sQT", oChildInsItem.sQType);
                                serializer.attribute(null, "iPItemID", "" + oChildInsItem.iPInsItemID);
                                serializer.attribute(null, "nc", oChildInsItem.sNameChanged == null ? "" : oChildInsItem.sNameChanged);
                                LoadiMarkToXML(lsTemp2ndLayout, oChildInsItem.iSLayoutID);
                                ProcessInsItemBody(oChildInsItem, oInsItem, (oChildInsItem.sQType == null || oChildInsItem.sQType.equals("")) ? oInsItem.sQType : oChildInsItem.sQType);
                                // COSTS
                                serializeProductCosts(serializer, oChildInsItem.getId().intValue());

                                serializer.endTag(null, "L2");
                            } catch (Exception eeee) {
                                //String bb = "CCC";
                            }
                        }
                        serializer.endTag(null, "L1");
                    } catch (Exception eee) {
                        //String cc = "bb";
                    }
                }
            }
            String abb = serializer.toString();
            serializer.endTag(null, "Ins");
            serializer.endDocument();
            serializer.flush();
            fileos.close();
            return true;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.PrepareUploadFile_RequestInspection", ex, this);
            return false;
        }
    }
    private boolean PrepareUploadFile(ai_Inspection oIns, String sFilePath){
        try{
            File newxmlfile = new File(sFilePath);
            try {
                newxmlfile.createNewFile();
            } catch (IOException e) {
                ai_BugHandler.ai_Handler_Exception(e);
                return false;
            }
            FileOutputStream fileos =  new FileOutputStream(newxmlfile);

            serializer = Xml.newSerializer();
            serializer.setOutput(fileos, "UTF-8");
            serializer.startDocument(null, Boolean.valueOf(true));
            serializer.startTag(null, "Ins");
            serializer.attribute(null, "iInsTypeID", "" + oIns.iSInsTypeID);
            serializer.attribute(null, "iAssetID", "" + oIns.iSAssetID);
            serializer.attribute(null, "sPTC", "" + oIns.sPTC);
            serializer.attribute(null, "iScheduleID", "" + oIns.iSScheduleID);
            serializer.attribute(null, "iProjectInsID", "" + oIns.getProjectInsID());
            // TimeZone Identifier
            serializer.attribute(null, "sTZID",  TimeZone.getDefault().getID());
            try{

                String sCopyInsID = CommonJson.GetJsonKeyValue("_iCopyInsID", oIns.sCustomTwo);
                if (sCopyInsID != null && sCopyInsID.length() > 0){
                    serializer.attribute(null, "iCopyInsID", "" + sCopyInsID);
                }
            }catch(Exception eeeeee){

            }
            try{

                String sReview = CommonJson.GetJsonKeyValue("_bReview", oIns.sCustomTwo);
                if (sReview != null && sReview.trim().length() > 0 && sReview.trim().equalsIgnoreCase("1")){
                    serializer.attribute(null, "bReview", "1");
                }
            }catch(Exception eeeeee){

            }
            serializer.attribute(null, "sType", "" + oIns.sType);
            int iTimer = 0;
            String sDateSchedule = "";
            try {
                String sCustomOne = oIns.sCustomOne;
                if (!StringUtils.isEmpty(sCustomOne)) {
                    String sTimer = CommonJson.GetJsonKeyValue("InsTimer", sCustomOne);
                    iTimer = Integer.parseInt(sTimer);
                    sDateSchedule = StringUtils.ifEmpty(
                        CommonJson.GetJsonKeyValue(Constants.Keys.DATE_SCHEDULE_PICKED, sCustomOne), "");
                }
            } catch(Exception exxx) {

            }
            serializer.attribute(null, "InsTimer", "" + iTimer);
            serializer.attribute(null, Constants.Keys.DATE_SCHEDULE_PICKED, sDateSchedule);

            try{


                if (oIns.iSInsID > 0) {

                    serializer.attribute(null, "iSInsID", "" + oIns.iSInsID);
                }

            }catch(Exception exxx){

            }
            WriteText("sInsTitle", oIns.sInsTitle);
            WriteText("sTitle", oIns.sTitle);
            //Date dtStart = org.apache.http.impl.cookie.DateUtils.parseDate(oIns.dtStartDate);
            //Date dtEnd = org.apache.http.impl.cookie.DateUtils.parseDate(oIns.dtEndDate);
            WriteText("dtStartDate", oIns.dtStartDate);

            WriteText("dtEndDate", oIns.dtEndDate);
            WriteText("sLat", oIns.sLat);
            WriteText("sLong", oIns.sLong);
            WriteText("sAddress1", oIns.sAddressOne);
            WriteText("sAddress2", oIns.sAddressTwo);
            List<ai_Layout> lsTemp1stLayout = CommonDB.GetParentLayout(oIns.sPTC);
            List<ai_InsItem> ls1stInsItem = CommonDB.GetChildInsItem_UploadService(0, oIns.getId());
            if (ls1stInsItem != null && ls1stInsItem.size() > 0) {
                Collections.sort(ls1stInsItem, (o1, o2) -> o1.iSort - o2.iSort);
                for (ai_InsItem oInsItem : ls1stInsItem) {
                    try {
                        serializer.startTag(null, "L1");
                        serializer.attribute(null, "iItemID", "" + oInsItem.getId());
                        serializer.attribute(null, "iSLayoutID", "" + oInsItem.iSLayoutID);
                        serializer.attribute(null, "sQT", oInsItem.sQType);
                        serializer.attribute(null, "iSALayoutID", "" + oInsItem.iSAssetLayoutID);
                        try {

                            String sCopyInsItemID = CommonJson.GetJsonKeyValue("_iCopyInsItemID", oInsItem.sCustomOne);
                            if (sCopyInsItemID != null && sCopyInsItemID.trim().length() > 0) {
                                serializer.attribute(null, "iCopyInsItemID", sCopyInsItemID);
                            }
                        } catch (Exception eeeeee) {

                        }
                        try {
                            String sEditInsItemID = CommonJson.GetJsonKeyValue("_iInsItemID", oInsItem.sCustomOne);
                            if (sEditInsItemID != null && sEditInsItemID.trim().length() > 0) {
                                serializer.attribute(null, "iEditInsItemID", sEditInsItemID);
                            }
                        } catch (Exception eeeeeee) {

                        }
                        LoadiMarkToXML(lsTemp1stLayout, oInsItem.iSLayoutID);
                        ProcessInsItemBody(oInsItem, null, oInsItem.sQType);
                        List<ai_InsItem> lsChild = ai_InsItem.findWithQuery(ai_InsItem.class,
                                "SELECT * FROM AIIns_Item WHERE I_Ins_ID = ? and B_Deleted = 0 and I_P_INS_ITEM_ID = ?", "" + oIns.getId(), "" + oInsItem.getId());
                        Collections.sort(lsChild, (o1, o2) -> o1.iSort - o2.iSort);
                        List<ai_Layout> lsTemp2ndLayout = CommonDB.GetChildLayout(oInsItem.iSLayoutID, this);
                        for (ai_InsItem oChildInsItem : lsChild) {
                            try {
                                serializer.startTag(null, "L2");
                                serializer.attribute(null, "iItemID", "" + oChildInsItem.getId());
                                serializer.attribute(null, "iSLayoutID", "" + oChildInsItem.iSLayoutID);
                                serializer.attribute(null, "sQT", oChildInsItem.sQType);
                                serializer.attribute(null, "iPItemID", "" + oChildInsItem.iPInsItemID);
                                serializer.attribute(null, "nc", oChildInsItem.sNameChanged == null ? "" : oChildInsItem.sNameChanged);
                                try {

                                    String sCopyInsItemID_Child = CommonJson.GetJsonKeyValue("_iCopyInsItemID", oChildInsItem.sCustomOne);
                                    if (sCopyInsItemID_Child != null && sCopyInsItemID_Child.trim().length() > 0) {
                                        serializer.attribute(null, "iCopyInsItemID", sCopyInsItemID_Child);
                                    }
                                } catch (Exception eeeeee) {

                                }
                                try {
                                    String sEditInsItemID_Child = CommonJson.GetJsonKeyValue("_iInsItemID", oChildInsItem.sCustomOne);
                                    if (sEditInsItemID_Child != null && sEditInsItemID_Child.trim().length() > 0) {
                                        serializer.attribute(null, "iEditInsItemID", sEditInsItemID_Child);
                                    }
                                } catch (Exception eeeeeee) {

                                }
                                LoadiMarkToXML(lsTemp2ndLayout, oChildInsItem.iSLayoutID);
                                ProcessInsItemBody(oChildInsItem, oInsItem, (oChildInsItem.sQType == null || oChildInsItem.sQType.equals("")) ? oInsItem.sQType : oChildInsItem.sQType);

                                // sCustomOne and sCustomTwo
                                WriteText("sCustom1", oChildInsItem.sCustomOne);
                                WriteText("sCustom2",
                                    CommonFloorPlan.processInsItemFloorPlanPhoto(oChildInsItem, oChildInsItem.sCustomTwo));

                                // COSTS
                                serializeProductCosts(serializer, oChildInsItem.getId().intValue());

                                serializer.endTag(null, "L2");
                            } catch (Exception eeee) {
                                String bb = "CCC";
                            }
                        }
                        serializer.endTag(null, "L1");
                    } catch (Exception eee) {
                        String cc = "bb";
                    }
                }
            }
            try{
                if (oIns.iSInsID > 0){
                    List<ai_InsItem> lsDeleted = db_InsItem.GetInsItems_Deleted(oIns.getId().intValue());
                    for (ai_InsItem oDeleted : lsDeleted){
                        String sEditInsItemID_Child = CommonJson.GetJsonKeyValue("_iInsItemID", oDeleted.sCustomOne);
                        if (sEditInsItemID_Child != null && sEditInsItemID_Child.trim().length() > 0){
                            serializer.startTag(null, "LDEL");
                            serializer.attribute(null, "iEditInsItemID", sEditInsItemID_Child);
                            serializer.endTag(null, "LDEL");
                        }
                    }

                }
            }catch(Exception ffffff){

            }


            String abb = serializer.toString();
            serializer.endTag(null, "Ins");
            serializer.endDocument();
            serializer.flush();
            fileos.close();
            return true;
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.PrepareUploadFile", ex, this);
            return false;
        }
    }
    /*private void ProcessInsItemDetail1st(String sConfig, String sValue, String sConfigTag, String sValueTag, int iInsItemID){
        if (sConfig != null && sConfig.length() > 0){
           // String sConfig1 = oInsItem.sConfigOne;
            if (sConfig.equalsIgnoreCase("P")){
                List<ai_Photo> lsPhoto = CommonDB.GetPhotoByIntItemID(Integer.parseInt("" + iInsItemID));
                String sServerPhotoID = "";
                for (ai_Photo oPhoto : lsPhoto){
                    if (oPhoto.bUploaded && oPhoto.iSPhotoID > 0){
                        sServerPhotoID = sServerPhotoID + oPhoto.iSPhotoID + ",";
                    }
                    else{
                        ai_BugHandler.ai_Handler_Exception("Per", "ProcessInsItemBody.LostPhoto - " + oPhoto.iSPhotoID + " - " + oPhoto.bUploaded + " - " + oPhoto.iInsItemID, new IOException());
                    }
                }
                if (sServerPhotoID.endsWith(",")){
                    sServerPhotoID = sServerPhotoID.substring(0, sServerPhotoID.length() - 1);
                }
                WriteText(sConfigTag, sConfig);
                WriteText(sValueTag, sServerPhotoID);
            }
            else{
                WriteText(sConfigTag, sConfig);
                WriteText(sValueTag, sValue == null ? "" : sValue);
            }
        }
    }
    private void ProcessInsItemBody1st(ai_InsItem oInsItem, String sType){
        WriteText("sName", oInsItem.sName);
        long lInsItemID = oInsItem.getId();
        int iInsItemID = (int)lInsItemID;
        ProcessInsItemDetail1st(oInsItem.sConfigOne, oInsItem.sValueOne, "sC1", "sV1", iInsItemID);
        ProcessInsItemDetail1st(oInsItem.sConfigTwo, oInsItem.sValueTwo, "sC2", "sV2", iInsItemID);
        ProcessInsItemDetail1st(oInsItem.sConfigThree, oInsItem.sValueThree, "sC3", "sV3", iInsItemID);
        ProcessInsItemDetail1st(oInsItem.sConfigFour, oInsItem.sValueFour, "sC4", "sV4", iInsItemID);
        ProcessInsItemDetail1st(oInsItem.sConfigFive, oInsItem.sValueFive, "sC5", "sV5", iInsItemID);
        ProcessInsItemDetail1st(oInsItem.sConfigSix, oInsItem.sValueSix, "sC6", "sV6", iInsItemID);
    }*/

    private String GetPhotoServerIDString(long lInsItemID, String sValue){
        String sServerPhotoID = "";
        try {
            List<ai_Photo> lsPhoto = CommonDB.GetPhotoByInsItemID(Integer.parseInt("" + lInsItemID), this);

            List<String> sSplit = Arrays.asList(sValue.split(","));
            for (ai_Photo oPhoto : lsPhoto) {
                if (sSplit.contains("" + oPhoto.getId())) {
                    if (oPhoto.bUploaded && oPhoto.iSPhotoID > 0) {
                        sServerPhotoID = sServerPhotoID + oPhoto.iSPhotoID + ",";
                    } else {
                        try {
                            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.ProcessInsItemBody.LostPhoto", new IOException(), this);
                        }catch(Exception eee){

                        }
                        //  ai_BugHandler.ai_Handler_Exception("Per", "ProcessInsItemBody.LostPhoto - " + oPhoto.iSPhotoID + " - " + oPhoto.bUploaded + " - " + oPhoto.iInsItemID, new IOException());
                    }
                }
            }
            if (sServerPhotoID.endsWith(",")) {
                sServerPhotoID = sServerPhotoID.substring(0, sServerPhotoID.length() - 1);
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.GetPhotoServerIDString", ex, this);
        }
        return sServerPhotoID == null ? "" : sServerPhotoID;
    }
    private void ProcessInsItemBody(ai_InsItem oInsItem, ai_InsItem oParentInsItem, String sQType){
        try {
            if (sQType.equals("C") || sQType.equals("P") || sQType.equals("A") || sQType.equals("G")) {
                WriteText("sName", oInsItem.sName);
                WriteText("sC", oInsItem.sConfig);
                String sConfig1 = oParentInsItem == null ? oInsItem.sConfigOne : CommonHelper.GetConfig(1, oInsItem, oParentInsItem);
                String sConfig2 = oParentInsItem == null ? oInsItem.sConfigTwo : CommonHelper.GetConfig(2, oInsItem, oParentInsItem);
                String sConfig3 = oParentInsItem == null ? oInsItem.sConfigThree : CommonHelper.GetConfig(3, oInsItem, oParentInsItem);
                String sConfig4 = oParentInsItem == null ? oInsItem.sConfigFour : CommonHelper.GetConfig(4, oInsItem, oParentInsItem);
                String sConfig5 = oParentInsItem == null ? oInsItem.sConfigFive : CommonHelper.GetConfig(5, oInsItem, oParentInsItem);
                String sConfig6 = oParentInsItem == null ? oInsItem.sConfigSix : CommonHelper.GetConfig(6, oInsItem, oParentInsItem);

                if (sConfig1 != null && sConfig1.length() > 0) {
                    int sControlType1 = CommonInsItem.getControlType(sConfig1);
                    if (sControlType1 == SI_CONTROL_TYPE_PTO || sControlType1 == SI_CONTROL_TYPE_SCAN) {
                        WriteText("sC1", oInsItem.sConfigOne);
                        WriteText("sV1", GetPhotoServerIDString(oInsItem.getId(), oInsItem.sValueOne));
                    } else {
                        WriteText("sC1", oInsItem.sConfigOne);
                        WriteText("sV1", oInsItem.sValueOne);
                    }
                }
                if (sConfig2 != null && sConfig2.length() > 0) {
                    int sControlType2 = CommonInsItem.getControlType(sConfig2);
                    if (sControlType2 == SI_CONTROL_TYPE_PTO || sControlType2 == SI_CONTROL_TYPE_SCAN) {
                        WriteText("sC2", oInsItem.sConfigTwo);
                        WriteText("sV2", GetPhotoServerIDString(oInsItem.getId(), oInsItem.sValueTwo));
                    } else {
                        WriteText("sC2", oInsItem.sConfigTwo);
                        WriteText("sV2", oInsItem.sValueTwo);
                    }
                }
                if (sConfig3 != null && sConfig3.length() > 0) {
                    int sControlType3 = CommonInsItem.getControlType(sConfig3);
                    if (sControlType3 == SI_CONTROL_TYPE_PTO || sControlType3 == SI_CONTROL_TYPE_SCAN) {
                        WriteText("sC3", oInsItem.sConfigThree);
                        WriteText("sV3", GetPhotoServerIDString(oInsItem.getId(), oInsItem.sValueThree));
                    } else {
                        WriteText("sC3", oInsItem.sConfigThree);
                        WriteText("sV3", oInsItem.sValueThree);
                    }
                }
                if (sConfig4 != null && sConfig4.length() > 0) {
                    int sControlType4 = CommonInsItem.getControlType(sConfig4);
                    if (sControlType4 == SI_CONTROL_TYPE_PTO || sControlType4 == SI_CONTROL_TYPE_SCAN) {
                        WriteText("sC4", oInsItem.sConfigFour);
                        WriteText("sV4", GetPhotoServerIDString(oInsItem.getId(), oInsItem.sValueFour));
                    } else {
                        WriteText("sC4", oInsItem.sConfigFour);
                        WriteText("sV4", oInsItem.sValueFour);
                    }
                }
                if (sConfig5 != null && sConfig5.length() > 0) {
                    int sControlType5 = CommonInsItem.getControlType(sConfig5);
                    if (sControlType5 == SI_CONTROL_TYPE_PTO || sControlType5 == SI_CONTROL_TYPE_SCAN) {
                        WriteText("sC5", oInsItem.sConfigFive);
                        WriteText("sV5", GetPhotoServerIDString(oInsItem.getId(), oInsItem.sValueFive));
                    } else {
                        WriteText("sC5", oInsItem.sConfigFive);
                        WriteText("sV5", oInsItem.sValueFive);
                    }
                }
                if (sConfig6 != null && sConfig6.length() > 0) {
                    int sControlType6 = CommonInsItem.getControlType(sConfig6);
                    if (sControlType6 == SI_CONTROL_TYPE_PTO || sControlType6 == SI_CONTROL_TYPE_SCAN) {
                        WriteText("sC6", oInsItem.sConfigSix);
                        WriteText("sV6", GetPhotoServerIDString(oInsItem.getId(), oInsItem.sValueSix));
                    } else {
                        WriteText("sC6", oInsItem.sConfigSix);
                        WriteText("sV6", oInsItem.sValueSix);
                    }
                }
            } else if (sQType.equals("S")) {
                WriteText("sName", oInsItem.sName);
                WriteText("sV1", GetPhotoServerIDString(oInsItem.getId(), oInsItem.sValueOne));
                WriteText("sV2",  oInsItem.sValueTwo);
                WriteText("sV3",  oInsItem.sValueThree);
                WriteText("sV4",  oInsItem.sValueFour);
                WriteText("sV5",  oInsItem.sValueFive);
            } else if (sQType.equals("V")) {
                WriteText("sName", oInsItem.sName);
                WriteText("sV1", db_Media.GetVideoServerIDString(CommonHelper.getLong(oInsItem.sValueOne)));
            }

            List<ai_Notification> lsNotice = ai_Notification.find(ai_Notification.class, "I_INS_ITEM_ID=? and I_INS_ID=? and B_DELETED=0", "" + oInsItem.getId(), "" + oInsItem.iInsID);
            for (ai_Notification oNotice : lsNotice){
                serializer.startTag(null, "NOT");
                WriteText("sTitle", oNotice.sTitle);
                WriteText("sDescription", oNotice.sDescription);
                WriteText("sCustom1", oNotice.sCustomOne);
                WriteText("sCustom2", oNotice.sCustomTwo);
                WriteText("dtDateTime", oNotice.dtDateTime);
                WriteText("sPhotoURL", GetPhotoServerIDString(oInsItem.getId(), oNotice.sPhotoURL));
                WriteText("iVideoID", db_Media.GetVideoServerIDString(CommonHelper.getLong(oNotice.sVideoID)));
                serializer.endTag(null, "NOT");
            }
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.ProcessInsItemBody", ex, this);
        }

    }
    private void WriteText(String sTagName, String sValue){
        try{
            serializer.startTag(null, sTagName);
            serializer.text(sValue == null ? "" : sValue);
            serializer.endTag(null, sTagName);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.WriteText", ex, this);
        }
    }
    private boolean UploadVideo_ExternalInspection(List<ai_Video> lsVideo, String sTitle, String sTokenID, String sToken){
        try{
            if (lsVideo != null && lsVideo.size() > 0){
                int counter = 1;
                for (int i=0; i< lsVideo.size(); i++){

                    ai_Video oVideo = lsVideo.get(i);
                    if (CommonHelper.bFileExist(oVideo.getFile())){
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Prepare Video - " + counter, "Prepare Video - " + counter);
                        PublishProgress(UPDATE_PROGRESS, "", "Prepare Video " + (i + 1));
                        /*ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
                        lsParams.add(new BasicNameValuePair("iRequestID", sRequestID));
                        lsParams.add(new BasicNameValuePair("sRequestCode",  sRequestCode));
                        lsParams.add(new BasicNameValuePair("iVideoSize", "" + oVideo.iSize));*/
                        HashMap<String, String> lsParams = new HashMap<String, String>();
                        lsParams.put("iTokenID", sTokenID);
                        lsParams.put("sToken", sToken);

                        lsParams.put("iVideoSize", "" + oVideo.iSize);

                        if (!StringUtils.isEmpty(oVideo.getGeo())) lsParams.put("sGeo", oVideo.getGeo());

                        JSONObject oJson = IF_SyncClient.PostRequest("/SyncExternal/GetVideoToken_ExternalInspection", lsParams);
                        try{
                            if (oJson != null && oJson.getBoolean("success")){
                                if (!UploadVideoAction(this, oVideo, i + 1, oJson, progressCallback)){
                                    if (!UploadVideoAction(this, oVideo, i + 1, oJson, progressCallback)){
                                        sMessage.append("Failed Upload Video Stage 1").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                sMessage.append("Revoke Video Token");
                                oJson = IF_SyncClient.PostRequest("/SyncExternal/GetVideoToken_ExternalInspection", lsParams);
                                if (oJson != null && oJson.getBoolean("success")){
                                    if (!UploadVideoAction(this, oVideo, i + 1, oJson, progressCallback)){
                                        if (!UploadVideoAction(this, oVideo, i + 1, oJson, progressCallback)){
                                            sMessage.append("Failed Upload Video Stage2").append('\n');
                                            return false;
                                        }
                                    }
                                }
                                else{
                                    sMessage.append("Failed Prepare Video Stage 1").append('\n');
                                    return false;
                                }
                            }
                            int iSVideoID = oJson.getInt("iVideoID");
                            if (iSVideoID > 0){
                                lsParams.clear();
                               /* lsParams.add(new BasicNameValuePair("iRequestID", sRequestID));
                                lsParams.add(new BasicNameValuePair("sRequestCode",  sRequestCode));
                                lsParams.add(new BasicNameValuePair("iVideoID", "" + iSVideoID));*/


                                lsParams.put("iTokenID", sTokenID);
                                lsParams.put("sToken", sToken);

                                lsParams.put("iVideoID", "" + iSVideoID);

                                JSONObject oReturn = IF_SyncClient.PostRequest("/SyncExternal/UploadVideoSuccess_ExternalInspection", lsParams);
                                if (oReturn != null && oReturn.getBoolean("success")){
                                    oVideo.iSVideoID = iSVideoID;

                                    oVideo.bUploaded = true;
                                    oVideo.bProcessed = true;
                                    oVideo.bGetURL = true;

                                    oVideo.save();
//                                    CommonHelper.DeleteFile(oVideo.getFile());
//                                    CommonHelper.DeleteFile(oVideo.getThumb());
                                }
                                else{
                                    oReturn = IF_SyncClient.PostRequest("/SyncExternal/UploadVideoSuccess_ExternalInspection", lsParams);
                                    if (oReturn != null && oReturn.getBoolean("success")){
                                        oVideo.iSVideoID = iSVideoID;

                                        oVideo.bUploaded = true;
                                        oVideo.bProcessed = true;
                                        oVideo.bGetURL = true;
                                        oVideo.save();
//                                        CommonHelper.DeleteFile(oVideo.getFile());
//                                        CommonHelper.DeleteFile(oVideo.getThumb());
                                    }
                                    else{
                                        sMessage.append("Failed Upload Video Stage 3").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                sMessage.append("Failed Upload Video Stage 1.1").append('\n');
                                return false;
                            }
                        }catch (Exception ex){
                            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto.InterException", ex);
                            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadVideoSuccess_ExternalInspection.InterException", ex, this);
                            return false;
                        }
                        //  counter++;
                    }
                    counter++;
                }
                CommonDB.InsertLog(this, "Upload", "" + counter + " Videos Uploaded - " + sTitle);
                HashMap<String, Object> oMap7 = new HashMap<String, Object>();
                oMap7.put("Title", sTitle);
                oMap7.put("Videos", counter + " out of " + lsVideo.size() + " Uploaded");
                CommonHelper.trackEvent(this, "Android Sync Upload Inspection Video", oMap7);
            }
            else{
                HashMap<String, Object> oMap8 = new HashMap<String, Object>();
                oMap8.put("Title", sTitle);
                oMap8.put("Videos", "No Videos");
                CommonHelper.trackEvent(this, "Android Sync Upload Inspection Video", oMap8);
            }
            return true;
        }catch(Exception ex){
            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto", ex);
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadVideo_ExternalInspection", ex, this);
            return false;
        }
    }

    private boolean UploadVideo(List<ai_Video> lsVideo, String sTitle){
        try{
            if (lsVideo != null && lsVideo.size() > 0){
                int counter = 1;
                for (int i=0; i< lsVideo.size(); i++){

                    ai_Video oVideo = lsVideo.get(i);
                    if (CommonHelper.bFileExist(oVideo.getFile())){
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Prepare Video - " + counter, "Prepare Video - " + counter);
                        PublishProgress(UPDATE_PROGRESS, "", "Prepare Video " + (i + 1));
                        /*ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
                        lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
                        lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));
                        lsParams.add(new BasicNameValuePair("iVideoSize", "" + oVideo.iSize));*/
                        HashMap<String, String> lsParams = new HashMap<String, String>();
                        lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                        lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
                        lsParams.put("iVideoSize", "" + oVideo.iSize);

                        if (!StringUtils.isEmpty(oVideo.getGeo())) lsParams.put("sGeo", oVideo.getGeo());

                        JSONObject oJson = IF_SyncClient.PostRequest("/Sync/GetVideoToken", lsParams);
                        try{
                            if (oJson != null && oJson.getBoolean("success")){
                                if (!UploadVideoAction(this, oVideo, i + 1, oJson, progressCallback)){
                                    if (!UploadVideoAction(this, oVideo, i + 1, oJson, progressCallback)){
                                        sMessage.append("Failed Upload Video Stage 1").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                sMessage.append("Revoke Video Token");
                                oJson = IF_SyncClient.PostRequest("/Sync/GetVideoToken", lsParams);
                                if (oJson != null && oJson.getBoolean("success")){
                                    if (!UploadVideoAction(this, oVideo, i + 1, oJson, progressCallback)){
                                        if (!UploadVideoAction(this, oVideo, i + 1, oJson, progressCallback)){
                                            sMessage.append("Failed Upload Video Stage2").append('\n');
                                            return false;
                                        }
                                    }
                                }
                                else{
                                    sMessage.append("Failed Prepare Video Stage 1").append('\n');
                                    return false;
                                }
                            }
                            int iSVideoID = oJson.getInt("iVideoID");
                            if (iSVideoID > 0){
                                lsParams.clear();
                                /*lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
                                lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));
                                lsParams.add(new BasicNameValuePair("iVideoID", "" + iSVideoID));*/
                                lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                                lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));

                                lsParams.put("iVideoID", "" + iSVideoID);
                                JSONObject oReturn = IF_SyncClient.PostRequest("/Sync/UploadVideoSuccess", lsParams);
                                if (oReturn != null && oReturn.getBoolean("success")){
                                    oVideo.iSVideoID = iSVideoID;

                                    oVideo.bUploaded = true;
                                    oVideo.bProcessed = true;
                                    oVideo.bGetURL = true;

                                    oVideo.save();
//                                    CommonHelper.DeleteFile(oVideo.getFile());
//                                    CommonHelper.DeleteFile(oVideo.getThumb());
                                }
                                else{
                                    oReturn = IF_SyncClient.PostRequest("/Sync/UploadVideoSuccess", lsParams);
                                    if (oReturn != null && oReturn.getBoolean("success")){
                                        oVideo.iSVideoID = iSVideoID;

                                        oVideo.bUploaded = true;
                                        oVideo.bProcessed = true;
                                        oVideo.bGetURL = true;
                                        oVideo.save();
//                                        CommonHelper.DeleteFile(oVideo.getFile());
//                                        CommonHelper.DeleteFile(oVideo.getThumb());
                                    }
                                    else{
                                        sMessage.append("Failed Upload Video Stage 3").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                sMessage.append("Failed Upload Video Stage 1.1").append('\n');
                                return false;
                            }
                        }catch (Exception ex){
                            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto.InterException", ex);
                            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadVideo.InterException", ex, this);
                            return false;
                        }
                        //  counter++;
                    }
                    counter++;
                }
                CommonDB.InsertLog(this, "Upload", "" + counter + " Videos Uploaded - " + sTitle);
                HashMap<String, Object> oMap7 = new HashMap<String, Object>();
                oMap7.put("Title", sTitle);
                oMap7.put("Videos", counter + " out of " + lsVideo.size() + " Uploaded");
                CommonHelper.trackEvent(this, "Android Sync Upload Inspection Video", oMap7);
            }
            else{
                HashMap<String, Object> oMap8 = new HashMap<String, Object>();
                oMap8.put("Title", sTitle);
                oMap8.put("Videos", "No Videos");
                CommonHelper.trackEvent(this, "Android Sync Upload Inspection Video", oMap8);
            }
            return true;
        }catch(Exception ex){
            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto", ex);
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadVideo", ex, this);
            return false;
        }
    }

    private boolean UploadPhoto_ExternalInspection(List<ai_Photo> lsPhoto, String sTitle, String sTokenID, String sToken ){
        try{
            if (lsPhoto != null && lsPhoto.size() > 0){
                int counter = 1;
                for (ai_Photo oPhoto : lsPhoto){
                    if (CommonHelper.bFileExist(oPhoto.getFile())){
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Upload Photo - " + counter, "Upload Photo - " + counter);
                        PublishProgress(UPDATE_PROGRESS, "", "Upload Photo " + counter + " of " + lsPhoto.size());
                        /*ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
                        lsParams.add(new BasicNameValuePair("iRequestID", sRequestID));
                        lsParams.add(new BasicNameValuePair("sRequestCode", sRequestCode));
                        lsParams.add(new BasicNameValuePair("dtCreated", oPhoto.dtDateTime));
                        lsParams.add(new BasicNameValuePair("sPhotoComment", oPhoto.sComments));
                        lsParams.add(new BasicNameValuePair("iSize", "" + oPhoto.iSize));*/
                        HashMap<String, String> lsParams = new HashMap<String, String>();
                        lsParams.put("iTokenID", sTokenID);
                        lsParams.put("sToken", sToken);

                        lsParams.put("dtCreated", oPhoto.dtDateTime);
                        lsParams.put("sPhotoComment", oPhoto.sComments);
                        lsParams.put("iSize", "" + oPhoto.iSize);

                        if (!StringUtils.isEmpty(oPhoto.getGeo())) lsParams.put("sGeo", oPhoto.getGeo());

                        JSONObject oJson = IF_SyncClient.PostRequest("/SyncExternal/UploadPhoto_ExternalInspection", lsParams);
                        try{
                            if (oJson != null && oJson.getBoolean("success")){
                                if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                    if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                        sMessage.append("Failed Upload Photo Stage 2").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                oJson = IF_SyncClient.PostRequest("/SyncExternal/UploadPhoto_ExternalInspection", lsParams);
                                if (oJson != null && oJson.getBoolean("success")){
                                    if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                        if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                            sMessage.append("Failed Upload Photo Stage 2").append('\n');
                                            return false;
                                        }
                                    }
                                }
                                else{
                                    sMessage.append("Failed Upload Photo Stage 1").append('\n');
                                    return false;
                                }
                            }
                            int iSPhotoID = oJson.getInt("iSPhotoID");
                            if (iSPhotoID > 0){
                                lsParams.clear();

                               /* lsParams.add(new BasicNameValuePair("iRequestID", sRequestID));
                                lsParams.add(new BasicNameValuePair("sRequestCode", sRequestCode));
                                lsParams.add(new BasicNameValuePair("iSPhotoID", "" + iSPhotoID));*/
                                lsParams.put("iTokenID", sTokenID);
                                lsParams.put("sToken", sToken);

                                lsParams.put("iSPhotoID", "" + iSPhotoID);
                                JSONObject oReturn = IF_SyncClient.PostRequest("/SyncExternal/UploadPhotoSuccess_ExternalInspection", lsParams);
                                if (oReturn != null && oReturn.getBoolean("success")){
                                    oPhoto.iSPhotoID = iSPhotoID;
                                    oPhoto.bUploaded = true;

                                    oPhoto.save();
//                                    CommonHelper.DeleteFile(oPhoto.getThumb());
//                                    CommonHelper.DeleteFile(oPhoto.getFile());
                                }
                                else{
                                    oReturn = IF_SyncClient.PostRequest("/SyncExternal/UploadPhotoSuccess_ExternalInspection", lsParams);
                                    if (oReturn != null && oReturn.getBoolean("success")){
                                        oPhoto.iSPhotoID = iSPhotoID;
                                        oPhoto.bUploaded = true;
                                        oPhoto.save();
//                                        CommonHelper.DeleteFile(oPhoto.getThumb());
//                                        CommonHelper.DeleteFile(oPhoto.getFile());
                                    }
                                    else{
                                        sMessage.append("Failed Upload Photo Stage 3").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                sMessage.append("Failed Upload Photo Stage 1.1").append('\n');
                                return false;
                            }
                        }catch (Exception ex){
                            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto.InterException", ex);
                            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhotoSuccess_ExternalInspection.InterException", ex, this);
                            return false;
                        }
                        counter++;
                    }
                }
                HashMap<String, Object> oMap = new HashMap<String, Object>();
                oMap.put("Title", sTitle);
                oMap.put("Photos", counter + " out of " + lsPhoto.size() + " Uploaded");
                CommonHelper.trackEvent(this, "Android Upload Inspection Photos", oMap);
                CommonDB.InsertLog(this, "Upload", "" + counter + " Photos Uploaded - " + sTitle);
            }
            else{
                HashMap<String, Object> oMap = new HashMap<String, Object>();
                oMap.put("Title", sTitle);
                oMap.put("Photos", "no photo uploaded");
                CommonHelper.trackEvent(this, "Android Upload Inspection Photos", oMap);
                CommonDB.InsertLog(this, LogType.ClientLog.toString(), "Photo list empty or no photo (BYPASS)");

            }

        }catch(Exception ex){
            //ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhoto", ex, this);
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhoto_ExternalInspection", ex, this);
            return false;
        }
        return true;
    }

    private boolean savePhotoComments(List<ai_Photo> lsPhoto, String sTitle) {
        try {
            if (lsPhoto == null || lsPhoto.isEmpty()) return true;
            int counter = 1;
            for (ai_Photo oPhoto : lsPhoto) {
                if (oPhoto.isUploadedAndUsingWhenEditInspection() &&
                        CommonHelper.getInt(CommonJson.GetJsonKeyValue(Constants.Keys.bUpdateComment, oPhoto.sFieldOne)) == 1) {
                    PublishProgress(UPDATE_PROGRESS, "", "Save Photo " + counter + " Comments");
                    HashMap<String, String> lsParams = new HashMap<>();
                    lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                    lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));
                    lsParams.put("iPhotoID", "" + oPhoto.iSPhotoID);
                    lsParams.put("sComment", oPhoto.sComments);
                    JSONObject oReturn = IF_SyncClient.PostRequest("/IOAPI/SavePhotoComment", lsParams);
                    if (oReturn != null && oReturn.getBoolean("success")) {
                        oPhoto.sFieldOne = CommonJson.RemoveJsonKey(Constants.Keys.bUpdateComment, oPhoto.sFieldOne);
                        oPhoto.save();
                    } else {
                        oReturn = IF_SyncClient.PostRequest("/IOAPI/SavePhotoComment", lsParams);
                        if (oReturn != null && oReturn.getBoolean("success")) {
                            oPhoto.sFieldOne = CommonJson.RemoveJsonKey(Constants.Keys.bUpdateComment, oPhoto.sFieldOne);
                            oPhoto.save();
                        } else {
                            sMessage.append("Failed Save Photo Comments ").append(oPhoto.iSPhotoID).append('\n');
                            return false;
                        }
                    }
                    counter++;
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.savePhotoComments", ex, this);
            return false;
        }
        return true;
    }

    private boolean UploadPhoto(List<ai_Photo> lsPhoto, String sTitle){
        try{
            if (lsPhoto != null && !lsPhoto.isEmpty()){
                int counter = 1;
                for (ai_Photo oPhoto : lsPhoto){
                    if (CommonHelper.bFileExist(oPhoto.getFile())){
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Upload Photo - " + counter, "Upload Photo - " + counter);
                        PublishProgress(UPDATE_PROGRESS, "", "Upload Photo " + counter + " of " + lsPhoto.size());
                       /* ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
                        lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
                        lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));
                        lsParams.add(new BasicNameValuePair("dtCreated", oPhoto.dtDateTime));
                        lsParams.add(new BasicNameValuePair("sPhotoComment", oPhoto.sComments));
                        lsParams.add(new BasicNameValuePair("iSize", "" + oPhoto.iSize));*/
                        HashMap<String, String> lsParams = new HashMap<String, String>();
                        lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                        lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));

                        lsParams.put("dtCreated", oPhoto.dtDateTime);
                        lsParams.put("sPhotoComment", oPhoto.sComments);
                        lsParams.put("iSize", "" + oPhoto.iSize);

                        if (!StringUtils.isEmpty(oPhoto.getGeo())) lsParams.put("sGeo", oPhoto.getGeo());

                        JSONObject oJson = IF_SyncClient.PostRequest("/Sync/UploadPhoto", lsParams);
                        try{
                            if (oJson != null && oJson.getBoolean("success")){
                                if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                    if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                        sMessage.append("Failed Upload Photo Stage 2").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                oJson = IF_SyncClient.PostRequest("/Sync/UploadPhoto", lsParams);
                                if (oJson != null && oJson.getBoolean("success")){
                                    if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                        if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                            sMessage.append("Failed Upload Photo Stage 2").append('\n');
                                            return false;
                                        }
                                    }
                                }
                                else{
                                    sMessage.append("Failed Upload Photo Stage 1").append('\n');
                                    return false;
                                }
                            }
                            int iSPhotoID = oJson.getInt("iSPhotoID");
                            if (iSPhotoID > 0){
                                //lsParams.remove(2);lsParams.remove(2); lsParams.remove(2);
                                lsParams.clear();
                                /*lsParams.add(new BasicNameValuePair("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID")));
                                lsParams.add(new BasicNameValuePair("sToken", CommonHelper.GetPreferenceString(this, "sToken")));
                                lsParams.add(new BasicNameValuePair("iSPhotoID", "" + iSPhotoID));*/
                                lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(this, "iCustomerID"));
                                lsParams.put("sToken", CommonHelper.GetPreferenceString(this, "sToken"));

                                lsParams.put("iSPhotoID", "" + iSPhotoID);
                                JSONObject oReturn = IF_SyncClient.PostRequest("/Sync/UploadPhotoSuccess", lsParams);
                                if (oReturn != null && oReturn.getBoolean("success")){
                                    oPhoto.iSPhotoID = iSPhotoID;
                                    oPhoto.bUploaded = true;

                                    oPhoto.save();
//                                    CommonHelper.DeleteFile(oPhoto.getThumb());
//                                    CommonHelper.DeleteFile(oPhoto.getFile());
                                }
                                else{
                                    oReturn = IF_SyncClient.PostRequest("/Sync/UploadPhotoSuccess", lsParams);
                                    if (oReturn != null && oReturn.getBoolean("success")){
                                        oPhoto.iSPhotoID = iSPhotoID;
                                        oPhoto.bUploaded = true;
                                        oPhoto.save();
//                                        CommonHelper.DeleteFile(oPhoto.getThumb());
//                                        CommonHelper.DeleteFile(oPhoto.getFile());
                                    }
                                    else{
                                        sMessage.append("Failed Upload Photo Stage 3").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                sMessage.append("Failed Upload Photo Stage 1.1").append('\n');
                                return false;
                            }
                        }catch (Exception ex){
                            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto.InterException", ex);
                            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhoto.InterException", ex, this);
                            return false;
                        }
                        counter++;
                    }
                }
                HashMap<String, Object> oMap = new HashMap<String, Object>();
                oMap.put("Title", sTitle);
                oMap.put("Photos", counter + " out of " + lsPhoto.size() + " Uploaded");
                CommonHelper.trackEvent(this, "Android Upload Inspection Photos", oMap);
                CommonDB.InsertLog(this, "Upload", "" + counter + " Photos Uploaded - " + sTitle);
            }
            else{
                HashMap<String, Object> oMap = new HashMap<String, Object>();
                oMap.put("Title", sTitle);
                oMap.put("Photos", "no photo uploaded");
                CommonHelper.trackEvent(this, "Android Upload Inspection Photos", oMap);
                CommonDB.InsertLog(this, LogType.ClientLog.toString(), "Photo list empty or no photo (BYPASS)");

            }

        }catch(Exception ex){
            //ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhoto", ex, this);
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhoto", ex, this);
            return false;
        }
        return true;
    }
    private boolean UploadPhoto_RequestInspection(List<ai_Photo> lsPhoto, String sTitle, String sRequestID, String sRequestCode ){
        try{
            if (lsPhoto != null && lsPhoto.size() > 0){
                int counter = 1;
                for (ai_Photo oPhoto : lsPhoto){
                    if (CommonHelper.bFileExist(oPhoto.getFile())){
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Upload Photo - " + counter, "Upload Photo - " + counter);
                        PublishProgress(UPDATE_PROGRESS, "", "Upload Photo " + counter + " of " + lsPhoto.size());
                        /*ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
                        lsParams.add(new BasicNameValuePair("iRequestID", sRequestID));
                        lsParams.add(new BasicNameValuePair("sRequestCode", sRequestCode));
                        lsParams.add(new BasicNameValuePair("dtCreated", oPhoto.dtDateTime));
                        lsParams.add(new BasicNameValuePair("sPhotoComment", oPhoto.sComments));
                        lsParams.add(new BasicNameValuePair("iSize", "" + oPhoto.iSize));*/
                        HashMap<String, String> lsParams = new HashMap<String, String>();
                        lsParams.put("iRequestID", sRequestID);
                        lsParams.put("sRequestCode", sRequestCode);

                        lsParams.put("dtCreated", oPhoto.dtDateTime);
                        lsParams.put("sPhotoComment", oPhoto.sComments);
                        lsParams.put("iSize", "" + oPhoto.iSize);

                        if (!StringUtils.isEmpty(oPhoto.getGeo())) lsParams.put("sGeo", oPhoto.getGeo());

                        JSONObject oJson = IF_SyncClient.PostRequest("/Sync/UploadPhoto_RequestInspection", lsParams);
                        try{
                            if (oJson != null && oJson.getBoolean("success")){
                                if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                    if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                        sMessage.append("Failed Upload Photo Stage 2").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                oJson = IF_SyncClient.PostRequest("/Sync/UploadPhoto_RequestInspection", lsParams);
                                if (oJson != null && oJson.getBoolean("success")){
                                    if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                        if (IF_SyncClient.UploadPhoto(oJson.getString("sURL"), oPhoto) != 200){
                                            sMessage.append("Failed Upload Photo Stage 2").append('\n');
                                            return false;
                                        }
                                    }
                                }
                                else{
                                    sMessage.append("Failed Upload Photo Stage 1").append('\n');
                                    return false;
                                }
                            }
                            int iSPhotoID = oJson.getInt("iSPhotoID");
                            if (iSPhotoID > 0){
                                lsParams.clear();

                               /* lsParams.add(new BasicNameValuePair("iRequestID", sRequestID));
                                lsParams.add(new BasicNameValuePair("sRequestCode", sRequestCode));
                                lsParams.add(new BasicNameValuePair("iSPhotoID", "" + iSPhotoID));*/
                                lsParams.put("iRequestID", sRequestID);
                                lsParams.put("sRequestCode", sRequestCode);

                                lsParams.put("iSPhotoID", "" + iSPhotoID);
                                JSONObject oReturn = IF_SyncClient.PostRequest("/Sync/UploadPhotoSuccess_RequestInspection", lsParams);
                                if (oReturn != null && oReturn.getBoolean("success")){
                                    oPhoto.iSPhotoID = iSPhotoID;
                                    oPhoto.bUploaded = true;

                                    oPhoto.save();
//                                    CommonHelper.DeleteFile(oPhoto.getThumb());
//                                    CommonHelper.DeleteFile(oPhoto.getFile());
                                }
                                else{
                                    oReturn = IF_SyncClient.PostRequest("/Sync/UploadPhotoSuccess_RequestInspection", lsParams);
                                    if (oReturn != null && oReturn.getBoolean("success")){
                                        oPhoto.iSPhotoID = iSPhotoID;
                                        oPhoto.bUploaded = true;
                                        oPhoto.save();
//                                        CommonHelper.DeleteFile(oPhoto.getThumb());
//                                        CommonHelper.DeleteFile(oPhoto.getFile());
                                    }
                                    else{
                                        sMessage.append("Failed Upload Photo Stage 3").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                sMessage.append("Failed Upload Photo Stage 1.1").append('\n');
                                return false;
                            }
                        }catch (Exception ex){
                            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto.InterException", ex);
                            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhoto_RequestInspection.InterException", ex, this);
                            return false;
                        }
                        counter++;
                    }
                }
                HashMap<String, Object> oMap = new HashMap<String, Object>();
                oMap.put("Title", sTitle);
                oMap.put("Photos", counter + " out of " + lsPhoto.size() + " Uploaded");
                CommonHelper.trackEvent(this, "Android Upload Inspection Photos", oMap);
                CommonDB.InsertLog(this, "Upload", "" + counter + " Photos Uploaded - " + sTitle);
            }
            else{
                HashMap<String, Object> oMap = new HashMap<String, Object>();
                oMap.put("Title", sTitle);
                oMap.put("Photos", "no photo uploaded");
                CommonHelper.trackEvent(this, "Android Upload Inspection Photos", oMap);
                CommonDB.InsertLog(this, LogType.ClientLog.toString(), "Photo list empty or no photo (BYPASS)");

            }

        }catch(Exception ex){
            //ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhoto", ex, this);
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadPhoto_RequestInspection", ex, this);
            return false;
        }
        return true;
    }
    private boolean UploadVideo_RequestInspection(List<ai_Video> lsVideo, String sTitle, String sRequestID, String sRequestCode){
        try{
            if (lsVideo != null && lsVideo.size() > 0){
                int counter = 1;

                for (int i=0; i< lsVideo.size(); i++){

                    ai_Video oVideo = lsVideo.get(i);
                    if (CommonHelper.bFileExist(oVideo.getFile())){
                        //Notice("Upload Inspections - " + sCurrentUploadTitle , "Prepare Video - " + counter, "Prepare Video - " + counter);
                        PublishProgress(UPDATE_PROGRESS, "", "Prepare Video " + counter);
                        /*ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
                        lsParams.add(new BasicNameValuePair("iRequestID", sRequestID));
                        lsParams.add(new BasicNameValuePair("sRequestCode",  sRequestCode));
                        lsParams.add(new BasicNameValuePair("iVideoSize", "" + oVideo.iSize));*/
                        HashMap<String, String> lsParams = new HashMap<String, String>();
                        lsParams.put("iRequestID", sRequestID);
                        lsParams.put("sRequestCode", sRequestCode);

                        lsParams.put("iVideoSize", "" + oVideo.iSize);

                        if (!StringUtils.isEmpty(oVideo.getGeo())) lsParams.put("sGeo", oVideo.getGeo());

                        JSONObject oJson = IF_SyncClient.PostRequest("/Sync/GetVideoToken_RequestInspection", lsParams);
                        try{
                            if (oJson != null && oJson.getBoolean("success")){
                                if (!UploadVideoAction(this, oVideo, counter, oJson, progressCallback)){
                                    if (!UploadVideoAction(this, oVideo, counter, oJson, progressCallback)){
                                        sMessage.append("Failed Upload Video Stage 1").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                sMessage.append("Revoke Video Token");
                                oJson = IF_SyncClient.PostRequest("/Sync/GetVideoToken_RequestInspection", lsParams);
                                if (oJson != null && oJson.getBoolean("success")){
                                    if (!UploadVideoAction(this, oVideo, counter, oJson, progressCallback)){
                                        if (!UploadVideoAction(this, oVideo, counter, oJson, progressCallback)){
                                            sMessage.append("Failed Upload Video Stage2").append('\n');
                                            return false;
                                        }
                                    }
                                }
                                else{
                                    sMessage.append("Failed Prepare Video Stage 1").append('\n');
                                    return false;
                                }
                            }
                            int iSVideoID = oJson.getInt("iVideoID");
                            if (iSVideoID > 0){
                                lsParams.clear();
                               /* lsParams.add(new BasicNameValuePair("iRequestID", sRequestID));
                                lsParams.add(new BasicNameValuePair("sRequestCode",  sRequestCode));
                                lsParams.add(new BasicNameValuePair("iVideoID", "" + iSVideoID));*/


                                lsParams.put("iRequestID", sRequestID);
                                lsParams.put("sRequestCode", sRequestCode);

                                lsParams.put("iVideo", "" + iSVideoID);

                                JSONObject oReturn = IF_SyncClient.PostRequest("/Sync/UploadVideoSuccess_RequestInspection", lsParams);
                                if (oReturn != null && oReturn.getBoolean("success")){
                                    oVideo.iSVideoID = iSVideoID;

                                    oVideo.bUploaded = true;
                                    oVideo.bProcessed = true;
                                    oVideo.bGetURL = true;

                                    oVideo.save();
//                                    CommonHelper.DeleteFile(oVideo.getFile());
//                                    CommonHelper.DeleteFile(oVideo.getThumb());
                                }
                                else{
                                    oReturn = IF_SyncClient.PostRequest("/Sync/UploadVideoSuccess_RequestInspection", lsParams);
                                    if (oReturn != null && oReturn.getBoolean("success")){
                                        oVideo.iSVideoID = iSVideoID;

                                        oVideo.bUploaded = true;
                                        oVideo.bProcessed = true;
                                        oVideo.bGetURL = true;
                                        oVideo.save();
//                                        CommonHelper.DeleteFile(oVideo.getFile());
//                                        CommonHelper.DeleteFile(oVideo.getThumb());
                                    }
                                    else{
                                        sMessage.append("Failed Upload Video Stage 3").append('\n');
                                        return false;
                                    }
                                }
                            }
                            else{
                                sMessage.append("Failed Upload Video Stage 1.1").append('\n');
                                return false;
                            }
                        }catch (Exception ex){
                            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto.InterException", ex);
                            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadVideo_RequestInspection.InterException", ex, this);
                            return false;
                        }
                        //  counter++;
                    }
                    counter++;
                }
                CommonDB.InsertLog(this, "Upload", "" + counter + " Videos Uploaded - " + sTitle);
                HashMap<String, Object> oMap7 = new HashMap<String, Object>();
                oMap7.put("Title", sTitle);
                oMap7.put("Videos", counter + " out of " + lsVideo.size() + " Uploaded");
                CommonHelper.trackEvent(this, "Android Sync Upload Inspection Video", oMap7);
            }
            else{
                HashMap<String, Object> oMap8 = new HashMap<String, Object>();
                oMap8.put("Title", sTitle);
                oMap8.put("Videos", "No Videos");
                CommonHelper.trackEvent(this, "Android Sync Upload Inspection Video", oMap8);
            }
            return true;
        }catch(Exception ex){
            //ai_BugHandler.ai_Handler_Exception("per", "IF_SyncClient.UploadPhoto", ex);
            ai_BugHandler.ai_Handler_Exception("Exception", "UploadService.UploadVideo_RequestInspection", ex, this);
            return false;
        }
    }

    /**
     * Created by TerrySun on 12/10/14.
     */
    public static class UploadLogService {
    }

    private void serializeProductCosts(XmlSerializer serializer, int childInsItemId) throws IOException {
        List<ai_ProductCost> allCosts = db_ProductCost.getAllProductCosts(childInsItemId);
        if (ArrayUtils.isNotEmpty(allCosts)) {
            serializer.startTag(null, "Costs");
            for (ai_ProductCost cost : allCosts) {
                serializer.startTag(null, "Cost");
                serializer.attribute(null, "iProductID", String.valueOf(cost.iProductID));
                serializer.attribute(null, "dUnit", String.valueOf(cost.dUnit));
                serializer.attribute(null, "dUnitCost", String.valueOf(cost.dUnitCost));
                serializer.attribute(null, "dTotalCost", String.valueOf(cost.dTotalCost));
                serializer.startTag(null, "sNotes");
                serializer.cdsect(StringUtils.ifEmpty(cost.sNotes, ""));
                serializer.endTag(null, "sNotes");
                serializer.endTag(null, "Cost");
            }
            serializer.endTag(null, "Costs");
        }
    }
}