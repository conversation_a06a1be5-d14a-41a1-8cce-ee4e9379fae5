package com.snapinspect.snapinspect3.activity;

import android.Manifest;
import android.annotation.SuppressLint;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.pm.PackageManager;
import android.graphics.*;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.hardware.camera2.CameraMetadata;
import android.hardware.camera2.CaptureRequest;
import android.location.Location;
import android.net.Uri;
import android.os.*;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.*;
import android.view.ScaleGestureDetector.SimpleOnScaleGestureListener;
import android.view.animation.Animation;
import android.view.animation.AnimationUtils;
import android.widget.*;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.camera.core.*;
import androidx.camera.core.Camera;
import androidx.camera.camera2.interop.Camera2Interop;
import androidx.camera.lifecycle.ProcessCameraProvider;
import androidx.camera.video.*;
import androidx.camera.video.VideoCapture;
import androidx.camera.view.PreviewView;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.google.common.util.concurrent.ListenableFuture;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.squareup.picasso.Picasso;

import java.io.File;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static android.view.ViewGroup.LayoutParams.MATCH_PARENT;
import static android.view.ViewGroup.LayoutParams.WRAP_CONTENT;
import static androidx.camera.video.VideoRecordEvent.Finalize.ERROR_DURATION_LIMIT_REACHED;
import static androidx.camera.video.VideoRecordEvent.Finalize.ERROR_NONE;
import static com.snapinspect.snapinspect3.Helper.Constants.Values.DEFAULT_VIDEO_QUALITY;

public class if_video extends AppCompatActivity implements SensorEventListener {

    private static final String CLASS_LABEL = "if_video";
    private static final String TAG = "if_video";
    private static final int REQUEST_PERMISSIONS = 10;
    private static final String[] CAMERA_PERMISSIONS = {
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.CAMERA,
    };

    // Video quality config, which might be from ai_InsType.sFieldOne
    private VideoQuality videoQuality;
    private PreviewView previewView;
    private ImageCapture imageCapture;
    private VideoCapture<Recorder> videoCapture;
    private ExecutorService cameraExecutor;
    private Camera camera;
    private Recorder recorder;
    private Recording recording;

    private boolean mRecording = false;
    private boolean isPreviewOn = false;
    private int lensFacing = CameraSelector.LENS_FACING_BACK;

    private SensorManager mSensorManager;
    private Sensor mOrientation;

    private boolean bFlash = false;
    private boolean bAutoFocus = false;
    private boolean bManualFocus;
    private BroadcastReceiver mReceiver = null;
    private boolean isFlashOn = false;

    private Button shutterBtn;
    private Button switchBtn;
    private Button lightBtn;
    private Button focusBtn;
    private ImageView previewImageView;
    private TextView timeLab;
    private TextView blinkLabel;
    private DrawingView drawingView;
    private TextView zoomLevelTextView;

    private long iInsItemID, iNoticeID;
    private int iVideoLength;

    private Animation blkAnimation;
    private PowerManager.WakeLock mWakeLock;

    private Timer timer;
    private TimerTask timerTask;
    private final Handler handler = new Handler(Looper.getMainLooper());
    private long mCurrentRecordingStartTime = 0;
    private boolean isFinishRecording = false;

    public static Intent newIntent(
        Context context, long iNoticeID, long iInsItemID, int iVideoLength, VideoQuality videoQuality
    ) {
        Intent intent = new Intent(context, if_video.class);
        intent.putExtra(Constants.Extras.iNoticeID, iNoticeID);
        intent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        intent.putExtra(Constants.Extras.iVideoLength, iVideoLength);
        intent.putExtra(Constants.Extras.VIDEO_QUALITY, videoQuality.getQualityName());
        return intent;
    }

    @SuppressLint("InvalidWakeLockTag")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
        setContentView(R.layout.activity_if_video_recorder);
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
        getWindow().setFormat(PixelFormat.TRANSLUCENT);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN);

        PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
        mWakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK, CLASS_LABEL);
        mWakeLock.acquire();

        iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, 0);
        iNoticeID = getIntent().getLongExtra(Constants.Extras.iNoticeID, 0);
        iVideoLength = getIntent().getIntExtra(Constants.Extras.iVideoLength, 300000);
        // Get the video quality from ai_InsType.sFieldOne
        String sVideoQuality = getIntent().getStringExtra(Constants.Extras.VIDEO_QUALITY);
        videoQuality = Objects.requireNonNullElse(
                VideoQuality.fromQualityName(sVideoQuality), DEFAULT_VIDEO_QUALITY);

        initialize();
        setLayout();

        cameraExecutor = Executors.newSingleThreadExecutor();

        if (allPermissionsGranted()) {
            previewView.postDelayed(this::startCamera, 1000);
        } else {
            ActivityCompat.requestPermissions(this, CAMERA_PERMISSIONS, REQUEST_PERMISSIONS);
        }

        CommonValidate.Permission_Validate(this);
        
        // request location permission
        CommonValidate.requestPermissions(this, Constants.LOCATION_PERMISSIONS);

        // request write_external_storage permission if needed
        CommonValidate.validateWriteExternalStoragePermission(this, REQUEST_PERMISSIONS);
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constants.RequestCodes.iVideoClips
                && resultCode == if_VideoClips.RESULT_START_MERGE) {
            startMerge();
        }
    }

    public void setLayout() {
        int sHeight = getWindowManager().getDefaultDisplay().getHeight();
        int sWidth = getWindowManager().getDefaultDisplay().getWidth();

        RelativeLayout topView = findViewById(R.id.top_view);
        RelativeLayout botView = findViewById(R.id.bottom_view);
        RelativeLayout.LayoutParams paramsTop = (RelativeLayout.LayoutParams) topView.getLayoutParams();
        RelativeLayout.LayoutParams paramsBot = (RelativeLayout.LayoutParams) botView.getLayoutParams();

        bAutoFocus = getPackageManager().hasSystemFeature(PackageManager.FEATURE_CAMERA_AUTOFOCUS);
        bManualFocus = CommonHelper.GetPreferenceBoolean(if_video.this, "bVideoFocus");

        if (bAutoFocus) {
            drawingView = new DrawingView(this);
            RelativeLayout.LayoutParams layoutParamsDrawing = new RelativeLayout.LayoutParams(WRAP_CONTENT, MATCH_PARENT);
            layoutParamsDrawing.setMargins(paramsTop.leftMargin + paramsTop.width + sHeight, 0, sWidth - paramsBot.width - paramsTop.width, 0);
            drawingView.setLayoutParams(layoutParamsDrawing);
            this.addContentView(drawingView, layoutParamsDrawing);
        }
    }

    public void initialize() {
        mSensorManager = (SensorManager) getSystemService(Context.SENSOR_SERVICE);
        mOrientation = mSensorManager.getDefaultSensor(Sensor.TYPE_ORIENTATION);
        previewView = findViewById(R.id.camera_view);

        switchBtn = findViewById(R.id.switchBtn);
        lightBtn = findViewById(R.id.lightBtn);
        focusBtn = findViewById(R.id.focusBtn);
        focusBtn.setVisibility(View.GONE);
        previewImageView = findViewById(R.id.previewImageView);

        timeLab = findViewById(R.id.timeLab);
        zoomLevelTextView = findViewById(R.id.zoomLevelText);
        blinkLabel = findViewById(R.id.blinkLabel);
        blinkLabel.setVisibility(View.GONE);
        shutterBtn = findViewById(R.id.shutterBtn);
        shutterBtn.setOnClickListener(view -> {
            if (!CommonUI.bAppPermission(if_video.this)) {
                return;
            }
            view.setEnabled(false);
            if (mRecording) {
                stopVideoRecording();
            } else {
                startVideoRecording();
            }
            view.setEnabled(true);
        });

        updatePreviewThumbnail();
        timeLab.setText(DateUtils.clockStringFromMilliseconds(getTotalVideoDuration()));
    }

    public void startTimer() {
        timer = new Timer();
        initializeTimerTask();
        timer.schedule(timerTask, 0, 1000);
    }

    public void stopTimer() {
        if (timer != null) {
            timer.cancel();
            timer = null;
        }
    }

    public void initializeTimerTask() {
        mCurrentRecordingStartTime = System.currentTimeMillis();
        timerTask = new TimerTask() {
            public void run() {
                handler.post(() -> updateTimeTextView());
            }
        };
    }

    private void updateTimeTextView() {
        long currentDuration = System.currentTimeMillis() - mCurrentRecordingStartTime;
        String sTime = DateUtils.clockStringFromMilliseconds(getTotalVideoDuration() + currentDuration);
        Log.d(TAG, "Current Duration: " + sTime);
        timeLab.setText(sTime);
    }

    private void updatePreviewThumbnail() {
        List<ai_Video> videoClips = getVideoClips();
        if (videoClips.isEmpty()) return;
        ai_Video recentVideo = videoClips.get(videoClips.size() - 1);
        Picasso.get()
                .load(new File(recentVideo.getThumb()))
                .into(previewImageView);
    }

    private void startCamera() {
        ListenableFuture<ProcessCameraProvider> cameraProviderFuture = ProcessCameraProvider.getInstance(this);

        cameraProviderFuture.addListener(() -> {
            try {
                ProcessCameraProvider cameraProvider = cameraProviderFuture.get();
                bindCameraUseCases(cameraProvider);
                restoreCameraZoomRatio();
            } catch (ExecutionException | InterruptedException e) {
                Log.e(TAG, "Error starting camera: " + e.getMessage());
            }
        }, ContextCompat.getMainExecutor(this));
    }

    private CameraSelector getDefaultCameraSelector() {
        return new CameraSelector.Builder()
                .requireLensFacing(lensFacing)
                .build();
    }

    @SuppressLint("RestrictedApi")
    private void bindCameraUseCases(ProcessCameraProvider cameraProvider) {
        DisplayMetrics metrics = new DisplayMetrics();
        if (previewView.getDisplay() == null) return;

        previewView.getDisplay().getRealMetrics(metrics);
        int rotation = previewView.getDisplay().getRotation();

        CameraSelector wideAngleCamera = CommonUI.getWideAngleCameraSelector(cameraProvider);
        //Select Camera
        CameraSelector cameraSelector = wideAngleCamera != null ? wideAngleCamera : getDefaultCameraSelector();
        Preview.Builder previewBuilder = new Preview.Builder()
                .setPreviewStabilizationEnabled(true)
                .setDefaultResolution(videoQuality.getResolution());

        try {
            // Additional stabilization using Camera2Interop for better compatibility
            Camera2Interop.Extender previewExtender = new Camera2Interop.Extender(previewBuilder);
            previewExtender.setCaptureRequestOption(
                    CaptureRequest.CONTROL_VIDEO_STABILIZATION_MODE,
                    CameraMetadata.CONTROL_VIDEO_STABILIZATION_MODE_ON);
            previewExtender.setCaptureRequestOption(
                    CaptureRequest.LENS_OPTICAL_STABILIZATION_MODE,
                    CameraMetadata.LENS_OPTICAL_STABILIZATION_MODE_ON);
        } catch (Exception e) {
            Log.e(TAG, "Failed to enable video stabilization", e);
        }

        Preview preview = previewBuilder.build();
        preview.setSurfaceProvider(previewView.getSurfaceProvider());

        recorder = new Recorder.Builder()
                // Use saved quality setting
                .setQualitySelector(QualitySelector.from(videoQuality.getQuality()))
                // Set bitrate based on saved setting
                .setTargetVideoEncodingBitRate(videoQuality.getBitRate())
                .build();

        VideoCapture.Builder<Recorder> videoCaptureBuilder = new VideoCapture.Builder<>(recorder)
                .setTargetRotation(rotation)
                .setVideoStabilizationEnabled(true)
                // Set fps based on saved setting
                .setTargetFrameRate(videoQuality.getFrameRateRange())
                .setDefaultResolution(videoQuality.getResolution());

        try {
            // Enable video stabilization
            Camera2Interop.Extender extender = new Camera2Interop.Extender(videoCaptureBuilder);
            extender.setCaptureRequestOption(
                    CaptureRequest.CONTROL_VIDEO_STABILIZATION_MODE,
                    CameraMetadata.CONTROL_VIDEO_STABILIZATION_MODE_ON);
            extender.setCaptureRequestOption(
                    CaptureRequest.LENS_OPTICAL_STABILIZATION_MODE,
                    CameraMetadata.LENS_OPTICAL_STABILIZATION_MODE_ON);
        } catch (Exception e) {
            Log.e(TAG, "Failed to enable video stabilization", e);
        }

        videoCapture = videoCaptureBuilder.build();

        try {
            cameraProvider.unbindAll();
            camera = cameraProvider.bindToLifecycle(this, cameraSelector, preview, videoCapture);
            enableZoomAndFocusFeatures();
        } catch (Exception e) {
            Log.e(TAG, "Use case binding failed", e);
        }
    }

    private void updateZoomLevelTextView(float zoomRatio) {
        zoomLevelTextView.setText(getString(R.string.camera_zoom_level_value, String.format("%.1f", zoomRatio)));
    }

    private void restoreCameraZoomRatio() {
        if (camera == null) return;
        ZoomState zoomState = camera.getCameraInfo().getZoomState().getValue();
        if (zoomState == null) return;
        float latestZoomValue;
        float savedZoomValue = CommonHelper.getFloat(
                CommonHelper.GetPreferenceString(this,  Constants.Keys.sVideoZoomLevel)
        );
        if (savedZoomValue > 0) {
            latestZoomValue = savedZoomValue;
            camera.getCameraControl().setZoomRatio(latestZoomValue);
        } else {
            latestZoomValue = zoomState.getZoomRatio();
        }
        updateZoomLevelTextView(latestZoomValue);
    }

    @SuppressLint({"ClickableViewAccessibility", "RestrictedApi"})
    private void enableZoomAndFocusFeatures() {
        if (camera == null) return;
        ZoomState zoomState = camera.getCameraInfo().getZoomState().getValue();
        if (zoomState != null) {
            ScaleGestureDetector scaleGestureDetector = new ScaleGestureDetector(this, new SimpleOnScaleGestureListener() {
                @Override
                public boolean onScale(ScaleGestureDetector detector) {
                    ZoomState zoomState = camera.getCameraInfo().getZoomState().getValue();
                    if (zoomState != null) {
                        float value = Math.min(
                            zoomState.getZoomRatio() * detector.getScaleFactor(),
                            Constants.Limits.iMaxiumVideoLevel
                        );
                        camera.getCameraControl().setZoomRatio(value);
                        updateZoomLevelTextView(value);
                        CommonHelper.SavePreference(
                                if_video.this, Constants.Keys.sVideoZoomLevel,  zoomState.getZoomRatio() + "");
                    }
                    return true;
                }
            });

            previewView.setOnTouchListener((v, event) -> {
                scaleGestureDetector.onTouchEvent(event);
                return true;
            });
        }
    }

    private void startVideoRecording() {
        if (videoCapture == null || recorder == null) return;
        int availVideoLength = iVideoLength - (int) getTotalVideoDuration();
        if (availVideoLength <= 0) {
            CommonUI.showToast(this, "Video duration limit reached");
            return;
        }

        mRecording = true;
        shutterBtn.setBackgroundResource(R.drawable.record_stop);
        previewImageView.setEnabled(false);
        startTimer();

        String videoFileName = getTempClipVideoPath();
        File videoFile = new File(videoFileName);

        FileOutputOptions fileOutputOptions = new FileOutputOptions.Builder(videoFile)
                .setDurationLimitMillis(availVideoLength)
                .setLocation(GeoLocationManager.getInstance(this).getLocation())
                .build();

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED ||
                ActivityCompat.checkSelfPermission(this, Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
            validateVideoPermission();
            return;
        }
        recording = recorder.prepareRecording(this, fileOutputOptions)
                .withAudioEnabled()
                .start(ContextCompat.getMainExecutor(this), videoRecordEvent -> {
                    if (videoRecordEvent instanceof VideoRecordEvent.Start) {
                        // Recording started successfully
                    } else if (videoRecordEvent instanceof VideoRecordEvent.Finalize) {
                        int error = ((VideoRecordEvent.Finalize) videoRecordEvent).getError();
                        if (error == ERROR_NONE) {
                            saveVideoRecord(videoFileName);
                        } else if (error == ERROR_DURATION_LIMIT_REACHED) {
                            stopVideoRecording();
                            saveVideoRecord(videoFileName);
                            CommonUI.showToast(this, "Video duration limit reached");
                        } else {
                            CommonHelper.trackEvent(this, "Video Recording Failed", "Error",
                                "" + ((VideoRecordEvent.Finalize) videoRecordEvent).getError());
                            // Handle recording error
                            Log.e(TAG, "Video capture failed: " + ((VideoRecordEvent.Finalize) videoRecordEvent).getError());
                        }

                        // done recording
                        if (isFinishRecording) doneFinishRecording();
                    }
                });
    }

    private void stopVideoRecording() {
        if (recording == null) return;

        mRecording = false;
        shutterBtn.setBackgroundResource(R.drawable.record_start);

        recording.stop();
        recording = null;
        stopTimer();

        previewImageView.setEnabled(true);
        updatePreviewThumbnail();
    }

    public void startBlinkingAnimation() {
        if (validateVideoPermission()) {
            if (blinkLabel.getVisibility() == View.GONE) {
                blinkLabel.setVisibility(View.VISIBLE);
                blinkLabel.setText(R.string.video_recording_orientation_alert);
                blkAnimation = AnimationUtils.loadAnimation(getApplicationContext(), R.anim.blinking_animation);
                blinkLabel.startAnimation(blkAnimation);
            }
        }
    }

    public void stopBlinkAnimation() {
        if (blkAnimation != null && blinkLabel.getVisibility() == View.VISIBLE) {
            blkAnimation.cancel();
            blkAnimation.reset();
            blkAnimation = null;
            blinkLabel.setVisibility(View.GONE);
            blinkLabel.clearAnimation();
        }
    }

    public void doneClicked(View v) {
        if (mRecording) {
            isFinishRecording = true;
            stopVideoRecording();
        } else {
            doneFinishRecording();
        }
    }

    public void doneFinishRecording() {
        if (getVideoClips().size() > 1) {
            startMerge();
        } else {
            didFinishMergingVideos();
            finish();
        }
    }

    public void previewClicked(View v) {
        Intent intent = if_VideoClips.newIntent(this, iInsItemID, iNoticeID);
        startActivityForResult(intent, Constants.RequestCodes.iVideoClips);
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == KeyEvent.KEYCODE_BACK) {
            return false;
        }
        return super.onKeyDown(keyCode, event);
    }

    private void startMerge() {
        AsyncTask<String, Integer, O_FileName> task = new MergeVideo(
            this,
            getMainVideoClip(),
            getVideoClips(), 
            oFileName -> didFinishMergingVideos()
        );
        task.execute();
    }

    public void lightClicked(View v) {
        toggleFlash();
    }

    public void focusClicked(View v) {
        if (!bAutoFocus) {
            return;
        }

        if (bManualFocus) {
            bManualFocus = false;
            focusBtn.setBackgroundResource(R.drawable.a_focus);
        } else {
            bManualFocus = true;
            focusBtn.setBackgroundResource(R.drawable.m_focus);
        }

        CommonHelper.SavePreferenceBoolean(if_video.this, "bVideoFocus", bManualFocus);
    }

    public void switchClicked(View v) {
        // Toggle flash if it's on before switching camera
        if (isFlashOn) toggleFlash();
        switchCamera();
    }

    private void switchCamera() {
        lensFacing = (lensFacing == CameraSelector.LENS_FACING_BACK) ?
                CameraSelector.LENS_FACING_FRONT : CameraSelector.LENS_FACING_BACK;
        startCamera();

        if (lensFacing == CameraSelector.LENS_FACING_FRONT)
            lightBtn.setVisibility(View.GONE);
        else {
            lightBtn.setVisibility(View.VISIBLE);
            if (isFlashOn) {
                camera.getCameraControl().enableTorch(true);
            }
        }

        switchBtn.setBackgroundResource(R.drawable.switch_clicked);

        new Handler().postDelayed(() -> switchBtn.setBackgroundResource(R.drawable.swich), 200);
    }

    private void toggleFlash() {
        if (camera == null) return;
        try {
            Integer torchState = camera.getCameraInfo().getTorchState().getValue();
            if (torchState == null) return;
            boolean enable = torchState != TorchState.ON;
            camera.getCameraControl().enableTorch(enable);
            isFlashOn = enable;
            lightBtn.setBackgroundResource(enable ? R.drawable.light_on : R.drawable.light_off);
            CommonHelper.SavePreferenceBoolean(this, "bVideoFlash", enable);
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    @SuppressLint({"SetTextI18n", "InvalidWakeLockTag"})
    @Override
    protected void onResume() {
        super.onResume();

        if (!validateVideoPermission()) {
            blinkLabel.setVisibility(View.VISIBLE);
            blinkLabel.setText(R.string.allow_permission);
        } else {
            blinkLabel.setVisibility(View.GONE);
        }

        mSensorManager.registerListener(this, mOrientation, SensorManager.SENSOR_DELAY_NORMAL);
        mSensorManager.registerListener(this,
                mSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER), SensorManager.SENSOR_DELAY_FASTEST);

        if (mWakeLock == null) {
            PowerManager pm = (PowerManager) getSystemService(Context.POWER_SERVICE);
            mWakeLock = pm.newWakeLock(PowerManager.SCREEN_BRIGHT_WAKE_LOCK, CLASS_LABEL);
            mWakeLock.acquire();
        }
        startCamera();
        GeoLocationManager.getInstance(this).startUpdateLocation();
    }

    @Override
    protected void onPause() {
        super.onPause();
        if (drawingView != null) {
            drawingView.setFocusStart(false);
            drawingView.setFocusResult(false);
            drawingView.setAnimationStep(4);
        }
        mSensorManager.unregisterListener(this);
        if (mWakeLock != null) {
            mWakeLock.release();
            mWakeLock = null;
        }

        if (mRecording) stopVideoRecording();

        GeoLocationManager.getInstance(this).stopUpdateLocation();
    }

    @Override
    public void onSensorChanged(SensorEvent event) {
        float azimuth_angle = event.values[0];
        float pitch_angle = event.values[1];
        float roll_angle = event.values[2];
        float angle = 0;

        if (event.sensor.getType() == Sensor.TYPE_ORIENTATION) {
            if (-180 < pitch_angle && pitch_angle < -10 && 45 > roll_angle && roll_angle > -45) {
                angle = -90;
                rotateControll(angle);
                startBlinkingAnimation();
            } else if (45 > pitch_angle && pitch_angle > -45 && -90 < roll_angle && roll_angle < -10) {
                angle = 180;
                rotateControll(angle);
                startBlinkingAnimation();
            } else if (180 > pitch_angle && pitch_angle > 10 && 45 > roll_angle && roll_angle > -45) {
                angle = 90;
                rotateControll(angle);
                startBlinkingAnimation();
            } else if (45 > pitch_angle && pitch_angle > -45 && 90 > roll_angle && roll_angle > 10) {
                angle = 0;
                rotateControll(angle);
                stopBlinkAnimation();
            }
        }
    }

    @Override
    public void onAccuracyChanged(Sensor sensor, int accuracy) {
    }

    public void rotateControll(float angle) {
        // Implementation left empty as in the original code
    }

    public String getTempClipVideoPath() {
        return String.format("%s%s/%s.mp4",
                CommonHelper.sFileRoot, Constants.Paths.tempFolder, CommonHelper.GetTimeString());
    }

    private long getTotalVideoDuration() {
        long totalDuration = 0;
        for (ai_Video video : getVideoClips()) {
            totalDuration += CommonHelper.getVideoDuration(this, video.getFile());
        }
        return totalDuration;
    }

    private List<ai_Video> getVideoClips() {
        return iNoticeID > 0 ? CommonDB.GetNoticeVideos(iNoticeID, iInsItemID) : CommonDB.GetInsItemVideos(iInsItemID);
    }

    private ai_Video getMainVideoClip() {
        return iNoticeID > 0 ? CommonDB.GetNoticeMainVideo(iNoticeID, iInsItemID) : CommonDB.GetInsItemMainVideo(iInsItemID);
    }

    private class DrawingView extends View {
        boolean focusstart;
        boolean focusresult;
        Paint drawingPaint;
        boolean havetouch;
        RectF roundingRect = null;
        float cornerX = (float) 10;
        float cornerY = (float) 10;
        int animationStep;
        long animationDuration = 500;
        long startTime;

        public DrawingView(Context context) {
            super(context);
            focusstart = false;
            havetouch = false;
            focusresult = false;
            animationStep = 0;

            drawingPaint = new Paint();
            drawingPaint.setColor(Color.TRANSPARENT);
            drawingPaint.setStyle(Paint.Style.STROKE);
            drawingPaint.setStrokeWidth(4);
        }

        public void setFocusStart(boolean start) {
            focusstart = start;
        }

        public void setAnimationStep(int step) {
            animationStep = step;
        }

        public void setFocusResult(boolean success) {
            focusresult = success;
        }

        public void setStartTime(long time) {
            startTime = time;
        }

        public void setHaveTouch(boolean touch, Rect eventRect) {
            havetouch = touch;
            roundingRect = new RectF(eventRect);
        }

        @Override
        protected void onDraw(Canvas canvas) {
            if (focusstart) {
                if (animationStep == 1) {
                    drawingPaint.setColor(Color.WHITE);
                    float delta = -5;
                    if (roundingRect != null) {
                        canvas.drawRoundRect(roundingRect, cornerX, cornerY, drawingPaint);
                        roundingRect.inset(delta, delta);
                        if (roundingRect.width() > 300)
                            animationStep++;
                    }
                } else if (animationStep == 2) {
                    float delta = 5;
                    if (roundingRect != null) {
                        canvas.drawRoundRect(roundingRect, cornerX, cornerY, drawingPaint);
                        roundingRect.inset(delta, delta);
                        if (roundingRect.width() < 150)
                            animationStep++;
                    }
                } else if (animationStep == 3) {
                    float delta = -5;
                    if (roundingRect != null) {
                        canvas.drawRoundRect(roundingRect, cornerX, cornerY, drawingPaint);
                        if (roundingRect.width() < 200)
                            roundingRect.inset(delta, delta);
                    }
                }
                invalidate();
            } else {
                if (animationStep == 4) {
                    long elapsedTime = System.currentTimeMillis() - startTime;

                    if (focusresult) {
                        if (roundingRect != null) {
                            drawingPaint.setColor(Color.GREEN);
                            canvas.drawRoundRect(roundingRect, cornerX, cornerY, drawingPaint);
                        }
                    } else {
                        if (roundingRect != null) {
                            drawingPaint.setColor(Color.RED);
                            canvas.drawRoundRect(roundingRect, cornerX, cornerY, drawingPaint);
                        }
                    }

                    if (elapsedTime >= animationDuration) {
                        animationStep++;
                        shutterBtn.setEnabled(true);
                    }

                    invalidate();
                } else {
                    canvas.drawColor(Color.TRANSPARENT);
                }
            }
        }
    }

    private void didFinishMergingVideos() {
        try {
            ai_Video mainVideoClip = getMainVideoClip();
            if (mainVideoClip != null && mainVideoClip.getId() > 0) {
                // Save the video to album
                try {
                    String sSaveAlbum = CommonHelper.GetPreferenceString(this, Constants.Settings.bSaveLocal);
                    if (sSaveAlbum != null && Boolean.parseBoolean(sSaveAlbum)) {
                        sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE,
                                Uri.fromFile(new File(getMainVideoClip().getFile()))));
                    }
                } catch (Exception ex) {
                    ai_BugHandler.ai_Handler_Exception("Exception", "if_video.didFinishMergingVideos", ex, this);
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_video.SaveVideoThumb", ex, this);
        }
    }

    private void saveVideoId(long iVideoID) {
        // Save the video to local database
        if (iNoticeID > 0) {
            Intent intent = new Intent(Constants.Broadcasts.sInsertNoticeVideo);
            intent.putExtra(Constants.Extras.iVideoID, iVideoID);
            LocalBroadcastManager.getInstance(if_video.this).sendBroadcast(intent);
        } else {
            ai_InsItem insItem = db_InsItem.GetInsItem_ByID(iInsItemID);
            if (insItem != null) {
                insItem.sValueOne = String.valueOf(iVideoID);
                insItem.save();
            }
        }
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mReceiver != null)
            unregisterReceiver(mReceiver);

        if (mWakeLock != null) {
            mWakeLock.release();
            mWakeLock = null;
        }

        if (cameraExecutor != null) {
            cameraExecutor.shutdown();
        }
    }

    private void saveVideoRecord(String videoPath) {
        if (StringUtils.isEmpty(videoPath)) return;

        try {
            // Get the video `O_FileName`
            O_FileName oFileName = O_FileName.getVideoFileName();
            File oFile = new File(oFileName.sFilePath);
            File oTempFile = new File(videoPath);

            // Move the video file from temp directory to `CommonHelper.sFileRoot`
            FileUtils.copyFile(oTempFile, oFile, false);
            FileUtils.deleteFile(oTempFile);

            // Create the video thumbnail
            VideoUtils.saveVideoThumbnail(oFileName);

            // Update the GPS tag of the thumbnail
            Location currentLocation = GeoLocationManager.getInstance(this).getLocation();
            FileUtils.updateGpsTag(oFileName.sThumbNail, currentLocation);

            // Save the database record
            long iVideoID = CommonDB.InsertVideo(iInsItemID, iNoticeID,
                    oFileName.sThumbNail, oFileName.sFilePath, currentLocation);

            // Save the first video id to the inspection item (sValueOne) or notice (sVideoID)
            List<ai_Video> videoClips = getVideoClips();
            if (videoClips != null && videoClips.size() == 1) {
                saveVideoId(iVideoID);
            }

        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }
    }

    private boolean validateVideoPermission() {
        String[] permissions = {Manifest.permission.RECORD_AUDIO, Manifest.permission.CAMERA};
        return CommonValidate.checkIfPermissionsGranted(this, permissions);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, @NonNull String[] permissions, @NonNull int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);
        if (requestCode == REQUEST_PERMISSIONS) {
            if (allPermissionsGranted()) {
                startCamera();
            } else {
                Toast.makeText(this, "Permissions not granted by the user.", Toast.LENGTH_SHORT).show();
                finish();
            }
        }
    }

    private boolean allPermissionsGranted() {
        for (String permission : CAMERA_PERMISSIONS) {
            if (ContextCompat.checkSelfPermission(this, permission) != PackageManager.PERMISSION_GRANTED) {
                return false;
            }
        }
        return true;
    }
}
