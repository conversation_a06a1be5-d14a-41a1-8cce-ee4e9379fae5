package com.snapinspect.snapinspect3.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Parcelable;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ListView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.orm.query.Condition;
import com.orm.query.Select;
import com.snapinspect.snapinspect3.Adapter.RequestInspectionAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.Home.if_HomeTab;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Ins_3rd;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Layout_3rd;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.ThrottledSearch;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.v_RequestInspection.State.COMPLETED;

public class frag_RequestIns extends Fragment implements RequestInspectionAdapter.SharedOnClickListener {

    private EditText searchBar;
    private ListView listView;
    private Parcelable listViewState;

    @SuppressLint("ClickableViewAccessibility")
    @Nullable
    @Override
    public View onCreateView(LayoutInflater inflater, @Nullable ViewGroup container, Bundle savedInstanceState) {
        return inflater.inflate(R.layout.frag_request_ins, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        searchBar = view.findViewById(R.id.search_inspection);
        new ThrottledSearch(getActivity(), this::reloadInspectionsByText).bindTo(searchBar);
        listView = view.findViewById(R.id.lv_Inspections);
        listView.setOnItemClickListener((parent, oView, position, id) -> {
            v_RequestInspection ins = (v_RequestInspection) parent.getItemAtPosition(position);
            viewInspection(ins.iInspectionID);
        });
        listView.setAdapter(new RequestInspectionAdapter(getActivity(), this));
    }

    @Override
    public void onResume() {
        super.onResume();
        if (listViewState != null) {
            reloadInspections();
            listView.onRestoreInstanceState(listViewState);
            listViewState = null;
        }
    }

    @Override
    public void onPause() {
        listViewState = listView.onSaveInstanceState();
        super.onPause();
    }

    private void reloadInspectionsByText(String text) {
        List<v_RequestInspection> inspections = CommonDB_Schedule.GetRequestInspectionsWithText(text);
        Collections.sort(inspections, (v_RequestInspection o1, v_RequestInspection o2) -> {
            return o1.getState().compareTo(o2.getState());
        });
        RequestInspectionAdapter adapter = (RequestInspectionAdapter) listView.getAdapter();
        if (adapter == null) return;
        adapter.setDataSource(inspections);
    }

    public void reloadInspections() {
        reloadInspectionsByText(searchBar.getText().toString());
    }

    @Override
    public void startInspection(v_RequestInspection ins) {
        if (!CommonUI.bAppPermission(getActivity())) return;
        ai_Inspection oInspection = CommonDB_Inspection.GetInspectionByScheduleID(ins.iSScheduleID);
        if (oInspection != null && oInspection.getId() > 0) {
            long lInspectionID = oInspection.getId();
            int iInspectionID = (int) lInspectionID;
            Intent oIntent = if_Ins_3rd.newIntent(getActivity(), iInspectionID, oInspection.sType, oInspection.sPTC);
            startActivity(oIntent);
        } else {
            if (CommonValidate.bExternalInspection(ins.sCustom1)) {
                try {
                    String sFileName = CommonHelper.sRequestInspection_FileName(ins.sCustom1);
                    if (sFileName == null) return;
                    ai_InsType oInsType = CommonRequestInspection.LoadInsType(sFileName);
                    if (oInsType == null) return;
                    boolean bPass = true;
                    List<ai_AssetLayout> lsAssetLayout = CommonRequestInspection.LoadAssetLayout(sFileName);
                    if (lsAssetLayout == null || lsAssetLayout.isEmpty()) {
                        List<ai_Layout> lsLayoutItem = CommonRequestInspection.LoadLayout(0, sFileName);
                        for (int i = 0; i < lsLayoutItem.size(); i++) {
                            String sConfig = ins.sType.equals("F") ? lsLayoutItem.get(i).sFConfig : lsLayoutItem.get(i).sSConfig;
                            if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null) {
                                bPass = false;
                                break;
                            }
                        }
                        if (bPass) {
                            IF_CreateInspection_ReqIns oCreateInspection = new IF_CreateInspection_ReqIns();
                            Date oNow = new Date();
                            int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                                    getActivity(), new ArrayList<>(), oInsType,
                                    ins.iSAssetID, "", CommonHelper.sDateToString(oNow), "",
                                    ins.sAddress1, ins.sAddress2,
                                    ins.iSScheduleID, ins.sCustom1, sFileName, ins.getScheduleDate());
                            CommonHelper.ScheduleAppendCustomOne(ins.iSScheduleID, "iInsID", "" + iInspectionID);
                            viewInspection(iInspectionID);
                        } else {
                            startActivity(if_Layout_3rd.newIntent(
                                    getActivity(), ins.iSAssetID, oInsType.iSInsTypeID,
                                    ins.sAddress1, ins.sAddress2, ins.iSScheduleID, ins.sCustom1, ins.getScheduleDate()));
                        }
                    } else {
                        IF_CreateInspection_ReqIns oCreateInspection = new IF_CreateInspection_ReqIns();
                        int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                                getActivity(), new ArrayList<>(lsAssetLayout), oInsType,
                                ins.iSAssetID, "", CommonHelper.sDateToString(null), "",
                                ins.sAddress1, ins.sAddress2,
                                ins.iSScheduleID, ins.sCustom1, sFileName, ins.getScheduleDate());
                        CommonHelper.ScheduleAppendCustomOne(ins.iSScheduleID, "iInsID", "" + iInspectionID);
                        viewInspection(iInspectionID);
                    }
                } catch (Exception e) {
                    ai_BugHandler.ai_Handler_Exception(e);
                }
            } else {
                boolean bPass = true;
                List<ai_InsType> lsInsType = ai_InsType.listAll(ai_InsType.class);
                ai_InsType oInsType = null;
                if (lsInsType != null && !lsInsType.isEmpty()) {
                    for (int i = 0; i < lsInsType.size(); i++) {
                        if (ins.iSInsTypeID == lsInsType.get(i).iSInsTypeID) {
                            oInsType = lsInsType.get(i);
                            break;
                        }
                    }
                }
                if (oInsType == null) return;
                List<ai_AssetLayout> lsAssetLayout = CommonDB.GetAssetLayouts(ins.iSAssetID, ins.sPTC, getActivity());
                if (lsAssetLayout == null || lsAssetLayout.isEmpty()) {
                    List<ai_Layout> lsLayoutItem = Select.from(ai_Layout.class)
                            .where(Condition.prop("S_PTC").eq(ins.sPTC), Condition.prop("I_SP_LAYOUT_ID").eq("" + 0)).list();
                    for (int i = 0; i < lsLayoutItem.size(); i++) {
                        String sConfig = ins.sType.equals("F") ? lsLayoutItem.get(i).sFConfig : lsLayoutItem.get(i).sSConfig;
                        if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null) {
                            bPass = false;
                            break;
                        }
                    }
                    if (bPass) {
                        IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                        Date oNow = new Date();
                        int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                                getActivity(), new ArrayList<>(), oInsType,
                                ins.iSAssetID, "", CommonHelper.sDateToString(oNow),
                                "", ins.sAddress1, ins.sAddress2, ins.iSScheduleID, ins.getScheduleDate());
                        CommonHelper.ScheduleAppendCustomOne(ins.iSScheduleID, "iInsID", "" + iInspectionID);
                        viewInspection(iInspectionID);
                    } else {
                        startActivity(if_Layout_3rd.newIntent(
                                getActivity(), ins.iSAssetID, oInsType.iSInsTypeID,
                                ins.sAddress1, ins.sAddress2, ins.iSScheduleID, "", ins.getScheduleDate()));
                    }
                } else {
                    List<ai_CheckList> lsCheckList = ai_CheckList.find(ai_CheckList.class, "s_PTC = ?", oInsType.sPTC);
                    ai_CheckList oChecklist = null;
                    if (lsCheckList != null && lsCheckList.size() == 1) {
                        oChecklist = lsCheckList.get(0);
                    }
                    boolean bSameVer = true;
                    for (ai_AssetLayout oAL : lsAssetLayout) {
                        if ((oAL.sFieldThree != null) && (!oAL.sFieldThree.equals("" + oChecklist.iLayoutVerID))) {
                            bSameVer = false;
                            break;
                        }
                    }

                    if (bSameVer) {
                        IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                        int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                                getActivity(), new ArrayList<>(lsAssetLayout), oInsType,
                                ins.iSAssetID, "", CommonHelper.sDateToString(null), "",
                                ins.sAddress1, ins.sAddress2, ins.iSScheduleID, ins.getScheduleDate());
                        CommonHelper.ScheduleAppendCustomOne(ins.iSScheduleID, "iInsID", "" + iInspectionID);
                        viewInspection(iInspectionID);

                    } else {
                        startActivity(if_Layout_3rd.newIntent(
                                getActivity(), ins.iSAssetID, oInsType.iSInsTypeID,
                                ins.sAddress1, ins.sAddress2, ins.iSScheduleID, "", ins.getScheduleDate()));
                    }
                }
            }
        }
    }

    private void viewInspection(int iInspectionID) {
        startActivity(if_Ins_3rd.newIntent(getActivity(), iInspectionID));
    }

    @Override
    public void uploadInspection(v_RequestInspection ins) {
        if (ins.getState() == COMPLETED) {
            ((if_HomeTab) getActivity()).UploadIndividualInspection(ins.iInspectionID);
        } else {
            new MaterialDialog.Builder(getActivity())
                    .title("Message")
                    .content(R.string.message_upload_request_ins)
                    .positiveText(R.string.complete_and_upload_ins)
                    .onPositive((dialog, which) -> {
                        ai_Inspection inspection = CommonDB_Inspection.GetInspectionByScheduleID(ins.iSScheduleID);
                        inspection.bComplete = true;
                        inspection.save();
                        if (!CommonValidate.validateInspectionsCompulsoryItems(getActivity(), ins.iInspectionID))
                            return;
                        ((if_HomeTab) getActivity()).UploadIndividualInspection(ins.iInspectionID);
                    })
                    .onNeutral((dialog, which) -> viewInspection(ins.iInspectionID))
                    .neutralText("Edit Inspection")
                    .negativeText(R.string.md_cancel_label)
                    .show();
        }
    }

    @Override
    public void reportInspection(v_RequestInspection ins) {
        ai_Inspection oInspection = CommonDB_Inspection.GetInspectionByID(ins.iInspectionID);
        if (oInspection == null) return;
        GetReportRequest(
                ins.iSInsID, CommonJson.GetJsonKeyValue("iTokenID", oInspection.sCustomTwo),
                CommonJson.GetJsonKeyValue("sToken", oInspection.sCustomTwo), "P");
    }

    @Override
    public void viewNotes(v_RequestInspection ins) {
        String sMessage = CommonJson.GetJsonKeyValue("sAddInfo", ins.sCustom1);
        if (!StringUtils.isEmpty(sMessage)) sMessage = sMessage.trim();
        String placeholder = "No notes for this schedule.";
        CommonHelper.ShowAlert("Notes", !StringUtils.isEmpty(sMessage) ? sMessage : placeholder, getActivity());
    }

    @Override
    public void viewRoute(v_RequestInspection ins) {
        try {
            String address = ins.getAddress();
            if (address == null) return;
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("http://maps.google.com/maps?" + "daddr=" + address));
            startActivity(intent);
        } catch (Exception e) {
            Toast.makeText(getActivity(), "Invalid address. Route failed!", Toast.LENGTH_SHORT).show();
        }
    }

    private void GetReportRequest(final int iInspectionID, String iTokenID, String sToken, String sType) {
        try {
            MaterialDialog oDialog = CommonUI.ShowMaterialProgressDialog(getActivity(), "Running", "Connecting to Server");
            RequestParams oParams = new RequestParams();
            oParams.add("iTokenID", iTokenID);
            oParams.add("sToken", sToken);
            oParams.add("iInsID", "" + iInspectionID);
            if (sType.equalsIgnoreCase("D")) {
                oParams.add("st", sType);
            } else {
                oParams.add("st", "P");
            }
            final String sURL = "/SyncExternal/GetExternalInspectionReport";
            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {

                @Override
                public void onFailure(int statusCode, Throwable e, JSONArray errorResponse) {
                    oDialog.dismiss();
                    new Handler().post(() -> CommonHelper.ShowAlert("Failed", "Please try again.", getActivity()));
                }

                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    oDialog.dismiss();
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            if (response.getBoolean("success")) {
                                new Handler().post(() -> {
                                    try {
                                        CommonDB.InsertLog(getActivity(), "Event", "View Report %d" + iInspectionID);
                                        startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(response.getString("sURL"))));
                                    } catch (Exception ex) {
                                        ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest_RequestInspection.ViewReport", ex);
                                    }
                                });
                                return;
                            } else {
                                new Handler().post(() -> CommonHelper.ShowAlert("Failed", "Please try again.", getActivity()));
                                return;
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest_RequestInspection", ex);
                        }
                    }

                    new Handler().post(() -> CommonHelper.ShowAlert("Error! Failed Connection", "Please try again later.", getActivity()));
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    public void forceSubmitRequestInsData() {
        Context context = getActivity();
        MaterialDialog mProgressDialog = CommonUI.ShowMaterialProgressDialog(
                context, "Uploading... Please keep SnapInspect in foreground until finish.", "");
        Intent oIntent = new Intent(context, SyncService.class);
        oIntent.putExtra("receiver", new ForceUploadDataSyncReceiver(mProgressDialog));
        oIntent.putExtra(Constants.Extras.bForceSubmitData, true);
        oIntent.putExtra(Constants.Extras.bRequestInspection, true);
        context.startService(oIntent);
    }
}
