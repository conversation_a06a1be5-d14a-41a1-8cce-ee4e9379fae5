package com.snapinspect.snapinspect3.activity;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.util.Log;
import android.view.KeyEvent;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.*;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.RequiresApi;
import androidx.fragment.app.Fragment;
import com.afollestad.materialdialogs.DialogAction;
import com.afollestad.materialdialogs.MaterialDialog;
import com.loopj.android.http.JsonHttpResponseHandler;
import com.loopj.android.http.RequestParams;
import com.orm.query.Condition;
import com.orm.query.Select;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Contact;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.IF_Object.ai_JsonStatus;
import com.snapinspect.snapinspect3.IF_Object.v_Inspection;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.Comments.if_InspectionComments;
import com.snapinspect.snapinspect3.activitynew.Home.if_HomeTab;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Ins_3rd;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.views.CircularTextView;
import com.snapinspect.snapinspect3.views.SegmentedControl;
import org.apache.http.NameValuePair;
import org.jetbrains.annotations.NotNull;
import org.json.JSONArray;

import java.util.ArrayList;
import java.util.List;

import static com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Ins_3rd.FRAG_INSPECTION_REQUEST;

/**
 * @Created by TerrySun on 11/03/14.
 */
public class frag_inspections extends Fragment implements SegmentedControl.Listener {

    public final static int INSPECTION_COMPLETED = 1;

    public enum Segment {
        InProgress, Completed, Uploaded
    }

    private View oRootView;
    private ListView oListView;
    private MaterialDialog oDialog;
    private int selectedControlIndex = 0;
    private List<v_Inspection> lsListItem = new ArrayList<v_Inspection>();

    private SegmentedControl sControl;
    private TextView tvNone;
    private EditText etSearch;
    private InspectionsListAdapter insAdapter;
    private RelativeLayout deadView;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        oRootView = inflater.inflate(R.layout.frag_inspections, container, false);
        return oRootView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        CommonDB.copyDBToSDCard(this.getActivity());
        try {
          //  CommonHelper.trackEvent(this.getActivity(), "Android View Inspections", null);
            oListView = oRootView.findViewById(R.id.lv_Inspections);
            tvNone = oRootView.findViewById(R.id.tv_none);
            deadView = oRootView.findViewById(R.id.dead_view);

            //Add new Inspection
            View btnAdd = oRootView.findViewById(R.id.btn_add_new);
            btnAdd.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View view) {
                    ((if_HomeTab) getActivity()).tappedAddBtn();
                }
            });

            sControl = oRootView.findViewById(R.id.segmentedControl);
            sControl.setCornerRadius(18);
            sControl.setDeactiveColor(getResources().getColor(R.color.colorPrimary));
            sControl.setActiveColor(getResources().getColor(R.color.white_color));

            List<String> buttonList = new ArrayList<>();
            buttonList.add(getTitleFromSegment(Segment.InProgress));
            buttonList.add(getTitleFromSegment(Segment.Completed));
            buttonList.add(getTitleFromSegment(Segment.Uploaded));

            sControl.setListener(getActivity(), this);
            sControl.setControlItems(buttonList);

            etSearch = oRootView.findViewById(R.id.search_inspection);
            etSearch.setOnEditorActionListener(new TextView.OnEditorActionListener() {
                @Override
                public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                    if (actionId == EditorInfo.IME_ACTION_SEARCH) {

                        ReloadData();
                        CommonHelper.hideSoftKeyboard(getActivity());

                        return true;
                    }
                    return false;
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_Inspections.main", ex, getActivity());
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == FRAG_INSPECTION_REQUEST && resultCode == INSPECTION_COMPLETED) {
            switchToSegment(Segment.Completed);
        }
    }

    private int getSelectedIndexFromSegment(Segment segment) {
        switch (segment) {
            case InProgress:
                return 0;
            case Completed:
                return 1;
            case Uploaded:
                return 2;
        }
        return -1;
    }

    private String getTitleFromSegment(Segment segment) {
        switch (segment) {
            case InProgress:
                return "In Progress";
            case Completed:
                return "Completed";
            case Uploaded:
                return "Uploaded";
        }
        return "";
    }

    public void ReloadData() {
        try {
            if (lsListItem != null) {
                lsListItem.clear();
            } else {
                lsListItem = new ArrayList<>();
            }
            if (etSearch != null) {
                DisplayInspection(etSearch.getText().toString());
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_inspections.ReloadData", ex, getActivity());
        }
    }

    private void GetReportRequest_ExternalInspection(final int iInspectionID, String sTokenID, String sToken, String sType) {
        try {
            oDialog = CommonUI.ShowMaterialProgressDialog(getActivity(), "Running", "Connecting to Server"); //CommonUI.GetProgressDialog(getActivity(), "Running", "Connecting to Server", true);
            ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
            RequestParams oParams = new RequestParams();
            oParams.add("iTokenID", sTokenID);
            oParams.add("sToken", sToken);
            oParams.add("iInsID", "" + iInspectionID);
            // oParams.add("iInsID", "" + iInspectionID);
            if (sType.equalsIgnoreCase("D")) {
                oParams.add("st", sType);
            } else {
                oParams.add("st", "P");
            }
            final String sURL = "/SyncExternal/GetExternalInspectionReport";
            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    oDialog.dismiss();
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            if (response.getBoolean("success")) {


                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        try {
                                            CommonDB.InsertLog(getActivity(), "Event", "View Report %d" + iInspectionID);
                                            startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(response.getString("sURL"))));
                                        } catch (Exception ex) {
                                            ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest_RequestInspection.ViewReport", ex);
                                        }
                                    }
                                });


                                return;
                            } else {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        ShowAlert("Failed", "Please try again.");
                                    }
                                });

                                return;
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest_RequestInspection", ex);
                        }

                    }
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            ShowAlert("Error! Failed Connection", "Please try again later.");
                        }
                    });
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_Inspections.GetReportRequest_RequestInspection", ex, getActivity());
        }
    }

    private void DownloadReportRequest(final int iInspectionID, final String sURL, String sType) {
        try {
            oDialog = CommonUI.ShowMaterialProgressDialog(getActivity(), "Running", "Connecting to Server");
//            oDialog = CommonUI.GetProgressDialog(getActivity(), "Running", "Connecting to Server", true);

            ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
            RequestParams oParams = new RequestParams();
            oParams.add("iCustomerID", CommonHelper.GetPreferenceString(getActivity(), "iCustomerID"));
            oParams.add("sToken", CommonHelper.GetPreferenceString(getActivity(), "sToken"));
            oParams.add("iInsID", "" + iInspectionID);
            if (sType.equalsIgnoreCase("D")) {
                oParams.add("sType", sType);
            }
            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    oDialog.dismiss();
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            if (response.getBoolean("success")) {

                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        try {
                                            CommonDB.InsertLog(getActivity(), "Event", "View Report %d" + iInspectionID);
                                            startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(response.getString("sURL"))));
                                        } catch (Exception ex) {
                                            ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest.ViewReport", ex);
                                        }
                                    }
                                });


                                return;
                            } else {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        ShowAlert("Failed", "Please try again.");
                                    }
                                });

                                return;
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest", ex);
                        }

                    }
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            ShowAlert("Error! Failed Connection", "Please try again later.");
                        }
                    });
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_Inspections.GetReportRequest", ex, getActivity());
        }
    }

    private void GetReportRequestAction(int iInspectionID, final String sURL, String sType, final String sRecipient) {
        oDialog = CommonUI.ShowMaterialProgressDialog(getActivity(), "Running", "Connecting to Server");
//        oDialog = CommonUI.GetProgressDialog(getActivity(), "Message", "Connecting to Server", true);

        RequestParams oParams = new RequestParams();
        oParams.add("iCustomerID", CommonHelper.GetPreferenceString(getActivity(), "iCustomerID"));
        oParams.add("sToken", CommonHelper.GetPreferenceString(getActivity(), "sToken"));
        oParams.add("iInsID", "" + iInspectionID);
        if (sType.equalsIgnoreCase("D")) {
            oParams.add("sType", sType);
        }
        final int iInsID = iInspectionID;
        IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
            @Override
            public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                oDialog.dismiss();
                if (statusCode == 200) {
                    //Push to next Activity
                    try {
                        if (response.getBoolean("success")) {
                            if (sURL.contains("Email")) {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        try {
                                            boolean bDisplayInst = CommonHelper.GetPreferenceBoolean(getActivity(), "bLocalEmailClient");


                                            if (!bDisplayInst) {
                                                goToServerEmailAcitivty(sRecipient == null ? "" : sRecipient, response.getString("sSubject"), response.getString("sMessageBody"), iInsID);
                                            } else {
                                                Intent emailIntent = new Intent(Intent.ACTION_SENDTO);
                                                if (sRecipient != null && sRecipient.length() > 0) {
                                                    emailIntent.setData(Uri.parse("mailto:" + sRecipient));
                                                }
                                                emailIntent.putExtra(Intent.EXTRA_SUBJECT, response.getString("sSubject"));
                                                emailIntent.putExtra(Intent.EXTRA_TEXT, StringUtils.convertHtml(response.getString("sMessageBody")));

                                                try {
                                                    startActivity(Intent.createChooser(emailIntent, "Send email using..."));
                                                } catch (android.content.ActivityNotFoundException ex) {
                                                    ex.printStackTrace();
                                                }
                                            }
                                        } catch (Exception ex) {
                                            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.GetReportRequest.SendEmail", ex, null);
                                        }
                                    }
                                });


                            } else {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        try {
                                            startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(response.getString("sURL"))));
                                        } catch (Exception ex) {
                                            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.GetReportRequest.ViewReport", ex, null);
                                        }
                                    }
                                });
                            }

                            return;
                        } else {
                            new Handler().post(new Runnable() {
                                @Override
                                public void run() {
                                    ShowAlert("Failed", "Please try again.");
                                }
                            });

                            return;
                        }
                    } catch (Exception ex) {
                        // ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest", ex);
                        ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.GetReportRequest", ex, null);
                    }

                }
                new Handler().post(new Runnable() {
                    @Override
                    public void run() {
                        ShowAlert("Error! Failed Connection", "Please try again later.");
                    }
                });
            }
        });

    }

    private void GetReportRequest(final int iInspectionID, final String sURL, final String sType, int iSAssetID) {
        try {
            List<ai_Contact> lsContact = Select.from(ai_Contact.class).where(Condition.prop("i_S_Asset_ID").eq(iSAssetID)).list();
            if (lsContact.size() == 0) {
                GetReportRequestAction(iInspectionID, sURL, sType, "");
            } else {
                int iCount = 0;
                for (int i = 0; i < lsContact.size(); i++) {
                    if (lsContact.get(i).sEmail != null && lsContact.get(i).sEmail.trim().length() > 0) {
                        iCount++;
                    }
                }

                final String[] lsItems = new String[iCount];
                for (int i = 0; i < lsContact.size(); i++) {
                    if (lsContact.get(i).sEmail != null && lsContact.get(i).sEmail.trim().length() > 0) {
                        lsItems[i] = lsContact.get(i).sEmail + " (" + lsContact.get(i).sTag + ")";
                    }
                }

                final List<String> selectedItems = new ArrayList<String>();

//                AlertDialog.Builder builder =
//                        new AlertDialog.Builder(getActivity(), R.style.DialogTheme);
//
//
//
//                builder.setMultiChoiceItems(lsItems, null, new DialogInterface.OnMultiChoiceClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialog, int which, boolean isChecked) {
//                        if (isChecked) {
//                            String sSelect = lsItems[which].toString();
//                            selectedItems.add(sSelect.substring(0, sSelect.indexOf("(")).trim());
//                        }
//                        else{
//                            String sSelect = lsItems[which].toString();
//                            selectedItems.remove(sSelect.substring(0, sSelect.indexOf("(")).trim());
//                        }
//                    }
//                }).setPositiveButton("Next", new DialogInterface.OnClickListener() {
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//
//                        String sResult = String.join(";", selectedItems);
//                        GetReportRequestAction(iInspectionID, sURL, sType, sResult);
//                    }
//                }).setNegativeButton("Cancel",
//                        new DialogInterface.OnClickListener() {
//
//                            public void onClick(DialogInterface dialog, int which) {
//                                dialog.dismiss();
//                            }
//                        }
//                );
//
//                builder.show();

                new MaterialDialog.Builder(getActivity())
                        .title("Send Report")
                        .content("Please select Recipients to send")
                        .items(lsItems)
                        .itemsCallbackMultiChoice(null, new MaterialDialog.ListCallbackMultiChoice() {
                            @Override
                            public boolean onSelection(MaterialDialog dialog, Integer[] which, CharSequence[] text) {
                                /**
                                 * If you use alwaysCallMultiChoiceCallback(), which is discussed below,
                                 * returning false here won't allow the newly selected check box to actually be selected.
                                 * See the limited multi choice dialog example in the sample project for details.
                                 **/
                                Log.e("osama", "which" + which + " -- " + text);

                                for (int i : which) {
                                    String sSelect = lsItems[i];
                                    selectedItems.add(sSelect.substring(0, sSelect.indexOf("(")).trim());
                                }

                                String sResult = String.join(";", selectedItems);
                                GetReportRequestAction(iInspectionID, sURL, sType, sResult);
                                return true;
                            }
                        })
                        .widgetColor(Color.GRAY)
                        .choiceWidgetColor(ColorStateList.valueOf(Color.GRAY))
                        .positiveText("Next")
                        .onPositive(new MaterialDialog.SingleButtonCallback() {
                            @Override
                            public void onClick(@NonNull MaterialDialog dialog, @NonNull DialogAction which) {
                                Log.e("osama", "Next Clicked" + which);
                            }
                        })
                        .negativeText(R.string.md_cancel_label)
                        .show();
            }


        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_existasset.GetReportRequest", ex, getActivity());
        }
    }

    private void goToServerEmailAcitivty(String title, String subject, String body, int iInsID) {
        Intent intent = new Intent(getActivity(), if_serveremail.class);
        intent.putExtra(Constants.Extras.mailto, title);
        intent.putExtra(Constants.Extras.subject, subject);
//        intent.putExtra(Constants.Extras.body, Html.fromHtml(body));
        intent.putExtra(Constants.Extras.iInsID, iInsID);
        intent.putExtra(Constants.Extras.body, body);

        startActivity(intent);
    }

    private boolean shouldHideDeadView() {
        return CommonHelper.isKioskMode(getActivity())
                || CommonJson.disableNewInspection(getContext())
                || CommonJson.disableEditAsset(getContext());
    }

    private void ShowAlert(String sTitle, String sMessage) {

        CommonUI.ShowAlert(getActivity(), sTitle, sMessage);
//        AlertDialog.Builder builder = CommonUI.GetAlertBuilder(sTitle, sMessage, getActivity(), false, true);
//
//        builder.show();
    }

    @Override
    public void onResume() {
        try {

            super.onResume();

            sControl.setSelectedIndex(selectedControlIndex);

            //CommonHelper.trackEvent(this.getActivity(), "Android View Inspections Resume", null);
            CommonDB.InsertLog(getActivity(), "Event", "Inspection List View");
            //List<ai_Inspection> lsInspections = ai_Inspection.listAll(ai_Inspection.class);
            // lsProgressInspections = ai_Inspection.find(ai_Inspection.class, "B_SYNCED = 0 and B_COMPLETE = 0 and B_DELETED = 0");
            // lsCompletedInspections = ai_Inspection.find(ai_Inspection.class, "B_SYNCED = 0 and B_COMPLETE = 1 and B_DELETED = 0");
            // lsUploadedInspection = ai_Inspection.findWithQuery(ai_Inspection.class, "SELECT * FROM AIINSPECTION WHERE B_SYNCED = 1 and B_DELETED = 0 order by ID desc LIMIT 100");

            DisplayInspection(etSearch.getText().toString());
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_Inspections.onResume", ex, getActivity());
        }

    }

    @SuppressLint("SetTextI18n")
    private void DisplayInspection(String sSearchTerm) {
        lsListItem = new ArrayList<>();

        deadView.setVisibility(View.GONE);
        if (selectedControlIndex == 0) {
            lsListItem = CommonDB_Inspection.SearchInspection(sSearchTerm, false, false);
            //insAdapter.notifyDataSetChanged();
            if (lsListItem.size() > 0) {
                // insAdapter.setInspectionsList(lsListItem);
            } else {
                //TODO set description on dead view if item is empty
                if (shouldHideDeadView()) {
                    deadView.setVisibility(View.GONE);
                } else {
                    deadView.setVisibility(View.VISIBLE);
                }

            }

        } else if (selectedControlIndex == 1) {
            lsListItem = CommonDB_Inspection.SearchInspection(sSearchTerm, true, false);
            //insAdapter.notifyDataSetChanged();
            if (lsListItem.size() > 0) {
                lsListItem.add(new v_Inspection(0, 0, 0, "Upload_button", "", "", "", false, false, "", "", "Upload_button"));

            } else {
                //TODO set description on dead view if item is empty
                if (shouldHideDeadView()) {
                    deadView.setVisibility(View.GONE);
                } else {
                    deadView.setVisibility(View.VISIBLE);
                }
            }
        } else {
            lsListItem = CommonDB_Inspection.SearchInspection(sSearchTerm, true, true);
            //insAdapter.notifyDataSetChanged();
            if (lsListItem.size() > 0) {

            } else {
                //TODO set description on dead view if item is empty
                if (shouldHideDeadView()) {
                    deadView.setVisibility(View.GONE);
                } else {
                    deadView.setVisibility(View.VISIBLE);
                }
            }

        }


        insAdapter = new InspectionsListAdapter(getActivity(),
                R.layout.frag_inspection_textview, lsListItem == null ? (new ArrayList<v_Inspection>()) : lsListItem);

        oListView.setAdapter(insAdapter);
        insAdapter.notifyDataSetChanged();

    }

    @Override
    public void onSegmentedValueChanged(int selectedIndex) {
        this.selectedControlIndex = selectedIndex;
        ReloadData();
    }

    public void switchToSegment(Segment segment) {
        int index = getSelectedIndexFromSegment(segment);
        if (index > -1 && sControl != null) {
            sControl.setSelectedIndex(index);
            onSegmentedValueChanged(sControl.selectedIndex);
        }
    }

    private class InspectionsListAdapter extends ArrayAdapter<v_Inspection> {
        Context oContext;
        List<v_Inspection> lsInspections;
        private final LayoutInflater vi;

        public InspectionsListAdapter(Context context, int textViewResourceId,
                                      List<v_Inspection> objects) {
            super(context, textViewResourceId, objects);
            oContext = context;
            lsInspections = objects;
            vi = (LayoutInflater) oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
        }

        private void setInspectionsList(List<v_Inspection> inspectinos) {
            lsInspections = inspectinos;
            notifyDataSetChanged();
        }

        @RequiresApi(api = Build.VERSION_CODES.JELLY_BEAN)
        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            View v = convertView;
            try {
                final v_Inspection oItem = lsInspections.get(position);
                if (oItem != null) {

                    final v_Inspection ei = oItem;

                    if (oItem.iInspectionID > 0) {

                        v = vi.inflate(R.layout.frag_inspection_textview, null);
                        TextView oInsTitle = v.findViewById(R.id.txt_InsAddress);
                        oInsTitle.setText(StringUtils.isEmpty(ei.sAddress)  ? ei.sTitle : ei.sAddress);
                        TextView oInsInfo = v.findViewById(R.id.txt_InsInfo);

                        String sStartDate = DateUtils.reformatDate(
                                ei.dtStartDate, DateUtils.DATE_TIME_FORMAT, DateUtils.DATE_TIME_READABLE_FORMAT);
                        String sEndDate = DateUtils.reformatDate(
                                ei.dtEndDate, DateUtils.DATE_TIME_FORMAT, DateUtils.DATE_TIME_READABLE_FORMAT);

                        oInsInfo.setText(ei.sInsTitle + " on " +
                                (StringUtils.isEmpty(ei.dtEndDate) ?
                                        (StringUtils.isEmpty(ei.dtStartDate) ? "In Progressing" : sStartDate) : sEndDate));
                        ImageView ivStatus = v.findViewById(R.id.iv_inspect_mark);
                        ImageView ivBtnOption = v.findViewById(R.id.button_image);
                        LinearLayout btnOption = v.findViewById(R.id.btn_option);
                        TextView tvInsRef = v.findViewById(R.id.txt_Asset_Ref);

                        if (StringUtils.isEmpty(oItem.sRef)) {
                            tvInsRef.setVisibility(View.GONE);
                        } else {
                            tvInsRef.setText(oItem.sRef);
                            tvInsRef.setVisibility(View.VISIBLE);
                        }


                        // tvInsRef.setVisibility(View.GONE);

                        TextView tvInsTag = v.findViewById(R.id.txt_InsTag);
                        tvInsTag.setText(ei.sTag);

                        int tagColor;

                        if (ei.isBluildingType()) {
                            tagColor = getResources().getColor(R.color.building_color);
                        } else if (ei.isAssetType()) {
                            tagColor = getResources().getColor(R.color.asset_color);
                        } else if (ei.isUnitype()) {
                            tagColor = getResources().getColor(R.color.unit_color);
                        } else {
                            tagColor = getResources().getColor(R.color.room_color);
                        }

                        GradientDrawable gd = new GradientDrawable();
                        gd.setColor(tagColor);
                        gd.setCornerRadius(CommonHelper.pxFromDp(getActivity(), 8));
                        tvInsTag.setBackground(gd);
                        View.OnClickListener eventEditInspection = view -> {
                            Intent oIntent = if_Ins_3rd.newIntent(getActivity(), ei.iInspectionID);
                            if (CommonHelper.isKioskMode(getActivity())) {
                                startActivityForResult(oIntent, 2);
                            } else {
                                startActivityForResult(oIntent, FRAG_INSPECTION_REQUEST);
                            }
                        };

                        if (!ei.bSynced && !ei.bComplete) {
                            ivStatus.setVisibility(View.GONE);
                            ivBtnOption.setImageDrawable(getResources().getDrawable(R.drawable.ic_more));

                            CircularTextView txtButton = v.findViewById(R.id.btn_uploaded_inspection);
                            txtButton.setVisibility(View.GONE);

                            btnOption.setOnClickListener(eventEditInspection);

                            LinearLayout oLayoutAction = v.findViewById(R.id.layout_Inspection_Action);
                            oLayoutAction.setTag(ei.iInspectionID);
                            oLayoutAction.setOnClickListener(eventEditInspection);
                        } else if (!ei.bSynced && ei.bComplete) {
                            ivStatus.setVisibility(View.GONE);
                            ivBtnOption.setImageDrawable(getResources().getDrawable(R.drawable.icon_upload));

                            CircularTextView txtButton = v.findViewById(R.id.btn_uploaded_inspection);
                            txtButton.setVisibility(View.GONE);

                            btnOption.setOnClickListener(view -> ((if_HomeTab) getActivity()).UploadIndividualInspection(ei.iInspectionID));
                            LinearLayout oLayoutAction = v.findViewById(R.id.layout_Inspection_Action);
                            oLayoutAction.setTag(ei.iInspectionID);
                            oLayoutAction.setOnClickListener(eventEditInspection);
                        } else if (ei.bSynced && ei.bComplete) {
                            if (ei.isBluildingType()) {
                                ivStatus.setImageDrawable(getResources().getDrawable(R.drawable.icon_apartment));
                            } else if (ei.isAssetType()) {
                                ivStatus.setImageDrawable(getResources().getDrawable(R.drawable.icon_house));
                            } else if (ei.isUnitype()) {
                                ivStatus.setImageDrawable(getResources().getDrawable(R.drawable.icon_single_room));
                            } else {
                                ivStatus.setImageDrawable(getResources().getDrawable(R.drawable.icon_roms));
                            }

                            ivBtnOption.setImageDrawable(getResources().getDrawable(R.drawable.ic_more));

                            CircularTextView txtButton = v.findViewById(R.id.btn_uploaded_inspection);

                            Button oButton = v.findViewById(R.id.btn_Inspection_Action);
                            oButton.setVisibility(View.GONE);

                            String sStatus = CommonJson.GetJsonKeyValue("Status", ei.sCustom1);
                            if (sStatus != null && sStatus.trim().length() > 0) {
                                txtButton.setVisibility(View.VISIBLE);
                                String sStatusCode = CommonJson.GetJsonKeyValue("S_Code", ei.sCustom1);
                                String sColor = (sStatusCode == null || sStatusCode.trim().length() == 0) ? "#CCCCCC" : sStatusCode;
                                txtButton.setText(CommonHelper.GetStatusCode(sStatus));
                                txtButton.setSolidColor(sColor.trim());
                                txtButton.setTextColor(Color.WHITE);
                            }

                            btnOption.setTag(ei.iSInsID);
                            View.OnClickListener eventClick = view -> {
                                final int iInsID = CommonHelper.getInt(view.getTag().toString());
                                ArrayList<String> lsOptions = new ArrayList<>();
                                if (CommonHelper.isKioskMode(oContext)) {
                                    lsOptions.add("Download PDF");
                                } else {
                                    lsOptions.add("Download PDF");
                                    lsOptions.add("Send PDF");
                                    if (CommonUI.bEnableWord(oContext)) {
                                        lsOptions.add("Send Word");
                                    }

                                    if (!CommonJson.disableEditInspection(oContext)) {
                                        lsOptions.add("Edit Inspection");
                                        lsOptions.add("Update Status");
                                    }

                                    lsOptions.add("Comments");
                                }

                                new MaterialDialog.Builder(oContext)
                                        .title(R.string.alert_title_action)
                                        .items(lsOptions)
                                        .itemsCallback((dialog, itemView, position1, text) -> {
                                            if (text.toString().equalsIgnoreCase("Download PDF")) {
                                                List<ai_Inspection> lsTemp = ai_Inspection.find(ai_Inspection.class, "I_S_INS_ID=?", "" + iInsID);
                                                ai_Inspection oTemp = new ai_Inspection();
                                                if (lsTemp != null || !lsTemp.isEmpty()) {
                                                    oTemp = lsTemp.get(0);
                                                }
                                                if (CommonHelper.bRequestInspection(oTemp.sCustomTwo)) {
                                                    GetReportRequest_RequestInspection(iInsID,
                                                            CommonJson.GetJsonKeyValue("RID", oTemp.sCustomTwo),
                                                            CommonJson.GetJsonKeyValue("RCode", oTemp.sCustomTwo),
                                                            "P");
                                                }
                                                if (CommonValidate.bExternalInspection(oTemp.sCustomTwo)) {
                                                    GetReportRequest_ExternalInspection(iInsID,
                                                            CommonValidate.sExternalTokenID(oTemp.sCustomTwo),
                                                            CommonValidate.sExternalToken(oTemp.sCustomTwo),
                                                            "P");
                                                } else {
                                                    DownloadReportRequest(iInsID, "/IOAPI/GetReport", "");
                                                }

                                            } else if (text.toString().equalsIgnoreCase("Send Word")) {
                                                GetReportRequest(iInsID, "/IOAPI/GetReportEmail", "D", ei.iSAssetID);
                                            } else if (text.toString().equalsIgnoreCase("Send PDF")) {
                                                GetReportRequest(iInsID, "/IOAPI/GetReportEmail", "", ei.iSAssetID);
                                            } else if (text.toString().equalsIgnoreCase("Edit Inspection")) {
                                                CommonInspection.editInspection(getContext(), ei.iSInsID);
                                            } else if (text.toString().equalsIgnoreCase("Comments")) {
                                                goToComments(iInsID, ei.iSInsID);
                                            } else if (text.toString().equalsIgnoreCase("Update Status")) {
                                                CommonInspection.updateInspectionStatus(
                                                    getContext(), ei.iSInsID, frag_inspections.this::ReloadData);
                                            }
                                        })
                                        .negativeText(R.string.md_cancel_label)
                                        .show();
                            };

                            btnOption.setOnClickListener(eventClick);

                            LinearLayout oLayoutAction = v.findViewById(R.id.layout_Inspection_Action);
                            oLayoutAction.setTag(ei.iSInsID);

                            oLayoutAction.setOnClickListener(eventClick);
                        }


                    } else if (ei.sTitle.equals("Upload_button")) {
                        v = vi.inflate(R.layout.frag_inspection_upload_button, null);
                        v.findViewById(R.id.upload_button).setOnClickListener(view -> {
                            if (getActivity() instanceof if_HomeTab) {
                                ((if_HomeTab) getActivity()).uploadInspections();
                            }
                        });
                        return v;
                    } else {
                        v = vi.inflate(R.layout.frag_inspection_textview_first, null);
                        TextView oInsTitle = v.findViewById(R.id.txt_InstructionTitle);
                        oInsTitle.setText(ei.sTitle);

                    }
                }

            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "frag_inspections.getView", ex, getActivity());
            }
            return v;
            /*f
            row = inflater.inflate(R.layout.frag_inspection_textview, parent, false);
            TextView oInsTitle = (TextView)row.findViewById(R.id.txt_InsAddress);
            oInsTitle.setText(oIns.sTitle);
            TextView oInsInfo = (TextView)row.findViewById(R.id.txt_InsInfo);
            oInsInfo.setText(oIns.sInsTitle + " @ " + (oIns.dtEndDate == null || oIns.dtEndDate.equals("") ? oIns.dtStartDate : oIns.dtEndDate));
            return (row);*/
        }
    }


    private void goToComments(final int iInspectionID, final int iSInsID) {
        Intent intent = new Intent(getActivity(), if_InspectionComments.class);
        intent.putExtra("iSInsID", iSInsID);
        getActivity().startActivity(intent);
    }

    private void GetReportRequest_RequestInspection(final int iInspectionID, String sRequestID, String sRequestCode, String sType) {
        try {
            oDialog = CommonUI.ShowMaterialProgressDialog(getActivity(), "Running", "Connecting to Server");
//            oDialog = CommonUI.GetProgressDialog(getActivity(), "Running", "Connecting to Server", true);

            ArrayList<NameValuePair> lsParams = new ArrayList<NameValuePair>();
            RequestParams oParams = new RequestParams();
            oParams.add("iRequestID", sRequestID);
            oParams.add("sRequestCode", sRequestCode);
            // oParams.add("iInsID", "" + iInspectionID);
            if (sType.equalsIgnoreCase("D")) {
                oParams.add("st", sType);
            } else {
                oParams.add("st", "P");
            }
            final String sURL = "/External/GetRequestInspectionReport";
            IF_RestClient.post(sURL, oParams, new JsonHttpResponseHandler() {
                @Override
                public void onSuccess(int statusCode, org.apache.http.Header[] headers, final org.json.JSONObject response) {
                    oDialog.dismiss();
                    if (statusCode == 200) {
                        //Push to next Activity
                        try {
                            if (response.getBoolean("success")) {


                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        try {
                                            CommonDB.InsertLog(getActivity(), "Event", "View Report %d" + iInspectionID);
                                            startActivity(new Intent(Intent.ACTION_VIEW, Uri.parse(response.getString("sURL"))));
                                        } catch (Exception ex) {
                                            ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest_RequestInspection.ViewReport", ex);
                                        }
                                    }
                                });


                                return;
                            } else {
                                new Handler().post(new Runnable() {
                                    @Override
                                    public void run() {
                                        ShowAlert("Failed", "Please try again.");
                                    }
                                });

                                return;
                            }
                        } catch (Exception ex) {
                            ai_BugHandler.ai_Handler_Exception("Per", "frag_inspections.GetReportRequest_RequestInspection", ex);
                        }

                    }
                    new Handler().post(new Runnable() {
                        @Override
                        public void run() {
                            ShowAlert("Error! Failed Connection", "Please try again later.");
                        }
                    });
                }
            });
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_Inspections.GetReportRequest_RequestInspection", ex, getActivity());
        }
    }
}