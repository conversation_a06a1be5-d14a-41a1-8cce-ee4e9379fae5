package com.snapinspect.snapinspect3.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Color;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.app.ActivityCompat;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ImageButton;
import android.widget.LinearLayout;
import android.widget.ListView;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.TextView;
import android.widget.Toast;

import androidx.fragment.app.Fragment;
import com.afollestad.materialdialogs.MaterialDialog;
import com.google.android.gms.maps.*;
import com.google.android.gms.maps.model.BitmapDescriptorFactory;
import com.google.android.gms.maps.model.CameraPosition;
import com.google.android.gms.maps.model.LatLng;
import com.google.android.gms.maps.model.LatLngBounds;
import com.google.android.gms.maps.model.Marker;
import com.google.android.gms.maps.model.MarkerOptions;
import com.orm.query.Condition;
import com.orm.query.Select;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonDB_Inspection;
import com.snapinspect.snapinspect3.Helper.CommonDB_Schedule;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonRequestInspection;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.CommonValidate;
import com.snapinspect.snapinspect3.Helper.IF_CreateInspection;
import com.snapinspect.snapinspect3.Helper.IF_CreateInspection_ReqIns;
import com.snapinspect.snapinspect3.Helper.NetworkUtils;
import com.snapinspect.snapinspect3.IF_Object.ai_AssetLayout;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_CheckList;
import com.snapinspect.snapinspect3.IF_Object.ai_InsType;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.IF_Object.ai_Layout;
import com.snapinspect.snapinspect3.IF_Object.v_Schedule;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Ins_3rd;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Layout_3rd;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import info.hoang8f.android.segmented.SegmentedGroup;

import static android.Manifest.permission.ACCESS_COARSE_LOCATION;
import static android.Manifest.permission.ACCESS_FINE_LOCATION;
import static android.content.pm.PackageManager.PERMISSION_GRANTED;

/**
 * Created by TerrySun on 11/03/14.
 */
public class frag_schedules extends Fragment implements
        RadioGroup.OnCheckedChangeListener, OnMapReadyCallback, GoogleMap.OnMarkerClickListener, GoogleMap.OnMapClickListener {
   // List<ai_Schedule> lsSchedules;
    List<v_Schedule> lsSchedules;
    ScheduleListAdapter adapter;
    ListView oListView;
    SegmentedGroup segmented;
    MapView mapView;
    private GoogleMap mMap;
    private static final String TAG = "FRAG_SCHEDULES";
    List<Marker> markersList = new ArrayList<Marker>();
    LatLngBounds.Builder builder;
    CameraUpdate cu;
    Double curLatitude, curLongitude;
    Boolean isShowing = false;
    final int MARKER_UPDATE_INTERVAL = 2000; /* milliseconds */
    Handler handler = new Handler();
    public static boolean showedPins;
   // ProgressDialog oDialog = null;
    RadioButton listBtn;

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View oRootView;
        if (Build.VERSION.SDK_INT >= 20) {
            oRootView = inflater.inflate(R.layout.frag_schedule, container, false);
        } else {
            oRootView = inflater.inflate(R.layout.frag_schedule_low, container, false);
        }
        return oRootView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // CommonHelper.trackEvent(this.getActivity(), "Android View Schedules", null);

        try {
            lsSchedules = CommonDB_Schedule.SearchSchedules("");

            // lsSchedules = ai_Schedule.listAll(ai_Schedule.class);
            adapter = new ScheduleListAdapter(getActivity(),
                    R.layout.frag_schedule_textview, lsSchedules == null ? (new ArrayList<v_Schedule>()) : lsSchedules);


            oListView = view.findViewById(R.id.lv_Schedules);

            oListView.setAdapter(adapter);

            oListView.setOnItemClickListener(new AdapterView.OnItemClickListener() {

                @Override
                public void onItemClick(AdapterView<?> parent, final View view,
                                        int position, long id) {

                    v_Schedule oSchedule = (v_Schedule) parent.getItemAtPosition(position);
                    ViewSchedule(oSchedule);

                }
            });

            if (Build.VERSION.SDK_INT >= 20) {
                segmented = view.findViewById(R.id.segmented2);
                segmented.setOnCheckedChangeListener(this);

                listBtn = segmented.findViewById(R.id.listBtn);
                listBtn.setChecked(true);

                mapView = view.findViewById(R.id.map);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.onCreateView", ex, getActivity());
        }
    }
    public void setMyCurrentLocation(boolean flag) {
        Context context = getActivity();
        if (context != null
                && ActivityCompat.checkSelfPermission(context, ACCESS_FINE_LOCATION) != PERMISSION_GRANTED
                && ActivityCompat.checkSelfPermission(context, ACCESS_COARSE_LOCATION) != PERMISSION_GRANTED) {
            mMap.setMyLocationEnabled(flag);
        }
    }

    @Override
    public void onMapReady(GoogleMap googleMap) {
        try{

           // if (!isNetworkConnected) return;

            googleMap.setMapType(GoogleMap.MAP_TYPE_NORMAL);
            googleMap.setTrafficEnabled(true);

            googleMap.getUiSettings().setZoomControlsEnabled(true);

            mMap = googleMap;

            markersList.clear();

            displayLocationsById(0); //started as 0

            /**initialize the padding for map boundary*/
            int padding = 50;
            /**create the bounds from latlngBuilder to set into map camera*/
            LatLngBounds bounds = builder.build();
            /**create the camera with bounds and padding to set into map*/
            cu = CameraUpdateFactory.newLatLngBounds(bounds, padding);
            /**call the map call back to know map is loaded or not*/
            mMap.setOnMapLoadedCallback(new GoogleMap.OnMapLoadedCallback() {
                @Override
                public void onMapLoaded() {
                    /**set animated zoom camera into map*/
                    mMap.animateCamera(cu);
                    showedPins = true;
                    //if (oDialog != null)
                   //     oDialog.dismiss();
                }
            });

            mMap.setOnMyLocationChangeListener(myLocationChangeListener);
            setMyCurrentLocation(true);

        }catch(SecurityException e){
            showedPins = true;
            //if (oDialog != null)
            //    oDialog.dismiss();

            e.printStackTrace();
        }catch(Exception e){
            showedPins = true;
           // if (oDialog != null)
           //     oDialog.dismiss();

            e.printStackTrace();
        }


        mMap.setOnMapClickListener(this);
        mMap.setOnMarkerClickListener(this);
        mMap.setOnInfoWindowClickListener(new GoogleMap.OnInfoWindowClickListener() {
            @Override
            public void onInfoWindowClick(Marker marker) {
                try {
                    Intent intent = new Intent(android.content.Intent.ACTION_VIEW,
                            Uri.parse("http://maps.google.com/maps?" + "daddr=" + marker.getPosition().latitude + "," + marker.getPosition().longitude));
                    startActivity(intent);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }

    private void displayLocationsById(final int index) {
        if (index < lsSchedules.size()) {
            final v_Schedule oSchedule = lsSchedules.get(index);
            getActivity().runOnUiThread(new Runnable() {
                public void run() {
                    showLocationFromAddress(getActivity(), oSchedule.sAddress1 + "," + oSchedule.sAddress2, oSchedule, index);
                }
            });
        }

    }
    public void showLocationFromAddress(Context context,String strAddress, v_Schedule oSchedule, int currentIndex) {

        Geocoder coder = new Geocoder(context);
        List<Address> address;
        LatLng p1 = null;

        try {

            address = coder.getFromLocationName(strAddress, 5);
            if (address == null || address.size() == 0) {
                displayLocationsById(currentIndex+1);
                return;
            }
            Address location = address.get(0);
            location.getLatitude();
            location.getLongitude();

            Log.e(TAG, "latitude" + location.getLatitude());
            Log.e(TAG, "longitude" + location.getLongitude());

            p1 = new LatLng(location.getLatitude(), location.getLongitude() );
            CameraPosition position = CameraPosition.builder()
                    .target(p1)
                    .zoom(1.5f)
                    .bearing(0.0f)
                    .tilt(0.0f)
                    .build();

            mMap.animateCamera(CameraUpdateFactory
                   .newCameraPosition(position), null);

            SimpleDateFormat curFormater = new SimpleDateFormat("MMM dd, yyyy HH:mm");
            Date dateObj = curFormater.parse(oSchedule.sDateTime);

            MarkerOptions mapOptions;
            if (System.currentTimeMillis() + 24*60*60*1000l < dateObj.getTime()) {
                mapOptions = new MarkerOptions().position(p1).title(strAddress).snippet(oSchedule.sDateTime).icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_MAGENTA));
            } else if (System.currentTimeMillis() + 24*60*60*1000l > dateObj.getTime() && System.currentTimeMillis() < dateObj.getTime()) {
                mapOptions = new MarkerOptions().position(p1).title(strAddress).snippet(oSchedule.sDateTime).icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_GREEN));
            } else {
                mapOptions = new MarkerOptions().position(p1).title(strAddress).snippet(oSchedule.sDateTime).icon(BitmapDescriptorFactory.defaultMarker(BitmapDescriptorFactory.HUE_RED));
            }

            Marker  marker = mMap.addMarker(mapOptions);
            marker.showInfoWindow();

            mMap.setOnMarkerClickListener(this);

            markersList.add(marker);

            builder.include(marker.getPosition());
        } catch (Exception ex) {

            ex.printStackTrace();
        }

        displayLocationsById(currentIndex+1);
    }

    private final GoogleMap.OnMyLocationChangeListener myLocationChangeListener = new GoogleMap.OnMyLocationChangeListener() {
        @Override
        public void onMyLocationChange(Location location) {
            curLatitude = location.getLatitude();
            curLongitude = location.getLongitude();

        }
    };

    private void RouteSchedule(v_Schedule oSchedule) {
        Geocoder coder = new Geocoder(getActivity());
        List<Address> address;
        LatLng p1 = null;

        try {
            String strAddress = oSchedule.sAddress1 + "," + oSchedule.sAddress2;
            address = coder.getFromLocationName(strAddress, 5);
            if (address == null) {
                return;
            }
            Address location = address.get(0);
            location.getLatitude();
            location.getLongitude();

            Log.e(TAG, "latitude" + location.getLatitude());
            Log.e(TAG, "longitude" + location.getLongitude());

            Intent intent = new Intent(android.content.Intent.ACTION_VIEW,
                    Uri.parse("http://maps.google.com/maps?" + "daddr=" + location.getLatitude() + "," + location.getLongitude()));
            startActivity(intent);

        }catch (Exception e) {
            //e.printStackTrace();
            Toast.makeText(getActivity(), "Invalid address. Route failed!", Toast.LENGTH_SHORT).show();
        }
    }
    private void ViewSchedule(final v_Schedule oSchedule) {

        String sText = "View Inspection";
        if (CommonJson.GetJsonKeyValue("iInsID", oSchedule.sCustom1) == null ) {
            sText = "Start Inspection";
        }
        else if (CommonJson.GetJsonKeyValue("iInsID", oSchedule.sCustom1) != null && CommonJson.GetJsonKeyValue("iInsIDCom", oSchedule.sCustom1) != null){
            sText = "Edit Completed Inspection";
        }
        else{
            sText = "Edit Inspection";
        }
//        final AlertDialog.Builder builder = CommonUI.GetAlertBuilder("", "", getActivity(), true, false);
        // options = null;


      //  if (Build.VERSION.SDK_INT >= 20) {
        if (CommonJson.GetJsonKeyValue("REmail", oSchedule.sCustom1) != null &&
                CommonJson.GetJsonKeyValue("REmail", oSchedule.sCustom1).length() > 0){

            if (CommonHelper.GetPreferenceString(getActivity(), "sEmail") != null &&
                    (CommonHelper.GetPreferenceString(getActivity(), "sEmail").equalsIgnoreCase(CommonJson.GetJsonKeyValue("REmail", oSchedule.sCustom1)))){
                String[]   options = new String[]{"Route To Destination", "View Notes" };
                // }else {
                //     options = new String[]{"Edit Completed Inspection"};
                /// }
//                builder.setTitle("Action");
//                builder.setItems(options, new DialogInterface.OnClickListener() {
//
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//
//                        dialog.dismiss();
//                        if (which == 0) {
//                            RouteSchedule(oSchedule);
//
//                        } else if (which == 1) {
//                            String sMessage = CommonJson.GetJsonKeyValue("sAddInfo", oSchedule.sCustom1);
//                            if (sMessage == null || sMessage.trim().length() == 0){
//                                sMessage = "No notes.";
//                            }
//
//                            ShowAlert("Notes", sMessage);
//                        }
//                       // else if (which == 2){
//                       //     StartSchedule(oSchedule);
//
//                        //}
//                    }
//
//                });
                new MaterialDialog.Builder(getActivity())
                        .title(R.string.alert_title_action)
                        .items(options)
                        .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                            @Override
                            public boolean onSelection(MaterialDialog dialog, View view, int which, CharSequence text) {
                                if (which == 0) {
                                    RouteSchedule(oSchedule);

                                } else if (which == 1) {
                                    String sMessage = CommonJson.GetJsonKeyValue("sAddInfo", oSchedule.sCustom1);
                                    if (sMessage == null || sMessage.trim().length() == 0){
                                        sMessage = "No notes.";
                                    }

                                    ShowAlert("Notes", sMessage);
                                }
                                return true;
                            }
                        })
                        .negativeText(R.string.md_cancel_label)
                        .show();
            }
            else{
                String[]   options = new String[]{"Route To Destination", "View Notes", sText };

//                builder.setTitle("Action");
//                builder.setItems(options, new DialogInterface.OnClickListener() {
//
//                    @Override
//                    public void onClick(DialogInterface dialog, int which) {
//
//                        dialog.dismiss();
//                        if (which == 0) {
//                            RouteSchedule(oSchedule);
//
//                        } else if (which == 1) {
//                            String sMessage = CommonJson.GetJsonKeyValue("sAddInfo", oSchedule.sCustom1);
//                            if (sMessage == null || sMessage.trim().length() == 0){
//                                sMessage = "No notes for this schedule.";
//                            }
//
//                            ShowAlert("Notes", sMessage);
//                        }
//                        else if (which == 2){
//                            StartSchedule(oSchedule);
//
//                        }
//                    }
//
//                });
                new MaterialDialog.Builder(getActivity())
                        .title(R.string.alert_title_action)
                        .items(options)
                        .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                            @Override
                            public boolean onSelection(MaterialDialog dialog, View view, int which, CharSequence text) {
                                if (which == 0) {
                                    RouteSchedule(oSchedule);

                                } else if (which == 1) {
                                    String sMessage = CommonJson.GetJsonKeyValue("sAddInfo", oSchedule.sCustom1);
                                    if (sMessage == null || sMessage.trim().length() == 0){
                                        sMessage = "No notes for this schedule.";
                                    }

                                    ShowAlert("Notes", sMessage);
                                }
                                else if (which == 2){
                                    StartSchedule(oSchedule);

                                }
                                return true;
                            }
                        })
                        .negativeText(R.string.md_cancel_label)
                        .show();
            }
        }
        else{
            String[]   options = new String[]{"Route To Destination", "View Notes", sText };

//            builder.setTitle("Action");
//            builder.setItems(options, new DialogInterface.OnClickListener() {
//
//                @Override
//                public void onClick(DialogInterface dialog, int which) {
//
//                    dialog.dismiss();
//                    if (which == 0) {
//                        RouteSchedule(oSchedule);
//
//                    } else if (which == 1) {
//                        String sMessage = CommonJson.GetJsonKeyValue("sAddInfo", oSchedule.sCustom1);
//                        if (sMessage == null || sMessage.trim().length() == 0){
//                            sMessage = "No notes for this schedule.";
//                        }
//
//                        ShowAlert("Notes", sMessage);
//                    }
//                    else if (which == 2){
//                        StartSchedule(oSchedule);
//
//                    }
//                }
//
//            });
            new MaterialDialog.Builder(getActivity())
                    .title(R.string.alert_title_action)
                    .items(options)
                    .itemsCallbackSingleChoice(-1, new MaterialDialog.ListCallbackSingleChoice() {
                        @Override
                        public boolean onSelection(MaterialDialog dialog, View view, int which, CharSequence text) {
                            if (which == 0) {
                            RouteSchedule(oSchedule);

                            } else if (which == 1) {
                                String sMessage = CommonJson.GetJsonKeyValue("sAddInfo", oSchedule.sCustom1);
                                if (sMessage == null || sMessage.trim().length() == 0){
                                    sMessage = "No notes for this schedule.";
                                }

                                ShowAlert("Notes", sMessage);
                            }
                            else if (which == 2){
                                StartSchedule(oSchedule);

                            }
                            return true;
                        }
                    })
                    .negativeText(R.string.md_cancel_label)
                    .show();
        }


//        builder.show();

    }

    private void StartSchedule(v_Schedule oSchedule){

        ai_Inspection oInspection = CommonDB_Inspection.GetInspectionByScheduleID(oSchedule.iSScheduleID);
        if (oInspection != null && oInspection.getId() > 0) {
            long lInspectionID = oInspection.getId();
            int iInspectionID = (int) lInspectionID;
            if (CommonHelper.isKioskMode(getActivity())) {
                Intent oIntent = if_Ins_3rd.newIntent(getActivity(), iInspectionID, oInspection.sType, oInspection.sPTC);
                if (CommonHelper.isKioskMode(getActivity())) {
                    startActivityForResult(oIntent, 2);
                } else {
                    startActivity(oIntent);
                }
            } else {
                Intent oIntent = if_Ins_3rd.newIntent(getActivity(), iInspectionID, oInspection.sType, oInspection.sPTC);
                if (CommonHelper.isKioskMode(getActivity())) {
                    startActivityForResult(oIntent, 2);
                } else {
                    startActivity(oIntent);
                }
            }
        } else {
            if (CommonValidate.bExternalInspection(oSchedule.sCustom1)) {
                try {
                    String sFileName = CommonHelper.sRequestInspection_FileName(oSchedule.sCustom1);
                    if (sFileName != null) {

                        boolean bPass = true;
                        ai_InsType oInsType = CommonRequestInspection.LoadInsType(sFileName);

                        if (oInsType == null) {
                            return;
                        }
                        List<ai_AssetLayout> lsAssetLayout = CommonRequestInspection.LoadAssetLayout(sFileName);
                        if (lsAssetLayout == null || lsAssetLayout.size() == 0) {
                            List<ai_Layout> lsLayoutItem = CommonRequestInspection.LoadLayout(0, sFileName);

                            for (int i = 0; i < lsLayoutItem.size(); i++) {
                                String sConfig = oSchedule.sType.equals("F") ? lsLayoutItem.get(i).sFConfig : lsLayoutItem.get(i).sSConfig;

                                if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null) {
                                    bPass = false;
                                    break;
                                }
                            }
                            if (bPass) {
                                IF_CreateInspection_ReqIns oCreateInspection = new IF_CreateInspection_ReqIns();
                                Date oNow = new Date();
                                int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(getActivity(), new ArrayList<>(), oInsType,
                                        oSchedule.iSAssetID, "", CommonHelper.sDateToString(oNow), "", oSchedule.sAddress1,
                                        oSchedule.sAddress2, oSchedule.iSScheduleID, oSchedule.sCustom1, sFileName, oSchedule.getScheduleDate());
                                CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                                GoToInspection(iInspectionID);

                                //   CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                            } else {
                                startActivity(if_Layout_3rd.newIntent(
                                    getActivity(), oSchedule.iSAssetID, oInsType.iSInsTypeID, oSchedule.sAddress1,
                                    oSchedule.sAddress2, oSchedule.iSScheduleID, oSchedule.sCustom1, oSchedule.getScheduleDate()));
                            }
                        } else {
                            IF_CreateInspection_ReqIns oCreateInspection = new IF_CreateInspection_ReqIns();
                            int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(getActivity(), new ArrayList<>(lsAssetLayout), oInsType,
                                    oSchedule.iSAssetID, "", CommonHelper.sDateToString(null), "", oSchedule.sAddress1,
                                    oSchedule.sAddress2,
                                    oSchedule.iSScheduleID, oSchedule.sCustom1, sFileName, oSchedule.getScheduleDate());
                            CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                            GoToInspection(iInspectionID);

                        }

                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return;
            } else {
                boolean bPass = true;
                List<ai_InsType> lsInsType = ai_InsType.listAll(ai_InsType.class);
                ai_InsType oInsType = null;
                if (lsInsType != null && lsInsType.size() > 0) {
                    for (int i = 0; i < lsInsType.size(); i++) {
                        if (oSchedule.iSInsTypeID == lsInsType.get(i).iSInsTypeID) {
                            oInsType = lsInsType.get(i);
                            break;
                        }
                    }

                }
                if (oInsType == null) {
                    return;
                }
                List<ai_AssetLayout> lsAssetLayout = CommonDB.GetAssetLayouts(oSchedule.iSAssetID, oSchedule.sPTC, getActivity());
                if (lsAssetLayout == null || lsAssetLayout.size() == 0) {
                    List<ai_Layout> lsLayoutItem = Select.from(ai_Layout.class).where(Condition.prop("S_PTC").eq(oSchedule.sPTC), Condition.prop("I_SP_LAYOUT_ID").eq("" + 0)).list();

                    for (int i = 0; i < lsLayoutItem.size(); i++) {
                        String sConfig = oSchedule.sType.equals("F") ? lsLayoutItem.get(i).sFConfig : lsLayoutItem.get(i).sSConfig;

                        if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null) {
                            bPass = false;
                            break;
                        }
                    }
                    if (bPass) {
                        IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                        Date oNow = new Date();
                        int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(getActivity(), new ArrayList<>(), oInsType,
                                oSchedule.iSAssetID, "", CommonHelper.sDateToString(oNow), "", oSchedule.sAddress1,
                                oSchedule.sAddress2, oSchedule.iSScheduleID, oSchedule.getScheduleDate());
                        CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                        GoToInspection(iInspectionID);

                        // CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                    } else {
                        startActivity(if_Layout_3rd.newIntent(
                                getActivity(), oSchedule.iSAssetID, oInsType.iSInsTypeID, oSchedule.sAddress1,
                                oSchedule.sAddress2, oSchedule.iSScheduleID, "", oSchedule.getScheduleDate()));
                    }
                } else {
                    List<ai_CheckList> lsCheckList = ai_CheckList.find(ai_CheckList.class, "s_PTC = ?", oInsType.sPTC);
                    ai_CheckList oChecklist = null;
                    if (lsCheckList != null && lsCheckList.size() == 1) {
                        oChecklist = lsCheckList.get(0);
                    }
                    boolean bSameVer = true;
                    for (ai_AssetLayout oAL : lsAssetLayout) {
                        if ((oAL.sFieldThree != null) && (!oAL.sFieldThree.equals("" + oChecklist.iLayoutVerID))) {
                            bSameVer = false;
                            break;
                        }
                    }

                    if (bSameVer) {
                        IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                        int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(getActivity(), new ArrayList<>(lsAssetLayout), oInsType,
                                oSchedule.iSAssetID, "", CommonHelper.sDateToString(null), "", oSchedule.sAddress1,
                                oSchedule.sAddress2, oSchedule.iSScheduleID, oSchedule.getScheduleDate());
                        CommonHelper.ScheduleAppendCustomOne(oSchedule.iSScheduleID, "iInsID", "" + iInspectionID);
                        GoToInspection(iInspectionID);

                    } else {
                        for (ai_AssetLayout oAL : lsAssetLayout) {
                            oAL.delete();
                        }
                        startActivity(if_Layout_3rd.newIntent(
                                getActivity(), oSchedule.iSAssetID, oInsType.iSInsTypeID, oSchedule.sAddress1,
                                oSchedule.sAddress2, oSchedule.iSScheduleID, "", oSchedule.getScheduleDate()));
                    }
                }
            }
        }

    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.e(TAG, "onDEstroy");
    }
    @Override
    public void onPause() {
        super.onPause();
        Log.e(TAG, "onPause");
    }
    @Override
    public void onResume(){
        super.onResume();
        try{
            oListView.setVisibility(View.VISIBLE);
            listBtn.setChecked(true);

            getActivity().runOnUiThread(new Runnable() {
                public void run() {
                    lsSchedules = CommonDB_Schedule.SearchSchedules("");
                   // lsSchedules = ai_Schedule.listAll(ai_Schedule.class);
                    adapter = new ScheduleListAdapter(getActivity(),
                            R.layout.frag_schedule_textview, lsSchedules == null ? (new ArrayList<v_Schedule>()) : lsSchedules);
                    adapter.notifyDataSetChanged();
                }
            });





        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.onResume", ex, getActivity());
        }
       // CommonHelper.trackEvent(this.getActivity(), "Android View Schedules Resume", null);
        CommonDB.InsertLog(getActivity(), "Event", "Schedule List View");


    }


    private void GoToInspection(int iInspectionID){
        if (CommonHelper.isKioskMode(getActivity())) {
            Intent oIntent = if_Ins_3rd.newIntent(getActivity(), iInspectionID);
            if (CommonHelper.isKioskMode(getActivity())) {
                startActivityForResult(oIntent, 2);
            } else {
                startActivity(oIntent);
            }
        }
        else {
            Intent oIntent = if_Ins_3rd.newIntent(getActivity(), iInspectionID);
            if (CommonHelper.isKioskMode(getActivity())) {
                startActivityForResult(oIntent, 2);
            } else {
                startActivity(oIntent);
            }
        }
    }
    public void ReloadData(){
        try {
            lsSchedules = CommonDB_Schedule.SearchSchedules("");
            adapter = new ScheduleListAdapter(getActivity(),
                    R.layout.frag_schedule_textview, lsSchedules == null ? (new ArrayList<v_Schedule>()) : lsSchedules);
            oListView.setAdapter(adapter);
            adapter.notifyDataSetChanged();
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.ReloadData", ex, getActivity());
        }
    }

    @Override
    public void onCheckedChanged(RadioGroup group, int checkedId) {
        switch (checkedId) {
            case R.id.listBtn:
                oListView.setVisibility(View.VISIBLE);
                break;
            case R.id.mapBtn:
                boolean isNetworkConnected = NetworkUtils.isNetworkAvailable(getActivity());
                if (!isNetworkConnected)
                    ShowAlert("Error", "Network Not Available");
                else {
                    if (Build.VERSION.SDK_INT >= 20) {
                        try {
                            if (isNetworkConnected && mapView != null) {
                                getActivity().runOnUiThread(new Runnable() {
                                    public void run() {
                                        mapView.getMapAsync(frag_schedules.this);
                                    }
                                });
                                oListView.setVisibility(View.INVISIBLE);
                            }
                        } catch (Exception exx) {

                        }
                    }


//                    if (!showedPins) {
//                        oDialog = ProgressDialog.show(getActivity(), "Connecting to Server", "Processing Request");
//                        oDialog.setCancelable(false);
//                        oDialog.setCanceledOnTouchOutside(false);
//                        oDialog.show();
//
//                    }


                }

                break;
            default:

        }
    }

    @Override
    public boolean onMarkerClick(Marker marker) {
        return false;
    }

    @Override
    public void onMapClick(LatLng latLng) {
    }

    private class ScheduleListAdapter extends ArrayAdapter<v_Schedule> {

        HashMap<v_Schedule, Integer> mIdMap = new HashMap<v_Schedule, Integer>();
        Context oContext;
        List<v_Schedule> lsSchedules;
        public ScheduleListAdapter(Context context, int textViewResourceId,
                                      List<v_Schedule> objects) {
            super(context, textViewResourceId, objects);
            oContext = context;
            lsSchedules = objects;
            for (int i = 0; i < objects.size(); ++i) {
                mIdMap.put(objects.get(i), i);
            }
        }

        @Override
        public long getItemId(int position) {
            v_Schedule item = getItem(position);
            return mIdMap.get(item);
        }

        @Override
        public boolean hasStableIds() {
            return true;
        }
        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = (LayoutInflater)oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            View row;

            row = inflater.inflate(R.layout.frag_schedule_textview, parent, false);
            try {


                final v_Schedule oSchedule = lsSchedules.get(position);
                ImageButton oButton = row.findViewById(R.id.btn_Inspection_Action);
                if (CommonJson.GetJsonKeyValue("iInsID", oSchedule.sCustom1) == null ){
                    oButton.setBackgroundResource (R.drawable.info);
                }
                else if (CommonJson.GetJsonKeyValue("iInsIDCom", oSchedule.sCustom1) != null){

                   // oButton.setText("Start");
                        oButton.setBackgroundResource (R.drawable.info);
                }
                else{
                        oButton.setBackgroundResource(R.drawable.forward);
                }
                if (CommonHelper.bRequestInspection(oSchedule.sCustom1)){
                    row.setBackgroundColor(Color.parseColor("#DBDDDE"));
                }
                else if (CommonValidate.bExternalInspection(oSchedule.sCustom1)){
                    row.setBackgroundColor(Color.parseColor("#DBDDDE"));
                }
                else if (CommonJson.GetJsonKeyValue("iInsIDCom", oSchedule.sCustom1) != null){
                    row.setBackgroundColor(Color.rgb(255, 229, 204));
                }
                else{
                    row.setBackgroundColor(Color.parseColor("#FFFFFF"));
                }

             /*   else if (CommonJson.GetJsonKeyValue("iInsID", oSchedule.sCustomOne) != null && CommonJson.GetJsonKeyValue("iInsIDCom", oSchedule.sCustomOne) != null){
//                    oButton.setText("Completed");
//                    oButton.setTextSize(14);
                    oButton.setBackgroundResource(R.drawable.check);
//                    oButton.setTextColor(getResources().getColor(R.color.upload_button));
                    row.setBackgroundColor(Color.parseColor("#CFFFFFFF"));
                }
                else{
                    oButton.setBackgroundResource(R.drawable.forward);
//                   oButton.setText("View");
//                    oButton.setTextColor(getResources().getColor(R.color.view_button));
                    row.setBackgroundColor(Color.rgb(255, 229, 204));
                }
                if (CommonHelper.bRequestInspection(oSchedule.sCustomOne)){
                    //oButton.setBackgroundResource(R.drawable.info);
//                    oButton.setTextColor(getResources().getColor(R.color.upload_button));
                   // Android
                    row.setBackgroundColor(Color.parseColor("#ACD1E9"));
                }*/



                oButton.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        ViewSchedule(oSchedule);
                    }
                });
                LinearLayout oLayout = row.findViewById(R.id.layout_Inspection_Action);
                oLayout.setOnClickListener(new View.OnClickListener() {
                    @Override
                    public void onClick(View view) {
                        ViewSchedule(oSchedule);
                    }
                });

//                Button rButton = (Button)row.findViewById(R.id.btn_map_Action);
//                rButton.setOnClickListener(new View.OnClickListener() {
//                    @Override
//                    public void onClick(View view) {
//                        RouteSchedule(oSchedule);
//                    }
//                });

                TextView oInsTitle = row.findViewById(R.id.txt_InsAddress);
                oInsTitle.setText(oSchedule.sAddress1 + ", " + oSchedule.sAddress2);
                TextView oRef = row.findViewById(R.id.txt_Asset_Ref);
                if (oSchedule.sRef == null || oSchedule.sRef.trim().length() == 0){
                    oRef.setVisibility(View.GONE);
                }
                else{
                    oRef.setText(oSchedule.sRef);
                    oRef.setVisibility(View.VISIBLE);
                }



               // oRef.setText( oSchedule.sRef);

                TextView oInsInfo = row.findViewById(R.id.txt_InsInfo);
                String sDetailsTitle = oSchedule.sInsTitle + " @ " + oSchedule.sDateTime;
                if (CommonHelper.bRequestInspection(oSchedule.sCustom1) || CommonValidate.bExternalInspection(oSchedule.sCustom1)){
                    sDetailsTitle = sDetailsTitle + " - For " + CommonJson.GetJsonKeyValue("RName", oSchedule.sCustom1);
                    if (CommonJson.GetJsonKeyValue("RIP", oSchedule.sCustom1) == null){
                        oButton.setBackgroundResource (0);
                    }
                }

                oInsInfo.setText(sDetailsTitle);
            }catch(Exception ex){
                ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.getView", ex, oContext);
            }

            return (row);
        }
    }

    private void ShowAlert(final String sTitle, String sMessage){
        CommonUI.ShowAlert(getActivity(), sTitle, sMessage);
//        AlertDialog.Builder builder = CommonUI.GetAlertBuilder(sTitle, sMessage, getActivity(), false, true);
//
//        builder.show();
    }
}