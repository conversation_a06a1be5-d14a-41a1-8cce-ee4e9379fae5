package com.snapinspect.snapinspect3.activity;

import android.view.MenuItem;
import android.widget.Button;
import android.widget.ListView;
import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.snapinspect.snapinspect3.Adapter.FormItemsAdapter;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonUI;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.Helper.IF_CreateInspection;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.activitynew.Home.if_HomeTab;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Ins_3rd;
import com.snapinspect.snapinspect3.activitynew.inspection3rd.if_Layout_3rd;
import com.snapinspect.snapinspect3.util.ArrayUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.SystemUiHider;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import com.orm.query.Condition;
import com.orm.query.Select;
import com.snapinspect.snapinspect3.R;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.addressLine1;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.addressLine2;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.ViewType.textInput;

/**
 * An example full-screen activity that shows and hides the system UI (i.e.
 * status bar and navigation/system bar) with user interaction.
 *
 * @see SystemUiHider
 */
public class if_NewAsset extends Activity {
    private int iSInsTypeID;
    private List<if_FormItem> formModels;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        try {
            super.onCreate(savedInstanceState);
           // CommonHelper.trackEvent(this, "Android View New Asset", null);
            setContentView(R.layout.activity_newasset);
            getActionBar().show();
            getActionBar().setDisplayHomeAsUpEnabled(true);

            iSInsTypeID = getIntent().getIntExtra(Constants.Extras.iSInsTypeID, 0);

            Button btnStartIns = findViewById(R.id.btn_start_new_inspection);
            btnStartIns.setOnClickListener(v -> startNewInspection());

            Button btnAssetList = findViewById(R.id.btn_asset_list);
            btnAssetList.setOnClickListener(v -> {
                finish();
                Intent intent = new Intent(Constants.Broadcasts.sHomeTabSwitched);
                intent.putExtra(Constants.Extras.iTabIndex, if_HomeTab.FRAGMENT_TAB.ASSETS.ordinal());
                LocalBroadcastManager.getInstance(this).sendBroadcast(intent);
            });

            ArrayList<if_FormItem> items = new ArrayList<>();
            if_FormItem line1 = new if_FormItem(addressLine1, textInput);
            line1.title = "Address Line 1";
            line1.requiredTitle = "Line 1 required";
            items.add(line1);

            if_FormItem line2 = new if_FormItem(addressLine2, textInput);
            line2.title = "Address Line 2";
            line2.requiredTitle = "Line 2 required";
            items.add(line2);
            formModels = items;

            ListView listView = findViewById(R.id.lv_formItems);
            listView.setAdapter(new FormItemsAdapter(this, items));

        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "if_newasset.onCreate", ex, this);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        CommonDB.InsertLog(if_NewAsset.this, "Event", "New Asset View");
    }

    @Override
    public boolean onMenuItemSelected(int featureId, @NonNull MenuItem item) {
        if (item.getItemId() == android.R.id.home) {
            finish();
        }
        return true;
    }

    private void goToInspection(int iInspectionID) {
        Intent oIntent = if_Ins_3rd.newIntent(this, iInspectionID);
        if (CommonHelper.isKioskMode(this)) {
            startActivityForResult(oIntent, 2);
        } else {
            startActivity(oIntent);
        }
    }

    private void PushToNext(ai_Assets oAsset) {
        startActivity(if_Layout_3rd.newIntent(this, oAsset.iSAssetID, iSInsTypeID,
                oAsset.sAddressOne, oAsset.sAddressTwo));
        finish();
    }

    public String getFormValue(String identifier) {
        for (if_FormItem formModel: formModels) {
            if (formModel.identifier.equals(identifier)) {
                return formModel.value;
            }
        }
        return "";
    }

    private void startNewInspection() {
        String sAddress1 = getFormValue(addressLine1);
        String sAddress2 = getFormValue(addressLine2);
        if (StringUtils.isEmpty(sAddress1) || StringUtils.isEmpty(sAddress2)) {
            CommonUI.ShowAlert(this, "Error", "Please enter Address Line 1 and Address Line 2");
            return;
        }
        List<ai_Assets> lsAssets = ai_Assets.find(ai_Assets.class, "S_FILTER = ?", CommonHelper.GetFilter(sAddress1, sAddress2));
        List<ai_InsType> lsInsType = ai_InsType.find(ai_InsType.class, "I_S_INS_TYPE_ID = ?", "" + iSInsTypeID);
        if (lsInsType != null && lsInsType.size() == 1) {
            ai_InsType oInsType = lsInsType.get(0);
            ai_Assets oAsset;
            if (!ArrayUtils.isEmpty(lsAssets)) {
                oAsset = lsAssets.get(0);
            } else {
                oAsset = new ai_Assets();
                oAsset.sAddressTwo = sAddress2;
                oAsset.sAddressOne = sAddress1;
                oAsset.iSAssetID = 0;
            }

            List<ai_AssetLayout> lsAssetLayout = CommonDB.GetAssetLayouts(oAsset.iSAssetID, oInsType.sPTC, if_NewAsset.this);
            if (lsAssetLayout == null || lsAssetLayout.size() == 0) {
                List<ai_Layout> lsLayoutItem = Select.from(ai_Layout.class).where(Condition.prop("S_PTC").eq(oInsType.sPTC), Condition.prop("I_SP_LAYOUT_ID").eq("" + 0)).list();
                boolean bPass = true;
                for (ai_Layout aiLayout : lsLayoutItem) {
                    String sConfig = oInsType.sType.equals("F") ? aiLayout.sFConfig : aiLayout.sSConfig;
                    if (CommonJson.GetJsonKeyValue("ADD", sConfig) == null) {
                        bPass = false;
                        break;
                    }

                }
                if (bPass) {
                    IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                    Date oNow = new Date();
                    int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                            if_NewAsset.this, new ArrayList<>(), oInsType, oAsset.iSAssetID, "",
                            CommonHelper.sDateToString(oNow), "", oAsset.sAddressOne, oAsset.sAddressTwo, 0, "");
                    CommonDB.InsertLog(if_NewAsset.this, "Ins Setup - New", "Load Ins By Pass - " +
                            oInsType.iSInsTypeID + " - InsTypeCount: " + lsInsType.size() + " Address1: "
                            + oAsset.sAddressOne + ", " + oAsset.sAddressTwo);
                    goToInspection(iInspectionID);
                } else {
                    CommonDB.InsertLog(if_NewAsset.this, "Ins Setup - New", "Setup Layout - " +
                            oInsType.iSInsTypeID + " - InsTypeCount: " + lsInsType.size() + " Address1: "
                            + oAsset.sAddressOne + ", " + oAsset.sAddressTwo);
                    PushToNext(oAsset);
                }
            } else {
                IF_CreateInspection oCreateInspection = new IF_CreateInspection();
                int iInspectionID = oCreateInspection.CreateInspectionFromAssetLayout(
                    if_NewAsset.this, new ArrayList<>(lsAssetLayout), oInsType, oAsset.iSAssetID, "",
                    CommonHelper.sDateToString(null), "", oAsset.sAddressOne, oAsset.sAddressTwo, 0, "");
                CommonDB.InsertLog(if_NewAsset.this, "Ins Setup - New", "Load Ins From Existing Layout InsTypeID: " +
                        oInsType.iSInsTypeID + " - InsTypeCount: " + lsInsType.size() + " Address1: "
                        + oAsset.sAddressOne + ", " + oAsset.sAddressTwo);

                goToInspection(iInspectionID);
            }
        }
    }
}
