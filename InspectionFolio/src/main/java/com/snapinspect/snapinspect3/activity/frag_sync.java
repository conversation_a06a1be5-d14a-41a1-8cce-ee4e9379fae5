package com.snapinspect.snapinspect3.activity;

import android.app.Fragment;
import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.ListView;
import android.widget.TextView;

import com.snapinspect.snapinspect3.R;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * Created by <PERSON><PERSON><PERSON> on 14/03/14.
 */
public class frag_sync extends Fragment {
    ArrayList<SyncProgressObject> lsSyncProgress;
    SyncListAdapter adapter;
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        SyncProgressObject oSync = new SyncProgressObject();
        oSync.sTitle = "Test1";
        oSync.bProgress = true;
        oSync.bCompleted = false;
        lsSyncProgress = new ArrayList<SyncProgressObject>();
        lsSyncProgress.add(oSync);
        View oRootView = inflater.inflate(R.layout.frag_sync, container, false);
        adapter = new SyncListAdapter(getActivity(),
                R.layout.frag_sync_textview, lsSyncProgress == null ? (new ArrayList<SyncProgressObject>()) : lsSyncProgress);
        // View rootView = inflater.inflate(R.layout.frag_assets, container, false);
        ListView oListView = oRootView.findViewById(R.id.lv_Sync);
        oListView.setAdapter(adapter);

        return oRootView;

    }
    private class SyncProgressObject{
        public String sTitle;
        public boolean bProgress;
        public boolean bCompleted;
    }
    private class SyncListAdapter extends ArrayAdapter<SyncProgressObject> {

        HashMap<SyncProgressObject, Integer> mIdMap = new HashMap<SyncProgressObject, Integer>();
        Context oContext;
        ArrayList<SyncProgressObject> lsObjects;
        public SyncListAdapter(Context context, int textViewResourceId,
                                 ArrayList<SyncProgressObject> objects) {
            super(context, textViewResourceId, objects);
            for (int i = 0; i < objects.size(); ++i) {
                mIdMap.put(objects.get(i), i);
            }
            oContext = context;
            lsObjects = objects;
        }

        @Override
        public long getItemId(int position) {
            SyncProgressObject item = getItem(position);
            return mIdMap.get(item);
        }

        @Override
        public boolean hasStableIds() {
            return true;
        }
        @Override
        public View getView(int position, View convertView, ViewGroup parent) {
            LayoutInflater inflater = (LayoutInflater)oContext.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
            View row;
            SyncProgressObject oSyncStatus = lsObjects.get(position);
            row = inflater.inflate(R.layout.frag_sync_textview, parent, false);
            TextView oInsTitle = row.findViewById(R.id.txt_SyncTitle);
            oInsTitle.setText(oSyncStatus.sTitle);
            row.findViewById(R.id.progress_Sync).setVisibility(View.GONE);
            row.findViewById(R.id.txt_SyncCompleted).setVisibility(View.GONE);
            if (oSyncStatus.bCompleted){
                row.findViewById(R.id.txt_SyncCompleted).setVisibility(View.VISIBLE);

            }
            if (oSyncStatus.bProgress){
                row.findViewById(R.id.progress_Sync).setVisibility(View.VISIBLE);
            }
            return (row);
        }

    }
}