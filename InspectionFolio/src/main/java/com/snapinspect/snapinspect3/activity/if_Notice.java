package com.snapinspect.snapinspect3.activity;

import android.app.Activity;
import android.app.DatePickerDialog;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.view.*;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ListView;

import androidx.annotation.NonNull;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Adapter.NoticeFormsAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.activity.if_CameraX.CameraSaveOption;
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayAllPhotos;
import com.snapinspect.snapinspect3.activitynew.Photo.if_DisplayPhoto;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.snapinspect.snapinspect3.Helper.Constants.Limits.iMaxVideoDuration;
import static com.snapinspect.snapinspect3.Helper.Constants.Values.DEFAULT_VIDEO_QUALITY;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.photos;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.taskCategory;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.taskDescription;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.taskName;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.Identifiers.videos;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.ViewType.tagSelect;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.ViewType.textInput;
import static com.snapinspect.snapinspect3.IF_Object.if_FormItem.ViewType.unknown;

public class if_Notice extends Activity {
    public static final SimpleDateFormat dueDateFormat = new SimpleDateFormat("MMM dd, yyyy");

    private ListView mListView;
    private NoticeFormsAdapter mFormsAdapter;
    private List<if_FormItem> mFormDataSource;

    private ai_Notification oNotification;
    private long iInsItemID;
    private long iNoticeID;

    public static Intent newIntent(Context context, long iNoticeID, long iInsItemID, long iInspectionID) {
        Intent intent = new Intent(context, if_Notice.class);
        intent.putExtra(Constants.Extras.iNoticeID, iNoticeID);
        intent.putExtra(Constants.Extras.iInsItemID, iInsItemID);
        intent.putExtra(Constants.Extras.iInsID, iInspectionID);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_if_notice);
        getActionBar().setDisplayHomeAsUpEnabled(true);

        mListView = findViewById(R.id.lv_formItems);
        mFormsAdapter = new NoticeFormsAdapter(new ArrayList<>(), this, noticeFormsAdapterDelegate);
        mListView.setAdapter(mFormsAdapter);

        iInsItemID = getIntent().getLongExtra(Constants.Extras.iInsItemID, 0);
        long iInsID = getIntent().getLongExtra(Constants.Extras.iInsID, 0);
        iNoticeID = getIntent().getLongExtra(Constants.Extras.iNoticeID, 0);

        oNotification = iNoticeID > 0 ? CommonDB_Notification.getNotification(iNoticeID) : null;
        // if iNoticeID is 0, or not found in database, create a new one
        if (oNotification == null) {
            oNotification = createTempNotification();
            oNotification.iInsItemID = iInsItemID;
            oNotification.iInsID = iInsID;
        } else {
            // if iNoticeID is found in database, update the insItemID and insID
            iInsItemID = oNotification.iInsItemID;
            iInsID = oNotification.iInsID;
        }

        buildFormDataSource();
        mFormsAdapter.setDataSource(mFormDataSource);
        mFormsAdapter.notifyDataSetChanged();

        LocalBroadcastManager.getInstance(this).registerReceiver(
                photoBroadcastReceiver, new IntentFilter(Constants.Broadcasts.sInsertNoticePhoto));
        LocalBroadcastManager.getInstance(this).registerReceiver(
                photoBroadcastReceiver, new IntentFilter(Constants.Broadcasts.sDeleteNoticePhoto));
        LocalBroadcastManager.getInstance(this).registerReceiver(
                photoBroadcastReceiver, new IntentFilter(Constants.Broadcasts.sPreviewNoticePhoto));
        LocalBroadcastManager.getInstance(this).registerReceiver(
                videoBroadcastReceiver, new IntentFilter(Constants.Broadcasts.sInsertNoticeVideo));
        LocalBroadcastManager.getInstance(this).registerReceiver(
                videoBroadcastReceiver, new IntentFilter(Constants.Broadcasts.sDeleteNoticeVideo));

        getActionBar().setTitle(isCreatingNotice() ? R.string.title_activity_if_notice_create : R.string.title_activity_if_notice_edit);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(photoBroadcastReceiver);
        LocalBroadcastManager.getInstance(this).unregisterReceiver(videoBroadcastReceiver);
    }

    @Override
    protected void onResume() {
        super.onResume();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.menu_save, menu);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onMenuItemSelected(int featureId, MenuItem item) {
        if (checkFormDataChanges()) {
            if (item.getItemId() == R.id.action_menu_save) {
                tryToSaveNotification();
            } else {
                confirmSaveNotification();
            }
            return true;
        } else {
            onBackPressed();
            return false;
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            View v = getCurrentFocus();
            if (v instanceof EditText) {
                Rect outRect = new Rect();
                v.getGlobalVisibleRect(outRect);
                if (!outRect.contains((int) event.getRawX(), (int) event.getRawY())) {
                    v.clearFocus();
                    CommonHelper.hideSoftKeyboard(this);
                }
            }
        }
        return super.dispatchTouchEvent(event);
    }

    private void buildFormDataSource() {
        ArrayList<if_FormItem> dataSource = new ArrayList<>();

        if_FormItem taskName = new if_FormItem(if_FormItem.Identifiers.taskName, textInput);
        taskName.title = "Task Name:";
        taskName.value = oNotification.sTitle;
        dataSource.add(taskName);

        if_FormItem taskDescription = new if_FormItem(if_FormItem.Identifiers.taskDescription, textInput);
        taskDescription.title = "Task Description:";
        taskDescription.value = oNotification.sDescription;
        dataSource.add(taskDescription);

        if_FormItem taskCategory = new if_FormItem(if_FormItem.Identifiers.taskCategory, tagSelect);
        taskCategory.title = "Task Category:";
        taskCategory.value = oNotification.sCategory;
        dataSource.add(taskCategory);

        if_FormItem taskMisc = new if_FormItem(if_FormItem.Identifiers.taskMisc, unknown);
        taskMisc.value = oNotification.sCustomOne;
        dataSource.add(taskMisc);

        if_FormItem photos = new if_FormItem(if_FormItem.Identifiers.photos, if_FormItem.ViewType.photos);
        photos.value = oNotification.sPhotoURL;
        dataSource.add(photos);

        if_FormItem videos = new if_FormItem(if_FormItem.Identifiers.videos, if_FormItem.ViewType.videos);
        videos.value = oNotification.sVideoID;
        dataSource.add(videos);

        mFormDataSource = dataSource;
    }

    private final BroadcastReceiver videoBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            if (Constants.Broadcasts.sInsertNoticeVideo.equals(intent.getAction())) {
                long videoID = intent.getLongExtra(Constants.Extras.iVideoID, 0);
                if (videoID > 0) oNotification.sVideoID = String.valueOf(videoID);
            } else if (Constants.Broadcasts.sDeleteNoticeVideo.equals(intent.getAction())) {
                oNotification.sVideoID = "";
            }

            oNotification.save();
            if_FormItem taskVideo = itemWithIdentifier(if_FormItem.Identifiers.videos);
            if (taskVideo != null) {
                taskVideo.value = oNotification.sVideoID;
            }
            reloadFormItemWithIdentifier(videos);
        }
    };

    private final BroadcastReceiver photoBroadcastReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            long photoID = intent.getLongExtra(Constants.Extras.iPhotoID, 0);
            if (photoID == 0) return;

            String sAction = intent.getAction();
            if (sAction == null) return;

            switch (sAction) {
                case Constants.Broadcasts.sInsertNoticePhoto:
                case Constants.Broadcasts.sDeleteNoticePhoto: {
                    if (StringUtils.isEmpty(oNotification.sPhotoURL)) {
                        oNotification.sPhotoURL = "";
                    }
                    if (sAction.equalsIgnoreCase(Constants.Broadcasts.sInsertNoticePhoto)) {
                        oNotification.sPhotoURL = CommonHelper.sAttachPhoto(oNotification.sPhotoURL, String.valueOf(photoID));
                    } else {
                        oNotification.sPhotoURL = CommonHelper.sRemovePhoto(oNotification.sPhotoURL, String.valueOf(photoID));
                    }

                    if_FormItem taskPhotos = itemWithIdentifier(if_FormItem.Identifiers.photos);
                    if (taskPhotos != null) {
                        taskPhotos.value = oNotification.sPhotoURL;
                    }
                    reloadFormItemWithIdentifier(photos);
                }
                break;
                case Constants.Broadcasts.sPreviewNoticePhoto: /**/{
                    startActivity(if_DisplayPhoto.newIntent(context.getApplicationContext(),
                            photoID, 0, iInsItemID, oNotification.sPhotoURL, true));
                }
                break;
            }
        }
    };

    private final NoticeFormsAdapter.Delegate noticeFormsAdapterDelegate = new NoticeFormsAdapter.Delegate() {
        @Override
        public void updateNoticeParam(String identifier, String value) {
            switch (identifier) {
                case taskName: oNotification.sTitle = value; break;
                case taskDescription: oNotification.sDescription = value; break;
                case taskCategory: {
                    oNotification.sCategory = value;
                    try {
                        oNotification.sCustomOne = CommonJson.AddJsonKeyValue(
                                StringUtils.isEmpty(oNotification.sCustomOne) ? "" : oNotification.sCustomOne,
                                ai_Notification.CustomKeys.category, value);
                    } catch (Exception eee) {
                        //
                    }

                }
                break;
            }
        }

        @Override
        public void didTakePhotos() {
            if (!CommonValidate.validateGPSLocationPermission(if_Notice.this)) return;
            startActivity(if_CameraX.newIntent(
                if_Notice.this, CameraSaveOption.PHOTO,
                (int) iInsItemID, 0, true, Constants.Limits.iMaxPhotoCount
            ));
        }

        @Override
        public void didTakeVideos() {
            if (!CommonValidate.validateGPSLocationPermission(if_Notice.this)) return;
            VideoQuality videoQuality = Objects.requireNonNullElse(db_InsItem.getInsTypeVideoSize(iInsItemID), DEFAULT_VIDEO_QUALITY);
            if (CommonHelper.bUseNewVideo()) {
                startActivity(if_video.newIntent(if_Notice.this, oNotification.getId(), iInsItemID, iMaxVideoDuration, videoQuality));
            } else {
                startActivity(if_video_old.newIntent(if_Notice.this, oNotification.getId(), iInsItemID, iMaxVideoDuration));
            }
        }

        @Override
        public void reloadData(int position) {
            mFormsAdapter.notifyDataSetChanged();
        }

        @Override
        public void didSelectDueDate() {
            Calendar calendar = Calendar.getInstance();
            String sDueDate = oNotification.sDueDate;
            try {
                calendar.setTime(dueDateFormat.parse(sDueDate));
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception(ex);
            }
            new DatePickerDialog(if_Notice.this, onDateSetListener,
                    calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH)
                ).show();
        }

        @Override
        public void didSelectAssignUser() {
            final List<ai_User> users = CommonDB.GetAllUsers();

            ArrayList<String> userNames = new ArrayList<>();
            for (int i = 0; i < users.size(); i++) {
                userNames.add(users.get(i).sName);
            }

            new MaterialDialog.Builder(if_Notice.this)
                    .title(R.string.title_assign_task)
                    .items(userNames)
                    .itemsCallbackSingleChoice(-1, (dialog, view, which, text) -> {
                        ai_User user = users.get(which);
                        oNotification.sCustomOne = CommonJson.AddJsonKeyValue(
                                StringUtils.isEmpty(oNotification.sCustomOne) ? "" : oNotification.sCustomOne,
                                ai_Notification.CustomKeys.assignedToUser, String.valueOf(user.iCustomerID));
                        reloadTaskMiscData();
                        return true;
                    })
                    .negativeText(R.string.md_cancel_label)
                    .show();
        }

        @Override
        public void didSelectTaskPriority() {
            new MaterialDialog.Builder(if_Notice.this)
                    .title(R.string.title_task_priority)
                    .items(TaskPriority.displayNames())
                    .itemsCallbackSingleChoice(-1, (dialog, view, which, text) -> {
                        int iPriority = TaskPriority.values()[which].getValue();
                        String sCustomOne = CommonJson.AddJsonKeyValue(
                                StringUtils.isEmpty(oNotification.sCustomOne) ? "" : oNotification.sCustomOne,
                                ai_Notification.CustomKeys.priority, String.valueOf(iPriority));
                        oNotification.iPriority = iPriority;
                        oNotification.sCustomOne = sCustomOne;
                        reloadTaskMiscData();
                        return true;
                    })
                    .negativeText(R.string.md_cancel_label)
                    .show();
        }

        @Override
        public void didViewAllPhotos() {
            // all photos
            startActivity(if_DisplayAllPhotos.newIntent(if_Notice.this, iInsItemID, oNotification.sPhotoURL, true));
        }

        @Override
        public void didViewPhoto(int photoId) {
            startActivity(if_DisplayPhoto.newIntent(if_Notice.this,
                    photoId, 0, iInsItemID, oNotification.sPhotoURL, true));
        }

        @Override
        public void didPlayVideo(long iVideoID, String sVideoFilePath) {
            if_Notice.this.startActivity(if_videoshow.newIntent(
                    if_Notice.this, iInsItemID, oNotification.getId(), iVideoID, sVideoFilePath));
        }
    };

    private final DatePickerDialog.OnDateSetListener onDateSetListener = (view, year, month, dayOfMonth) -> {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, dayOfMonth);

        String dueDate = dueDateFormat.format(calendar.getTime());
        String sCustomOne = CommonJson.AddJsonKeyValue(
                StringUtils.isEmpty(oNotification.sCustomOne) ? "" : oNotification.sCustomOne,
                ai_Notification.CustomKeys.dueDate, dueDate);
        oNotification.sDueDate = dueDate;
        oNotification.sCustomOne = sCustomOne;
        reloadTaskMiscData();
    };

    private if_FormItem itemWithIdentifier(@NonNull String identifier) {
        for (if_FormItem item: mFormDataSource) {
            if (item.identifier.equals(identifier)) return item;
        }
        return null;
    }

    private void reloadTaskMiscData() {
        if_FormItem taskMisc = itemWithIdentifier(if_FormItem.Identifiers.taskMisc);
        if (taskMisc == null) return;
        taskMisc.value = oNotification.sCustomOne;
        reloadFormItemWithIdentifier(if_FormItem.Identifiers.taskMisc);
    }

    private void reloadFormItemWithIdentifier(String itemIdentifier) {
        if_FormItem formItem = itemWithIdentifier(itemIdentifier);
        if (formItem == null) return;
        mFormsAdapter.notifyDataSetChanged();
    }

    private void confirmSaveNotification() {
        new MaterialDialog.Builder(this)
                .title(R.string.alert_title_attention)
                .content("This reminder is not saved, do you wanna discard the changes?")
                .positiveText(R.string.btn_action_save_back)
                .onPositive((dialog, which) -> tryToSaveNotification())
                .negativeText(R.string.btn_action_discard)
                .onNegative((dialog, which) -> {
                    if (isCreatingNotice()) {
                        if (!StringUtils.isEmpty(oNotification.sPhotoURL)) {
                            List<ai_Photo> existingPhotos = CommonDB_Inspection.GetInsItemPhotos(
                                    iInsItemID, oNotification.sPhotoURL);
                            for (ai_Photo photo : existingPhotos) {
                                CommonDB.DeletePhotoByPhotoID(photo.getId().intValue());
                            }
                        }
                        if (!StringUtils.isEmpty(oNotification.sVideoID)) {
                            long videoId = CommonHelper.getInt(oNotification.sVideoID);
                            CommonDB.DeleteVideoByIDNotification(videoId, -1, if_Notice.this);
                        }
                    }
                    finish();
                })
                .show();
    }

    private boolean isCreatingNotice() {
        return iNoticeID <= 0;
    }

    private void tryToSaveNotification() {
        if (!validateFormData()) return;

        if (!isCreatingNotice()) {
            ai_Notification notificationInDB = CommonDB_Notification.getNotification(oNotification.getId());
            if (notificationInDB.getId() > 0 & !StringUtils.isEmpty(notificationInDB.sPhotoURL)) {
                List<ai_Photo> existingPhotos = CommonDB_Inspection.GetInsItemPhotos(iInsItemID, notificationInDB.sPhotoURL);
                String[] photoIds = StringUtils.isEmpty(oNotification.sPhotoURL) ? new String[]{} : oNotification.sPhotoURL.split(",");
                for (ai_Photo photo : existingPhotos) {
                    if (Arrays.asList(photoIds).contains(photo.getId().toString())) continue;
                    CommonDB.DeletePhotoByPhotoID(photo.getId().intValue());
                }
            }

            if (!StringUtils.isEmpty(notificationInDB.sVideoID) && !notificationInDB.sVideoID.equals(oNotification.sVideoID)) {
                long videoId = CommonHelper.getInt(notificationInDB.sVideoID);
                CommonDB.DeleteVideoByIDNotification(videoId, notificationInDB.getId(), if_Notice.this);
            }
        }

        oNotification.bDeleted = false;
        oNotification.save();
        ai_InsItem oInsItem = ai_InsItem.findById(ai_InsItem.class, iInsItemID);
        oInsItem.sCustomOne = CommonJson.AddJsonKeyValue(oInsItem.sCustomOne, "notice", "1");
        oInsItem.save();

        finish();
    }

    private boolean checkFormDataChanges() {
        return isCreatingNotice() || !oNotification.equals(CommonDB_Notification.getNotification(oNotification.getId()));
    }

    private boolean validateFormData() {
        if (StringUtils.isEmpty(oNotification.sTitle)) {
            CommonUI.ShowAlert(this, "Error", "Task Name can not be empty.");
            return false;
        }

        /*
        if (StringUtils.isEmpty(oNotification.sDescription)) {
            CommonUI.ShowAlert(this, "Error", "Task Description can not be empty.");
            return false;
        }

        if (StringUtils.isEmpty(oNotification.sCategory)) {
            CommonUI.ShowAlert(this, "Error", "Please select task category.");
            return false;
        }

        if (StringUtils.isEmpty(oNotification.sDueDate)) {
            CommonUI.ShowAlert(this, "Error", "Please select due date.");
            return false;
        }

        if (oNotification.getTaskAssignToUser() == null) {
            CommonUI.ShowAlert(this, "Error", "Please select due date.");
            return false;
        }
        */
        return true;
    }

    private ai_Notification createTempNotification() {
        ai_Notification notice = new ai_Notification();
        notice.bDeleted = true;
        notice.save();
        return notice;
    }
}

