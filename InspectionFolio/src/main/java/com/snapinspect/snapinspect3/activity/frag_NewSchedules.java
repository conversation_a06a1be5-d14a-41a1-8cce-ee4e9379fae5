package com.snapinspect.snapinspect3.activity;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Context;
import android.graphics.Point;
import android.graphics.drawable.Drawable;
import android.os.AsyncTask;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.text.InputType;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.EditorInfo;
import android.widget.Button;
import android.widget.EditText;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import com.prolificinteractive.materialcalendarview.*;
import com.prolificinteractive.materialcalendarview.format.WeekDayFormatter;
import com.prolificinteractive.materialcalendarview.spans.DotSpan;
import com.snapinspect.snapinspect3.Adapter.ScheduleSectionModel;
import com.snapinspect.snapinspect3.Adapter.SchedulesAdapter;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.v_Schedule;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.util.ThrottledSearch;
import com.snapinspect.snapinspect3.views.StickyHeaderListView.StickyHeaderListView;
import org.dmfs.rfc5545.DateTime;
import org.dmfs.rfc5545.recur.RecurrenceRule;
import org.dmfs.rfc5545.recur.RecurrenceRuleIterator;
import org.jetbrains.annotations.NotNull;

import java.lang.ref.WeakReference;
import java.util.*;
import java.util.concurrent.Executors;

public class frag_NewSchedules extends Fragment {

    private static class TimeRange {
        public long start;
        public long end;
        private boolean isValid() {
            return start > 0 && end > 0 && start < end;
        }
    }

    private final static String PREFIX_R_RULE = "RRULE:";
    private final static String TAG = "FRAG_SCHEDULES";
    private final static int DELAY = 200;
    private final static int UPCOMING_MONTHS = 3;

    /// State bundle
    private final static String keySelectedTabIndex = "tabIndex";
    private int selectedTabIndex = 0;

    private ThrottledSearch throttledSearch;
    private long mEarliestDateTime = -1;
    private final TimeRange mTimeRange = new TimeRange();
    private List<ScheduleSectionModel> sectionModels;

    private StickyHeaderListView oListView;
    private MaterialCalendarView calendarView;
    private EditText oSearchBar;
    private View oSchedulesTab, vBottomLine;
    private SchedulesAdapter.Type scheduleType;
    private Button selectedTab;
    private Button[] btnTabs;
    private View oRootView, emptyView, todayHeaderView;
    private TextView tvTodayTitle, tvTodaySubtitle, tvEmpty;
    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {

        if (Build.VERSION.SDK_INT >= 20) {
            oRootView = inflater.inflate(R.layout.frag_new_schedule, container, false);
        } else {
            oRootView = inflater.inflate(R.layout.frag_schedule_low, container, false);
        }

        return oRootView;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        //  CommonHelper.trackEvent(getActivity(), "Android View Schedules", null);

        try {
            /// Calendar
            configureCalendarView();

            /// List View
            oListView = view.findViewById(R.id.lv_Schedules);
            oListView.getListView().setDividerHeight(0);
            oListView.getListView().setOnItemClickListener((parent, v, position, id) -> {
                v_Schedule item = (v_Schedule) parent.getItemAtPosition(position);
                if (item != null) CommonSchedule.viewSchedule(getActivity(), item);
            });

            if (Build.VERSION.SDK_INT >= 20) {
                /// search bar
                configureSearchEditText();
                /// tab bar
                oSchedulesTab = oRootView.findViewById(R.id.schedules_tab_bar);
                vBottomLine = oRootView.findViewById(R.id.line_selected_bottom);
                todayHeaderView = oRootView.findViewById(R.id.today_header_view);
                tvTodayTitle = oRootView.findViewById(R.id.today_header_tv_title);
                tvTodaySubtitle = oRootView.findViewById(R.id.today_header_tv_subtitle);
                emptyView = oRootView.findViewById(R.id.schedule_empty_view);
                tvEmpty = oRootView.findViewById(R.id.schedule_empty_text);

                /// tab buttons
                configureTabButtons();

                new Handler().postDelayed(() -> setSelectedTab(btnTabs[selectedTabIndex]), DELAY);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.onCreateView", ex, getActivity());
        }
    }

    public void ReloadSchedules() {
        if (oSearchBar == null) return;
        ReloadSchedules(oSearchBar.getText().toString());
    }

    @Override
    public void onAttach(@NonNull Context context) {
        super.onAttach(context);
        throttledSearch = new ThrottledSearch(getActivity(), this::ReloadSchedules);
    }

    @Override
    public void onSaveInstanceState(Bundle outState) {
        super.onSaveInstanceState(outState);
        outState.putInt(keySelectedTabIndex, selectedTabIndex);
    }

    @Override
    public void onViewStateRestored(Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        if (null != savedInstanceState) {
            selectedTabIndex = savedInstanceState.getInt(keySelectedTabIndex);
        }
    }

    private void configureSearchEditText() {
        oSearchBar = oRootView.findViewById(R.id.search_schedules);
        oSearchBar.setInputType(InputType.TYPE_CLASS_TEXT);
        oSearchBar.setOnEditorActionListener((v, actionId, event) -> {
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                CommonHelper.hideSoftKeyboard(getActivity());
                oSearchBar.clearFocus();
                return true;
            }
            return false;
        });

        throttledSearch.bindTo(oSearchBar);
    }

    private void configureTabButtons() {
        btnTabs = new Button[] {
             oRootView.findViewById(R.id.btn_schedule_today),
             oRootView.findViewById(R.id.btn_schedule_weekly),
             oRootView.findViewById(R.id.btn_schedule_monthly),
             oRootView.findViewById(R.id.btn_schedule_upcoming),
             oRootView.findViewById(R.id.btn_schedule_overdue)
        };

        for (int i = 0; i < btnTabs.length; i++) {
            Button tab = btnTabs[i];
            int tabIndex = i;
            tab.setOnClickListener(v -> {
                switch (v.getId()) {
                    case R.id.btn_schedule_today:
                    case R.id.btn_schedule_weekly:
                    case R.id.btn_schedule_monthly:
                        oSearchBar.getText().clear();
                        CommonHelper.hideSoftKeyboard(getActivity());
                        break;
                    default: break;
                }
                selectedTabIndex = tabIndex;
                setSelectedTab((Button) v);
            });
        }
    }

    private void setSelectedTab(Button tabButton) {
        try {
            selectedTab = tabButton;
            calendarView.clearSelection();
            UpdateViewsWithSelected(selectedTab);
            UpdateTimeRange();
            if (!isCalendarVisible()) {
                ReloadSchedules(oSearchBar.getText().toString());
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.GetRecurSchedules", ex, getActivity());
        }
    }

    private void configureCalendarView() {
        calendarView = oRootView.findViewById(R.id.calendarView);
        calendarView.setWeekDayFormatter(WeekDayFormatter.DEFAULT);
        calendarView.setWeekDayFormatter(day -> {
            Calendar calendar = CalendarUtils.getInstance();
            calendar.get(Calendar.DAY_OF_WEEK);
            calendar.set(Calendar.DAY_OF_WEEK, day);
            int format = Calendar.SHORT;
            if (Build.VERSION.SDK_INT > 26) {
                format = Calendar.NARROW_FORMAT;
            }
            return calendar.getDisplayName(Calendar.DAY_OF_WEEK, format, Locale.US);
        });

        calendarView.addDecorator(new TodayDecorator(getActivity()));

        calendarView.setOnDateChangedListener((widget, date, selected) -> {
            UpdateTimeRange();
            ReloadSchedules(oSearchBar.getText().toString());
        });

        calendarView.setOnMonthChangedListener((widget, date) -> {
            widget.clearSelection();
            UpdateTimeRange();
            ReloadSchedules(oSearchBar.getText().toString());
        });
    }

    @SuppressLint("NonConstantResourceId")
    private void UpdateTimeRange() {
        if (selectedTab == null) return;
        long start = 0, end = 0;
        Date today = new Date();
        SchedulesAdapter.Type type = SchedulesAdapter.Type.Today;
        switch (selectedTab.getId()) {
            case R.id.btn_schedule_today:
                type = SchedulesAdapter.Type.Today;
                start = DateUtils.startDate(today).getTime();
                end = DateUtils.endDate(today).getTime();
                break;
            case R.id.btn_schedule_weekly:
                type = SchedulesAdapter.Type.Weekly;
                int firstDayOfWeek = calendarView.getFirstDayOfWeek();
                if (calendarView.getSelectedDate() != null) {
                    Date day = calendarView.getSelectedDate().getDate();
                    start = DateUtils.startDate(day).getTime();
                    end = DateUtils.endDate(day).getTime();
                } else {
                    Date day = calendarView.getCurrentDate().getDate();
                    start = DateUtils.startDateOfWeek(day, firstDayOfWeek).getTime();
                    end = DateUtils.endDateOfWeek(day, firstDayOfWeek).getTime();
                }
                break;
            case R.id.btn_schedule_monthly:
                type = SchedulesAdapter.Type.Monthly;
                if (calendarView.getSelectedDate() != null) {
                    Date day = calendarView.getSelectedDate().getDate();
                    start = DateUtils.startDate(day).getTime();
                    end = DateUtils.endDate(day).getTime();
                } else {
                    Date day = calendarView.getCurrentDate().getDate();
                    start = DateUtils.startDateOfMonth(day).getTime();
                    end = DateUtils.endDateOfMonth(day).getTime();
                }
                break;
            case R.id.btn_schedule_upcoming:
                type = SchedulesAdapter.Type.Upcoming;
                start = DateUtils.startDate(today).getTime();
                end = DateUtils.endDate(DateUtils.addMonths(today, UPCOMING_MONTHS)).getTime();
                break;
            case R.id.btn_schedule_overdue:
                type = SchedulesAdapter.Type.Overdue;
                start = GetEarliestDateTime();
                end = DateUtils.startDate(today).getTime();
                break;
        }

        mTimeRange.start = start;
        mTimeRange.end = end;

        this.scheduleType = type;
    }

    private boolean isCalendarVisible() {
        if (selectedTab == null) return false;
        return selectedTab.getId() == R.id.btn_schedule_weekly ||
                selectedTab.getId() == R.id.btn_schedule_monthly;
    }

    private long GetEarliestDateTime() {
        if (mEarliestDateTime < 0) {
            v_Schedule earliest = CommonDB_Schedule.GetTheEarliestSchedule();
            if (earliest != null) {
                mEarliestDateTime = DateUtils.startDate(new Date(earliest.iUnixTime)).getTime();
            }
        }
        return mEarliestDateTime;
    }

    private List<v_Schedule> GetRecurSchedules(final String searchTerm) {
        List<v_Schedule> result = new ArrayList<>();
        if (mTimeRange.isValid()) {
            Date start = new Date(mTimeRange.start), end = new Date(mTimeRange.end);
            try {
                List<v_Schedule> recurSchedules = CommonDB_Schedule.SearchRecurSchedules(searchTerm);
                for (v_Schedule schedule: recurSchedules) {
                    if (StringUtils.isEmpty(schedule.sRRule) || schedule.iUnixTime > end.getTime())
                        continue;

                    boolean startsWithRRULE = schedule.sRRule.startsWith(PREFIX_R_RULE);
                    String ruleString = startsWithRRULE ?
                            schedule.sRRule.substring(PREFIX_R_RULE.length()) : schedule.sRRule;
                    RecurrenceRule rule = new RecurrenceRule(ruleString);
                    RecurrenceRuleIterator iterator = rule.iterator(schedule.iUnixTime, TimeZone.getDefault());
                    long startTime = Math.max(start.getTime(), schedule.iUnixTime);
                    while (iterator.hasNext()) {
                        long nextDateTime = iterator.nextDateTime().getTimestamp();
                        if (nextDateTime > end.getTime()) break;
                        if (nextDateTime > startTime) {
                            v_Schedule copied = schedule.copy();
                            copied.bRecurred = true;
                            copied.iUnixTime = nextDateTime;
                            result.add(copied);
                        }
                    }
                }
            } catch (Exception ex) {
                ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.GetRecurSchedules", ex, getActivity());
            }
        }
        return result;
    }

    private List<v_Schedule> GetSchedules(final String searchTerm) {
        List<v_Schedule> result = new ArrayList<>();
        if (mTimeRange.isValid()) {
            Date start = new Date(mTimeRange.start);
            Date end = new Date(mTimeRange.end);
            List<v_Schedule> schedules = CommonDB_Schedule.SearchSchedules(searchTerm, start, end);
            result.addAll(schedules);
        }

        return result;
    }

    private List<ScheduleSectionModel> GetScheduleSectionModels(List<v_Schedule> schedules) {
        List<ScheduleSectionModel> result = new ArrayList<>();
        Collections.sort(schedules, (o1, o2) -> Long.compare(o1.iUnixTime, o2.iUnixTime));

        HashMap<Date, List<v_Schedule>> map = new HashMap<>();
        for (int i = 0; i < schedules.size(); i++) {
            v_Schedule schedule = schedules.get(i);
            Date day = DateUtils.startDate(new Date(schedule.iUnixTime));
            List<v_Schedule> v_schedules = map.containsKey(day)
                    ? new ArrayList<>(map.get(day)) : new ArrayList<>();
            v_schedules.add(schedule);
            map.put(day, v_schedules);
        }

        Date[] allKeys = map.keySet().toArray(new Date[0]);
        Arrays.sort(allKeys, Date::compareTo);

        for (Date date : allKeys) {
            List<v_Schedule> items = map.get(date);
            int numOfCompleted = 0;
            for (v_Schedule item: items) {
                if (!item.bRecurred && item.isInsCompleted()) numOfCompleted ++;
            }

            ScheduleSectionModel sectionModel = new ScheduleSectionModel();
            sectionModel.day = date;
            sectionModel.schedules = items;
            sectionModel.title = DateUtils.formatAsScheduleTitle(date);
            sectionModel.subtitle = numOfCompleted + "/" + items.size();
            result.add(sectionModel);
        }

        return result;
    }

    private void ReloadSchedules(final String searchTerm) {
        try {
            new Thread(() -> {
                List<v_Schedule> lsRecurSchedules = GetRecurSchedules(searchTerm);
                List<v_Schedule> lsSchedules = GetSchedules(searchTerm);
                lsSchedules.addAll(lsRecurSchedules);

                sectionModels = GetScheduleSectionModels(lsSchedules);
                final Activity activity = getActivity();
                if (activity == null) return;

                final SchedulesAdapter adapter = new SchedulesAdapter(activity, sectionModels, scheduleType);
                activity.runOnUiThread(() -> {
                    oListView.setAdapter(adapter);
                    adapter.notifyDataSetChanged();
                    boolean hasData = sectionModels.size() > 0;
                    emptyView.setVisibility(hasData ? View.GONE : View.VISIBLE);
                    oListView.setVisibility(hasData ? View.VISIBLE : View.GONE);
                    if (isCalendarVisible() && calendarView.getSelectedDate() == null) {
                        new PastTodayEventDotTask(frag_NewSchedules.this, mTimeRange)
                                .executeOnExecutor(Executors.newSingleThreadExecutor());
                        new FutureEventDotTask(frag_NewSchedules.this, mTimeRange)
                                .executeOnExecutor(Executors.newSingleThreadExecutor());
                    }
                    tvTodaySubtitle.setText(hasData ? sectionModels.get(0).subtitle : "0/0");
                });
            }).start();
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.ReloadSchedules", ex, getActivity());
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
    }

    @Override
    public void onPause() {
        super.onPause();
    }

    @Override
    public void onResume(){
        super.onResume();
        try {
            final SchedulesAdapter adapter = (SchedulesAdapter)oListView.getListView().getAdapter();
            if (adapter != null) {
                new Thread(() -> {
                    String searchTerm = oSearchBar.getText().toString();
                    List<v_Schedule> lsRecurSchedules = GetRecurSchedules(searchTerm);
                    List<v_Schedule> lsSchedules = GetSchedules(searchTerm);
                    lsSchedules.addAll(lsRecurSchedules);
                    sectionModels = GetScheduleSectionModels(lsSchedules);

                    Activity activity = getActivity();
                    if (activity == null) return;
                    activity.runOnUiThread(() -> {
                        adapter.mSectionModels = sectionModels;
                        adapter.notifyDataSetChanged();
                        boolean hasData = sectionModels.size() > 0;
                        tvTodaySubtitle.setText(hasData ? sectionModels.get(0).subtitle : "0/0");
                    });
                }).start();
            }
        } catch(Exception ex) {
            ai_BugHandler.ai_Handler_Exception("Exception", "frag_schedules.onResume", ex, getActivity());
        }

       // CommonHelper.trackEvent(this.getActivity(), "Android View Schedules Resume", null);
        CommonDB.InsertLog(getActivity(), "Event", "Schedule List View");
    }

    private void ShowAlert(final String sTitle, String sMessage) {
        CommonUI.ShowAlert(getActivity(), sTitle, sMessage);
    }

    @SuppressLint("NonConstantResourceId")
    private void UpdateViewsWithSelected(View selected) {
        // Calendar view
        CalendarMode calendarMode = null;
        todayHeaderView.setVisibility(View.GONE);
        boolean displaySearchBar = false;
        int emptyText = 0;
        switch (selected.getId()) {
            case R.id.btn_schedule_today:
                tvTodayTitle.setText(DateUtils.formatAsScheduleTitle(new Date()));
                todayHeaderView.setVisibility(View.VISIBLE);
                emptyText = R.string.schedule_empty_text_today;
                break;
            case R.id.btn_schedule_overdue:
                emptyText = R.string.schedule_empty_text_overdue;
                displaySearchBar = true;
                break;
            case R.id.btn_schedule_upcoming:
                emptyText = R.string.schedule_empty_text_upcoming;
                displaySearchBar = true;
                break;
            case R.id.btn_schedule_weekly:
                calendarMode = CalendarMode.WEEKS;
                emptyText = R.string.schedule_empty_text_weekly;
                break;
            case R.id.btn_schedule_monthly:
                calendarMode = CalendarMode.MONTHS;
                emptyText = R.string.schedule_empty_text_monthly;
                break;
        }

        if (emptyText != 0) { tvEmpty.setText(emptyText); }

        if (calendarMode != null) {
            calendarView.setVisibility(View.VISIBLE);
            MaterialCalendarView.StateBuilder state = calendarView.state().edit();
            state.setCalendarDisplayMode(calendarMode);
            state.commit();
        } else {
            calendarView.setVisibility(View.GONE);
        }

        /// button title color
        for (Button element : btnTabs) {
            if (element.getId() == R.id.btn_schedule_overdue) break;
            final int color = getResources().getColor(
                    element.getId() == selected.getId() ? R.color.comments_tab_selected : R.color.comments_tab_normal
            );
            element.setTextColor(color);
        }

        /// bottom line
        Point centerOfBtn = new Point((int) (selected.getWidth() * 0.5), 0);
        Point targetPos = ViewUtils.convertPoint(centerOfBtn, selected, oSchedulesTab);
        float posX = (float) (targetPos.x - vBottomLine.getWidth() * 0.5);
        int background = R.id.btn_schedule_overdue == selected.getId() ?
                R.color.schedules_tab_overdue : R.color.comments_tab_selected;
        vBottomLine.setBackgroundResource(background);
        vBottomLine.animate().setDuration(100).x(posX);

        /// Search Bar
        oSearchBar.setVisibility(displaySearchBar ? View.VISIBLE : View.GONE);
    }

    private static class PastTodayEventDotTask extends AsyncTask<Void, Void, List<CalendarDay>> {

        private final TimeRange mTimeRange;
        private final CalendarDay today = CalendarDay.today();

        private final WeakReference<frag_NewSchedules> weakReference;
        PastTodayEventDotTask(frag_NewSchedules newSchedules, TimeRange timeRange) {
            weakReference = new WeakReference<>(newSchedules);
            this.mTimeRange = timeRange;
        }

        @Override
        protected List<CalendarDay> doInBackground(@NonNull Void... voids) {
            frag_NewSchedules schedules = weakReference.get();
            if (schedules == null) return null;

            final ArrayList<CalendarDay> dates = new ArrayList<>();
            for (ScheduleSectionModel sectionModel: schedules.sectionModels) {
                CalendarDay date = CalendarDay.from(sectionModel.day.getTime());
                if (date.isInRange(CalendarDay.from(mTimeRange.start), today)) {
                    dates.add(date);
                }
            }
            return dates;
        }

        @Override
        protected void onPostExecute(@NonNull List<CalendarDay> calendarDays) {
            super.onPostExecute(calendarDays);
            frag_NewSchedules schedules = weakReference.get();
            if (schedules == null || schedules.getActivity() == null) return;
            schedules.calendarView.addDecorator(new EventDecorator(
                    schedules.getActivity().getResources().getColor(R.color.schedules_tab_overdue), calendarDays
            ));
        }
    }

    private static class FutureEventDotTask extends AsyncTask<Void, Void, List<CalendarDay>> {

        private final TimeRange mTimeRange;
        private final CalendarDay today = CalendarDay.today();

        private final WeakReference<frag_NewSchedules> weakReference;
        FutureEventDotTask(frag_NewSchedules newSchedules, TimeRange timeRange) {
            this.mTimeRange = timeRange;
            weakReference = new WeakReference<>(newSchedules);
        }

        @Override
        protected List<CalendarDay> doInBackground(@NonNull Void... voids) {
            frag_NewSchedules schedules = weakReference.get();
            if (schedules == null) return null;

            final ArrayList<CalendarDay> dates = new ArrayList<>();
            for (ScheduleSectionModel sectionModel: schedules.sectionModels) {
                CalendarDay date = CalendarDay.from(sectionModel.day.getTime());
                if (date.isInRange(today, CalendarDay.from(mTimeRange.end))) {
                    dates.add(date);
                }
            }
            return dates;
        }

        @Override
        protected void onPostExecute(@NonNull List<CalendarDay> calendarDays) {
            super.onPostExecute(calendarDays);
            frag_NewSchedules schedules = weakReference.get();
            if (schedules == null || schedules.getActivity() == null) return;

            schedules.calendarView.addDecorator(new EventDecorator(
                    schedules.getActivity().getResources().getColor(R.color.calendar_tile_selected), calendarDays
            ));
        }
    }

    private static class EventDecorator implements DayViewDecorator {
        private final int color;
        private final HashSet<CalendarDay> dates;

        public EventDecorator(int color, Collection<CalendarDay> dates) {
            this.color = color;
            this.dates = new HashSet<>(dates);
        }

        @Override
        public boolean shouldDecorate(CalendarDay day) {
            return dates.contains(day);
        }

        @Override
        public void decorate(DayViewFacade view) {
            view.addSpan(new DotSpan(5, color));
        }
    }

    private static class TodayDecorator implements DayViewDecorator {

        private final Drawable drawable;
        private final CalendarDay today = CalendarDay.today();
        public TodayDecorator(Activity context) {
            drawable = context.getResources().getDrawable(R.drawable.bg_calendar_today);
        }

        @Override
        public boolean shouldDecorate(final CalendarDay day) {
            return day.equals(today) ;
        }

        @Override
        public void decorate(final DayViewFacade view) {
            view.setBackgroundDrawable(drawable);
        }
    }

    /// Display the inspection for the external schedule
    public void displayExternalSchedule(int iSScheduleID) {
        final v_Schedule vSchedule = CommonDB_Schedule.GetVScheduleBySScheduleID(iSScheduleID);
        if (vSchedule != null) CommonSchedule.startSchedule(getActivity(), vSchedule);
    }
}