package com.snapinspect.snapinspect3.app;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.WindowManager;
import androidx.multidex.MultiDexApplication;
import com.datadog.android.Datadog;
import com.datadog.android.core.configuration.BatchProcessingLevel;
import com.datadog.android.core.configuration.BatchSize;
import com.datadog.android.core.configuration.Configuration;
import com.datadog.android.core.configuration.UploadFrequency;
import com.datadog.android.log.Logger;
import com.datadog.android.log.Logs;
import com.datadog.android.log.LogsConfiguration;
import com.datadog.android.privacy.TrackingConsent;
import com.google.firebase.FirebaseApp;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.database.RoomDatabaseManager;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.Obserble.NotificationCenter;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Osama on 7/18/2016.
 */
public class App extends MultiDexApplication {

    private static float oDensity = 1;
    private NotificationCenter notObj;
    private ActivityLifecycleLogger lifecycleLogger;

    public Logger dataDogLogger;

    @SuppressLint("StaticFieldLeak")
    private static Context mContext = null;
    public static Context getContext() {
        return mContext;
    }

    @Override
    public void onCreate() {
        super.onCreate();
        mContext = getApplicationContext();
        // Initialize Room Database
        RoomDatabaseManager.getInstance(this);
        DisplayMetrics oMetrics = new DisplayMetrics();
        ((WindowManager) getApplicationContext().getSystemService(Context.WINDOW_SERVICE))
                .getDefaultDisplay().getMetrics(oMetrics);

        oDensity = oMetrics.density;
        notObj = new NotificationCenter();

        lifecycleLogger = ActivityLifecycleLogger.shared;
        registerActivityLifecycleCallbacks(lifecycleLogger);

        // Initialize DataDog
        initializeDataDog();

        // Initialize Firebase
        FirebaseApp.initializeApp(this);

        // Initialize Intercom
        CommonHelper.IntercomInit(this);
    }

    @Override
    public void onTerminate() {
        super.onTerminate();
        // Terminate Room database
        RoomDatabaseManager.getInstance(this).getDatabase().close();
        unregisterActivityLifecycleCallbacks(lifecycleLogger);
    }

    public float GetDeviceDensity(){
        return oDensity;
    }

    public NotificationCenter getObserver() {
        return notObj;
    }

    private void initializeDataDog() {
        Configuration configuration =
                new Configuration.Builder(
                    Constants.Tokens.dataDog,
                    "production",
                    "SnapInspect-Android"
                )
                .setBatchSize(BatchSize.LARGE)
                .setUploadFrequency(UploadFrequency.RARE)
                .setBatchProcessingLevel(BatchProcessingLevel.HIGH)
                .build();
        Datadog.initialize(this, configuration, TrackingConsent.GRANTED);

        LogsConfiguration logsConfig = new LogsConfiguration.Builder().build();
        Logs.enable(logsConfig);

        dataDogLogger = new Logger.Builder()
                .setNetworkInfoEnabled(true)
                .setLogcatLogsEnabled(true)
                .setBundleWithTraceEnabled(true)
                .setService("SnapInspect-Android")
                .setName("SnapInspect")
                .build();
    }
}

class ActivityLifecycleLogger implements Application.ActivityLifecycleCallbacks {

    public static ActivityLifecycleLogger shared = new ActivityLifecycleLogger();

    private final HashMap<String, Date> timeElapsedMap = new HashMap<>();

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
    }

    @Override
    public void onActivityStarted(Activity activity) {
    }

    @Override
    public void onActivityResumed(Activity activity) {
        String key = activity.getClass().getName();
        timeElapsedMap.put(key, new Date()); // milliseconds
        CommonHelper.trackEvent(activity, String.format("Enter `%s`", getEventName(activity)), null);
    }

    @SuppressLint("DefaultLocale")
    @Override
    public void onActivityPaused(Activity activity) {
        try {
            String key = activity.getClass().getName();
            long cost = new Date().getTime() - timeElapsedMap.get(key).getTime();
            Map<String, Object> meta = new HashMap<>();
            meta.put("Time_elapsed", String.format("%.02fs", cost / 1000.0));
            CommonHelper.trackEvent(activity, String.format("Leave `%s`", getEventName(activity)), meta);
            timeElapsedMap.remove(key);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    @SuppressLint("DefaultLocale")
    @Override
    public void onActivityStopped(Activity activity) {
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {
    }

    public void onActivityDestroyed(Activity activity) {
    }

    private String getEventName(Activity activity) {
        String event = "";
        try {
            event += (String) activity.getActionBar().getTitle();
        } catch (Exception ex) {
            //
        }
        String className = activity.getClass().getSimpleName();
        return StringUtils.isEmpty(event) ? className : event + " - " + className;
    }
}