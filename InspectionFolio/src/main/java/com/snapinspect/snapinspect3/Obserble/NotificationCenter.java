package com.snapinspect.snapinspect3.Obserble;

import java.util.Observable;

/**
 * Created by <PERSON><PERSON><PERSON> on 12/4/16.
 */
public class NotificationCenter extends Observable {
    private String name;

    /**
     * @return the value
     */
    public String getValue() {
        return name;
    }


    public void setValue(String name) {
        this.name = name;
        setChanged();
        notifyObservers();
    }
}
