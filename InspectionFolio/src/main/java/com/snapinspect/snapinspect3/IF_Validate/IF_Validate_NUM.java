package com.snapinspect.snapinspect3.IF_Validate;

import android.text.Editable;
import android.text.TextWatcher;
import android.widget.TextView;

/**
 * Created by terrysun1 on 13/01/16.
 */


    public abstract class IF_Validate_NUM implements TextWatcher {
        private final TextView textView;

        public IF_Validate_NUM(TextView textView) {
            this.textView = textView;
        }

        public abstract void validate(TextView textView, String text);

        @Override
        final public void afterTextChanged(Editable s) {
            String text = textView.getText().toString();
            validate(textView, text);
        }

        @Override
        final public void beforeTextChanged(CharSequence s, int start, int count, int after) { /* Don't care */ }

        @Override
        final public void onTextChanged(CharSequence s, int start, int before, int count) { /* Don't care */ }
    }

