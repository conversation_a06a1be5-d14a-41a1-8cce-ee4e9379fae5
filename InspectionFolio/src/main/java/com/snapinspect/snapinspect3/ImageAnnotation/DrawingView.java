package com.snapinspect.snapinspect3.ImageAnnotation;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Path;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.Typeface;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.View;

import java.util.ArrayList;

/**
 * Created by crane on 1/20/16.
 */

import java.lang.Math;

import com.snapinspect.snapinspect3.activity.if_ImageAnnotation;
public class DrawingView extends View implements View.OnTouchListener {

    private Canvas m_Canvas;

    private Path m_Path;

    private Paint m_Paint;
    private Paint m_TextPaint;
    private Paint m_FillPaint;

    private final ArrayList<Shapes> shapesArrayList = new ArrayList<Shapes>();
    private final ArrayList<Shapes> undoShapesArray = new ArrayList<Shapes>();

    private float mX, mY;
    private float x, x1;
    private float y, y1;
    private float lastPX, lastPY;
    private float offsetX = 0, offsetY = 0, newoffsetX = 0, newoffsetY = 0;
    private String action;
    private String inputString = "";
    private String bufStr;
    private static final float TOUCH_TOLERANCE = 4;
    private static final int DEFAULT_MARGIN = 10;
    private boolean isTouchUp = false, showEditView = false;
    if_ImageAnnotation mainActivity;

    private boolean isBold = false;
    private boolean isItalic = false;
    private boolean isUnder = false;
    private boolean isTrash = false;
    private int mLineColor = Color.RED;
    private int mFillColor = Color.RED;
    private boolean isFill = false;
    private float mLineWidth = 10.f;
    private final boolean goneTextRect = false;
    private final boolean saved = false;

    public DrawingView(Context context, AttributeSet attr) {
        super(context, attr);

        setFocusable(true);

        setFocusableInTouchMode(true);

        this.setOnTouchListener(this);
        mainActivity = (if_ImageAnnotation)context;

        onCanvasInitialization();
    }

    public void onCanvasInitialization() {
        m_Paint = new Paint();
        m_Paint.setAntiAlias(true);
        m_Paint.setDither(true);
        m_Paint.setColor(mLineColor);
        m_Paint.setStyle(Paint.Style.STROKE);
        m_Paint.setStrokeWidth(10);

        m_Canvas = new Canvas();


        m_Path = new Path();
        Paint newPaint = new Paint(m_Paint);
    }
    public Paint getDrawPaint (Paint oldPaint) {
        Paint paint = new Paint(oldPaint);
        paint.setColor(mLineColor);
        paint.setStrokeWidth(mLineWidth);

        return paint;
    }
    public Paint getFillPaint() {
        Paint paint = new Paint();

        paint.setStyle(Paint.Style.FILL);
        paint.setColor(mFillColor);

        return paint;
    }

    public Paint getFillPaint(Paint oldPaint) {
        Paint paint = new Paint(oldPaint);

        paint.setStyle(Paint.Style.FILL);
        paint.setColor(mFillColor);

        return paint;
    }
    public Paint getDrawTextPaint() {
        Paint paint = new Paint(Paint.ANTI_ALIAS_FLAG);
        paint.setColor(mLineColor);
        paint.setTextSize(50);
        paint.setStyle(Paint.Style.FILL);
        return paint;
    }

    public Paint getDrawTextPaint (Paint oldPaint) {
        Paint paint = new Paint(oldPaint);
        paint.setColor(mLineColor);
        paint.setTextSize(oldPaint.getTextSize());
        paint.setStyle(Paint.Style.FILL);
        return paint;
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
    }

    public boolean onTouch(View arg0, MotionEvent event) {
        switch (event.getAction()) {
            case MotionEvent.ACTION_DOWN:
            case MotionEvent.ACTION_POINTER_DOWN:
                x = x1 = event.getX();
                y = y1 = event.getY();
                touch_start(x, y);
                isTouchUp = false;
                invalidate();
                break;
            case MotionEvent.ACTION_MOVE:
                x1 = event.getX();
                y1 = event.getY();

                touch_move(x1, y1);
                invalidate();
                break;
            case MotionEvent.ACTION_UP:
            case MotionEvent.ACTION_POINTER_UP:
                lastPX = x1 = event.getX();
                lastPY = y1 = event.getY();
                isTouchUp = true;
                if (action.compareTo("Pen") == 0) {
                    Pen pen_obj = new Pen (m_Path, m_Paint);
                    Shapes shapes = new Shapes();
                    shapes.setPen(pen_obj);
                    shapesArrayList.add(shapes);
                }
                if (action.compareTo("Oval") == 0) {
                    Circle circle_obj = new Circle(x, y, x1, y1, m_Paint, m_FillPaint, isFill);
                    Shapes shapes = new Shapes();
                    shapes.setCircle(circle_obj);
                    shapesArrayList.add(shapes);
                }
                if (action.compareTo("Rect") == 0)
                {
                    Rectangle rect_obj = new Rectangle(x,y,x1,y1, m_Paint, m_FillPaint, isFill);
                    Shapes shapes = new Shapes();
                    shapes.setRect(rect_obj);
                    shapesArrayList.add(shapes);
                }
                if (action.compareTo("Line") == 0)
                {
                    Line line_obj = new Line(x,y,x1,y1, m_Paint);
                    Shapes shapes = new Shapes();
                    shapes.setLine(line_obj);
                    shapesArrayList.add(shapes);
                }
                if (action.compareTo("Arrow") == 0)
                {
                    Arrow arrow_obj = new Arrow(x,y,x1,y1, m_Paint);
                    Shapes shapes = new Shapes();
                    shapes.setArrow(arrow_obj);
                    shapesArrayList.add(shapes);
                }

                touch_up();
                invalidate();
                break;
        }
        return true;
    }

    public float minV (float a, float b) {
        return a < b ? a : b;
    }

    public float maxV (float a, float b) {
        return a > b ? a : b;
    }

    public void drawArrow (Canvas canvas, float a, float b, float a1, float b1, Paint paint) {
        double phi = Math.atan2(b1 - b, a1 - a);

        double tip1angle = phi - Math.PI / 4; // -45°
        double tip2angle = phi + Math.PI / 4; // +45°
        double h = paint.getStrokeWidth() * 3;
        double offH = paint.getStrokeWidth() / 2;
        float x2 = (float)(a1 - h * Math.cos(tip1angle)); // substitute h here and for the following 3 places
        float x3 = (float)(a1 - h * Math.cos(tip2angle));
        float y2 = (float)(b1 - h * Math.sin(tip1angle));
        float y3 = (float)(b1 - h * Math.sin(tip2angle));


        float x12 = (float)(a1 + offH * Math.cos(tip1angle)); // substitute h here and for the following 3 places
        float x13 = (float)(a1 + offH * Math.cos(tip2angle));
        float y12 = (float)(b1 + offH * Math.sin(tip1angle));
        float y13 = (float)(b1 + offH * Math.sin(tip2angle));
        canvas.drawLine(x12, y12, x2, y2, paint);
        canvas.drawLine(x13, y13, x3, y3, paint);
    }

    public void drawText (Canvas canvas, float a, float b, String captionString, Paint paint, Boolean isRect) {
        canvas.drawText(captionString, a, b, paint);
        if (isRect) {
            Rect rectText = new Rect();
            Paint rectPaint = new Paint(paint);
            rectPaint.setStyle(Paint.Style.STROKE);
            rectPaint.getTextBounds(captionString, 0, captionString.length(), rectText);

            Paint.FontMetrics fm = new Paint.FontMetrics();
            rectPaint.getFontMetrics(fm);

            canvas.drawRect(a - DEFAULT_MARGIN, b - DEFAULT_MARGIN + fm.top,
                    a + paint.measureText(captionString) + DEFAULT_MARGIN, b + fm.bottom
                            + DEFAULT_MARGIN, rectPaint);
        }
    }

    public boolean isBound(Paint paint, String text, float a, float b, float pointX, float pointY) {
        boolean ret = false;
        float firstX, firstY, lastX, lastY;

        if (paint == null) return ret;

        Rect rectText = new Rect();

        paint.getTextBounds(text, 0, text.length(), rectText);
        Paint.FontMetrics fm = new Paint.FontMetrics();
        paint.getFontMetrics(fm);

        firstX = a - DEFAULT_MARGIN;
        firstY = b - DEFAULT_MARGIN + fm.top;
        lastX = a + paint.measureText(text) + DEFAULT_MARGIN;
        lastY = b + fm.bottom + DEFAULT_MARGIN;
        if ((firstX < pointX && pointX < lastX) && (firstY < pointY && pointY < lastY)) {
            ret = true;
        }

        return ret;
    }

    public Paint getPaintFromTextArray (float a, float b) {
        Paint paint = null;
        for (Shapes shape : shapesArrayList) {
            DrawText draw_obj;

            if (null == (draw_obj = shape.getDrawText())) continue;
            if (isBound(draw_obj.paint, draw_obj.textStr, draw_obj.stopX, draw_obj.stopY, a, b)) {
                mX -= (newoffsetX = a - draw_obj.stopX);
                mY -= (newoffsetY = b - draw_obj.stopY);
                paint = getDrawTextPaint(draw_obj.paint);
                bufStr = draw_obj.textStr;

                shapesArrayList.remove(shape);
                break;
            }
        }

        return paint;
    }

    @Override
    protected void onDraw(Canvas canvas) {
        /*Old shape Drawing*/
        for (Shapes shape: shapesArrayList)
        {
            if (shape.getPen() != null)
                canvas.drawPath (shape.getPen().path, shape.getPen().paint);

            if (shape.getCircle() != null) {
                Circle circle = shape.getCircle();
                RectF rect = new RectF(minV(circle.startX, circle.stopX),
                        minV(circle.startY, circle.stopY),
                        maxV(circle.startX, circle.stopX),
                        maxV(circle.startY, circle.stopY));

                canvas.drawOval(rect, circle.paint);
                if (circle.fill && circle.fillPaint != null) {
                    RectF rectfill = new RectF(minV(circle.startX + circle.paint.getStrokeWidth() / 2, circle.stopX + circle.paint.getStrokeWidth() / 2),
                            minV(circle.startY + circle.paint.getStrokeWidth() / 2, circle.stopY + circle.paint.getStrokeWidth() / 2),
                            maxV(circle.startX - circle.paint.getStrokeWidth() / 2, circle.stopX - circle.paint.getStrokeWidth() / 2),
                            maxV(circle.startY - circle.paint.getStrokeWidth() / 2, circle.stopY - circle.paint.getStrokeWidth() / 2));


                    canvas.drawOval(rectfill, circle.fillPaint);
                }
            }

            if (shape.getRect() != null) {
                Rectangle rect = shape.getRect();
                canvas.drawRect(minV(rect.startX, rect.stopX),
                        minV(rect.startY, rect.stopY),
                        maxV(rect.startX, rect.stopX),
                        maxV(rect.startY, rect.stopY), rect.paint);
                if (rect.fill && rect.fillPaint != null) {
                    canvas.drawRect(minV(rect.startX + rect.paint.getStrokeWidth() / 2, rect.stopX + rect.paint.getStrokeWidth() / 2),
                            minV(rect.startY + rect.paint.getStrokeWidth() / 2, rect.stopY + rect.paint.getStrokeWidth() / 2),
                            maxV(rect.startX - rect.paint.getStrokeWidth() / 2, rect.stopX - rect.paint.getStrokeWidth() / 2),
                            maxV(rect.startY - rect.paint.getStrokeWidth() / 2, rect.stopY - rect.paint.getStrokeWidth() / 2), rect.fillPaint);
                }
            }

            if (shape.getLine() != null)
                canvas.drawLine(shape.getLine().startX, shape.getLine().startY,
                        shape.getLine().stopX, shape.getLine().stopY, shape.getLine().paint);

            if (shape.getArrow() != null) {
                canvas.drawLine(shape.getArrow().startX, shape.getArrow().startY,
                        shape.getArrow().stopX, shape.getArrow().stopY, shape.getArrow().paint);
                this.drawArrow(canvas, shape.getArrow().startX, shape.getArrow().startY,
                        shape.getArrow().stopX, shape.getArrow().stopY,shape.getArrow().paint);
            }

            if (shape.getDrawText() != null) {
                this.drawText(canvas, shape.getDrawText().stopX, shape.getDrawText().stopY,
                        shape.getDrawText().textStr, shape.getDrawText().paint, false);
            }
        }

        /*Current shape Drawing*/
        if (action.compareTo("Pen") == 0 && !isTouchUp)
            canvas.drawPath(m_Path, m_Paint);

        if (action.compareTo("Oval") == 0 && !isTouchUp) {
            RectF rectf = new RectF(minV(x, x1), minV(y, y1), maxV(x, x1), maxV(y, y1));
            canvas.drawOval(rectf, m_Paint);
            if (isFill && m_FillPaint != null) {
                RectF rectfill = new RectF(minV(x, x1) + mLineWidth / 2, minV(y, y1) + mLineWidth / 2,
                        maxV(x, x1) - mLineWidth / 2, maxV(y, y1) - mLineWidth / 2);
                canvas.drawOval(rectfill, m_FillPaint);
            }
        }

        if (action.compareTo("Rect") == 0 && !isTouchUp) {
            canvas.drawRect(minV(x, x1), minV(y, y1), maxV(x, x1), maxV(y, y1), m_Paint);
            if (isFill && m_FillPaint != null) {
                canvas.drawRect(minV(x, x1) + mLineWidth / 2, minV(y, y1) + mLineWidth / 2,
                        maxV(x, x1) - mLineWidth / 2, maxV(y, y1) - mLineWidth / 2, m_FillPaint);
            }
        }

        if (action.compareTo("Line") == 0 && !isTouchUp)
            canvas.drawLine(x, y, x1, y1, m_Paint);

        if (action.compareTo("Arrow") == 0  && !isTouchUp) {
            canvas.drawLine(x, y, x1, y1, m_Paint);
            this.drawArrow(canvas, x, y, x1, y1, m_Paint);
        }

        if (action.compareTo("Text") == 0 && showEditView && !isTrash) {
            this.drawText(canvas, x1 - offsetX, y1 - offsetY, inputString ,m_TextPaint, true);
        }
    }

    private void touch_start(float x, float y) {
        m_Path.reset();
        m_Path.moveTo(x, y);
        mX = x;
        mY = y;

        if (action.compareTo("Text") == 0) {

            Paint newPaint;
            if (null != (newPaint = this.getPaintFromTextArray (x, y))) {

                if ((inputString.compareTo("") != 0) && m_TextPaint != null && !isTrash) {
                    DrawText text_obj = new DrawText(inputString, lastPX - offsetX, lastPY - offsetY, m_TextPaint);
                    Shapes shapes = new Shapes();
                    shapes.setDrawText(text_obj);
                    shapesArrayList.add(shapes);
                }
                if (!showEditView) {
                    mainActivity.textEditView.setVisibility(View.VISIBLE);
                    mainActivity.showKeyBoard();
                    showEditView = true;
                }
                offsetX = newoffsetX;
                offsetY = newoffsetY;
                inputString = bufStr;
                mainActivity.inputText.setText(bufStr);
                m_TextPaint = newPaint;

            }
            else if (isBound(m_TextPaint, inputString, lastPX, lastPY, x, y)) {
                if (!showEditView) {
                    mainActivity.textEditView.setVisibility(View.VISIBLE);
                    mainActivity.showKeyBoard();

                    showEditView = true;
                }
            }

            else {
                if ((inputString.compareTo("") != 0) && m_TextPaint != null && !isTrash) {
                    DrawText text_obj = new DrawText(inputString, lastPX - offsetX, lastPY - offsetY, m_TextPaint);
                    Shapes shapes = new Shapes();
                    shapes.setDrawText(text_obj);
                    shapesArrayList.add(shapes);
                    offsetX = 0;
                    offsetY = 0;
                    inputString = "";
                    mainActivity.inputText.setText("");
                }

                if (showEditView) {
                    mainActivity.textEditView.setVisibility(View.INVISIBLE);
                    showEditView = false;
                    if (mainActivity.sliderView.getVisibility() == View.VISIBLE)
                        mainActivity.sliderView.setVisibility(View.INVISIBLE);
                    mainActivity.hiddenKeyBoard();
                }
                else {
                    mainActivity.textEditView.setVisibility(View.VISIBLE);
                    showEditView = true;
                    mainActivity.showKeyBoard();

                    m_TextPaint = this.getDrawTextPaint();
                }
            }
            isTrash = false;
        }
    }

    private void touch_move(float x, float y) {
        float dx = Math.abs(x - mX);
        float dy = Math.abs(y - mY);
        if (dx >= TOUCH_TOLERANCE || dy >= TOUCH_TOLERANCE) {
            m_Path.quadTo(mX, mY, (x + mX) / 2, (y + mY) / 2);
            mX = x;
            mY = y;
            if (action.compareTo("Text") == 0) {
                mX = mX - offsetX;
                mY = mY - offsetY;
            }
        }
    }

    private void touch_up() {
        m_Path.lineTo(mX, mY);

        // commit the path to our offscreen
        m_Canvas.drawPath(m_Path, m_Paint);

        // kill this so we don't double draw
        m_Path = new Path();
    }

    public void reset()
    {
        shapesArrayList.clear();
        if (showEditView) {
            mainActivity.textEditView.setVisibility(View.INVISIBLE);
            showEditView = false;
            if (mainActivity.sliderView.getVisibility() == View.VISIBLE)
                mainActivity.sliderView.setVisibility(View.INVISIBLE);
            mainActivity.hiddenKeyBoard();
        }

        invalidate();
        mainActivity.inputText.setText("");
        m_TextPaint = null;
    }


    public void onClickUndo() {
        if (shapesArrayList.size() > 0) {
            undoShapesArray.add(shapesArrayList.remove(shapesArrayList.size() - 1));
            invalidate();
        } else {

        }
    }

    public void onClickRedo() {
        if (undoShapesArray.size() > 0) {
            shapesArrayList.add(undoShapesArray.remove(undoShapesArray.size() - 1));
            invalidate();
        } else {

        }
    }

    public void setAction (String act) {
        action = act;
    }

    public void setLabelText (String str) {
        inputString = str;

        invalidate();
    }

    public void setBoldText () {
        isBold = !isBold;

        if (isBold && isItalic) {
            m_TextPaint.setTypeface(Typeface.create(Typeface.DEFAULT, Typeface.BOLD_ITALIC));
        } else if (isBold && !isItalic) {
            m_TextPaint.setTypeface(Typeface.create(Typeface.DEFAULT, Typeface.BOLD));
        }else if (isItalic && !isBold) {
            m_TextPaint.setTypeface(Typeface.create(Typeface.DEFAULT, Typeface.ITALIC));
        }else if (!isItalic && !isBold) {
            m_TextPaint.setTypeface(Typeface.create(Typeface.DEFAULT, Typeface.NORMAL));
        }

        invalidate();
    }

    public void setItalicText () {
        isItalic = !isItalic;
        if (isBold && isItalic) {
            m_TextPaint.setTypeface(Typeface.create(Typeface.DEFAULT, Typeface.BOLD_ITALIC));
        } else if (isItalic && !isBold) {
            m_TextPaint.setTypeface(Typeface.create(Typeface.DEFAULT, Typeface.ITALIC));
        } else if (isBold && !isItalic) {
            m_TextPaint.setTypeface(Typeface.create(Typeface.DEFAULT, Typeface.BOLD));
        } else if (!isItalic && !isBold) {
            m_TextPaint.setTypeface(Typeface.create(Typeface.DEFAULT, Typeface.NORMAL));
        }

        invalidate();
    }

    public void setUnderLineText() {
        isUnder = !isUnder;
        if (isUnder) {
            m_TextPaint.setFlags(Paint.UNDERLINE_TEXT_FLAG);
        } else {
            m_TextPaint.setFlags(Paint.ANTI_ALIAS_FLAG);

        }
        invalidate();
    }

    public void setTextSize (int progress){
        m_TextPaint.setTextSize((float) progress);
        invalidate();
    }

    public void trash(){
        isTrash = true;
        inputString = "";
        mainActivity.inputText.setText("");

        invalidate();
    }

    public void setLineColor(int color) {
        mLineColor = color;

        m_Paint = getDrawPaint(m_Paint);
        if (m_TextPaint != null)
            m_TextPaint = getDrawTextPaint(m_TextPaint);
    }
    public void setLineWidth (float width){
        mLineWidth = width;

        m_Paint = getDrawPaint(m_Paint);
    }

    public void setFillColor (int color, boolean is_fill) {
        isFill = is_fill;
        if (is_fill) {
            mFillColor = color;

            if (m_FillPaint != null)
                m_FillPaint = getFillPaint(m_FillPaint);
            else
                m_FillPaint = getFillPaint();
        }
    }
    public void saveAnnotation (){
        if ((inputString.compareTo("") != 0) && m_TextPaint != null && !isTrash) {
            DrawText text_obj = new DrawText(inputString, lastPX - offsetX, lastPY - offsetY, m_TextPaint);
            Shapes shapes = new Shapes();
            shapes.setDrawText(text_obj);
            shapesArrayList.add(shapes);
            offsetX = 0;
            offsetY = 0;
            inputString = "";
            mainActivity.inputText.setText("");
        }
        if (showEditView) {
            mainActivity.textEditView.setVisibility(View.INVISIBLE);
            showEditView = false;
            if (mainActivity.sliderView.getVisibility() == View.VISIBLE)
                mainActivity.sliderView.setVisibility(View.INVISIBLE);
            mainActivity.hiddenKeyBoard();
        }
        invalidate();
    }
}

class Line extends Shapes
{
    float startX, startY, stopX, stopY;
    Paint paint;
    public Line(float startX, float startY, float stopX, float stopY, Paint paint)
    {
        this.startX = startX;
        this.startY = startY;
        this.stopX = stopX;
        this.stopY = stopY;
        this.paint = paint;
    }
}

class Arrow extends Shapes
{
    float startX, startY, stopX, stopY;
    Paint paint;

    public Arrow(float startX, float startY, float stopX, float stopY, Paint paint)
    {
        this.paint = paint;
        this.startX = startX;
        this.startY = startY;
        this.stopX = stopX;
        this.stopY = stopY;
    }
}

class DrawText extends Shapes
{
    float stopX, stopY;
    String textStr;
    Paint paint;

    public DrawText(String str,float stopX, float stopY, Paint paint)
    {
        this.paint = paint;
        this.textStr = str;
        this.stopX = stopX;
        this.stopY = stopY;
    }
}

class Rectangle extends Shapes
{
    float startX, startY, stopX, stopY;
    Paint paint, fillPaint;
    boolean fill;
    public Rectangle(float startX, float startY, float stopX, float stopY, Paint paint, Paint fillPaint, boolean isFill)
    {
        //added comments
        this.paint = paint;
        this.startX = startX;
        this.startY = startY;
        this.stopX = stopX;
        this.stopY = stopY;
        this.fillPaint = fillPaint;
        this.fill = isFill;
    }
}
class Circle extends Shapes
{
    float startX, startY, stopX, stopY;
    Paint paint, fillPaint;
    boolean fill;
    public Circle(float startX, float startY, float stopX, float stopY, Paint paint, Paint fillPaint, boolean isFill) {
        this.paint = paint;
        this.startX = startX;
        this.startY = startY;
        this.stopX = stopX;
        this.stopY = stopY;
        this.fillPaint = fillPaint;
        this.fill = isFill;
    }
}
class Pen extends Shapes
{
    Path path;
    Paint paint;
    public Pen (Path path, Paint paint) {
        this.path = path;
        this.paint = paint;
    }
}

class Shapes
{
    Circle circle;
    Rectangle rect;
    Line line;
    Pen pen;
    Arrow arrow;
    DrawText dtext;

    public Pen getPen() {
        return pen;
    }

    public void setPen(Pen pen) {
        this.pen = pen;
    }

    public Circle getCircle() {
        return circle;
    }

    public void setCircle(Circle circle) {
        this.circle = circle;
    }

    public Rectangle getRect() {
        return rect;
    }

    public void setRect(Rectangle rect) {
        this.rect = rect;
    }

    public Line getLine() {
        return line;
    }

    public void setLine(Line line) {
        this.line = line;
    }

    public Arrow getArrow() {
        return arrow;
    }

    public void setArrow(Arrow arrow) {
        this.arrow = arrow;
    }

    public DrawText getDrawText() {
        return dtext;
    }

    public void setDrawText(DrawText dtext) {
        this.dtext = dtext;
    }
}