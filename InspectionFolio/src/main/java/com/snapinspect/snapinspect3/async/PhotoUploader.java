package com.snapinspect.snapinspect3.async;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.AsyncTask;
import android.widget.Toast;
import androidx.annotation.Nullable;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.IF_SyncClient;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_File;
import com.snapinspect.snapinspect3.util.BitmapUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONObject;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR> 03.02.18
 */
public class PhotoUploader extends AsyncTask<Uri, PhotoUploader.Progress, Boolean> {

    public static class Progress {
        public final Uri photoUri;
        public final String message;

        public Progress(final Uri photoUri, final String message) {
            this.photoUri = photoUri;
            this.message = message;
        }
    }

    public interface Callback {
        void onProgress(Progress progress);
        void onSuccess(List<ai_File> aFileList);
        void onFailure(String message);
    }

    private final Context context;
    private Callback callback;
    private final String errorMessage = "Photo Uploading Failed.";
    private final int iSAssetID;
    private final List<ai_File> aFileList = new ArrayList<>();

    public PhotoUploader(final Context context, int iSAssetID) {
        this.context = context;
        this.iSAssetID = iSAssetID;
    }

    public synchronized void setCallback(final Callback callback) {
        this.callback = callback;
    }

    @Override
    protected Boolean doInBackground(final Uri... photos) {
        if ( 0 == photos.length ) {
            return true;
        }

        for (Uri photo : photos) {
            ai_File oFile = getPhotoFile(photo);
            if (oFile != null && CommonHelper.bFileExist(oFile.sFile)) {
                HashMap<String, String> lsParams = new HashMap<String, String>();
                lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(context, "iCustomerID"));
                lsParams.put("sToken", CommonHelper.GetPreferenceString(context, "sToken"));
                lsParams.put("iPropertyID", "" + oFile.iSObjectID);
                lsParams.put("dtDateTaken", oFile.dtDateTime);
                lsParams.put("iSize", "" + oFile.iSize);
                lsParams.put("sClientFileName", oFile.sFile.substring(oFile.sFile.lastIndexOf("/") + 1));

                JSONObject oJson = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFile", lsParams);
                try {
                    if (oJson != null && oJson.getBoolean("success")) {
                        if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200) {
                            if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200) {
                                return false;
                            }
                        }
                    } else {
                        oJson = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFile", lsParams);
                        if (oJson != null && oJson.getBoolean("success")) {
                            if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200) {
                                if (IF_SyncClient.UploadFile(oJson.getString("sURL"), oFile.sFile) != 200) {
                                    return false;
                                }
                            }
                        } else {
                            return false;
                        }
                    }
                    int iSFileID = oJson.getJSONObject("oFile").getInt("iFileID");
                    if (iSFileID > 0) {
                        lsParams.clear();

                        lsParams.put("iCustomerID", CommonHelper.GetPreferenceString(context, "iCustomerID"));
                        lsParams.put("sToken", CommonHelper.GetPreferenceString(context, "sToken"));
                        lsParams.put("iSFileID", "" + iSFileID);
                        JSONObject oReturn = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFileSuccess", lsParams);
                        if (oReturn != null && oReturn.getBoolean("success")) {
                            oFile.iFileID = iSFileID;
                            oFile.bUploaded = true;

                            aFileList.add(oFile);
                        } else {
                            oReturn = IF_SyncClient.PostRequest("/IOAPI/UploadAssetFileSuccess", lsParams);
                            if (oReturn != null && oReturn.getBoolean("success")) {
                                oFile.iFileID = iSFileID;
                                oFile.bUploaded = true;
                                aFileList.add(oFile);
                            } else {
                                return false;
                            }
                        }
                    } else {
                        return false;
                    }
                } catch (Exception ex) {
                    ai_BugHandler.ai_Handler_Exception("Exception", "SyncService.UploadAssetFile.InterException", ex, context);
                    return false;
                }
            }
        }

        return true;
    }

    @Override
    protected void onProgressUpdate(final Progress... progress) {
        if ( null != callback ) {
            callback.onProgress(progress[0]);
        }
    }

    @Override
    protected void onPostExecute(final Boolean result) {
        if ( null != callback ) {
            if ( result ) {
                callback.onSuccess(aFileList);
            } else if ( !StringUtils.isEmpty(errorMessage) ) {
                callback.onFailure(errorMessage);
            }
        }
    }

    @SuppressLint("SimpleDateFormat")
    @Nullable
    private ai_File getPhotoFile(Uri photo) {
        Bitmap compressedPhoto = BitmapUtils.decodeBitmap(context, photo, 900, 900);

        String sFileName = CommonHelper.GetFileSaveFileName();
        CommonHelper.SaveImage(sFileName, compressedPhoto);
        if (CommonHelper.bFileExist(sFileName)) {
            File oFile = new File(sFileName);
            if (oFile.length() == 0) {
                Toast.makeText(context, "Error! Photo can not be saved. Please try again!", Toast.LENGTH_LONG).show();
                return null;
            }
        } else {
            Toast.makeText(context, "Error! Photo can not be saved. Please try again!", Toast.LENGTH_LONG).show();
            return null;
        }

        ai_File oFile = new ai_File();
        oFile.bUploaded = false;
        oFile.iFileID = 0;
        oFile.iSObjectID = iSAssetID;
        oFile.iSize = CommonHelper.GetFileLength(sFileName);
        oFile.sFile = sFileName;
        oFile.dtDateTime = CommonHelper.sDateToString(new Date());
        oFile.bDeleted = false;

        return oFile;
    }


}
