package com.snapinspect.snapinspect3.async;

import android.content.Context;
import android.os.AsyncTask;
import com.afollestad.materialdialogs.MaterialDialog;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_FloorPlan;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.util.FileUtils;
import org.json.JSONObject;

import java.io.File;
import java.lang.ref.WeakReference;
import java.util.HashMap;

public class InitFloorPlanTask extends AsyncTask<Void, Void, Integer> {
    private MaterialDialog progressDialog;

    public interface InitFloorPlanTaskListener {
        void onInitFloorPlanTaskCompleted(int iSFloorPlanID);
    }
    private final WeakReference<Context> mContext;
    private final ai_Photo mPhoto;
    private final int mIsAssetID;
    private final InitFloorPlanTaskListener mListener;
    public InitFloorPlanTask(Context context, ai_Photo aiPhoto, int iSAssetID, InitFloorPlanTaskListener listener) {
        mContext = new WeakReference<>(context);
        mPhoto = aiPhoto;
        mIsAssetID = iSAssetID;
        mListener = listener;
    }

    @Override
    protected void onPreExecute() {
        super.onPreExecute();
        progressDialog = CommonUI.ShowMaterialProgressDialog(
                mContext.get(), "Message", "Uploading Blueprint image...");
    }

    @Override
    protected Integer doInBackground(Void... voids) {
        HashMap<String, String> params = new HashMap<>();
        params.put(Constants.Keys.iAssetID, String.valueOf(mIsAssetID));
        params.put(Constants.Keys.iCustomerID, CommonHelper.GetPreferenceString(mContext.get(), Constants.Keys.iCustomerID));
        params.put(Constants.Keys.sToken, CommonHelper.GetPreferenceString(mContext.get(), Constants.Keys.sToken));

        JSONObject result = IF_SyncClient.UploadData(
                "/IOAPI/InitFloorPlan", params, mPhoto.getFile(), "fileData");
        try {
            if (result == null || !result.has("success") || !result.getBoolean("success")) {
                return -1;
            }
            ai_FloorPlan floorPlan = new ai_FloorPlan(result.getJSONObject("oFloorPlan"));
            // Copy the file to the floor plan folder
            FileUtils.copyFile(new File(mPhoto.getFile()), new File(floorPlan.savedPlanFilePath()), false);
            floorPlan.sPlanPath = floorPlan.getPlanFileName();
            // thumbnail
            FileUtils.copyFile(new File(mPhoto.getThumb()), new File(floorPlan.savedImageFilePath()), false);
            floorPlan.sImagePath = floorPlan.getImageFileName();

            // Delete the original file
            CommonDB.DeletePhotoByPhotoID(mPhoto.getId().intValue());

            floorPlan.save();
            return floorPlan.iSFloorPlanID;
        } catch (Exception e) {
            // Handle exception
        }
        return -1;
    }

    @Override
    protected void onPostExecute(Integer iSFloorPlanID) {
        super.onPostExecute(iSFloorPlanID);
        CommonUI.DismissMaterialProgressDialog(progressDialog);
        mListener.onInitFloorPlanTaskCompleted(iSFloorPlanID);
    }
}
