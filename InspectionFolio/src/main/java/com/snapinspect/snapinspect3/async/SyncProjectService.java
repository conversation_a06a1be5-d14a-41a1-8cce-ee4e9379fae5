package com.snapinspect.snapinspect3.async;

import android.app.IntentService;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.ResultReceiver;
import androidx.annotation.Nullable;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Project;
import com.snapinspect.snapinspect3.IF_Object.ai_ProjectInspection;
import com.snapinspect.snapinspect3.R;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.HashMap;

public class SyncProjectService extends IntentService {
    public static final int SHOW_LOADING = 0;
    public static final int HIDE_LOADING = 1;
    public static final int SYNC_SUCCESS = 3;

    private Thread loadProjectsThread, downloadProjectsThread, parseProjectsThread;
    private WeakReference<ResultReceiver> receiverWeakReference;
    private boolean bInitialSync = false;

    private Context getContext() {
        return getApplicationContext();
    }

    private ResultReceiver getReceiver() {
        return receiverWeakReference.get();
    }

    public SyncProjectService() {
        super("SyncProjectService");
    }

    @Override
    protected void onHandleIntent(@Nullable Intent intent) {
        if (intent == null) return;
        bInitialSync = intent.getBooleanExtra(Constants.Keys.bInitialSync, false);
        receiverWeakReference = new WeakReference<>(intent.getParcelableExtra(Constants.Keys.oResultReceiver));
        loadProjects();
    }

    private void showLoading(String title, String message) {
        if (getReceiver() != null) {
            Bundle bundle = new Bundle();
            bundle.putString(Constants.Keys.sTitle, title);
            bundle.putString(Constants.Keys.sMessage, message);
            getReceiver().send(SHOW_LOADING, bundle);
        }
    }

    private void hideLoading() {
        if (getReceiver() != null) {
            getReceiver().send(HIDE_LOADING, null);
        }
    }

    private void loadProjects() {
        if (!NetworkUtils.isNetworkAvailable(getContext())) {
            CommonUI.ShowAlert(getContext(), getString(R.string.alert_title_message),  getString(R.string.please_connect_internet));
            return;
        }
        if (loadProjectsThread != null) loadProjectsThread.interrupt();
        showLoading(getString(R.string.alert_title_message), getString(R.string.loading_projects));
        HashMap<String, String> lsParams = new HashMap<>();
        lsParams.put(Constants.Keys.iCustomerID, CommonHelper.GetPreferenceString(getContext(), Constants.Keys.iCustomerID));
        lsParams.put(Constants.Keys.sToken, CommonHelper.GetPreferenceString(getContext(), Constants.Keys.sToken));
        lsParams.put("sDateTime", "" + CommonHelper.GetPreferenceString(
                getContext(), Constants.Keys.sProjectsLastUpdated, "1980-01-01 00:00:00"));
        // When syncing project at first time, `bInitialSync` can be passed and server will return all the records
        if (bInitialSync) lsParams.put(Constants.Keys.bInitialSync, "1");
        loadProjectsThread = new Thread(() -> {
            JSONObject oReturn = IF_SyncClient.PostRequest("/IOAPI/SyncProjects", lsParams);
            new Handler(Looper.getMainLooper()).post(() -> {
                hideLoading();
                if (oReturn != null) {
                    try {
                        if (oReturn.getBoolean("success")) {
                            downloadProjects(oReturn.getString("sFile"), oReturn.getString("dtSyncDate"));
                        } else {
                            CommonUI.ShowAlert(getContext(), "Error", oReturn.getString("message"));
                        }
                    } catch (JSONException e) {
                        ai_BugHandler.ai_Handler_Exception(e);
                    }
                } else {
                    CommonUI.ShowAlert(getContext(), "Error", "Unable to connect to server");
                }
            });
        });
        loadProjectsThread.start();
    }

    private void downloadProjects(String sFileURL, String dtSyncDate) {
        if (downloadProjectsThread != null) downloadProjectsThread.interrupt();
        showLoading(getString(R.string.alert_title_message), getString(R.string.downloading_projects));
        downloadProjectsThread = new Thread(() -> {
            String sContent = IF_SyncClient.DownloadData(sFileURL);
            new Handler(Looper.getMainLooper()).post(() -> {
                hideLoading();
                if (sContent != null && !sContent.isEmpty()) {
                    try {
                        JSONObject objResponse = new JSONObject(sContent);
                        parseProjectsAndInspections(
                                objResponse.getJSONArray("lsProject"),
                                objResponse.getJSONArray("lsProjectInspection"),
                                dtSyncDate);
                    } catch (JSONException e) {
                        ai_BugHandler.ai_Handler_Exception(e);
                    }
                }
            });
        });
        downloadProjectsThread.start();
    }

    private void parseProjectsAndInspections(JSONArray lsProject, JSONArray lsProjectInspection, String dtSyncDate) {
        if (parseProjectsThread != null) parseProjectsThread.interrupt();
        parseProjectsThread = new Thread(() -> {
            try {
                ArrayList<ai_Project> aiProjects = new ArrayList<>();
                for (int i = 0; i < lsProject.length(); i++) {
                    ai_Project oProject = new ai_Project(lsProject.getJSONObject(i));
                    oProject.checkIfExist();
                    aiProjects.add(oProject);
                }
                CommonDB.SaveProjectsSugar(aiProjects);

                ArrayList<ai_ProjectInspection> aiProjectInspections = new ArrayList<>();
                for (int i = 0; i < lsProjectInspection.length(); i++) {
                    ai_ProjectInspection oProjectInspection = new ai_ProjectInspection(lsProjectInspection.getJSONObject(i));
                    oProjectInspection.checkIfExist();
                    aiProjectInspections.add(oProjectInspection);
                }
                CommonDB.SaveProjectInspectionsSugar(aiProjectInspections);

                // Update the last updated date
                CommonHelper.SavePreference(getContext(), Constants.Keys.sProjectsLastUpdated, dtSyncDate);

                new Handler(Looper.getMainLooper()).post(() -> {
                    if (getReceiver() != null) {
                        getReceiver().send(SYNC_SUCCESS, null);
                    }
                });
            } catch (JSONException e) {
                ai_BugHandler.ai_Handler_Exception(e);
            }
        });
        parseProjectsThread.start();
    }
}
