package com.snapinspect.snapinspect3.SI_DB;

import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.util.ArrayUtils;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by Terry<PERSON> on 25/09/17.
 */

public class db_InsItem {
    public static ai_InsItem GetInsItem_ByID(long iID){
        try{
            return CommonDB.findInsItemById(iID);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_InsItem.GetInsItem_ByID", ex);

        }
        return null;
    }

    public static ai_InsItem GetInsItem_BySInsItemID(int iSInsItemID) {
        try {
            List<ai_InsItem> insItems = CommonDB.findInsItemsBySInsItemID(iSInsItemID);
            if (insItems != null && !insItems.isEmpty()) {
                return insItems.get(0);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_InsItem.GetInsItem_BySInsItemID", ex);
        }
        return null;
    }

    public static List<ai_InsItem> GetInsItems_Deleted(int iInsID){
        try{
            return CommonDB.findDeletedInsItems(iInsID);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_InsItem.GetInsItems_Deleted", ex);

        }
        return null;
    }
    public static void BulkClearInsItem(int iInspectionID){
        try{
            CommonDB.executeInsItemQuery("UPDATE AIINS_ITEM set B_DELETED = 1, S_CUSTOM_ONE=? || S_CUSTOM_ONE WHERE I_INS_ID=? and B_DELETED = 0",
                    CommonHelper.GetTimeString(), "" + iInspectionID  );
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_InsItem.BulkClearInsItem", ex);
        }
    }
    public static void markNeedValidateCompulsoryConfig(ai_InsItem insItem) {
        insItem.sConfig = CommonJson.AddJsonKeyValue(insItem.sConfig, "IN", "1");
        CommonDB.saveInsItem(insItem);
    }

    public static ai_Assets getAssetByInsItemID(long iInsItemID) {
        if (iInsItemID <= 0) return null;
        ai_InsItem insItem = GetInsItem_ByID(iInsItemID);
        if (insItem == null || insItem.iInsID <= 0) return null;
        ai_Inspection inspection = db_Inspection.GetInspection_ByID(insItem.iInsID);
        if (inspection == null || inspection.iSAssetID <= 0) return null;
        return db_Asset.GetAssetBySAssetID(inspection.iSAssetID);
    }

    public static void addDropPinData(ai_InsItem insItem, int iSFloorPlanID, List<Map<String, Object>> sDropPinData, int iPhotoID) {
        try {
            List<Map<String, Object>> dropPins = getDropPinData(insItem);
            for (int i = 0; i < dropPins.size(); i++) {
                Map<String, Object> dropPin = dropPins.get(i);
                Object iSFloorPlanIDObj = dropPin.get(Constants.Keys.iFloorPlanID);
                if (iSFloorPlanIDObj instanceof Double && (double) iSFloorPlanIDObj == iSFloorPlanID) {
                    deleteDropPinPhotoIfExist(dropPin);
                    dropPins.remove(i);
                    break;
                }
            }

            if (ArrayUtils.isNotEmpty(sDropPinData)) {
                Map<String, Object> dropPin = new LinkedTreeMap<>();
                dropPin.put(Constants.Keys.iFloorPlanID, iSFloorPlanID);
                dropPin.put(Constants.Keys.objects, sDropPinData);
                dropPin.put(Constants.Keys.iPhotoID, iPhotoID);
                dropPins.add(dropPin);
            }

            insItem.sCustomTwo = new Gson().toJson(dropPins);
            CommonDB.saveInsItem(insItem);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_InsItem.addDropPinData", ex);
        }
    }

    private static void deleteDropPinPhotoIfExist(Map<String, Object> dropPin) {
        try {
            ai_Photo oPhoto = null;
            // Try to find photo by iPhotoID
            if (dropPin.containsKey(Constants.Keys.iPhotoID)) {
                Object iPhotoIDObj = dropPin.get(Constants.Keys.iPhotoID);
                if (iPhotoIDObj instanceof Double) {
                    int iPhotoID = ((Double) iPhotoIDObj).intValue();
                    if (iPhotoID > 0) {
                        oPhoto = CommonDB.GetPhotoByIdSugar(iPhotoID);
                    }
                }
            }

            // If photo is not found by iPhotoID, try to find it by iSPhotoID
            if (oPhoto == null && dropPin.containsKey(Constants.Keys.iSPhotoID)) {
                Object iSPhotoIDObj = dropPin.get(Constants.Keys.iSPhotoID);
                if (iSPhotoIDObj instanceof Double) {
                    int iSPhotoID = ((Double) iSPhotoIDObj).intValue();
                    if (iSPhotoID > 0) {
                        oPhoto = db_Media.getPhotoByServerID(iSPhotoID);
                    }
                }
            }

            // Delete the photo if it exists
            if (oPhoto != null) {
                CommonDB.DeletePhotoByPhotoID(oPhoto.getId().intValue());
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_InsItem.deleteDropPinPhotoIfExist", ex);
        }
    }


    /**
     * Retrieves the drop pin data for a given ai_InsItem, the element is stored in the sCustomTwo field.
     * and the format is as follows:
     * [{
     *     "iFloorPlanID": 1,
     *     "objects": [{......}]
     * },
     * {
     *    "iFloorPlanID": 2,
     *    "objects": [{......}]
     * },
     *    ......
     * ]
     *
     * @param insItem The ai_InsItem object for which to retrieve drop pin data.
     * @return The drop pin data in JSONArray format. Returns null if an exception occurs.
     */
    public static List<Map<String, Object>> getDropPinData(ai_InsItem insItem) {
        if (StringUtils.isEmpty(insItem.sCustomTwo)) return new ArrayList<>();
        try {
            return (List<Map<String, Object>>) new Gson().fromJson(insItem.sCustomTwo, List.class);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_InsItem.getDropPinData", ex);
        }
        return new ArrayList<>();
    }

    public static Map<String, Object> getDropPinData(ai_InsItem insItem, int iSFloorPlanID) {
        try {
            List<Map<String, Object>> dropPins = getDropPinData(insItem);
            for (Map<String, Object> dropPin : dropPins) {
                if (dropPin != null && !dropPin.isEmpty()) {
                    Object iSFloorPlanIDObj = dropPin.get(Constants.Keys.iFloorPlanID);
                    if (iSFloorPlanIDObj instanceof Double && (double) iSFloorPlanIDObj == iSFloorPlanID) {
                        return dropPin;
                    }
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_InsItem.getDropPinData", ex);
        }
        return new LinkedTreeMap<>();
    }

    /**
     * Get the video size from the inspection type
     * The video size is stored in the sFieldOne of the inspection type
     * @see VideoQuality
     */
    public static VideoQuality getInsTypeVideoSize(long iInsItemID) {
        ai_InsItem insItem = GetInsItem_ByID(iInsItemID);
        if (insItem == null || insItem.iInsID <= 0) return null;
        return db_Inspection.getInsTypeVideoSize(insItem.iInsID);
    }

    /**
     * Get the photo size from the inspection type
     * The photo size is stored in the sFieldOne of the inspection type
     * @see VideoQuality
     */
    public static int getInsTypePhotoSize(long iInsItemID) {
        ai_InsItem insItem = GetInsItem_ByID(iInsItemID);
        if (insItem == null || insItem.iInsID <= 0) return 0;
        return db_Inspection.getInsTypePhotoSize(insItem.iInsID);
    }
}
