package com.snapinspect.snapinspect3.SI_DB

// Removed: import com.orm.SugarRecord
import com.snapinspect.snapinspect3.Helper.CommonDB
import com.snapinspect.snapinspect3.IF_Object.ai_Layout
import com.snapinspect.snapinspect3.IF_Object.ai_PropertyLayout

object db_PropertyLayout {
    fun savePropertyLayouts(layouts: List<ai_PropertyLayout>) {
        if (layouts.isEmpty()) return

        val existingLayouts = layouts
            .map(ai_PropertyLayout::iSPropertyLayoutID)
            .mapNotNull(::getPropertyLayoutBySID)

        val layoutsToSave = layouts.map { layout ->
            val existingLayout = existingLayouts.find { 
                it.iSPropertyLayoutID == layout.iSPropertyLayoutID 
            }
            if (existingLayout != null) {
                layout.id = existingLayout.id
            }
            layout
        }

        CommonDB.savePropertyLayoutsInTransaction(layoutsToSave)
    }

    fun getPropertyLayoutBySID(iSPropertyLayoutID: Int): ai_PropertyLayout? {
        return CommonDB.findPropertyLayoutByServerID(iSPropertyLayoutID)
    }

    @JvmStatic
    fun getPropertyLayouts(sPTC: String, iPropertyID: Int): List<ai_Layout> {
        val propertyLayout = CommonDB.findPropertyLayoutByPTCAndProperty(sPTC, iPropertyID) ?: return emptyList()

        return propertyLayout.getLayoutItems().mapIndexed { index, item ->
            val layout = getLayoutWithLayoutItem(item, sPTC)
            layout.sName = item.getLayoutName()
            layout.sFieldThree = index.toString()
            layout.sFieldOne = "1"
            layout
        }
    }

    fun getLayoutWithLayoutItem(item: ai_PropertyLayout.LayoutItem, sPTC: String): ai_Layout {
        // Try finding layout by ID first
        item.layoutId.takeIf { it > 0 }?.let { layoutId ->
            db_Inspection.GetLayoutBySLayoutID(layoutId)?.let { return it }
        }
        
        // Try finding layout by name and address
        val possibleNames = listOfNotNull(item.name, item.address)
            .filter { it.isNotEmpty() }
        
        for (name in possibleNames) {
            findLayoutByName(name, sPTC)?.let { return it }
        }
        
        // If no existing layout found, create a new one
        return ai_Layout(item.layoutId, item.getLayoutName().orEmpty())
    }
    
    private fun findLayoutByName(name: String, sPTC: String): ai_Layout? {
        return CommonDB.findLayoutByNameAndPTC(name, sPTC)
    }
}