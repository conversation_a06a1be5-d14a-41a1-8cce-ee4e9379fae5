package com.snapinspect.snapinspect3.SI_DB;

import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.IF_Object.ai_ProjectInspection;
import com.snapinspect.snapinspect3.IF_Object.v_ProjectInspection;
import com.snapinspect.snapinspect3.activitynew.Projects.if_ProjectInspection;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

public class db_Projects {

    public static List<v_ProjectInspection> getProjectInspection(
            int iSProjectID, if_ProjectInspection.InspectionStatus status, String keyword) {
        String query = "";
        if (status == if_ProjectInspection.InspectionStatus.IN_PROGRESS) {
            query = "SELECT pIns.ID, pIns.I_S_PROJECT_ASSET_INS_TYPE_ID, pIns.I_PROJECT_ID, pIns.I_COMPANY_ID, pIns.I_ASSET_ID, pIns.I_INS_TYPE_ID, pIns.I_INSPECTION_ID, pIns.I_S_INSPECTION_ID, pIns.I_INSPECTOR_ID, pIns.DT_START, pIns.DT_END, pIns.S_CUSTOM, pIns.B_DELETED, pIns.DT_UPDATE, pIns.DT_DATE_TIME, IFNULL(asset.S_FIELD_ONE, '') AS S_CUSTOM_ONE, IFNULL(asset.S_FIELD_TWO, '') AS S_CUSTOM_TWO, IFNULL(asset.S_ADDRESS_ONE, '') AS S_ADDRESS_ONE, IFNULL(asset.S_ADDRESS_TWO, '') AS S_ADDRESS_TWO, IFNULL(ins.S_INS_TITLE, \"\") AS S_INS_TITLE, IFNULL(asset.S_FIELD_THREE, \"\") AS S_REFERENCE, IFNULL(ins.S_TITLE, ''), IFNULL(insType.S_INS_TITLE, '') AS S_INS_TYPE_TITLE, IFNULL(ins.B_COMPLETE, '') AS B_INS_COMPLETED, IFNULL(ins.B_SYNCED, '') AS B_INS_SYNCED, IFNULL(ins.DT_START_DATE, '') AS DT_INS_START, IFNULL(ins.DT_END_DATE, '') AS DT_INS_END FROM AIPROJECT_INSPECTION pIns LEFT JOIN AIASSETS asset on pIns.I_ASSET_ID = asset.I_S_ASSET_ID LEFT JOIN AIINSPECTION ins on (pIns.I_S_INSPECTION_ID > 0 AND pIns.I_S_INSPECTION_ID == ins.I_S_INS_ID) OR pIns.I_INSPECTION_ID == ins.ID LEFT JOIN AIINS_TYPE insType on insType.I_S_INS_TYPE_ID == pIns.I_INS_TYPE_ID WHERE pIns.B_DELETED = 0 AND pIns.I_S_INSPECTION_ID == 0 AND pIns.I_PROJECT_ID = ? ";
        } else if (status == if_ProjectInspection.InspectionStatus.COMPLETED) {
            query = "SELECT pIns.ID, pIns.I_S_PROJECT_ASSET_INS_TYPE_ID, pIns.I_PROJECT_ID, pIns.I_COMPANY_ID, pIns.I_ASSET_ID, pIns.I_INS_TYPE_ID, pIns.I_INSPECTION_ID, pIns.I_S_INSPECTION_ID, pIns.I_INSPECTOR_ID, pIns.DT_START, pIns.DT_END, pIns.S_CUSTOM, pIns.B_DELETED, pIns.DT_UPDATE, pIns.DT_DATE_TIME, IFNULL(asset.S_FIELD_ONE, '') AS S_CUSTOM_ONE, IFNULL(asset.S_FIELD_TWO, '') AS S_CUSTOM_TWO, IFNULL(asset.S_ADDRESS_ONE, '') AS S_ADDRESS_ONE, IFNULL(asset.S_ADDRESS_TWO, '') AS S_ADDRESS_TWO, IFNULL(ins.S_TITLE, ''), IFNULL(ins.S_INS_TITLE, \"\") AS S_INS_TITLE, IFNULL(asset.S_FIELD_THREE, \"\") AS S_REFERENCE, IFNULL(insType.S_INS_TITLE, '') AS S_INS_TYPE_TITLE, IFNULL(ins.B_COMPLETE, '') AS B_INS_COMPLETED, IFNULL(ins.B_SYNCED, '') AS B_INS_SYNCED, IFNULL(ins.DT_START_DATE, '') AS DT_INS_START, IFNULL(ins.DT_END_DATE, '') AS DT_INS_END FROM AIPROJECT_INSPECTION pIns LEFT JOIN AIASSETS asset on pIns.I_ASSET_ID = asset.I_S_ASSET_ID LEFT JOIN AIINSPECTION ins on (pIns.I_S_INSPECTION_ID > 0 AND pIns.I_S_INSPECTION_ID == ins.I_S_INS_ID) OR pIns.I_INSPECTION_ID == ins.ID LEFT JOIN AIINS_TYPE insType on insType.I_S_INS_TYPE_ID == pIns.I_INS_TYPE_ID WHERE pIns.B_DELETED = 0 AND pIns.I_S_INSPECTION_ID > 0 AND pIns.I_PROJECT_ID = ? ";
        }
        if (StringUtils.isEmpty(query)) return new ArrayList<>();
        if (!StringUtils.isEmpty(keyword)) {
            query += "AND (asset.S_ADDRESS_ONE LIKE '%%" + keyword + "%%' OR " +
                    "asset.S_ADDRESS_TWO LIKE '%%" + keyword + "%%' OR " +
                    "ins.S_INS_TITLE LIKE '%%" + keyword + "%%' OR " +
                    "asset.S_FIELD_THREE LIKE '%%" + keyword + "%%')";
        }
        query += " ORDER BY DT_INS_START, DT_UPDATE DESC";

        return CommonDB.findProjectInspectionWithQuery(query, String.valueOf(iSProjectID));
    }

    public static List<ai_ProjectInspection> getProjectInspections(int iSInsID) {
        return CommonDB.findProjectInspectionsByInsId(iSInsID);
    }

    public static void updateProjectInspections(int iSInsID, int iInsID) {
        List<ai_ProjectInspection> projectInspections = getProjectInspections(iSInsID);
        for (ai_ProjectInspection projectInspection : projectInspections) {
            projectInspection.iInspectionID = iInsID;
        }
        CommonDB.saveProjectInspectionsInTx(projectInspections);
    }
}
