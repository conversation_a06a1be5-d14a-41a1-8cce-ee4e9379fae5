package com.snapinspect.snapinspect3.SI_DB;

import android.content.Context;
import android.database.Cursor;

import android.text.TextUtils;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.*;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * Created by TerryS on 9/09/17.
 */

public class db_Asset {

    public static v_Asset GetVAssetBySAssetID(int iSAssetID) {
        v_Asset vAsset = null;
        try {
            Cursor oCursor = CommonDB.getAssetCursor(
                    v_Asset.class, "iAssetID = " + iSAssetID, null, null, null, "1");
            if (oCursor.moveToNext()) {
                vAsset = new v_Asset(oCursor);
            }
            oCursor.close();
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }

        return vAsset;
    }

    public static ai_Assets GetAssetBySAssetID(int iSAssetID) {
        try {
            return CommonDB.findAssetBySAssetID(iSAssetID);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Asset.GetAssetBySAssetID", ex);

        }
        return null;
    }

    public static List<ai_Assets> SearchAssetList(String sFilter, int iSPAssetID, Context oContext, int iOffSet, String sSortString){
        try {
            String sRole = CommonHelper.GetPreferenceString(oContext, "sRole");
            String iCustomerID = CommonHelper.GetPreferenceString(oContext, "iCustomerID");
            String predicate = (!TextUtils.isEmpty(sRole) && sRole.equals("CT") && !TextUtils.isEmpty(iCustomerID)) ? (" AND I_CUSTOMER_ID = " + iCustomerID) : "";
            String query = "SELECT * FROM aiAssets WHERE (S_ADDRESS_ONE like '%" +
                    CommonDB.EscapeString(sFilter) + "%' or S_ADDRESS_TWO like '%" +
                    CommonDB.EscapeString(sFilter) + "%' or S_FIELD_THREE like '%" + sFilter + "%') AND I_SP_ASSET_ID = " + iSPAssetID +
                    predicate + " " +
                    ((sSortString == null || sSortString.equals("")) ? "COLLATE NOCASE" : sSortString) + " LIMIT 300 OFFSET " + iOffSet;
            return CommonDB.findAssetsWithQuery(query);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception("Exception", "CommonDB.SearchAssetList", ex, oContext);
        }
        return Collections.emptyList();
    }

    public static List<ai_Assets> SearchAssetList_WithGroup(String sFilter, int iSPAssetID, int iOffSet, String sSortString, String sGroupPermission) {
        try {
            String sSql = "SELECT * FROM aiAssets WHERE (S_ADDRESS_ONE like '%" +
                    CommonDB.EscapeString(sFilter) + "%' or S_ADDRESS_TWO like '%" +
                    CommonDB.EscapeString(sFilter) + "%' or S_FIELD_THREE like '%" + sFilter + "%') AND I_SP_ASSET_ID = " + iSPAssetID +
                    " AND (I_GROUP_ID = NULL or I_GROUP_ID = 0 or I_GROUP_ID IN (" + sGroupPermission +  ")) " +
                    (StringUtils.isEmpty(sSortString) ? "COLLATE NOCASE" : sSortString) + " LIMIT 300 OFFSET " + iOffSet;
            return CommonDB.findAssetsWithQuery(sSql);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Asset.SearchAssetList_WithGroup", ex);
        }
        return Collections.emptyList();
    }

    public static List<ai_Assets> SearchAssetList_V2(String sFilter, int iSPAssetID, int iOffSet, String sSortString, int[] lsAssetViewId) {
        try {
            String sSql = "SELECT * FROM aiAssets WHERE (S_ADDRESS_ONE like '%" +
                    CommonDB.EscapeString(sFilter) + "%' or S_ADDRESS_TWO like '%" +
                    CommonDB.EscapeString(sFilter) + "%' or S_FIELD_THREE like '%" + sFilter + "%') AND I_SP_ASSET_ID = " + iSPAssetID;
            // Filter by asset view
            if (lsAssetViewId != null && lsAssetViewId.length > 0) {
                List<String> conditions = new ArrayList<>();
                for (int assetViewId : lsAssetViewId) {
                    conditions.add(String.format("S_FIELD_TWO LIKE '%%[%d]%%'", assetViewId));
                }
                sSql += " AND (" + String.join(" OR ", conditions) + ") ";
            }
            // Sort and limit
            sSql += (StringUtils.isEmpty(sSortString) ? " COLLATE NOCASE" : sSortString) + " LIMIT 300 OFFSET " + iOffSet;
            return CommonDB.findAssetsWithQuery(sSql);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Asset.SearchAssetList_V2", ex);
        }
        return Collections.emptyList();
    }

    public static List<ai_Assets> getAssetList(
            Context context, String sFilter, int iSPAssetID, int iOffSet, String sSortString,
            String sGroupPermission, int[] lsAssetViewId) {
        ai_AssetView savedSelectedAssetView = db_AssetView.getSavedSelectedAssetView(context);
        if (savedSelectedAssetView == null || iSPAssetID > 0) { // Assets List
            if ("1".equals(CommonJson.sFolder(context)) && CommonJson.isCustomRole(context) && iSPAssetID == 0) {
                if ("1".equals(CommonJson.sFolderPermission(context))) { // Option 2 with Asset Views
                    if (lsAssetViewId.length == 0) return Collections.emptyList();
                    return SearchAssetList_V2(sFilter, iSPAssetID, iOffSet, sSortString, lsAssetViewId);
                } else { // Option 2 without Asset Views
                    return SearchAssetList_V2(sFilter, iSPAssetID, iOffSet, sSortString, null);
                }
            } else {
                if (iSPAssetID > 0) {
                    return SearchAssetList(sFilter, iSPAssetID, context, iOffSet, sSortString);
                } else {
                    if (CommonValidate.bCompanyGroupEnabled(context)) {
                        return SearchAssetList_WithGroup(sFilter, iSPAssetID, iOffSet, sSortString, sGroupPermission);
                    } else {
                        return SearchAssetList(sFilter, iSPAssetID, context, iOffSet, sSortString);
                    }
                }
            }
        } else {
            return SearchAssetList_V2(sFilter, iSPAssetID, iOffSet, sSortString, lsAssetViewId);
        }
    }

    public static ai_Contact GetContactByID(int iSContactID) {
        List<ai_Contact> lsContact = CommonDB.findContactsBySContactID(iSContactID);
        return !lsContact.isEmpty() ? lsContact.get(0) : null;
    }
}
