package com.snapinspect.snapinspect3.SI_DB;

import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.IF_Object.ai_Product;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class db_Product {
    public static void deleteProduct(int iProductID) {
        String query = "UPDATE AIPRODUCT SET B_DELETED = 1 WHERE I_PRODUCT_ID = ?";
        CommonDB.executeProductQuery(query, String.valueOf(iProductID));
    }

    public static ai_Product getProductBySProductID(int iSProductID) {
        String query = "SELECT * FROM AIPRODUCT WHERE I_S_PRODUCT_ID = ? LIMIT 1";
        List<ai_Product> products = CommonDB.findProductsWithQuery(query, String.valueOf(iSProductID));
        return !products.isEmpty() ? products.get(0) : null;
    }

    public static ai_Product getProductByID(long iProductID) {
        return CommonDB.findProductById(iProductID);
    }

    public static List<ai_Product> getProducts() {
        String query = "SELECT * FROM AIPRODUCT WHERE B_DELETED = 0";
        return CommonDB.findProductsWithQuery(query);
    }

    public static List<ai_Product> getProductsByInsItemTitles(List<String> titles, String keyword) {
        StringBuilder queryBuilder = new StringBuilder("SELECT * FROM AIPRODUCT WHERE B_DELETED = 0");
        if (!titles.isEmpty()) {
            queryBuilder.append(" AND (");
            for (int i = 0; i < titles.size(); i++) {
                queryBuilder
                        .append("S_ASSOCIATE_ITEM LIKE '%")
                        .append(titles.get(i))
                        .append("%'");
                if (i < titles.size() - 1) {
                    queryBuilder.append(" OR ");
                }
            }
            queryBuilder.append(")");
        }
        if (keyword != null && !keyword.isEmpty()) {
            queryBuilder.append(" AND (S_NAME LIKE '%")
                    .append(keyword)
                    .append("%' OR S_SKU LIKE '%")
                    .append(keyword)
                    .append("%' OR S_MODEL LIKE '%")
                    .append(keyword)
                    .append("%' OR S_DESP LIKE '%")
                    .append(keyword)
                    .append("%')");
        }
        return CommonDB.findProductsWithQuery(queryBuilder.toString());
    }

    public static void saveProduct(ai_Product product) {
        saveProducts(Collections.singletonList(product));
    }

    public static void saveProducts(List<ai_Product> products) {
        ArrayList<ai_Product> productsToSave = new ArrayList<>();
        for (ai_Product product : products) {
            if (product.iSProductID > 0) {
                ai_Product existingProduct = getProductBySProductID(product.iSProductID);
                if (existingProduct != null) {
                    product.setId(existingProduct.getId());
                }
            }
            productsToSave.add(product);
        }
        CommonDB.saveProductsInTx(productsToSave);
    }

    public static List<ai_Product> getProductsByAssociateItem(String title) {
        String[] keywords = title.toLowerCase().split("\\s+");
        StringBuilder queryBuilder = new StringBuilder("SELECT * FROM AIPRODUCT WHERE B_DELETED = 0");
        
        for (int i = 0; i < keywords.length; i++) {
            queryBuilder.append(" AND S_ASSOCIATE_ITEM LIKE ?");
        }
        
        String query = queryBuilder.toString();
        String[] args = new String[keywords.length];
        for (int i = 0; i < keywords.length; i++) {
            args[i] = "%\"" + keywords[i] + "\"%";
        }
        
        return CommonDB.findProductsWithQuery(query, args);
    }

}
