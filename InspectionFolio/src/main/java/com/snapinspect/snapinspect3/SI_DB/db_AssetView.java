package com.snapinspect.snapinspect3.SI_DB;

import android.content.Context;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.IF_Object.ai_AssetView;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.R;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

public class db_AssetView {

    private db_AssetView() {
        // private constructor to avoid instantiation of this class
    }
    public static ai_AssetView getAssetViewForAllAssets(Context context) {
        ai_AssetView assetView = new ai_AssetView();
        assetView.iSAssetViewID = Constants.Values.kAllAssetsAssetViewID;
        assetView.sName = context.getString(R.string.assets_list);
        return assetView;
    }

    public static List<ai_AssetView> getAllAssetViews() {
        return CommonDB.findAssetViews("B_DELETED = 0 AND B_ARCHIVED = 0");
    }

    public static ai_AssetView getAssetView(int iSAssetViewID) {
        List<ai_AssetView> assetViews = CommonDB.findAssetViews("I_S_ASSET_VIEW_ID = ?", iSAssetViewID + "");
        return !assetViews.isEmpty() ? assetViews.get(0) : null;
    }

    public static List<ai_AssetView> getAssetViewsByCustomer(int iSCustomerID) {
        return CommonDB.findAssetViews("B_DELETED = 0 AND B_ARCHIVED = 0 AND ARR_MEM LIKE ?", "%%[" + iSCustomerID + "]%%");
    }

    public static List<ai_AssetView> getAvailableAssetViewsForCurrentUser(Context oContext) {
        int customerID = CommonHelper.GetPreferenceInt(oContext, "iCustomerID");
        if ((CommonJson.isCompanyAdmin(oContext) ||
                CommonJson.isPropertyManager(oContext) || CommonJson.isContractor(oContext))) {
            return getAllAssetViews();
        } else if ("1".equals(CommonJson.sFolderPermission(oContext))) {
            return getAssetViewsByCustomer(customerID);
        } else {
            return new ArrayList<>();
        }
    }

    public static void saveAssetView(String sAssetView) {
        try {
            JSONArray assetViewJsons = CommonJson.GetJSONArrayFromJson(sAssetView);
            ArrayList<ai_AssetView> assetViews = new ArrayList<>();
            for (int i = 0; i < assetViewJsons.length(); i++) {
                JSONObject assetViewJson = assetViewJsons.getJSONObject(i);
                assetViews.add(new ai_AssetView(assetViewJson));
            }
            CommonDB.saveAssetViewsInTx(assetViews);
        } catch (Exception e) {
            ai_BugHandler.ai_Handler_Exception(e);
        }
    }

    public static ai_AssetView getSavedSelectedAssetView(Context context) {
        int selectedAssetViewID = CommonHelper.GetPreferenceInt(context, Constants.Keys.kSelectedAssetViewID);
        return selectedAssetViewID > 0 ? getAssetView(selectedAssetViewID) : null;
    }

    public static List<ai_AssetView> getDefaultAvailableAstViews(Context context) {
        // if user has selected an asset view, return it
        ai_AssetView selectedAstView = getSavedSelectedAssetView(context);
        if (selectedAstView != null) {
            List<ai_AssetView> selectedAstViews = new ArrayList<>();
            selectedAstViews.add(selectedAstView);
            return selectedAstViews;
        }

        return getAvailableAssetViewsForCurrentUser(context);
    }

}
