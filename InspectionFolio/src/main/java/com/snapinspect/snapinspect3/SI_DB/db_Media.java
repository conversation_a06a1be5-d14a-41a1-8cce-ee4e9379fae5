package com.snapinspect.snapinspect3.SI_DB;

import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_Photo;
import com.snapinspect.snapinspect3.IF_Object.ai_Video;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * Created by TerryS on 13/09/17.
 */

public class db_Media {

    public static long GetPhotosCountForIDs(String photoIds) {
        return CommonDB.countPhotos("ID in (" + photoIds + ") AND B_DELETED = 0", photoIds);
    }

    public static List<ai_Photo>GetPhotosForIds(String photoIds) {
        return CommonDB.findPhotos("ID in (" + photoIds + ") AND B_DELETED = 0", photoIds);
    }

    public static List<ai_Photo> GetPhotosForInsItem(int iInsItemID) {
        return CommonDB.findPhotos("I_INS_ITEM_ID = ? AND B_DELETED = 0", "" + iInsItemID);
    }

    public static List<ai_Photo> GetPhotosForInspection_NeedUpload(int iInspectionID){
        try {
            return ai_Photo.find(ai_Photo.class, "I_INS_ID=? and B_DELETED = 0 and B_UPLOADED=0", "" + iInspectionID);
        } catch (Exception ex) {

        }
        return null;
    }
    public static List<ai_Video> GetVideosForInspection_NeedUpload(int iInspectionID){
        try{
            return ai_Video.find(ai_Video.class, "I_INS_ID=? and B_DELETED= 0 and B_GET_URL = 0", "" + iInspectionID);
        }catch(Exception ex){

        }
        return null;
    }
    public static List<ai_Photo> GetPhotosForInspection(int iInspectionID){
        try{
            return ai_Photo.find(ai_Photo.class, "I_INS_ID=? and B_DELETED = 0", "" + iInspectionID);
        }catch(Exception ex){

        }

        return null;
    }
    public static ai_Video GetVideoByServerVideoID(int iSVideoID, int iInspectionID){
        try{
            List<ai_Video> lsVideo  =ai_Video.find(ai_Video.class, "I_S_VIDEO_ID =? and I_INS_ID=? and B_DELETED = 0", ""+iSVideoID, ""+iInspectionID);
            if (lsVideo != null && lsVideo.size() > 0){
                return lsVideo.get(lsVideo.size() - 1);
            }
        }catch(Exception ex){

        }
        return null;
    }

    public static ai_Photo GetPhotoByServerPhotoID(int iSPhotoID, int iInsID){
        try {
            List<ai_Photo> lsPhoto = ai_Photo.find(ai_Photo.class, "I_S_PHOTO_ID = ? and B_DELETED = 0 and I_INS_ID = ?", "" + iSPhotoID, "" + iInsID);
            if (lsPhoto != null && lsPhoto.size() > 0){
                return lsPhoto.get(lsPhoto.size() - 1);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return null;
    }

    public static ai_Photo getPhotoByServerID(int iSPhotoID) {
        try {
            List<ai_Photo> lsPhoto = CommonDB.findPhotos("I_S_PHOTO_ID = ? and B_DELETED = 0", "" + iSPhotoID);
            if (lsPhoto != null && !lsPhoto.isEmpty()) {
                return lsPhoto.get(0);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return null;
    }

    public static ai_Photo getPhotoOrCreateByServerID(int iSPhotoID) {
        ai_Photo photo = getPhotoByServerID(iSPhotoID);
        if (photo == null) {
            photo = new ai_Photo();
            photo.iSPhotoID = iSPhotoID;
            photo.bUploaded = true;
            photo.dtDateTime = CommonHelper.sDateToString(new Date());
            photo.save();
        }
        return getPhotoByServerID(iSPhotoID);
    }

    public static ai_Video getVideoByServerID(int iSVideoID) {
        try {
            List<ai_Video> lsVideo = CommonDB.findVideos("I_S_VIDEO_ID = ? and B_DELETED = 0", "" + iSVideoID);
            if (lsVideo != null && !lsVideo.isEmpty()) {
                return lsVideo.get(0);
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return null;
    }

    public static ai_Video getVideoOrCreateByServerID(int iSVideoID) {
        ai_Video video = getVideoByServerID(iSVideoID);
        if (video == null) {
            video = new ai_Video();
            video.iSVideoID = iSVideoID;
            video.bUploaded = true;
            video.dtDateTime = CommonHelper.sDateToString(new Date());
            video.save();
        }
        return getVideoByServerID(iSVideoID);
    }

    public static ai_Video GetVideoByInsItemID(ai_InsItem oInsItem) {
        try {
            List<ai_Video> lsVideo = CommonDB.GetInsItemVideosSugar(oInsItem.getId());
            if (lsVideo != null && lsVideo.size() > 0) {
                if (oInsItem.sValueOne != null && oInsItem.sValueOne.trim().length() > 0) {
                    ai_Video oVideo = null;
                    for (ai_Video oTempVideo : lsVideo) {
                        if (oInsItem.sValueOne.trim().equalsIgnoreCase("" + oTempVideo.getId())) {
                            oVideo = oTempVideo;
                            return oVideo;
                        }
                    }
                } else {
                    return lsVideo.get(0);
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return null;
    }

    public static String GetVideoServerIDString(long iVideoID) {
        ai_Video video = CommonDB.findVideoById(iVideoID);
        return video == null ? "" : String.valueOf(video.iSVideoID);
    }
    public static int UpdatePhoto(ai_Photo aPhoto) {
        if (aPhoto != null && aPhoto.getId() > 0) {
            ai_Photo uPhoto = CommonDB.GetPhotoByIdSugar(aPhoto.getId());
            uPhoto.iSPhotoID = aPhoto.iSPhotoID;
            uPhoto.sThumb = aPhoto.sThumb;
            uPhoto.sFile = aPhoto.sFile;
            uPhoto.sComments = CommonHelper.EscapeString(aPhoto.sComments);
            uPhoto.bUploaded = aPhoto.bUploaded;
            uPhoto.bDeleted = aPhoto.bDeleted;

            uPhoto.save();
            return uPhoto.getId().intValue();
        } else {
            ai_Photo sPhoto = new ai_Photo();
            sPhoto.iInsItemID = aPhoto.iInsItemID;
            sPhoto.iInsID = aPhoto.iInsID;
            sPhoto.sThumb = aPhoto.sThumb;
            sPhoto.iSPhotoID = aPhoto.iSPhotoID;
            sPhoto.sFile = aPhoto.sFile;
            sPhoto.iSize = CommonHelper.GetFileLength(aPhoto.sFile);
            Date oNow = new Date();
            sPhoto.dtDateTime = CommonHelper.sDateToString(oNow);

            sPhoto.save();
            return sPhoto.getId().intValue();
        }
    }
}
