package com.snapinspect.snapinspect3.SI_DB;

import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.ai_InsItem;
import com.snapinspect.snapinspect3.IF_Object.ai_ProductCost;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class db_ProductCost {
    public static List<ai_ProductCost> getAllProductCosts(int iInsItemID) {
        String query = "SELECT * FROM AIPRODUCT_COST WHERE I_INS_ITEM_ID = ? AND B_DELETED = 0";
        return CommonDB.findProductCostsWithQuery(query, String.valueOf(iInsItemID));
    }

    public static void deleteProductCost(long id) {
        ai_ProductCost productCost = getProductCost(id);
        if (productCost != null) {
            productCost.bDeleted = true;
            saveProductCost(productCost);
        }
    }

    public static ai_ProductCost getProductCost(long id) {
        return CommonDB.findProductCostById(id);
    }

    public static ai_ProductCost getProductCostBySCostingID(int iSCostingID) {
        String query = "SELECT * FROM AIPRODUCT_COST WHERE I_S_COSTING_ID = ? AND B_DELETED = 0";
        List<ai_ProductCost> productCosts = CommonDB.findProductCostsWithQuery(query, String.valueOf(iSCostingID));
        if (productCosts != null && !productCosts.isEmpty()) {
            return productCosts.get(0);
        }
        return null;
    }

    public static List<ai_ProductCost> getProductCosts(int iProductID) {
        String query = "SELECT * FROM AIPRODUCT_COST WHERE I_PRODUCT_ID = ? AND B_DELETED = 0";
        return CommonDB.findProductCostsWithQuery(query, String.valueOf(iProductID));
    }

    public static ai_ProductCost getProductCostByInsItemID(int iInsItemID, int iProductID) {
        String query = "SELECT * FROM AIPRODUCT_COST WHERE I_INS_ITEM_ID = ? AND I_PRODUCT_ID = ? AND B_DELETED = 0";
        List<ai_ProductCost> productCosts = CommonDB.findProductCostsWithQuery(
            query, String.valueOf(iInsItemID), String.valueOf(iProductID));
        if (productCosts != null && !productCosts.isEmpty()) {
            return productCosts.get(0);
        }
        return null;
    }

    public static void saveProductCost(ai_ProductCost productCost) {
        saveProductCosts(Collections.singletonList(productCost));
    }

    public static void saveProductCosts(List<ai_ProductCost> productCosts) {
        ArrayList<ai_ProductCost> productCostsToSave = new ArrayList<>();
        for (ai_ProductCost productCost : productCosts) {
            ai_ProductCost existingProductCost;
            if (productCost.iSCostingID > 0) {
                existingProductCost = getProductCostBySCostingID(productCost.iSCostingID);
            } else if (productCost.iInsItemID > 0 && productCost.iProductID > 0) {
                existingProductCost = getProductCostByInsItemID(productCost.iInsItemID, productCost.iProductID);
            } else {
                existingProductCost = null;
            }
            if (existingProductCost != null) {
                productCost.setId(existingProductCost.getId());
            }
            productCostsToSave.add(productCost);
        }
        CommonDB.saveProductCostsInTx(productCostsToSave);
    }

    public static double getTotalCost(int iInsItemID) {
        List<ai_ProductCost> productCosts = getAllProductCosts(iInsItemID);
        double totalCost = 0;
        for (ai_ProductCost productCost : productCosts) {
            totalCost += productCost.dTotalCost;
        }
        return totalCost;
    }

    public static void updateInsItemCost(int iInsItemID, int iPosition, double value) {
        ai_InsItem insItem = db_InsItem.GetInsItem_ByID(iInsItemID);
        if (insItem != null) {
            CommonHelper.SetValue(iPosition, insItem, String.valueOf(value));
            insItem.save();
        }
    }

    public static void deleteAllCosts(long iInsID) {
        List<ai_InsItem> insItems = CommonDB.findInsItemsWithQuery("I_INS_ID = ?", String.valueOf(iInsID));
        List<ai_ProductCost> costs = new ArrayList<>();
        for (ai_InsItem insItem : insItems) {
            List<ai_InsItem> childItems = CommonDB.findInsItemsWithQuery("I_P_INS_ITEM_ID = ? AND I_INS_ID = ?", String.valueOf(insItem.getId()), String.valueOf(iInsID));
            for (ai_InsItem childItem : childItems) {
                List<ai_ProductCost> productCosts = getAllProductCosts(childItem.getId().intValue());
                costs.addAll(productCosts);
            }
        }
        for (ai_ProductCost cost : costs) {
            cost.bDeleted = true;
        }
        CommonDB.saveProductCostsInTx(costs);
    }
}