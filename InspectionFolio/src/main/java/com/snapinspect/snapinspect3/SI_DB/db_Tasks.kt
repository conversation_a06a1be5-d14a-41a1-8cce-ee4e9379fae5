package com.snapinspect.snapinspect3.SI_DB

import android.content.Context
// Removed: import com.orm.SugarRecord
import com.snapinspect.snapinspect3.Helper.CommonDB
import com.snapinspect.snapinspect3.Helper.CommonHelper
import com.snapinspect.snapinspect3.IF_Object.TaskPriority
import com.snapinspect.snapinspect3.IF_Object.ai_NoticeCategory
import com.snapinspect.snapinspect3.IF_Object.ai_Task
import com.snapinspect.snapinspect3.IF_Object.ai_UpdateAssetTask
import com.snapinspect.snapinspect3.activitynew.fragments.TaskCategorySelection
import com.snapinspect.snapinspect3.activitynew.tasks.TaskCompletionStatus
import com.snapinspect.snapinspect3.views.tasks.SelectedFilterData
import com.snapinspect.snapinspect3.views.tasks.TaskFilterData
import com.snapinspect.snapinspect3.views.tasks.TaskPrioritySelection
import com.snapinspect.snapinspect3.views.tasks.TaskStatusSelection
import java.util.*

object db_Tasks {

    fun updateLastSync(iSAssetID: Int, dtLastSync: String, dtUpdate: Date = Date()) {
        val taskUpdate = updateAssetTask(iSAssetID) ?: ai_UpdateAssetTask()
        taskUpdate.dtLastSync = dtLastSync
        taskUpdate.dtUpdate = dtUpdate
        CommonDB.save(taskUpdate)
    }

    private fun updateAssetTask(iSAssetID: Int): ai_UpdateAssetTask? {
        return CommonDB.findUpdateAssetTaskByAssetId(iSAssetID)
    }

    fun lastSync(iSAssetID: Int): String? {
        return updateAssetTask(iSAssetID)?.dtLastSync
    }

    fun saveTask(task: ai_Task) {
        saveTasks(listOf(task))
    }

    fun saveTasks(tasks: List<ai_Task>) {
        tasks
            .map { task ->
                getTaskBySTaskID(task.iSNotificationID)?.let {
                    task.id = it.id
                }
                task
            }
            .takeIf { it.isNotEmpty() }?.let { tasksToSave ->
                CommonDB.saveTasksInTransaction(tasksToSave)
            }
    }

    fun getTaskBySTaskID(iSNotificationID: Int): ai_Task? {
        return CommonDB.findTaskByNotificationId(iSNotificationID)
    }

    @JvmStatic
    fun getTask(taskId: Int): ai_Task? {
        return CommonDB.findTaskById(taskId)
    }

    fun getTasks(
        assetId: Int, 
        iPTaskID: Int = 0, 
        filterType: TaskCompletionStatus = TaskCompletionStatus.All
    ): List<ai_Task> {
        var whereClause = "I_PROPERTY_ID = ? AND B_DELETED = 0 AND I_P_TASK_ID = ?"
        val whereArgs = arrayOf("" + assetId, "" + iPTaskID)

        when (filterType) {
            TaskCompletionStatus.All -> {}
            TaskCompletionStatus.InProgress -> {
                whereClause += " AND B_CLOSED = 0"
            }
            TaskCompletionStatus.Completed -> {
                whereClause += " AND B_CLOSED = 1" 
            }
        }

        return CommonDB.findTasksWithQuery(whereClause, *whereArgs)
    }

    fun getTaskFilterData(context: Context): TaskFilterData {
        return TaskFilterData(
            statuses = CommonHelper.GetTaskStatus(context).map(TaskStatusSelection.Companion::fromTaskStatus),
            categories = CommonDB.getNoticeCategories(0)
                .map { 
                    val ai = ai_NoticeCategory()
                    ai.iSNoticeCategoryID = it.iSNoticeCategoryID
                    ai.sName = it.sName ?: ""
                    ai
                }
                .map(TaskCategorySelection.Companion::fromTaskCategory),
            priorities = TaskPriority.entries.toTypedArray().map(TaskPrioritySelection::fromTaskPriority)
        )
    }

    fun getMyTasks(
        iCustomerID: Int? = null,
        selectedFilterData: SelectedFilterData
    ): List<ai_Task> {
        var whereClause = "B_DELETED = 0 AND I_P_TASK_ID = 0"
        val whereArgs = mutableListOf<String>()

        iCustomerID?.let {
            whereClause += " AND ',' || ARR_MEMBER || ',' LIKE '%,' || ? || ',%'"
            whereArgs.add("" + iCustomerID)
        }

        // Add completion status filter
        when (selectedFilterData.taskCompletionStatus) {
            TaskCompletionStatus.All -> {}
            TaskCompletionStatus.InProgress -> {
                whereClause += " AND B_CLOSED = 0"
            }
            TaskCompletionStatus.Completed -> {
                whereClause += " AND B_CLOSED = 1" 
            }
        }

        // Add status filter
        if (selectedFilterData.taskStatuses.isNotEmpty()) {
            val placeholders = selectedFilterData.taskStatuses.joinToString(",") { "?" }
            whereClause += " AND S_STATUS IN ($placeholders)"
            whereArgs.addAll(selectedFilterData.taskStatuses.map(TaskStatusSelection::title))
        }

        // Add category filter
        if (selectedFilterData.taskCategories.isNotEmpty()) {
            val placeholders = selectedFilterData.taskCategories.joinToString(",") { "?" }
            whereClause += " AND I_CATEGORY_ID IN ($placeholders)"
            whereArgs.addAll(selectedFilterData.taskCategories.map { it.id.toString() })
        }

        // Add priority filter
        if (selectedFilterData.taskPriorities.isNotEmpty()) {
            val placeholders = selectedFilterData.taskPriorities.joinToString(",") { "?" }
            whereClause += " AND I_PRIORITY IN ($placeholders)"
            whereArgs.addAll(selectedFilterData.taskPriorities.map { it.id.toString() })
        }

        // Sort by due date
        whereClause += " ORDER BY DT_DATE_DUE DESC"

        return CommonDB.findTasksWithQuery(whereClause, *whereArgs.toTypedArray())
    }

}