package com.snapinspect.snapinspect3.SI_DB;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.Helper.VideoQuality;
import com.snapinspect.snapinspect3.IF_Object.ai_InsType;
import com.snapinspect.snapinspect3.IF_Object.ai_Inspection;
import com.snapinspect.snapinspect3.IF_Object.ai_BugHandler;
import com.snapinspect.snapinspect3.IF_Object.ai_Layout;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.List;


/**
 * Created by TerryS on 8/09/17.
 */

public class db_Inspection {

    public static List<ai_Inspection> GetInspectionBySAssetID(int iSAssetID, boolean bCompleted, boolean bSynced, boolean bExcludeChild){
        try{
            if (iSAssetID > 0){
                String sChild = "";
                if (bExcludeChild){
                    sChild = "_sChild\":\"1\"";
                }
                List<ai_Inspection> lsInspection = CommonDB.findInspectionsWithQuery(
                        "SELECT * FROM AIINSPECTION WHERE I_S_ASSET_ID = ? and B_COMPLETE = ? and B_SYNCED = ? and B_DELETED = 0 and ('' = ? or S_CUSTOM_TWO not like ? ) Order by I_S_INS_ID desc",
                    "" + iSAssetID, "" + (bCompleted ? 1 : 0), "" + (bSynced ? 1 : 0), sChild, "%" + sChild + "%" );

                return lsInspection;
            }
            else{
                return CommonDB.findInspectionsByConditions(bSynced, bCompleted, false);
            }


        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Inspection.DeleteAllAssetInspection", ex);

        }
        return null;
    }
    public static void DeleteAllAssetInspection(int iSAssetID){
        try{
            CommonDB.executeInspectionQuery("UPDATE AIINSPECTION SET B_DELETED = 1 WHERE I_S_Asset_ID = ? AND B_SYNCED = 1 and B_COMPLETE = 1", "" + iSAssetID);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Inspection.DeleteAllAssetInspection", ex);

        }

    }
    public static List<ai_InsType> GetAllInsType(){
        try{
            return CommonDB.findAllInsTypes();
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Inspection.GetAllInsType", ex);

        }
        return null;
    }
    public static ai_InsType GetInsTypeBySInsTypeID(int iSInsTypeID){
        try{
            return CommonDB.findInsTypeBySInsTypeID(iSInsTypeID);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Inspection.GetInsTypeBySInsTypeID", ex);

        }
        return null;
    }
    public static ai_Inspection GetInspection_BySInsID(int iSInsID){
        try{
            return CommonDB.findInspectionById(iSInsID);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Inspection.GetInspection_BySInsID", ex);

        }
        return null;
    }
    public static ai_Inspection GetInspection_ByID(long iInspectionID){
        try{
            return CommonDB.findInspectionById(iInspectionID);
        }catch(Exception ex){
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Inspection.GetInspection_ByID", ex);

        }
        return null;

    }

    public static ai_Layout GetLayoutBySLayoutID(int iSLayoutID) {
        try {
            return CommonDB.findLayoutBySLayoutID(iSLayoutID);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Inspection.GetLayoutBySLayoutID", ex);

        }
        return null;
    }

    /**
     * Get the video size from the inspection type
     * The video size is stored in the sFieldOne of the inspection type
     * @see VideoQuality
     */
    public static VideoQuality getInsTypeVideoSize(long iInsID) {
        try {
            ai_Inspection oInspection = GetInspection_ByID(iInsID);
            if (oInspection == null || oInspection.iSInsTypeID <= 0) return null;

            ai_InsType oInsType = GetInsTypeBySInsTypeID(oInspection.iSInsTypeID);
            if (oInsType == null || StringUtils.isEmpty(oInsType.sFieldOne)) return null;

            String sVideoSize = CommonJson.GetJsonKeyValue("_sVideoSize", oInsType.sFieldOne);
            if (StringUtils.isEmpty(sVideoSize)) {
                return null;
            }

            return VideoQuality.fromQualityName(sVideoSize);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Inspection.getInsTypeVideoSize", ex);
            return Constants.Values.DEFAULT_VIDEO_QUALITY;
        }
    }

    /**
     * Get the photo size from the inspection type
     * The photo size is stored in the sFieldOne of the inspection type
     * @return The photo size as an integer, or 0 if not found
     */
    public static int getInsTypePhotoSize(long iInsID) {
        try {
            ai_Inspection oInspection = GetInspection_ByID(iInsID);
            if (oInspection == null || oInspection.iSInsTypeID <= 0) return 0;

            ai_InsType oInsType = GetInsTypeBySInsTypeID(oInspection.iSInsTypeID);
            if (oInsType == null || StringUtils.isEmpty(oInsType.sFieldOne)) return 0;

            return CommonHelper.getInt(CommonJson.GetJsonKeyValue("_iPhotoSize", oInsType.sFieldOne));
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception_New("Exception", "db_Inspection.getInsTypePhotoSize", ex);
            return 0;
        }
    }
}
