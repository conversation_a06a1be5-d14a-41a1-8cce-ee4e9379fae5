package com.snapinspect.snapinspect3.Scanner.Utils;

import android.graphics.Bitmap;
import android.os.AsyncTask;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

public class SaveFile extends AsyncTask<Object, String, Boolean> {
		
//		private ShowActivity mActivity;
		public SaveFile(/*ShowActivity mainActivity*/) {
//	        mActivity = mainActivity;
	    }
		
		@Override
		protected void onPreExecute() {
			//savefile start progress
		}

		@Override
		protected Boolean doInBackground(Object... params) {
			// TODO Auto-generated method stub
			Bitmap bmp = (Bitmap)params[0];
			String path = (String)params[1];
			boolean result = true;
			FileOutputStream out = null;
			File file = new File(path);

			if (file.exists()) file.delete();

			try {
			    out = new FileOutputStream(path);
			    bmp.compress(Bitmap.CompressFormat.JPEG, 100, out);
			} catch (Exception e) {
				result = false;
			    e.printStackTrace();
			} finally {
			    try {
			        if (out != null) {
			            out.close();
			        }
			    } catch (IOException e) {
			        e.printStackTrace();
			    }
			}
			
			return Boolean.valueOf(result);
		}


		@Override
		protected void onPostExecute(Boolean result) {			//action after file saved

		}
		
}
