package com.snapinspect.snapinspect3.Scanner.Utils;

import com.snapinspect.snapinspect3.Helper.CommonHelper;

import java.io.File;
import java.io.FileFilter;
import java.util.Arrays;
import java.util.Comparator;

public class FileList {
	private static String pDir = "";
	public static File SAVE_DIR;
	public FileList(String dir) {
		// TODO Auto-generated constructor stub
		pDir = "";
		pDir += dir;

		if (pDir.equals(""))
			SAVE_DIR = new File(CommonHelper.sFileRoot);
		else
			SAVE_DIR = new File(CommonHelper.sFileRoot + File.separator + pDir);
	}

  
    public static FileFilter fileFilter = new FileFilter() {
		@Override
		public boolean accept(File pathname) {
			String tmpName = pathname.getName();
			tmpName = tmpName.toLowerCase();
			return tmpName.contains(".jpg");
		}
	};	
		
	public static File[] getFileList(File dir){
		return getFileList(dir, "name");
	}
	
	public static Comparator<File> fileComparator(final String sortType){
		return new Comparator<File>() {

			@Override
			public int compare(File first, File second) {
				if (sortType.equals("name")){
					if (first.isDirectory()){
						if (second.isDirectory()){
							return first.getName().compareToIgnoreCase(second.getName());
						}else{
							return -1;
						}
					}else{
						if (second.isDirectory()){
							return 1;
						}else{
							return first.getName().compareToIgnoreCase(second.getName());
						}
					}
				}
				return 0;
			}
		};
	}
	
	
	public static File[] getFileList(File dir, String sortType){
		if (!dir.exists()){
			dir.mkdir();
			return  null;
		}
		if (dir.isDirectory()){
			File[] result = dir.listFiles(fileFilter);
			Arrays.sort(result, fileComparator(sortType));
			return result;
		}else{
			return null;
		}
	}

	public static boolean deleteFile (File file) {
		boolean ret = false;
		if (file.exists()) {
			ret = file.delete();
		}

		return ret;
	}

	public static boolean deleteDirectory(File path) {
		if( path.exists() ) {
			File[] files = path.listFiles();
			if (files == null) {
				return true;
			}
			for(int i=0; i<files.length; i++) {
				if(files[i].isDirectory()) {
					deleteDirectory(files[i]);
				}
				else {
					files[i].delete();
				}
			}
		}
		return( path.delete() );
	}
}
