package com.snapinspect.snapinspect3.Scanner.Utils

import android.app.Activity
import android.content.IntentSender
import android.net.Uri
import androidx.activity.result.ActivityResult
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.IntentSenderRequest
import com.google.mlkit.vision.documentscanner.GmsDocumentScannerOptions
import com.google.mlkit.vision.documentscanner.GmsDocumentScanning
import com.google.mlkit.vision.documentscanner.GmsDocumentScanningResult
import com.snapinspect.snapinspect3.Helper.CommonUI
import com.snapinspect.snapinspect3.R

object ScannerUtils {
    /**
     * Launches the document scanner activity using the Google Mobile Services (GMS) Document Scanning API.
     */
    @JvmStatic
    fun launchDocumentScanner(
        parentActivity: Activity,
        scannerLauncher: ActivityResultLauncher<IntentSenderRequest>
    ) {
        // Configure document scanner options
        val options = GmsDocumentScannerOptions.Builder()
            .setScannerMode(GmsDocumentScannerOptions.SCANNER_MODE_BASE)
            .setResultFormats(GmsDocumentScannerOptions.RESULT_FORMAT_JPEG)
            .setGalleryImportAllowed(true)
            .setPageLimit(1)
            .build()

        // Get the document scanning client and start the scan
        GmsDocumentScanning.getClient(options)
            .getStartScanIntent(parentActivity)
            .addOnSuccessListener { intentSender: IntentSender ->
                // Launch the scanner activity
                scannerLauncher.launch(IntentSenderRequest.Builder(intentSender).build())
            }
            .addOnFailureListener { e: Exception ->
                // Handle the failure, showing an alert to the user
                CommonUI.ShowAlert(
                    parentActivity,
                    parentActivity.getString(R.string.title_alert_error),
                    e.message ?: parentActivity.getString(R.string.default_error_message)
                )
            }
    }

    /**
     * Retrieves the image Uri from the result of an activity launched with the ActivityResult API.
     */
    @JvmStatic
    fun getImageUriFromActivityResult(activityResult: ActivityResult): Uri? {
        // Extract the scanning result and retrieve the image Uri
        val result = GmsDocumentScanningResult.fromActivityResultIntent(activityResult.data)
        return result?.pages?.firstOrNull()?.imageUri
    }
}