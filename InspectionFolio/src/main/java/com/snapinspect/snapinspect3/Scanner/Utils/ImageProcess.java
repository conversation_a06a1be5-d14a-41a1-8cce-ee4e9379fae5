package com.snapinspect.snapinspect3.Scanner.Utils;

import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.ColorMatrix;
import android.graphics.ColorMatrixColorFilter;
import android.graphics.Matrix;
import android.graphics.Paint;

public class ImageProcess {
	public ImageProcess() {
	}

	public Bitmap setImageFilter (Bitmap src) {

		Paint paint = new Paint();
		Canvas canvas = new Canvas(src);
		ColorMatrix cm = new ColorMatrix();
		// Increase Contrast, Slightly Reduce Brightness
		float contrast = 2;
		float brightness = 0.2f;
		cm.set(new float[]{contrast, 0, 0, 0, brightness, 0,
				contrast, 0, 0, brightness, 0, 0, contrast, 0,
				brightness, 0, 1, 0, 0, 0});

		paint.setColorFilter(new ColorMatrixColorFilter(cm));
		Matrix matrix = new Matrix();
		canvas.drawBitmap(src, matrix, paint);
		return src;
	}

	public Bitmap rotateBitmap (Bitmap bm, float angle) {
		Matrix matrix = new Matrix();

		// rotate the Bitmap
		matrix.postRotate(angle); // anti-clockwise by 90 degrees
		// create a new bitmap from the original using the matrix to transform the result
		Bitmap rotatedBitmap = Bitmap.createBitmap(bm, 0, 0, bm.getWidth(), bm.getHeight(), matrix, true);
		return rotatedBitmap;
	}
	public Bitmap convertColorIntoBlackAndWhiteImage(Bitmap orginalBitmap) {
		ColorMatrix colorMatrix = new ColorMatrix();
		colorMatrix.setSaturation(0);

		ColorMatrixColorFilter colorMatrixFilter = new ColorMatrixColorFilter(
				colorMatrix);

		Bitmap blackAndWhiteBitmap = orginalBitmap.copy(
				Bitmap.Config.ARGB_8888, true);

		Paint paint = new Paint();
		paint.setColorFilter(colorMatrixFilter);

		Canvas canvas = new Canvas(blackAndWhiteBitmap);
		canvas.drawBitmap(blackAndWhiteBitmap, 0, 0, paint);

		return blackAndWhiteBitmap;
	}
}
