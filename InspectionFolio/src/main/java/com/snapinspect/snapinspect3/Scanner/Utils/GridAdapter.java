package com.snapinspect.snapinspect3.Scanner.Utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.BaseAdapter;
import android.widget.ImageView;
import android.widget.TextView;

import com.snapinspect.snapinspect3.R;

import java.io.File;
import java.util.ArrayList;


/**
 * Created by <PERSON> on 4/27/2016.
 */
public class GridAdapter extends BaseAdapter {

    Context context;
    private static LayoutInflater inflater=null;
    private  ArrayList<String> image_paths;

    public GridAdapter(Context mcontext, ArrayList<String> filePaths) {
        context=mcontext;
        image_paths = new ArrayList<>();
        image_paths=(ArrayList<String>)filePaths.clone();

        inflater = ( LayoutInflater )context.
                getSystemService(Context.LAYOUT_INFLATER_SERVICE);

    }

    @Override
    public int getCount() {
        return image_paths.size();
    }

    @Override
    public Object getItem(int position) {
        return position;
    }

    @Override
    public long getItemId(int position) {
        return position;
    }

    public class Holder
    {
        TextView tv;
        ImageView img;
    }
    @Override
    public View getView(final int position, View convertView, ViewGroup parent) {
        Holder holder=new Holder();
        View rowView;

        rowView = inflater.inflate(R.layout.grid_item_layout, null);
        holder.tv= rowView.findViewById(R.id.nameLab);
        holder.img= rowView.findViewById(R.id.grid_image_view);

        holder.tv.setText(String.valueOf(position+1));
        BitmapFactory.Options bmOptions = new BitmapFactory.Options();
        Bitmap bitmap = BitmapFactory.decodeFile(image_paths.get(position), bmOptions);


        if (bitmap != null) {
            Matrix thm_matrix = new Matrix();
            float thum_scaleWidth = (float) 0.125;
            float thum_scaleHeight = (float) 0.125;
            thm_matrix.postScale(thum_scaleWidth, thum_scaleHeight);
            Bitmap thumb = Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), thm_matrix, false);
            holder.img.setImageBitmap(thumb);

            bitmap.recycle();
        }

        ImageView delBtn = rowView.findViewById(R.id.delBtn);
        if (delBtn != null) {
            delBtn.setOnClickListener(new View.OnClickListener() {

                @Override
                public void onClick(View v) {
//                    Toast.makeText(context, "You Clicked " + String.valueOf(position), Toast.LENGTH_LONG).show();
                    String path = image_paths.get(position);
                    File del_file = new File(path);
                    if (del_file.exists()) {
                        FileList.deleteFile(del_file);
                    }

                    image_paths.remove(position);
                    notifyDataSetChanged();
                }
            });
        }
        return rowView;
    }

}