package com.snapinspect.snapinspect3.Scanner.Utils;

import android.content.Context;
import android.graphics.Bitmap;

import android.view.Display;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.viewpager.widget.PagerAdapter;

import com.snapinspect.snapinspect3.R;
import com.squareup.picasso.Picasso;
import com.squareup.picasso.Transformation;

import java.io.File;
import java.util.ArrayList;

/**
 * Created by <PERSON> on 4/27/2016.
 */
public class GalleryPagerAdapter extends PagerAdapter {

    public ImageView imgView;
    public TextView name_label;
    private  ArrayList<String> imgs;
    private final Context mContext;
    private final int currIndex = 0;
    private final ViewGroup viewGroup = null;
    private final ViewGroup parent = null;
    protected OnItemChangeListener mOnItemChangeListener;
    int mSelection = -1;
    LayoutInflater _inflater;

    public GalleryPagerAdapter(Context context, ArrayList<String> filePaths) {
        mContext = context;
        imgs = new ArrayList<>();
        this.imgs = (ArrayList<String>)filePaths.clone();
        _inflater = (LayoutInflater) context.getSystemService(Context.LAYOUT_INFLATER_SERVICE);
    }

    @Override
    public void setPrimaryItem(ViewGroup container, final int position, Object object) {
        super.setPrimaryItem(container, position, object);
        if (mSelection == position) return;

        mSelection = position;
        if (mOnItemChangeListener != null) mOnItemChangeListener.onItemChange(mSelection);
    }

    @Override
    public Object instantiateItem(ViewGroup container, int position) {
        View itemView = _inflater.inflate(R.layout.gallery_item_layout, container, false);
        String layTag  = String.valueOf(position);
        itemView.setTag(layTag);
        container.addView(itemView);

        imgView = itemView.findViewById(R.id.gl_imageView);
        name_label = itemView.findViewById(R.id.page_label);
        name_label.setText(getPageTitle(position));
        String photoTag = "photo" + position;
        imgView.setTag(photoTag);

        Transformation transformation = new Transformation() {

            @Override
            public Bitmap transform(Bitmap source) {
                WindowManager wm = (WindowManager) mContext.getSystemService(Context.WINDOW_SERVICE);
                Display display = wm.getDefaultDisplay();
                int targetWidth = display.getWidth();

                float aspectRatio = (float) source.getHeight() / (float) source.getWidth();
                int targetHeight = (int) (targetWidth * aspectRatio);
                if (targetHeight > (display.getHeight())) {
                    targetHeight = display.getHeight();
                }

                Bitmap result = Bitmap.createScaledBitmap(source, targetWidth, targetHeight, false);
                if (result != source) {
                    source.recycle();
                }
                return result;
            }

            @Override
            public String key() {
                return "transformation" + " desiredWidth";
            }
        };

        Picasso.get()
                .load(new File(imgs.get(position)))
                .transform(transformation)
                .into(imgView);

        return itemView;
    }

    @Override
    public void destroyItem(ViewGroup collection, int position, Object view) {
        collection.removeView((View) view);
    }

    public void removeView(int position){
        imgs.remove(position);
        notifyDataSetChanged();
    }

    @Override
    public int getCount() {
        return imgs.size();
    }

    @Override
    public boolean isViewFromObject(View view, Object object) {
        return view == object;
    }

    @Override
    public CharSequence getPageTitle(int position) {
       String title = position + 1 + " of " + getCount();

        return title;
    }
    public void setOnItemChangeListener(OnItemChangeListener listener) { mOnItemChangeListener = listener; }
    public interface OnItemChangeListener
    {
        void onItemChange(int currentPosition);
    }
    public void rotateImage() {
        Bitmap bitmap = imgView.getDrawingCache();

//        this.instantiateItem(parent, currIndex);
    }

    public int getCurrIndex() {
        return currIndex;
    }
}
