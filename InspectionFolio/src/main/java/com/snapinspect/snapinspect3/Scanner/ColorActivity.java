package com.snapinspect.snapinspect3.Scanner;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.location.Location;
import android.os.Bundle;
import android.util.Size;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.ImageView;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.IF_Object.O_FileName;
import com.snapinspect.snapinspect3.R;
import com.snapinspect.snapinspect3.Scanner.Utils.FileList;
import com.snapinspect.snapinspect3.Scanner.Utils.ImageProcess;
import com.snapinspect.snapinspect3.activitynew.if_DisplayFloorPlan;
import com.snapinspect.snapinspect3.util.BitmapUtils;

import java.io.File;

public class ColorActivity extends Activity {
    private static final int IMAGE_MAX_WIDTH = 1500;
    private static final int IMAGE_MAX_HEIGHT = 1500;

    private ImageView imgView;
    private Bitmap finalBmp;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_color);
        getActionBar().setDisplayHomeAsUpEnabled(true);
        getActionBar().setTitle("");

        Bitmap orginalBitmap = getOrigBitmapfromPath(getIntent().getStringExtra(Constants.Extras.CROP_PATH));
        if (orginalBitmap.getWidth() > IMAGE_MAX_WIDTH || orginalBitmap.getHeight() > IMAGE_MAX_HEIGHT) {
            finalBmp = BitmapUtils.scaleBitmap(orginalBitmap, new Size(IMAGE_MAX_WIDTH, IMAGE_MAX_HEIGHT));
        } else {
            finalBmp = orginalBitmap;
        }

        imgView = findViewById(R.id.colorImgView);
        imgView.setImageBitmap(finalBmp);

        // request location permission
        CommonValidate.requestPermissions(this, Constants.LOCATION_PERMISSIONS);
    }

    @Override
    protected void onResume() {
        super.onResume();
        GeoLocationManager.getInstance(this).startUpdateLocation();
    }

    @Override
    protected void onPause() {
        super.onPause();
        GeoLocationManager.getInstance(this).stopUpdateLocation();
    }

    public Bitmap getOrigBitmapfromPath (String path) {
        Bitmap bitmap = null;
        if (path != null) {
            File imgfile = new File(path);
            BitmapFactory.Options bmOptions = new BitmapFactory.Options();
            bitmap = BitmapFactory.decodeFile(imgfile.getAbsolutePath(), bmOptions);
        }

        return bitmap;
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        // Inflate the menu; this adds items to the action bar if it is present.
        getMenuInflater().inflate(R.menu.menu_scan_color, menu);
        return true;
    }

    @SuppressLint("NonConstantResourceId")
    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        int id = item.getItemId();
        switch (id) {
            case android.R.id.home:
                deletePhotoAndFinish();
                break;
            case R.id.action_done:
                int iInsItemID = getIntent().getIntExtra(Constants.Extras.iInsItemID, 0);
                int iPosition = getIntent().getIntExtra(Constants.Extras.iPosition, 0);
                int iSAssetID = getIntent().getIntExtra(Constants.Extras.iSAssetID, 0);

                Location currentLocation = GeoLocationManager.getInstance(this).getLocation();
                O_FileName oFileName = O_FileName.getPhotoFileName();
                CommonHelper.SaveImage(oFileName.sFilePath, finalBmp);
                /*Create thumbnail*/
                CommonHelper.SaveThumb_Height(oFileName.sThumbNail, finalBmp, 180);

                long iPhotoID = 0;
                if (oFileName.sThumbNail != null && oFileName.sFilePath != null) {
                    iPhotoID = CommonDB.InsertPhoto(
                            this, iInsItemID, oFileName.sThumbNail, oFileName.sFilePath, currentLocation);
                }

                if (!(CommonHelper.bFileExist(oFileName.sFilePath) && CommonHelper.GetFileLength(oFileName.sFilePath) > 0)) {
                    runOnUiThread(() -> CommonUI.ShowAlert(
                                    this,
                                    "Error",
                                    "Photo can not be saved, please check the storage, permission and restart the app"
                            )
                    );
                    return false;
                }

                if (iInsItemID > 0) {
                    CommonDB.updateInsItemValue(iInsItemID, iPosition, iPhotoID);
                } else if (iSAssetID > 0) {
                    startActivity(if_DisplayFloorPlan.newIntent(this, iSAssetID, iPhotoID));
                }
                clearBmp();
                finish();
                break;
            case R.id.action_menu_rotation:
                if (finalBmp != null) {
                    finalBmp = new ImageProcess().rotateBitmap(finalBmp, 90);
                    imgView.setImageBitmap(finalBmp);
                }
                break;
            default:
                break;
        }

        return super.onOptionsItemSelected(item);
    }

    @Override
    public void onBackPressed() {
        super.onBackPressed();
        deletePhotoAndFinish();
    }

    public boolean clearBmp() {
        boolean ret = false;
        FileList fileL = new FileList(".ORIGINAL");
        File filterDir = FileList.SAVE_DIR;
        if (filterDir.exists())
            ret = FileList.deleteDirectory(filterDir);
        FileList orgL = new FileList(".CROP_PATH");
        File orgDir = FileList.SAVE_DIR;
        if (orgDir.exists())
            ret = FileList.deleteDirectory(orgDir);

        return ret;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (finalBmp != null) {
            finalBmp.recycle();
            finalBmp = null;
        }
    }
    private void deletePhotoAndFinish() {
        // delete the photo
        CommonDB.DeletePhotoByPhotoID(
            getIntent().getLongExtra(Constants.Extras.iPhotoID, 0), true);
        finish();
    }

}
