package com.snapinspect.snapinspect3.IF_Object;

import android.content.Context;
import android.text.TextUtils;
// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonDB_Assets;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.database.entities.Layout;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/03/14.
 */
//AILAYOUT
public class ai_Layout {
    public Long id;
    //I_S_LAYOUT_ID
    public int iSLayoutID;
    //I_SP_LAYOUT_ID
    public int iSPLayoutID;
    //S_PTC
    public String sPTC;
    //S_Q_TYPE
    public String sQType;
    //S_NAME
    public String sName;
    //S_FV_ONE_CONFIG
    public String sFVOneConfig;
    //S_FV_TWO_CONFIG
    public String sFVTwoConfig;
    //S_FV_THREE_CONFIG
    public String sFVThreeConfig;
    //S_FV_FOUR_CONFIG
    public String sFVFourConfig;
    //S_FV_FIVE_CONFIG
    public String sFVFiveConfig;
    //S_FV_SIX_CONFIG
    public String sFVSixConfig;
    //S_SV_ONE_CONFIG
    public String sSVOneConfig;
    //S_SV_TWO_CONFIG
    public String sSVTwoConfig;
    //S_SV_THREE_CONFIG
    public String sSVThreeConfig;
    //S_SV_FOUR_CONFIG
    public String sSVFourConfig;
    //S_SV_FIVE_CONFIG
    public String sSVFiveConfig;
    //S_SV_SIX_CONFIG
    public String sSVSixConfig;
    //S_F_CONFIG
    public String sFConfig;
    //S_S_CONFIG
    public String sSConfig;
  //  public boolean bAutoAdd;
    //sFieldOne is used to record number of selected layout on the if_Layout screen, reserved for that
    //S_FIELD_ONE
    public String sFieldOne;

    //sFieldTwo is used to record iMark of this layout.
    //S_FIELD_TWO
    public String sFieldTwo;

    // sFieldThree is used to record iSort of this layout.
    //S_FIELD_THREE
    public String sFieldThree;
    //B_DELETED
    public boolean bDeleted;
    public ai_Layout(){

    }
    public ai_Layout( int iSLayoutID, int iSPLayoutID, String sPTC, String sQType, String sName, String sFVOneConfig, String sFVTwoConfig,
                     String sFVThreeConfig, String sFVFourConfig, String sFVFiveConfig, String sFVSixConfig, String sSVOneConfig, String sSVTwoConfig,
                     String sSVThreeConfig, String sSVFourConfig, String sSVFiveConfig, String sSVSixConfig, String sFConfig, String sSConfig,
                      String sFieldOne, String sFieldTwo, String sFieldThree, boolean bDeleted) {
        this.iSLayoutID = iSLayoutID;
        this.iSPLayoutID = iSPLayoutID;
        this.sPTC = sPTC;
        this.sQType = sQType;
        this.sName = sName;
        this.sFVOneConfig = sFVOneConfig;
        this.sFVTwoConfig = sFVTwoConfig;
        this.sFVThreeConfig = sFVThreeConfig;
        this.sFVFourConfig = sFVFourConfig;
        this.sFVFiveConfig = sFVFiveConfig;
        this.sFVSixConfig = sFVSixConfig;
        this.sSVOneConfig = sSVOneConfig;
        this.sSVTwoConfig = sSVTwoConfig;
        this.sSVThreeConfig = sSVThreeConfig;
        this.sSVFourConfig = sSVFourConfig;
        this.sSVFiveConfig = sSVFiveConfig;
        this.sSVSixConfig = sSVSixConfig;
        this.sFConfig = sFConfig;
        this.sSConfig = sSConfig;
       // this.bAutoAdd = bAutoAdd;
        this.sFieldOne = sFieldOne;
        this.sFieldTwo = sFieldTwo;
        this.sFieldThree = sFieldThree;
        this.bDeleted = bDeleted;
    }

    public ai_Layout(JSONObject obj) {
        iSLayoutID = obj.optInt("iLayoutID", 0);
        sName = obj.optString("sName");
    }

    public ai_Layout(int iSLayoutID, String sName) {
        this.iSLayoutID = iSLayoutID;
        this.sName = sName;
    }

    public ai_Layout(int iSLayoutID, String sName, int iCount, int iOrder) {
        this.iSLayoutID = iSLayoutID;
        this.sName = sName;
        this.sFieldOne = String.valueOf(iCount);
        this.sFieldThree = String.valueOf(iOrder);
    }

    // Constructor from Room entity
    public ai_Layout(Layout roomEntity) {
        this.id = roomEntity.id;
        this.iSLayoutID = roomEntity.iSLayoutID != null ? roomEntity.iSLayoutID : 0;
        this.iSPLayoutID = roomEntity.iSPLayoutID != null ? roomEntity.iSPLayoutID : 0;
        this.sPTC = roomEntity.sPTC;
        this.sQType = roomEntity.sQType;
        this.sName = roomEntity.sName;
        this.sFVOneConfig = roomEntity.sFVOneConfig;
        this.sFVTwoConfig = roomEntity.sFVTwoConfig;
        this.sFVThreeConfig = roomEntity.sFVThreeConfig;
        this.sFVFourConfig = roomEntity.sFVFourConfig;
        this.sFVFiveConfig = roomEntity.sFVFiveConfig;
        this.sFVSixConfig = roomEntity.sFVSixConfig;
        this.sSVOneConfig = roomEntity.sSVOneConfig;
        this.sSVTwoConfig = roomEntity.sSVTwoConfig;
        this.sSVThreeConfig = roomEntity.sSVThreeConfig;
        this.sSVFourConfig = roomEntity.sSVFourConfig;
        this.sSVFiveConfig = roomEntity.sSVFiveConfig;
        this.sSVSixConfig = roomEntity.sSVSixConfig;
        this.sFConfig = roomEntity.sFConfig;
        this.sSConfig = roomEntity.sSConfig;
        this.sFieldOne = roomEntity.sFieldOne;
        this.sFieldTwo = roomEntity.sFieldTwo;
        this.sFieldThree = roomEntity.sFieldThree;
        this.bDeleted = roomEntity.bDeleted != null ? roomEntity.bDeleted : false;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public int getDefaultNumOfAreas(Context context, int iSAssetID) {
        int iDefNum = 0;
        try {
            // Find the asset attribute with the same name and _bLayout = 1 in the sCustom1 field
            String assetPropertyKey = CommonJson.getAssetPropertyKeyWhenCustom1HasDefLayout(context, sName);
            if (!TextUtils.isEmpty(assetPropertyKey)) {
                ai_Assets oAsset = CommonDB_Assets.GetAssetBy_iSAssetID(iSAssetID);
                // Get the value of the CV_XXX field in the sCustom1 field of the asset sCustom1 field
                if (oAsset != null)
                    iDefNum = CommonHelper.getInt(CommonJson.GetJsonKeyValue(assetPropertyKey, oAsset.sFieldOne));
            }

            // if the value is not valid, then get the value from the layout sFConfig field
            if (iDefNum <= 0 && sFConfig != null) {
                iDefNum = CommonHelper.getInt(CommonJson.GetJsonKeyValue("_iDefNum", sFConfig));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return iDefNum;
    }

    // Get the order of the layout
    public int getOrder() {
        return CommonHelper.getInt(sFieldThree);
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_Layout> find(Class<ai_Layout> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static List<ai_Layout> findWithQuery(Class<ai_Layout> clazz, String query) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_Layout findById(Class<ai_Layout> clazz, Object id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_Layout> listAll(Class<ai_Layout> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static void deleteAll(Class<ai_Layout> clazz) {
        // Simplified implementation for compatibility
    }

    public static void deleteAll(Class<ai_Layout> clazz, String whereClause, String... params) {
        // Simplified implementation for compatibility with where clause
    }

    public boolean delete() {
        // Simplified implementation for compatibility
        return true;
    }
}
