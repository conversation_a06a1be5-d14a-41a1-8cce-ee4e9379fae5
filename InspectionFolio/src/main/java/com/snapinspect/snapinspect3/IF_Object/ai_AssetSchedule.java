package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;

public class ai_AssetSchedule extends v_Schedule {
    public int iInspectorID;
    public int iInsTypeID;
    public int iSInspectionID;
    public String sInspectorName;
    public String dtSchedule;
    public boolean bDeleted;
    public String sAdditionalInfo;
    public int iCompanyID;
    public String sPM;
    public String dtDue;
    public String sStatus;
    public String sStatusColor;

    public ai_AssetSchedule(JSONObject jsonObject) {
        iSScheduleID = jsonObject.optInt("iScheduleID", 0);
        iInspectorID = jsonObject.optInt("iInspectorID", 0);
        iSInsTypeID = jsonObject.optInt("iInsTypeID", 0);
        iSInspectionID = jsonObject.optInt("iInspectionID", 0);
        sInspectorName = jsonObject.optString("sInspectorName");
        sType = jsonObject.optString("sType");
        sPTC = jsonObject.optString("sPTC");
        sInsTitle = jsonObject.optString("sInsTitle");
        dtSchedule = jsonObject.optString("dtSchedule");
        bCompleted = jsonObject.optBoolean("bCompleted", false);
        bDeleted = jsonObject.optBoolean("bDeleted", false);

        sAdditionalInfo = jsonObject.optString("sAdditionalInfo");
        sCustom1 = jsonObject.optString("sCustom1");
        sCustom2 = jsonObject.optString("sCustom2");
        bRecurred = jsonObject.optBoolean("bRecur", false);
        sRRule = jsonObject.optString("sRRule");
        sEXRule = jsonObject.optString("sEXRule");
        iAssetID = 0;
        iSAssetID = jsonObject.optInt("iAssetID", 0);
        iPSAssetID = jsonObject.optInt("iPAssetID", 0);
        iPPSAssetID = jsonObject.optInt("iPPAssetID", 0);
        iCustomerID = jsonObject.optInt("iCustomerID", 0);
        iCompanyID = jsonObject.optInt("iCompanyID", 0);
        iGroupID = jsonObject.optInt("iGroupID", 0);
        bApartment = jsonObject.optBoolean("bApartment", false);
        sBuildingAddress = jsonObject.optString("sBuildingAddress");
        sUnitAddress = jsonObject.optString("sUnitAddress");
        sRoomAddress = jsonObject.optString("sRoomAddress");
        sPM = jsonObject.optString("sPM");
        dtDue = jsonObject.optString("dtDue");
        sRef = jsonObject.optString("sRef");
        sStatus = jsonObject.optString("sA_Status");
        sStatusColor = jsonObject.optString("sA_S_Code");
        iUnixTime = getUnixTime(dtSchedule, "MMM dd, yyyy HH:mm");

        sAddress1 = sBuildingAddress;
        ArrayList<String> lsAddress = new ArrayList<>();
        if (!StringUtils.isEmpty(sUnitAddress)) lsAddress.add(sUnitAddress);
        if (!StringUtils.isEmpty(sRoomAddress)) lsAddress.add(sRoomAddress);
        sAddress2 = String.join(", ", lsAddress);
    }

    private long getUnixTime(String dtSchedule, String format) {
        Date date = DateUtils.parse(dtSchedule, format);
        if (date == null) return 0;
        return date.getTime();
    }
}
