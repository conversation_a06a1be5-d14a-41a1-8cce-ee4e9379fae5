package com.snapinspect.snapinspect3.IF_Object;

import android.database.Cursor;
import android.text.TextUtils;

import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.util.ArrayUtils;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;

/**
 * Created by terry<PERSON> on 26/03/19.
 */

public class v_Schedule extends v_Asset {
    public int iScheduleID;
    public int iSScheduleID;
   // public int iSAssetID;
    public int iSInsTypeID;
    public String sPTC;
    public String sInsTitle;
    public String sDateTime;
    public long iUnixTime;
    public boolean bCompleted;

    public String sCustom1;
    public String sCustom2;
    public String sType;
    public String sAddress1;
    public String sAddress2;

    public String sRRule;
    public String sEXRule;

    public boolean bRecurred = false;

    public v_Schedule() { }

    public v_Schedule(Cursor oCursor) {
        super(oCursor);
        this.iScheduleID = oCursor.getInt(oCursor.getColumnIndex( "iScheduleID"));
        this.iSScheduleID = oCursor.getInt(oCursor.getColumnIndex( "iSScheduleID"));
        this.iSInsTypeID = oCursor.getInt(oCursor.getColumnIndex( "iSInsTypeID"));
        this.sPTC = oCursor.getString(oCursor.getColumnIndex( "sPTC"));
        this.sInsTitle = oCursor.getString(oCursor.getColumnIndex( "sInsTitle"));
        this.sDateTime = oCursor.getString(oCursor.getColumnIndex( "sDateTime"));
        try {
            this.iUnixTime = oCursor.getLong(oCursor.getColumnIndex("iUnixTime"));
        }catch(Exception eee){}
        this.bCompleted = oCursor.getInt(oCursor.getColumnIndex( "bCompleted")) == 1;
        this.sType = oCursor.getString(oCursor.getColumnIndex("sType"));
        this.sCustom1 = oCursor.getString(oCursor.getColumnIndex( "sCustom1"));
        this.sCustom2 = oCursor.getString(oCursor.getColumnIndex( "sCustom2"));
        this.sAddress1 = oCursor.getString(oCursor.getColumnIndex("sAddress1"));
        this.sAddress2 = oCursor.getString(oCursor.getColumnIndex("sAddress2"));

        this.sRRule = oCursor.getString(oCursor.getColumnIndex("sRRule"));
        this.sEXRule = oCursor.getString(oCursor.getColumnIndex("sEXRule"));
    }

    public String GetAddress() {
        try {
            ArrayList<String> lsAddress = new ArrayList<>();

            if (!StringUtils.isEmpty(sRoomAddress)) lsAddress.add(sRoomAddress.trim());
            if (!StringUtils.isEmpty(sUnitAddress)) lsAddress.add(sUnitAddress.trim());
            if (!StringUtils.isEmpty(sBuildingAddress)) lsAddress.add(sBuildingAddress.trim());

            // if no room, unit or building address, then use schedule address1 and address2
            if (ArrayUtils.isEmpty(lsAddress)) {
                if (!StringUtils.isEmpty(sAddress1)) lsAddress.add(sAddress1.trim());
                if (!StringUtils.isEmpty(sAddress2)) lsAddress.add(sAddress2.trim());
            }
            return String.join(", ", lsAddress);
        } catch(Exception ex) {
            return "";
        }
    }

    public v_Schedule copy() {
        v_Schedule schedule = new v_Schedule();

		schedule.iAssetID = iAssetID;
		schedule.iSAssetID = iSAssetID;
		schedule.iPSAssetID = iPSAssetID;
		schedule.iPPSAssetID = iPPSAssetID;
		schedule.sRef = sRef;
		schedule.sBuildingAddress = sBuildingAddress;
		schedule.sUnitAddress = sUnitAddress;
		schedule.sRoomAddress = sRoomAddress;
		schedule.iCustomerID = iCustomerID;
		schedule.iGroupID = iGroupID;
		schedule.bApartment = bApartment;
		schedule.sA_Custom1 = sA_Custom1;
		schedule.sA_Custom2 = sA_Custom2;
		schedule.sTag = sTag;
			
		schedule.iScheduleID = iScheduleID;
		schedule.iSScheduleID = iSScheduleID;
		schedule.iSAssetID = iSAssetID;
		schedule.iSInsTypeID = iSInsTypeID;
		schedule.sPTC = sPTC;
		schedule.sInsTitle = sInsTitle;
		schedule.sDateTime = sDateTime;
		schedule.iUnixTime = iUnixTime;
		schedule.bCompleted = bCompleted;

		schedule.sCustom1 = sCustom1;
		schedule.sCustom2 = sCustom2;
		schedule.sType = sType;
		schedule.sAddress1 = sAddress1;
		schedule.sAddress2 = sAddress2;

		schedule.sRRule = sRRule;
		schedule.sEXRule = sEXRule;
        return schedule;
    }

    public boolean isInsStarted() {
        return !StringUtils.isEmpty(CommonJson.GetJsonKeyValue("iInsID", sCustom1));
    }

    public boolean isInsInProcess() {
        return isInsStarted() && !isInsCompleted();
    }

    public boolean isInsCompleted() {
        return !StringUtils.isEmpty(CommonJson.GetJsonKeyValue("iInsIDCom", sCustom1));
    }

    public boolean hasREmail() {
        return !StringUtils.isEmpty(CommonJson.GetJsonKeyValue("REmail", sCustom1));
    }

    public String getScheduleDate() {
        return DateUtils.format(new Date(iUnixTime), DateUtils.SCHEDULE_TIME_FORMAT);
    }

}
