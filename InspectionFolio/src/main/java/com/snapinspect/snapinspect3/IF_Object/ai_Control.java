package com.snapinspect.snapinspect3.IF_Object;

/**
 * Created by <PERSON><PERSON><PERSON> on 3/16/19.
 */

public class ai_Control {
    // public String sControlType;
    private String sPrompt;
    private String sLabel;
    private String sValue;
    private int iControlType;

    public void setControlType(int type) {
        iControlType = type;
    }

    public void setsValue(String str) {
        this.sValue = str;
    }
    
    // Getter methods for Room compatibility
    public String getsPrompt() {
        return sPrompt;
    }
    
    public String getsLabel() {
        return sLabel;
    }
    
    public String getsValue() {
        return sValue;
    }
    
    public int getiControlType() {
        return iControlType;
    }
}
