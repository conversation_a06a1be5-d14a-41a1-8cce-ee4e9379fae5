package com.snapinspect.snapinspect3.IF_Object;

import android.database.Cursor;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;

public class v_RequestInspection extends v_Schedule {

    public enum State {
        UNKNOWN, PENDING, ONGOING, COMPLETED, UPLOADED
    }

    public int iInspectionID;
    public String sInspectionTitle;
    public String sTitle;
    public boolean bComplete;
    public boolean bSynced;
    public int iSInsID;
    public String sInsSearchTerm;

    public v_RequestInspection(Cursor oCursor) {
        super(oCursor);
        iInspectionID = oCursor.getInt(oCursor.getColumnIndex("iInspectionID"));
        sInspectionTitle = oCursor.getString(oCursor.getColumnIndex("sInspectionTitle"));
        sTitle = oCursor.getString(oCursor.getColumnIndex("sTitle"));
        bComplete = oCursor.getInt(oCursor.getColumnIndex("bComplete")) == 1;
        bSynced = oCursor.getInt(oCursor.getColumnIndex("bSynced")) == 1;
        iSInsID = oCursor.getInt(oCursor.getColumnIndex("iSInsID"));
        sInsSearchTerm = oCursor.getString(oCursor.getColumnIndex("sInsSearchTerm"));
    }

    public String getAddress() {
        if (!StringUtils.isEmpty(sTitle)) return sTitle;
        ArrayList<String> addressItems = new ArrayList<>();
        if (!StringUtils.isEmpty(sAddress1) && !sAddress1.trim().isEmpty()) {
            addressItems.add(sAddress1.trim());
        }
        if (!StringUtils.isEmpty(sAddress2) && !sAddress2.trim().isEmpty()) {
            addressItems.add(sAddress2.trim());
        }
        return String.join(", ", addressItems);
    }

    public String getInsTitle() {
        if (!StringUtils.isEmpty(sInspectionTitle)) return sInspectionTitle;
        return sInsTitle;
    }

    public State getState() {
        if (iInspectionID == 0 && iSScheduleID > 0) return State.PENDING;
        if (iInspectionID > 0 && !bComplete && !bSynced) return State.ONGOING;
        if (iInspectionID > 0 && bComplete && !bSynced) return State.COMPLETED;
        if (iInspectionID > 0 && bComplete && bSynced) return State.UPLOADED;
        return State.UNKNOWN;
    }

}
