package com.snapinspect.snapinspect3.IF_Object;

import android.content.Context;
// Removed: import com.orm.SugarRecord;
// Removed: import com.orm.dsl.Unique;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonProduct;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.SI_DB.db_Product;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.json.JSONObject;

import java.util.Date;

public class ai_ProductCost {
    public Long id;
    public int iSCostingID;
    public int iAssetID;
    public int iInspectionID;
    public int iInsItemID;
    public int iPInsItemID;
    public int iTaskID;
    public int iProductID;
    public double dUnit;
    public double dUnitCost;
    public double dTotalCost;
    public String sNotes;
    public String sCustom1;
    public String sCustom2;
    public String sCustom3;
    public boolean bDeleted;
    public int iCreatedBy;
    public int iUpdatedBy;
    public String dtUpdate;
    public String dtDateTime;

    public String sUnitName() {
        ai_Product product = getAssociatedProduct();
        String unitName = (product != null) ? product.sUnitName : "";
        return StringUtils.ifEmpty(unitName, Constants.Values.kUnitName) + (dUnit > 1 ? "s" : "");
    }

    public ai_Product getAssociatedProduct() {
        return db_Product.getProductBySProductID(iProductID);
    }

    public ai_ProductCost() {
    }

    public ai_ProductCost(int iSCostingID, int iAssetID, int iInspectionID, int iInsItemID, int iPInsItemID, int iTaskID, int iProductID, double dUnit, double dUnitCost, double dTotalCost, String sNotes, String sCustom1, String sCustom2, String sCustom3, boolean bDeleted, int iCreatedBy, int iUpdatedBy, String dtUpdate, String dtDateTime) {
        this.iSCostingID = iSCostingID;
        this.iAssetID = iAssetID;
        this.iInspectionID = iInspectionID;
        this.iInsItemID = iInsItemID;
        this.iPInsItemID = iPInsItemID;
        this.iTaskID = iTaskID;
        this.iProductID = iProductID;
        this.dUnit = dUnit;
        this.dUnitCost = dUnitCost;
        this.dTotalCost = dTotalCost;
        this.sNotes = sNotes;
        this.sCustom1 = sCustom1;
        this.sCustom2 = sCustom2;
        this.sCustom3 = sCustom3;
        this.bDeleted = bDeleted;
        this.iCreatedBy = iCreatedBy;
        this.iUpdatedBy = iUpdatedBy;
        this.dtUpdate = dtUpdate;
        this.dtDateTime = dtDateTime;
    }

    public ai_ProductCost(JSONObject data) {
        try {
            this.iSCostingID = data.optInt("iSCostingID", 0);
            this.iAssetID = data.optInt("iAssetID", 0);
            this.iInspectionID = data.optInt("iInspectionID", 0);
            this.iInsItemID = data.optInt("iInsItemID", 0);
            this.iPInsItemID = data.optInt("iPInsItemID", 0);
            this.iTaskID = data.optInt("iTaskID", 0);
            this.iProductID = data.optInt("iProductID", 0);
            this.dUnit = data.optDouble("dUnit", 0);
            this.dUnitCost = data.optDouble("dUnitCost", 0);
            this.dTotalCost = data.optDouble("dTotalCost", 0);
            this.sNotes = data.optString("sNotes", "");
            this.sCustom1 = data.optString("sCustom1", "");
            this.sCustom2 = data.optString("sCustom2", "");
            this.sCustom3 = data.optString("sCustom3", "");
            this.bDeleted = data.optBoolean("bDeleted", false);
            this.iCreatedBy = data.optInt("iCreatedBy", 0);
            this.iUpdatedBy = data.optInt("iUpdatedBy", 0);
            this.dtUpdate = data.optString("dtUpdate", "");
            this.dtDateTime = data.optString("dtDateTime", "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public ai_ProductCost(Context context, ai_Product product, int iInsItemID) {
        this.iProductID = product.iSProductID;
        this.dUnit = 1;
        this.dUnitCost = product.dUnitCost;
        this.dTotalCost = product.dUnitCost * this.dUnit;
        this.iInsItemID = iInsItemID;
        this.iCreatedBy = CommonHelper.getCurrentUserID(context);
        this.iUpdatedBy = CommonHelper.getCurrentUserID(context);
        this.dtUpdate = DateUtils.format(new Date(), DateUtils.DATE_RANGE_FORMAT);
        this.dtDateTime = DateUtils.format(new Date(), DateUtils.DATE_RANGE_FORMAT);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
