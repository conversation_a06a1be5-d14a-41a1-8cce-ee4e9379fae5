package com.snapinspect.snapinspect3.IF_Object;

// Removed: import com.orm.SugarRecord;

import java.util.Date;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/02/18.
 */

public class ai_Comment {
    public Long id;
    public int iCommentID;
    public int iCompanyID;
    public int iCustomerID;
    public long iInspectionID;

    public String sDate;
    public String sTime;
    public Date date;
    public String sName;
    public String sDescription;
    public String sType;
    public String sCustom1;
    public String iTaskID;
    public String sPhotoUrl;

    public int colorResId;
    public Boolean bDeleted;

    public ai_Comment(){

    }
    public ai_Comment(int iCommentID,
            int iCompanyID,
            int iCustomerID,
            long iInspectionID,
            String sDate,
            String sTime,
            Date date,
            String sName,
            String sDescription,
            String sType,
            String sCustom1,
            String iTaskID ){

        this.iCommentID = iCommentID;
        this.iCompanyID = iCompanyID;
        this.iCustomerID = iCustomerID;
        this.iInspectionID = iInspectionID;
        this.sDate = sDate;
        this.sTime = sTime;
        this.date = date;
        this.sName = sName;
        this.sDescription = sDescription;
        this.sType = sType;
        this.sCustom1 = sCustom1;
        this.iTaskID = iTaskID;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
