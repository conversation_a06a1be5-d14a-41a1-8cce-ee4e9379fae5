package com.snapinspect.snapinspect3.IF_Object;

import android.content.Context;

// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Created by terrysun1 on 21/07/15.
 */
//AINOTIFICATION
public class ai_Notification {
    public Long id;
    //I_S_NOTIFICATION_ID
    public int iSNotificationID;
    //I_INS_ITEM_ID
    public long iInsItemID;
    //I_INS_ID
    public long iInsID;
    //S_TITLE
    public String sTitle;
    //S_DESCRIPTION
    public String sDescription;
    //S_PHOTO_URL
    public String sPhotoURL;
    //I_VIDEO_ID
    public String sVideoID;
    //S_DUE_DATE
    public String sDueDate;
    //I_PRIORITY
    public int iPriority;
    //S_CATEGORY
    public String sCategory;
    //S_CUSTOM_ONE
    public String sCustomOne;
    //S_CUSTOM_TWO
    public String sCustomTwo;
    //B_DELETED
    public boolean bDeleted;
    //DT_DATE_TIME
    public String dtDateTime;

    public ai_Notification() {
        this.dtDateTime = CommonHelper.sDateToString(new Date());
    }

    public ai_Notification(Context ctx, int _iSNotificationID, String _sTitle, String _sDescription,
                           long _iInsItemID, String _sPhotoURL, String _sVideoID, String _sDueDate, int _iPriority, String _sCategory,
                           String _sCustom1, String _sCustom2, boolean _bDeleted) {
        this.iSNotificationID = _iSNotificationID;
        this.sTitle = _sTitle;
        this.sDescription = _sDescription;
        this.iInsItemID = _iInsItemID;
        this.sPhotoURL = _sPhotoURL;
        this.sVideoID = _sVideoID;
        this.sCustomOne = _sCustom1;
        this.sDueDate = _sDueDate;
        this.iPriority = _iPriority;
        this.sCategory = _sCategory;
        this.sCustomTwo = _sCustom2;
        this.bDeleted = _bDeleted;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    @Override
    public String toString() {
        return sTitle;
    }

    public interface CustomKeys {
        String priority = "iPriority";
        String category = "sCategory";
        String dueDate = "sDueDate";
        String assignedToUser = "iAssignedToUser";
    }

    public ai_User getTaskAssignToUser() {
        if (StringUtils.isEmpty(this.sCustomOne)) return null;
        int userId = CommonHelper.getInt(
            CommonJson.GetJsonKeyValue(CustomKeys.assignedToUser, sCustomOne)
        );
        return userId > 0 ? CommonDB.GetUserSugar(userId) : null;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ai_Notification that = (ai_Notification) o;
        return Objects.equals(iSNotificationID, that.iSNotificationID) &&
                Objects.equals(iInsID, that.iInsID) &&
                Objects.equals(iPriority, that.iPriority) &&
                Objects.equals(bDeleted, that.bDeleted) &&
                Objects.equals(sTitle, that.sTitle) &&
                Objects.equals(sDescription, that.sDescription) &&
                Objects.equals(sPhotoURL, that.sPhotoURL) &&
                Objects.equals(sVideoID, that.sVideoID) &&
                Objects.equals(sDueDate, that.sDueDate) &&
                Objects.equals(sCategory, that.sCategory) &&
                Objects.equals(sCustomOne, that.sCustomOne) &&
                Objects.equals(sCustomTwo, that.sCustomTwo) &&
                Objects.equals(dtDateTime, that.dtDateTime);
    }

    @Override
    public int hashCode() {
        return Objects.hash(iSNotificationID, iInsItemID, iInsID, sTitle, sDescription, sPhotoURL, sVideoID, sDueDate, iPriority, sCategory, sCustomOne, sCustomTwo, bDeleted, dtDateTime);
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_Notification> find(Class<ai_Notification> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_Notification findById(Class<ai_Notification> clazz, Object id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_Notification> listAll(Class<ai_Notification> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }
}
