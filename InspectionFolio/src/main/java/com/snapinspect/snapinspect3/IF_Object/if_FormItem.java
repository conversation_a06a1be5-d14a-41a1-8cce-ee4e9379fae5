package com.snapinspect.snapinspect3.IF_Object;

import androidx.annotation.LayoutRes;
import android.text.InputType;

import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.R;

import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONObject;

public class if_FormItem {

    public interface Identifiers {
        String addressLine1 = "sAddress1";
        String addressLine2 = "sAddress2";
        String reference = "sRef";
        String keys = "sKey";
        String alarms = "sAlarm";
        String multiFamily = "bApartment";
        String firstName = "sFirstName";
        String lastName = "sLastName";
        String email = "sEmail";
        String phone = "sPhone";
        String mobile = "sMobile";
        String contactType = "sTag";
        String bIsPrimary = "bIsPrimary";
        String bDateRange = "bDateRange";

        String taskName = "sTaskName";
        String taskDescription = "sTaskDescription";
        String taskCategory = "sTaskCategory";
        String taskMisc = "sTaskMisc";

        String photos = "sPhotos";
        String videos = "sVideos";
    }

    public enum ViewType {
        unknown, textInput, textSelect, checkBox, textArea, tagSelect, attachment, photos, videos, dateRange
    }

    public enum ValidateResult {
        none, success, failure
    }

    public String identifier;
    public ViewType viewType;

    public String title;
    public String placeholder;

    public String value;
    public String value2;

    public String requiredTitle;

    public ValidateResult validateResult;
    public int inputType;
    public boolean selectable;

    /// used for `attachment`
    public int sFileID;
    public String sFileName;

    public if_FormItem(String identifier, ViewType viewType) {
        this.identifier = identifier;
        this.viewType = viewType;
        this.inputType = InputType.TYPE_CLASS_TEXT;
        this.validateResult = ValidateResult.none;
        this.selectable = true;
    }

    /**
     * @return the layout resource or zero ("0")
     */
    @LayoutRes public int getLayoutRes() {
        switch (viewType) {
            case checkBox: return R.layout.cell_form_item_check;
            case textInput: return R.layout.cell_form_item_input;
            case textArea: return R.layout.cell_form_item_textarea;
            case textSelect: return R.layout.cell_form_item_select;
            case attachment: return R.layout.cell_form_item_attachment;
            case tagSelect: return R.layout.cell_form_item_tags;
            case photos: return R.layout.cell_form_item_photos;
            case videos: return R.layout.cell_form_item_videos;
            case dateRange: return R.layout.cell_form_item_date_range;
        }
        return 0;
    }

    public static boolean hasFile(String value) {
        if (StringUtils.isEmpty(value)) return false;
        try {
            JSONObject object = new JSONObject(value);
            if (object.getInt("iFileID") > 0) return true;
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return CommonHelper.getInt(value) > 0;
    }
}
