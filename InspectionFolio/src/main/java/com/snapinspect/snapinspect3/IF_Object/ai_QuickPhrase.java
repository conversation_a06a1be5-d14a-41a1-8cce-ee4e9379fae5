package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.database.entities.QuickPhrase;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/03/14.
 */
//AIQUICK_PHRASE
public class ai_QuickPhrase {
    public Long id;
    //I_QUICK_PHRASE_ID
    public int iQuickPhraseID;
    //I_S_LAYOUT_ID
    public int iSLayoutID;
    //S_COMMENTS
    public String sComments;
    //S_FIELD_ONE
    public String sFieldOne;
    //S_FIELD_TWO
    public String sFieldTwo;
    //S_FIELD_THREE
    public String sFieldThree;
    public ai_QuickPhrase(){
    }
    public ai_QuickPhrase(int iQuickPhraseID, int iSLayoutID, String sComments, String sFieldOne, String sFieldTwo, String sFieldThree){

        this.iQuickPhraseID = iQuickPhraseID;
        this.iSLayoutID = iSLayoutID;
        this.sComments = sComments;
        this.sFieldOne = sFieldOne;
        this.sFieldTwo = sFieldTwo;
        this.sFieldThree = sFieldThree;

    }
    
    // Constructor to convert from Room entity
    public ai_QuickPhrase(QuickPhrase roomEntity) {
        this.iQuickPhraseID = roomEntity.iSQuickPhraseID != null ? roomEntity.iSQuickPhraseID : 0;
        this.iSLayoutID = roomEntity.iSLayoutID != null ? roomEntity.iSLayoutID : 0;
        this.sComments = roomEntity.sComments;
        this.sFieldOne = roomEntity.sPhrase;
        this.sFieldTwo = "";
        this.sFieldThree = "";
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_QuickPhrase> find(Class<ai_QuickPhrase> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_QuickPhrase findById(Class<ai_QuickPhrase> clazz, Object id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_QuickPhrase> listAll(Class<ai_QuickPhrase> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static void deleteAll(Class<ai_QuickPhrase> clazz) {
        // Simplified implementation for compatibility
    }

    public boolean delete() {
        // Simplified implementation for compatibility
        return true;
    }
}
