package com.snapinspect.snapinspect3.IF_Object;

import java.util.Date;
// Removed: import com.orm.SugarRecord;

public class ai_UpdateAssetTask {
    public Long id;
    
    public int iAssetID;
    public String dtLastSync;
    public String sCustom1;
    public String sCustom2;
    public boolean bDeleted;
    public Date dtUpdate;
    public Date dtDateTime;

    // Default constructor
    public ai_UpdateAssetTask(int iAssetID, String dtLastSync) {
        this(iAssetID, dtLastSync, "", "", false, new Date(), new Date());
    }

    public ai_UpdateAssetTask() {
        this(0, "", "", "", false, new Date(), new Date());
    }

    // Full constructor
    public ai_UpdateAssetTask(int iAssetID, String dtLastSync, String sCustom1, 
                            String sCustom2, boolean bDeleted, Date dtUpdate, Date dtDateTime) {
        this.iAssetID = iAssetID;
        this.dtLastSync = dtLastSync;
        this.sCustom1 = sCustom1;
        this.sCustom2 = sCustom2;
        this.bDeleted = bDeleted;
        this.dtUpdate = dtUpdate;
        this.dtDateTime = dtDateTime;
    } 
}