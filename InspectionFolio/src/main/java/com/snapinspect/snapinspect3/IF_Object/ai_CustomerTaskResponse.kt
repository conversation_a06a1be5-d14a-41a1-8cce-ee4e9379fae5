package com.snapinspect.snapinspect3.IF_Object

import org.json.JSONObject

/**
 * Represents a response from the customer tasks API
 */
class ai_CustomerTaskResponse(jsonObject: JSONObject) {
    // List of tasks in the response
    val lsTask: List<ai_Task>

    // Sync date from the response
    val dtSync: String?
    
    init {
        // Parse tasks from the response
        lsTask = jsonObject.optJSONArray("lsTask")?.let { array ->
            List(array.length()) { i ->
                ai_Task(array.getJSONObject(i))
            }
        } ?: emptyList()

        // Parse sync date
        dtSync = jsonObject.optString("dtSync")
    }
} 