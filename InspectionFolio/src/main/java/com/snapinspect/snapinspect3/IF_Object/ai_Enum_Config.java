package com.snapinspect.snapinspect3.IF_Object;

public class ai_Enum_Config {

    public enum SI_Inspection_Type {
        Full, Simple
    }

    public enum SI_Question_Type {
        SI_Question_C,
        SI_Question_G,
        SI_Question_A,
        SI_Question_S,
        SI_Question_V,
        SI_Question_P
    }


    public static final String SI_QUESTION_TYPE_C = "C";
    public static final String SI_QUESTION_TYPE_G = "G";
    public static final String SI_QUESTION_TYPE_A = "A";
    public static final String SI_QUESTION_TYPE_S = "S";
    public static final String SI_QUESTION_TYPE_V = "V";
    public static final String SI_QUESTION_TYPE_P = "P";

    public static final String INSPECTION_TYPE_FULL   = "F";
    public static final String INSPECTION_TYPE_SIMPLE = "S";

    public static final String SI_S_CONFIG_KEY_CHK   = "CHK";
    public static final String SI_S_CONFIG_KEY_SCHK  = "SCHK";
    public static final String SI_S_CONFIG_KEY_MCHK  = "MCHK";
    public static final String SI_S_CONFIG_KEY_CMT   = "CMT";
    public static final String SI_S_CONFIG_KEY_PTO   = "PTO";
    public static final String SI_S_CONFIG_KEY_SCAN  = "SCAN";
    public static final String SI_S_CONFIG_KEY_SEL   = "SEL";
    public static final String SI_S_CONFIG_KEY_MSEL  = "MSEL";
    public static final String SI_S_CONFIG_KEY_NUM   = "NUM";
    public static final String SI_S_CONFIG_KEY_LST   = "LST";
    public static final String SI_S_CONFIG_KEY_DT    = "DT";
    public static final String SI_S_CONFIG_KEY_COST  = "COST";
    public static final String SI_S_CONFIG_KEY_OTHER = "{";

    public static final int SI_CONTROL_TYPE_CHK      = 1;
    public static final int SI_CONTROL_TYPE_SCHK     = 2;
    public static final int SI_CONTROL_TYPE_MCHK     = 3;
    public static final int SI_CONTROL_TYPE_CMT      = 4;
    public static final int SI_CONTROL_TYPE_PTO      = 5;
    public static final int SI_CONTROL_TYPE_SCAN     = 6;
    public static final int SI_CONTROL_TYPE_SEL      = 7;
    public static final int SI_CONTROL_TYPE_MSEL     = 8;
    public static final int SI_CONTROL_TYPE_NUM      = 9;
    public static final int SI_CONTROL_TYPE_LST      = 10;
    public static final int SI_CONTROL_TYPE_DT       = 11;
    public static final int SI_CONTROL_TYPE_COST    = 12;
    public static final int SI_CONTROL_TYPE_NONE     = 0;
}
