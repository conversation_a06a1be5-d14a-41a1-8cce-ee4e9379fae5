package com.snapinspect.snapinspect3.IF_Object;

import android.content.Context;
// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.database.RoomDatabaseManager;
import com.snapinspect.snapinspect3.database.entities.User;
import com.snapinspect.snapinspect3.app.App;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by TerryS on 26/03/17.
 */
public class ai_User {
    public Long id;
    //I_Customer_ID
    public int iCustomerID;
    //S_NAME
    public String sName;
    //S_EMAIL
    public String sEmail;
    public boolean bDeleted;

    public ai_User(){}
    public ai_User( int _iCustomerID, String _sName, String _sEmail){
        this.iCustomerID = _iCustomerID;
        this.sName = _sName;
        this.sEmail = _sEmail;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNameLetters() {
        return StringUtils.getFirstLetters(sName);
    }

    public boolean isLoggedIn(Context context) {
        return CommonHelper.getInt(CommonHelper.GetPreferenceString(context, Constants.Keys.iCustomerID))
                == this.iCustomerID;
    }

    public String getAvatarColor() {
        String[] colors = Constants.USER_COLORS;
        if (StringUtils.isEmpty(sEmail)) return colors[0];

        int hash = Math.abs(sEmail.hashCode());
        int colorIndex = hash % colors.length;
        return colors[colorIndex];
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof ai_User)) return false;
        ai_User other = (ai_User) obj;
        return this.iCustomerID == other.iCustomerID;
    }

    // Helper method to get Room Database Manager
    private static RoomDatabaseManager getRoomManager() {
        return RoomDatabaseManager.getInstance(App.getContext());
    }

    // Convert to Room entity
    public User toRoomEntity() {
        User roomEntity = new User();
        roomEntity.id = this.id;
        roomEntity.iCustomerID = this.iCustomerID;
        roomEntity.sName = this.sName;
        roomEntity.sEmail = this.sEmail;
        roomEntity.bDeleted = this.bDeleted;
        return roomEntity;
    }

    // Create from Room entity
    public static ai_User fromRoomEntity(User roomEntity) {
        if (roomEntity == null) return null;
        
        ai_User sugar = new ai_User();
        sugar.id = roomEntity.id;
        sugar.iCustomerID = roomEntity.iCustomerID != null ? roomEntity.iCustomerID : 0;
        sugar.sName = roomEntity.sName;
        sugar.sEmail = roomEntity.sEmail;
        sugar.bDeleted = roomEntity.bDeleted != null ? roomEntity.bDeleted : false;
        return sugar;
    }

    // Sugar ORM compatibility methods - now using Room DAO
    public Long save() {
        try {
            User roomEntity = this.toRoomEntity();
            if (this.id == null) {
                // Insert new user
                long newId = getRoomManager().getDatabase().userDao().insertUser(roomEntity);
                this.id = newId;
            } else {
                // Update existing user
                getRoomManager().getDatabase().userDao().updateUser(roomEntity);
            }
            return this.id;
        } catch (Exception ex) {
            // Fallback to simple ID generation if Room fails
            if (this.id == null) {
                this.id = System.currentTimeMillis();
            }
            return this.id;
        }
    }

    public static List<ai_User> find(Class<ai_User> clazz, String whereClause, String... whereArgs) {
        try {
            // Basic implementation - more sophisticated parsing would be needed for full compatibility
            List<User> roomUsers = getRoomManager().getDatabase().userDao().getAllUsers();
            List<ai_User> sugarUsers = new ArrayList<>();
            for (User roomUser : roomUsers) {
                sugarUsers.add(fromRoomEntity(roomUser));
            }
            return sugarUsers;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public static ai_User findById(Class<ai_User> clazz, Object id) {
        try {
            if (id instanceof Long) {
                User roomUser = getRoomManager().getDatabase().userDao().getUserById((Long) id);
                return fromRoomEntity(roomUser);
            } else if (id instanceof Integer) {
                User roomUser = getRoomManager().getDatabase().userDao().getUserById(((Integer) id).longValue());
                return fromRoomEntity(roomUser);
            }
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    public static List<ai_User> listAll(Class<ai_User> clazz) {
        try {
            List<User> roomUsers = getRoomManager().getDatabase().userDao().getAllUsers();
            List<ai_User> sugarUsers = new ArrayList<>();
            for (User roomUser : roomUsers) {
                sugarUsers.add(fromRoomEntity(roomUser));
            }
            return sugarUsers;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public static void deleteAll(Class<ai_User> clazz) {
        try {
            // Use soft delete for users
            List<User> allUsers = getRoomManager().getDatabase().userDao().getAllUsers();
            for (User user : allUsers) {
                user.bDeleted = true;
                getRoomManager().getDatabase().userDao().updateUser(user);
            }
        } catch (Exception ex) {
            // Silently fail for compatibility
        }
    }

    public boolean delete() {
        try {
            if (this.id != null) {
                // Use soft delete
                this.bDeleted = true;
                return this.save() != null;
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }
}

