package com.snapinspect.snapinspect3.IF_Object;

import androidx.annotation.Nullable;
// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.database.RoomDatabaseManager;
import com.snapinspect.snapinspect3.database.entities.Video;
import com.snapinspect.snapinspect3.app.App;
import org.json.JSONObject;

import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/03/14.
 */
//AIVIDEO
public class ai_Video {
    public Long id;
    //I_S_VIDEO_ID
    public int iSVideoID;
    //I_INS_ID
    public int iInsID;
    //I_INS_ITEM_ID
    public long iInsItemID;
    //S_THUMB
    public String sThumb;
    //S_FILE
    public String sFile;
    //S_S_THUMB
    public String sSThumb;
    //S_S_FILE
    public String sSFile;
    //B_GET_URL
    public boolean bGetURL;
    //B_UPLOADED
    public boolean bUploaded;
    //B_PROCESSED
    public boolean bProcessed;
    //S_FIELD_ONE
    public String sFieldOne;
    //S_FIELD_TWO
    public String sFieldTwo;
    //S_FIELD_THREE
    public String sFieldThree;
    //B_DELETED
    public boolean bDeleted;
    //DT_DATE_TIME
    public String dtDateTime;
    //I_SIZE
    public int iSize;

    public ai_Video() {

    }

    public ai_Video(int iSVideoID, int iInsID, long iInsItemID, String sThumb, String sFile, String sSThumb, String sSFile, boolean bGetURL,
                    boolean bUploaded, boolean bProcessed, String dtDateTime, int iSize) {
        this.iSVideoID = iSVideoID;
        this.iInsID = iInsID;
        this.iInsItemID = iInsItemID;
        this.sThumb = sThumb;
        this.sFile = sFile;
        this.sSThumb = sSThumb;
        this.sSFile = sSFile;
        this.bGetURL = bGetURL;
        this.bUploaded = bUploaded;
        this.bProcessed = bProcessed;
        this.sFieldOne = null;
        this.sFieldTwo = null;
        this.sFieldThree = null;
        this.bDeleted = false;
        this.dtDateTime = dtDateTime;
        this.iSize = iSize;
    }

    public ai_Video(JSONObject jsonObject) {
        this.iSVideoID = jsonObject.optInt("iVideoID", 0);
        this.iInsID = jsonObject.optInt("iInspectionID", 0);
        this.iInsItemID = jsonObject.optLong("iInsItemID", 0);
        this.sSThumb = jsonObject.optString("sThumbURL", "");
        this.sSFile = jsonObject.optString("sVideoURL", "");
        this.bUploaded = jsonObject.optBoolean("bUploaded", false);
        this.bProcessed = jsonObject.optBoolean("bTransfered", false);
        this.bDeleted = jsonObject.optBoolean("bDeleted", false);
        this.iSize = jsonObject.optInt("iVideoSize", 0);
        this.sFieldOne = jsonObject.optString("sAppendix", "");
        this.dtDateTime = jsonObject.optString("dtDateTime", "");
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFile() {
        return FileUtils.validFilePath(sFile);
    }

    public String getThumb() {
        return FileUtils.validFilePath(sThumb);
    }

    public String getGeo() {
        return CommonJson.GetJsonKeyValue("_sGPS", sFieldOne);
    }

    @Override
    public boolean equals(@Nullable @org.jetbrains.annotations.Nullable Object obj) {
        if (!(obj instanceof ai_Video)) return false;
        ai_Video video = (ai_Video) obj;
        return getId().equals(video.getId());
    }

    public URI getSSFileURL() {
        if (StringUtils.isEmpty(sSFile)) return null;
        try {
            URI baseUri = new URI(Constants.Urls.VIDEO_URL);
            return baseUri.resolve(sSFile);
        } catch (Exception e) {
            return null;
        }
    }

    public URI getSSThumbURL() {
        if (StringUtils.isEmpty(sSThumb)) return null;
        try {
            URI baseUri = new URI(Constants.Urls.VIDEO_URL);
            return baseUri.resolve(sSThumb);
        } catch (Exception e) {
            return null;
        }
    }

    // Helper method to get Room Database Manager
    private static RoomDatabaseManager getRoomManager() {
        return RoomDatabaseManager.getInstance(App.getContext());
    }

    // Convert to Room entity
    public Video toRoomEntity() {
        Video roomEntity = new Video();
        roomEntity.id = this.id;
        roomEntity.iSVideoID = this.iSVideoID;
        roomEntity.iInsID = this.iInsID;
        roomEntity.iInsItemID = this.iInsItemID;
        roomEntity.sThumb = this.sThumb;
        roomEntity.sFile = this.sFile;
        roomEntity.sSThumb = this.sSThumb;
        roomEntity.sSFile = this.sSFile;
        roomEntity.bGetURL = this.bGetURL;
        roomEntity.bUploaded = this.bUploaded;
        roomEntity.bProcessed = this.bProcessed;
        roomEntity.sFieldOne = this.sFieldOne;
        roomEntity.sFieldTwo = this.sFieldTwo;
        roomEntity.sFieldThree = this.sFieldThree;
        roomEntity.bDeleted = this.bDeleted;
        roomEntity.dtDateTime = this.dtDateTime;
        roomEntity.iSize = this.iSize;
        return roomEntity;
    }

    // Create from Room entity
    public static ai_Video fromRoomEntity(Video roomEntity) {
        if (roomEntity == null) return null;
        
        ai_Video sugar = new ai_Video();
        sugar.id = roomEntity.id;
        sugar.iSVideoID = roomEntity.iSVideoID != null ? roomEntity.iSVideoID : 0;
        sugar.iInsID = roomEntity.iInsID != null ? roomEntity.iInsID : 0;
        sugar.iInsItemID = roomEntity.iInsItemID != null ? roomEntity.iInsItemID : 0;
        sugar.sThumb = roomEntity.sThumb;
        sugar.sFile = roomEntity.sFile;
        sugar.sSThumb = roomEntity.sSThumb;
        sugar.sSFile = roomEntity.sSFile;
        sugar.bGetURL = roomEntity.bGetURL != null ? roomEntity.bGetURL : false;
        sugar.bUploaded = roomEntity.bUploaded != null ? roomEntity.bUploaded : false;
        sugar.bProcessed = roomEntity.bProcessed != null ? roomEntity.bProcessed : false;
        sugar.sFieldOne = roomEntity.sFieldOne;
        sugar.sFieldTwo = roomEntity.sFieldTwo;
        sugar.sFieldThree = roomEntity.sFieldThree;
        sugar.bDeleted = roomEntity.bDeleted != null ? roomEntity.bDeleted : false;
        sugar.dtDateTime = roomEntity.dtDateTime;
        sugar.iSize = roomEntity.iSize != null ? roomEntity.iSize : 0;
        return sugar;
    }

    // Sugar ORM compatibility methods - now using Room DAO
    public Long save() {
        try {
            Video roomEntity = this.toRoomEntity();
            if (this.id == null) {
                // Insert new video
                long newId = getRoomManager().getDatabase().videoDao().insertVideo(roomEntity);
                this.id = newId;
            } else {
                // Update existing video
                getRoomManager().getDatabase().videoDao().updateVideo(roomEntity);
            }
            return this.id;
        } catch (Exception ex) {
            // Fallback to simple ID generation if Room fails
            if (this.id == null) {
                this.id = System.currentTimeMillis();
            }
            return this.id;
        }
    }

    public static List<ai_Video> find(Class<ai_Video> clazz, String whereClause, String... whereArgs) {
        try {
            // Basic implementation - more sophisticated parsing would be needed for full compatibility
            List<Video> roomVideos = getRoomManager().getDatabase().videoDao().getAllVideos();
            List<ai_Video> sugarVideos = new ArrayList<>();
            for (Video roomVideo : roomVideos) {
                sugarVideos.add(fromRoomEntity(roomVideo));
            }
            return sugarVideos;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public static ai_Video findById(Class<ai_Video> clazz, Object id) {
        try {
            if (id instanceof Long) {
                Video roomVideo = getRoomManager().getDatabase().videoDao().getVideoById((Long) id);
                return fromRoomEntity(roomVideo);
            } else if (id instanceof Integer) {
                Video roomVideo = getRoomManager().getDatabase().videoDao().getVideoById(((Integer) id).longValue());
                return fromRoomEntity(roomVideo);
            }
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    public static List<ai_Video> listAll(Class<ai_Video> clazz) {
        try {
            List<Video> roomVideos = getRoomManager().getDatabase().videoDao().getAllVideos();
            List<ai_Video> sugarVideos = new ArrayList<>();
            for (Video roomVideo : roomVideos) {
                sugarVideos.add(fromRoomEntity(roomVideo));
            }
            return sugarVideos;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public boolean delete() {
        try {
            if (this.id != null) {
                // Use soft delete
                this.bDeleted = true;
                return this.save() != null;
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }
}
