package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.database.entities.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by terrysun1 on 21/07/15.
 */
//AIFILE
public class ai_File {
    public Long id;
    //I_FILE_ID, this is used as purpose as iSFileID in iOS
    public int iFileID;
    //I_S_OBJECT_ID
    public int iSObjectID;
    //S_FILE
    public String sFile;
    //S_COMMENTS
    public String sComments;
    //S_LAT
    public String sLat;
    //S_LONG
    public String sLong;
    //B_UPLOADED
    public boolean bUploaded;
    //B_DELETED
    public boolean bDeleted;
    //DT_DATE_TIME
    public String dtDateTime;
    //I_SIZE
    public int iSize;
    //S_CUSTOM_ONE
    public String sCustomOne;
    //S_CUSTOM_TWO
    public String sCustomTwo;
    public ai_File(){}
    public ai_File( int _iFileID, int _iSObjectID, String _sFile, String _sComments, String _sLat, String _sLong,
                   boolean _bUploaded, boolean _bDeleted, String _dtDateTime, int _iSize){
        this.iFileID = _iFileID;
        this.iSObjectID = _iSObjectID;
        this.sFile = _sFile;
        this.sComments = _sComments;
        this.sLat = _sLat;
        this.sLong = _sLong;
        this.bUploaded = _bUploaded;
        this.bDeleted = _bDeleted;
        this.dtDateTime = _dtDateTime;
        this.iSize = _iSize;

    }
    
    // Constructor to convert from Room entity
    public ai_File(File roomEntity) {
        if (roomEntity.id != null) {
            this.setId(roomEntity.id);
        }
        this.iFileID = roomEntity.iFileID != null ? roomEntity.iFileID : 0;
        this.iSObjectID = roomEntity.iSObjectID != null ? roomEntity.iSObjectID : 0;
        this.sFile = roomEntity.sFile;
        this.sComments = roomEntity.sComments;
        this.sLat = roomEntity.sLat;
        this.sLong = roomEntity.sLong;
        this.bUploaded = roomEntity.bUploaded != null ? roomEntity.bUploaded : false;
        this.bDeleted = roomEntity.bDeleted != null ? roomEntity.bDeleted : false;
        this.dtDateTime = roomEntity.dtDateTime;
        this.iSize = roomEntity.iSize != null ? roomEntity.iSize : 0;
        this.sCustomOne = roomEntity.sCustomOne;
        this.sCustomTwo = roomEntity.sCustomTwo;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_File> find(Class<ai_File> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_File findById(Class<ai_File> clazz, Long id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_File> listAll(Class<ai_File> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static void deleteAll(Class<ai_File> clazz) {
        // Simplified implementation for compatibility
    }

    public boolean delete() {
        // Simplified implementation for compatibility
        return true;
    }
}
