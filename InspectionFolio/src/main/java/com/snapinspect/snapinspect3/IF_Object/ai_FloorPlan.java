package com.snapinspect.snapinspect3.IF_Object;

// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.util.DateUtils;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

public class ai_FloorPlan {
    public Long id;
    private static final String FLOOR_PLAN_IMAGE_NAME_PREFIX = "FloorPlan";
    public int iFloorPlanID;
    public int iSFloorPlanID;
    public int iSAssetID;
    public int iCreateCustomerID;
    public String sMember;
    public String sTitle;
    public String sDesp;
    public String sPlanPath;
    public String sImagePath;
    public String sMarks;
    public String sCustom1;
    public String sCustom2;
    public String sCustom3;
    public boolean bArchive;
    public boolean bDeleted;
    public Date dtUpdate;  // Assuming DATE will be stored as String
    public Date dtDateTime;  // Assuming DATE will be stored as String

    // Default constructor is needed for Sugar ORM
    public ai_FloorPlan() {
    }

    public ai_FloorPlan(int iFloorPlanID, int iSFloorPlanID, int iSAssetID, int iCreateCustomerID, String sMember,
                        String sTitle, String sDesp, String sPlanPath, String sImagePath, String sMarks,
                        String sCustom1, String sCustom2, String sCustom3, boolean bArchive, boolean bDeleted,
                        Date dtUpdate, Date dtDateTime) {
        this.iFloorPlanID = iFloorPlanID;
        this.iSFloorPlanID = iSFloorPlanID;
        this.iSAssetID = iSAssetID;
        this.iCreateCustomerID = iCreateCustomerID;
        this.sMember = sMember;
        this.sTitle = sTitle;
        this.sDesp = sDesp;
        this.sPlanPath = sPlanPath;
        this.sImagePath = sImagePath;
        this.sMarks = sMarks;
        this.sCustom1 = sCustom1;
        this.sCustom2 = sCustom2;
        this.sCustom3 = sCustom3;
        this.bArchive = bArchive;
        this.bDeleted = bDeleted;
        this.dtUpdate = dtUpdate;
        this.dtDateTime = dtDateTime;
    }

    public ai_FloorPlan(JSONObject object) {
        try {
            iSFloorPlanID = object.getInt("iFloorPlanID");
            iSAssetID = object.getInt("iAssetID");
            iCreateCustomerID = object.getInt("iCreateCustomerID");
            sMember = object.getString("sMember");
            sTitle = object.getString("sTitle");
            sDesp = object.getString("sDesp");
            sPlanPath = object.getString("sPlanPath");
            sImagePath = object.getString("sImagePath_Current");
            sMarks = object.getString("sMarks");
            sCustom1 = object.getString("sCustom1");
            sCustom2 = object.getString("sCustom2");
            sCustom3 = object.getString("sCustom3");
            bArchive = object.getBoolean("bArchive");
            bDeleted = object.getBoolean("bDeleted");
            dtUpdate = DateUtils.parse(object.getString("dtUpdate"), DateUtils.DATE_RANGE_FORMAT);
            dtDateTime = DateUtils.parse(object.getString("dtDateTime"), DateUtils.DATE_RANGE_FORMAT);
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (obj instanceof ai_FloorPlan) {
            ai_FloorPlan floorPlan = (ai_FloorPlan) obj;
            return floorPlan.iSFloorPlanID == iSFloorPlanID;
        }
        return false;
    }

    @Override
    public int hashCode() {
        return Objects.hash(iSFloorPlanID);
    }

    /**
     * Returns the file path for the saved blueprint image file, the file name is generated by `getPlanFileName`
     */
    public String savedPlanFilePath() {
        return CommonHelper.sFileRoot + Constants.Paths.FLOOR_PLAN_FOLDER + "/" + getPlanFileName();
    }

    /**
     * Returns the file path of the blueprint image.
     *
     * @return the file path of the blueprint image with sFileRoot and blueprint folder
     */
    public String getPlanFilePath() {
        return CommonHelper.sFileRoot + Constants.Paths.FLOOR_PLAN_FOLDER + "/" + sPlanPath;
    }

    /**
     * Returns the file path to save the floor image file, the file name is generated by `getImageFileName`
     */
    public String savedImageFilePath() {
        return CommonHelper.sFileRoot + Constants.Paths.FLOOR_PLAN_FOLDER + "/" + getImageFileName();
    }

    /**
     * Returns the file path of the image.
     *
     * @return the file path of the image with sFileRoot and blueprint folder
     */
    public String getImageFilePath() {
        return CommonHelper.sFileRoot + Constants.Paths.FLOOR_PLAN_FOLDER + "/" + sImagePath;
    }

    /**
     * Returns the file name of the blueprint image.
     *
     * @return the file name of the blueprint image
     * in the format FLOOR_PLAN_IMAGE_NAME_PREFIX_iSFloorPlanID.jpg, for ex FloorPlan_81.jpg
     */
    public String getPlanFileName() {
        return FLOOR_PLAN_IMAGE_NAME_PREFIX + "_" + iSFloorPlanID + ".jpg";
    }

    /**
     * Returns the file name of the image.
     *
     * @return the file name of the image
     * in the format FLOOR_PLAN_IMAGE_NAME_PREFIX_iSFloorPlanID_image.jpg, for example, FloorPlan_81_image.jpg
     */
    public String getImageFileName() {
        return FLOOR_PLAN_IMAGE_NAME_PREFIX + "_" + iSFloorPlanID + "_image.jpg";
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_FloorPlan> find(Class<ai_FloorPlan> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_FloorPlan findById(Class<ai_FloorPlan> clazz, Object id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_FloorPlan> listAll(Class<ai_FloorPlan> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }
}