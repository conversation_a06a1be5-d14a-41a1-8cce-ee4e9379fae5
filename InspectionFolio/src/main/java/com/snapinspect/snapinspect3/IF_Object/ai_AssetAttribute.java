package com.snapinspect.snapinspect3.IF_Object;

import org.json.JSONObject;

/**
 * Created by te<PERSON><PERSON> on 25/11/18.
 */

public class ai_AssetAttribute {
    public int iAssetAttributeID;
    public int iCompanyID;
    public String sLabel;
    public O_MapItem.CustomInfoType sType;
    public String sCustom1;
    public String sCustom2;
    public boolean bDeleted;

    public String sValue;
    public int sFileID;
    public String sFileName;

    public ai_AssetAttribute(JSONObject json) {
        iAssetAttributeID = json.optInt("iAssetAttributeID", 0);
        iCompanyID = json.optInt("iCompanyID", 0);
        sLabel = json.optString("sLabel", "");
        sType = O_MapItem.getCustomInfoType(json.optString("sType", ""));
        sCustom1 = json.optString("sCustom1", "");
        sCustom2 = json.optString("sCustom2", "");
        bDeleted = json.optBoolean("bDeleted", false);
    }

    public String getValue() {
        if (sType == O_MapItem.CustomInfoType.pto) return sFileName;
        return sValue;
    }

    public if_FormItem.ViewType getViewType() {
        switch (sType) {
            case text: return if_FormItem.ViewType.textInput;
            case checkBox: return if_FormItem.ViewType.checkBox;
            case textArea: return if_FormItem.ViewType.textArea;
            case pto: return if_FormItem.ViewType.attachment;
            default:
                return if_FormItem.ViewType.unknown;
        }
    }
}
