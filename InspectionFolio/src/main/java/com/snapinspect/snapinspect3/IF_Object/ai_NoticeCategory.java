package com.snapinspect.snapinspect3.IF_Object;

// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by terrysun1 on 25/09/15.
 */
//AINOTICE_CATEGORY
public class ai_NoticeCategory {
    public Long id;

    public int iSNoticeCategoryID;
    public String sName;
    public String sDescription;
    public boolean bDeleted;
    public String dtUpdate;
    public String dtDateTime;
    public int iPNoteCategoryID;
    public String sCustom1;
    public String sCustom2;

    public ai_NoticeCategory() {}

    public ai_NoticeCategory(JSONObject oObject) {
        iSNoticeCategoryID = oObject.optInt("iNoteCategoryID", 0);
        sName = oObject.optString("sName", "");
        sDescription = oObject.optString("sDescription", "");
        bDeleted = oObject.optBoolean("bDeleted", false);
        dtUpdate = oObject.optString("dtUpdate", "");
        dtDateTime = oObject.optString("dtDateTime", "");
        iPNoteCategoryID = oObject.optInt("iPNoteCategoryID", 0);
        sCustom1 = oObject.optString("sCustom1", "");
        sCustom2 = oObject.optString("sCustom2", "");
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_NoticeCategory> find(Class<ai_NoticeCategory> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_NoticeCategory findById(Class<ai_NoticeCategory> clazz, Object id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_NoticeCategory> listAll(Class<ai_NoticeCategory> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
