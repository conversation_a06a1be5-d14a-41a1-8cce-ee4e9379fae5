package com.snapinspect.snapinspect3.IF_Object;

// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.SI_DB.db_AssetView;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;

public class ai_AssetView {
    public Long id;
    // I_S_ASSET_VIEW_ID
    public int iSAssetViewID;

    // I_CUSTOMER_ID
    public int iCustomerID;

    // I_COMPANY_ID
    public int iCompanyID;

    // I_GROUP_ID (can be null)
    public int iGroupID;

    // S_NAME
    public String sName;

    // S_DESCRIPTION
    public String sDescription;

    // S_CUSTOM1
    public String sCustom1;

    // S_CUSTOM2
    public String sCustom2;

    // B_ARCHIVED
    public boolean bArchived;

    // B_DELETED
    public boolean bDeleted;

    // DT_UPDATE
    public Date dtUpdate;

    // ARR_MEM
    public String arrMem;

    public ai_AssetView() {
    }
    public ai_AssetView(JSONObject json) {
        iSAssetViewID = json.optInt("iAssetViewID");
        ai_AssetView existAstView = db_AssetView.getAssetView(iSAssetViewID);
        if (existAstView != null) {
            setId(existAstView.getId());
        }
        iCustomerID = json.optInt("iCustomerID");
        iCompanyID = json.optInt("iCompanyID");
        iGroupID = json.optInt("iGroupID");
        sName = json.optString("sName");
        sDescription = json.optString("sDescription");
        sCustom1 = json.optString("sCustom1");
        sCustom2 = json.optString("sCustom2");
        bArchived = json.optBoolean("bArchived");
        bDeleted = json.optBoolean("bDeleted");
        try {
            dtUpdate = Constants.dateFormat.parse(json.optString("dtUpdate", ""));
        } catch (ParseException e) {
            ai_BugHandler.logException(e);
        }

        JSONArray arrMem = CommonJson.GetJSONArrayValue("_arrMem", sCustom1);
        if (arrMem == null || arrMem.length() == 0) this.arrMem = "";
        else {
            ArrayList<String> arrMemList = new ArrayList<>();
            for (int i = 0; i < arrMem.length(); i++) {
                arrMemList.add("[" + arrMem.optInt(i) + "]");
            }
            this.arrMem = String.join(",", arrMemList);
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}
