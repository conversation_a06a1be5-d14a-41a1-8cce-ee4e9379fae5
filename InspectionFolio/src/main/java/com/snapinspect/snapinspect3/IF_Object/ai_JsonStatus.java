package com.snapinspect.snapinspect3.IF_Object;

import org.json.JSONObject;

public class ai_JsonStatus {
    public enum StatusType {
        INSPECTION, TASK, ASSET, PROJECT;

        public static StatusType fromType(String sType) {
            if ("I".equalsIgnoreCase(sType)) return INSPECTION;
            else if ("T".equalsIgnoreCase(sType)) return TASK;
            else if ("A".equalsIgnoreCase(sType)) return ASSET;
            else if ("P".equalsIgnoreCase(sType)) return PROJECT;
            else return INSPECTION;
        }
    }

    public int iStatusID;
    public int iCompanyID;
    public String sName;
    public StatusType sType;
    public String sDescription;
    public String sColorCode;
    public int iOrder;
    public boolean bPrimary;
    public String sCustom1;
    public boolean bDeleted;

    public ai_JsonStatus(JSONObject dictionary) {
        iStatusID = dictionary.optInt("iStatusID", 0);
        iCompanyID = dictionary.optInt("iCompanyID", 0);
        sName = dictionary.optString("sName", "");
        sType = StatusType.fromType(dictionary.optString("sType", "I"));
        sDescription = dictionary.optString("sDescription", "");
        sColorCode = dictionary.optString("sColorCode", "").trim();
        iOrder = dictionary.optInt("iOrder", 0);
        bPrimary = dictionary.optBoolean("bPrimary", false);
        sCustom1 = dictionary.optString("sCustom1", "");
        bDeleted = dictionary.optBoolean("bDeleted", false);
    }
}
