package com.snapinspect.snapinspect3.IF_Object;

import android.content.Context;
import android.os.Build;
import android.util.Log;
import com.google.firebase.crashlytics.FirebaseCrashlytics;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.app.App;

/**
 * Created by <PERSON><PERSON><PERSON> on 31/01/15.
 */
public class ai_BugHandler {

    public static void logError(String category, String message) {
        FirebaseCrashlytics firebaseCrashlytics = FirebaseCrashlytics.getInstance();
        firebaseCrashlytics.log(message);
        // Must record an exception for log to be sent to Firebase
        final Throwable throwable = new Throwable(category);
        // Remove the last line of the stack trace
        if (throwable.getStackTrace().length > 0) {
            StackTraceElement[] cleanedUpStackTrace = new StackTraceElement[throwable.getStackTrace().length - 1];
            System.arraycopy(throwable.getStackTrace(), 1, cleanedUpStackTrace, 0, cleanedUpStackTrace.length);
            throwable.setStackTrace(cleanedUpStackTrace);
        }
        firebaseCrashlytics.recordException(throwable);
    }

    // These will only be used if the app later crashes
    public static void logMessage(String message) {
        FirebaseCrashlytics firebaseCrashlytics = FirebaseCrashlytics.getInstance();
        firebaseCrashlytics.log(message);
    }

    // this will log the exception and send it to Firebase
    public static void logException(Exception e) {
        FirebaseCrashlytics.getInstance().recordException(e);
    }

    public static void ai_Handler_Exception(Exception ex) {
        try {
            StackTraceElement[] stackTraceElements = Thread.currentThread().getStackTrace();
            /*
            0 = "dalvik.system.VMStack.getThreadStackTrace(Native Method)"
            1 = "java.lang.Thread.getStackTrace(Thread.java:1538)"
            2 = "com.snapinspect.snapinspect3.IF_Object.ai_BugHandler.ai_Handler_Exception(ai_BugHandler.java:20)"
            3 = "com.snapinspect.snapinspect3.activitynew.Edit.if_UpdateAsset_2nd.onCreate(if_UpdateAsset_2nd.java:72)"
            */
            String sType = "Exception", sMessage = stackTraceElements.length > 3 ?
                    stackTraceElements[3].getClass().getSimpleName() + "." + stackTraceElements[3].getMethodName() : "";
            FirebaseCrashlytics.getInstance().log(sType + " " + sMessage);
            FirebaseCrashlytics.getInstance().recordException(ex);
        } catch (Exception exx) {

        }
    }

    public static void ai_Handler_Exception(String sType, String sMessage, Exception ex){
        try {
            //ai_BugHandler.ai_Handler_Exception("per", sType, ex);
            FirebaseCrashlytics.getInstance().log(sType + " " + sMessage);
            FirebaseCrashlytics.getInstance().recordException(ex);
        } catch (Exception exx) {

        }

    }
    public static void ai_Handler_Exception(String sType, String sMessage, Exception ex, Context oContext){
        try {
            //ai_BugHandler.ai_Handler_Exception("per", sType, ex);
            FirebaseCrashlytics.getInstance().log(sType + " " + sMessage + " " + ex.getStackTrace());
            FirebaseCrashlytics.getInstance().recordException(ex);
            Log.e(sType, sMessage);
        } catch (Exception exx) {

        }
    }

    public static void ai_Handler_Exception_New(String sType, String sMessage, Exception ex){
        try {
            //ai_BugHandler.ai_Handler_Exception("per", sType, ex);
            Log.d("SI_Bug", sMessage);
            FirebaseCrashlytics.getInstance().log(sType + " " + sMessage + " " + ex.getStackTrace());
            FirebaseCrashlytics.getInstance().recordException(ex);
        } catch(Exception exx) {

        }
    }
    private static String GetPhoneDetail() {
        return Build.BRAND + " || " + Build.MODEL + " || " + Build.VERSION.SDK_INT;
    }
}
