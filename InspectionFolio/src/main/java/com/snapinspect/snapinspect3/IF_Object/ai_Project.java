package com.snapinspect.snapinspect3.IF_Object;

// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.Constants;
import org.json.JSONArray;
import org.json.JSONObject;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ai_Project {
    public Long id;
    // I_S_PROJECT_ID
    public int iSProjectID;
    // I_CREATOR_ID
    public int iCreatorID;
    // I_COMPANY_ID
    public int iCompanyID;
    // S_REFERENCE
    public String sReference;
    // S_NAME
    public String sName;
    // S_DESCRIPTION
    public String sDescription;
    // S_CUSTOM_FIELD
    public String sCustomField;
    // I_GROUP_ID
    public int iGroupID;
    // I_TOTAL_INS
    public int iTotalIns;
    // I_COMPLETED_INS
    public int iCompletedIns;
    // S_STATUS
    public String sStatus;
    // S_STATUS_CODE
    public String sStatusCode;
    // S_CUSTOM_ONE
    public String sCustom1;
    // S_CUSTOM_TWO
    public String sCustom2;
    // B_ACTIVE
    public boolean bActive;
    // B_DELETED
    public boolean bDeleted;
    // DT_UPDATE
    public Date dtUpdate;
    // DT_DATE_TIME
    public Date dtDateTime;
    // ARR_INSPECTOR
    public String arrInspector;
    // I_MANAGER_ID
    public int iManagerID;

    public ai_Project() {
    }

    public ai_Project(
        int iSProjectID,
        int iCreatorID,
        int iCompanyID,
        String sReference,
        String sName,
        String sDescription,
        String sCustomField,
        int iGroupID,
        int iTotalIns,
        int iCompletedIns,
        String sStatus,
        String sStatusCode,
        String sCustom1,
        String sCustom2,
        boolean bActive,
        boolean bDeleted,
        Date dtUpdate,
        Date dtDateTime,
        String arrInspector,
        int iManagerID
    ) {
        this.iSProjectID = iSProjectID;
        this.iCreatorID = iCreatorID;
        this.iCompanyID = iCompanyID;
        this.sReference = sReference;
        this.sName = sName;
        this.sDescription = sDescription;
        this.sCustomField = sCustomField;
        this.iGroupID = iGroupID;
        this.iTotalIns = iTotalIns;
        this.iCompletedIns = iCompletedIns;
        this.sStatus = sStatus;
        this.sStatusCode = sStatusCode;
        this.sCustom1 = sCustom1;
        this.sCustom2 = sCustom2;
        this.bActive = bActive;
        this.bDeleted = bDeleted;
        this.dtUpdate = dtUpdate;
        this.dtDateTime = dtDateTime;
        this.arrInspector = arrInspector;
        this.iManagerID = iManagerID;
    }

    public ai_Project(JSONObject objProject) {
        iSProjectID = objProject.optInt("iProjectID");
        iCreatorID = objProject.optInt("iCreatorID");
        iCompanyID = objProject.optInt("iCompanyID", 0);
        sReference = objProject.optString("sReference", "");
        sName = objProject.optString("sName", "");
        sDescription = objProject.optString("sDescription", "");
        sCustomField = objProject.optString("sCustomField", "");
        iGroupID = objProject.optInt("iGroupID", 0);
        iTotalIns = objProject.optInt("iTotalIns", 0);
        iCompletedIns = objProject.optInt("iCompletedIns", 0);
        sStatus = objProject.optString("sStatus", "");
        sStatusCode = objProject.optString("sStatusCode", "");
        sCustom1 = objProject.optString("sCustom1", "");
        sCustom2 = objProject.optString("sCustom2", "");
        bActive = objProject.optBoolean("bActive", false);
        bDeleted = objProject.optBoolean("bDeleted", false);
        try {
            dtUpdate = Constants.dateFormat.parse(objProject.optString("dtUpdate", ""));
            dtDateTime = Constants.dateFormat.parse(objProject.optString("dtDateTime", ""));
        } catch (ParseException e) {
            ai_BugHandler.logException(e);
        }

        String arrInspectorValue = objProject.optString("arrInspector", "");
        JSONArray inspectors = CommonJson.GetJSONArrayFromJson(arrInspectorValue);
        ArrayList<String> arrInspectorID = new ArrayList<>();
        for (int i = 0; i < inspectors.length(); i++) {
            int inspectorId = CommonHelper.getInt(inspectors.optString(i));
            if (inspectorId > 0) {
                arrInspectorID.add("[" + inspectorId + "]");
            } else if (inspectors.optInt(i, 0) > 0) {
                arrInspectorID.add("[" + inspectors.optInt(i, 0) + "]");
            }
        }
        arrInspector = String.join(",", arrInspectorID);
        iManagerID = objProject.optInt("iManagerID", 0);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void checkIfExist() {
        if (iSProjectID > 0 ) {
            List<ai_Project> projects = CommonDB.findProjects("I_S_PROJECT_ID = ?", String.valueOf(iSProjectID));
            if (!projects.isEmpty()) {
                ai_Project project = projects.get(0);
                setId(project.getId());
            }
        }
    }

    public void checkIfExistAndSave() {
        checkIfExist();
        CommonDB.saveProject(this);
    }

    public int getUncompletedInsCount() {
        return iTotalIns - iCompletedIns;
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_Project> find(Class<ai_Project> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_Project findById(Class<ai_Project> clazz, Object id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_Project> listAll(Class<ai_Project> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }
}
