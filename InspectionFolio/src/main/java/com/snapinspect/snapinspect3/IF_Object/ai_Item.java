package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.Helper.CommonInsItem;
import com.snapinspect.snapinspect3.Helper.CommonDB;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_Inspection_Type.Full;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_Inspection_Type.Simple;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_Question_Type.SI_Question_A;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_Question_Type.SI_Question_C;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_Question_Type.SI_Question_G;

public class ai_Item {

    String sPrompt;
    ai_Control oControl1;
    ai_Control oControl2;
    ai_Control oControl3;
    ai_Control oControl4;
    ai_Control oControl5;
    ai_Control oControl6;

    int iLevel;

    public int iPInsItemID;
    //I_INS_ID
    public int iInsID;
    //I_S_LAYOUT_ID
    public int iSLayoutID;
    //S_NAME
    public String sName;
    //S_VALUE_ONE
    public String sValueOne;
    //S_VALUE_TWO
    public String sValueTwo;
    //S_VALUE_THREE
    public String sValueThree;
    //S_VALUE_FOUR
    public String sValueFour;
    //S_VALUE_FIVE
    public String sValueFive;
    //S_VALUE_SIX
    public String sValueSix;
    //S_Q_TYPE
    public String sQType;
    //S_CONFIG_ONE
    public String sConfigOne;
    //S_CONFIG_TWO
    public String sConfigTwo;
    //S_CONFIG_THREE
    public String sConfigThree;
    //S_CONFIG_FOUR
    public String sConfigFour;
    //S_CONFIG_FIVE
    public String sConfigFive;
    //S_CONFIG_SIX
    public String sConfigSix;

    //B_COMPLETED
    public boolean bCompleted;
    //S_CONFIG
    public String sConfig;
    //I_SORT
    public int iSort;
    //I_S_ASSET_LAYOUT_ID
    public int iSAssetLayoutID;
    //S_NAME_CHANGED
    public String sNameChanged;
    //S_CUSTOM_ONE
    public String sCustomOne;
    //S_CUSTOM_TWO
    public String sCustomTwo;
    public ai_Item(){

    }
    public ai_Item(ai_InsItem oInsItem){

        this.iPInsItemID = oInsItem.iPInsItemID;
        this.iInsID = oInsItem.iInsID;
        this.iSLayoutID = oInsItem.iSLayoutID;
        this.sName = oInsItem.sName;
        this.sValueOne = oInsItem.sValueOne;
        this.sValueTwo = oInsItem.sValueTwo;
        this.sValueThree = oInsItem.sValueThree;
        this.sValueFour = oInsItem.sValueFour;
        this.sValueFive = oInsItem.sValueFive;
        this.sValueSix = oInsItem.sValueSix;
        this.sQType = oInsItem.sQType;
        this.sConfigOne = oInsItem.sConfigOne;
        this.sConfigTwo = oInsItem.sConfigTwo;
        this.sConfigThree = oInsItem.sConfigThree;
        this.sConfigFour = oInsItem.sConfigFour;
        this.sConfigFive = oInsItem.sConfigFive;
        this.sConfigSix = oInsItem.sConfigSix;
        this.sConfig = oInsItem.sConfig;
        this.bCompleted = oInsItem.bCompleted;
        this.iSort = iSort;
        this.iSAssetLayoutID = oInsItem.iSAssetLayoutID;
        this.sNameChanged = oInsItem.sName;
        this.sCustomOne = oInsItem.sCustomOne;
        this.sCustomTwo = oInsItem.sCustomTwo;
    }
    public void setsPrompt(String sPrompt) {
        this.sPrompt = sPrompt;
    }

    public String getsPrompt() {
        return sPrompt;
    }

    public void setiLevel(int level) {
        this.iLevel = level;
    }

    public int getiLevel() {
        return iLevel;
    }

    public ai_Control getoControl1() {
        if (oControl1 == null) {
            oControl1 = new ai_Control();
        }

        oControl1.setControlType(CommonInsItem.getControlType(this.sConfigOne));
        oControl1.setsValue(this.sValueOne);
        return oControl1;
    }

    public ai_Control getoControl2() {
        if (oControl2 == null) {
            oControl2 = new ai_Control();
        }

        oControl2.setControlType(CommonInsItem.getControlType(this.sConfigTwo));
        oControl2.setsValue(this.sValueTwo);

        return oControl2;
    }

    public ai_Control getoControl3() {
        if (oControl3 == null) {
            oControl3 = new ai_Control();
        }

        oControl3.setControlType(CommonInsItem.getControlType(sConfigThree));
        oControl3.setsValue(this.sValueThree);

        return oControl3;
    }

    public ai_Control getoControl4() {
        if (oControl4 == null) {
            oControl4 = new ai_Control();
        }

        oControl4.setControlType(CommonInsItem.getControlType(sConfigFour));
        oControl4.setsValue(this.sValueFour);

        return oControl4;
    }

    public ai_Control getoControl5() {
        if (oControl5 == null) {
            oControl5 = new ai_Control();
        }

        oControl5.setControlType(CommonInsItem.getControlType(sConfigFive));
        oControl5.setsValue(this.sValueFive);

        return oControl5;
    }

    public ai_Control getoControl6() {
        if (oControl6 == null) {
            oControl6 = new ai_Control();
        }

        oControl6.setControlType(CommonInsItem.getControlType(sConfigSix));
        oControl6.setsValue(this.sValueSix);

        return oControl6;
    }

    public ai_Enum_Config.SI_Question_Type getQuestionType() {
        if (sQType.equalsIgnoreCase(ai_Enum_Config.SI_QUESTION_TYPE_C)) {
            return ai_Enum_Config.SI_Question_Type.SI_Question_C;
        } else if (sQType.equalsIgnoreCase(ai_Enum_Config.SI_QUESTION_TYPE_G)) {
            return ai_Enum_Config.SI_Question_Type.SI_Question_G;
        } else if (sQType.equalsIgnoreCase(ai_Enum_Config.SI_QUESTION_TYPE_A)) {
            return ai_Enum_Config.SI_Question_Type.SI_Question_A;
        } else if (sQType.equalsIgnoreCase(ai_Enum_Config.SI_QUESTION_TYPE_S)) {
            return ai_Enum_Config.SI_Question_Type.SI_Question_S;
        } else if (sQType.equalsIgnoreCase(ai_Enum_Config.SI_QUESTION_TYPE_V)) {
            return ai_Enum_Config.SI_Question_Type.SI_Question_V;
        } else if (sQType.equalsIgnoreCase(ai_Enum_Config.SI_QUESTION_TYPE_P)) {
            return ai_Enum_Config.SI_Question_Type.SI_Question_P;
        } else {
            return ai_Enum_Config.SI_Question_Type.SI_Question_C;
        }
    }

    public int getControlType() {
        return CommonInsItem.getControlType(sConfig);
    }

    public boolean bSimpleInsItem() {
        ai_Inspection oInspection = CommonDB.findInspectionById(iInsID);
        ai_Enum_Config.SI_Inspection_Type insType = oInspection.getEnumInsType();
        ai_Enum_Config.SI_Question_Type qType = getQuestionType();
        return (insType == Full && (qType == SI_Question_G || qType == SI_Question_A)) ||
                ((insType == Simple) && (qType == SI_Question_C || qType == SI_Question_G || qType == SI_Question_A));
    }

    public String getSPrompt() {
        return sPrompt;
    }

    public int getILevel() {
        return iLevel;
    }
}
