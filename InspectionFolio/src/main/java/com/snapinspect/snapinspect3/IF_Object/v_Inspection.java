package com.snapinspect.snapinspect3.IF_Object;

import android.database.Cursor;

import com.snapinspect.snapinspect3.Helper.CommonDB;

import java.util.ArrayList;

/**
 * Created by terry<PERSON> on 13/10/18.
 */

public class v_Inspection{



    public int iInspectionID;
    public int iSInsID;
    public int iSAssetID;
    public String sTitle;
    public String sAddress;
    public String sInsTitle;
    public String dtStartDate;
    public String dtEndDate;
    public boolean bComplete;
    public boolean bSynced;
    public String sCustom1;
    public String sCustom2;
    public int iSScheduleID;
    public String sTag;
    public String sRef;

    public String TAG_BUILDING = "building";
    public String TAG_UNIT  = "unit";
    public String TAG_ASSET = "asset";
    public String TAG_ROOM = "room";
    public void Update() {
        ai_Inspection oTempIns = CommonDB.findInspectionById(this.iInspectionID);
        oTempIns.sCustomOne = this.sCustom1;
        oTempIns.sCustomTwo = this.sCustom2;
        oTempIns.bComplete = this.bComplete;
        oTempIns.bSynced = this.bSynced;
        CommonDB.saveInspection(oTempIns);
    }

    public v_Inspection(Cursor oCursor){
        try {
            this.iInspectionID = oCursor.getInt(oCursor.getColumnIndex( "iInspectionID"));
            this.iSInsID = oCursor.getInt(oCursor.getColumnIndex( "iSInsID"));
            this.iSAssetID = oCursor.getInt(oCursor.getColumnIndex( "iSAssetID"));
            this.sTitle = oCursor.getString(oCursor.getColumnIndex( "sTitle"));
            this.sInsTitle = oCursor    .getString(oCursor.getColumnIndex( "sInsTitle"));
            this.dtStartDate = oCursor.getString(oCursor.getColumnIndex( "dtStartDate"));
            this.dtEndDate = oCursor.getString(oCursor.getColumnIndex( "dtEndDate"));
            this.bComplete = oCursor.getInt(oCursor.getColumnIndex( "bComplete")) == 1;
            this.bSynced = oCursor.getInt(oCursor.getColumnIndex( "bSynced")) == 1;
            this.sCustom1 = oCursor.getString(oCursor.getColumnIndex( "sCustom1"));
            this.sCustom2 = oCursor.getString(oCursor.getColumnIndex( "sCustom2"));
            this.sRef = oCursor.getString(oCursor.getColumnIndex("sRef"));
            try {
                String sTemp1 = oCursor.getString(oCursor.getColumnIndex("sBuildingAddress"));
                String sTemp2 = oCursor.getString(oCursor.getColumnIndex("sUnitAddress"));
                String sTemp3 = oCursor.getString(oCursor.getColumnIndex("sRoomAddress"));
                ArrayList<String> lsAddress = new ArrayList<String>();
                if (sTemp1 != null && sTemp1.trim().length() > 0) {
                    lsAddress.add(sTemp1.trim());
                    sTag = "Asset";
                    try{
                        if (oCursor.getInt(oCursor.getColumnIndex("bApartment")) == 1){
                            sTag = "Building";
                        }
                    }catch(Exception  eeee){

                    }
                }
                if (sTemp2 != null && sTemp2.trim().length() > 0) {
                    lsAddress.add(sTemp2.trim());
                    sTag = "Unit";
                }
                if (sTemp3 != null && sTemp3.trim().length() > 0) {
                    lsAddress.add(sTemp3.trim());
                    sTag = "Room";
                }
                if (sTag == null || sTag.equalsIgnoreCase("")){
                    sTag = "Asset";
                }
                this.sAddress = String.join(" - ", lsAddress);
            }catch(Exception ex){
                this.sAddress = sTitle;
                this.sTag = "Asset";
            }
            try {
                iSScheduleID = oCursor.getInt(oCursor.getColumnIndex("iSScheduleID"));
            }catch(Exception eee){

            }

        }catch(Exception eaa){
            String cc = "bb";
        }

    }
    public v_Inspection(int _iInspectionID, int _iSInsID, int _iSAssetID, String _sTitle, String _sInsTitle, String _dtStart, String _dtEnd, boolean _bCompleted,
                        boolean _bSynced, String _sCustom1, String _sCustom2, String _sAddress){
        this.iInspectionID = _iInspectionID;
        this.sTitle = _sTitle;
        this.iSInsID = _iSInsID;
        this.iSAssetID = _iSAssetID;
        this.sInsTitle = _sInsTitle;
        this.dtStartDate = _dtStart;
        this.dtEndDate = _dtEnd;
        this.bComplete = _bCompleted;
        this.bSynced = _bSynced;
        this.sCustom1 = _sCustom1;
        this.sCustom2 = _sCustom2;
        this.sAddress  = _sAddress;
    }


    public boolean isBluildingType() {
        try {
            return sTag.toLowerCase().equals(TAG_BUILDING);
        }catch(Exception ex){

        }
        return false;

    }

    public boolean isAssetType() {

        try {
            return sTag.toLowerCase().equals(TAG_ASSET);
        }catch(Exception ex){

        }
        return false;


    }

    public boolean isUnitype() {

        try {
            return sTag.toLowerCase().equals(TAG_UNIT);
        }catch(Exception ex){

        }
        return false;


    }

    public boolean isRoomType() {

        try {
            return sTag.toLowerCase().equals(TAG_ROOM);
        }catch(Exception ex){

        }
        return false;


    }


}
