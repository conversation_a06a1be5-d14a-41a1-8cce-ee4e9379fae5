package com.snapinspect.snapinspect3.IF_Object

import android.content.Context
// Removed: import com.orm.SugarRecord
import com.snapinspect.snapinspect3.Helper.CommonDB
import com.snapinspect.snapinspect3.Helper.CommonDB_Assets
import com.snapinspect.snapinspect3.IF_Object.ai_User
import com.snapinspect.snapinspect3.Helper.CommonJson
import com.snapinspect.snapinspect3.Helper.Constants
import com.snapinspect.snapinspect3.SI_DB.db_InsItem
import com.snapinspect.snapinspect3.SI_DB.db_Media
import java.util.*

val ai_Task.sInsItemTitle: String?
    get() = db_InsItem.GetInsItem_BySInsItemID(iInsItemID)?.sName

val ai_Task.sAssetAddress: String
    get() = CommonDB_Assets.GetAssetBy_iSAssetID(iPropertyID)?.sFullAddress() ?: ""

val ai_Task.isSubTask: Boolean
    get() = iPTaskID > 0

val ai_Task.settings: TaskSettings
    get() = TaskSettings(
        dueDate = if (dtDateDue != null) dtDateDue else null,
        assignTo = iFollowUpCustomerID,
        status = sStatus,
        statusCode = sStatusCode,
        priority = priority,
        taskCategory = sCategory,
        taskCategoryID = iCategoryID,
        members = arrMember?.split(",")
            ?.mapNotNull { it.toIntOrNull() }
            ?.filter { it > 0 } ?: emptyList(),
        isSubTask = isSubTask
    )

val ai_Task.attachments: TaskAttachments
    get() = TaskAttachments(
        photos = sPhotoURL?.split(",")
            ?.mapNotNull(String::toIntOrNull)
            ?.filter { it > 0 }
            ?.map(FileId::Remote) ?: emptyList(),
        video = sVideoURL?.toIntOrNull()
            ?.takeIf { it > 0 }
            ?.let(FileId::Remote)
    )

val ai_Task.sReminder: String
    get() = CommonJson.GetJsonKeyValue("_sReminder", sCustom1) ?: ""

// TaskSettings
data class TaskSettings(
    var dueDate: Date? = null,
    var assignTo: Int? = null,
    var status: String? = null,
    var statusCode: String? = null,
    var priority: TaskPriority = TaskPriority.LOW,
    var taskCategory: String? = null,
    var taskCategoryID: Int? = null,
    var members: List<Int> = emptyList(),
    var isSubTask: Boolean = false
)

val TaskSettings.assignToName: String?
    get() = assignToUser?.sName

val TaskSettings.assignToUser: ai_User?
    get() = assignTo?.let { CommonDB.GetUser(it) }

val TaskSettings.memberToUsers: List<ai_User>
    get() = members
        .mapNotNull { CommonDB.GetUser(it) }
        .filter { (it.iCustomerID ?: 0) > 0 }

// TaskAttachments

sealed class FileId(open val id: Int) {
    data class Local(override val id: Int) : FileId(id)
    data class Remote(override val id: Int) : FileId(id)

    val isRemote: Boolean
        get() = this is Remote

    fun asPhoto(): ai_Photo? {
        return when (this) {
            is Local -> CommonDB.GetPhotoByIdSugar(id.toLong())
            is Remote -> db_Media.getPhotoByServerID(id)
        }
    }

    fun asVideo(): ai_Video? {
        return when (this) {
            is Local -> CommonDB.GetVideoByIdSugar(id.toLong())
            is Remote -> db_Media.getVideoByServerID(id)
        }
    }
}

data class TaskAttachments(
    var photos: List<FileId> = emptyList(),
    var video: FileId? = null
)

// ai_CustomInfo
fun ai_Task.taskCustomInfos(context: Context): List<ai_CustomInfo> {
    return CommonDB.getAllCustomInfos(context)
        .filter { it.isTask }
        .map { customInfo ->
            customInfo.apply {
                sValue = CommonJson.GetJsonKeyValue(
                    "${Constants.Values.kCustomInfoPrefix}${customInfo.iCustomInfoID}",
                    this@taskCustomInfos.sCustom1
                ) ?: ""
            }
        }
}