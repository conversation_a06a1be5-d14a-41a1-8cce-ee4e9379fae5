package com.snapinspect.snapinspect3.IF_Object;

// Removed: import com.orm.SugarRecord;
import androidx.room.Ignore;
import com.snapinspect.snapinspect3.Helper.*;
import com.snapinspect.snapinspect3.SI_DB.db_InsItem;
import com.snapinspect.snapinspect3.SI_DB.db_Media;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.database.RoomDatabaseManager;
import com.snapinspect.snapinspect3.database.entities.InsItem;
import com.snapinspect.snapinspect3.app.App;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.*;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_Inspection_Type.Full;
import static com.snapinspect.snapinspect3.IF_Object.ai_Enum_Config.SI_Question_Type.*;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/03/14.
 */
//AIINS_ITEM
public class ai_InsItem {
    public Long id;

    //Reference to Same ID column
    //I_P_INS_ITEM_ID
    public int iPInsItemID;
    //I_INS_ID
    public int iInsID;
    //I_S_LAYOUT_ID
    public int iSLayoutID;
    //S_NAME
    public String sName;
    //S_VALUE_ONE
    public String sValueOne;
    //S_VALUE_TWO
    public String sValueTwo;
    //S_VALUE_THREE
    public String sValueThree;
    //S_VALUE_FOUR
    public String sValueFour;
    //S_VALUE_FIVE
    public String sValueFive;
    //S_VALUE_SIX
    public String sValueSix;
    //S_Q_TYPE
    public String sQType;
    //S_CONFIG_ONE
    public String sConfigOne;
    //S_CONFIG_TWO
    public String sConfigTwo;
    //S_CONFIG_THREE
    public String sConfigThree;
    //S_CONFIG_FOUR
    public String sConfigFour;
    //S_CONFIG_FIVE
    public String sConfigFive;
    //S_CONFIG_SIX
    public String sConfigSix;
    //B_DELETED
    public boolean bDeleted;
    //B_COMPLETED
    public boolean bCompleted;
    //S_CONFIG
    public String sConfig;
    //I_SORT
    public int iSort;
    //I_S_ASSET_LAYOUT_ID
    public int iSAssetLayoutID;
    //S_NAME_CHANGED
    public String sNameChanged;
    //S_CUSTOM_ONE
    public String sCustomOne;
    //S_CUSTOM_TWO
    public String sCustomTwo;

    // Check if inspection has bypass compulsory
    @Ignore
    public boolean bInspectionByPassCompulsory;

    public ai_InsItem(){

    }
    
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    public ai_InsItem(int iPInsItemID, int iInsID, int iSLayoutID, String sName, String sValue1, String sValue2, String sValue3,
                      String sValue4,String sValue5, String sValue6, String sQType, String sConfigOne, String sConfigTwo, String sConfigThree,
                      String sConfigFour, String sConfigFive, String sConfigSix, String sConfig, boolean bCompleted,
                      int iSort, int _iSAssetLayoutID, String _sNameChanged, String _sCustom1, String _sCustom2){
        this.iPInsItemID = iPInsItemID;
        this.iInsID = iInsID;
        this.iSLayoutID = iSLayoutID;
        this.sName = sName;
        this.sValueOne = sValue1;
        this.sValueTwo = sValue2;
        this.sValueThree = sValue3;
        this.sValueFour = sValue4;
        this.sValueFive = sValue5;
        this.sValueSix = sValue6;
        this.sQType = sQType;
        this.sConfigOne = sConfigOne;
        this.sConfigTwo = sConfigTwo;
        this.sConfigThree = sConfigThree;
        this.sConfigFour = sConfigFour;
        this.sConfigFive = sConfigFive;
        this.sConfigSix = sConfigSix;
        this.sConfig = sConfig;
        this.bCompleted = bCompleted;
        this.bDeleted = false;
        this.iSort = iSort;
        this.iSAssetLayoutID = _iSAssetLayoutID;
        this.sNameChanged = _sNameChanged;
        this.sCustomOne = _sCustom1;
        this.sCustomTwo = _sCustom2;
    }

    /// Has any of values
    public boolean bEdited() {
        ai_Item item = new ai_Item(this);

        String[] values = new String[] {
            StringUtils.isEmpty(sValueOne) ? "" : sValueOne,
            StringUtils.isEmpty(sValueTwo) ? "" : sValueTwo,
            StringUtils.isEmpty(sValueThree) ? "" : sValueThree,
            StringUtils.isEmpty(sValueFour) ? "" : sValueFour,
            StringUtils.isEmpty(sValueFive) ? "" : sValueFive,
            StringUtils.isEmpty(sValueSix) ? "" : sValueSix,
        };

        int oControlType = item.getControlType();
        if (oControlType == ai_Enum_Config.SI_CONTROL_TYPE_SCHK
                || oControlType == ai_Enum_Config.SI_CONTROL_TYPE_MCHK
                || oControlType == ai_Enum_Config.SI_CONTROL_TYPE_CHK) {
            String allValue = String.join("|", values);
            return allValue.contains("Y") || allValue.contains("N");
        } else if (oControlType == -1) {
            String allValue = String.join("|", values);
            String[] flatValues = allValue.split("\\|");
            for (String value: flatValues) {
                if (!value.isEmpty() && !value.equalsIgnoreCase("A")) {
                    return true;
                }
            }
        } else {
            String[] configs = {
                StringUtils.isEmpty(sConfigOne) ? "" : sConfigOne,
                StringUtils.isEmpty(sConfigTwo) ? "" : sConfigTwo,
                StringUtils.isEmpty(sConfigThree) ? "" : sConfigThree,
                StringUtils.isEmpty(sConfigFour) ? "" : sConfigFour,
                StringUtils.isEmpty(sConfigFive) ? "" : sConfigFive,
                StringUtils.isEmpty(sConfigSix) ? "" : sConfigSix,
            };
            for (String config: configs) {
                if (!config.isEmpty()) return true;
            }
        }

        return false;
    }

    public boolean canAssignAllRating() {
        ai_Inspection oInspection = CommonDB.findInspectionById(iInsID);
        return sQType.equalsIgnoreCase(SI_QUESTION_TYPE_C) && oInspection.getEnumInsType() == Full;
    }

    public boolean hasCompulsoryItems() {
        if (bInspectionByPassCompulsory) return false;

        List<ai_InsItem> items = CommonDB.GetChildInsItemSugar(getId(), iInsID);
        if (items == null || items.isEmpty()) return false;
        for (ai_InsItem item: items) {
            if (item.hasCompulsoryConfigs()) return true;
        }
        return false;
    }

    public boolean hasCompulsoryConfigs() {
        ai_Item item = new ai_Item(this);
        if (item.getQuestionType() == SI_Question_C) {
            ai_InsItem p = db_InsItem.GetInsItem_ByID(iPInsItemID);
            if (p == null) return false;
            return isCompulsory(p.sConfigOne, sValueOne)
                    || isCompulsory(p.sConfigTwo, sValueTwo)
                    || isCompulsory(p.sConfigThree, sValueThree)
                    || isCompulsory(p.sConfigFour, sValueFour)
                    || isCompulsory(p.sConfigFive, sValueFive)
                    || isCompulsory(p.sConfigSix, sValueSix);
        } else if (item.getQuestionType() == SI_Question_V) {
            String bCompulsory = CommonJson.GetJsonKeyValue(Constants.Keys.bC, sConfigOne);
            if (CommonHelper.getInt(bCompulsory) <= 0) return false;

            ai_Video oVideo = db_Media.GetVideoByInsItemID(this);
            return oVideo == null || oVideo.getId() <= 0;
        } else if (item.getQuestionType() == SI_Question_S) {
            String bCompulsory = CommonJson.GetJsonKeyValue(Constants.Keys.bC, sConfigOne);
            if (CommonHelper.getInt(bCompulsory) <= 0) return false;

            List<ai_Photo> lsPhoto = CommonDB.GetPhotoByInsItemIDSugar(getId().intValue());
            return lsPhoto == null || lsPhoto.isEmpty() || !CommonHelper.bFileExist(lsPhoto.get(0).getFile());
        } else {
            return isCompulsory(sConfigOne, sValueOne)
                    || isCompulsory(sConfigTwo, sValueTwo)
                    || isCompulsory(sConfigThree, sValueThree)
                    || isCompulsory(sConfigFour, sValueFour)
                    || isCompulsory(sConfigFive, sValueFive)
                    || isCompulsory(sConfigSix, sValueSix);
        }
    }

    public boolean isConditionalCompolusory(String sConfig) {
        String bC = CommonJson.GetJsonKeyValue(Constants.Keys.bC, sConfig);
        return !StringUtils.isEmpty(CommonJson.GetJsonKeyValue(Constants.Keys.bcType, bC));
    }

    public boolean isCompulsory(String sConfig, String sValue) {
        if (CommonInsItem.getConfig_bWeb(sConfig)) return false;

        String bCompulsory = CommonJson.GetJsonKeyValue(Constants.Keys.bC, sConfig);
        String bCType = CommonJson.GetJsonKeyValue(Constants.Keys.bcType, bCompulsory);
        boolean isCompulsory;
        int compulsoryCount = 0;
        if (Constants.CompulsoryType.rating.equals(bCType)) {
            String[] bcAndV = CommonJson.GetStringsFromJsonArray(CommonJson.GetJsonKeyValue(Constants.Keys.bcAndV, bCompulsory));
            String[] bcOrV = CommonJson.GetStringsFromJsonArray(CommonJson.GetJsonKeyValue(Constants.Keys.bcOrV, bCompulsory));
            isCompulsory =
                    (bcAndV.length > 0 && checkIfSelectAllTheItems(bcAndV)) ||
                    (bcOrV.length > 0 && checkIfSelectOneOfThemItems(bcOrV)
            );
            if (isCompulsory) compulsoryCount = 1;
        } else {
            compulsoryCount = CommonHelper.getInt(bCompulsory);
            isCompulsory = compulsoryCount > 0;
        }

        if (!isCompulsory) return false;

        switch (CommonInsItem.getControlType(sConfig)) {
            case SI_CONTROL_TYPE_SEL:
            case SI_CONTROL_TYPE_MSEL:
            case SI_CONTROL_TYPE_CMT:
            case SI_CONTROL_TYPE_NUM:
            case SI_CONTROL_TYPE_DT:
                return StringUtils.isEmpty(sValue);
            case SI_CONTROL_TYPE_COST:
                return CommonHelper.getDouble(sValue) <= 0;
            case SI_CONTROL_TYPE_PTO:
            case SI_CONTROL_TYPE_SCAN:
                return db_Media.GetPhotosCountForIDs(sValue) < compulsoryCount;
            case SI_CONTROL_TYPE_CHK:
            case SI_CONTROL_TYPE_MCHK:
                return !(sValue.contains("Y") || sValue.contains("N"));
            case SI_CONTROL_TYPE_SCHK: {
                String[] sArrayItems = CommonInsItem.getCHKItemsWithAttributeName("D", sConfig);
                String[] sArrayValue = sValue.split("\\|");
                ArrayList<String> checks = new ArrayList<>();
                for (int i = 0; i < sArrayItems.length; i++) {
                    if (CommonHelper.getInt(sArrayItems[i]) == 1) continue;
                    if (i < sArrayValue.length) checks.add(sArrayValue[i]);
                }
                return !checks.isEmpty() && !(checks.contains("Y") || checks.contains("N"));
            }
            case SI_CONTROL_TYPE_LST:
                return StringUtils.isEmpty(sValue) || sValue.contains("0");
            default:
                return false;
        }
    }

    private boolean checkIfSelectAllTheItems(String[] items) {
        for (int i = 0; i < 6; i++) {
            String[] ratings = CommonInsItem.getAllSelectedCHKLabel(
                    CommonHelper.getConfigById(i + 1, this),
                    CommonHelper.getValueById(i + 1, this));
            ArrayList<String> allSatisfied = new ArrayList<>();
            for (String item: items) {
                boolean found;
                for (String rating: ratings) {
                    found = item.equalsIgnoreCase(rating);
                    if (found) {
                        allSatisfied.add(item);
                        break;
                    }
                }
            }

            if (Arrays.equals(items, allSatisfied.toArray(new String[0]))) return true;
        }

        return false;
    }

    private boolean checkIfSelectOneOfThemItems(String[] items) {
        for (int i = 0; i < 6; i++) {
            String[] ratings = CommonInsItem.getAllSelectedCHKLabel(
                    CommonHelper.getConfigById(i + 1, this),
                    CommonHelper.getValueById(i + 1, this));
            for (String item: items) {
                boolean found;
                for (String rating: ratings) {
                    found = item.equalsIgnoreCase(rating);
                    if (found) return true;
                }
            }
        }
        return false;
    }

    public boolean checkIfNeedValidatingCompulsoryItem(String sFV) {
        if (!StringUtils.isEmpty(sFV) && isConditionalCompolusory(sFV)) {
            return true;
        }
        return CommonHelper.getInt(CommonJson.GetJsonKeyValue("IN", sConfig)) > 0;
    }

    public boolean isHidden() {
        return CommonHelper.getInt(CommonJson.GetJsonKeyValue("_bH", sConfig)) > 0;
    }

    public long getServerInsItemID() {
        return CommonHelper.getInt(CommonJson.GetJsonKeyValue("_iInsItemID", sCustomOne));
    }

    public boolean hasFloorPlanDropPins() {
        return !db_InsItem.getDropPinData(this).isEmpty();
    }

    public String getParentItemName() {
        ai_InsItem parent = db_InsItem.GetInsItem_ByID(iPInsItemID);
        return parent == null ? null : parent.sName;
    }

    public boolean hasSelectedCHKItem(String sRating) {
        ai_InsItem parent = db_InsItem.GetInsItem_ByID(iPInsItemID);
        for (int i = 0; i < 6; i++) {
            String config = CommonHelper.GetConfig(i + 1, this, parent);
            String sValue = CommonHelper.GetValue(i + 1, this);
            String[] ratings = CommonInsItem.getAllSelectedCHKLabel(config, sValue);
            if (Arrays.asList(ratings).contains(sRating)) return true;
        }
        return false;
    }

    // Helper method to get Room Database Manager
    private static RoomDatabaseManager getRoomManager() {
        return RoomDatabaseManager.getInstance(App.getContext());
    }

    // Convert to Room entity
    public InsItem toRoomEntity() {
        InsItem roomEntity = new InsItem();
        roomEntity.id = this.id;
        roomEntity.iInsID = this.iInsID;
        roomEntity.iPInsItemID = this.iPInsItemID;
        roomEntity.iSLayoutID = this.iSLayoutID;
        roomEntity.iSAssetLayoutID = this.iSAssetLayoutID;
        roomEntity.iSort = this.iSort;
        roomEntity.sName = this.sName;
        roomEntity.sNameChanged = this.sNameChanged;
        roomEntity.sQType = this.sQType;
        roomEntity.sValueOne = this.sValueOne;
        roomEntity.sValueTwo = this.sValueTwo;
        roomEntity.sValueThree = this.sValueThree;
        roomEntity.sValueFour = this.sValueFour;
        roomEntity.sValueFive = this.sValueFive;
        roomEntity.sValueSix = this.sValueSix;
        roomEntity.sConfigOne = this.sConfigOne;
        roomEntity.sConfigTwo = this.sConfigTwo;
        roomEntity.sConfigThree = this.sConfigThree;
        roomEntity.sConfigFour = this.sConfigFour;
        roomEntity.sConfigFive = this.sConfigFive;
        roomEntity.sConfigSix = this.sConfigSix;
        roomEntity.sConfig = this.sConfig;
        roomEntity.sCustomOne = this.sCustomOne;
        roomEntity.sCustomTwo = this.sCustomTwo;
        roomEntity.bCompleted = this.bCompleted;
        roomEntity.bDeleted = this.bDeleted;
        return roomEntity;
    }

    // Create from Room entity
    public static ai_InsItem fromRoomEntity(InsItem roomEntity) {
        if (roomEntity == null) return null;
        
        ai_InsItem sugar = new ai_InsItem();
        sugar.id = roomEntity.id;
        sugar.iInsID = roomEntity.iInsID != null ? roomEntity.iInsID : 0;
        sugar.iPInsItemID = roomEntity.iPInsItemID != null ? roomEntity.iPInsItemID : 0;
        sugar.iSLayoutID = roomEntity.iSLayoutID != null ? roomEntity.iSLayoutID : 0;
        sugar.iSAssetLayoutID = roomEntity.iSAssetLayoutID != null ? roomEntity.iSAssetLayoutID : 0;
        sugar.iSort = roomEntity.iSort != null ? roomEntity.iSort : 0;
        sugar.sName = roomEntity.sName;
        sugar.sNameChanged = roomEntity.sNameChanged;
        sugar.sQType = roomEntity.sQType;
        sugar.sValueOne = roomEntity.sValueOne;
        sugar.sValueTwo = roomEntity.sValueTwo;
        sugar.sValueThree = roomEntity.sValueThree;
        sugar.sValueFour = roomEntity.sValueFour;
        sugar.sValueFive = roomEntity.sValueFive;
        sugar.sValueSix = roomEntity.sValueSix;
        sugar.sConfigOne = roomEntity.sConfigOne;
        sugar.sConfigTwo = roomEntity.sConfigTwo;
        sugar.sConfigThree = roomEntity.sConfigThree;
        sugar.sConfigFour = roomEntity.sConfigFour;
        sugar.sConfigFive = roomEntity.sConfigFive;
        sugar.sConfigSix = roomEntity.sConfigSix;
        sugar.sConfig = roomEntity.sConfig;
        sugar.sCustomOne = roomEntity.sCustomOne;
        sugar.sCustomTwo = roomEntity.sCustomTwo;
        sugar.bCompleted = roomEntity.bCompleted != null ? roomEntity.bCompleted : false;
        sugar.bDeleted = roomEntity.bDeleted != null ? roomEntity.bDeleted : false;
        return sugar;
    }

    // Sugar ORM compatibility methods - now using Room DAO
    public Long save() {
        try {
            InsItem roomEntity = this.toRoomEntity();
            if (this.id == null) {
                // Insert new inspection item
                long newId = getRoomManager().getDatabase().insItemDao().insertInsItem(roomEntity);
                this.id = newId;
            } else {
                // Update existing inspection item
                getRoomManager().getDatabase().insItemDao().updateInsItem(roomEntity);
            }
            return this.id;
        } catch (Exception ex) {
            // Fallback to simple ID generation if Room fails
            if (this.id == null) {
                this.id = System.currentTimeMillis();
            }
            return this.id;
        }
    }

    public static List<ai_InsItem> find(Class<ai_InsItem> clazz, String whereClause, String... whereArgs) {
        try {
            // Basic implementation - more sophisticated parsing would be needed for full compatibility
            List<InsItem> roomInsItems = getRoomManager().getDatabase().insItemDao().getAllInsItems();
            List<ai_InsItem> sugarInsItems = new ArrayList<>();
            for (InsItem roomInsItem : roomInsItems) {
                sugarInsItems.add(fromRoomEntity(roomInsItem));
            }
            return sugarInsItems;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public static ai_InsItem findById(Class<ai_InsItem> clazz, Object id) {
        try {
            if (id instanceof Long) {
                InsItem roomInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById((Long) id);
                return fromRoomEntity(roomInsItem);
            } else if (id instanceof Integer) {
                InsItem roomInsItem = getRoomManager().getDatabase().insItemDao().getInsItemById(((Integer) id).longValue());
                return fromRoomEntity(roomInsItem);
            }
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    public static List<ai_InsItem> listAll(Class<ai_InsItem> clazz) {
        try {
            List<InsItem> roomInsItems = getRoomManager().getDatabase().insItemDao().getAllInsItems();
            List<ai_InsItem> sugarInsItems = new ArrayList<>();
            for (InsItem roomInsItem : roomInsItems) {
                sugarInsItems.add(fromRoomEntity(roomInsItem));
            }
            return sugarInsItems;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public static List<ai_InsItem> findWithQuery(Class<ai_InsItem> clazz, String query, String... args) {
        try {
            // Basic implementation - this would need more sophisticated parsing for full compatibility
            List<InsItem> roomInsItems = getRoomManager().getDatabase().insItemDao().getAllInsItems();
            List<ai_InsItem> sugarInsItems = new ArrayList<>();
            for (InsItem roomInsItem : roomInsItems) {
                sugarInsItems.add(fromRoomEntity(roomInsItem));
            }
            return sugarInsItems;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public boolean delete() {
        try {
            if (this.id != null) {
                // Use soft delete
                this.bDeleted = true;
                return this.save() != null;
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }

}
