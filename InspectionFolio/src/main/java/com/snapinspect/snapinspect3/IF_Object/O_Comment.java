package com.snapinspect.snapinspect3.IF_Object;

import java.util.ArrayList;
import java.util.HashMap;

public class O_Comment {

    public enum O_Comment_Type {
        quickPhrase, smartComment
    }

    public int order;

    public long id;
    public O_Comment_Type type;
    public String sTitle;
    public String sBody;

    public O_Comment(ai_QuickPhrase phrase) {
        this.id = phrase.iQuickPhraseID;
        this.type = O_Comment_Type.quickPhrase;
        this.sBody = phrase.sComments;
    }

    public O_Comment(HashMap<String, String> smartComment) {
        try {
            this.id = Integer.parseInt(smartComment.get("I"));
        } catch (Exception e) {
            this.id = 0;
        }
        this.type = O_Comment_Type.smartComment;
        this.sTitle = smartComment.get("T");
        this.sBody = smartComment.get("C");
    }

    public boolean equals(O_Comment obj) {
        return type == obj.type &&
                id == obj.id &&
                stringNullAsEmpty(sTitle).equals(stringNullAsEmpty(obj.sTitle)) &&
                stringNullAsEmpty(sBody).equals(stringNullAsEmpty(obj.sBody));
    }

    public boolean containedIn(ArrayList<O_Comment> comments) {
        for (O_Comment comment : comments) {
            if (comment.equals(this)) { return true; }
        }
        return false;
    }

    public int indexIn(ArrayList<O_Comment> comments) {
        for (int i = 0; i < comments.size(); i++) {
            if (equals(comments.get(i))) { return i; }
        }
        return -1;
    }

    private String stringNullAsEmpty(String original) {
        return original == null ? "" : original;
    }
}
