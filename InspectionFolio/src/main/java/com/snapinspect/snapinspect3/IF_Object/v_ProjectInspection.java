package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class v_ProjectInspection extends ai_ProjectInspection {
    public String sCustomOne;
    public String sCustomTwo;
    public String sAddressOne;
    public String sAddressTwo;
    public String sTitle;
    public String sInsTitle;
    public String sReference;
    public String sInsTypeTitle;
    public boolean bInsCompleted;
    public boolean bInsSynced;
    public String dtInsStart;
    public String dtInsEnd;

    public String getAddress() {
        List<String> addressItems = new ArrayList<>();
        if (!StringUtils.isEmpty(sAddressOne) && !StringUtils.isEmpty(sAddressOne.trim())) {
            addressItems.add(sAddressOne.trim());
        }
        if (!StringUtils.isEmpty(sAddressTwo) && !StringUtils.isEmpty(sAddressTwo.trim())) {
            addressItems.add(sAddressTwo.trim());
        }
        return String.join(", ", addressItems);
    }

    public Date getInsStartDate() {
        return DateUtils.parse(dtInsStart, "yyyy-MM-dd HH:mm");
    }

    public Date getInsEndDate() {
        return DateUtils.parse(dtInsEnd, "yyyy-MM-dd HH:mm");
    }

    public String getInspectionTitle() {
        return StringUtils.ifEmpty(getAddress(), sTitle, getCustomInsTitle());
    }
}
