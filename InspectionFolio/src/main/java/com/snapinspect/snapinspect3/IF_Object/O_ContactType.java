package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.Helper.CommonHelper;
import org.json.JSONObject;

public class O_ContactType {
    public static final String CONTACT_COLOR_DEFAULT = "#F40056";

    public String sLabel;
    public boolean bPrimary;
    public boolean bDateRange;
    public int iSort;
    public String sColorCode;

    public O_ContactType(String sLabel, boolean bPrimary, boolean bDateRange, int iSort, String sColorCode) {
        this.sLabel = sLabel;
        this.bPrimary = bPrimary;
        this.bDateRange = bDateRange;
        this.iSort = iSort;
        this.sColorCode = sColorCode;
    }

    public O_ContactType(JSONObject oObject) {
        this.sLabel = oObject.optString("sLabel");
        this.bPrimary = CommonHelper.getBoolean(oObject.optString("bPrimary"));
        this.bDateRange = CommonHelper.getBoolean(oObject.optString("bDateRange"));
        this.iSort = oObject.optInt("iSort", 0);
        this.sColorCode = oObject.optString("sColorCode");
    }
}
