package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class ai_CustomInfo {
    public enum Type {
        TASK("task"),
        ASSET("asset"),
        INSPECTION("ins");

        private final String mValue;

        Type(String value) {
            this.mValue = value;
        }

        public String getValue() {
            return mValue;
        }

        public static Type fromString(String value) {
            for (Type type : Type.values()) {
                if (type.mValue.equalsIgnoreCase(value)) {
                    return type;
                }
            }
            return TASK; // Default fallback
        }
    }

    public enum QuestionType {
        MULTIPLE_CHECK("mulcheck"),
        FILE("file"),
        CHECK("check"), 
        <PERSON><PERSON><PERSON>("radio"),
        TEXT_AREA("textarea"),
        TEXT("text"),
        DATE("date");

        private final String mValue;
        QuestionType(String value) {
            this.mValue = value;
        }
        
        public String getValue() {
            return mValue;
        }

        public static QuestionType fromString(String value) {
            for (QuestionType type : QuestionType.values()) {
                if (type.mValue.equalsIgnoreCase(value)) {
                    return type;
                }
            }
            return TEXT; // Default fallback
        }
    }

    public int iCustomInfoID;
    public int iCompanyID;
    public Type sType;
    public String sLabel;
    public QuestionType sQType;
    public String sConfig;
    public String sGroup;
    public String sDescription;
    public int iSort;
    public String sCustom1;
    public String sCustom2;
    public boolean bDeleted;
    public String sValue;

    public boolean isTask() {
        return sType == Type.TASK;
    }

    public List<String> getOptions() {
        if (sQType != QuestionType.MULTIPLE_CHECK && sQType != QuestionType.RADIO) {
            return Collections.emptyList();
        }
        
        JSONArray options = CommonJson.GetJSONArrayValue("options", sConfig);
        if (options == null) {
            return Collections.emptyList();
        }

        List<String> result = new ArrayList<>();
        for (int i = 0; i < options.length(); i++) {
            String option = options.optString(i, "");
            if (!StringUtils.isEmpty(option)) {
                result.add(option);
            }
        }
        return result;
    }

    public ai_CustomInfo() {
    }

    public ai_CustomInfo copy() {
        ai_CustomInfo copy = new ai_CustomInfo();
        copy.iCustomInfoID = this.iCustomInfoID;
        copy.iCompanyID = this.iCompanyID;
        copy.sType = this.sType;
        copy.sLabel = this.sLabel;
        copy.sQType = this.sQType;
        copy.sConfig = this.sConfig;
        copy.sGroup = this.sGroup;
        copy.sDescription = this.sDescription;
        copy.iSort = this.iSort;
        copy.sCustom1 = this.sCustom1;
        copy.sCustom2 = this.sCustom2;
        copy.bDeleted = this.bDeleted;
        copy.sValue = this.sValue;
        return copy;
    }

    public ai_CustomInfo(JSONObject json) {
        iCustomInfoID = json.optInt("iCustomInfoID", 0);
        iCompanyID = json.optInt("iCompanyID", 0);
        sType = Type.fromString(json.optString("sType", ""));
        sLabel = json.optString("sLabel", "");
        sQType = QuestionType.fromString(json.optString("sQType", ""));
        sConfig = json.optString("sConfig", "");
        sGroup = json.optString("sGroup", "");
        sDescription = json.optString("sDescription", "");
        iSort = json.optInt("iSort", 0);
        sCustom1 = json.optString("sCustom1", "");
        sCustom2 = json.optString("sCustom2", "");
        bDeleted = json.optBoolean("bDeleted", false);
        sValue = json.optString("sValue", "");
    }

}
