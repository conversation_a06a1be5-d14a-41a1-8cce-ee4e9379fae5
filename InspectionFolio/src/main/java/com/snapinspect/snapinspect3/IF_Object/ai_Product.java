package com.snapinspect.snapinspect3.IF_Object;

// Removed: import com.orm.SugarRecord;
// Removed: import com.orm.dsl.Unique;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonProduct;
import org.json.JSONObject;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.List;

import static com.snapinspect.snapinspect3.Helper.Constants.Values.kUnitName;

public class ai_Product {
    public Long id;
    // Removed: @Unique
    public int iSProductID;
    public String sCheckListID;
    public int iCompanyID;
    public String sSKU;
    public String sName;
    public String sModel;
    public String sDesp;
    public String sAssociateArea;
    public String sAssociateItem;
    public double dUnitCost;
    public boolean bAllowEdit;
    public boolean bOneOffCost;
    public String sProductCategory;
    public String sUnitName;
    public String sURL;
    public String sImage;
    public String sCustom1;
    public String sCustom2;
    public boolean bArchive;
    public boolean bDeleted;
    public int iCreatedBy;
    public int iUpdatedBy;
    public String dtUpdate;
    public String dtDateTime;

    public ai_Product() {
    }

    public ai_Product(int iSProductID, String sCheckListID, int iCompanyID, String sSKU, String sName, String sModel, String sDesp, String sAssociateArea, String sAssociateItem, double dUnitCost, boolean bAllowEdit, boolean bOneOffCost, String sProductCategory, String sUnitName, String sURL, String sImage, String sCustom1, String sCustom2, boolean bArchive, boolean bDeleted, int iCreatedBy, int iUpdatedBy, String dtUpdate, String dtDateTime) {
        this.iSProductID = iSProductID;
        this.sCheckListID = sCheckListID;
        this.iCompanyID = iCompanyID;
        this.sSKU = sSKU;
        this.sName = sName;
        this.sModel = sModel;
        this.sDesp = sDesp;
        this.sAssociateArea = sAssociateArea;
        this.sAssociateItem = sAssociateItem;
        this.dUnitCost = dUnitCost;
        this.bAllowEdit = bAllowEdit;
        this.bOneOffCost = bOneOffCost;
        this.sProductCategory = sProductCategory;
        this.sUnitName = sUnitName;
        this.sURL = sURL;
        this.sImage = sImage;
        this.sCustom1 = sCustom1;
        this.sCustom2 = sCustom2;
        this.bArchive = bArchive;
        this.bDeleted = bDeleted;
        this.iCreatedBy = iCreatedBy;
        this.iUpdatedBy = iUpdatedBy;
        this.dtUpdate = dtUpdate;
        this.dtDateTime = dtDateTime;
    }

    public ai_Product(JSONObject data) {
        try {
            JSONObject jsonObject = CommonJson.removeNullAttributes(data);
            this.iSProductID = jsonObject.optInt("iProductID", 0);
            this.sCheckListID = jsonObject.optString("sCheckListID", "");
            this.iCompanyID = jsonObject.optInt("iCompanyID", 0);
            this.sSKU = jsonObject.optString("sSKU", "");
            this.sName = jsonObject.optString("sName", "");
            this.sModel = jsonObject.optString("sModel", "");
            this.sDesp = jsonObject.optString("sDesp", "");
            this.sAssociateArea = jsonObject.optString("sAssociateArea", "");
            this.sAssociateItem = jsonObject.optString("sAssociateItem", "");
            this.dUnitCost = jsonObject.optDouble("dUnitCost", 0);
            this.bAllowEdit = jsonObject.optBoolean("bAllowEdit", false);
            this.bOneOffCost = jsonObject.optBoolean("bOneOffCost", false);
            this.sProductCategory = jsonObject.optString("sProductCategory", "");
            this.sUnitName = jsonObject.optString("sUnitName", kUnitName);
            this.sURL = jsonObject.optString("sURL", "");
            this.sImage = jsonObject.optString("sImage", "");
            this.sCustom1 = jsonObject.optString("sCustom1", "");
            this.sCustom2 = jsonObject.optString("sCustom2", "");
            this.bArchive = jsonObject.optBoolean("bArchive", false);
            this.bDeleted = jsonObject.optBoolean("bDeleted", false);
            this.iCreatedBy = jsonObject.optInt("iCreatedBy", 0);
            this.iUpdatedBy = jsonObject.optInt("iUpdatedBy", 0);
            this.dtUpdate = jsonObject.optString("dtUpdate", "");
            this.dtDateTime = jsonObject.optString("dtDateTime", "");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public List<String> lsAssociatedItems() {
        return Arrays.asList(CommonJson.GetStringsFromJsonArray(sAssociateItem));
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }
}