package com.snapinspect.snapinspect3.IF_Object;

import android.content.Context;
import android.text.TextUtils;
// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/03/14.
 */
//AICONTACT
public class ai_Contact {
    public Long id;

    public interface Keys {
        String kIsPrimary = "bPrm", kDateFrom = "dtF", kDateTo = "dtT";
    }

    //I_S_CONTACT_ID
    public int iSContactID;
    //Reference to ai_Assets ID Column
    //iAssetID is redenunt   use iSAssetID instead
    //I_ASSET_ID
    public int iAssetID;
    //I_S_ASSET_ID
    public int iSAssetID;
    //S_FIRST_NAME
    public String sFirstName;
    //S_LAST_NAME
    public String sLastName;
    //S_PHONE
    public String sPhone;
    //S_MOBILE
    public String sMobile;
    //S_EMAIL
    public String sEmail;
    //S_TAG
    public String sTag;
    //S_FIELD_ONE
    public String sFieldOne;
    //S_FIELD_TWO
    public String sFieldTwo;
    //S_FIELD_THREE
    public String sFieldThree;
    //B_DELETED
    public boolean bDeleted;

    public ai_Contact() {

    }

    public ai_Contact(int iSContactID, int iAssetID, int iSAssetID, String sFirstName, String sLastName, String sPhone, String sMobile,
                      String sEmail, String sTag) {
        this.iSContactID = iSContactID;
        this.iAssetID = iAssetID;
        this.iSAssetID = iSAssetID;
        this.sFirstName = sFirstName;
        this.sLastName = sLastName;
        this.sPhone = sPhone;
        this.sMobile = sMobile;
        this.sEmail = sEmail;
        this.sTag = sTag;
        this.sFieldOne = null;
        this.sFieldTwo = null;
        this.sFieldThree = null;
        this.bDeleted = false;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public boolean isPrimary() {
        return CommonHelper.getBoolean(CommonJson.GetJsonKeyValue(Keys.kIsPrimary, sFieldThree));
    }

    public String dtFrom() {
        return CommonJson.GetJsonKeyValue(Keys.kDateFrom, sFieldThree);
    }

    public String dtTo() {
        return CommonJson.GetJsonKeyValue(Keys.kDateTo, sFieldThree);
    }

    public O_ContactType getContactType(Context context) {
        if (iSContactID <= 0 || getId() <= 0 || TextUtils.isEmpty(sTag)) return null;

        List<O_ContactType> contactTypes = CommonJson.getContactTypes(context);
        if (contactTypes == null || contactTypes.isEmpty()) return null;

        for (O_ContactType contactType : contactTypes) {
            if (!TextUtils.isEmpty(contactType.sLabel) &&
                    contactType.sLabel.equalsIgnoreCase(sTag)) {
                return contactType;
            }
        }

        return null;
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_Contact> find(Class<ai_Contact> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_Contact findById(Class<ai_Contact> clazz, Object id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_Contact> listAll(Class<ai_Contact> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static void deleteAll(Class<ai_Contact> clazz) {
        // Simplified implementation for compatibility
    }

    public static void executeQuery(String query, String... params) {
        // Simplified implementation for compatibility
    }

    public boolean delete() {
        // Simplified implementation for compatibility
        return true;
    }
}
