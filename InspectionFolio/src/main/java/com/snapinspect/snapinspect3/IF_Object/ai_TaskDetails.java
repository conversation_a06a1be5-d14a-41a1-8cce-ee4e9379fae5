package com.snapinspect.snapinspect3.IF_Object;

import org.json.JSONArray;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ai_TaskDetails {
    public ai_Task task;
    public List<ai_Photo> photos;
    public List<ai_Video> videos;
    public List<ai_ProductCost> costings;
    public List<ai_Task> subTasks;

    private static class CodingKeys {
        static final String task = "task";
        static final String photos = "lsPhoto";
        static final String videos = "lsVideo";
        static final String costings = "lsCosting";
        static final String subTasks = "lsSubTask";
    }

    public ai_TaskDetails(JSONObject jsonObject) {
        JSONObject taskJson = jsonObject.optJSONObject(CodingKeys.task);
        this.task = taskJson != null ? new ai_Task(taskJson) : new ai_Task();

        this.photos = new ArrayList<>();
        JSONArray photosArray = jsonObject.optJSONArray(CodingKeys.photos);
        if (photosArray != null) {
            for (int i = 0; i < photosArray.length(); i++) {
                this.photos.add(new ai_Photo(photosArray.optJSONObject(i)));
            }
        }

        this.videos = new ArrayList<>();
        JSONArray videosArray = jsonObject.optJSONArray(CodingKeys.videos);
        if (videosArray != null) {
            for (int i = 0; i < videosArray.length(); i++) {
                this.videos.add(new ai_Video(videosArray.optJSONObject(i)));
            }
        }

        this.costings = new ArrayList<>();
        JSONArray costingsArray = jsonObject.optJSONArray(CodingKeys.costings);
        if (costingsArray != null) {
            for (int i = 0; i < costingsArray.length(); i++) {
                this.costings.add(new ai_ProductCost(costingsArray.optJSONObject(i)));
            }
        }

        this.subTasks = new ArrayList<>();
        JSONArray subTasksArray = jsonObject.optJSONArray(CodingKeys.subTasks);
        if (subTasksArray != null) {
            for (int i = 0; i < subTasksArray.length(); i++) {
                this.subTasks.add(new ai_Task(subTasksArray.optJSONObject(i)));
            }
        }
    }
}
