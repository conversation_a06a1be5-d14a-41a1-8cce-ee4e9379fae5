package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.IF_Object.ai_User;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONObject;

import java.util.Date;
import java.util.TimeZone;

public class ai_TaskComment {
    public static final String COMMENT_TYPE_TEXT = "C";
    public static final String COMMENT_TYPE_FILE = "F";

    public int iCommentID;
    public int iTaskID;
    public Integer iInspectionID;
    public int iCustomerID;
    public int iCompanyID;
    public String sSource;
    public String sType;
    public String sDesp;
    public String sJson;
    public String sCustom1;
    public boolean bDeleted;
    // UTC time from the api response
    public Date dtDateTime;

    public ai_TaskComment(int iCommentID, int iTaskID, Integer iInspectionID, int iCustomerID, int iCompanyID,
                          String sSource, String sType, String sDesp, String sJson, String sCustom1,
                          boolean bDeleted, Date dtDateTime) {
        this.iCommentID = iCommentID;
        this.iTaskID = iTaskID;
        this.iInspectionID = iInspectionID;
        this.iCustomerID = iCustomerID;
        this.iCompanyID = iCompanyID;
        this.sSource = sSource;
        this.sType = sType;
        this.sDesp = sDesp;
        this.sJson = sJson;
        this.sCustom1 = sCustom1;
        this.bDeleted = bDeleted;
        this.dtDateTime = dtDateTime;
    }

    public ai_TaskComment(JSONObject jsonObject) {
        this.iCommentID = jsonObject.optInt("iCommentID", 0);
        this.iTaskID = jsonObject.optInt("iTaskID", 0);
        this.iInspectionID = jsonObject.optInt("iInspectionID", 0);
        this.iCustomerID = jsonObject.optInt("iCustomerID", 0);
        this.iCompanyID = jsonObject.optInt("iCompanyID", 0);
        this.sSource = jsonObject.optString("sSource", "");
        this.sType = jsonObject.optString("sType", "");
        this.sDesp = jsonObject.optString("sDesp", "");
        this.sJson = CommonJson.GetJsonKeyValue_String("sJson", jsonObject);
        this.sCustom1 = CommonJson.GetJsonKeyValue_String("sCustom1", jsonObject);
        this.bDeleted = jsonObject.optBoolean("bDeleted", false);
        this.dtDateTime = parseDateTimeFromJson(jsonObject);
    }

    private Date parseDateTimeFromJson(JSONObject json) {
        String dateStr = json.optString("dtDateTime", "");
        return StringUtils.isEmpty(dateStr) ? null : DateUtils.parse(
                dateStr, TimeZone.getTimeZone("UTC"), DateUtils.possibleDateFormats
        );
    }

    public ai_User getAuthor() {
        return CommonDB.GetUser(iCustomerID);
    }
}
