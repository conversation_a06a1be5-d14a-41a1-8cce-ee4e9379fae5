package com.snapinspect.snapinspect3.IF_Object;

import android.database.Cursor;
import com.snapinspect.snapinspect3.SI_DB.db_Asset;

import java.util.ArrayList;

/**
 * Created by te<PERSON><PERSON> on 26/03/19.
 */

public class v_Asset {
    public enum AssetType {
        ASSET, UNIT, ROOM
    }

    public int iAssetID;
    public int iSAssetID;
    public int iPSAssetID;
    public int iPPSAssetID;
    public String sRef;
    public String sBuildingAddress;
    public String sUnitAddress;
    public String sRoomAddress;
    public int iCustomerID;
    public int iGroupID;
    public boolean bApartment;
    public String sA_Custom1;
    public String sA_Custom2;
    public String sTag;
    public v_Asset(){}
    public v_Asset(Cursor oCursor){
        try {

            this.iAssetID = oCursor.getInt(oCursor.getColumnIndex( "iID"));
            if (oCursor.getColumnIndex("iAssetID") > 0){
                this.iSAssetID = oCursor.getInt(oCursor.getColumnIndex( "iAssetID"));
            }
            else if (oCursor.getColumnIndex("iSAssetID") > 0){
                this.iSAssetID = oCursor.getInt(oCursor.getColumnIndex( "iSAssetID"));
            }
            else{
                this.iSAssetID = 0;
            }

            int iPSAssetID_Index = oCursor.getColumnIndexOrThrow("iPAssetID");
            if (!oCursor.isNull(iPSAssetID_Index)) {

                this.iPSAssetID = oCursor.getInt(iPSAssetID_Index);
            }
            else{
                this.iPSAssetID = 0;
            }
            int iPPSAssetID_Index = oCursor.getColumnIndexOrThrow("iPPAssetID");
            if (!oCursor.isNull(iPPSAssetID_Index)) {

                this.iPPSAssetID = oCursor.getInt(iPPSAssetID_Index);
            }
            else{
                this.iPPSAssetID = 0;
            }
            //this.iPPSAssetID = oCursor.getInt(oCursor.getColumnIndex( "iPPSAssetID"));
            this.sRef = oCursor    .getString(oCursor.getColumnIndex( "sRef"));
            this.sBuildingAddress = oCursor.getString(oCursor.getColumnIndex( "sBuildingAddress"));
            this.sUnitAddress = oCursor.getString(oCursor.getColumnIndex( "sUnitAddress"));
            this.sRoomAddress = oCursor.getString(oCursor.getColumnIndex( "sRoomAddress"));

            if (oCursor.getColumnIndex("iCustomerID") > 0) {
                this.iCustomerID = oCursor.getInt(oCursor.getColumnIndex("iCustomerID"));
            }
            else {
                this.iCustomerID = 0;
            }
            int iGroupID_Index = oCursor.getColumnIndexOrThrow("iGroupID");
            if (!oCursor.isNull(iGroupID_Index)) {

                this.iGroupID = oCursor.getInt(iGroupID_Index);
            }
            else{
                this.iGroupID = 0;
            }
            int bApartment_Index = oCursor.getColumnIndexOrThrow("bApartment");
            if (!oCursor.isNull(bApartment_Index)) {

                this.bApartment = oCursor.getInt(bApartment_Index) == 1;
            }
            else{
                this.bApartment = false;
            }

            this.sA_Custom1 = oCursor.getString(oCursor.getColumnIndex( "sCustom1"));
            this.sA_Custom2 = oCursor.getString(oCursor.getColumnIndex( "sCustom2"));
            try {

                ArrayList<String> lsAddress = new ArrayList<String>();
                if (sBuildingAddress != null && sBuildingAddress.trim().length() > 0) {

                    sTag = "Asset";
                    try{
                        if (oCursor.getInt(oCursor.getColumnIndex("bApartment")) == 1){
                            sTag = "Building";
                        }
                    }catch(Exception  eeee){

                    }
                }
                if (sUnitAddress != null && sUnitAddress.trim().length() > 0) {

                    sTag = "Unit";
                }
                if (sRoomAddress != null && sRoomAddress.trim().length() > 0) {

                    sTag = "Room";
                }
                if (sTag == null || sTag.equalsIgnoreCase("")){
                    sTag = "Asset";
                }
              //  this.sAddress = String.join(" - ", lsAddress);
            }catch(Exception ex) {
                // this.sAddress = sTitle;
                this.sTag = "Asset";
            }

        }catch(Exception eaa){
           // String cc = "bb";
        }

    }

    // Get Asset Type
    public AssetType getAssetType() {
        if (iAssetID > 0 && iPSAssetID > 0) {
            return iPPSAssetID > 0 ? AssetType.ROOM : AssetType.UNIT;
        } else if (iPSAssetID > 0) {
            v_Asset pAsset = db_Asset.GetVAssetBySAssetID(iPSAssetID);
            if (pAsset != null) {
                return pAsset.iPSAssetID > 0 ? AssetType.ROOM : AssetType.UNIT;
            }
        }

        return AssetType.ASSET;
    }
}
