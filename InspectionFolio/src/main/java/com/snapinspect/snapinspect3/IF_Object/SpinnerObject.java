package com.snapinspect.snapinspect3.IF_Object;

/**
 * Created by <PERSON> on 21/07/2014.
 */
public class SpinnerObject {
    private final String text;
    private final int item;
    public SpinnerObject(String text, int item){
        this.text = text;
        this.item = item;

    }
    public String getText(){
        return text;

    }
    public int getItem(){
        return item;

    }
    @Override
    public String toString(){
        return getText();
    }
}
