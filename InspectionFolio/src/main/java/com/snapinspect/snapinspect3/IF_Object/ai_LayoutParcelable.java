package com.snapinspect.snapinspect3.IF_Object;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Created by <PERSON><PERSON><PERSON> on 22/03/14.
 */
public class ai_LayoutParcelable implements Parcelable {
    private int mData;
    public ai_LayoutParcelable(int iNumber){
        mData = iNumber;
    }
    @Override
    public String toString(){
        return "" + mData;
    }
    public void SetNumber(int _iNumber){
        mData = _iNumber;
    }
    public int describeContents() {
        return 0;
    }

    public void writeToParcel(Parcel out, int flags) {
        out.writeInt(mData);
    }

    public static final Parcelable.Creator<ai_LayoutParcelable> CREATOR
            = new Parcelable.Creator<ai_LayoutParcelable>() {
        public ai_LayoutParcelable createFromParcel(Parcel in) {
            return new ai_LayoutParcelable(in);
        }

        public ai_LayoutParcelable[] newArray(int size) {
            return new ai_LayoutParcelable[size];
        }
    };

    private ai_LayoutParcelable(Parcel in) {
        mData = in.readInt();
    }
}
