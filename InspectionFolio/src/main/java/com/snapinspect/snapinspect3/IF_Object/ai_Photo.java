package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.SI_DB.db_Inspection;
import com.snapinspect.snapinspect3.SI_DB.db_Tasks;
import com.snapinspect.snapinspect3.util.FileUtils;
import com.snapinspect.snapinspect3.database.RoomDatabaseManager;
import com.snapinspect.snapinspect3.database.entities.Photo;
import com.snapinspect.snapinspect3.app.App;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/03/14.
 */
//AIPHOTO
public class ai_Photo {
    public Long id;
    //I_INS_ID
    public int iInsID;
    //Reference to ai_InsItem   ID Column
    //I_INS_ITEM_ID
    public int iInsItemID;
    //I_S_PHOTO_ID
    public int iSPhotoID;
    //S_THUMB
    public String sThumb;
    //S_FILE
    public String sFile;
    //S_COMMENTS
    public String sComments;
    //S_LAT
    public String sLat;
    //S_LONG
    public String sLong;
    //B_UPLOADED
    public boolean bUploaded;
    //B_DELETED
    public boolean bDeleted;
    //DT_DATE_TIME
    public String dtDateTime;
    //I_SIZE
    public int iSize;
    // S_FIELD_ONE
    public String sFieldOne;
    public ai_Photo() {
    }

    public ai_Photo(int iInsID, int iInsItemID, int iSPhotoID, String sThumb, String sFile, String sComments, String sLat, String sLong,
                    String dtDateTime, int iSize, String sFieldOne) {
        this.iInsID = iInsID;
        this.iInsItemID = iInsItemID;
        this.iSPhotoID = iSPhotoID;
        this.sThumb = sThumb;
        this.sFile = sFile;
        this.sComments = sComments;
        this.sLat = sLat;
        this.sLong = sLong;
        this.bUploaded = false;
        this.bDeleted = false;
        this.dtDateTime = dtDateTime;
        this.iSize = iSize;
        this.sFieldOne = sFieldOne;
    }

    public ai_Photo(JSONObject jsonObject) {
        this.iInsID = jsonObject.optInt("iInspectionID", 0);
        this.iInsItemID = jsonObject.optInt("iInsItemID", 0); 
        this.iSPhotoID = jsonObject.optInt("iPhotoID", 0);
        this.sComments = jsonObject.optString("sPhotoComment", "");
        this.bUploaded = jsonObject.optBoolean("bUploaded", false);
        this.bDeleted = jsonObject.optBoolean("bDeleted", false);
        this.dtDateTime = jsonObject.optString("dtCreate", "");
        this.iSize = jsonObject.optInt("iSize", 0);
        this.sFieldOne = jsonObject.optString("sAppendix", "");
    }

    // Constructor from Room entity
    public ai_Photo(com.snapinspect.snapinspect3.database.entities.Photo roomEntity) {
        this.id = roomEntity.id;
        this.iInsID = roomEntity.iInsID != null ? roomEntity.iInsID : 0;
        this.iInsItemID = roomEntity.iInsItemID != null ? roomEntity.iInsItemID : 0;
        this.iSPhotoID = roomEntity.iSPhotoID != null ? roomEntity.iSPhotoID : 0;
        this.sThumb = roomEntity.sThumb;
        this.sFile = roomEntity.sFile;
        this.sComments = roomEntity.sComments;
        this.sLat = roomEntity.sLat;
        this.sLong = roomEntity.sLong;
        this.bUploaded = roomEntity.bUploaded != null ? roomEntity.bUploaded : false;
        this.bDeleted = roomEntity.bDeleted != null ? roomEntity.bDeleted : false;
        this.dtDateTime = roomEntity.dtDateTime;
        this.iSize = roomEntity.iSize != null ? roomEntity.iSize : 0;
        this.sFieldOne = roomEntity.sFieldOne;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFile() {
        return FileUtils.validFilePath(sFile);
    }

    public String getThumb() {
        return FileUtils.validFilePath(sThumb);
    }

    public String getGeo() {
        return CommonJson.GetJsonKeyValue("_sGPS", sLat);
    }

    public boolean isUploadedAndUsingWhenEditInspection() {
        ai_Inspection inspection = db_Inspection.GetInspection_ByID(iInsID);
        if (inspection == null) return false;
        return bUploaded && inspection.iSInsID > 0;
    }

    public boolean isTaskPhoto() {
        try {
            int iSTaskID = CommonHelper.getInt(CommonJson.GetJsonKeyValue(Constants.Keys.iTaskID, sFieldOne));
            ai_Task task = db_Tasks.INSTANCE.getTaskBySTaskID(iSTaskID);
            if (task == null || task.sPhotoURL == null) return false;
            
            List<String> photoURLs = Arrays.asList(task.sPhotoURL.split(","));
            return photoURLs.contains(String.valueOf(iSPhotoID));
        } catch (NumberFormatException e) {
            return false;
        }
    }

    // Helper method to get Room Database Manager
    private static RoomDatabaseManager getRoomManager() {
        return RoomDatabaseManager.getInstance(App.getContext());
    }

    // Convert to Room entity
    public Photo toRoomEntity() {
        Photo roomEntity = new Photo();
        roomEntity.id = this.id;
        roomEntity.iInsID = this.iInsID;
        roomEntity.iInsItemID = this.iInsItemID;
        roomEntity.iSPhotoID = this.iSPhotoID;
        roomEntity.sThumb = this.sThumb;
        roomEntity.sFile = this.sFile;
        roomEntity.sComments = this.sComments;
        roomEntity.sLat = this.sLat;
        roomEntity.sLong = this.sLong;
        roomEntity.bUploaded = this.bUploaded;
        roomEntity.bDeleted = this.bDeleted;
        roomEntity.dtDateTime = this.dtDateTime;
        roomEntity.iSize = this.iSize;
        roomEntity.sFieldOne = this.sFieldOne;
        return roomEntity;
    }

    // Create from Room entity
    public static ai_Photo fromRoomEntity(Photo roomEntity) {
        if (roomEntity == null) return null;
        
        ai_Photo sugar = new ai_Photo();
        sugar.id = roomEntity.id;
        sugar.iInsID = roomEntity.iInsID != null ? roomEntity.iInsID : 0;
        sugar.iInsItemID = roomEntity.iInsItemID != null ? roomEntity.iInsItemID : 0;
        sugar.iSPhotoID = roomEntity.iSPhotoID != null ? roomEntity.iSPhotoID : 0;
        sugar.sThumb = roomEntity.sThumb;
        sugar.sFile = roomEntity.sFile;
        sugar.sComments = roomEntity.sComments;
        sugar.sLat = roomEntity.sLat;
        sugar.sLong = roomEntity.sLong;
        sugar.bUploaded = roomEntity.bUploaded != null ? roomEntity.bUploaded : false;
        sugar.bDeleted = roomEntity.bDeleted != null ? roomEntity.bDeleted : false;
        sugar.dtDateTime = roomEntity.dtDateTime;
        sugar.iSize = roomEntity.iSize != null ? roomEntity.iSize : 0;
        sugar.sFieldOne = roomEntity.sFieldOne;
        return sugar;
    }

    // Sugar ORM compatibility methods - now using Room DAO
    public Long save() {
        try {
            Photo roomEntity = this.toRoomEntity();
            if (this.id == null) {
                // Insert new photo
                long newId = getRoomManager().getDatabase().photoDao().insertPhoto(roomEntity);
                this.id = newId;
            } else {
                // Update existing photo
                getRoomManager().getDatabase().photoDao().updatePhoto(roomEntity);
            }
            return this.id;
        } catch (Exception ex) {
            // Fallback to simple ID generation if Room fails
            if (this.id == null) {
                this.id = System.currentTimeMillis();
            }
            return this.id;
        }
    }

    public static List<ai_Photo> find(Class<ai_Photo> clazz, String whereClause, String... whereArgs) {
        try {
            // Basic implementation - more sophisticated parsing would be needed for full compatibility
            List<Photo> roomPhotos = getRoomManager().getDatabase().photoDao().getAllPhotos();
            List<ai_Photo> sugarPhotos = new ArrayList<>();
            for (Photo roomPhoto : roomPhotos) {
                sugarPhotos.add(fromRoomEntity(roomPhoto));
            }
            return sugarPhotos;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public static ai_Photo findById(Class<ai_Photo> clazz, Object id) {
        try {
            if (id instanceof Long) {
                Photo roomPhoto = getRoomManager().getDatabase().photoDao().getPhotoById((Long) id);
                return fromRoomEntity(roomPhoto);
            } else if (id instanceof Integer) {
                Photo roomPhoto = getRoomManager().getDatabase().photoDao().getPhotoById(((Integer) id).longValue());
                return fromRoomEntity(roomPhoto);
            }
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    public static List<ai_Photo> listAll(Class<ai_Photo> clazz) {
        try {
            List<Photo> roomPhotos = getRoomManager().getDatabase().photoDao().getAllPhotos();
            List<ai_Photo> sugarPhotos = new ArrayList<>();
            for (Photo roomPhoto : roomPhotos) {
                sugarPhotos.add(fromRoomEntity(roomPhoto));
            }
            return sugarPhotos;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public boolean delete() {
        try {
            if (this.id != null) {
                // Use soft delete
                this.bDeleted = true;
                return this.save() != null;
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }
}
