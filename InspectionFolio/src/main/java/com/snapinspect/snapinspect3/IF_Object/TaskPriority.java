package com.snapinspect.snapinspect3.IF_Object;

import androidx.annotation.ColorRes;

import com.snapinspect.snapinspect3.R;

import java.util.ArrayList;
import java.util.Arrays;

public enum TaskPriority {

    LOW(1), MILD(2), NORMAL(3), HIGH(4), URGENT(5);

    private final int mValue;

    TaskPriority(int value) {
        this.mValue = value;
    }

    public static TaskPriority getTaskPriority(int value) {
        for (TaskPriority priority : values()) {
            if (priority.mValue == value) {
                return priority;
            }
        }
        return LOW;
    }

    public int getValue() {
        return mValue;
    }

    public String getDisplayName() {
        switch (this) {
            case LOW: return "Low";
            case MILD: return "Mild";
            case NORMAL: return "Normal";
            case HIGH: return "High";
            case URGENT: return "Urgent";
            default: return null;
        }
    }

    public @ColorRes int getBackgroundColor() {
        switch (this) {
            case LOW: return R.color.low_priority_background;
            case MILD: return R.color.mild_priority_background;
            case NORMAL: return R.color.normal_priority_background;
            case HIGH: return R.color.high_priority_background;
            case URGENT: return R.color.urgent_priority_background;
            default: return 0;
        }
    }

    public @ColorRes int getTextColor() {
        return R.color.white_color;
    }

    public static String[] displayNames() {
        ArrayList<String> names = new ArrayList<>();
        for (TaskPriority value: TaskPriority.values()) {
            names.add(value.getDisplayName());
        }
        return names.toArray(new String[] { });
    }
}