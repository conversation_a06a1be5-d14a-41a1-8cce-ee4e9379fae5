package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.Helper.CommonHelper;

/**
 * Created by <PERSON><PERSON><PERSON> on 5/04/14.
 */
public class O_FileName {
    public String sThumbNail;
    public String sFilePath;
    public O_FileName(String _sT, String _sF){
        this.sThumbNail = _sT;
        this.sFilePath = _sF;
    }
    public O_FileName(){

    }

    public static O_FileName getVideoFileName(){
        String sRandomString = CommonHelper.GetTimeString();
        String sFilePath = CommonHelper.sFileRoot + "//";
        return new O_FileName(
                sFilePath + "image_" + sRandomString + "_t.jpg",
                sFilePath + "video_" + sRandomString + ".mp4");
    }

    public static O_FileName getPhotoFileName() {
        String sRandomString = CommonHelper.GetTimeString();
        String sFilePath = CommonHelper.sFileRoot + "//";
        return new O_FileName(
                sFilePath + "image_" + sRandomString + "_t.jpg",
                sFilePath + "image_" + sRandomString + ".jpg");
    }
}
