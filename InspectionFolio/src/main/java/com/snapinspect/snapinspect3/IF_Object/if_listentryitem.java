package com.snapinspect.snapinspect3.IF_Object;

/**
 * Created by <PERSON><PERSON><PERSON> on 12/04/14.
 */

//Import notes. The iID is used as local inspection ID, and iInsID is the server InsID. Be careful.
public class if_listentryitem implements if_listitem{
    public long iID;
    public final String title;
    public final String subtitle;
    public int iInsID;
    public int iSScheduleID;
    public boolean bCompleted;
    public boolean bUploaded;
    public ai_Inspection oInspection;

    public if_listentryitem(long _iID, String title, String subtitle,  int _iInsID, int _iSScheduleID, boolean _bCompleted, boolean _bUploaded) {
        this.iID = _iID;
        this.title = title;
        this.subtitle = subtitle;

        this.iInsID = _iInsID;
        this.iSScheduleID = _iSScheduleID;
        this.bCompleted = _bCompleted;
        this.bUploaded = _bUploaded;
    }

    @Override
    public boolean isSection() {
        return false;
    }
}
