package com.snapinspect.snapinspect3.IF_Object;

// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.database.entities.InsType;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/03/14.
 */
//AIINS_TYPE
public class ai_InsType {
    public Long id;
   // public int iInsTypeID;
    //I_S_INS_TYPE_ID
    public int iSInsTypeID;
    //S_PTC
    public String sPTC;
    //S_INS_TITLE
    public String sInsTitle;
    //S_TYPE
    public String sType;
    //B_REM_LAYOUT
    public boolean bRemLayout;
    //S_FIELD_ONE
    public String sFieldOne;
    //S_FIELD_TWO
    public String sFieldTwo;
    //S_FIELD_THREE
    public String sFieldThree;
    //B_DELETED
    public boolean bDeleted;
    public ai_InsType(){

    }
    public ai_InsType(int iSInsTypeID, String sPTC, String sInsTitle, String sType, boolean bRemLayout){
        this.iSInsTypeID = iSInsTypeID;
        this.sPTC = sPTC;
        this.sInsTitle = sInsTitle;
        this.sType = sType;
        this.bRemLayout = bRemLayout;
        this.sFieldOne = null;
        this.sFieldTwo = null;
        this.sFieldThree = null;
        this.bDeleted = false;
        //this.iInsTypeID = iInsTypeID;
    }
    
    // Constructor to convert from Room entity
    public ai_InsType(InsType roomEntity) {
        this.iSInsTypeID = roomEntity.iSInsTypeID != null ? roomEntity.iSInsTypeID : 0;
        this.sPTC = roomEntity.sPTC;
        this.sInsTitle = roomEntity.sName; // Room entity uses sName instead of sInsTitle
        this.sType = roomEntity.sDescription; // Room entity uses sDescription instead of sType
        this.bRemLayout = false; // Default value, Room entity doesn't have this field
        this.sFieldOne = null; // Default value, Room entity doesn't have this field
        this.sFieldTwo = null; // Default value, Room entity doesn't have this field
        this.sFieldThree = null; // Default value, Room entity doesn't have this field
        this.bDeleted = roomEntity.bDeleted != null ? roomEntity.bDeleted : false;
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_InsType> find(Class<ai_InsType> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_InsType findById(Class<ai_InsType> clazz, Object id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_InsType> listAll(Class<ai_InsType> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static void deleteAll(Class<ai_InsType> clazz) {
        // Simplified implementation for compatibility
    }

    public boolean delete() {
        // Simplified implementation for compatibility
        return true;
    }
}
