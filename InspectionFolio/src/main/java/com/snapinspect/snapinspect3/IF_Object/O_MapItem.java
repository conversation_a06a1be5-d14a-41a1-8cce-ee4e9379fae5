package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.util.StringUtils;

public class O_MapItem<T> {

    public enum CustomInfoType {
        unknown, text, checkBox, pto, textArea, datePicker
    }

    public static final int kAddressInfo = 0;
    public static final int kContact = 1;
    public static final int kMapData = 2;
    public static final int kNone = 4;
    public static final int kAddress = 5;
    public static final int kFloorPlan = 6;

    public int sType;
    public String sKey;
    public T value;
    public String sAssetAttributeID;
    public boolean isEditable;
    public boolean hasPTO;
    public String sInfoName;
    public String sActionName;

    public CustomInfoType customInfoType;

    public void updateCustomInfoType(String sType) {
        this.customInfoType = getCustomInfoType(sType);
    }

    public static CustomInfoType getCustomInfoType(String sType) {
        if (StringUtils.isEmpty(sType)) return CustomInfoType.unknown;
        CustomInfoType customInfoType = CustomInfoType.unknown;
        switch (sType) {
            case "pto":
                customInfoType = CustomInfoType.pto;
                break;
            case "text":
                customInfoType = CustomInfoType.text;
                break;
            case "textarea":
                customInfoType = CustomInfoType.textArea;
                break;
            case "check":
                customInfoType = CustomInfoType.checkBox;
                break;
            case "datepicker":
                customInfoType = CustomInfoType.datePicker;
                break;
            default:
                break;
        }
        return customInfoType;
    }
}
