package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.database.RoomDatabaseManager;
import com.snapinspect.snapinspect3.database.entities.Assets;
import com.snapinspect.snapinspect3.app.App;

import java.util.ArrayList;
import java.util.List;


/**
 * Created by <PERSON><PERSON><PERSON> on 10/03/14.
 */
//AIASSETS
public class ai_Assets {
    public Long id;
    //I_S_ASSET_ID
    public int iSAssetID;
    //I_SP_ASSET_ID
    public int iSPAssetID;
    //S_ADDRESS_ONE
    public String sAddressOne;
    //S_ADDRESS_TWO
    public String sAddressTwo;
    //S_FILTER
    public String sFilter;
    //I_CUSTOMER_ID
    public int iCustomerID;
    //DT_INS_DUE
    public String dtInsDue;
    //S_NOTES
    public String sNotes;
    //S_ALARM
    public String sAlarm;
    //S_KEY
    public String sKey;
    //B_PUSH
    public boolean bPush;
    //S_FIELD_ONE
    public String sFieldOne;
    //S_FIELD_TWO
    public String sFieldTwo;
    //S_FIELD_THREE
    public String sFieldThree;
    //B_DELETED
    public boolean bDeleted;
    //I_PL_VER_ID
    public int iPLVerID;
    //I_GROUP_ID
    public int iGroupID;
    public ai_Assets(){

    }
    public ai_Assets(int iSAssetID, int iSPAssetID, String sAddress1, String sAddress2, String sFilter, int iCustomerID, String dtInsDue, String sNotes, String sAlarm, String sKey, int iPLVerID){
        this.iSAssetID = iSAssetID;
        this.sAddressOne = sAddress1;
        this.sAddressTwo = sAddress2;
        this.sFilter = sFilter;
        this.iCustomerID = iCustomerID;
        this.dtInsDue = dtInsDue;
        this.sNotes = sNotes;
        this.sAlarm = sAlarm;
        this.sKey = sKey;
        this.bPush = false;
        this.sFieldOne = null;
        this.sFieldTwo = null;
        this.sFieldThree = null;
        this.bDeleted = false;
        this.iPLVerID = iPLVerID;
         this.iGroupID = 0;

    }
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    @Override
    public String toString(){
        return sAddressOne + ", " + sAddressTwo;
    }

    public String sFullAddress() {
        ArrayList<String> address = new ArrayList<>();
        if (!StringUtils.isEmpty(sAddressOne)) {
            address.add(sAddressOne);
        }
        if (!StringUtils.isEmpty(sAddressTwo)) {
            address.add(sAddressTwo);
        }
        return String.join(", ", address);
    }

    // Helper method to get Room Database Manager
    private static RoomDatabaseManager getRoomManager() {
        return RoomDatabaseManager.getInstance(App.getContext());
    }

    // Convert to Room entity
    public Assets toRoomEntity() {
        Assets roomEntity = new Assets();
        roomEntity.id = this.id;
        roomEntity.iSAssetID = this.iSAssetID;
        roomEntity.iSPAssetID = this.iSPAssetID;
        roomEntity.sAddressOne = this.sAddressOne;
        roomEntity.sAddressTwo = this.sAddressTwo;
        roomEntity.sFilter = this.sFilter;
        roomEntity.iCustomerID = this.iCustomerID;
        roomEntity.dtInsDue = this.dtInsDue;
        roomEntity.sNotes = this.sNotes;
        roomEntity.sAlarm = this.sAlarm;
        roomEntity.sKey = this.sKey;
        roomEntity.bPush = this.bPush;
        roomEntity.sFieldOne = this.sFieldOne;
        roomEntity.sFieldTwo = this.sFieldTwo;
        roomEntity.sFieldThree = this.sFieldThree;
        roomEntity.bDeleted = this.bDeleted;
        roomEntity.iPLVerID = this.iPLVerID;
        roomEntity.iGroupID = this.iGroupID;
        return roomEntity;
    }

    // Create from Room entity
    public static ai_Assets fromRoomEntity(Assets roomEntity) {
        if (roomEntity == null) return null;
        
        ai_Assets sugar = new ai_Assets();
        sugar.id = roomEntity.id;
        sugar.iSAssetID = roomEntity.iSAssetID != null ? roomEntity.iSAssetID : 0;
        sugar.iSPAssetID = roomEntity.iSPAssetID != null ? roomEntity.iSPAssetID : 0;
        sugar.sAddressOne = roomEntity.sAddressOne;
        sugar.sAddressTwo = roomEntity.sAddressTwo;
        sugar.sFilter = roomEntity.sFilter;
        sugar.iCustomerID = roomEntity.iCustomerID != null ? roomEntity.iCustomerID : 0;
        sugar.dtInsDue = roomEntity.dtInsDue;
        sugar.sNotes = roomEntity.sNotes;
        sugar.sAlarm = roomEntity.sAlarm;
        sugar.sKey = roomEntity.sKey;
        sugar.bPush = roomEntity.bPush != null ? roomEntity.bPush : false;
        sugar.sFieldOne = roomEntity.sFieldOne;
        sugar.sFieldTwo = roomEntity.sFieldTwo;
        sugar.sFieldThree = roomEntity.sFieldThree;
        sugar.bDeleted = roomEntity.bDeleted != null ? roomEntity.bDeleted : false;
        sugar.iPLVerID = roomEntity.iPLVerID != null ? roomEntity.iPLVerID : 0;
        sugar.iGroupID = roomEntity.iGroupID != null ? roomEntity.iGroupID : 0;
        return sugar;
    }

    // Sugar ORM compatibility methods - now using Room DAO
    public Long save() {
        try {
            Assets roomEntity = this.toRoomEntity();
            if (this.id == null) {
                // Insert new asset
                long newId = getRoomManager().getDatabase().assetsDao().insertAsset(roomEntity);
                this.id = newId;
            } else {
                // Update existing asset
                getRoomManager().getDatabase().assetsDao().updateAsset(roomEntity);
            }
            return this.id;
        } catch (Exception ex) {
            // Fallback to simple ID generation if Room fails
            if (this.id == null) {
                this.id = System.currentTimeMillis();
            }
            return this.id;
        }
    }

    public static List<ai_Assets> find(Class<ai_Assets> clazz, String whereClause, String... whereArgs) {
        try {
            // Basic implementation - more sophisticated parsing would be needed for full compatibility
            List<Assets> roomAssets = getRoomManager().getDatabase().assetsDao().getAllAssets();
            List<ai_Assets> sugarAssets = new ArrayList<>();
            for (Assets roomAsset : roomAssets) {
                sugarAssets.add(fromRoomEntity(roomAsset));
            }
            return sugarAssets;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public static ai_Assets findById(Class<ai_Assets> clazz, Object id) {
        try {
            if (id instanceof Long) {
                Assets roomAsset = getRoomManager().getDatabase().assetsDao().getAssetById((Long) id);
                return fromRoomEntity(roomAsset);
            } else if (id instanceof Integer) {
                Assets roomAsset = getRoomManager().getDatabase().assetsDao().getAssetById(((Integer) id).longValue());
                return fromRoomEntity(roomAsset);
            }
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    public static List<ai_Assets> listAll(Class<ai_Assets> clazz) {
        try {
            List<Assets> roomAssets = getRoomManager().getDatabase().assetsDao().getAllAssets();
            List<ai_Assets> sugarAssets = new ArrayList<>();
            for (Assets roomAsset : roomAssets) {
                sugarAssets.add(fromRoomEntity(roomAsset));
            }
            return sugarAssets;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public static void deleteAll(Class<ai_Assets> clazz) {
        try {
            getRoomManager().getDatabase().assetsDao().deleteAllAssets();
        } catch (Exception ex) {
            // Silently fail for compatibility
        }
    }

    public boolean delete() {
        try {
            if (this.id != null) {
                // Use soft delete
                this.bDeleted = true;
                return this.save() != null;
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }
}
