package com.snapinspect.snapinspect3.IF_Object;

// Removed: import com.orm.SugarRecord;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.CommonHelper;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.Constants;
import org.json.JSONObject;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ai_ProjectInspection {
    public Long id;
    // I_S_PROJECT_ASSET_INS_TYPE_ID
    public int iSProjectAssetInsTypeID;
    // I_PROJECT_ID
    public int iProjectID;
    // I_COMPANY_ID
    public int iCompanyID;
    // I_ASSET_ID
    public int iAssetID;
    // I_INS_TYPE_ID
    public int iInsTypeID;
    // I_INSPECTION_ID
    public int iInspectionID;
    // I_S_INSPECTION_ID
    public int iSInspectionID;
    // I_INSPECTOR_ID
    public int iInspectorID;
    // DT_START
    public Date dtStart;
    // DT_END
    public Date dtEnd;
    // S_CUSTOM
    public String sCustom;
    // B_DELETED
    public boolean bDeleted;
    // DT_UPDATE
    public Date dtUpdate;
    // DT_DATE_TIME
    public Date dtDateTime;

    public ai_ProjectInspection() {
    }

    public ai_ProjectInspection(
        int iSProjectAssetInsTypeID,
        int iProjectID,
        int iCompanyID,
        int iAssetID,
        int iInsTypeID,
        int iInspectionID,
        int iSInspectionID,
        int iInspectorID,
        Date dtStart,
        Date dtEnd,
        String sCustom,
        boolean bDeleted,
        Date dtUpdate,
        Date dtDateTime
    ) {
        this.iSProjectAssetInsTypeID = iSProjectAssetInsTypeID;
        this.iProjectID = iProjectID;
        this.iCompanyID = iCompanyID;
        this.iAssetID = iAssetID;
        this.iInsTypeID = iInsTypeID;
        this.iInspectionID = iInspectionID;
        this.iSInspectionID = iSInspectionID;
        this.iInspectorID = iInspectorID;
        this.dtStart = dtStart;
        this.dtEnd = dtEnd;
        this.sCustom = sCustom;
        this.bDeleted = bDeleted;
        this.dtUpdate = dtUpdate;
        this.dtDateTime = dtDateTime;
    }

    public ai_ProjectInspection(JSONObject jsonObject) {
        iSProjectAssetInsTypeID = jsonObject.optInt("iProjectAssetInsTypeID", 0);
        iProjectID = jsonObject.optInt("iProjectID", 0);
        iCompanyID = jsonObject.optInt("iCompanyID", 0);
        iAssetID = jsonObject.optInt("iAssetID", 0);
        iInsTypeID = jsonObject.optInt("iInsTypeID", 0);
        iInspectionID = 0;
        iSInspectionID = jsonObject.optInt("iInspectionID", 0);
        iInspectorID = jsonObject.optInt("iInspectorID", 0);
        try {
            dtStart = Constants.dateFormat.parse(jsonObject.optString("dtStart", ""));
            dtEnd = Constants.dateFormat.parse(jsonObject.optString("dtEnd", ""));
        } catch (ParseException e) {
            ai_BugHandler.logException(e);
        }
        sCustom = jsonObject.optString("sCustom", "");
        bDeleted = jsonObject.optBoolean("bDeleted", false);
        try {
            dtUpdate = Constants.dateFormat.parse(jsonObject.optString("dtUpdate", ""));
            dtDateTime = Constants.dateFormat.parse(jsonObject.optString("dtDateTime", ""));
        } catch (ParseException e) {
            ai_BugHandler.logException(e);
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void checkIfExist() {
        if (iSProjectAssetInsTypeID > 0 ) {
            List<ai_ProjectInspection> projectInspections = CommonDB.findProjectInspections(
                    "I_S_PROJECT_ASSET_INS_TYPE_ID = ?", String.valueOf(iSProjectAssetInsTypeID));
            if (!projectInspections.isEmpty()) {
                ai_ProjectInspection projectInspection = projectInspections.get(0);
                setId(projectInspection.getId());
                iInspectionID = projectInspection.iInspectionID;
            }
        }
    }

    public ai_User getLockedInspector() {
        int id = CommonHelper.getInt(CommonJson.GetJsonKeyValue(Keys.LOCK, sCustom, ""));
        List<ai_User> userList = CommonDB.GetAllUsersSugar();
        for (ai_User user : userList) {
            if (id == user.iCustomerID) return user;
        }
        return null;
    }

    public ai_User getAssignedInspector() {
        int id = CommonHelper.getInt(CommonJson.GetJsonKeyValue(Keys.ASSIGN, sCustom, ""));
        List<ai_User> userList = CommonDB.GetAllUsersSugar();
        for (ai_User user : userList) {
            if (id == user.iCustomerID) return user;
        }
        return null;
    }

    public ai_User getInspector() {
        int id = CommonHelper.getInt(CommonJson.GetJsonKeyValue(Keys.INSPECT, sCustom, ""));
        List<ai_User> userList = CommonDB.GetAllUsersSugar();
        for (ai_User user : userList) {
            if (id == user.iCustomerID) return user;
        }
        return null;
    }

    public void checkIfExistAndSave() {
        checkIfExist();
        CommonDB.saveProjectInspection(this);
    }

    public boolean isUploaded() {
        return iSInspectionID > 0;
    }

    public boolean isCompletedNotUploaded() {
        ai_Inspection inspection = CommonDB.findInspectionById(iInspectionID);
        return inspection != null && inspection.bComplete && !inspection.bSynced;
    }

    public boolean isCompletedAndContinue() {
        ai_Inspection inspection = CommonDB.findInspectionById(iInspectionID);
        if (inspection == null) return false;
        return isUploaded() && inspection.iSInsID == iSInspectionID && !inspection.bSynced;
    }

    public String getCustomInsTitle() {
        return CommonJson.GetJsonKeyValue(Keys.TITLE, sCustom, "");
    }

    public String getInspectionStatusCode() {
        return CommonJson.GetJsonKeyValue(Keys.INS_STATUS_CODE, sCustom, "");
    }

    public String getInspectionStatus() {
        return CommonJson.GetJsonKeyValue(Keys.INS_STATUS, sCustom, "");
    }
    public static final class Keys {
        private Keys() {
            throw new IllegalStateException("Utility class");
        }
        public static final String TITLE = "sTitle";
        public static final String LOCK = "_iLock";
        public static final String ASSIGN = "_iAssign";
        public static final String INSPECT = "_iInspect";
        public static final String INS_STATUS_CODE = "sInsStatusCode";
        public static final String INS_STATUS = "sInsStatus";
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_ProjectInspection> find(Class<ai_ProjectInspection> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_ProjectInspection findById(Class<ai_ProjectInspection> clazz, Long id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_ProjectInspection> listAll(Class<ai_ProjectInspection> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static long count(Class<ai_ProjectInspection> clazz, String whereClause, String[] whereArgs) {
        // Simplified implementation for compatibility
        return 0;
    }

    public boolean delete() {
        // Simplified implementation for compatibility
        return true;
    }
}
