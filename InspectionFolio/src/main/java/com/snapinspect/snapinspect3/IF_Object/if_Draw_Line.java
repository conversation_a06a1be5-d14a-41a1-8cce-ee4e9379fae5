package com.snapinspect.snapinspect3.IF_Object;

import android.graphics.PointF;

/**
 * Created by terrysun1 on 26/12/15.
 */
public class if_Draw_Line extends if_Draw_Base {
    public PointF oStart;
    public PointF oEnd;
    public void if_Draw_Line(int iStoke, int _iColor, PointF _oStart, PointF _oEnd){
        this.iStrokeWidth = iStoke;
        this.sType = "Line";
        this.iColor = _iColor;
        this.oStart = _oStart;
        this.oEnd = _oEnd;
    }
}
