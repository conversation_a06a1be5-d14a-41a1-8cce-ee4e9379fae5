package com.snapinspect.snapinspect3.IF_Object;

import com.google.gson.Gson;
import com.snapinspect.snapinspect3.Helper.CommonDB;
import com.snapinspect.snapinspect3.Helper.Constants;
import com.snapinspect.snapinspect3.SI_DB.db_Inspection;
import com.snapinspect.snapinspect3.util.StringUtils;
import com.snapinspect.snapinspect3.database.RoomDatabaseManager;
import com.snapinspect.snapinspect3.database.entities.Inspection;
import com.snapinspect.snapinspect3.app.App;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/03/14.
 */
//TableName: AIINSPECTION
public class ai_Inspection {
    public Long id;
    //I_S_INS_TYPE_ID
    public int iSInsTypeID;
    //I_S_ASSET_ID
    public int iSAssetID;
    //S_PTC
    public String sPTC;
    //S_TYPE
    public String sType;
    //S_INS_TITLE
    public String sInsTitle;
    //S_TITLE
    public String sTitle;
    //S_COMMENTS
    public String sComments;
    //DT_START_DATE
    public String dtStartDate;
    //DT_END_DATE
    public String dtEndDate;
    //S_LAT
    public String sLat;
    //S_LONG
    public String sLong;
    //B_COMPLETE
    public boolean bComplete;
    //B_SYNCED
    public boolean bSynced;
    //B_LOCK
    public boolean bLock;
    //B_DELETED
    public boolean bDeleted;
    //S_ADDRESS_ONE
    public String sAddressOne;
    //S_ADDRESS_TWO
    public String sAddressTwo;
    //I_S_INS_ID
    public int iSInsID;
    //I_S_SCHEDULE_ID
    public int iSScheduleID;
    //S_CUSTOM_ONE
    public String sCustomOne;
    //S_CUSTOM_TWO
    public String sCustomTwo;

    public ai_Inspection() {

    }

    public ai_Inspection(JSONObject oInspection) throws JSONException {
        iSAssetID = oInspection.getInt("iPropertyID");
        iSInsID = oInspection.getInt("iInspectionID");
        ai_InsType oInsType = db_Inspection.GetInsTypeBySInsTypeID(oInspection.getInt("iInsTypeID"));
        if (oInsType != null) {
            iSInsTypeID = oInsType.iSInsTypeID;
            sPTC = oInsType.sPTC;
            sType = oInsType.sType;
            sInsTitle = oInsType.sInsTitle;
        }
        sTitle = oInspection.getString("sTitle");
        dtStartDate = oInspection.getString("dtStart");
        dtEndDate = oInspection.getString("dtEnd");
        sAddressOne = oInspection.getString("sAddress1");
        sAddressTwo = oInspection.getString("sAddress2");
        iSScheduleID = 0;
        sCustomOne = oInspection.getString("sCustom1");
        sCustomTwo = oInspection.getString("sCustom2");
    }

    public ai_Inspection(
        int iSInsTypeID, int iSAssetID, String sPTC, String sType,
        String sInsTitle, String sTitle, String sComments, String dtStartDate, String dtEndDate,
        String sLat, String sLong, String sAddress1, String sAddress2, int iSScheduleID,
        String _sCustom1, String _sCustom2
    ) {
        this.iSInsTypeID = iSInsTypeID;
        this.iSAssetID = iSAssetID;
        this.sPTC = sPTC;
        this.sType = sType;
        this.sInsTitle = sInsTitle;
        this.sTitle = sTitle;
        this.sComments = sComments;
        this.dtStartDate = dtStartDate;
        this.dtEndDate = dtEndDate;
        this.sLat = sLat;
        this.sLong = sLong;
        this.sAddressOne = sAddress1;
        this.sAddressTwo = sAddress2;
        this.iSInsID = 0;
        this.iSScheduleID = iSScheduleID;
        this.bComplete = false;
        this.bDeleted = false;
        this.bLock = false;
        this.bSynced = false;
        this.sCustomOne = _sCustom1;
        this.sCustomTwo = _sCustom2;
    }

    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public ai_Enum_Config.SI_Inspection_Type getEnumInsType() {
        return ai_Enum_Config.INSPECTION_TYPE_SIMPLE.equalsIgnoreCase(sType) ?
                ai_Enum_Config.SI_Inspection_Type.Simple : ai_Enum_Config.SI_Inspection_Type.Full;
    }

    public boolean hasNotCompletedCompulsoryItems() {
        if (shouldBypassCompulsory()) return false;

        List<ai_InsItem> arrInsItem = CommonDB.GetChildInsItemSugar(0, getId());
        if (arrInsItem == null || arrInsItem.isEmpty()) return false;
        for (ai_InsItem insItem : arrInsItem) {
            if (insItem.hasCompulsoryItems()) return true;
        }
        return false;
    }

    public int getProjectInsID() {
        try {
            List<ai_ProjectInspection> lsPIns = CommonDB.findProjectInspections(
                    "I_INSPECTION_ID = ?", String.valueOf(getId()));
            if (lsPIns.isEmpty()) return 0;
            return lsPIns.get(0).iSProjectAssetInsTypeID;
        }catch(Exception ex){return 0;}
    }

    public boolean shouldBypassCompulsory() {
        try {
            ai_InsType insType = db_Inspection.GetInsTypeBySInsTypeID(iSInsTypeID);
            if (insType == null) return false;

            Gson gson = new Gson();
            Map<String, Object> jsonObjects = gson.fromJson(insType.sFieldOne, Map.class);
            if (jsonObjects == null) return false;

            Map<String, Object> byPassJson = (Map<String, Object>) jsonObjects.get(Constants.Keys.BYPASS_COMPULSORY);
            if (byPassJson == null || byPassJson.isEmpty()) return false;

            Object iLayoutIDObj = byPassJson.get(Constants.Keys.LAYOUT_ID);
            if (!(iLayoutIDObj instanceof Double)) return false;

            int iLayoutID = ((Double) iLayoutIDObj).intValue();
            if (iLayoutID == 0) return false;

            String sRating = (String) byPassJson.get(Constants.Keys.RATING);
            if (StringUtils.isEmpty(sRating)) return false;

            List<ai_InsItem> arrItems1st = CommonDB.GetChildInsItemSugar(0, getId());
            for (ai_InsItem insItem : arrItems1st) {
                List<ai_InsItem> arrInsItems = CommonDB.GetChildInsItemSugar(insItem.getId(), getId());
                for (ai_InsItem child : arrInsItems) {
                    if (child.iSLayoutID == iLayoutID && child.hasSelectedCHKItem(sRating)) return true;
                }
            }
        } catch (Exception ex) {
            ai_BugHandler.ai_Handler_Exception(ex);
        }
        return false;
    }

    // Helper method to get Room Database Manager
    private static RoomDatabaseManager getRoomManager() {
        return RoomDatabaseManager.getInstance(App.getContext());
    }

    // Convert to Room entity
    public Inspection toRoomEntity() {
        Inspection roomEntity = new Inspection();
        roomEntity.id = this.id;
        roomEntity.iSInsTypeID = this.iSInsTypeID;
        roomEntity.iSAssetID = this.iSAssetID;
        roomEntity.sPTC = this.sPTC;
        roomEntity.sType = this.sType;
        roomEntity.sInsTitle = this.sInsTitle;
        roomEntity.sTitle = this.sTitle;
        roomEntity.sComments = this.sComments;
        roomEntity.dtStartDate = this.dtStartDate;
        roomEntity.dtEndDate = this.dtEndDate;
        roomEntity.sLat = this.sLat;
        roomEntity.sLong = this.sLong;
        roomEntity.bComplete = this.bComplete;
        roomEntity.bSynced = this.bSynced;
        roomEntity.bLock = this.bLock;
        roomEntity.bDeleted = this.bDeleted;
        return roomEntity;
    }

    // Create from Room entity
    public static ai_Inspection fromRoomEntity(Inspection roomEntity) {
        if (roomEntity == null) return null;
        
        ai_Inspection sugar = new ai_Inspection();
        sugar.id = roomEntity.id;
        sugar.iSInsTypeID = roomEntity.iSInsTypeID != null ? roomEntity.iSInsTypeID : 0;
        sugar.iSAssetID = roomEntity.iSAssetID != null ? roomEntity.iSAssetID : 0;
        sugar.sPTC = roomEntity.sPTC;
        sugar.sType = roomEntity.sType;
        sugar.sInsTitle = roomEntity.sInsTitle;
        sugar.sTitle = roomEntity.sTitle;
        sugar.sComments = roomEntity.sComments;
        sugar.dtStartDate = roomEntity.dtStartDate;
        sugar.dtEndDate = roomEntity.dtEndDate;
        sugar.sLat = roomEntity.sLat;
        sugar.sLong = roomEntity.sLong;
        sugar.bComplete = roomEntity.bComplete != null ? roomEntity.bComplete : false;
        sugar.bSynced = roomEntity.bSynced != null ? roomEntity.bSynced : false;
        sugar.bLock = roomEntity.bLock != null ? roomEntity.bLock : false;
        sugar.bDeleted = roomEntity.bDeleted != null ? roomEntity.bDeleted : false;
        return sugar;
    }

    // Sugar ORM compatibility methods - now using Room DAO
    public Long save() {
        try {
            Inspection roomEntity = this.toRoomEntity();
            if (this.id == null) {
                // Insert new inspection
                long newId = getRoomManager().getDatabase().inspectionDao().insertInspection(roomEntity);
                this.id = newId;
            } else {
                // Update existing inspection
                getRoomManager().getDatabase().inspectionDao().updateInspection(roomEntity);
            }
            return this.id;
        } catch (Exception ex) {
            // Fallback to simple ID generation if Room fails
            if (this.id == null) {
                this.id = System.currentTimeMillis();
            }
            return this.id;
        }
    }

    public static List<ai_Inspection> find(Class<ai_Inspection> clazz, String whereClause, String... whereArgs) {
        try {
            // Basic implementation - more sophisticated parsing would be needed for full compatibility
            List<Inspection> roomInspections = getRoomManager().getDatabase().inspectionDao().getAllInspections();
            List<ai_Inspection> sugarInspections = new ArrayList<>();
            for (Inspection roomInspection : roomInspections) {
                sugarInspections.add(fromRoomEntity(roomInspection));
            }
            return sugarInspections;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public static ai_Inspection findById(Class<ai_Inspection> clazz, Object id) {
        try {
            if (id instanceof Long) {
                Inspection roomInspection = getRoomManager().getDatabase().inspectionDao().getInspectionById((Long) id);
                return fromRoomEntity(roomInspection);
            } else if (id instanceof Integer) {
                Inspection roomInspection = getRoomManager().getDatabase().inspectionDao().getInspectionById(((Integer) id).longValue());
                return fromRoomEntity(roomInspection);
            }
            return null;
        } catch (Exception ex) {
            return null;
        }
    }

    public static List<ai_Inspection> listAll(Class<ai_Inspection> clazz) {
        try {
            List<Inspection> roomInspections = getRoomManager().getDatabase().inspectionDao().getAllInspections();
            List<ai_Inspection> sugarInspections = new ArrayList<>();
            for (Inspection roomInspection : roomInspections) {
                sugarInspections.add(fromRoomEntity(roomInspection));
            }
            return sugarInspections;
        } catch (Exception ex) {
            return new ArrayList<>();
        }
    }

    public boolean delete() {
        try {
            if (this.id != null) {
                // Use soft delete
                this.bDeleted = true;
                return this.save() != null;
            }
            return false;
        } catch (Exception ex) {
            return false;
        }
    }
}
