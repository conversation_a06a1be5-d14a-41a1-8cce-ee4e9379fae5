package com.snapinspect.snapinspect3.IF_Object;

import com.snapinspect.snapinspect3.Helper.CommonDB_Assets;
import com.snapinspect.snapinspect3.Helper.CommonJson;
import com.snapinspect.snapinspect3.Helper.CommonTaskKt;
import com.snapinspect.snapinspect3.SI_DB.db_Asset;
import com.snapinspect.snapinspect3.util.DateUtils;
import com.snapinspect.snapinspect3.util.StringUtils;
import org.json.JSONObject;

import java.util.Date;

public class ai_Task {
    public Long id;
    public int iSNotificationID;
    public int iSubmitCustomerID;
    public int iFollowUpCustomerID;
    public int iPropertyID;
    public int iCompanyID;
    public int iInsItemID;
    public int iInspectionID;
    public String sCategory;
    public int iPriority;
    public String sCode;
    public String sTitle;
    public String sDescription;
    public String sPhotoURL;
    public String sVideoURL;
    public Date dtDateDue;
    public String sCustom1;
    public String sCustom2;
    public boolean bClosed;
    public boolean bDeleted;
    public Date dtDateTime;
    public Date dtComplete;
    public Date dtUpdate;
    public int iPTaskID;
    public String arrMember;
    public String sStatus;
    public String sStatusCode;
    public int iCategoryID;
    public Date dtUpdate_Msg;
    public double dCost;

    public TaskPriority getPriority() {
        TaskPriority priority = TaskPriority.getTaskPriority(iPriority);
        return priority != null ? priority : TaskPriority.LOW;
    }

    public ai_Task() {
    }

    public ai_Task(JSONObject jsonObject) {
        if (!jsonObject.isNull("iNotificationID")) {
            iSNotificationID = jsonObject.optInt("iNotificationID", 0);
        } else if (!jsonObject.isNull("iTaskID")) {
            // task details endpoint uses iTaskID
            iSNotificationID = jsonObject.optInt("iTaskID", 0); 
        }

        iSubmitCustomerID = jsonObject.optInt("iSubmitCustomerID", 0);
        iFollowUpCustomerID = jsonObject.optInt("iFollowUpCustomerID", 0);

        if (!jsonObject.isNull("iPropertyID")) {
            iPropertyID = jsonObject.optInt("iPropertyID", 0);
        } else if (!jsonObject.isNull("iAssetID")) {
            // task details endpoint uses iAssetID
            iPropertyID = jsonObject.optInt("iAssetID", 0);
        }

        iCompanyID = jsonObject.optInt("iCompanyID", 0);
        iInsItemID = jsonObject.optInt("iInsItemID", 0);
        iInspectionID = jsonObject.optInt("iInspectionID", 0);
        sCategory = jsonObject.optString("sCategory", "");
        iPriority = jsonObject.optInt("iPriority", 1);
        sCode = jsonObject.optString("sCode", "");
        sTitle = jsonObject.optString("sTitle", "");
        sDescription = jsonObject.optString("sDescription", "");
        sPhotoURL = jsonObject.optString("sPhotoURL", "");
        sVideoURL = jsonObject.optString("sVideoURL", "");

        if (!jsonObject.isNull("dtDateDue")) {
            dtDateDue = parseDate(jsonObject, "dtDateDue");
        } else if (!jsonObject.isNull("dtTaskDue")) {
            dtDateDue = parseDate(jsonObject, "dtTaskDue");
        }

        // Handle sCustom1/sCustom2 that could be dictionary or string
        sCustom1 = CommonJson.GetJsonKeyValue_String("sCustom1", jsonObject);
        sCustom2 = CommonJson.GetJsonKeyValue_String("sCustom2", jsonObject);

        bClosed = jsonObject.optBoolean("bClosed", false);
        bDeleted = jsonObject.optBoolean("bDeleted", false);

        if (!jsonObject.isNull("dtDateTime")) {
            dtDateTime = parseDate(jsonObject, "dtDateTime");
        } else if (!jsonObject.isNull("dtCreate")) {
            dtDateTime = parseDate(jsonObject, "dtCreate");
        }

        dtComplete = parseDate(jsonObject, "dtComplete");
        dtUpdate = parseDate(jsonObject, "dtUpdate");

        if (!jsonObject.isNull("iPTaskID")) {
            iPTaskID = jsonObject.optInt("iPTaskID", 0);
        } else if (!jsonObject.isNull("iParentTaskID")) {
            // task details endpoint uses iParentTaskID
            iPTaskID = jsonObject.optInt("iParentTaskID", 0);
        }

        arrMember = jsonObject.optString("arrMember", "");

        if (!jsonObject.isNull("sStatus")) {
            sStatus = jsonObject.optString("sStatus", "");
        } else if (!jsonObject.isNull("sTaskStatus")) {
            // asset tasks endpoint uses sTaskStatus
            sStatus = jsonObject.optString("sTaskStatus", "");
        } else {
            sStatus = "";
        }

        sStatusCode = jsonObject.optString(
            "sTaskStatusCode", CommonJson.GetJsonKeyValue("S_Code", sCustom1));

        iCategoryID = jsonObject.optInt("iCategoryID", 0);
        dtUpdate_Msg = parseDate(jsonObject, "dtUpdate_Msg");
        dCost = jsonObject.optDouble("dCost", 0.0);
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public boolean matchSearch(String search) {
        if (search == null || StringUtils.isEmpty(search)) {
            return true;
        }
        
        String[] searchTerms = search.split(" ");
        for (String term : searchTerms) {
            if (StringUtils.isEmpty(term)) continue;
            
            boolean termFound = containsIgnoreCase(sTitle, term) ||
                    containsIgnoreCase(sDescription, term) ||
                    containsIgnoreCase(sCategory, term) ||
                    containsIgnoreCase(sStatus, term);
            
            if (!termFound) {
                return false;
            }
        }
        return true;
    }

    private boolean containsIgnoreCase(String str, String searchTerm) {
        return str != null && str.toLowerCase().contains(searchTerm.toLowerCase());
    }

    private Date parseDate(JSONObject jsonObject, String key) {
        return CommonTaskKt.optDate(jsonObject, key, DateUtils.possibleDateFormats);
    }

    public String dump() {
        return "ai_Task{" +
                "iSNotificationID=" + iSNotificationID +
                ", iSubmitCustomerID=" + iSubmitCustomerID +
                ", iFollowUpCustomerID=" + iFollowUpCustomerID +
                ", iPropertyID=" + iPropertyID +
                ", iCompanyID=" + iCompanyID +
                ", iInsItemID=" + iInsItemID +
                ", iInspectionID=" + iInspectionID +
                ", sCategory='" + sCategory + '\'' +
                ", iPriority=" + iPriority +
                ", sCode='" + sCode + '\'' +
                ", sTitle='" + sTitle + '\'' +
                ", sDescription='" + sDescription + '\'' +
                ", sPhotoURL='" + sPhotoURL + '\'' +
                ", sVideoURL='" + sVideoURL + '\'' +
                ", dtDateDue=" + dtDateDue +
                ", sCustom1='" + sCustom1 + '\'' +
                ", sCustom2='" + sCustom2 + '\'' +
                ", bClosed=" + bClosed +
                ", bDeleted=" + bDeleted +
                ", dtDateTime=" + dtDateTime +
                ", dtComplete=" + dtComplete +
                ", dtUpdate=" + dtUpdate +
                ", iPTaskID=" + iPTaskID +
                ", arrMember='" + arrMember + '\'' +
                ", sStatus='" + sStatus + '\'' +
                ", sStatusCode='" + sStatusCode + '\'' +
                ", iCategoryID=" + iCategoryID +
                ", dtUpdate_Msg=" + dtUpdate_Msg +
                ", dCost=" + dCost +
                '}';
    }

    /**
     * Returns the v_asset associated with this task based on the property ID.
     */
    public v_Asset getAssociatedVAsset() {
        return db_Asset.GetVAssetBySAssetID(iPropertyID);
    }
}