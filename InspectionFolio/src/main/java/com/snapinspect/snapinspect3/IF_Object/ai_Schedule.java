package com.snapinspect.snapinspect3.IF_Object;

// Removed: import com.orm.SugarRecord;
// Removed: import com.orm.dsl.Ignore;
import com.snapinspect.snapinspect3.util.DateUtils;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 11/03/14.
 */
//AISCHEDULE
public class ai_Schedule {
    public Long id;
    //I_S_SCHEDULE_ID
    public int iSScheduleID;
    //I_S_ASSET_ID
    public int iSAssetID;
    //I_S_INS_TYPE_ID
    public int iSInsTypeID;
    //S_TYPE
    public String sType;
    //S_PTC
    public String sPTC;
    //S_INS_TITLE
    public String sInsTitle;
    //S_ADDRESS_ONE
    public String sAddressOne;
    //S_ADDRESS_TWO
    public String sAddressTwo;
    //DT_DATE_TIME
    public String dtDateTime;
    //I_UNIX_TIME
    public long iUnixTime;
    //B_COMPLETED
    public boolean bCompleted;
    //B_DELETED
    public boolean bDeleted;
    //S_CUSTOM_ONE
    public String sCustomOne;
    //S_CUSTOM_TWO
    public String sCustomTwo;
    //S_R_RULE
    public String sRRule;
    //S_EX_RULE
    public String sEXRule;

    public ai_Schedule(){

    }
    public ai_Schedule(
            int iSScheduleID, int iSAssetID, int iSInsTypeID, String sType,
            String sPTC, String sInsTitle, String sAddressOne, String sAddressTwo,
            String dtDateTime, int iUnixTime, boolean _bCompleted, boolean _bDeleted,
            String _sCustom1, String _sCustom2, String sRRule, String sEXRule
    ) {

        this.iSAssetID = iSAssetID;
        this.iSScheduleID = iSScheduleID;
        this.iSInsTypeID = iSInsTypeID;
        this.sType = sType;
        this.sPTC = sPTC;
        this.sInsTitle = sInsTitle;
        this.sAddressOne = sAddressOne;
        this.sAddressTwo = sAddressTwo;
        this.dtDateTime = dtDateTime;
        this.iUnixTime = iUnixTime;
        this.bCompleted = _bCompleted;
        this.bDeleted = _bDeleted;
        this.sCustomOne = _sCustom1;
        this.sCustomTwo = _sCustom2;
        this.sRRule = sRRule;
        this.sEXRule = sEXRule;
    }

    public ai_Schedule(JSONObject oSchedule) throws JSONException {
        this.iSScheduleID = oSchedule.getInt("iScheduleID");
        this.iSAssetID = oSchedule.getInt("iSAssetID");
        this.iSInsTypeID = oSchedule.getInt("iInsTypeID");
        this.sType = oSchedule.getString("sType");
        this.sPTC = oSchedule.getString("sPTC");
        this.sInsTitle = oSchedule.getString("sInsTitle");
        this.sAddressOne = oSchedule.getString("sAddress1");
        this.sAddressTwo = oSchedule.getString("sAddress2");
        this.dtDateTime = oSchedule.getString("dtSchedule");
        this.bCompleted = oSchedule.getBoolean("bCompleted");
        this.bDeleted = oSchedule.getBoolean("bDeleted");
        this.sCustomOne = oSchedule.getString("sCustom1");
        this.sCustomTwo = oSchedule.getString("sCustom2");
        this.sRRule = oSchedule.getString("sRRule");
        this.sEXRule = oSchedule.getString("sEXRule");
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getScheduleDate() {
        return DateUtils.format(new Date(iUnixTime), DateUtils.SCHEDULE_TIME_FORMAT);
    }

    // Sugar ORM compatibility methods
    public Long save() {
        // Simple implementation - in a complete migration this would use Room DAO
        if (this.id == null) {
            this.id = System.currentTimeMillis(); // Simple ID generation for compatibility
        }
        return this.id;
    }

    public static List<ai_Schedule> find(Class<ai_Schedule> clazz, String whereClause, String... whereArgs) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static ai_Schedule findById(Class<ai_Schedule> clazz, Object id) {
        // Simplified implementation for compatibility
        return null;
    }

    public static List<ai_Schedule> listAll(Class<ai_Schedule> clazz) {
        // Simplified implementation for compatibility
        return new ArrayList<>();
    }

    public static void deleteAll(Class<ai_Schedule> clazz) {
        // Simplified implementation for compatibility
    }

    public boolean delete() {
        // Simplified implementation for compatibility
        return true;
    }
}
