<!DOCTYPE html>
<html lang="en">
<head>
  <title>SnapInspect Editor</title>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"/>
  <script type="text/javascript">
    %@
  </script>
  <script type="text/javascript">
    %@
  </script>
  <script type="text/javascript">
    %@
  </script>
  <script type="text/javascript">
    %@
  </script>
  <style>
    .top-cover-example {
      height: 90vh;
    }
  </style>
</head>
<body>
<div id="si-app" class="top-cover-example">
</div>
<script type="text/javascript">
  %@
</script>
<script type="text/javascript">
  // code was copied from https://github.com/sd2020/SI_ImageAnnotation/blob/master/examples/public/index.js
  const App = () => {
    return React.createElement(
            "div",
            { },
            SIWhiteBoard.ImageEditor({
              localImage: null,
              bgImage: '%@',
              backgroundColor: 'white',
              getEditorDataCallback: getEditorDataCallback,
              initializedCallback: initializedCallback,
              layer: `###LAYER###`,
              canvasData: `###CANVAS_DATA###`,
              resize: false,
              saveButton: false,
              thumbnail2: true,
              thumbnailQuality: 0.55
            }),
    );
  };

  function saveImageEditorData() {
    document.getElementById("save_canvans_button").click()
  }

  function getEditorDataCallback(data) {
    jsInterface.saveImageEditorData(JSON.stringify(data));
  }

  function initializedCallback() {
    jsInterface.initializedCallback();
  }

  const editor  = document.getElementById("si-app");
  const root = ReactDOM.createRoot(editor);
  root.render(React.createElement(App));
</script>
</body>
</html>
