<?xml version="1.0" encoding="UTF-8"?>
<project name="module_inspectionfolio" default="compile.module.inspectionfolio">
  <dirname property="module.inspectionfolio.basedir" file="${ant.file.module_inspectionfolio}"/>
  
  <property name="module.jdk.home.inspectionfolio" value=""/>
  <property name="module.jdk.bin.inspectionfolio" value=""/>
  <property name="module.jdk.classpath.inspectionfolio" value=""/>
  
  <property name="compiler.args.inspectionfolio" value="-encoding UTF-8 -source 1.7 -target 1.7 ${compiler.args}"/>
  
  <property name="inspectionfolio.output.dir" value="${module.inspectionfolio.basedir}/../build/classes/production/InspectionFolio"/>
  <property name="inspectionfolio.testoutput.dir" value="${module.inspectionfolio.basedir}/../build/classes/test/InspectionFolio"/>
  
  <path id="inspectionfolio.module.bootclasspath">
    <!-- Paths to be included in compilation bootclasspath -->
  </path>
  
  <path id="inspectionfolio.module.production.classpath"/>
  
  <path id="inspectionfolio.runtime.production.module.classpath">
    <pathelement location="${inspectionfolio.output.dir}"/>
  </path>
  
  <path id="inspectionfolio.module.classpath">
    <pathelement location="${inspectionfolio.output.dir}"/>
  </path>
  
  <path id="inspectionfolio.runtime.module.classpath">
    <pathelement location="${inspectionfolio.testoutput.dir}"/>
    <pathelement location="${inspectionfolio.output.dir}"/>
  </path>
  
  
  <patternset id="excluded.from.module.inspectionfolio">
    <patternset refid="ignored.files"/>
  </patternset>
  
  <patternset id="excluded.from.compilation.inspectionfolio">
    <patternset refid="excluded.from.module.inspectionfolio"/>
  </patternset>
  
  
  <target name="compile.module.inspectionfolio" depends="compile.module.inspectionfolio.production,compile.module.inspectionfolio.tests" description="Compile module InspectionFolio"/>
  
  <target name="compile.module.inspectionfolio.production" depends="register.custom.compilers" description="Compile module InspectionFolio; production classes"/>
  
  <target name="compile.module.inspectionfolio.tests" depends="register.custom.compilers,compile.module.inspectionfolio.production" description="compile module InspectionFolio; test classes" unless="skip.tests"/>
  
  <target name="clean.module.inspectionfolio" description="cleanup module">
    <delete dir="${inspectionfolio.output.dir}"/>
    <delete dir="${inspectionfolio.testoutput.dir}"/>
  </target>
</project>