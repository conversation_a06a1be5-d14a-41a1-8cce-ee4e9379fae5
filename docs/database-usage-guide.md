# Database Usage Guide - SnapInspect Android

This document describes how to use the database in the SnapInspect Android project, which uses Sugar ORM with SQLite.

## Overview

The project uses Sugar ORM as an Object-Relational Mapping (ORM) library to interact with a SQLite database. The database file is named `if_data.db` and is currently at version 11.

## Configuration

### Sugar ORM Setup

**Application Class (App.java):**
```java
@Override
public void onCreate() {
    super.onCreate();
    SugarContext.init(this); // Initialize Sugar ORM
}

@Override
public void onTerminate() {
    super.onTerminate();
    SugarContext.terminate(); // Clean up Sugar ORM
}
```

**AndroidManifest.xml:**
```xml
<meta-data android:name="DATABASE" android:value="if_data.db" />
<meta-data android:name="VERSION" android:value="11" />
<meta-data android:name="QUERY_LOG" android:value="false" />
<meta-data android:name="DOMAIN_PACKAGE_NAME" android:value="com.snapinspect" />
```

## Entity Classes

All database entities extend `SugarRecord` and follow the naming pattern `ai_[EntityName]`. Key entities include:

### Core Entities

**ai_Assets** - Property/asset information
```java
public class ai_Assets extends SugarRecord {
    public int iSAssetID;           // Server asset ID
    public String sAddressOne;      // Primary address
    public String sAddressTwo;      // Secondary address
    public int iCustomerID;         // Customer ID
    public String dtInsDue;         // Inspection due date
    public boolean bDeleted;        // Soft delete flag
}
```

**ai_Inspection** - Inspection records
```java
public class ai_Inspection extends SugarRecord {
    public int iSAssetID;           // Related asset ID
    public String sTitle;           // Inspection title
    public String sComments;        // Comments
    public boolean bComplete;       // Completion status
    public boolean bSynced;         // Sync status
    public boolean bDeleted;        // Soft delete flag
}
```

**ai_InsItem** - Individual inspection items
```java
public class ai_InsItem extends SugarRecord {
    public int iInsID;              // Inspection ID
    public String sName;            // Item name
    public String sValueOne;        // Value field 1
    public String sQType;           // Question type
    public boolean bCompleted;      // Completion status
    
    @Ignore
    public boolean bInspectionByPassCompulsory; // Runtime field
}
```

**ai_Photo** - Photo records
```java
public class ai_Photo extends SugarRecord {
    public int iInsItemID;          // Inspection item ID
    public String sFile;            // File path
    public String sThumb;           // Thumbnail path
    public String sComments;        // Photo comments
    public boolean bUploaded;       // Upload status
    public boolean bDeleted;        // Soft delete flag
}
```

## Basic Database Operations

### Create/Save
```java
ai_Assets asset = new ai_Assets();
asset.sAddressOne = "123 Main St";
asset.iCustomerID = 1;
asset.save(); // Returns saved record ID
```

### Read
```java
// Find by ID
ai_Assets asset = ai_Assets.findById(ai_Assets.class, assetId);

// Find with conditions
List<ai_Assets> assets = ai_Assets.find(ai_Assets.class, 
    "I_S_ASSET_ID = ? AND B_DELETED = 0", String.valueOf(assetId));

// Get all records
List<ai_Assets> allAssets = ai_Assets.listAll(ai_Assets.class);
```

### Update
```java
ai_Assets asset = ai_Assets.findById(ai_Assets.class, assetId);
asset.sAddressOne = "456 Oak St";
asset.save(); // Updates existing record
```

### Delete (Soft Delete Pattern)
```java
// Always use soft delete, never hard delete
asset.bDeleted = true;
asset.save();
```

## Advanced Queries

### Using Query Builder
```java
// Select with multiple conditions
List<ai_Inspection> inspections = Select.from(ai_Inspection.class)
    .where(Condition.prop("B_SYNCED").eq(false))
    .where(Condition.prop("B_COMPLETE").eq(true))
    .where(Condition.prop("B_DELETED").eq(false))
    .orderBy("id desc")
    .list();

// Get single result
ai_InsType insType = Select.from(ai_InsType.class)
    .where(Condition.prop("I_S_INS_TYPE_ID").eq(insTypeId))
    .first();
```

### Raw SQL Queries
```java
// Complex queries with conditions
List<ai_Assets> assets = SugarRecord.findWithQuery(ai_Assets.class,
    "SELECT * FROM AIASSETS WHERE " +
    "(S_ADDRESS_ONE LIKE ? OR S_ADDRESS_TWO LIKE ?) " +
    "AND I_SP_ASSET_ID = ? AND B_DELETED = 0 " +
    "ORDER BY S_ADDRESS_ONE COLLATE NOCASE",
    "%" + filter + "%", "%" + filter + "%", String.valueOf(parentAssetId));

// Execute update/delete queries
ai_Inspection.executeQuery(
    "UPDATE AIINSPECTION SET B_DELETED = 1 WHERE I_S_ASSET_ID = ?",
    String.valueOf(assetId));
```

### Complex Joins
```java
String query = "SELECT pIns.*, asset.S_ADDRESS_ONE, " +
    "ins.S_TITLE, insType.S_INS_TITLE AS S_INS_TYPE_TITLE " +
    "FROM AIPROJECT_INSPECTION pIns " +
    "LEFT JOIN AIASSETS asset ON pIns.I_ASSET_ID = asset.I_S_ASSET_ID " +
    "LEFT JOIN AIINSPECTION ins ON pIns.I_INSPECTION_ID = ins.ID " +
    "WHERE pIns.B_DELETED = 0 AND pIns.I_PROJECT_ID = ?";

List<v_ProjectInspection> result = SugarRecord.findWithQuery(
    v_ProjectInspection.class, query, String.valueOf(projectId));
```

## Database Helper Classes

### CommonDB.java - Central Operations
```java
// Reset entire database
CommonDB.ResetDB();

// Insert photo with location
long photoId = CommonDB.InsertPhoto(context, insItemId, thumbPath, filePath, location);

// Batch operations
List<ai_Photo> photos = ai_Photo.listAll(ai_Photo.class);
// Modify photos...
SugarRecord.saveInTx(photos); // Save all in transaction
```

### Specialized Database Classes
```java
// db_Inspection.java
List<ai_Inspection> inspections = db_Inspection.GetInspectionBySAssetID(
    assetId, completed, synced, excludeChild);

// db_Media.java  
List<ai_Photo> photos = db_Media.GetPhotosForInsItem(insItemId);
long photoCount = db_Media.GetPhotosCountForIDs(photoIds);
```

## Common Patterns

### Soft Delete Pattern
Always use soft delete instead of hard delete:
```java
// Mark as deleted
record.bDeleted = true;
record.save();

// Always filter deleted records in queries
"B_DELETED = 0" // Include in WHERE clause
```

### Sync Status Tracking
Track sync status for offline-first architecture:
```java
inspection.bSynced = false;   // Needs sync
inspection.bUploaded = false; // Needs upload
inspection.save();
```

### JSON Field Storage
Store complex data as JSON in string fields:
```java
// Store JSON data
String config = CommonJson.AddJsonKeyValue("{}", "key", "value");
item.sConfig = config;

// Retrieve JSON values
String value = CommonJson.GetJsonKeyValue("key", item.sConfig);
```

### Error Handling
```java
try {
    List<ai_Assets> assets = ai_Assets.find(ai_Assets.class, "condition");
    // Process results
} catch (Exception ex) {
    ai_BugHandler.ai_Handler_Exception(ex);
}
```

## Database Schema

### Main Table Categories

**Assets Management:**
- `ai_Assets` - Property/asset information
- `ai_AssetLayout` - Asset layout configurations
- `ai_AssetView` - Asset view settings

**Inspection System:**
- `ai_Inspection` - Inspection records
- `ai_InsItem` - Individual inspection items
- `ai_InsType` - Inspection type definitions
- `ai_Layout` - Layout configurations

**Media Management:**
- `ai_Photo` - Photo records
- `ai_Video` - Video records
- `ai_File` - File attachments

**Project Management:**
- `ai_Project` - Project information
- `ai_ProjectInspection` - Project-inspection relationships
- `ai_Task` - Task management
- `ai_Schedule` - Scheduling

**User Management:**
- `ai_User` - User accounts
- `ai_Contact` - Contact information

## Migration and Versioning

### Database Migrations
- Migration scripts located in `sugar_upgrades/` directory
- Automatically applied by Sugar ORM when database version increases
- Current version: 11 (defined in AndroidManifest.xml)

### Example Migration Scripts
```sql
-- 5.sql: Add new column
ALTER TABLE AIPHOTO ADD S_FIELD_ONE TEXT;

-- 9.sql: Drop view
DROP VIEW IF EXISTS VSCHEDULE;
```

## Best Practices

1. **Always use soft delete** - Set `bDeleted = true` instead of removing records
2. **Filter deleted records** - Include `B_DELETED = 0` in WHERE clauses
3. **Use transactions for batch operations** - `SugarRecord.saveInTx()` for multiple saves
4. **Handle exceptions** - Wrap database operations in try-catch blocks
5. **Use specialized helper classes** - Leverage `CommonDB`, `db_Inspection`, etc.
6. **Track sync status** - Use `bSynced` and `bUploaded` flags for offline functionality
7. **Store complex data as JSON** - Use string fields with JSON for flexible data storage

## Performance Tips

1. **Use indexes** - Ensure frequently queried fields have indexes
2. **Limit result sets** - Use LIMIT clause for large datasets
3. **Use specific queries** - Select only needed columns instead of `SELECT *`
4. **Batch operations** - Use transactions for multiple database operations
5. **Close cursors** - Sugar ORM handles this automatically, but be aware in custom queries