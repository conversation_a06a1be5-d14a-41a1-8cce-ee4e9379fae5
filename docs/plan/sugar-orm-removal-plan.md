# Sugar ORM Removal Plan

## Overview
Fix all remaining Sugar ORM `.find()`, `.listAll()`, `.deleteAll()`, `.executeQuery()` and `.getCursor()` method calls that are causing build errors by replacing them with appropriate CommonDB or Room database calls.

## Current Problem Analysis
The build is failing due to multiple Sugar ORM method calls across many files:

### `.find()` calls to fix:
- `ai_Photo.find(ai_Photo.class, query, ...)`
- `ai_InsItem.find(ai_InsItem.class, query, ...)`
- `ai_Inspection.find(ai_Inspection.class, query, ...)`
- `ai_Assets.find(ai_Assets.class, query, ...)`
- `ai_CheckList.find(ai_CheckList.class, query, ...)`
- `ai_Layout.find(ai_Layout.class, query, ...)`
- `ai_Project.find(ai_Project.class, query, ...)`
- `ai_Video.find(ai_Video.class, query, ...)`

### `.listAll()` calls to fix:
- `ai_InsType.listAll(ai_InsType.class)`
- `ai_QuickPhrase.listAll(ai_QuickPhrase.class)`
- `ai_CheckList.listAll(ai_CheckList.class)`

### `.deleteAll()` calls to fix:
- `ai_Contact.deleteAll(ai_Contact.class)`
- `ai_AssetLayout.deleteAll(ai_AssetLayout.class)`
- `ai_CheckList.deleteAll(ai_CheckList.class)`
- `ai_InsType.deleteAll(ai_InsType.class)`
- `ai_Layout.deleteAll(ai_Layout.class)`

### Other issues:
- `.executeQuery()` and `.getCursor()` calls
- Missing imports for `Select` and `Condition`
- Missing `.getId()` method calls

## Strategy and Approach
1. **Add missing methods to CommonDB**: Create helper methods for all the missing operations
2. **Systematic file-by-file replacement**: Update each affected file to use CommonDB methods
3. **Import fixes**: Add necessary imports and remove Sugar ORM imports
4. **Test and validate**: Ensure build succeeds after all changes

## Implementation Steps

### Phase 1: Add missing CommonDB methods ⏳
- Add `getAllQuickPhrases()` method
- Add `getAllInsTypes()` method  
- Add `getAllCheckLists()` method
- Add any missing find methods for specific queries
- Add proper executeQuery alternatives

### Phase 2: Fix individual files ❌
**High Priority Files:**
- if_quickphrase.java
- frag_RequestIns.java
- if_NewAsset.java
- if_CommentsLibrary.java
- frag_schedules.java
- CommonDB_Inspection.java

**Medium Priority Files:**
- if_UpdateAsset.java
- if_smartcomment.java
- db_ProductCost.java
- db_Media.java
- db_Product.java
- if_HomeTab.java

**Additional Files:**
- frag_Projects.java
- if_InspectionComments.java
- if_InspectionInfo.java
- if_UpdateAsset_2nd.java
- if_EditCustomInfo.java
- if_EditContact_2nd.java
- if_Ins_Edit_3rd.java
- DynamicListView.java
- ItemAdapter.java
- InitFloorPlanTask.java

### Phase 3: Import cleanup and validation ❌
- Remove Sugar ORM imports
- Add proper CommonDB imports
- Fix any remaining compilation issues
- Test build completion

## Timeline
- Phase 1: 30 minutes
- Phase 2: 60-90 minutes 
- Phase 3: 15 minutes
- **Total Estimated Time**: 2-2.5 hours

## Risk Assessment
**Low Risk**: Most methods already exist in CommonDB, just need to add missing ones and replace calls
**Medium Risk**: Some complex queries might need custom implementation
**Mitigation**: Test build frequently during development

## Success Criteria
✅ All Sugar ORM method calls replaced
✅ Build completes successfully without Sugar ORM errors
✅ No functionality regressions
✅ Clean imports in all affected files

## Progress Tracking
- ⏳ **Phase 1**: Adding missing CommonDB methods (In Progress)
- ❌ **Phase 2**: File-by-file replacement (Pending)
- ❌ **Phase 3**: Import cleanup and validation (Pending)

## Related Files
**Core Files Modified:**
- /InspectionFolio/src/main/java/com/snapinspect/snapinspect3/Helper/CommonDB.java

**Files to Update (25+ files):**
- CommonDB_Inspection.java, frag_RequestIns.java, if_NewAsset.java
- if_CommentsLibrary.java, if_quickphrase.java, frag_schedules.java
- if_UpdateAsset.java, if_smartcomment.java, db_ProductCost.java
- db_Media.java, db_Product.java, if_HomeTab.java, frag_Projects.java
- if_InspectionComments.java, if_InspectionInfo.java, if_UpdateAsset_2nd.java
- if_EditCustomInfo.java, if_EditContact_2nd.java, if_Ins_Edit_3rd.java
- DynamicListView.java, ItemAdapter.java, InitFloorPlanTask.java

## Notes
- CommonDB already has most required methods, but some `listAll` and specific query methods are missing
- Most changes are straightforward method call replacements
- Need to maintain backward compatibility with existing functionality