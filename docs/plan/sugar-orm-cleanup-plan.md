# Sugar ORM Cleanup Plan

## Overview
Systematically remove Sugar ORM imports from non-entity files after Room database migration completion, while maintaining backward compatibility with ai_* entities.

## Current Problem Analysis
- Room database migration is complete
- Sugar ORM imports still present in many non-entity files
- Need to clean up imports while preserving functionality
- ai_* entity files must keep SugarRecord for backward compatibility

## Strategy and Approach
1. Keep Sugar ORM imports in ai_* entity files (legacy entities)
2. Remove Sugar ORM imports from non-entity files that now use Room
3. Replace SugarRecord.findById() calls with Room equivalents using CommonDB compatibility methods
4. Focus on critical files first: activities, adapters, helpers

## Implementation Steps

### Phase 1: Activity Files ⏳
- [ ] /activity/if_AssetDetails.java
- [ ] /activity/if_CameraX.java  
- [ ] /activity/if_sign.java
- [ ] /activitynew/* files

### Phase 2: Adapter Files ⏳
- [ ] /Adapter/NoticeFormsAdapter.java
- [ ] Other adapter files with Sugar imports

### Phase 3: Helper and Database Files ⏳
- [ ] /SI_DB/db_* files (database helper files)
- [ ] /Helper/* files
- [ ] /async/SyncProjectService.java

### Phase 4: Verification ⏳
- [ ] Compile after each batch
- [ ] Test critical functionality
- [ ] Verify no broken imports

## Timeline
- Phase 1: Activity files cleanup
- Phase 2: Adapter files cleanup  
- Phase 3: Helper/DB files cleanup
- Phase 4: Final verification

## Risk Assessment
- **Risk**: Breaking existing functionality by removing needed imports
- **Mitigation**: Test compilation after each file, use Room compatibility methods
- **Risk**: Missing Sugar ORM calls in non-obvious places
- **Mitigation**: Careful analysis of each file before changes

## Success Criteria
- All non-entity files use Room database calls
- Sugar ORM imports removed from non-entity files
- ai_* entities retain Sugar ORM for compatibility
- Project compiles successfully
- Core functionality preserved

## Progress Tracking
- ⏳ Plan creation
- ⏳ Initial analysis
- ❌ Phase 1 execution
- ❌ Phase 2 execution
- ❌ Phase 3 execution
- ❌ Final verification

## Related Files
Files to be modified (non-entity files only):
- Activity files: if_AssetDetails.java, if_CameraX.java, if_sign.java
- Adapter files: NoticeFormsAdapter.java and others
- Helper files: All files in /Helper/ directory
- Database files: All db_* files in /SI_DB/
- Service files: SyncProjectService.java