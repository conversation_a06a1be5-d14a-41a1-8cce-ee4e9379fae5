<?xml version="1.0" encoding="UTF-8"?>
<project name="module_snap3android-new2" default="compile.module.snap3android-new2">
  <dirname property="module.snap3android-new2.basedir" file="${ant.file.module_snap3android-new2}"/>
  
  <property name="module.jdk.home.snap3android-new2" value=""/>
  <property name="module.jdk.bin.snap3android-new2" value=""/>
  <property name="module.jdk.classpath.snap3android-new2" value=""/>
  
  <property name="compiler.args.snap3android-new2" value="-encoding UTF-8 -source 1.7 -target 1.7 ${compiler.args}"/>
  
  <property name="snap3android-new2.output.dir" value="undefined"/>
  <property name="snap3android-new2.testoutput.dir" value="undefined"/>
  
  <path id="snap3android-new2.module.bootclasspath">
    <!-- Paths to be included in compilation bootclasspath -->
  </path>
  
  <path id="snap3android-new2.module.production.classpath"/>
  
  <path id="snap3android-new2.runtime.production.module.classpath"/>
  
  <path id="snap3android-new2.module.classpath"/>
  
  <path id="snap3android-new2.runtime.module.classpath"/>
  
  
  <patternset id="excluded.from.module.snap3android-new2">
    <patternset refid="ignored.files"/>
  </patternset>
  
  <patternset id="excluded.from.compilation.snap3android-new2">
    <patternset refid="excluded.from.module.snap3android-new2"/>
  </patternset>
  
  
  <target name="compile.module.snap3android-new2" depends="compile.module.snap3android-new2.production,compile.module.snap3android-new2.tests" description="Compile module Snap3Android-New2"/>
  
  <target name="compile.module.snap3android-new2.production" depends="register.custom.compilers" description="Compile module Snap3Android-New2; production classes"/>
  
  <target name="compile.module.snap3android-new2.tests" depends="register.custom.compilers,compile.module.snap3android-new2.production" description="compile module Snap3Android-New2; test classes" unless="skip.tests"/>
  
  <target name="clean.module.snap3android-new2" description="cleanup module">
    <delete dir="${snap3android-new2.output.dir}"/>
    <delete dir="${snap3android-new2.testoutput.dir}"/>
  </target>
</project>