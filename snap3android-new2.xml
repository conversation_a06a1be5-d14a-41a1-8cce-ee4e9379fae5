<?xml version="1.0" encoding="UTF-8"?>
<project name="snap3android-new2" default="all">
  
  
  <property file="snap3android-new2.properties"/>
  <!-- Uncomment the following property if no tests compilation is needed -->
  <!-- 
  <property name="skip.tests" value="true"/>
   -->
  
  <!-- Compiler options -->
  
  <property name="compiler.debug" value="on"/>
  <property name="compiler.generate.no.warnings" value="off"/>
  <property name="compiler.args" value=""/>
  <property name="compiler.max.memory" value="700m"/>
  <patternset id="ignored.files">
    <exclude name="**/*.hprof/**"/>
    <exclude name="**/*.pyc/**"/>
    <exclude name="**/*.pyo/**"/>
    <exclude name="**/*.rbc/**"/>
    <exclude name="**/*~/**"/>
    <exclude name="**/.DS_Store/**"/>
    <exclude name="**/.git/**"/>
    <exclude name="**/.hg/**"/>
    <exclude name="**/.svn/**"/>
    <exclude name="**/CVS/**"/>
    <exclude name="**/RCS/**"/>
    <exclude name="**/SCCS/**"/>
    <exclude name="**/__pycache__/**"/>
    <exclude name="**/_svn/**"/>
    <exclude name="**/rcs/**"/>
    <exclude name="**/vssver.scc/**"/>
    <exclude name="**/vssver2.scc/**"/>
  </patternset>
  <patternset id="library.patterns">
    <include name="*.war"/>
    <include name="*.swc"/>
    <include name="*.apk"/>
    <include name="*.zip"/>
    <include name="*.ear"/>
    <include name="*.egg"/>
    <include name="*.ane"/>
    <include name="*.jar"/>
  </patternset>
  <patternset id="compiler.resources">
    <exclude name="**/?*.java"/>
    <exclude name="**/?*.form"/>
    <exclude name="**/?*.class"/>
    <exclude name="**/?*.groovy"/>
    <exclude name="**/?*.scala"/>
    <exclude name="**/?*.flex"/>
    <exclude name="**/?*.kt"/>
    <exclude name="**/?*.clj"/>
    <exclude name="**/?*.aj"/>
  </patternset>
  
  <!-- JDK definitions -->
  
  <property name="jdk.bin.1.7" value="${jdk.home.1.7}/bin"/>
  <path id="jdk.classpath.1.7">
    <fileset dir="${jdk.home.1.7}">
      <include name="jre/lib/charsets.jar"/>
      <include name="jre/lib/deploy.jar"/>
      <include name="jre/lib/ext/dnsns.jar"/>
      <include name="jre/lib/ext/localedata.jar"/>
      <include name="jre/lib/ext/sunec.jar"/>
      <include name="jre/lib/ext/sunjce_provider.jar"/>
      <include name="jre/lib/ext/sunpkcs11.jar"/>
      <include name="jre/lib/ext/zipfs.jar"/>
      <include name="jre/lib/htmlconverter.jar"/>
      <include name="jre/lib/javaws.jar"/>
      <include name="jre/lib/jce.jar"/>
      <include name="jre/lib/jfr.jar"/>
      <include name="jre/lib/jfxrt.jar"/>
      <include name="jre/lib/jsse.jar"/>
      <include name="jre/lib/management-agent.jar"/>
      <include name="jre/lib/plugin.jar"/>
      <include name="jre/lib/resources.jar"/>
      <include name="jre/lib/rt.jar"/>
      <include name="lib/ant-javafx.jar"/>
      <include name="lib/dt.jar"/>
      <include name="lib/javafx-doclet.jar"/>
      <include name="lib/javafx-mx.jar"/>
      <include name="lib/jconsole.jar"/>
      <include name="lib/sa-jdi.jar"/>
      <include name="lib/tools.jar"/>
    </fileset>
  </path>
  
  <property name="project.jdk.home" value="${jdk.home.1.7}"/>
  <property name="project.jdk.bin" value="${jdk.bin.1.7}"/>
  <property name="project.jdk.classpath" value="jdk.classpath.1.7"/>
  
  
  <!-- Project Libraries -->
  <!-- Register Custom Compiler Taskdefs -->
  <property name="javac2.home" value="${idea.home}/lib"/>
  <path id="javac2.classpath">
    <pathelement location="${javac2.home}/javac2.jar"/>
    <pathelement location="${javac2.home}/jdom.jar"/>
    <pathelement location="${javac2.home}/asm-all.jar"/>
    <pathelement location="${javac2.home}/jgoodies-forms.jar"/>
  </path>
  <target name="register.custom.compilers">
    <taskdef name="javac2" classname="com.intellij.ant.Javac2" classpathref="javac2.classpath"/>
    <taskdef name="instrumentIdeaExtensions" classname="com.intellij.ant.InstrumentIdeaExtensions" classpathref="javac2.classpath"/>
  </target>
  
  <!-- Modules -->
  
  <import file="${basedir}/aws-android-sdk-2.1.1-core/module_aws-android-sdk-2.1.1-core.xml"/>
  
  <import file="${basedir}/aws-android-sdk-2.1.1-s3/module_aws-android-sdk-2.1.1-s3.xml"/>
  
  <import file="${basedir}/aws-android-sdk-2.1.1-sdb/module_aws-android-sdk-2.1.1-sdb.xml"/>
  
  <import file="${basedir}/InspectionFolio/module_inspectionfolio.xml"/>
  
  <import file="${basedir}/openCVLibrary310/module_opencvlibrary310.xml"/>
  
  <import file="${basedir}/ScreenSharingSDK/module_screensharingsdk.xml"/>
  
  <import file="${basedir}/module_snap3android-new2.xml"/>
  
  <import file="${basedir}/z-validations-library/module_z-validations-library.xml"/>
  
  <target name="init" description="Build initialization">
    <!-- Perform any build initialization in this target -->
  </target>
  
  <target name="clean" depends="clean.module.aws-android-sdk-2.1.1-core, clean.module.aws-android-sdk-2.1.1-s3, clean.module.aws-android-sdk-2.1.1-sdb, clean.module.inspectionfolio, clean.module.opencvlibrary310, clean.module.screensharingsdk, clean.module.snap3android-new2, clean.module.z-validations-library" description="cleanup all"/>
  
  <target name="build.modules" depends="init, clean, compile.module.aws-android-sdk-2.1.1-core, compile.module.aws-android-sdk-2.1.1-s3, compile.module.aws-android-sdk-2.1.1-sdb, compile.module.inspectionfolio, compile.module.opencvlibrary310, compile.module.screensharingsdk, compile.module.snap3android-new2, compile.module.z-validations-library" description="build all modules"/>
  
  <target name="all" depends="build.modules" description="build all"/>
</project>